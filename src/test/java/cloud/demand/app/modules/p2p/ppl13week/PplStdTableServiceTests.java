package cloud.demand.app.modules.p2p.ppl13week;

import cloud.demand.app.modules.p2p.ppl13week.service.PplStdTableService;
import java.time.YearMonth;
import javax.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PplStdTableServiceTests {

    @Resource
    private PplStdTableService pplStdTableService;

    @Test
    public void test() {
        pplStdTableService.syncPplItemLatestToCkStdTable();
    }

    @Test
    public void test2() {
        pplStdTableService.syncPplItemVersionToCkStdTable();
    }

    @Test
    public void test3() {
        pplStdTableService.syncPplYunxiaoApplyToCkStdTable();
    }

    @Test
    public void test4() {
        pplStdTableService.syncLatestPplItemVersionBaseToCkStdTable();
    }

    @Test
    public void test5() {
        pplStdTableService.syncLatestPplItemVersion532ToCkStdTable();
    }

    @Test
    public void test6() {
        YearMonth end = YearMonth.of(2023,7);
        pplStdTableService.syncYunxiaoApplyOrderDetailToToCkStdTable(end, 1, false);
    }

    @Test
    public void test7() {
        pplStdTableService.syncPplItemVersionNewestFromStdSwapToCkStdTable();
    }

    @Test
    public void test8() {
        pplStdTableService.syncOrderItemAndInfoToCkStdTable();
    }

}
