-- 月末历史月份规模存量
select year_month,
       if(${has_industry_dept}, origin_industry_dept, null)      as any_industry_dept,
       if(${has_war_zone}, war_zone, null)                       as any_war_zone,
       if(${has_customer_short_name}, customer_short_name, null) as any_customer_short_name,
       if(${has_instance_type}, instance_type, NULL)             as any_instance_type,
       if(${has_instance_family}, ${instance_family_map}, NULL)    as any_instance_family,
       if(${has_gpu_card_type}, gpu_card_type, null)             as any_gpu_card_type,
       if(${has_customhouse_title}, customhouse_title, null)     as any_customhouse_title,
       if(${has_region_name}, region_name, null)                 as any_region_name,
       if(${has_zone_name}, zone_name, null)                     as any_zone_name,
       null                                                      as any_instance_model,
       null                                                      as any_specifications,
       sum(case
               when '${queryRange}' = '计费用量' then
                   case
                       when '${unit}' = '核数' then
                           cur_bill_core
                       else
                           cur_bill_gpu
                       end
               else
                   case
                       when '${unit}' = '核数' then
                           cur_service_core
                       else
                           cur_service_gpu
                       end
           END)                                                  as all_amount
from (select formatDateTime(stat_time, '%Y-%m')      as year_month,
             origin_industry_dept                    as origin_industry_dept,
             if(${has_war_zone}, crp_war_zone, null) as war_zone,            -- crp 战区
             if(${has_customer_short_name}, un_customer_short_name,
                null)                                as customer_short_name, -- 通用客户简称
             instance_type,                                                  -- 实例类型（只能用实例类型做去重，和行业数据看板对齐）
             gpu_card_type,
             customhouse_title,
             region_name,                                                    -- 是 zone_name的上级，可以不用加 has_region_name
             zone_name,
             sum(cur_bill_core)                      as cur_bill_core,
             sum(cur_service_core)                   as cur_service_core,
             sum(cur_bill_gpu)                       as cur_bill_gpu,
             sum(cur_service_gpu)                    as cur_service_gpu
      from std_crp.dwd_txy_scale_df_view ${where}
      group by year_month, origin_industry_dept, war_zone, customer_short_name, gpu_card_type,
               instance_type, customhouse_title, region_name, zone_name)
group by year_month, any_industry_dept, any_war_zone, any_customer_short_name, any_instance_type,
         any_instance_family, any_gpu_card_type, any_customhouse_title, any_region_name,
         any_zone_name, any_instance_model
    ${having}