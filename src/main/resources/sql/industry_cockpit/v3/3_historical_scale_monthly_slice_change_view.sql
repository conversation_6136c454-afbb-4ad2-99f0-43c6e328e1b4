-- 历史月份规模变化量
select year_month,
       if(${has_industry_dept}, any_origin_industry_dept, null)      as any_industry_dept,
       if(${has_war_zone}, any_war_zone, null)                       as any_war_zone,
       if(${has_customer_short_name}, any_customer_short_name, null) as any_customer_short_name,
       if(${has_instance_type}, any_instance_type, NULL)             as any_instance_type,
       if(${has_instance_family}, any_instance_family, NULL)         as any_instance_family,
       if(${has_gpu_card_type}, any_gpu_card_type, null)             as any_gpu_card_type,
       if(${has_customhouse_title}, any_customhouse_title, null)     as any_customhouse_title,
       if(${has_region_name}, any_region_name, null)                 as any_region_name,
       if(${has_zone_name}, any_zone_name, null)                     as any_zone_name,
       null                                                          as any_instance_model,
       null                                                          as any_specifications,
       case
           when '${demandType}' = '新增' then
               sum(case
                       when '${queryRange}' = '计费用量' then
                           case
                               when '${unit}' = '核数' then
                                   new_elastic_bill_cores
                               else
                                   new_elastic_bill_gpu
                               end
                       else
                           case
                               when '${unit}' = '核数' then
                                   new_elastic_service_cores
                               else
                                   new_elastic_service_gpu
                               end
                   END)
           when '${demandType}' = '退回' then
               sum(case
                       when '${queryRange}' = '计费用量' then
                           case
                               when '${unit}' = '核数' then
                                   return_bill_cores
                               else
                                   return_bill_gpu
                               end
                       else
                           case
                               when '${unit}' = '核数' then
                                   return_service_cores
                               else
                                   return_service_gpu
                               end
                   END)
           else
               sum(case
                       when '${queryRange}' = '计费用量' then
                           case
                               when '${unit}' = '核数' then
                                   net_increase_bill_cores
                               else
                                   net_increase_bill_gpu
                               end
                       else
                           case
                               when '${unit}' = '核数' then
                                   net_increase_service_cores
                               else
                                   net_increase_service_gpu
                               end
                   END)
           end                                                       as all_amount
from (select formatDateTime(stat_time, '%Y-%m')                        as year_month,
             if(${has_dis_industry_dept}, origin_industry_dept, null)  as any_origin_industry_dept,
             if(${has_dis_war_zone}, crp_war_zone, null)               as any_war_zone,            -- crp 战区
             if(${has_dis_customer_short_name} or ${has_dis_show_customer_short_name},
                if(${has_dis_customer_short_name}, un_customer_short_name,
                   if(${has_dis_show_customer_short_name}, ${show_customer_short_name_map}, null)),
                null)                                                  as any_customer_short_name, -- 通用客户简称
             if(${has_instance_family}, ${instance_family_map}, NULL)  as any_instance_family,
             if(${has_dis_instance_type}, instance_type, null)         as any_instance_type,
             if(${has_dis_gpu_card_type}, gpu_card_type, null)         as any_gpu_card_type,
             if(${has_dis_customhouse_title}, customhouse_title, null) as any_customhouse_title,
             if(${has_dis_region_name}, region_name, null)             as any_region_name,         -- 是 zone_name的上级，可以不用加 has_region_name
             if(${has_dis_zone_name}, zone_name, null)                 as any_zone_name,
             sum(change_bill_core_from_last_month)                     as net_increase_bill_cores,
             if(net_increase_bill_cores > 0, net_increase_bill_cores,
                toDecimal64(0, 6))                                     as new_elastic_bill_cores,
             if(net_increase_bill_cores < 0, net_increase_bill_cores,
                toDecimal64(0, 6))                                     as return_bill_cores,
             sum(change_service_core_from_last_month)                  as net_increase_service_cores,
             if(net_increase_service_cores > 0, net_increase_service_cores,
                toDecimal64(0, 6))                                     as new_elastic_service_cores,
             if(net_increase_service_cores < 0, net_increase_service_cores,
                toDecimal64(0, 6))                                     as return_service_cores,
             sum(change_bill_gpu_from_last_month)                      as net_increase_bill_gpu,
             if(net_increase_bill_gpu > 0, net_increase_bill_gpu,
                toDecimal64(0, 6))                                     as new_elastic_bill_gpu,
             if(net_increase_bill_gpu < 0, net_increase_bill_gpu,
                toDecimal64(0, 6))                                     as return_bill_gpu,
             sum(change_service_gpu_from_last_month)                   as net_increase_service_gpu,
             if(net_increase_service_gpu > 0, net_increase_service_gpu,
                toDecimal64(0, 6))                                     as new_elastic_service_gpu,
             if(net_increase_service_gpu < 0, net_increase_service_gpu,
                toDecimal64(0, 6))                                     as return_service_gpu
      from std_crp.dwd_txy_scale_df_view ${where}
      group by year_month, any_origin_industry_dept, any_war_zone, any_customer_short_name,
               any_gpu_card_type, any_instance_family, any_instance_type, any_customhouse_title,
               any_region_name,
               any_zone_name)
group by year_month, any_industry_dept, any_war_zone, any_customer_short_name, any_instance_type,
         any_instance_family, any_gpu_card_type, any_customhouse_title, any_region_name,
         any_zone_name
    ${having}
order by year_month
