-- Ppl指定版本
select
    year_month,
    if(${has_industry_dept}, industry_dept, null) as any_industry_dept,
    if(${has_war_zone}, war_zone, null) as any_war_zone,
    if(${has_customer_short_name}, customer_short_name, null) as any_customer_short_name,
    if(${has_instance_type}, instance_type, NULL) as any_instance_type,
    if(${has_instance_family}, ${instance_family_map}, NULL) as any_instance_family,
    if(${has_gpu_card_type}, gpu_card_type, null) as any_gpu_card_type,
    if(${has_customhouse_title}, customhouse_title, null) as any_customhouse_title,
    if(${has_region_name}, region_name, null) as any_region_name,
    if(${has_zone_name}, zone_name, null) as any_zone_name,
    sum(case when '${demandType}' = '新增&弹性' then
                 case when '${unit}' = '核数' then
                          new_elastic_cores
                      else
                          new_elastic_gpu_num
                     end
             when '${demandType}' = '新增' then
                 case when '${unit}' = '核数' then
                          new_cores
                      else
                          new_gpu_num
                     end
             when '${demandType}' = '弹性' then
                 case when '${unit}' = '核数' then
                          elastic_cores
                      else
                          elastic_gpu_num
                     end
             when '${demandType}' = '退回' then
                 case when '${unit}' = '核数' then
                          return_cores
                      else
                          return_gpu_num
                     end
             else
                 case when '${unit}' = '核数' then
                          net_increase_cores
                      else
                          net_increase_gpu_num
                     end
        END) as all_amount
from (
         select formatDateTime(begin_buy_date,'%Y-%m') as year_month,
                industry_dept, war_zone, common_customer_short_name as customer_short_name,
                instance_type,
                gpu_type as gpu_card_type,
                customhouse_title,
                region_name,
                zone_name,
                case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_elastic_cores,
                case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as new_elastic_gpu_num,
                case when demand_type in ('NEW') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_cores,
                case when demand_type in ('NEW') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as new_gpu_num,
                case when demand_type in ('ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as elastic_cores,
                case when demand_type in ('ELASTIC') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as elastic_gpu_num,
                case when demand_type in ('RETURN') then  -toDecimal64(total_core, 6) else toDecimal64(0, 6) end as return_cores,
                case when demand_type in ('RETURN') then  -toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as return_gpu_num,
                case when demand_type in ('RETURN') then -toDecimal64(total_core, 6) else toDecimal64(total_core, 6) end as net_increase_cores,
                case when demand_type in ('RETURN') then -toDecimal64(total_gpu_num, 6) else toDecimal64(total_gpu_num, 6) end as net_increase_gpu_num
         from std_crp.dwd_crp_ppl_item_version_cf
         ${pplVersionWhere}
         ${predictionFixSql}
     )
group by  year_month,any_industry_dept,any_war_zone,any_customer_short_name,any_instance_type,any_instance_family,any_gpu_card_type,any_customhouse_title,any_region_name,any_zone_name
union all
-- Ppl最新版本
select
    year_month,
    if(${has_industry_dept}, industry_dept, null) as any_industry_dept,
    if(${has_war_zone}, war_zone, null) as any_war_zone,
    if(${has_customer_short_name}, customer_short_name, null) as any_customer_short_name,
    if(${has_instance_type}, instance_type, NULL) as any_instance_type,
    if(${has_instance_family}, ${instance_family_map}, NULL) as any_instance_family,
    if(${has_gpu_card_type}, gpu_card_type, null) as any_gpu_card_type,
    if(${has_customhouse_title}, customhouse_title, null) as any_customhouse_title,
    if(${has_region_name}, region_name, null) as any_region_name,
    if(${has_zone_name}, zone_name, null) as any_zone_name,
    sum(case when '${demandType}' = '新增&弹性' then
                 case when '${unit}' = '核数' then
                          new_elastic_cores
                      else
                          new_elastic_gpu_num
                     end
             when '${demandType}' = '新增' then
                 case when '${unit}' = '核数' then
                          new_cores
                      else
                          new_gpu_num
                     end
             when '${demandType}' = '弹性' then
                 case when '${unit}' = '核数' then
                          elastic_cores
                      else
                          elastic_gpu_num
                     end
             when '${demandType}' = '退回' then
                 case when '${unit}' = '核数' then
                          return_cores
                      else
                          return_gpu_num
                     end
             else
                 case when '${unit}' = '核数' then
                          net_increase_cores
                      else
                          net_increase_gpu_num
                     end
        END) as all_amount
from (
        select formatDateTime(begin_buy_date,'%Y-%m') as year_month,
            industry_dept, war_zone, common_customer_short_name as customer_short_name,
            instance_type,
            gpu_type as gpu_card_type,
            customhouse_title,
            region_name,
            zone_name,
            case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_elastic_cores,
            case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as new_elastic_gpu_num,
            case when demand_type in ('NEW') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_cores,
            case when demand_type in ('NEW') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as new_gpu_num,
            case when demand_type in ('ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as elastic_cores,
            case when demand_type in ('ELASTIC') then toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as elastic_gpu_num,
            case when demand_type in ('RETURN') then  -toDecimal64(total_core, 6) else toDecimal64(0, 6) end as return_cores,
            case when demand_type in ('RETURN') then  -toDecimal64(total_gpu_num, 6) else toDecimal64(0, 6) end as return_gpu_num,
            case when demand_type in ('RETURN') then -toDecimal64(total_core, 6) else toDecimal64(total_core, 6) end as net_increase_cores,
            case when demand_type in ('RETURN') then -toDecimal64(total_gpu_num, 6) else toDecimal64(total_gpu_num, 6) end as net_increase_gpu_num
        from std_crp.dws_crp_ppl_item_version_newest_cf
            ${pplNewestWhere}
            ${predictionFixSql}
    )
group by  year_month,any_industry_dept,any_war_zone,any_customer_short_name,any_instance_type,any_instance_family,any_gpu_card_type,any_customhouse_title,any_region_name,any_zone_name
