-- 需求趋势表格数据查询
-- 需求趋势表格数据查询
with history_month_detail_view as (
    -- 历史月份规模变化量(去重：机型+实例类型)
    select
        data_month,
        industry_dept,
        war_zone, -- crp 战区
        customer_short_name, -- 通用客户简称
        instance_type, -- 实例类型（只能用实例类型做去重，和行业数据看板对齐）
        region_name,
        zone_name,
        sum(net_increase_cores) as net_increase_cores,
        sum(new_elastic_cores) as new_elastic_cores,
        sum(return_cores) as return_cores
    from (
             select toYYYYMM(stat_time) as data_month,
                    origin_industry_dept as industry_dept,
                    if(${has_war_zone},crp_war_zone,null) as war_zone, -- crp 战区
                    if(${has_customer_short_name},un_customer_short_name,null) as customer_short_name, -- 通用客户简称
                    case when '是' = '${mergeInstanceType}' then un_instance_type else instance_type end as instance_type,
                    region_name, -- 是 zone_name的上级，可以不用加 has_region_name
                    zone_name,
                    if('${queryRange}' = '计费用量',sum(change_bill_core_from_last_month),sum(change_service_core_from_last_month)) as net_increase_cores,
                    if(net_increase_cores > 0, net_increase_cores, toDecimal64(0, 6)) as new_elastic_cores,
                    if(net_increase_cores < 0, net_increase_cores, toDecimal64(0, 6)) as return_cores
             from std_crp.dwd_txy_scale_df_view
                      ${HISTORY_FILTER}
                 ${HISTORY_CHANGE_FIX_WHERE}
             group by data_month,industry_dept,war_zone,customer_short_name,un_instance_type,instance_type,region_name, zone_name
         ) group by data_month,industry_dept,war_zone,customer_short_name,instance_type,region_name, zone_name
),
latest_forecast_detail_view as (
    -- 未来月份的规模变化预测量
    select toYYYYMM(begin_buy_date) as data_month,
        industry_dept, war_zone, common_customer_short_name as customer_short_name,
        case when '是' = '${mergeInstanceType}' then common_instance_type else instance_type end as instance_type,
        region_name, zone_name,
        case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_elastic_cores,
        case when demand_type in ('RETURN') then  -toDecimal64(total_core, 6) else toDecimal64(0, 6) end as return_cores,
        case when demand_type in ('RETURN') then -toDecimal64(total_core, 6) else toDecimal64(total_core, 6) end as net_increase_cores
    from std_crp.dws_crp_ppl_item_version_newest_cf
    ${PREDICTION_FILTER}
    ${PREDICTION_FIX_WHERE}
    union all
    select toYYYYMM(begin_buy_date) as data_month,
        industry_dept, war_zone, common_customer_short_name as customer_short_name,
        case when '是' = '${mergeInstanceType}' then common_instance_type else instance_type end as instance_type,
        region_name, zone_name,
        case when demand_type in ('NEW', 'ELASTIC') then toDecimal64(total_core, 6) else toDecimal64(0, 6) end as new_elastic_cores,
        case when demand_type in ('RETURN') then  -toDecimal64(total_core, 6) else toDecimal64(0, 6) end as return_cores,
        case when demand_type in ('RETURN') then -toDecimal64(total_core, 6) else toDecimal64(total_core, 6) end as net_increase_cores
    from std_crp.dwd_crp_ppl_item_version_cf
    ${VERSION_PREDICTION_FILTER}
    ${PREDICTION_FIX_WHERE}
)
select ${industry_dept_field}
       ${war_zone_field}
       ${customer_short_name_field}
       ${instance_type_field}
       ${region_name_field}
       ${zone_name_field}
       toDecimal64(sum(case when data_month%100 = 1 then all_cores else toDecimal64(0, 6) end), 2) as jan_cores,
       toDecimal64(sum(case when data_month%100 = 2 then all_cores else toDecimal64(0, 6) end), 2) as feb_cores,
       toDecimal64(sum(case when data_month%100 = 3 then all_cores else toDecimal64(0, 6) end), 2) as mar_cores,
       toDecimal64(sum(case when data_month%100 = 4 then all_cores else toDecimal64(0, 6) end), 2) as apr_cores,
       toDecimal64(sum(case when data_month%100 = 5 then all_cores else toDecimal64(0, 6) end), 2) as may_cores,
       toDecimal64(sum(case when data_month%100 = 6 then all_cores else toDecimal64(0, 6) end), 2) as jun_cores,
       toDecimal64(sum(case when data_month%100 = 7 then all_cores else toDecimal64(0, 6) end), 2) as jul_cores,
       toDecimal64(sum(case when data_month%100 = 8 then all_cores else toDecimal64(0, 6) end), 2) as aug_cores,
       toDecimal64(sum(case when data_month%100 = 9 then all_cores else toDecimal64(0, 6) end), 2) as sep_cores,
       toDecimal64(sum(case when data_month%100 = 10 then all_cores else toDecimal64(0, 6) end), 2) as oct_cores,
       toDecimal64(sum(case when data_month%100 = 11 then all_cores else toDecimal64(0, 6) end), 2) as nov_cores,
       toDecimal64(sum(case when data_month%100 = 12 then all_cores else toDecimal64(0, 6) end), 2) as dec_cores,
       toDecimal64(sum(case when data_month%100 in (1, 2, 3) then all_cores else toDecimal64(0, 6) end), 2) as q1_cores,
       toDecimal64(sum(case when data_month%100 in (4, 5, 6) then all_cores else toDecimal64(0, 6) end), 2) as q2_cores,
       toDecimal64(sum(case when data_month%100 in (7, 8, 9) then all_cores else toDecimal64(0, 6) end), 2) as q3_cores,
       toDecimal64(sum(case when data_month%100 in (10, 11, 12) then all_cores else toDecimal64(0, 6) end), 2) as q4_cores,
       toDecimal64(sum(all_cores), 2) as total_cores
from (
    select data_month,
           industry_dept,
           ${war_zone_field}
           ${customer_short_name_field}
           ${instance_type_field}
           ${region_name_field}
           ${zone_name_field}
           sum(case when '${demand_type}' = '新增&弹性' then new_elastic_cores when '${demand_type}' = '退回' then return_cores else net_increase_cores end) as all_cores
    from history_month_detail_view
    group by data_month, industry_dept
             ${war_zone_group}
             ${customer_short_name_group}
             ${instance_type_group}
             ${region_name_group}
             ${zone_name_group}
    union all
    select data_month, industry_dept,
           ${war_zone_field}
           ${customer_short_name_field}
           ${instance_type_field}
           ${region_name_field}
           ${zone_name_field}
           sum(case when '${demand_type}' = '新增&弹性' then new_elastic_cores when '${demand_type}' = '退回' then return_cores else net_increase_cores end) as all_cores
    from latest_forecast_detail_view
    group by data_month, industry_dept
             ${war_zone_group}
             ${customer_short_name_group}
             ${instance_type_group}
             ${region_name_group}
             ${zone_name_group}
) tmp
group by industry_dept
         ${war_zone_group}
         ${customer_short_name_group}
         ${instance_type_group}
         ${region_name_group}
         ${zone_name_group}
