-- 获取服务水平和计算服务水平的环比数据
-- 查询条件：产品、需求年度、行业、战区、客户、境内外、实例类型、地域、可用区、准确率口径、预测量口径
select previous_month_satisfaction_rate,
       current_month_satisfaction_rate,
       case
           when previous_month_satisfaction_rate = 0.0 then 0.0
           else round((current_month_satisfaction_rate - previous_month_satisfaction_rate), 2) end as month_on_month_rate
from (
    select sum(case when `year_month` = '${PREVIOUS_MONTH}' then satisfaction_rate else 0.0 end) as previous_month_satisfaction_rate,
           sum(case when `year_month` = '${CURRENT_MONTH}' then satisfaction_rate else 0.0 end) as current_month_satisfaction_rate
    from industry_cockpit_server_level
    ${FILTER}
) tmp;