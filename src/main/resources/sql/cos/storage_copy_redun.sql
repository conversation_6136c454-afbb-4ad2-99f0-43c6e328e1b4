select '境内' as type, sum(Capacity) as num
from cos.cloud_cos_utilization
where date = ?
  and cloud in ('公有云','金融云')
  and DataName = '存储副本冗余'
  and type = '境内'

union all

select '境外' as type, sum(Capacity) as num
from cos.cloud_cos_utilization
where date = ?
  and cloud in ('公有云','金融云')
  and DataName = '存储副本冗余'
  and type = '境外'

union all

select '合计' as type, sum(Capacity) as num
from cos.cloud_cos_utilization
where date = ?
  and cloud in ('公有云','金融云')
  and DataName = '存储副本冗余'
  and type in ('境内','境外')