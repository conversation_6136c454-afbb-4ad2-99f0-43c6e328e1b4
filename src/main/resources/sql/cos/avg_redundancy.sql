-- 平均冗余度计算规则(分境内外）
# 境内/境外平均冗余度
select t1.disk_used_size / t2.logic_size 境内平均冗余度,
       t3.disk_used_size / t4.logic_size 境外平均冗余度
from (
      (select sum(capacity) disk_used_size
       from cos_detail
       where date = :statTime
         and service_name = '昨日已使用物理量'
         and customhouse_title = '境内'
         and type = '公有云') t1,
      (select sum(capacity) logic_size
       from cos_detail
       where date = :statTime
         and service_name = '业务存储量'
         and customhouse_title = '境内'
         and type = '公有云') t2,
      (select sum(capacity) disk_used_size
       from cos_detail
       where date = :statTime
         and service_name = '昨日已使用物理量'
         and customhouse_title = '境外'
         and type = '公有云') t3,
      (select sum(capacity) logic_size
       from cos_detail
       where date = :statTime
         and service_name = '业务存储量'
         and customhouse_title = '境外'
         and type = '公有云')
t4);