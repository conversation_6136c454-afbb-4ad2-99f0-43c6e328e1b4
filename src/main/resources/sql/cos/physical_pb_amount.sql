-- 关于两个物理量的计算，单位是PB
-- 直接把地域、云类型、业务、和az合起来(手动转换汇总)
select concat(region,cloud,ifnull(business,''),(case when az_count is null then ''
      else case when az_count = 1 then '单AZ' else '多AZ' end end)) as regionName,
      service_name, customhouse_title, sum(capacity) pb_amount
from cos_detail
where date = :statTime
  and service_name in ('昨日已上架物理量', '昨日已使用物理量', '业务存储量')
  and type = '公有云'
group by regionName, service_name, customhouse_title;