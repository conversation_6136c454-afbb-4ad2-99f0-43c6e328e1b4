-- 物理机退回已执行
select sum(toInt64(TotalLogicCore)) as `sum(core_num)`,if(bgName in (?) and IsYunReturn in ('是','否'),'云业务','自研业务') as business_type , 1 as is_executed
from cubes.server_return1_for_hedging
where
  version = ?
  and UworkFinishedTime >= ?
--    and UworkFinishedTime  >= '2023-01-01 00:00:00'
--    and UworkFinishedTime <= '2023-12-32 00:00:00'
group by business_type
order by business_type

-- 数据源
-- select case
--            when (
--                             ReturnSysType = '云业务'
--                         or BgName = 'CSIG云与智慧产业事业群'
--                     )
--                and IsYunReturn in ('是', '否') then '云业务'
--            else '自研业务'
--            end as business_calss,
--        DeptName dept_name,
--        BgName bg_name,
--        TotalLogicCore device_applied_core
-- from cubes.return_slice
-- where DAY = '2023-09-26'
--   and ReasonClass != '退役回收(折旧满4年)'
--   and IsUworkFinish = '是'
--   and YEAR(UworkFinishedTime) = substring('2023-09-26', 1, 4)