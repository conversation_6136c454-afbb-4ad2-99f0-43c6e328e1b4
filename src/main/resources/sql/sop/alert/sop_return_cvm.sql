-- cvm退回,排除算力平台
select sum(core_num),business_type,is_executed from std_crp.dwd_sop_report_return_cvm
where version = ?
  and index_date >= ?
-- and dept_name != '算力平台' -- sop退回cvm清洗部门，上游底表不清洗，两边口径不同意，这里不限制部门
and is_executed = 1
and order_id not like 'QR%' -- 排除QR单，这里的QR单直接来自云梯
and order_id not like 'ZY%' -- 排除ZY单，这里ZY单来自订单表
group by business_type,is_executed
having is_executed = 1
union all
select sum(core_num),business_type,is_executed from std_crp.dwd_sop_report_return_cvm_ext
where version = ?
  and index_date >= ?
  and plan_type = 'IN_PLAN'
  and dept_name != '算力平台'
group by business_type,is_executed
having is_executed = 0

-- select sum(core_num),version,is_executed from std_crp.dwd_sop_report_return_cvm
-- where  index_date >= '2023-01-01'
--   and dept_name != '算力平台'
-- and is_executed = 1
-- group by version ,is_executed
-- having is_executed = 1
-- order by version desc limit 5