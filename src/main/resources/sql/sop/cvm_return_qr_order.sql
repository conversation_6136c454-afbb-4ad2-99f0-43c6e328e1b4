-- cvm退回的QR单
select bg_name,
       custom_bg          as custom_bg_name,
       dept_name,
       plan_product_name,
       project_name,
       DATE_FORMAT(t1.create_time,'%Y-%m-%d') as plan_time,
       resource_pool_type,
       core_type,
       country_name,
       region_name,
       city_name,
       generation_type,
       device_family_name,
       instance_type,
       instance_model,
       zone_name,
       order_id,
       item_id,
       change_cvm_amount  as num,
       change_core_amount as core_num
from (select *
      from yunti_demand.yunti_return_plan_cvm_item_record
      where order_id like 'QR%'
        and create_time <= ?) t1
         left join yunti_demand.yunti_return_plan_cvm_item t2
                   on t1.item_id = t2.id
where t2.plan_type = 'IN_PLAN' and (change_core_amount > 0 or change_cvm_amount > 0) and t2.id is not null

