-- 销-预测净增 按月
select
    '${product_type}' as product_type,
    demand_source,
    instance_type,
    instance_model,
    region_name,
    zone_name,
    gpu_type,
    if(${is_cvm},-1,win_rate) as win_rate,           -- 赢率（GPU需要考虑赢率）
    customer_short_name ,
    industry_dept,
    yunxiao_order_id,
    if(${is_cvm},sum(abs(total_core)),sum(abs(total_gpu_num))) as core_num,
    begin_buy_date,
    end_buy_date,
    formatDateTime(begin_buy_date, '%Y-%m') as `year_month`,
    null as `year_week`,
    multiIf(demand_type = 'NEW','新增',demand_type = 'ELASTIC','弹性','退回') as demand_type_name
from std_crp.ads_mck_forecast_summary_df
where
  stat_time = '${demand_stat_time}' -- 2024-02-07
  and ((`year_month` < '${min_version_year_month}' and demand_source = '预约单') -- 历史13周的只看预约单
    or (`year_month` >= '${min_version_year_month}' and demand_source = '大客户') -- 13周内只看需求预测
    or demand_source = '中长尾')
  and begin_buy_date >= '${start_date}'
  and begin_buy_date <= '${end_date}'
  and demand_type in ('${demand_type}') -- NEW or RETURN
  and product in ('${product_class}')
group by
    demand_source,
    instance_type,
    instance_model,
    region_name,
    zone_name,
    gpu_type,
    win_rate,
    customer_short_name ,
    industry_dept,
    yunxiao_order_id,
    begin_buy_date,
    end_buy_date,
    `year_month`,
    `year_week`,
    demand_type
having core_num != 0