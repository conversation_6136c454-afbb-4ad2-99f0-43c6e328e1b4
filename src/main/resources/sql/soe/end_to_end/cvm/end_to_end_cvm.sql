-- 端到端（可查询【CVM or GPU】【期初售卖 or 总数】）
select
    product_type ,
    null as customhouse_title ,
    null as area_name ,
    null as region_name ,
    zone_name,
    null as category,
    null as sale_type,
    null as item,
    device_model as device_type,
    null as instance_type ,
    null as year_month,
    null as year_week,
    sum(cpu_count) as core_num
from public.end_to_end_zone_device_model
where data_date = '${int_stat_time}' -- 20240305
  ${category_sale_type_item} -- 分类一 & 售卖类型 & 分类二
  and product_type = '${product_type}' -- 产品类型：CVM or GPU
  and cpu_count != 0
group by
    product_type,
    customhouse_title,
    area_name,
    region_name,
    zone_name,
    instance_type,
    device_model,
    year_month,
    year_week