insert into aws_consensus_cycle_elastic_order_performance_track_df
(
    stat_date,
    create_time,
    elastic_type,
    order_number,
    zone_name,
    instance_type,
    stat_cycle,
    cycle_start_date,
    cycle_end_date,
    total_deduct_core,
    total_deduct_gpu,
    total_buy_core,
    total_buy_gpu,
    total_idle_core,
    total_idle_gpu
)
select
    ? as stat_date,
    create_time,
    elastic_type,
    order_number,
    zone_name,
    instance_type,
    stat_cycle,
    cycle_start_date,
    cycle_end_date,
    total_deduct_core,
    total_deduct_gpu,
    total_buy_core,
    total_buy_gpu,
    total_idle_core,
    total_idle_gpu
from aws_consensus_cycle_elastic_order_performance_track_df
where stat_date = ? and order_number not in (?)