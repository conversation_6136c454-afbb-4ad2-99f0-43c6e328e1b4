select b.consensus_begin_buy_date  as `date`, b.supply_zone_name as zone_name,
       b.supply_instance_type as instance_type,
       sum(a.total_result_match_core) as num ,
       sum(a.pre_deduct_core) as pre_deduct_num
from order_supply_plan_detail_satisfy a
         left join order_supply_plan_detail b on a.supply_detail_id = b.id
where a.deleted = 0 and b.deleted = 0 and a.available = 1
  and a.order_number = ? and b.id in (?)
group by b.consensus_begin_buy_date  , b.supply_zone_name  , b.supply_instance_type