with t as (
select
       ''                                            as small_or_big,
       0                                             as last_day_num,
       ''                                            as industry_dept,
       zone_name                                     as zone_name,
       any(customhouse_title)                        as customhouse_title,
       any(region)                                   as region,
       any(region_name)                              as region_name,
       instance_type                                 as gins_family,
       sum(change_bill_service_core_from_last_month) as diff,
       case when diff > 0 then diff else 0 end       as new_diff,
       case when diff < 0 then abs(diff) else 0 end  as ret_diff,
       0                                             as cur_core

from dwd_txy_scale_df
where 1 = 1
  and stat_time = month_end_date
    and stat_time between :inputDateStart and :inputDateEnd

  ${CONDITION}
/*${WEB_CONDITION}*/
group by zone_name, instance_type
having new_diff > 0 or ret_diff > 0

)
select *, 0 as year, 0 as month
from t

