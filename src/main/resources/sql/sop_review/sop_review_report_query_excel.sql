select if(${has_business_type}, business_type, null)            as any_business_type,
       ${business_range}                                        as business_range,          -- 这里不能作为 any，这个字段底数不能用，得用清洗后的
       if(${has_dept_name}, dept_name, null)                    as any_dept_name,
       if(${has_plan_product_name}, plan_product_name, null)    as any_plan_product_name,
       if(${has_obs_project_type}, obs_project_type, null)      as any_obs_project_type,
       if(${has_resource_type}, resource_type, null)            as any_resource_type,
       if(${has_compute_type}, compute_type, null)              as any_compute_type,
       if(${has_device_type}, device_type, null)                as any_device_type,
       if(${has_device_family}, device_family, null)            as any_device_family,
       if(${has_instance_family}, instance_family, null)        as any_instance_family,
       if(${has_instance_type}, instance_type, null)            as any_instance_type,
       if(${has_instance_model}, instance_model, null)          as any_instance_model,
       if(${has_resource_pool_type}, resource_pool_type, null)  as any_resource_pool_type,
       if(${has_gpu_abbr}, gpu_abbr, null)                      as any_gpu_abbr,
       if(${has_customhouse_title}, customhouse_title, null)    as any_customhouse_title,
       if(${has_country_name}, country_name, null)              as any_country_name,
       if(${has_city_name}, city_name, null)                    as any_city_name,

       if(${has_cpu_abbr}, cpu_abbr, null)                      as any_cpu_abbr,
       if(${has_memory_volume}, memory_volume, null)            as any_memory_volume,
       if(${has_disk_abbr}, disk_abbr, null)                    as any_disk_abbr,
       if(${has_disk2_abbr}, disk2_abbr, null)                  as any_disk2_abbr,
       if(${has_disk_volume}, disk_volume, null)                 as any_disk_volume,
       if(${has_disk2_volume}, disk2_volume, null)               as any_disk2_volume,
       if(${has_nic_abbr}, nic_abbr, null)                      as any_nic_abbr,
       if(${has_ssd_abbr}, ssd_abbr, null)                      as any_ssd_abbr,
       if(${has_ssd2_abbr}, ssd2_abbr, null)                    as any_ssd2_abbr,
       if(${has_ssd3_abbr}, ssd3_abbr, null)                    as any_ssd3_abbr,
       if(${has_cpu_vender}, cpu_vender, null)                  as any_cpu_vender,
       if(${has_intellect_network_card_type}, intellect_network_card_type,
          null)                                                 as any_intellect_network_card_type,
       if(${has_technical_class}, technical_class, null)        as any_technical_class,
       if(${has_core_components}, core_components_bit, null)    as any_core_components_bit, -- has_core_components是对的，_bit不对外展示，在应用层手动转换
       if(${has_core_components} or ${has_components_field} or ${has_enum_value},
          enum_bit_str,
          null)                                                 as any_enum_bit_str,        -- has_core_components是对的，_bit不对外展示，在应用层手动转换

       if(${has_index_year_month}, holiday_year_month, null)    as any_index_year_month,
       sum(device_num)                                          as total_num,
       sum(core_num)                                            as total_core_num,
       sum(gpu_num)                                             as total_gpu_num,
       sum(logic_capacity)                                      as total_logic_capacity,
       sum(device_num * cpu_number)                             as total_cpu_number,
       sum(device_num * memory_number)                          as total_memory_number,
       sum(device_num * total_disk_number)                      as total_disk_number,
       -- 需求已执行
       sumIf(device_num, index = 'SOP_DEMAND_EXECUTED')         as demand_executed_num,
       sumIf(core_num, index = 'SOP_DEMAND_EXECUTED')           as demand_executed_core_num,
       sumIf(gpu_num, index = 'SOP_DEMAND_EXECUTED')            as demand_executed_gpu_num,
       sumIf(logic_capacity, index = 'SOP_DEMAND_EXECUTED')     as demand_executed_logic_capacity,
       -- 需求未执行
       sumIf(device_num, index = 'SOP_DEMAND_NOT_EXECUTED')     as demand_not_executed_num,
       sumIf(core_num, index = 'SOP_DEMAND_NOT_EXECUTED')       as demand_not_executed_core_num,
       sumIf(gpu_num, index = 'SOP_DEMAND_NOT_EXECUTED')        as demand_not_executed_gpu_num,
       sumIf(logic_capacity, index = 'SOP_DEMAND_NOT_EXECUTED') as demand_not_executed_logic_capacity,
       -- 退回已执行
       sumIf(device_num, index = 'SOP_RETURN_EXECUTED')         as return_executed_num,
       sumIf(core_num, index = 'SOP_RETURN_EXECUTED')           as return_executed_core_num,
       sumIf(gpu_num, index = 'SOP_RETURN_EXECUTED')            as return_executed_gpu_num,
       sumIf(logic_capacity, index = 'SOP_RETURN_EXECUTED')     as return_executed_logic_capacity,
       -- 退回未执行
       sumIf(device_num, index = 'SOP_RETURN_NOT_EXECUTED')     as return_not_executed_num,
       sumIf(core_num, index = 'SOP_RETURN_NOT_EXECUTED')       as return_not_executed_core_num,
       sumIf(gpu_num, index = 'SOP_RETURN_NOT_EXECUTED')        as return_not_executed_gpu_num,
       sumIf(logic_capacity, index = 'SOP_RETURN_NOT_EXECUTED') as return_not_executed_logic_capacity
from std_crp.dws_sop_review_report ${where}
group by any_business_type,
         business_range,
         any_dept_name,
         any_plan_product_name,
         any_obs_project_type,
         any_resource_type,
         any_compute_type,
         any_device_type,
         any_device_family,
         any_instance_family,
         any_instance_type,
         any_instance_model,
         any_resource_pool_type,
         any_gpu_abbr,
         any_customhouse_title,
         any_index_year_month,
         any_city_name,
         any_country_name,
         any_cpu_abbr,
         any_memory_volume,
         any_disk_abbr,
         any_disk2_abbr,
         any_disk_volume,
         any_disk2_volume,
         any_nic_abbr,
         any_ssd_abbr,
         any_ssd2_abbr,
         any_ssd3_abbr,
         any_cpu_vender,
         any_intellect_network_card_type,
         any_technical_class,
         any_core_components_bit,
         any_enum_bit_str
order by any_business_type,
         business_range,
         any_dept_name,
         any_plan_product_name,
         any_obs_project_type,
         any_resource_type,
         any_compute_type,
         any_device_type,
         any_device_family,
         any_instance_family,
         any_instance_type,
         any_instance_model,
         any_resource_pool_type,
         any_gpu_abbr,
         any_customhouse_title,
         any_index_year_month,
         any_city_name,
         any_country_name,
         any_cpu_abbr,
         any_memory_volume,
         any_disk_abbr,
         any_disk2_abbr,
         any_disk_volume,
         any_disk2_volume,
         any_nic_abbr,
         any_ssd_abbr,
         any_ssd2_abbr,
         any_ssd3_abbr,
         any_cpu_vender,
         any_intellect_network_card_type,
         any_technical_class,
         any_core_components_bit,
         any_enum_bit_str
