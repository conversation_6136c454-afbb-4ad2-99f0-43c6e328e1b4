select t.*,
       null region_type,
       null customer_inside_or_outside,
       '报备客户' customer_type,
       null customer_group,
       null sort_idx,
       null customer_person_type
from (
-- CVM
-- 内部业务
         select null              order_type,
                '内领业务'                                                 biz_type,
                o.industry_dept                                            industry_or_product,
                o.industry_dept                                            industry_dept,
                i.region_name                                              region_name,
                i.zone_name                                                zone_name,
                i.instance_type                                            gins_family,
                o.customer_uin                                             uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                      demand_type,
                'CVM'                                                      data_product,
                date_format(i.begin_buy_date, '%Y-%m')                     `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_core, total_core)) num,
                o.customer_name,
                o.customer_short_name,
                o.war_zone war_zone_name
         from (select max(record_version) record_version, version_group_id
               from ppl_version_group_record
               where deleted = 0
               group by version_group_id) v
                  join ppl_version_group_record_item i on v.record_version = i.record_version and i.version_group_id = v.version_group_id
                  join ppl_version_group g on g.id = v.version_group_id
                  join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and g.deleted = 0
           and i.product = 'CVM&CBS'
           and c.category = '内部业务'
           -- -- and i.ppl_order like 'PN%'
           and ((o.source = 'IMPORT' and i.is_comd = 0) or o.source = 'COMD_INTERVENE')
           and g.status <> 'REJECT' ${FILTER}
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m'),
                  o.customer_name, o.customer_short_name, o.war_zone
-- PASS产品全部算内部
         union all
         select null              order_type,
                '内领业务'                                                 biz_type,
                i.product                                                  industry_or_product,
                o.industry_dept                                            industry_dept,
                i.region_name                                              region_name,
                i.zone_name                                                zone_name,
                i.instance_type                                            gins_family,
                o.customer_uin                                             uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                      demand_type,
                'CVM'                                                      data_product,
                date_format(i.begin_buy_date, '%Y-%m')                     `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_core, total_core)) num,
                o.customer_name,
                o.customer_short_name,
                o.war_zone war_zone_name
         from (select max(record_version) record_version, version_group_id
               from ppl_version_group_record
               where deleted = 0
               group by version_group_id) v
                  join ppl_version_group_record_item i on v.record_version = i.record_version and i.version_group_id = v.version_group_id
                  join ppl_version_group g on g.id = v.version_group_id
                  join ppl_order o on o.ppl_order = i.ppl_order
                  left join (select distinct product_name from ppl_config_product_enum where flag = 'PAAS' and deleted = 0) e on i.product = e.product_name
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and g.deleted = 0
           and e.product_name is not null
           -- and i.ppl_order like 'PN%'
           and ((o.source = 'IMPORT' and i.is_comd = 0) or o.source = 'COMD_INTERVENE')
           and g.status <> 'REJECT' ${FILTER}
         group by order_type, i.product, o.industry_dept, i.region_name, i.zone_name,
                  i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m'),
                  o.customer_name, o.customer_short_name, o.war_zone
-- 外部业务
         union all
         select null              order_type,
                '外部行业'                                                 biz_type,
                o.industry_dept                                            industry_or_product,
                o.industry_dept                                            industry_dept,
                i.region_name                                              region_name,
                i.zone_name                                                zone_name,
                i.instance_type                                            gins_family,
                o.customer_uin                                             uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                      demand_type,
                'CVM'                                                      data_product,
                date_format(i.begin_buy_date, '%Y-%m')                     `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_core, total_core)) num,
                o.customer_name,
                o.customer_short_name,
                o.war_zone war_zone_name
         from (select max(record_version) record_version, version_group_id
               from ppl_version_group_record
               where deleted = 0
               group by version_group_id) v
                  join ppl_version_group_record_item i on v.record_version = i.record_version and i.version_group_id = v.version_group_id
                  join ppl_version_group g on g.id = v.version_group_id
                  join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and g.deleted = 0
           and i.product = 'CVM&CBS'
           and (o.industry_dept is null or c.category <> '内部业务')
           -- and i.ppl_order like 'PN%'
           and ((o.source = 'IMPORT' and i.is_comd = 0) or o.source = 'COMD_INTERVENE')
           and g.status <> 'REJECT' ${FILTER}
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m'),
                  o.customer_name, o.customer_short_name, o.war_zone
         union all
         -- GPU
-- 内部业务
         select null                    order_type,
                '内领业务'                                                       biz_type,
                o.industry_dept                                                  industry_or_product,
                o.industry_dept                                                  industry_dept,
                i.region_name                                                    region_name,
                i.zone_name                                                      zone_name,
                i.instance_type                                                  gins_family,
                o.customer_uin                                                   uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                            demand_type,
                'GPU'                                                            data_product,
                date_format(i.begin_buy_date, '%Y-%m')                           `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)) num,
                o.customer_name,
                o.customer_short_name,
                o.war_zone war_zone_name
         from (select max(record_version) record_version, version_group_id
               from ppl_version_group_record
               where deleted = 0
               group by version_group_id) v
                  join ppl_version_group_record_item i on v.record_version = i.record_version and i.version_group_id = v.version_group_id
                  join ppl_version_group g on g.id = v.version_group_id
                  join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and g.deleted = 0
           and i.product = 'GPU(裸金属&CVM)'
           and c.category = '内部业务'
           -- and i.ppl_order like 'PN%'
           and ((o.source = 'IMPORT' and i.is_comd = 0) or o.source = 'COMD_INTERVENE')
           and g.status <> 'REJECT' ${FILTER}
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m'),
                  o.customer_name, o.customer_short_name, o.war_zone
-- 外部业务
         union all
         select null                    order_type,
                '外部行业'                                                       biz_type,
                o.industry_dept                                                  industry_or_product,
                o.industry_dept                                                  industry_dept,
                i.region_name                                                    region_name,
                i.zone_name                                                      zone_name,
                i.instance_type                                                  gins_family,
                o.customer_uin                                                   uin,
                case i.demand_type
                    when 'NEW' then '新增'
                    when 'ELASTIC' then '弹性'
                    when 'RETURN' then '退回'
                    else '(空值)' end                                            demand_type,
                'GPU'                                                            data_product,
                date_format(i.begin_buy_date, '%Y-%m')                           `year_month`,
                sum(if(i.demand_type = 'RETURN', -total_gpu_num, total_gpu_num)) num,
                o.customer_name,
                o.customer_short_name,
                o.war_zone war_zone_name
         from (select max(record_version) record_version, version_group_id
               from ppl_version_group_record
               where deleted = 0
               group by version_group_id) v
                  join ppl_version_group_record_item i on v.record_version = i.record_version and i.version_group_id = v.version_group_id
                  join ppl_version_group g on g.id = v.version_group_id
                  join ppl_order o on o.ppl_order = i.ppl_order
                  left join ppl_config_stat_industry_dept_class c
                            on o.industry_dept = c.industry_dept
         where i.deleted = 0
           and o.deleted = 0
           and o.industry_dept <> '中长尾'
           and g.deleted = 0
           and i.product = 'GPU(裸金属&CVM)'
           and (o.industry_dept is null or c.category <> '内部业务')
           -- and i.ppl_order like 'PN%'
           and ((o.source = 'IMPORT' and i.is_comd = 0) or o.source = 'COMD_INTERVENE')
           and g.status <> 'REJECT' ${FILTER}
         group by order_type, o.industry_dept, i.region_name, i.zone_name, i.instance_type,
                  o.customer_uin,
                  i.demand_type, date_format(i.begin_buy_date, '%Y-%m'),
                  o.customer_name, o.customer_short_name, o.war_zone) t
where t.`year_month` >= ?
  and t.num <> 0