select
    customer_type,
    any(stat_time) as `date`,
    formatDateTime(date_add(day,-1,now()),'%Y-%m') as h_ym,
    formatDateTime(now(),'%Y-%m') as ym,
    multiIf(
    ym='2023-09',count(`avg_forecast_num@2023-09`),
    ym='2023-10',count(`avg_forecast_num@2023-10`),
    ym='2023-11',count(`avg_forecast_num@2023-11`),
    ym='2023-12',count(`avg_forecast_num@2023-12`),
    ym='2024-01',count(`avg_forecast_num@2024-01`),
    ym='2024-02',count(`avg_forecast_num@2024-02`),
    ym='2024-03',count(`avg_forecast_num@2024-03`),
    ym='2024-04',count(`avg_forecast_num@2024-04`),
    ym='2024-05',count(`avg_forecast_num@2024-05`),
    ym='2024-06',count(`avg_forecast_num@2024-06`),
    ym='2024-07',count(`avg_forecast_num@2024-07`),
    ym='2024-08',count(`avg_forecast_num@2024-08`),
    ym='2024-09',count(`avg_forecast_num@2024-09`),
    1)
    as `532版预测量`,
    multiIf(
    h_ym='2023-09',count(`month_avg_not_distinct_change_bill_num@2023-09`),
    h_ym='2023-10',count(`month_avg_not_distinct_change_bill_num@2023-10`),
    h_ym='2023-11',count(`month_avg_not_distinct_change_bill_num@2023-11`),
    h_ym='2023-12',count(`month_avg_not_distinct_change_bill_num@2023-12`),
    h_ym='2024-01',count(`month_avg_not_distinct_change_bill_num@2024-01`),
    h_ym='2024-02',count(`month_avg_not_distinct_change_bill_num@2024-02`),
    h_ym='2024-03',count(`month_avg_not_distinct_change_bill_num@2024-03`),
    h_ym='2024-04',count(`month_avg_not_distinct_change_bill_num@2024-04`),
    h_ym='2024-05',count(`month_avg_not_distinct_change_bill_num@2024-05`),
    h_ym='2024-06',count(`month_avg_not_distinct_change_bill_num@2024-06`),
    h_ym='2024-07',count(`month_avg_not_distinct_change_bill_num@2024-07`),
    h_ym='2024-08',count(`month_avg_not_distinct_change_bill_num@2024-08`),
    h_ym='2024-09',count(`month_avg_not_distinct_change_bill_num@2024-09`),
    1)
    as `532版计费变化量`,
    multiIf(
    h_ym='2023-09',count(`month_avg_not_distinct_cur_bill_num@2023-09`),
    h_ym='2023-10',count(`month_avg_not_distinct_cur_bill_num@2023-10`),
    h_ym='2023-11',count(`month_avg_not_distinct_cur_bill_num@2023-11`),
    h_ym='2023-12',count(`month_avg_not_distinct_cur_bill_num@2023-12`),
    h_ym='2024-01',count(`month_avg_not_distinct_cur_bill_num@2024-01`),
    h_ym='2024-02',count(`month_avg_not_distinct_cur_bill_num@2024-02`),
    h_ym='2024-03',count(`month_avg_not_distinct_cur_bill_num@2024-03`),
    h_ym='2024-04',count(`month_avg_not_distinct_cur_bill_num@2024-04`),
    h_ym='2024-05',count(`month_avg_not_distinct_cur_bill_num@2024-05`),
    h_ym='2024-06',count(`month_avg_not_distinct_cur_bill_num@2024-06`),
    h_ym='2024-07',count(`month_avg_not_distinct_cur_bill_num@2024-07`),
    h_ym='2024-08',count(`month_avg_not_distinct_cur_bill_num@2024-08`),
    h_ym='2024-09',count(`month_avg_not_distinct_cur_bill_num@2024-09`),
    1)
    as `532版计费存量`,
    multiIf(
    h_ym='2023-09',count(`month_avg_not_distinct_cur_serve_num@2023-09`),
    h_ym='2023-10',count(`month_avg_not_distinct_cur_serve_num@2023-10`),
    h_ym='2023-11',count(`month_avg_not_distinct_cur_serve_num@2023-11`),
    h_ym='2023-12',count(`month_avg_not_distinct_cur_serve_num@2023-12`),
    h_ym='2024-01',count(`month_avg_not_distinct_cur_serve_num@2024-01`),
    h_ym='2024-02',count(`month_avg_not_distinct_cur_serve_num@2024-02`),
    h_ym='2024-03',count(`month_avg_not_distinct_cur_serve_num@2024-03`),
    h_ym='2024-04',count(`month_avg_not_distinct_cur_serve_num@2024-04`),
    h_ym='2024-05',count(`month_avg_not_distinct_cur_serve_num@2024-05`),
    h_ym='2024-06',count(`month_avg_not_distinct_cur_serve_num@2024-06`),
    h_ym='2024-07',count(`month_avg_not_distinct_cur_serve_num@2024-07`),
    h_ym='2024-08',count(`month_avg_not_distinct_cur_serve_num@2024-08`),
    h_ym='2024-09',count(`month_avg_not_distinct_cur_serve_num@2024-09`),
    1)
    as `532版服务存量`,
    multiIf(
    h_ym='2023-09',count(`month_avg_not_distinct_change_serve_num@2023-09`),
    h_ym='2023-10',count(`month_avg_not_distinct_change_serve_num@2023-10`),
    h_ym='2023-11',count(`month_avg_not_distinct_change_serve_num@2023-11`),
    h_ym='2023-12',count(`month_avg_not_distinct_change_serve_num@2023-12`),
    h_ym='2024-01',count(`month_avg_not_distinct_change_serve_num@2024-01`),
    h_ym='2024-02',count(`month_avg_not_distinct_change_serve_num@2024-02`),
    h_ym='2024-03',count(`month_avg_not_distinct_change_serve_num@2024-03`),
    h_ym='2024-04',count(`month_avg_not_distinct_change_serve_num@2024-04`),
    h_ym='2024-05',count(`month_avg_not_distinct_change_serve_num@2024-05`),
    h_ym='2024-06',count(`month_avg_not_distinct_change_serve_num@2024-06`),
    h_ym='2024-07',count(`month_avg_not_distinct_change_serve_num@2024-07`),
    h_ym='2024-08',count(`month_avg_not_distinct_change_serve_num@2024-08`),
    h_ym='2024-09',count(`month_avg_not_distinct_change_serve_num@2024-09`),
    1)
    as `532版服务变化量`,
    multiIf(
    h_ym='2023-09',count(`daily_not_distinct_cur_bill_num@2023-09`),
    h_ym='2023-10',count(`daily_not_distinct_cur_bill_num@2023-10`),
    h_ym='2023-11',count(`daily_not_distinct_cur_bill_num@2023-11`),
    h_ym='2023-12',count(`daily_not_distinct_cur_bill_num@2023-12`),
    h_ym='2024-01',count(`daily_not_distinct_cur_bill_num@2024-01`),
    h_ym='2024-02',count(`daily_not_distinct_cur_bill_num@2024-02`),
    h_ym='2024-03',count(`daily_not_distinct_cur_bill_num@2024-03`),
    h_ym='2024-04',count(`daily_not_distinct_cur_bill_num@2024-04`),
    h_ym='2024-05',count(`daily_not_distinct_cur_bill_num@2024-05`),
    h_ym='2024-06',count(`daily_not_distinct_cur_bill_num@2024-06`),
    h_ym='2024-07',count(`daily_not_distinct_cur_bill_num@2024-07`),
    h_ym='2024-08',count(`daily_not_distinct_cur_bill_num@2024-08`),
    h_ym='2024-09',count(`daily_not_distinct_cur_bill_num@2024-09`),
    1)
    as `532版计费变化量(月切)`,
    multiIf(
    h_ym='2023-09',count(`daily_not_distinct_cur_serve_num@2023-09`),
    h_ym='2023-10',count(`daily_not_distinct_cur_serve_num@2023-10`),
    h_ym='2023-11',count(`daily_not_distinct_cur_serve_num@2023-11`),
    h_ym='2023-12',count(`daily_not_distinct_cur_serve_num@2023-12`),
    h_ym='2024-01',count(`daily_not_distinct_cur_serve_num@2024-01`),
    h_ym='2024-02',count(`daily_not_distinct_cur_serve_num@2024-02`),
    h_ym='2024-03',count(`daily_not_distinct_cur_serve_num@2024-03`),
    h_ym='2024-04',count(`daily_not_distinct_cur_serve_num@2024-04`),
    h_ym='2024-05',count(`daily_not_distinct_cur_serve_num@2024-05`),
    h_ym='2024-06',count(`daily_not_distinct_cur_serve_num@2024-06`),
    h_ym='2024-07',count(`daily_not_distinct_cur_serve_num@2024-07`),
    h_ym='2024-08',count(`daily_not_distinct_cur_serve_num@2024-08`),
    h_ym='2024-09',count(`daily_not_distinct_cur_serve_num@2024-09`),
    1)
    as `532版服务变化量(月切)`
from
    cloud_demand.${tableName}
where stat_time = toDate(now())
  and data_product = 'CVM'
group by customer_type