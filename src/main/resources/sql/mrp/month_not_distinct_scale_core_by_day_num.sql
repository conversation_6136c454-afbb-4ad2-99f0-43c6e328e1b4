select '头部客户'                                                             order_type,
       case biz_range_type
           when '外部业务' then '外部行业'
           when '外部行业' then '外部行业'
           when '内部业务' then '内领业务'
           when '内领业务' then '内领业务'
           else '(空值)' end                                                  r_biz_type,
       industry_dept                                                        industry_or_product,
       industry_dept,
       customhouse_title                                                    region_type,
       region_name,
       zone_name,
       instance_type                                                        gins_family,
       uin,
       case is_inner when 1 then '内部' when 0 then '外部' else '(空值)' end      customer_inside_or_outside,
       customer_tab_type                                                    r_customer_type,
       war_zone                                                             war_zone_name,
       null                                                     customer_group,
       customer_short_name,
       customer_name,
       case customer_type when 1 then '企业' when 0 then '个人' else '(空值)' end customer_person_type,
       if(product = 'GPU','GPU','CVM')                                     data_product,
       concat(toString(`year`), '-', if(`month` < 10, concat('0', toString(`month`)), toString(`month`)))   `year_month`,
       sum(total_bill_core_day)                                        bill_core_by_day_num,
       sum(total_service_core_day)                                     serve_core_by_day_num
from dws_txy_scale_core_day_mi
where
--     customer_tab_type <> '中长尾客户' -- 中长尾判定标准变了
    (customer_short_name in (?)  or uin in (?) or customer_tab_type != '中长尾客户') -- 前两个是新头部分类，后面是老客户分类，为了兼容两种，这里都查
  and year_month >= ?
  and product in ('GPU','CVM')
group by r_biz_type, industry_dept, customhouse_title, region_name, zone_name, instance_type,
         uin, customer_inside_or_outside, customer_tab_type, war_zone, customer_group, customer_short_name,
         customer_name, customer_person_type, data_product, `year_month`