select
t.res_type,
t.plan_product_name,
t.cvm_gins_type,
t.phy_device_type
from (
    select
    res_type,
    plan_product_name,
    '(空值)' as cvm_gins_type,
    phy_device_type
    from
    std_crp.dwd_sop_report_return_device
    where
    version='${version}'
union all
    select
    res_type,
    plan_product_name,
    cvm_gins_type,
    '(空值)' as phy_device_type
    from
    std_crp.dwd_sop_report_return_cvm
    where
    version='${version}'
    and is_executed = 1
union all
select
    res_type,
    plan_product_name,
    cvm_gins_type,
    '(空值)' as phy_device_type
from
    std_crp.dwd_sop_report_return_cvm_ext
where
    version='${version}'
    and dept_name != '算力平台'
    and is_executed = 0
    and plan_type = 'IN_PLAN'
) t
group by t.res_type,t.plan_product_name,t.cvm_gins_type,t.phy_device_type