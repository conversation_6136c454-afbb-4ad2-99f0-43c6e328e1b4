-- cdb售卖数据
select
    null as biz_range_type,
    '头部客户' as new_customer_type,
    '重点项目' as project_type,
    product as product_class,
    industry_dept as origin_industry_dept,
    customhouse_title ,
    region_name,
    zone_name,
    instance_model as instance_type,
    uin,
    customer_short_name,
    null as customer_tab_type,
    product,
    null as app_role,
    year_month ,
    sum(change_bill_mem_from_last_month) as diff_bill_mem_num, -- 计费变化量【内存】
    sum(change_service_mem_from_last_month) as diff_service_mem_num, -- 服务变化量【内存】
    sum(cur_bill_mem) as stock_bill_mem_num, -- 计费存量【内存】
    sum(cur_service_mem) as stock_service_mem_num, -- 服务存量【内存】
    sum(change_bill_disk_from_last_month) as diff_bill_disk_num, -- 计费变化量【存储】
    sum(change_service_disk_from_last_month) as diff_service_disk_num, -- 服务变化量【存储】
    sum(cur_bill_disk) as stock_bill_disk_num, -- 计费存量【存储】
    sum(cur_service_disk) as stock_service_disk_num -- 服务存量【存储】
from std_crp.dwd_txy_cdb_scale_df
where stat_time in ('${month_end_day_range}') -- 限制起始年月
group by
    industry_dept,
    customhouse_title,
    region_name,
    zone_name,
    instance_type,
    uin,
    customer_short_name,
    product,
    year_month