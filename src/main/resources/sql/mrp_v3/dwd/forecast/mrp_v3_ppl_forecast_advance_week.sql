select '外部行业'              biz_type,  -- 业务类型（默认外部）
       industry_dept,                   -- 行业部门
       customhouse_title,               -- 境内外
       region_name,                     -- 地域
       null              zone_name,     -- 暂时没有 zone
       instance_type,                   -- 机型
       null              uin,           -- 暂时没有 uin
       multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
               demand_type = 'RETURN', '退回',
               '(空值)') `demand_type`, -- 需求类型
       product_type      data_product,  -- 产品
       product           product_class, -- 产品子类
       year_month,                      -- 预测年月
       customer_short_name,             -- 客户简称
       war_zone          war_zone_name, -- crp 战区
       sum(w6)  as       sum_w6,        -- 提前 6 周预测量
       sum(w13) as       sum_w13        -- 提前 13 周预测量
from std_crp.dwd_fio_forecast_base_item_df
where product in ('CVM&CBS', ${PAAS})
  and stat_time = '${stat_time}'
  and product_type = 'CVM'
  and is_mod = 1 -- 干预后
  and `year_month` >= '${start_year_month}'
group by product,
         biz_type,
         industry_dept,
         customhouse_title,
         region_name,
         zone_name,
         instance_type,
         uin,
         `demand_type`,
         data_product,
         `year_month`,
         customer_short_name,
         war_zone_name