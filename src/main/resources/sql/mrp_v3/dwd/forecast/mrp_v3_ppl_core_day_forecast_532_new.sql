select multiIf(category = '内部业务' or product in (${PAAS}), '内领业务', '外部行业') biz_type,          -- 业务类型
       industry_dept,                                                                                    -- 行业部门
       multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
               customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
               '(空值)')                                                              customhouse_title, -- 境内外
       region_name,                                                                                      -- 地域
       zone_name,                                                                                        -- 可用区
       instance_type,                                                                                    -- 机型
       customer_uin                                                                   uin,               -- 客户 uin
       multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
               demand_type = 'RETURN', '退回',
               '(空值)')                                                              `demand_type`,     -- 需求类型
       'CVM'                                                                          data_product,      -- 产品
       product                                                                        product_class,     -- 产品子类
       formatDateTime(begin_buy_date, '%Y-%m')                                        `year_month`,      -- 预测年月
       customer_short_name,                                                                              -- 客户简称
       war_zone                                                                       war_zone_name,     -- crp 战区
       is_spike,                                                                                         -- 是否为毛刺
       sum(if(demand_type = '退回', -min_core_day, min_core_day))                     min_core_day_num,  -- 核天 min
       sum(if(demand_type = '退回', -max_core_day, max_core_day))                     max_core_day_num   -- 核天 max
from std_crp.dws_crp_ppl_item_version_532_new_core_day_mif
where product in ('CVM&CBS', ${PAAS})
  and ((source in ('IMPORT', 'COMD_INTERVENE', 'APPLY_AUTO_FILL') and is_comd = 0) or source = 'FORECAST')
  and `year_month` >= '${start_year_month}'
group by product,
         biz_type,
         industry_dept,
         customhouse_title,
         region_name,
         zone_name,
         instance_type,
         uin,
         `demand_type`,
         data_product,
         `year_month`,
         customer_short_name,
         war_zone_name,
         is_spike
union all
select multiIf(category = '内部业务', '内领业务', '外部行业') biz_type,          -- 业务类型
       industry_dept,                                                                                    -- 行业部门
       multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
               customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
               '(空值)')                                                              customhouse_title, -- 境内外
       region_name,                                                                                      -- 地域
       zone_name,                                                                                        -- 可用区
       instance_type,                                                                                    -- 机型
       customer_uin                                                                   uin,               -- 客户 uin
       multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
               demand_type = 'RETURN', '退回',
               '(空值)')                                                              `demand_type`,     -- 需求类型
       'GPU'                                                                          data_product,      -- 产品
       product                                                                        product_class,     -- 产品子类
       formatDateTime(begin_buy_date, '%Y-%m')                                        `year_month`,      -- 预测年月
       customer_short_name,                                                                              -- 客户简称
       war_zone                                                                       war_zone_name,     -- crp 战区
       is_spike,                                                                                         -- 是否为毛刺
       sum(if(demand_type = '退回', -min_core_day, min_core_day))                     min_core_day_num,  -- 核天 min
       sum(if(demand_type = '退回', -max_core_day, max_core_day))                     max_core_day_num   -- 核天 max
from std_crp.dws_crp_ppl_item_version_532_new_core_day_mif
where product in ('GPU(裸金属&CVM)')
  and ((source in ('IMPORT', 'COMD_INTERVENE', 'APPLY_AUTO_FILL') and is_comd = 0) or source = 'FORECAST')
  and `year_month` >= '${start_year_month}'
group by product,
         biz_type,
         industry_dept,
         customhouse_title,
         region_name,
         zone_name,
         instance_type,
         uin,
         `demand_type`,
         data_product,
         `year_month`,
         customer_short_name,
         war_zone_name,
         is_spike