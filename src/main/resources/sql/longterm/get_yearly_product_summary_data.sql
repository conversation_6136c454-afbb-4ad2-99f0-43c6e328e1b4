select demand_year, product,
       sum(case when product like '%GPU%' then
                    (case when demand_type='RETURN' then -abs(gpu_num) else gpu_num end)
                else
                    (case when demand_type='RETURN' then -abs(core_num) else core_num end)
           end) as net_num,
       (case when product like '%GPU%' then '卡' else '核' end) as unit
from longterm_version_group_record_item
where version_group_record_id=:versionGroupRecordId
group by demand_year, product



