-- 已交付量（需求月份）
-- 已采购量
select
    p_ym,
    dim_region,
    dim_region_class,
    dim_industry,
    dim_customer,
    sum(num) as total_num,
    sum(cores) as total_core_num
from
    cloud_demand.ads_tcres_demand_annual_excueted_report
where p_index = '已交付量（需求月份）'
  and version = '${int_stat_time}' -- 20240707(max = yesterday)
  and p_ym >= '${start_year_month}' -- 2024-01
  and p_ym <= '${end_year_month}' -- 2024-12
  and cores != 0
group by
    p_ym,
    dim_region,
    dim_region_class,
    dim_industry,
    dim_customer