-- 查询审批概览
SELECT a.instance_type,
       a.win_rate,
       a.instance_model,
       (CASE
            WHEN a.`region_name` IN (:overSeaRegions) THEN '境外'
            ELSE '国内' END)            AS customhouse_title,
       a.region_name,
       a.zone_name,
       YEAR(a.begin_buy_date) as year,
       MONTH(a.begin_buy_date) as month,
       b.war_zone,
       b.center,
       b.customer_uin,
       b.customer_short_name,
       b.submit_user,
       a.gpu_type,
       a.product,

       -- 弹性需求和新增需求合并为新增需求
       (CASE
            WHEN a.`demand_type` = 'ELASTIC' THEN 'NEW'
            ELSE a.`demand_type` END) AS demand_type2,

       -- 关键逻辑说明：找到每个ppl_order的当前审批大版本下的最小的record_id(审批前)和最大的record_id(审批后)
       (CASE
            WHEN a.audit_record_id IN (
                                       SELECT MIN(id)
                                       FROM `ppl_order_audit_record`
                                       WHERE deleted = 0
                                         AND version_id IN (:currentVersionIds)
                                         AND ppl_order IN (:pplOrder)
                                       GROUP BY ppl_order
                                       )
                THEN 'BEFORE'
            WHEN a.audit_record_id IN (SELECT MAX(id)
                                       FROM `ppl_order_audit_record`
                                       WHERE deleted = 0
                                         AND ppl_order IN (:pplOrder)
                                         AND version_id IN (:currentVersionIds)
                                       GROUP BY ppl_order)
                THEN 'AFTER'
           END)                       AS record_type,

        (CASE
        WHEN a.ppl_order LIKE 'PN%' THEN 'INPUT' ELSE 'SYSTEM_INPUT' END) AS ppl_source,
        COALESCE(SUM(a.total_core_apply_before), 0) AS total_core_apply_before,
        COALESCE(SUM(a.total_core_apply_after), 0) AS total_core_apply_after,

    COALESCE(SUM(a.total_gpu_num_apply_before), 0) AS total_gpu_num_apply_before,
    COALESCE(SUM(a.total_gpu_num_apply_after), 0) AS total_gpu_num_apply_after,

       SUM(a.total_core)                                                           AS total_core,
       SUM(a.total_gpu_num)                                                           AS total_gpu_num,
       SUM(a.instance_num)                                                           AS total_instance_num,

    COALESCE(sum(a.total_memory), 0)    as  total_memory,
    COALESCE(sum(a.total_cos_storage), 0)  as  total_cos_storage,
    COALESCE(sum(a.total_database_storage), 0)  as  total_database_storage

FROM `ppl_order_audit_record_item` a
         JOIN `ppl_order` b ON a.ppl_order = b.`ppl_order`
WHERE a.deleted = 0
  AND b.`deleted` = 0
  AND a.ppl_order IN (:pplOrder) ${FILTER} -- 其它查询条件

GROUP BY a.instance_model, customhouse_title, a.region_name, a.zone_name,a.product,
    YEAR(a.begin_buy_date), MONTH(a.begin_buy_date), b.war_zone, b.customer_uin, b.customer_short_name,
    demand_type2, record_type, ppl_source
HAVING record_type IN ('BEFORE', 'AFTER')