
select c.industry_dept,
       a.accept_gpu	,
       a.affinity_type	,
       a.affinity_value	,
       a.alternative_instance_type	,
       a.apply_discount	,
       b.apply_num as apply_instance_num	,
       b.apply_core as apply_total_core	,
       b.apply_order_id as yunxiao_order_id ,
       a.begin_buy_date	,
       a.begin_elastic_date	,
       a.bill_type	,
       a.biz_detail	,
       a.biz_scene	,
       a.business_cpq	,
       a.cpq	,
       c.submit_user as creator	,
       a.data_disk_num	,
       a.data_disk_storage	,
       a.data_disk_type	,
       a.demand_scene	,
       a.demand_type	,
       a.end_buy_date	,
       a.end_elastic_date	,
       a.forecast_model_detail_id	,
       a.gpu_num	,
       a.gpu_product_type	,
       a.gpu_type	,
       '' as import_type	,
       a.instance_model	,
       case when d.id is null then a.instance_num else d.instance_num end as instance_num,
       a.instance_type	,
       a.is_accept_adjust	,
       a.is_comd	,
       a.note	,
       a.ppl_id	,
       a.ppl_order	,
       a.product	,
       a.project_name	,
       a.region_name	,
       a.sale_duration	,
       a.sale_duration_year	,
       a.service_time	,
       a.source_ppl_id	,
       a.status	,
       a.system_disk_num	,
       a.system_disk_storage	,
       a.system_disk_type	,
       case when d.id is null then a.total_core else d.total_core end as total_core,
       a.total_disk	,
       case when d.id is null then a.total_gpu_num else d.total_gpu_num end as total_gpu_num,
       a.win_rate	,
       a.zone_name ,
       d.id ,   -- 查出来id为空则表示需要insert，否则需要update
       d.occupy_others_core_num,
       d.occupy_others_ppl_ids,
       d.occupied_core_num,
       d.occupied_by_ppl_ids,
       d.consensus_match_type	,
       d.consensus_status	,
       c.customer_uin,
       a.instance_num_apply_after,
       a.total_core_apply_after,
       a.total_gpu_num_apply_after
from ppl_applied_supply b
left join ppl_item a on a.ppl_id = b.ppl_id
left join ppl_order c on a.ppl_order = c.ppl_order
left join ppl_version_group_record_item d on d.ppl_id = b.ppl_id and d.version_group_record_id = ? and d.deleted = 0
where
a.deleted = 0 and c.deleted = 0
and b.version_code = ? and b.apply_status not in ('已取消','已强制取消','驳回','草稿') and a.ppl_id like 'PE%'
and a.product = ? and c.industry_dept = ?
and c.source in('SYNC_YUNXIAO', 'APPLY_AUTO_FILL')