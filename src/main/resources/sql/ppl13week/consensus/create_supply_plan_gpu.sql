SELECT
       c.industry_dept as industry_dept ,
       'GPU(裸金属&CVM)' as product ,
       a.supply_gpu_id as supply_id ,
       a.ppl_id ,
       a.country_name ,
       a.region ,
       a.`zone` ,
       a.region_name ,
       a.zone_name ,
       a.remark ,
       a.match_type ,
       a.match_instance_type ,
       a.host_type ,
       a.host_num ,
       a.instance_num ,
       a.cpu_num as instance_total_core ,
       a.gpu_num as instance_total_gpu ,
       b.status as ppl_item_status ,
       b.source_ppl_id as source_ppl_id ,
       b.total_core as normal_core_num,
       b.total_core as intervene_core_num,
       b.begin_buy_date as demand_date,
       b.instance_type as demand_instance_type,
       b.zone_name as demand_zone_name,
       b.begin_buy_date as match_demand_date
FROM ppl_stock_supply_gpu_detail a
	LEFT JOIN ppl_version_group_record_item b ON a.ppl_id = b.ppl_id
	LEFT JOIN ppl_order c on b.ppl_order = c.ppl_order

WHERE a.deleted = 0
	AND b.deleted = 0
	AND c.deleted = 0
	AND b.version_group_record_id IN (
		SELECT max(id)
		FROM ppl_version_group_record
		WHERE deleted = 0
			AND version_group_id IN (
				SELECT id
				FROM ppl_version_group
				WHERE deleted = 0
					AND version_code = ?
					AND industry_dept = ?
					AND product = ?
			)
	)
	AND a.supply_gpu_id = (
		SELECT id
		FROM ppl_stock_supply_gpu a
		WHERE a.version_code =?
		order by id desc limit 1
	);