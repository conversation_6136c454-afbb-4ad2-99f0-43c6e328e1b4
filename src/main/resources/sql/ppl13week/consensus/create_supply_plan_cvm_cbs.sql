select C.version_code ,
       A.industry_dept ,
       A.product ,
       D.supply_id ,
       A.ppl_id ,
       D.country_name ,
       D.region ,
       D.`zone` ,
       D.region_name ,
       D.zone_name ,
       D.remark ,
       D.match_type ,
       D.match_instance_type ,
       D.host_type ,
       D.host_num ,
       D.instance_num ,
       D.instance_total_core ,
       0 as instance_total_gpu ,
       E.status as ppl_item_status ,
       E.source_ppl_id as source_ppl_id ,
       G.source ,
       case when F.id is null then E.total_core else F.total_core end as normal_core_num ,
       E.total_core as intervene_core_num,
       A.ppl_id as split_ppl_id,
       E.begin_buy_date as demand_date,
       E.instance_type as demand_instance_type,
       E.zone_name as demand_zone_name,
       D.match_demand_date as match_demand_date
from ppl_stock_supply_rsp D
         left join ppl_stock_supply_req A on D.req_id = A.id
         left join ppl_version_group_record B on A.version_group_record_id = B.id
         left join ppl_version_group C on B.version_group_id = C.id
         left join ppl_version_group_record_item E
             on A.ppl_id = E.ppl_id and E.deleted = 0 and A.version_group_record_id = E.version_group_record_id
         left join ppl_item F on E.source_ppl_id = F.ppl_id
         left join ppl_order G on A.ppl_order = G.ppl_order
where C.version_code  = ? and A.industry_dept = ? and A.product = ?
        -- 暂时只处理cvm
        and D.instance_total_core is not null and D.instance_total_core > 0
        -- 取版本中最后一次对冲结果
        and D.supply_id in (
            select max(id) from ppl_stock_supply
            where version_code = ? and deleted = 0
        )
        and D.deleted = 0 and A.deleted = 0