SELECT a.demand_type,
        YEAR(a.begin_buy_date) AS year,
        MONTH(a.begin_buy_date) AS month,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN a.instance_num
                        ELSE 0 END), 0) AS v2Num,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN a.instance_num
                        ELSE 0 END), 0) AS v1Num,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN a.total_core
                        ELSE 0 END), 0) AS v2Core,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN a.total_core
                        ELSE 0 END), 0) AS v1Core,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN a.total_gpu_num
                        ELSE 0 END), 0) AS v2Gpu,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN a.total_gpu_num
                        ELSE 0 END), 0) AS v1Gpu,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN a.apply_instance_num
                        ELSE 0 END), 0) AS v2applyInstanceNum,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN a.apply_instance_num
                        ELSE 0 END), 0) AS v1applyInstanceNum,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN a.apply_total_core
                        ELSE 0 END), 0) AS v2ApplyCore,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN a.apply_total_core
                        ELSE 0 END), 0) AS v1ApplyCore,

       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v2GroupRecordId THEN
                                (a.system_disk_storage + a.data_disk_storage * a.data_disk_num) *
                                a.instance_num
                        ELSE 0 END), 0) AS v2Storage,
       COALESCE(SUM(CASE
                        WHEN a.version_group_record_id = :v1GroupRecordId THEN
                                (a.system_disk_storage + a.data_disk_storage * a.data_disk_num) *
                                a.instance_num
                        ELSE 0 END), 0) AS v1Storage

FROM `ppl_version_group_record_item` a
         LEFT JOIN `ppl_order` b ON a.ppl_order = b.`ppl_order`
WHERE a.`deleted` = 0 AND b.`deleted` = 0
  AND version_group_record_id IN (:v1GroupRecordId, :v2GroupRecordId) ${FILTER} -- 额外的条件
GROUP BY a.demand_type, YEAR(a.begin_buy_date), MONTH(a.begin_buy_date)