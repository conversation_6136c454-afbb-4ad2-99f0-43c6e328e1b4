-- 客户明细 - 最新版
SELECT
    `year`,`month`,
    `customer_short_name`, `customer_uin`,`industry_dept`,`war_zone`,
    `demand_type`,`demand_scene`,`bill_type`,
    `region_name`,`zone_name`,`instance_type`,`instance_model`,
    '' as yunxiao_order_status,
    (CASE WHEN category IS NULL or category = '(空值)' THEN '未分类' ELSE `category` END) AS category,
    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_core ELSE 0 END) AS demandTotalCore,
    0 AS appliedTotalCore,

    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_disk ELSE 0 END) AS demandTotalDisk,
    0 AS appliedTotalDisk,

    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN instance_num ELSE 0 END) AS demandTotalInstanceNum,
    0 AS appliedTotalInstanceNum,

    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_gpu_num ELSE 0 END) AS demandTotalGpuNum,
        0 AS appliedTotalGpuNum

FROM dws_crp_ppl_item_version_newest_cf
WHERE 1=1
   ${FILTER}
-- 过滤条件
GROUP BY `year`,`month`,
    `customer_short_name`, `customer_uin`,`industry_dept`,`war_zone`,
    `demand_type`,`demand_scene`,`bill_type`,
    `region_name`,`zone_name`,`instance_type`,`instance_model`, category

-- 预约单只能取最新的
union all
SELECT
    `year`,`month`,
    `customer_short_name`, `customer_uin`,`industry_dept`,`war_zone`,
    `demand_type`,`demand_scene`,`bill_type`,
    `region_name`,`zone_name`,`instance_type`,`instance_model`,
    yunxiao_order_status,
    (CASE WHEN category IS NULL or category = '(空值)' THEN '未分类' ELSE `category` END) AS category,

    0 AS demandTotalCore,
    SUM(CASE WHEN `status` = 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_core_apply_after` ELSE 0 END) AS appliedTotalCore,

    0 AS demandTotalDisk,
    SUM(CASE WHEN `status`= 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_disk` ELSE 0 END) AS appliedTotalDisk,

    0 AS demandTotalInstanceNum,
    SUM(CASE WHEN `status`= 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `instance_num_apply_after` ELSE 0 END) AS appliedTotalInstanceNum,

    0 AS demandTotalGpuNum,
    SUM(CASE WHEN `status`= 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_gpu_num_apply_after` ELSE 0 END) AS appliedTotalGpuNum


FROM dwd_crp_ppl_item_cf
WHERE 1=1
    ${FILTER}
-- 过滤条件
GROUP BY `year`,`month`,
    `customer_short_name`, `customer_uin`,`industry_dept`,`war_zone`,
    `demand_type`,`demand_scene`,`bill_type`,
    `region_name`,`zone_name`,`instance_type`,`instance_model`,yunxiao_order_status, category