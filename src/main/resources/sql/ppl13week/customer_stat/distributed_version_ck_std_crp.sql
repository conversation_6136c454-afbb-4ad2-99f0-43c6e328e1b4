-- 客户分布 - 版本化数据
SELECT
    `year`,`month`,`region_name`,`zone_name`,`customer_short_name`,`war_zone`,
    `instance_type`,`instance_model`, area_name, customhouse_title,
    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_core ELSE 0 END) AS demandTotalCore,
    0 AS appliedTotalCore,
    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_gpu_num ELSE 0 END) AS demandTotalGpuNum,
    0 AS appliedTotalGpuNum,
    SUM(CASE WHEN `source` in ('IMPORT','COMD_INTERVENE','APPLY_AUTO_FILL','FORECAST') and is_comd != 1 THEN total_disk ELSE 0 END) AS demandTotalDisk,
    0 AS appliedTotalDisk
FROM dwd_crp_ppl_item_version_cf
WHERE version_code = ?
    ${FILTER}
-- 过滤条件
GROUP BY `year`,`month`,`region_name`,`zone_name`, `customer_short_name`,`war_zone`,
    `instance_type`,`instance_model`, area_name, customhouse_title

union all
-- 预约单部分，只能用最新的数据
SELECT
    `year`,`month`,`region_name`,`zone_name`, `customer_short_name`,`war_zone`,`instance_type`,`instance_model`,
    area_name, customhouse_title,
    0 AS demandTotalCore,
    SUM(CASE WHEN `status` = 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_core_apply_after` ELSE 0 END) AS appliedTotalCore,
    0 AS demandTotalDisk,
    SUM(CASE WHEN `status`= 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_disk` ELSE 0 END) AS appliedTotalDisk,
    0 AS demandTotalGpuNum,
    SUM(CASE WHEN `status`= 'APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY} THEN `total_gpu_num_apply_after` ELSE 0 END) AS appliedTotalGpuNum
FROM dwd_crp_ppl_item_cf
WHERE 1=1
    ${FILTER}
-- 过滤条件
GROUP BY `year`,`month`,`region_name`,`zone_name`, `customer_short_name`,`war_zone`,`instance_type`,
`instance_model`, area_name, customhouse_title