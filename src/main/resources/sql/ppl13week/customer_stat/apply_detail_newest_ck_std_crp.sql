-- 预约明细 - 最新数据
SELECT
    yunxiao_order_id,
    yunxiao_detail_id,
    yunxiao_order_status,
    product,
    demand_type,
    demand_scene,
    bill_type,
    begin_buy_date,
    end_buy_date,
    begin_elastic_time as begin_elastic_date,
    end_elastic_time as end_elastic_date,
    note,
    region_name,
    zone_name,
    instance_type,
    instance_model,
    total_core_apply_after as total_core,
    total_disk,
    system_disk_type,
    system_disk_storage,
    system_disk_num,
    data_disk_type,
    data_disk_storage,
    data_disk_num,
    instance_num_apply_after as instance_num,
    year,
    month,
    industry,
    industry_dept,
    customer_short_name,
    customer_name,
    submit_user,
    customer_uin,
    war_zone,
    customer_source,
    (CASE WHEN category IS NULL or category = '(空值)' THEN '未分类' ELSE `category` END) AS category,
    yunxiao_app_role as app_role
FROM dwd_crp_ppl_item_cf
WHERE yunxiao_order_id!=''

${FILTER}
-- 过滤条件
AND `status`='APPLIED' and `source` != 'COMD_INTERVENE' ${FILTER_APPLY}
