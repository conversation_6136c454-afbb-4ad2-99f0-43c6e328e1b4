select device_family,
       device_type,
       sum(`m1_logic_core_num`) as `sum_m1_logic_core_num`,
       sum(`m2_logic_core_num`) as `sum_m2_logic_core_num`,
       sum(`m3_logic_core_num`) as `sum_m3_logic_core_num`,
       sum(`m4_logic_core_num`) as `sum_m4_logic_core_num`,
       sum(`m5_logic_core_num`) as `sum_m5_logic_core_num`,
       sum(`m6_logic_core_num`) as `sum_m6_logic_core_num`,
       sum(`m7_logic_core_num`) as `sum_m7_logic_core_num`,
       sum(`m8_logic_core_num`) as `sum_m8_logic_core_num`,
       sum(`m9_logic_core_num`) as `sum_m9_logic_core_num`,
       sum(`m10_logic_core_num`) as `sum_m10_logic_core_num`,
       sum(`m11_logic_core_num`) as `sum_m11_logic_core_num`,
       sum(`m12_logic_core_num`) as `sum_m12_logic_core_num`
from yunti.cvm_inventory_estimation_temp
where data_date = '${firstDayOfMonth}'   -- 2. 这是需要查的版本时间。注意时间格式：20240201。
  and data_source = '对冲2'
  and statistics_dim = '总库存-累计'
group by
    device_family,device_type