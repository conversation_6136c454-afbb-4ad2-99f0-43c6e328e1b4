SELECT category,
       CASE
           indicator_code
           WHEN 'E1' THEN '资源转进'
           WHEN 'NO1' THEN '资源转进'
           WHEN 'E2' THEN '资源转出盘活'
           WHEN 'NO2' THEN '资源转出盘活'
           WHEN 'RT' THEN '资源转出盘活'
           ELSE indicator_name
           ENd as    p_name,
       null    as    start_val,
       null    as    end_val,
       SUM(core_num)/10000 diff
from report_cvm_jxc
where product_type = 'CVM'
  and deleted = 0
  and stat_time BETWEEN '{date_start_af}' and '{date_end}'
  and indicator_code in ('C1', 'C2', 'E1', 'NO1', 'E2', 'NO2', 'M', 'RT')
group by category,
         p_name
union ALL
SELECT case indicator_code
           when 'D' then '超卖'
           else category
           end                                                                                                     as category,
       indicator_name                                                                                              as p_name,
       sum(IF(stat_time = '{date_start}', core_num, 0)) / 10000                                                    as start_num,
       SUM(IF(stat_time = '{date_end}', core_num, 0)) / 10000                                                      as end_num,
       (sum(IF(stat_time = '{date_end}', core_num, 0)) - SUM(IF(stat_time = '{date_start}', core_num, 0))) /
       10000                                                                                                       as diff
from report_cvm_jxc
where stat_time in ('{date_start}', '{date_end}')
  and product_type = 'CVM'
  and deleted = 0
  and indicator_code in ('a', 'b', 'c', 'buff','K', 'e', 'f', 'g', 'Q', 'P', 'D')
group by category,
         p_name
union ALL
select '非可售', IFNULL(p_name, '非可售汇总'), SUM(start_val), SUM(end_val), sum(diff)
from (
         select case indicator_code
                    when 'NS' then '其他非可售'
                    else indicator_name
                    end                                                                                                     as p_name,
                SUM(if(stat_time = '{date_start}', core_num, 0)) / 10000                                                    as start_val,
                SUM(if(stat_time = '{date_end}', core_num, 0)) / 10000                                                      as end_val,
                (SUM(if(stat_time = '{date_end}', core_num, 0)) - SUM(if(stat_time = '{date_start}', core_num, 0))) /10000  as diff
         from report_cvm_jxc
         where deleted = 0
           and product_type = 'CVM'
           and stat_time in ('{date_start}', '{date_end}')
           and indicator_code in ( 'NS', 'VC')
         group by indicator_name
     ) t
group by p_name WITH ROLLUP
union ALL
--  利用率
select ''                                                 as category,
       '端到端利用率',
       SUM(IF(stat_time = '{date_start}', IF(indicator_code = 'K', core_num, 0), 0)) /
       SUM(IF(stat_time = '{date_start}', core_num, 0))   as rate1,
       SUM(IF(stat_time = '{date_end}', IF(indicator_code = 'K', core_num, 0), 0)) /
       SUM(IF(stat_time = '{date_end}', core_num, 0))     as rate2,
       (SUM(IF(stat_time = '{date_end}', IF(indicator_code = 'K', core_num, 0), 0)) /
        SUM(IF(stat_time = '{date_end}', core_num, 0)) -
        SUM(IF(stat_time = '{date_start}', IF(indicator_code = 'K', core_num, 0), 0)) /
        SUM(IF(stat_time = '{date_start}', core_num, 0))) as rateDiff
from report_cvm_jxc
where deleted = 0
  and product_type = 'CVM'
  and stat_time in ('{date_start}', '{date_end}')
  and indicator_code in ('K', 'D', 'Q', 'NS', 'VC')

ORDER BY CASE category WHEN '进' THEN 0 WHEN '销' THEN 1 WHEN '存' THEN 2 WHEN '超卖' THEN 3 WHEN '非可售' THEN 4 ELSE 5 END,
         CASE p_name
             WHEN '采购提货-新增采购' THEN 2
             WHEN '采购提货-库存复用' THEN 3
             WHEN '资源转进' THEN 4
             WHEN '资源转出盘活' THEN 5
             WHEN '总进货' THEN 6
             WHEN '外部常规售卖' THEN 1
             WHEN '外部LH售卖' THEN 2
             WHEN '内部申领' THEN 3
             WHEN '弹性规模' THEN 4
             WHEN '总售卖' THEN 5
             WHEN '好料' THEN 2
             WHEN '差料' THEN 3
             WHEN '呆料' THEN 4
             WHEN '线下库存' THEN 5
             WHEN '总库存' THEN 6
             WHEN '非可售' THEN 0
             WHEN '超卖量' THEN 1

             WHEN '虚拟化开销' THEN 2
             WHEN '其他非可售' THEN 3
             WHEN '非可售汇总' THEN 4
             WHEN '端到端利用率' THEN 99
             END