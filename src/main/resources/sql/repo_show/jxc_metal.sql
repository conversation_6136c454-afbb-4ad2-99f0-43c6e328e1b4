SELECT category,
       CASE
           indicator_code
           WHEN 'E1' THEN '资源转进'
           WHEN 'NO1' THEN '资源转进'
           WHEN 'E2' THEN '资源转出盘活'
           WHEN 'NO2' THEN '资源转出盘活'
           WHEN 'RT' THEN '资源转出盘活'
           ELSE indicator_name
           ENd as     p_name,
       null    as     start_val,
       null    as     end_val,
       SUM(core_num)/10000 diff
from report_cvm_jxc
where product_type = '裸金属'
  and deleted = 0
  and stat_time BETWEEN '{date_start_af}' and '{date_end}'
  and indicator_code in ('C1', 'C2', 'E1', 'NO1', 'E2', 'NO2', 'M', 'RT')
group by category,
         p_name
union ALL
SELECT
    category                                                                                        as category,
    indicator_name                                                                                  as p_name,
    sum(IF(stat_time = '{date_start}', core_num, 0)) /10000                                           as start_num,
    SUM(IF(stat_time = '{date_end}', core_num, 0)) /10000                                            as end_num,
    (sum(IF(stat_time = '{date_end}', core_num, 0)) - SUM(IF(stat_time = '{date_start}', core_num, 0)))/10000 as diff
from report_cvm_jxc
where stat_time in ('{date_start}', '{date_end}')
  and product_type = '裸金属'
  and deleted = 0
  and indicator_code in ('a', 'c', 'K', 'e', 'f', 'g', 'Q', 'P','NS')
group by category,
         p_name

union ALL
select '大类' as category ,'端到端利用率',
       SUM(IF(stat_time ='{date_start}',IF(indicator_code='K',core_num ,0),0))/SUM(IF(stat_time ='{date_start}',core_num ,0))  as rate1,
       SUM(IF(stat_time ='{date_end}',IF(indicator_code='K',core_num ,0),0))/SUM(IF(stat_time ='{date_end}',core_num ,0))  as rate2,
       (SUM(IF(stat_time ='{date_end}',IF(indicator_code='K',core_num ,0),0))/SUM(IF(stat_time ='{date_end}',core_num ,0)) -
        SUM(IF(stat_time ='{date_start}',IF(indicator_code='K',core_num ,0),0))/SUM(IF(stat_time ='{date_start}',core_num ,0))) as rateDiff
from
    report_cvm_jxc
where
        deleted = 0
  and product_type = '裸金属'
  and stat_time in( '{date_start}' , '{date_end}')
  and indicator_code in('K', 'Q', 'NS')
ORDER BY
    CASE category WHEN '进' THEN 0 WHEN '销' THEN 1 WHEN '存' THEN 2 ELSE 3 END,

    CASE p_name
        WHEN '采购提货-新增采购' THEN 2
        WHEN '采购提货-库存复用' THEN 3
        WHEN '资源转进' THEN 4
        WHEN '资源转出盘活' THEN 5
        WHEN '总进货' THEN 9

        WHEN '外部计费规模' THEN 1
        WHEN '内部售卖规模' THEN 2
        WHEN '销汇总' THEN 3

        WHEN '线上好料' THEN 0
        WHEN '线上差料' THEN 1
        WHEN '线上呆料' THEN 2
        WHEN '线下好料' THEN 3
        WHEN '库存汇总' THEN 4

        WHEN '非可售' THEN 0
        WHEN '端到端利用率' THEN 3
        END