#  行转列
select product_type,
       indicator_code,
       num
from (
         select product_type,
                indicator_code,
                case
                    when product_type in ('CVM', '裸金属') then sum(core_num)
                    when product_type = '网络' then sum(num)
                    else sum(logic_num) end as num
         from report_cvm_jxc
         where deleted = 0
           and product_type in (?)
           and stat_time = ?
         group by stat_time,product_type, indicator_code) t
group by indicator_code, product_type
order by indicator_code, product_type;