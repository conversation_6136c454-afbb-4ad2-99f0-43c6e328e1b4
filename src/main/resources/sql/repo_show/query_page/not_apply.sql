#该领未领
SELECT a.AssetId         as assert_id,
       1                 as type,       a.<PERSON>ceClass     as device_type,
       e.<PERSON>ZoneName     as campus,
       e.<PERSON><PERSON><PERSON><PERSON><PERSON>      as module_name,
       b.plan_product_id as plan_product_id,
       b.plan_product    as plan_product_name
FROM matrix_api_order_task_detail AS a
         LEFT JOIN quota_list AS b ON a.QuotaId = b.id
         LEFT JOIN t_resource.apply_resource_detail AS c ON a.AssetId = c.AssetId
         LEFT JOIN t_resource.apply_detailrecord AS d ON a.QuotaId = d.YuyueNo
         LEFT JOIN sto_rmdb_servers AS e on a.AssetId = e.PhysicsPcCode
    AND c.DetailId = d.ID
WHERE b.sys_source = 1
  AND a.`Status` NOT IN (7, 999)
  AND GREATEST(c.SlaDateByRes, c.SlaDateExpect, d.ExpectDate) <= NOW();
