-- 运管提供的SQL
-- 1、先用这个SQL查个初步信息（属于公司资源池，属于云的全量的库存设备明细）
SELECT a.LogicPcCode      AS asset_id,
       c.YuyueNo          AS YuyueNo,
       c.PromisTime       AS PromisTime,
       b.ActualDate       AS ActualDate,
       e.inventory_class1 AS inventory_class1,
       e.inventory_class2 AS inventory_class2,
       e.inventory_class3 AS inventory_class3,
       e.inventory_class4 AS inventory_class4,
       a.GoUpDate         AS GoUpDate,
       c.ExpectDate       AS ExpectDate,
       a.DeviceType       AS DeviceType,
       a.SvrTypeVersion   AS SvrTypeVersion,
       a.RegionName       AS RegionName,
       a.ZoneName         AS ZoneName,
       a.SubZoneName      AS SubZoneName,
       a.ModuleName       AS ModuleName,
       a.BelongUnitName   AS BelongUnitName,
       c.PlanProduct      AS PlanProductName
FROM sto_rmdb_servers AS a
         LEFT JOIN apply_resource_detail AS b ON a.OID = b.Id
         LEFT JOIN apply_detailrecord AS c ON b.DetailId = c.ID
         LEFT JOIN sto_rmdb_servers_sub AS e ON a.ID = e.ID
WHERE a.OID > 0
  AND c.ErpBizType = '云业务'
  AND a.BelongDpId = 32;