-- 2、再用这个SQL查出一部分记录，覆盖上面的记录初次期望交付日期（2查出来的设备是1中的子集，对应的初次需求时间以这个为准，所以需要覆盖掉1中相同设备对应的初次需求时间）
SELECT a.LogicPcCode AS asset_id,d.hurry_time AS PromisTime
FROM sto_rmdb_servers AS a
         LEFT JOIN apply_resource_detail AS b ON a.OID = b.Id
         LEFT JOIN apply_detailrecord AS c ON b.DetailId = c.ID
         LEFT JOIN cloud_matrix.quota_hurry_order AS d ON c.YuyueNo = d.quota_id
         LEFT JOIN sto_rmdb_servers_sub AS e ON a.ID = e.ID
WHERE a.OID > 0 AND c.ErpBizType = '云业务' AND a.BelongDpId = 32 AND d.msg = '星云提单触发拆单后的自动追加';