package cloud.demand.app;

import cloud.demand.app.web.weblog.CommonWebLogFilter;
import com.pugwoo.dbhelper.DBHelper;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;

import javax.annotation.Resource;

@OpenAPIDefinition(
        info = @Info(title = "云需求管理系统接口文档"))
@SpringBootApplication(exclude = { FreeMarkerAutoConfiguration.class })
public class Application {

    /**
     * Spring上下文
     */
    public static ConfigurableApplicationContext context;

    @Resource
    private DBHelper demandDBHelper;

    public static void main(String[] args) {
        context = SpringApplication.run(Application.class, args);
    }

    @Bean
    public CommonWebLogFilter webLogFilter() {
        return new CommonWebLogFilter(demandDBHelper);
    }
}




