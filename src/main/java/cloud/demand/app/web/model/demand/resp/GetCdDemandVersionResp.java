package cloud.demand.app.web.model.demand.resp;

import cloud.demand.app.entity.demand.CdDemandVersionDO;
import cloud.demand.app.enums.DemandVersionStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Calendar;
import java.util.Date;
import lombok.Data;

@Data
public class GetCdDemandVersionResp {

    private Long id;
    private Boolean deleted;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Integer status;
    private String statusName;
    private String demandVersion;
    private String name;
    private String desc;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportOpenDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportCloseDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandAckOpenDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandAckCloseDate;
    private Integer forecastFromYear;
    private Integer forecastFromMonth;
    private Integer forecastToYear;
    private Integer forecastToMonth;
    private Date forecastFrom;
    private Date forecastTo;

    public static GetCdDemandVersionResp copy(CdDemandVersionDO cdDemandVersionDO) {
        GetCdDemandVersionResp getCdDemandVersionDOResp = new GetCdDemandVersionResp();
        getCdDemandVersionDOResp.setId(cdDemandVersionDO.getId());
        getCdDemandVersionDOResp.setDeleted(cdDemandVersionDO.getDeleted());
        getCdDemandVersionDOResp.setCreateTime(cdDemandVersionDO.getCreateTime());
        getCdDemandVersionDOResp.setUpdateTime(cdDemandVersionDO.getUpdateTime());
        getCdDemandVersionDOResp.setCreator(cdDemandVersionDO.getCreator());
        getCdDemandVersionDOResp.setUpdater(cdDemandVersionDO.getUpdater());
        getCdDemandVersionDOResp.setStatus(cdDemandVersionDO.getStatus());
        getCdDemandVersionDOResp.setStatusName(
                DemandVersionStatusEnum.getById(getCdDemandVersionDOResp.getStatus()).getStatusName());
        getCdDemandVersionDOResp.setDemandVersion(cdDemandVersionDO.getDemandVersion());
        getCdDemandVersionDOResp.setName(cdDemandVersionDO.getName());
        getCdDemandVersionDOResp.setDesc(cdDemandVersionDO.getDesc());
        getCdDemandVersionDOResp.setDemandImportOpenDate(cdDemandVersionDO.getDemandImportOpenDate());
        getCdDemandVersionDOResp.setDemandImportCloseDate(cdDemandVersionDO.getDemandImportCloseDate());
        getCdDemandVersionDOResp.setDemandAckOpenDate(cdDemandVersionDO.getDemandAckOpenDate());
        getCdDemandVersionDOResp.setDemandAckCloseDate(cdDemandVersionDO.getDemandAckCloseDate());
        getCdDemandVersionDOResp.setForecastFromYear(cdDemandVersionDO.getForecastFromYear());
        getCdDemandVersionDOResp.setForecastFromMonth(cdDemandVersionDO.getForecastFromMonth());
        getCdDemandVersionDOResp.setForecastToYear(cdDemandVersionDO.getForecastToYear());
        getCdDemandVersionDOResp.setForecastToMonth(cdDemandVersionDO.getForecastToMonth());
        Calendar calendar = Calendar.getInstance();
        Date forecastFrom = DateUtils.parse(getCdDemandVersionDOResp.getForecastFromYear()
                + "-" + String.format("%02d", getCdDemandVersionDOResp.getForecastFromMonth()));
        calendar.setTime(forecastFrom);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        getCdDemandVersionDOResp.setForecastFrom(calendar.getTime());

        Date forecastTo = DateUtils.parse(getCdDemandVersionDOResp.getForecastToYear()
                + "-" + String.format("%02d", getCdDemandVersionDOResp.getForecastToMonth()));
        calendar.setTime(forecastTo);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        getCdDemandVersionDOResp.setForecastTo(calendar.getTime());
        return getCdDemandVersionDOResp;

    }
}
