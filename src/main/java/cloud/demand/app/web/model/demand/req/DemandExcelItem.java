package cloud.demand.app.web.model.demand.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class DemandExcelItem {
    @ExcelProperty(index = 0)
    private String isEstimatedPlan;
    @ExcelProperty(index = 1)
    private String customerName;
    @ExcelProperty(index = 2)
    private String winRate;
    @ExcelProperty(index = 3)
    private String customerDemandScene;
    @ExcelProperty(index = 4)
    private String demandRetainedType;
    @ExcelProperty(index = 5)
    private String demandDate;
    @ExcelProperty(index = 6)
    private String returnDate;
    @ExcelProperty(index = 7)
    private String regionType;
    @ExcelProperty(index = 8)
    private String regionName;
    @ExcelProperty(index = 9)
    private String zoneName;
    @ExcelProperty(index = 10)
    private String instanceType;
    @ExcelProperty(index = 11)
    private String instanceModel;
    @ExcelProperty(index = 12)
    private String demandNum;
    @ExcelProperty(index = 13)
    private String demandCoreNum;
    @ExcelProperty(index = 14)
    private String demandDiskType;
    @ExcelProperty(index = 15)
    private String demandDiskSize;
    @ExcelProperty(index = 16)
    private String demandSystemDiskType;
    @ExcelProperty(index = 17)
    private String demandSystemDiskSize;
    @ExcelProperty(index = 18)
    private String remark;
}
