package cloud.demand.app.web.model.demand.resp;

import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

@Data
public class DemandItemForExcel {

    /**行业*/
    private String industry;
    /**客户名称*/
    private String customerName;
    /**是否方案*/
    private String isEstimatedPlan;
    /**赢率百分比*/
    private String winRate;
    /** 客户需求场景*/
    private String customerDemandScene;
    /** 需求留存类型*/
    private String demandRetainedType;
    /** 需求时间*/
    private String demandDate;
    /** 预计退回日期*/
    private String returnDate;
    /** 地域类型*/
    private String regionType;
    /** region中文名*/
    private String regionName;
    /** 可用区中文名*/
    private String zoneName;
    /** 实例类型*/
    private String instanceType;
    /** 实例规格*/
    private String instanceModel;
    /** 需求数量*/
    private Integer demandNum;
    /** 需求核心数*/
    private Integer demandCoreNum;
    /** 云盘类型*/
    private String demandDiskType;
    /** 云盘大小，单位G*/
    private Integer demandDiskSize;
    /** 系统盘类型*/
    private String demandSystemDiskType;
    /** 系统盘大小，单位G*/
    private Integer demandSystemDiskSize;
    /** 备注*/
    private String remark;

    public static DemandItemForExcel from(CdDemandVersionItemDO itemDO) {
        DemandItemForExcel demandItem = new DemandItemForExcel();
        demandItem.setIndustry(itemDO.getIndustry());
        demandItem.setCustomerName(itemDO.getCustomerName());
        demandItem.setIsEstimatedPlan(itemDO.getIsEstimatedPlan() != null && itemDO.getIsEstimatedPlan()
                ? "是" : "否");
        demandItem.setWinRate(itemDO.getWinRate() != null ? itemDO.getWinRate() + "%" : "");
        demandItem.setCustomerDemandScene(itemDO.getCustomerDemandScene());
        demandItem.setDemandRetainedType(itemDO.getDemandRetainedType());
        demandItem.setDemandDate(DateUtils.formatDate(itemDO.getDemandDate()));
        demandItem.setReturnDate(DateUtils.formatDate(itemDO.getReturnDate()));
        demandItem.setRegionType(itemDO.getRegionType());
        demandItem.setRegionName(itemDO.getRegionName());
        demandItem.setZoneName(itemDO.getZoneName());
        demandItem.setInstanceType(itemDO.getInstanceType());
        demandItem.setInstanceModel(itemDO.getInstanceModel());
        demandItem.setDemandNum(itemDO.getDemandNum());
        demandItem.setDemandCoreNum(itemDO.getDemandCoreNum());
        demandItem.setDemandDiskType(itemDO.getDemandDiskType());
        demandItem.setDemandDiskSize(itemDO.getDemandDiskSize());
        demandItem.setDemandSystemDiskType(itemDO.getDemandSystemDiskType());
        demandItem.setDemandSystemDiskSize(itemDO.getDemandSystemDiskSize());
        demandItem.setRemark(itemDO.getRemark());
        return demandItem;
    }

}
