package cloud.demand.app.web.model.demand.resp;

import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import cloud.demand.app.enums.DemandStageEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import lombok.Data;

@Data
public class GetCdDemandVersionItemResp {

    private Long id;
    private Boolean deleted;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private String demandVersion;
    private Long demandGroupId;
    private Integer demandStage;
    private String demandStageText;
    private String industry;
    private String customerName;
    private Integer winRate;
    private String winRateText;
    private String customerDemandScene;
    private String demandRetainedType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date returnDate;
    private Integer demandYear;
    private Integer demandMonth;
    private String regionType;
    private String regionName;
    private String zoneName;
    private String instanceType;
    private String instanceModel;
    private Integer demandNum;
    private Integer demandCoreNum;
    private String demandDiskType;
    private Integer demandDiskSize;
    private String demandDiskSizeText;
    private String demandSystemDiskType;
    private Integer demandSystemDiskSize;
    private String demandSystemDiskSizeText;
    private String remark;
    private Boolean isEstimatedPlan;
    private String isEstimatedPlanText;

    public static GetCdDemandVersionItemResp copy(CdDemandVersionItemDO cdDemandVersionItemDO) {
        GetCdDemandVersionItemResp getCdDemandVersionItemResp = new GetCdDemandVersionItemResp();
        getCdDemandVersionItemResp.setId(cdDemandVersionItemDO.getId());
        getCdDemandVersionItemResp.setDeleted(cdDemandVersionItemDO.getDeleted());
        getCdDemandVersionItemResp.setCreateTime(cdDemandVersionItemDO.getCreateTime());
        getCdDemandVersionItemResp.setUpdateTime(cdDemandVersionItemDO.getUpdateTime());
        getCdDemandVersionItemResp.setCreator(cdDemandVersionItemDO.getCreator());
        getCdDemandVersionItemResp.setUpdater(cdDemandVersionItemDO.getUpdater());
        getCdDemandVersionItemResp.setDemandVersion(cdDemandVersionItemDO.getDemandVersion());
        getCdDemandVersionItemResp.setDemandGroupId(cdDemandVersionItemDO.getDemandGroupId());
        getCdDemandVersionItemResp.setDemandStage(cdDemandVersionItemDO.getDemandStage());
        getCdDemandVersionItemResp
                .setDemandStageText(
                        DemandStageEnum.getById(getCdDemandVersionItemResp.getDemandStage()).getStageName());
        getCdDemandVersionItemResp.setIndustry(cdDemandVersionItemDO.getIndustry());
        getCdDemandVersionItemResp.setCustomerName(cdDemandVersionItemDO.getCustomerName());
        getCdDemandVersionItemResp.setWinRate(cdDemandVersionItemDO.getWinRate());
        getCdDemandVersionItemResp.setWinRateText(getCdDemandVersionItemResp.getWinRate() + "%");
        getCdDemandVersionItemResp.setCustomerDemandScene(cdDemandVersionItemDO.getCustomerDemandScene());
        getCdDemandVersionItemResp.setDemandRetainedType(cdDemandVersionItemDO.getDemandRetainedType());
        getCdDemandVersionItemResp.setDemandDate(cdDemandVersionItemDO.getDemandDate());
        getCdDemandVersionItemResp.setReturnDate(cdDemandVersionItemDO.getReturnDate());
        getCdDemandVersionItemResp.setDemandYear(cdDemandVersionItemDO.getDemandYear());
        getCdDemandVersionItemResp.setDemandMonth(cdDemandVersionItemDO.getDemandMonth());
        getCdDemandVersionItemResp.setRegionType(cdDemandVersionItemDO.getRegionType());
        getCdDemandVersionItemResp.setRegionName(cdDemandVersionItemDO.getRegionName());
        getCdDemandVersionItemResp.setZoneName(cdDemandVersionItemDO.getZoneName());
        getCdDemandVersionItemResp.setInstanceType(cdDemandVersionItemDO.getInstanceType());
        getCdDemandVersionItemResp.setInstanceModel(cdDemandVersionItemDO.getInstanceModel());
        getCdDemandVersionItemResp.setDemandNum(cdDemandVersionItemDO.getDemandNum());
        getCdDemandVersionItemResp.setDemandCoreNum(cdDemandVersionItemDO.getDemandCoreNum());
        getCdDemandVersionItemResp.setDemandDiskType(cdDemandVersionItemDO.getDemandDiskType());
        getCdDemandVersionItemResp.setDemandDiskSize(cdDemandVersionItemDO.getDemandDiskSize());
        getCdDemandVersionItemResp.setDemandDiskSizeText(getCdDemandVersionItemResp.getDemandDiskSize() + "G");
        getCdDemandVersionItemResp.setDemandSystemDiskType(cdDemandVersionItemDO.getDemandSystemDiskType());
        getCdDemandVersionItemResp.setDemandSystemDiskSize(cdDemandVersionItemDO.getDemandSystemDiskSize());
        getCdDemandVersionItemResp.setDemandSystemDiskSizeText(
                getCdDemandVersionItemResp.getDemandSystemDiskSize() + "G");
        getCdDemandVersionItemResp.setRemark(cdDemandVersionItemDO.getRemark());
        getCdDemandVersionItemResp.setIsEstimatedPlan(cdDemandVersionItemDO.getIsEstimatedPlan());
        Boolean isEstimatedPlan = getCdDemandVersionItemResp.getIsEstimatedPlan();
        getCdDemandVersionItemResp.setIsEstimatedPlanText(isEstimatedPlan ? "是" : "否");
        return getCdDemandVersionItemResp;
    }

    public String key() {
        return String.join("-",
                demandVersion, demandGroupId.toString(),
                demandStage.toString(), industry, customerName,
                winRate.toString(), customerDemandScene, demandRetainedType,
                DateUtils.format(demandDate), DateUtils.format(returnDate),
                regionType, regionName, zoneName, instanceType, instanceModel,
                demandDiskType, demandSystemDiskType, isEstimatedPlan.toString());
    }
}
