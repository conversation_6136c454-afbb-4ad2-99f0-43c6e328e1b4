package cloud.demand.app.web.model.stat;

import cloud.demand.app.entity.demand.CdDemandVersionItemDO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ChangeDetailResp {

    /**表数据*/
    private List<Row> data;

    /**表头*/
    private List<Map<String, Object>> tableTitle;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class Row {
        private String yearMonth;
        private String regionName;
        private String instanceType;
        private String instanceModel;
        private Long currentNum;
        private Long lastNum;
        private Long changeNum;
        private Integer changePercent;
    }

    /**说明：调用方需要保证current和last同属一个分组，且至少有个一个非空*/
    public static Row genRow(List<CdDemandVersionItemDO> current, List<CdDemandVersionItemDO> last,
                             List<String> compareGroups) {
        Row row = new Row();
        CdDemandVersionItemDO itemDO = !CollectionUtils.isEmpty(current) ? current.get(0) : last.get(0);
        if (compareGroups != null) {
            if (compareGroups.contains("yearMonth")) {
                row.setYearMonth(itemDO.getDemandYear() + "年" + itemDO.getDemandMonth() + "月");
            }
            if (compareGroups.contains("regionName")) {
                row.setRegionName(itemDO.getRegionName());
            }
            if (compareGroups.contains("instanceType")) {
                row.setInstanceType(itemDO.getInstanceType());
            }
            if (compareGroups.contains("instanceModel")) {
                row.setInstanceModel(itemDO.getInstanceModel());
            }
        }
        row.setCurrentNum(NumberUtils.sum(current, CdDemandVersionItemDO::getDemandCoreNum).longValue());
        row.setLastNum(NumberUtils.sum(last, CdDemandVersionItemDO::getDemandCoreNum).longValue());
        row.setChangeNum(row.getCurrentNum() - row.getLastNum());
        long lastNum = row.getLastNum() == 0 ? 1 : row.getLastNum(); // 处理上期数量是0的情况
        row.setChangePercent(NumberUtils.percent(BigDecimal.valueOf(row.getChangeNum()), BigDecimal.valueOf(lastNum)));
        return row;
    }

    public static List<Map<String, Object>> genTableTitle(List<String> compareGroups) {
        List<Map<String, Object>> tableTitle = new ArrayList<>();
        if (compareGroups != null) {
            if (compareGroups.contains("yearMonth")) {
                tableTitle.add(MapUtils.of("title", "需求年月",
                        "dataIndex", "yearMonth", "type", "string"));
            }
            if (compareGroups.contains("regionName")) {
                tableTitle.add(MapUtils.of("title", "城市",
                        "dataIndex", "regionName", "type", "string"));
            }
            if (compareGroups.contains("instanceType")) {
                tableTitle.add(MapUtils.of("title", "实例类型",
                        "dataIndex", "instanceType", "type", "string"));
            }
            if (compareGroups.contains("instanceModel")) {
                tableTitle.add(MapUtils.of("title", "实例规格",
                        "dataIndex", "instanceModel", "type", "string"));
            }
        }
        tableTitle.add(MapUtils.of("title", "本期数据",
                "dataIndex", "currentNum", "type", "number", "sorter", "true"));
        tableTitle.add(MapUtils.of("title", "上期数据",
                "dataIndex", "lastNum", "type", "number", "sorter", "true"));
        tableTitle.add(MapUtils.of("title", "本期-上期",
                "dataIndex", "changeNum", "type", "number", "sorter", "true"));
        tableTitle.add(MapUtils.of("title", "波动百分比",
                "dataIndex", "changePercent", "type", "number", "sorter", "true"));

        return tableTitle;
    }

}
