package cloud.demand.app.web;

import cloud.demand.app.modules.p2p.industry_demand.dto.IndustryDemandApproveDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.KeyWordParam;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.GroupTypeEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalMessageBody;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.product_demand.service.ProductTodoService;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/api/todo")
@Slf4j
public class TodoController {

    @Autowired
    TodoService todoService;

    @Resource
    ProductTodoService productTodoService;
    @Resource
    PermissionService permissionService;
    @Resource
    private DBHelper demandDBHelper;

    /**
     * 获取审批流程
     */
    @RequestMapping
    public Object getIndustryDemandFlow(@JsonrpcParam @Valid DemandGroupId groupId) {
        return todoService.industryDemandApproveFlow(groupId.getDemandGroupId());
    }

    @RequestMapping
    public Object getProductDemandFlow(@JsonrpcParam @Valid DemandGroupId groupId) {
        return productTodoService.productDemandApproveFlow(groupId.getDemandGroupId());
    }

    /**
     * 审批确认
     */
    @RequestMapping
    public Object confirmOrderApproval(@CurrentUser TofUser user,
            @JsonrpcParam ConfirmOrderApprovalRequest request) {
        if (StringUtils.isBlank(request.getSystem())) {
            // 默认值，兼容老流程
            request.setSystem("industry");
        }
        log.info("req {}", request);
        if (request.getSystem().equals("industry")) {
            IndustryDemandVersionGroupDO demandGroup = demandDBHelper.getOne(IndustryDemandVersionGroupDO.class,
                    "where id=?", request.demandGroupId);
            if (demandGroup == null || !demandGroup.getStatus().equals(request.status)) {
                throw new BizException("单据状态已发送变化，请刷新页面后重试.");
            }
            val flow = todoService.getApproveFlowStep(GroupTypeEnum.getBySp(demandGroup.getIndustry()), request.status);

            if (flow == null) {
                throw new BizException("当前状态不可审批，请刷新页面后重试.");
            }
            KeyWordParam param = KeyWordParam.buildByDemandGroup(demandGroup);
            String curHandler = todoService.getApproveByStatus(GroupTypeEnum.getBySp(demandGroup.getIndustry()),
                    demandGroup.getStatus(), param);
            String operator = request.getOperator() == null ? user.getUsername() : request.getOperator();

            if (!Splitter.on(";").omitEmptyStrings().splitToList(curHandler).contains(operator)) {
                throw new BizException("没有当前节点审批权限.");
            }

            IndustryDemandApproveDTO approveDTO = new IndustryDemandApproveDTO();
            approveDTO.setDemandGroup(demandGroup);
            approveDTO.setStep(flow);
            approveDTO.setApprove(operator);
            approveDTO.setPass(request.getPassFlag() == 0);
            approveDTO.setMemo(request.getApproveMemo());

            todoService.doIndustryDemandApprove(approveDTO);

        } else if (request.getSystem().equals("product")) {
            if (request.getPassFlag().equals(1) && StringUtils.isBlank(request.getApproveMemo())) {
                throw new BizException("驳回时，驳回原因为必填项");
            }
            ProductDemandVersionGroupDO demandGroup = demandDBHelper.getOne(ProductDemandVersionGroupDO.class,
                    "where id=?", request.demandGroupId);
            if (demandGroup == null || !demandGroup.getStatus().equals(request.status)) {
                throw new BizException("单据状态已发送变化，请刷新页面后重试.");
            }

            ApprovalMessageBody messageBody = new ApprovalMessageBody();
            messageBody.setApproveMemo(request.getApproveMemo());
            String operator = request.getOperator() == null ? user.getUsername() : request.getOperator();
            messageBody.setApprover(operator);
            messageBody.setApproverAppOrder(request.version + "-" + request.demandGroupId);
            String orderTaskId =
                    "ProductDemand_" + demandGroup.getExecCount() + "-" + demandGroup.getId() + "-" + request.status;
            messageBody.setApproverOrder(orderTaskId);
            messageBody.setApproveResult(request.getPassFlag());

            productTodoService.doProductDemandApprove(ProductTodoService.ApprovalMessageBody.copy(messageBody));
        }

        return ImmutableMap.of();
    }

//    /**
//     * 提交单据
//     */
//    @RequestMapping
//    public Object submitOrder(@CurrentUser TofUser user, @JsonrpcParam @Valid SubmitOrderRequest request) {
//        val demandGroup = demandDBHelper.getOne(IndustryDemandVersionGroupDO.class, "where id=?",
//                request.getDemandGroupId());
//        String admins = permissionService.getUserByRole(IndustryDemandAuthRoleEnum.ADMIN);
////        if (admins.contains(user.getUsername())) {
////            todoService.startIndustryDemandApproveFlow(request.demandGroupId, user.getUsername());
////        }
//
//        return ImmutableMap.of("rs", "ok");
//    }

    /**
     * 批量审批
     *
     * @param user
     * @param request
     * @return
     */
    @RequestMapping
    public Object confirmOrderApprovalBatch(@CurrentUser TofUser user,
            @JsonrpcParam BatchConfirm request) {
        return ImmutableMap.of();
    }


    @Data
    private static class BatchConfirm {

        @NotNull
        Integer passFlag;
        String approveMemo = "";
        String operator;
        @NotNull
        private Integer status;
        @NotEmpty
        private List<String> orders;
    }

    @Data
    private static class ConfirmOrderApprovalRequest {

        @NotEmpty
        String system;
        @NotNull
        Long demandGroupId;
        @NotEmpty
        String version;
        @NotNull
        String status;
        @NotNull
        Integer passFlag;
        String sourceApp;
        String approveMemo = "";
        String operator;
    }

    @Data
    private static class SubmitOrderRequest {

        @NotNull
        Long demandGroupId;
    }

    @Data
    public static class DemandGroupId {

        @NotNull
        private Long demandGroupId;
    }
}
