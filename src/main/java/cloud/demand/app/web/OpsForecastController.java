package cloud.demand.app.web;


import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import cloud.demand.app.modules.forecast_compute.model.ForecastAlgorithms.Algorithm;
import cloud.demand.app.modules.forecast_compute.model.ForecastAlgorithms.AlgorithmEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastGroupDimsEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.CreateSplitVersionReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryForecastCommonReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekTransformReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCdbMemGroupEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastProductEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekInputService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekOpsService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekPredictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekTransformService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekViewService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13WeekInputServiceImpl;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ops")
public class OpsForecastController {

    @Resource
    Ppl13weekSplitService ppl13weekSplitService;
    @Resource
    private Ppl13WeekInputServiceImpl ppl13WeekInputService;
    @Resource
    private Ppl13weekPredictService ppl13weekPredictService;
    @Resource
    Ppl13weekTransformService ppl13weekTransformService;
    @Resource
    Ppl13weekInputService ppl13weekInputService;
    @Resource
    Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;

    @Resource
    Ppl13weekViewService ppl13weekViewService;


    @Resource
    Ppl13weekOpsService ppl13weekOpsService;


    @GetMapping("/cleanForecastData")
    public String cleanDataByTaskId(String taskIds, String category,Boolean prepare, Integer isEnable, String andSql) {
        List<Long> ids = parseNonNumericParts(taskIds);
        QueryForecastCommonReq req = new QueryForecastCommonReq();
        req.setTaskIds(ids);
        req.setCategory(category);
        req.setPrepare(prepare);
        req.setIsEnable(isEnable);
        req.setAndSql(andSql);
        return "<pre>" + ppl13weekOpsService.cleanDataByTaskId(req) + "</pre>";
    }

    public static List<Long> parseNonNumericParts(String input) {
        if (Strings.isBlank(input)) {
            return Lang.list();
        }
        List<Long> numbers = new ArrayList<>();
        String[] parts = input.split("\\D+");

        for (String part : parts) {
            if (!part.isEmpty()) {
                Long number = Long.parseLong(part);
                numbers.add(number);
            }
        }
        return numbers;
    }

    @GetMapping("/createSplitVersion")
    public String createSplitVersion(String taskIds, String desc,String type) {
        CreateSplitVersionReq req = new CreateSplitVersionReq();
        List<Long> taskId = Arrays.stream(taskIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        req.setOutputVersionName(desc);
        req.setTaskIds(taskId);
        req.setOutputVersionType(type);
        return ppl13weekViewService.createSplitVersion(req).toString();
    }

    @GetMapping("/createZiyanTransForm")
    public String createZiyanTransForm(Long outputId, String start, String end, boolean isAppend) {

        QueryPpl13weekTransformReq queryPpl13weekTransformReq = new QueryPpl13weekTransformReq();
        queryPpl13weekTransformReq.setOutputVersionIds(Lang.list(outputId));
        queryPpl13weekTransformReq.setStartYearMonth(start);
        queryPpl13weekTransformReq.setEndYearMonth(end);
        queryPpl13weekTransformReq.setIsAppend(isAppend);
        ppl13weekTransformService.createZiyanTransForm(queryPpl13weekTransformReq);
        return "done";
    }


    @GetMapping("/splitProjectCustomBgDeviceGroup")
    public String splitProjectCustomBgDeviceGroup(Long taskId, String name, String note) {
        ppl13weekSplitService.splitProjectCustomBgDeviceGroup(taskId, name, note);
        return "done";
    }

    @GetMapping("/genLatestInputDetailForMrpData")
    public String genLatestInputDetailForMrpData() {
        ppl13weekInputService.genLatestInputDetailForMrpData();
        return "success";
    }

    /**
     * 这个是当前创建月度预测的入口（for 自研），尽量都收敛到这个入口
     */
    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/createMonthForecastForZiyan")
    public void createMonthForecastForZiyan(String start, String end, String serialInterval,
            String category,
            String alName,
            String alArg,
            Boolean transInstanceType, Boolean isByArea,
            Integer conditionType, Integer initConditionType,
            Integer predictN) {
        LocalDate startLocalDate = LocalDate.parse(start);
        LocalDate endLocalDate = LocalDate.parse(end);

        Algorithm algorithm = new Algorithm(AlgorithmEnum.getByName(alName), Lang.list(alArg.split(",")));

        // 预测间隔默认是月
        SerialIntervalEnum serialIntervalEnum = StringTools.isBlank(serialInterval) ? SerialIntervalEnum.MONTH :
                SerialIntervalEnum.getByCode(serialInterval);

        isByArea = Optional.ofNullable(isByArea).orElse(false);
        conditionType = Optional.ofNullable(conditionType).orElse(0);
        initConditionType = Optional.ofNullable(initConditionType).orElse(0);
        predictN = Optional.ofNullable(predictN ).orElse(6);

        while (!startLocalDate.isAfter(endLocalDate)) {
            ppl13WeekInputService.createZiyanForecastCompute(
                    category, startLocalDate.toString(), serialIntervalEnum,
                    algorithm, false, transInstanceType, isByArea, conditionType,initConditionType,predictN);
            if (serialIntervalEnum == SerialIntervalEnum.WEEK) {
                startLocalDate = startLocalDate.plusDays(7);
            } else {
                startLocalDate = startLocalDate.plusMonths(1);
            }
        }
    }

    /**
     * 用于腾讯云全量预测：只剔除指定的客户+毛刺
     * @param product 重要：不同的产品有不同的输入，取值如CVM，参考：Ppl13weekForecastProductEnum
     */
    @Synchronized(waitLockMillisecond = 100)
    @RequestMapping("/createMonthForecastForWhole")
    public void createMonthForecastForWhole(String start, String end,
                                            String serialInterval, // 默认是月
                                            String product, // 默认是CVM
                                            String category,
                                            String sourceType, // 默认内部+外部
                                            String billType,  // 默认全量
                                            String alName,
                                            String alArg,
                                            String retAlArgs,
                                            Boolean transInstanceType,
                                            String groupDimType,
                                            Boolean isRemoveSpike, // 默认剔除
                                            /*模型预测范围，1为预测长尾，2是预测31个头部(且不含毛刺)，3是预测31个头部+毛刺*/ Integer forecastScope,

                                            Integer predictN,
                                            /*额外参与预测的可用区*/String extraZones,
                                            /*自定义大客户，逗号隔开，传NONE表示没有大客户*/String customHeadCustomers,
                                            Integer customSpikeThreshold,
                                            String cdbMemGroup,
                                            @RequestBody(required = false) String customSql) {
        LocalDate startLocalDate = LocalDate.parse(start);
        LocalDate endLocalDate = LocalDate.parse(end);
        Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.getByCode(sourceType);
        if (sourceTypeEnum == null) {
            sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
        }
        Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.getByCode(billType);
        if (billTypeEnum == null) {
            billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
        }

        if (forecastScope == null) {
            forecastScope = 1;
        }

        Ppl13weekForecastCdbMemGroupEnum memGroupEnum = Ppl13weekForecastCdbMemGroupEnum.getByCode(cdbMemGroup);
        if (memGroupEnum == null) {
            memGroupEnum = Ppl13weekForecastCdbMemGroupEnum.GROUP;
        }

        alArg = alArg == null ? "" : alArg;
        Algorithm algorithm = new Algorithm(AlgorithmEnum.getByName(alName), Lang.list(alArg.split(",")));

        // 预测间隔默认是月
        SerialIntervalEnum serialIntervalEnum = StringTools.isBlank(serialInterval) ? SerialIntervalEnum.MONTH :
                SerialIntervalEnum.getByCode(serialInterval);

        Ppl13weekForecastProductEnum productEnum = Ppl13weekForecastProductEnum.getByCode(product);
        productEnum = productEnum == null ? Ppl13weekForecastProductEnum.CVM : productEnum;

        PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.getByCode(groupDimType);
        if (productEnum == Ppl13weekForecastProductEnum.CVM && groupDimsEnum == null) {
            throw new WrongWebParameterException("CVM执行量聚合方式必须提供或提供错误");
        }

        if (isRemoveSpike == null) {
            isRemoveSpike = true;
        }


        List<String> extraZoneList = StringTools.isBlank(extraZones) ? null : Lang.list(extraZones.split(","));

        List<String> customHeadCustomersList = null;
        if (StringTools.isNotBlank(customHeadCustomers)) {
            if ("NONE".equals(customHeadCustomers)) {
                customHeadCustomersList = new ArrayList<>();
            } else {
                String[] strs = customHeadCustomers.split(",");
                customHeadCustomersList = new ArrayList<>();
                for (String s : strs) {
                    if (StringTools.isNotBlank(s)) {
                        customHeadCustomersList.add(s.trim());
                    }
                }
            }
        }

        while (!startLocalDate.isAfter(endLocalDate)) {
            ppl13WeekInputService.createCloudWholeForecastCompute(productEnum,
                    category, startLocalDate.toString(),
                    serialIntervalEnum,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, false, transInstanceType, groupDimsEnum, isRemoveSpike,
                    customSql, forecastScope, retAlArgs, predictN, extraZoneList,
                    customHeadCustomersList, customSpikeThreshold,memGroupEnum);
            if (serialIntervalEnum == SerialIntervalEnum.WEEK) {
                startLocalDate = startLocalDate.plusDays(7);
            } else {
                startLocalDate = startLocalDate.plusMonths(1);
            }
        }
    }

    /**
     * 用于腾讯云新腰部预测
     */
    @Synchronized(waitLockMillisecond = 100)
    @GetMapping("/createMonthForecastForMiddleNew")
    public void createMonthForecastForMiddleNew(String start, String end, String serialInterval,
            String category,
            String sourceType,
            String billType,
            String alName,
            String alArg,
            Boolean transInstanceType,
            String groupDimType,
            Boolean isRemoveSpike) {
        LocalDate startLocalDate = LocalDate.parse(start);
        LocalDate endLocalDate = LocalDate.parse(end);
        Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.getByCode(sourceType);
        if (sourceTypeEnum == null) {
            sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
        }
        Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.getByCode(billType);
        if (billTypeEnum == null) {
            billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
        }

        Algorithm algorithm = new Algorithm(AlgorithmEnum.getByName(alName), Lang.list(alArg.split(",")));

        // 预测间隔默认是月
        SerialIntervalEnum serialIntervalEnum = StringTools.isBlank(serialInterval) ? SerialIntervalEnum.MONTH :
                SerialIntervalEnum.getByCode(serialInterval);

        PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.getByCode(groupDimType);
        if (groupDimsEnum == null) {
            throw new WrongWebParameterException("执行量聚合方式必须提供或提供错误");
        }

        if (isRemoveSpike == null) {
            isRemoveSpike = true;
        }

        while (!startLocalDate.isAfter(endLocalDate)) {
            ppl13WeekInputService.createCloudNewMiddleForecastCompute(category, startLocalDate.toString(),
                    serialIntervalEnum,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, false, transInstanceType, groupDimsEnum, isRemoveSpike);
            if (serialIntervalEnum == SerialIntervalEnum.WEEK) {
                startLocalDate = startLocalDate.plusDays(7);
            } else {
                startLocalDate = startLocalDate.plusMonths(1);
            }
        }
    }

    @GetMapping("/createVersionItemYearMonthCustomerDefines")
    public String createVersionItemYearMonthCustomerDefines(String date) {
        ppl13weekCommonDataAccess.createVersionItemYearMonthCustomerDefines(DateUtils.parseLocalDate(date));
        return "success";
    }

    @GetMapping("/createAllCustomerDefines")
    public String createAllCustomerDefines() {
        ppl13weekCommonDataAccess.createAllCustomerDefines();
        return "success";
    }

    @GetMapping("/createYearMonthCustomerDefines")
    public String createYearMonthCustomerDefines(String date) {
        ppl13weekCommonDataAccess.createYearMonthCustomerDefines(DateUtils.parseLocalDate(date));
        return "success";
    }

    @GetMapping("/updateWeekData")
    public String updateWeekData() {
        ppl13weekTransformService.updateWeekData();
        return "success";
    }

    @GetMapping("/reinitSpikeLatestData")
    public String reinitSpikeLatestData() {
        ppl13WeekInputService.reinitSpikeLatestData();
        return "success";
    }

    @GetMapping("/syncCbsHeaderCustomer")
    public String syncCbsHeaderCustomer() {
        ppl13weekOpsService.syncCbsHeaderCustomer();
        return "success";
    }

}
