package cloud.demand.app.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum TaskRunLogLevelEnum {

    ERROR("ERROR"),
    WARN("WARN");

    private final String code;

    TaskRunLogLevelEnum(String code) {
        this.code = code;
    }

    public static String getCodeByEnum(TaskRunLogLevelEnum logLevelEnum){
        for (TaskRunLogLevelEnum value : TaskRunLogLevelEnum.values()) {
            if (Objects.equals(value, logLevelEnum)){
                return value.getCode();
            }
        }
        return "";
    }
}
