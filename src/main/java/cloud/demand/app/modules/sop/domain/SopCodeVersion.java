package cloud.demand.app.modules.sop.domain;

import cloud.demand.app.modules.sop.entity.common.ISopCodeVersion;
import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SopCodeVersion implements ISopCodeVersion {
    /** 代码版本（1.0、2.0）{@link SopCodeVersionEnum} */
    private String codeVersion;
}
