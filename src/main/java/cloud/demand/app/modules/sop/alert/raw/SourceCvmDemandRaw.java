package cloud.demand.app.modules.sop.alert.raw;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import cloud.demand.app.modules.sop.alert.entity.SourceDemand;
import cloud.demand.app.modules.sop.enums.ExecutedType;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.pugwoo.dbhelper.DBHelper;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

public class SourceCvmDemandRaw extends AbstractSourceRaw<SourceDemand> {


    public SourceCvmDemandRaw(SopQueryParam param) {
        super(param);
    }

    @Override
    public List<SopCoreNumAlterDo> transForm(List<SourceDemand> list) {
        List<SopCoreNumAlterDo> ret = new ArrayList<>();
        for (SourceDemand sourceDemand : list) {
            // 已执行
            SopCoreNumAlterDo exeItem = new SopCoreNumAlterDo();
            exeItem.setBusinessType(sourceDemand.getBusinessType());
            exeItem.setCoreNum(sourceDemand.getExeCoreNum());
            exeItem.setIsExecuted(ExecutedType.EXECUTED.getLocalCode());
            // 未执行
            SopCoreNumAlterDo nonExeItem = new SopCoreNumAlterDo();
            nonExeItem.setBusinessType(sourceDemand.getBusinessType());
            nonExeItem.setCoreNum(sourceDemand.getNonExeCoreNum());
            nonExeItem.setIsExecuted(ExecutedType.NOT_EXECUTED.getLocalCode());
            ret.add(exeItem);
            ret.add(nonExeItem);
        }
        return ret;
    }

    @Override
    public List<SourceDemand> getRaw() {
        String versionDate = param.getVersionDate();
        // 取上一天的数据（yunti每天24点存切片数据,7号切片取6月的云梯切片）
        String beforeDate = SopDateUtils.beforeDate(versionDate);
        String monthFirstDay = SopDateUtils.getHolidayMonthFirstDay(versionDate);
        // 未执行还是当天区分
        return getDbHelper().getRaw(getDbClass(),getSql(),
                monthFirstDay,
                param.getCloudBgName(),
                beforeDate,
                param.getStartDate());
    }



    @Override
    public DBHelper getDbHelper() {
        return DBList.ckcldyuntiDBHelper;
    }


    @Override
    public String getSql() {
        return ORMUtils.getSql("/sql/sop/alert/source_demand_cvm.sql");
    }

    @Override
    public Class<SourceDemand> getDbClass() {
        return SourceDemand.class;
    }
}
