package cloud.demand.app.modules.sop.domain.http;

import cloud.demand.app.modules.sop.deserializer.SopLocalDateDeserializer;
import cloud.demand.app.modules.sop.deserializer.SopStringDeserializer;
import cloud.demand.app.modules.sop.entity.clean.OriIndexDate;
import cloud.demand.app.modules.sop.entity.clean.OriModZoneInfo;
import cloud.demand.app.modules.sop.entity.clean.OriProductInfo;
import cloud.demand.app.modules.sop.entity.clean.OriZoneCity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.LocalDate;
import lombok.Data;


/** 获取修改后采购净预测明细 */
@Data
public class SopPGRes implements OriProductInfo, OriModZoneInfo, OriZoneCity, OriIndexDate {

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourceType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourcePool;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String projectType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String businessType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String customBgName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deptName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String planProductName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String areaType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String country;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String city;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String campus;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String module;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceFamily;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceType;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceModel;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceType;
    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate date;

    private Integer year;

    private Integer month;

    private Integer week;

    private Integer isHedging;

    private Integer isValidHedging;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String version;
    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate dateVersion;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String demandSubmitYearMonth;
    private Integer amount;

    @Override
    public String getZone() {
        return null;
    }
}
