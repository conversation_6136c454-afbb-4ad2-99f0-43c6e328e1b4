package cloud.demand.app.modules.sop.service.task.dwd_task;

import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.sop.entity.dwd.IVersion;
import cloud.demand.app.modules.sop.entity.task.SopDwdTaskDO;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.enums.InsertType;
import cloud.demand.app.modules.sop.http.service.SopApiHttpService;
import cloud.demand.app.modules.sop.service.CleaningHelper;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.task.work.SopDwdAbstractWork;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.TaskUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import java.util.List;
import java.util.function.Supplier;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import yunti.boot.config.DynamicProperty;

/**
 * DWD 任务接口，每一个任务自己实现一个接口，分开写定时任务，便于管理
 */
@Slf4j
public abstract class DWDTaskService extends SopDwdAbstractWork {

    @Resource
    protected RedisHelper redisHelper;

    @Resource
    protected DBHelper ckcldStdCrpDBHelper;

    @Resource
    protected CommonDbHelper commonDbHelper;

    @Resource
    protected CleaningHelper cleaningHelper;

    @Resource
    protected SopApiHttpService sopService;

    /** 数据插入方式 */
    protected InsertType insertType = InsertType.ALL;

    private static final Supplier<String> defSize = DynamicProperty.create("sop.http.pageSize","10000");

    private static final Supplier<String> startDate = DynamicProperty.create("sop.http.startDate","20230101");


    /**
     * @return 获取默认http请求分页量，最小1k条一次
     */
    public int getDefSize(){
        int ret = 1000;
        try{
            ret = Integer.parseInt(defSize.get());
        }catch (Exception ignore){}
        return Math.max(ret,1000);
    }

    public static String getStartDate(){
        String date = startDate.get();
        try {
            DateUtils.parse(date, "yyyyMMdd");
        }catch (Exception ignore){
            // 解析失败默认 20230101
            date = "20230101";
        }
        return date;
    }

    public Integer getStartYear() {
        try {
            String date = getStartDate();
            return Integer.parseInt(date.substring(0, 4));
        } catch (Exception ignore) {
            return 2023;
        }
    }

    /**
     * 获取代表的 DWDTask 枚举，意味着每有一个新的 DWDTask 都要来枚举注册
     * @return DWDTask 枚举
     */
    public abstract DwdTaskEnum task();

    /**
     * 创建 DWD 数据 (实际执行的业务代码)
     * @param version 数据版本
     */
    public abstract void createDWDData(String version);

    @Override
    public void doWork(SopDwdTaskDO sopDwdTaskDO) {
        log.info("sopDwdTaskDO {}", sopDwdTaskDO);
        SpringUtil.getBean(this.getClass()).createDWDData(sopDwdTaskDO.getVersion());
    }


    protected <T> void delete(String verison,Class<T> tClass ){
        if (insertType.equals(InsertType.ALL)){
            CkDBUtils.delete(ckcldStdCrpDBHelper,verison,tClass);
        }
    }

    protected <T> void delete(Class<T> tClass ){
        if (insertType.equals(InsertType.ALL)){
            CkDBUtils.delete(ckcldStdCrpDBHelper,tClass);
        }
    }

    protected <T> void insert(List<T> saveData){
        if (ListUtils.isNotEmpty(saveData)){
            ckcldStdCrpDBHelper.insertBatchWithoutReturnId(saveData);
        }
    }

    protected <T extends IVersion> void insertVersion(List<T> saveData){
        ckcldStdCrpDBHelper.insertBatchWithoutReturnId(saveData);
    }

    /** 按版本分区删除后插入 */
    protected <T> void insert(String version, List<T> saveData, Class<T> tClass){
        CkDBUtils.insert(insertType, ckcldStdCrpDBHelper,version,saveData,tClass);
    }

    /** 全量删除后插入 */
    protected <T> void insert(List<T> saveData, Class<T> tClass){
        CkDBUtils.insert(insertType, ckcldStdCrpDBHelper,saveData,tClass);
    }

    /** 可以重复调用实现集合追加 */
    public <T> void initLocalMap(List<T> oriData){
        cleaningHelper.initLocalMap(oriData);
    }

    public <T> void cleaning(T o){
        cleaningHelper.cleaning(o);
    }

}
