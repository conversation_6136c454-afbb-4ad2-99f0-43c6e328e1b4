package cloud.demand.app.modules.sop.util;

import static io.opentracing.tag.Tags.SPAN_KIND;

import io.opentracing.Scope;
import io.opentracing.Span;
import io.opentracing.Tracer;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class SpanUtil {
    @Resource
    private Tracer tracer;

    public Span createSpan(String operationName) {
        return tracer.buildSpan(operationName)
                .withTag(SPAN_KIND, "server")
                .start();
    }
    public Scope active(Span span) {
        return tracer.scopeManager().activate(span);
    }

    public void close(Scope scope) {
        scope.close();
    }
}
