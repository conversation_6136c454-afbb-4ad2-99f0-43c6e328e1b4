package cloud.demand.app.modules.sop.http.service;


import cloud.demand.app.modules.sop.domain.SopCommonPageReq;
import cloud.demand.app.modules.sop.domain.SopCommonReq;
import cloud.demand.app.modules.sop.domain.SopCommonSimplePageReq;
import cloud.demand.app.modules.sop.domain.SopServerPageReq;
import cloud.demand.app.modules.sop.domain.http.SopCvmDemandResList;
import cloud.demand.app.modules.sop.domain.http.SopCvmReturnResList;
import cloud.demand.app.modules.sop.domain.http.SopDataVersionRes;
import cloud.demand.app.modules.sop.domain.http.SopHedgingDetailRes;
import cloud.demand.app.modules.sop.domain.http.SopHedgingResultRes;
import cloud.demand.app.modules.sop.domain.http.SopPGRes;
import cloud.demand.app.modules.sop.domain.http.SopPageRes;
import cloud.demand.app.modules.sop.domain.http.SopPurchaseDetailRes;
import cloud.demand.app.modules.sop.domain.http.SopServerDemandResList;
import cloud.demand.app.modules.sop.domain.http.SopServerReturnResList;
import cloud.demand.app.modules.sop.domain.http.SopStockDetailRes;
import cloud.demand.app.modules.sop.domain.http.SopTransferAndReplaceDetailRes;
import cloud.demand.app.modules.sop.domain.http.SopTransformRes;
import cs.easily.tp.annotation.TpApi;
import cs.easily.tp.annotation.TpBody;
import cs.easily.tp.annotation.TpClient;
import java.util.List;

/**
 * api文档[<a href="https://iwiki.woa.com/pages/viewpage.action?pageId=4008545602">...</a>]
 */
@TpClient(baseUrl = "${report.sop-gateway}", prefix = "api/inventory-report", desc = "sop提供的接口", apikey = true, readTimeoutSeconds = 60)
public interface SopApiHttpService {

    @TpApi(path = "getAllHedgingDataVersion", desc = "获取对冲版本号，日期版本")
    public List<SopDataVersionRes> getAllHedgingDataVersion();

    @TpApi(path = "getSopHedgingDetailByVersion", desc = "获取对冲明细")
    public SopHedgingDetailRes getSopHedgingDetailByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getHedgingResultByVersion", desc = "获取对冲结果明细")
    public SopPageRes<SopHedgingResultRes> getHedgingResultByVersion(@TpBody SopCommonSimplePageReq req);

    @TpApi(path = "getSopTransferAndReplaceDetailByVersion", desc = "获取转移和置换明细")
    public SopTransferAndReplaceDetailRes getSopTransferAndReplaceDetailByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getSopPurchaseDetailByVersion", desc = "获取采购明细")
    public SopPurchaseDetailRes getSopPurchaseDetailByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getSopStockStartDetailByVersion", desc = "获取期初可用库存")
    public SopPageRes<SopStockDetailRes> getSopStockStartDetailByVersion(@TpBody SopCommonPageReq req);

    @TpApi(path = "getSopStockEndNoValidDetailByVersion", desc = "获取期末不可用库存")
    public SopPageRes<SopStockDetailRes> getSopStockEndNoValidDetailByVersion(@TpBody SopCommonPageReq req);

    @TpApi(path = "getSopStockEndValidDetailByVersion", desc = "获取期末可用库存")
    public SopPageRes<SopStockDetailRes> getSopStockEndValidDetailByVersion(@TpBody SopCommonPageReq req);

    @TpApi(path = "getSopSourceTransformByVersion", desc = "获取改造前原始机型")
    public SopTransformRes getSopSourceTransformByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getSopTargetTransformByVersion", desc = "获取改造后目标机型")
    public SopTransformRes getSopTargetTransformByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getPGByVersion", desc = "获取修改后采购净预测明细")
    public List<SopPGRes> getPGByVersion(@TpBody SopCommonReq req);

    @TpApi(path = "getServerReturnByVersion", desc = "获取物理机退回明细")
    public SopPageRes<SopServerReturnResList> getServerReturnByVersion(@TpBody SopServerPageReq req);

    @TpApi(path = "getServerDemandByVersion", desc = "获取物理机需求明细")
    public SopPageRes<SopServerDemandResList> getServerDemandByVersion(@TpBody SopServerPageReq req);

    @TpApi(path = "getCvmReturnByVersion", desc = "获取CVM退回明细")
    public SopPageRes<SopCvmReturnResList> getCvmReturnByVersion(@TpBody SopServerPageReq req);

    @TpApi(path = "getCvmDemandByVersion", desc = "获取CVM需求明细")
    public SopPageRes<SopCvmDemandResList> getCvmDemandByVersion(@TpBody SopServerPageReq req);

}
