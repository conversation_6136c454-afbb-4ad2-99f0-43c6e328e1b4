package cloud.demand.app.modules.sop.util;

import cloud.demand.app.modules.sop.exception.SopException;
import java.util.Arrays;
import java.util.Iterator;
import java.util.function.BiFunction;
import org.apache.commons.lang3.ObjectUtils;

/** dwd2dws分页组件 */
public class SopDwd2DwsIteratorUtil implements Iterator<long[][]> {

    private final BiFunction<Long,Long,Long>[] nextIdFunArr;

    private final String[] indexTypeArr;

    private final int length;

    private final long[] currentIdArr;

    private final long[] nextIdArr;

    private long size;

    private long defId;

    public SopDwd2DwsIteratorUtil(BiFunction<Long,Long,Long>[] nextIdFunArr, String[] indexTypeArr,long size) throws SopException {
        this.nextIdFunArr = nextIdFunArr;
        this.indexTypeArr = indexTypeArr;
        if (nextIdFunArr == null || indexTypeArr == null || nextIdFunArr.length != indexTypeArr.length){
            throw new SopException("id获取方法长度和指标类型长度不同或者任意一个为空");
        }
        this.size = size;
        this.length = this.nextIdFunArr.length;
        this.currentIdArr = new long[length];
        this.nextIdArr = new long[length];
        this.init();
    }

    private void init(){
        this.size = Math.max(size,100);
        this.defId = CkDBUtils.startId() - 1;
        Arrays.fill(currentIdArr,-1);
        Arrays.fill(nextIdArr,defId);
    }

    @Override
    public boolean hasNext() {
        for (int i = 0; i < this.length; i++) {
            if (currentIdArr[i] != nextIdArr[i]){
                return true;
            }
        }
        return false;
    }

    @Override
    public long[][] next() {
        for (int i = 0; i < length; i++) {
            long startId = currentIdArr[i];
            long endId = nextIdArr[i];
            if (startId != endId){
                // 注意 这个方法如果查不到匹配的数据，会返回0， 即此时代表没有合适的数据了，
                // 那么把currentIdArr，nextIdArr两个数组的值变成完全一致，hasNext的时候就会返回false了
                Long nextId = nextIdFunArr[i].apply(endId, size);
                if (nextId < endId){
                    nextIdArr[i] = startId;
                    continue;
                }
                startId = endId;
                endId = ObjectUtils.defaultIfNull(nextId,defId);
                currentIdArr[i] = startId;
                nextIdArr[i] = endId;
            }
        }
        return new long[][]{currentIdArr,nextIdArr};
    }

    public String[] getIndexTypeArr() {
        return indexTypeArr;
    }
}
