package cloud.demand.app.modules.sop.entity.dwd;

import cloud.demand.app.modules.sop.entity.clean.CvmDevice;
import cloud.demand.app.modules.sop.entity.clean.CvmFamily;
import cloud.demand.app.modules.sop.entity.clean.CvmM12Core;
import cloud.demand.app.modules.sop.entity.clean.DeviceFamily;
import cloud.demand.app.modules.sop.entity.clean.DeviceM12Core;
import cloud.demand.app.modules.sop.entity.clean.IndexDate;
import cloud.demand.app.modules.sop.entity.clean.ModZoneInfo;
import cloud.demand.app.modules.sop.entity.clean.ProductInfo;
import cloud.demand.app.modules.sop.entity.clean.ZoneCity;
import cloud.demand.app.modules.sop.entity.common.SopCodeVersionDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Table("dwd_sop_report_inventory_unavailable")
public class DwdSopReportInventoryUnavailableDO extends SopCodeVersionDO implements CvmDevice, CvmM12Core, CvmFamily, DeviceM12Core, DeviceFamily, ProductInfo, ModZoneInfo, ZoneCity, IndexDate,IVersion {
    /** 代表数据版本<br/>Column: [version] */
    @Column(value = "version")
    private String version;

    /** 类似于 MYSQL 的自增 ID，靠内存写入，方便下游 dws 宽表分页拉取数据<br/>Column: [id] */
    @Column(value = "id")
    private Long id;

    /** 数据版本生成时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDateTime statTime;

    /** 设备数<br/>Column: [num] */
    @Column(value = "num")
    private BigDecimal[] num;

    /** 核心数<br/>Column: [core_num] */
    @Column(value = "core_num")
    private BigDecimal[] coreNum;

    /** 容量<br/>Column: [capacity] */
    @Column(value = "capacity")
    private BigDecimal[] capacity;

    /** 指标日期<br/>Column: [index_date] */
    @Column(value = "index_date")
    private LocalDate indexDate;

    /** 指标年份<br/>Column: [index_year] */
    @Column(value = "index_year")
    private Integer indexYear;

    /** 指标月份<br/>Column: [index_month] */
    @Column(value = "index_month")
    private Integer indexMonth;

    /** 指标年月<br/>Column: [index_year_month] */
    @Column(value = "index_year_month")
    private String indexYearMonth;

    /** 指标周数（全年第 n 周）<br/>Column: [index_week] */
    @Column(value = "index_week")
    private Integer indexWeek;

    /** 资源类型<br/>Column: [res_type] */
    @Column(value = "res_type")
    private String resType;

    /** 资源池类型<br/>Column: [res_pool_type] */
    @Column(value = "res_pool_type")
    private String resPoolType;

    /** 项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** BG名<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 自定义BG名<br/>Column: [custom_bg_name] */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /** 部门名<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品名<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 国内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 城市：可同时表示物理机城市，也可以表示腾讯云 region<br/>Column: [city_name] */
    @Column(value = "city_name")
    private String cityName;

    /** cmdb campus<br/>Column: [cmdb_campus_name] */
    @Column(value = "cmdb_campus_name")
    private String cmdbCampusName;

    /** cmdb module<br/>Column: [cmdb_module_name] */
    @Column(value = "cmdb_module_name")
    private String cmdbModuleName;

    /** 腾讯云 zone<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name")
    private String txyZoneName;

    /** cvm 实例类型<br/>Column: [cvm_gins_family] */
    @Column(value = "cvm_gins_family")
    private String cvmGinsFamily;

    /** cvm 实例规格<br/>Column: [cvm_gins_type] */
    @Column(value = "cvm_gins_type")
    private String cvmGinsType;

    /** cvm 机型族<br/>Column: [cvm_gins_family] */
    @Column(value = "cvm_gins_kingdom")
    private String cvmGinsKingdom;

    /** 物理机机型族<br/>Column: [phy_device_family] */
    @Column(value = "phy_device_family")
    private String phyDeviceFamily;

    /** 物理机设备类型<br/>Column: [phy_device_type] */
    @Column(value = "phy_device_type")
    private String phyDeviceType;

    /** 是否CA，0 否 1 是<br/>Column: [is_ca] */
    @Column(value = "is_ca")
    private Integer isCa;

    /** 业务类型，云业务; 自研业务<br/>Column: [business_type] */
    @Column(value = "business_type")
    private String businessType;

    /** obs业务类型<br/>Column: [obs_business_type] */
    @Column(value = "obs_business_type")
    private String obsBusinessType;

    /** 是否参与对冲，0 否 1 是<br/>Column: [is_hedge] */
    @Column(value = "is_hedge")
    private Integer isHedge;

    /** 是否有效对冲，0 否 1 是<br/>Column: [has_hedged] */
    @Column(value = "has_hedged")
    private Integer hasHedged;


    /** 额外字段<br/>Column: [json_text] */
    @Column(value = "json_text")
    private String jsonText;

    /** 容量单位<br/>Column: [capacity_unit] */
    @Column(value = "capacity_unit")
    private String capacityUnit;
}
