package cloud.demand.app.modules.sop.service.task.dwd_task.physical;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReturnDeviceExtDO;
import cloud.demand.app.modules.sop.entity.other.DwdYuntiDeviceReturnPlanItemDfDO;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.PlanTypeEnum;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.exception.SopException;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

/**
 * cvm退回预测 DWD 接口
 */
@Service
@Slf4j
public class PhysicalReturnExtTask extends DWDTaskService {

    @Resource
    private DBHelper ckcldyuntiDBHelper;

    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.PHYSICAL_NEW_RETURN_EXT;
    }

    @SneakyThrows
    @Override
    // 这个 taskLog 是唯一没法抽象的东西，只能自己写好这个字符串，规则："sopCreateDWDData@" + 类名
    @TaskLog(taskName = "sopCreateDWDData@PhysicalReturnExtTask")
    public void createDWDData(String version) {
        //这里只需要写怎么从 API 接口里拉取数据，并写入到 CK 的对于 DWD 表取即可，无需关心定时任务逻辑，已经抽象分离了
        StopWatch stopWatch = new StopWatch("物理机退回扩展");
        stopWatch.start("1. 情况ck版本分区数据");
        delete(version, DwdSopReportReturnDeviceExtDO.class);
        stopWatch.stop();
        stopWatch.start("2. 请求上游api获取退回数据");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
        AtomicLong startId = new AtomicLong(CkDBUtils.startId());
        LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());
        // 拉取物理机退回计划数据
        doProcess(version,dateVersion,startId,statTime);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    @SneakyThrows
    private void doProcess(String version,
                           LocalDate dateVersion,
                           AtomicLong startId,
                           LocalDateTime statTime) {
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();

        // 云梯取昨天的数据
        String beforeDate = SopDateUtils.beforeDate(dateVersion);
        whereContent.andEqual(DwdYuntiDeviceReturnPlanItemDfDO::getStatDate, beforeDate);
        List<DwdYuntiDeviceReturnPlanItemDfDO> raw = ORMUtils.db(ckcldyuntiDBHelper).getAll(DwdYuntiDeviceReturnPlanItemDfDO.class, whereContent);
        if (CollectionUtils.isEmpty(raw) && EnvUtils.isProduction()){
            throw new SopException(String.format("物理机退回计划云梯数据为空（云梯取昨天的数据），切片日期：[%s]", beforeDate));
        }
        // 已申领数据
        List<DwdSopReportReturnDeviceExtDO> saveData = ListUtils.transform(raw,item->transform(item,version,true,startId,statTime));
        // 未执行数据
        saveData.addAll(ListUtils.transform(raw,item->transform(item,version,false,startId,statTime)));
        // 过滤掉核心数为0的
        saveData = saveData.stream().filter(item->item.getCoreNum().compareTo(BigDecimal.ZERO)>0
                || item.getNum().compareTo(BigDecimal.ZERO)>0).collect(Collectors.toList());
        // 存储ck
        insert(saveData);
    }


    private DwdSopReportReturnDeviceExtDO transform(DwdYuntiDeviceReturnPlanItemDfDO item,
                                                          String version,
                                                          boolean isExecuted,
                                                          AtomicLong startId,
                                                          LocalDateTime statTime) {
        DwdSopReportReturnDeviceExtDO ret = new DwdSopReportReturnDeviceExtDO();
        ret.setId(startId.getAndIncrement());
        ret.setVersion(version);
        ret.setStatTime(statTime);
        // 核心数（计划内取已申领或未执行）
        BigDecimal num;
        BigDecimal coreNum;
        if (isExecuted){
            num = item.getAppliedDeviceAmount();
            coreNum = item.getAppliedCoreAmount();
        }else {
            num = item.getDeviceAmount().subtract(item.getAppliedDeviceAmount());
            coreNum = item.getCoreAmount().subtract(item.getAppliedCoreAmount());
        }
        ret.setNum(num);
        ret.setCoreNum(coreNum);
        // 未执行无销毁类型，不是ca，给 0
        ret.setIsCa(FlagType.NO.getCode());
        ret.setPlanType(item.getPlanType());

        SopDateUtils.dateTransform(() -> SopDateUtils.toLocalDate(item.getPlanTime()), ret);
        ret.setResType(ResourceType.DEVICE.getName());

        ret.setObsProjectType(item.getProjectName());
        ret.setBgName(item.getBgName());
        ret.setCustomBgName(item.getCustomBgName());
        ret.setDeptName(item.getDeptName());
        ret.setPlanProductName(item.getPlanProductName());
        ret.setCountryName(item.getCountryName());

        // 无城市，固资等信息
//        ret.setCityName(item.getCityName());
//        ret.setTxyZoneName(item.getZoneName());
//        ret.setResPoolType(Constant.EMPTY_VALUE_STR);
//        ret.setAssetId(Constant.EMPTY_VALUE_STR);

        ret.setPhyDeviceType(item.getDeviceType());

        // 未执行计划内默认参与对冲
        ret.setIsHedge(FlagType.getCode(item.getPlanType().equals(PlanTypeEnum.IN_PLAN.getCode())));
        ret.setIsExecuted(FlagType.getCode(isExecuted));
//        ret.setBusinessType(item.getBusinessType());
//        ret.setObsBusinessType(item.getBusinessType());
        ret.setHasHedged(FlagType.getCode(item.getPlanType().equals(PlanTypeEnum.IN_PLAN.getCode())));
        ret.setJsonText(null);
        cleaning(ret);
        return ret;
    }
}
