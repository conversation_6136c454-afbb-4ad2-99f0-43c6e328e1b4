package cloud.demand.app.modules.sop.domain.http;

import cloud.demand.app.modules.sop.deserializer.SopLocalDateDeserializer;
import cloud.demand.app.modules.sop.deserializer.SopStringDeserializer;
import cloud.demand.app.modules.sop.entity.clean.OriIndexDate;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class SopTransferAndReplaceDetailResList implements OriIndexDate {
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourceType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourcePool;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inProjectType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outProjectType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inCustomBgName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outCustomBgName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inDeptName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outDeptName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inPlanProductName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outPlanProductName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String areaType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String module;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inCountry;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inCity;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inCampus;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outCountry;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outCity;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outCampus;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceFamily;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceModel;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceType;

    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate date;
    private Integer year;
    private Integer month;
    private Integer week;
    private Integer isHedging;
    private Integer isValidHedging;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String assetId;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inBusinessType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outBusinessType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inProduct;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outProduct;
    private LocalDateTime inStockTime;

    private LocalDate inTime;
    private LocalDate outTime;
    private LocalDateTime outStockTime;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String inProcess;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String outProcess;
}
