package cloud.demand.app.modules.sop.deserializer;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class SopLocalDateDeserializer extends LocalDateDeserializer {

    @Override
    public LocalDate deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        if (p.hasToken(JsonToken.VALUE_STRING)){
            if (p.getText().trim().equals("0000-00-00")){
                return null;
            }
        }
        LocalDate ret = null;
        try{
           ret = super.deserialize(p, ctxt);
        }catch (Exception ignore){}
        return ret;
    }



    @Override
    protected LocalDateDeserializer withDateFormat(DateTimeFormatter dtf) {
        return new SopLocalDateDeserializer(dtf);
    }

    @Override
    protected LocalDateDeserializer withLeniency(Boolean leniency) {
        return new SopLocalDateDeserializer(this,leniency);
    }

    @Override
    protected LocalDateDeserializer withShape(JsonFormat.Shape shape) {
        return new SopLocalDateDeserializer(this,shape);
    }

    public SopLocalDateDeserializer() {
        super();
    }

    public SopLocalDateDeserializer(DateTimeFormatter dtf) {
        super(dtf);
    }

    public SopLocalDateDeserializer(LocalDateDeserializer base, DateTimeFormatter dtf) {
        super(base, dtf);
    }

    public SopLocalDateDeserializer(LocalDateDeserializer base, Boolean leniency) {
        super(base, leniency);
    }

    public SopLocalDateDeserializer(LocalDateDeserializer base, JsonFormat.Shape shape) {
        super(base, shape);
    }
}
