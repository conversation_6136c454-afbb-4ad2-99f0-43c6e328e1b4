package cloud.demand.app.modules.sop.service.task.dwd_task.physical;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.domain.SopServerPageReq;
import cloud.demand.app.modules.sop.domain.http.SopPageRes;
import cloud.demand.app.modules.sop.domain.http.SopServerReturnResList;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportReturnDeviceDO;
import cloud.demand.app.modules.sop.enums.*;
import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import cloud.demand.app.modules.sop.enums.codeVersion.version.SopCodeVersionDwdEnum;
import cloud.demand.app.modules.sop.enums.codeVersion.version.SopDoProcessType;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop.util.SopHttpIteratorUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

/**
 * 物理机退回预测 DWD 接口
 */
@Service
@Slf4j
public class PhysicalReturnTask extends DWDTaskService {

    private final LocalDate coreNumLimitDate = LocalDate.parse("2023-11-15", DateTimeFormatter.ISO_LOCAL_DATE);

    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.PHYSICAL_NEW_RETURN;
    }

    @SneakyThrows
    @Override
    // 这个 taskLog 是唯一没法抽象的东西，只能自己写好这个字符串，规则："sopCreateDWDData@" + 类名
    @TaskLog(taskName = "sopCreateDWDData@PhysicalReturnTask")
    public void createDWDData(String version) {
        //这里只需要写怎么从 API 接口里拉取数据，并写入到 CK 的对于 DWD 表取即可，无需关心定时任务逻辑，已经抽象分离了
        StopWatch stopWatch = new StopWatch("物理机退回预测");
        stopWatch.start("1. 情况ck版本分区数据");
        delete(version, DwdSopReportReturnDeviceDO.class);
        stopWatch.stop();
        stopWatch.start("2. 请求上游api获取退回数据");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
        AtomicLong startId = new AtomicLong(CkDBUtils.startId());
        LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());
        String startDate = getStartDate();

        for (String codeVersion : SopCodeVersionEnum.getCodeList()) {
            doProcessWithCodeVersion(version,dateVersion,startId,statTime,startDate,codeVersion);
        }

        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    private void doProcessWithCodeVersion(String version,
                                          LocalDate dateVersion,
                                          AtomicLong startId,
                                          LocalDateTime statTime,
                                          String startDate,
                                          String codeVersion){
        // 已执行
        doProcess(version, ExecutedType.EXECUTED, dateVersion, startId, statTime, startDate, codeVersion);
        // 进行中
        doProcess(version, ExecutedType.RUNNING, dateVersion, startId, statTime, startDate, codeVersion);
        // 未执行
        doProcess(version, ExecutedType.NOT_EXECUTED, dateVersion, startId, statTime, startDate, codeVersion);
    }

    private void doProcess(String version,
                           ExecutedType executedType,
                           LocalDate indexDate,
                           AtomicLong startId,
                           LocalDateTime statTime,
                           String startDate,
                           String codeVersion) {

        // step1：参数准备（调上游状态码，是否已执行，代码版本）
        int dataType = executedType.getCode();
        boolean isExecuted = ExecutedType.isExec(executedType);
        SopDoProcessType doProcessType = SopCodeVersionDwdEnum.getDoProcessType(task(), codeVersion, isExecuted);

        if (doProcessType == SopDoProcessType.notDo){
            return;
        }

        // 获取转换后的代码版本
        String saveCodeVersion = doProcessType.getGetCodeVersion().apply(codeVersion);

        // step2：拉取上游数据
        long count = 0L;
        try {
            int current = 0;
            int size = getDefSize();

            String dateVersion = SopDateUtils.dateVersion(indexDate);
            SopServerPageReq pageReq = SopServerPageReq.builder()
                    .version(version)
                    .pageNum(current)
                    .pageSize(size)
                    .startDate(startDate)
                    .dateVersion(dateVersion)
                    .dataType(dataType)
                    .build();
            // 上游的代码版本
            String upStreamCodeVersion = SopCodeVersionEnum.code2UpStreamCode(codeVersion);
            pageReq.setCodeVersion(upStreamCodeVersion);
            SopPageRes<SopServerReturnResList> firstRes = sopService.getServerReturnByVersion(pageReq);
            if (firstRes == null || CollectionUtils.isEmpty(firstRes.getBody())) {
                return;
            }
            if (!isExecuted) {
                count = firstRes.getTotal();
            }
            // 初始化本地清洗映射结合(可追加)
            initLocalMap(firstRes.getBody());
            List<DwdSopReportReturnDeviceDO> saveData = getSaveData(version, executedType, indexDate, startId, statTime, firstRes.getBody(), saveCodeVersion);
            if (isExecuted) {
                count += saveData.size();
            }
            insert(saveData);

            if (firstRes.getTotal() <= size) {
                return;
            }
            current++;
            long total = firstRes.getTotal() - size;

            SopHttpIteratorUtil<SopServerReturnResList> iterator = new SopHttpIteratorUtil<>(size, current, total,
                    (req) -> {
                        SopServerPageReq build = SopServerPageReq.builder()
                                .version(version)
                                .pageNum(req.getCurrent())
                                .pageSize(req.getSize())
                                .startDate(startDate)
                                .dataType(dataType)
                                .dateVersion(dateVersion)
                                .build();
                        build.setCodeVersion(upStreamCodeVersion);
                        return sopService.getServerReturnByVersion(build);
                    });
            while (iterator.hasNext()) {
                List<SopServerReturnResList> body = iterator.next();
                if (!CollectionUtils.isEmpty(body)) {
                    // 初始化本地清洗映射结合(可追加)
                    initLocalMap(body);
                    saveData = getSaveData(version, executedType, indexDate, startId, statTime, body, saveCodeVersion);
                    if (isExecuted) {
                        count += saveData.size();
                    }
                    insert(saveData);
                }
            }
        } finally {
            log.info("DWD执行CVM退回 ----> version: [{}], indexDate: [{}], isExecuted: [{}], count: [{}]", version, indexDate, isExecuted, count);
        }
    }

    private List<DwdSopReportReturnDeviceDO> getSaveData(String version,
                                                         ExecutedType executedType,
                                                         LocalDate dateVersion,
                                                         AtomicLong startId,
                                                         LocalDateTime statTime,
                                                         List<SopServerReturnResList> body,
                                                         String codeVersion) {
        if (ExecutedType.isExec(executedType)) {
            return body.stream()
                    .filter((item) -> DataTransformUtil.isPositive(item.getExeAmount()))
                    .map((item) -> transform(item, executedType, version, dateVersion, startId, statTime, codeVersion))
                    .collect(Collectors.toList());
        } else {
            return ListUtils.transform(body, (item) -> transform(item, executedType, version, dateVersion, startId, statTime, codeVersion));
        }
    }

    private DwdSopReportReturnDeviceDO transform(SopServerReturnResList item,
                                                 ExecutedType executedType,
                                                 String version,
                                                 LocalDate dateVersion,
                                                 AtomicLong startId,
                                                 LocalDateTime statTime,
                                                 String codeVersion) {
        DwdSopReportReturnDeviceDO ret = new DwdSopReportReturnDeviceDO();

        // 是否已执行
        boolean isExecuted = ExecutedType.isExec(executedType);

        // 设置代码版本
        ret.setCodeVersion(codeVersion);

        ret.setId(startId.getAndIncrement());
        ret.setVersion(version);
        ret.setStatTime(statTime);
        ret.setNum(ObjectUtils.defaultIfNull(isExecuted ? item.getExeAmount() : item.getNonAmount(), BigDecimal.ZERO));
        if (isExecuted){
            // 这里上游核数在2023-11-15号（包含）之前都有问题
            if (coreNumLimitDate.isBefore(dateVersion)){
                ret.setCoreNum(item.getCpuExeAmount());
            }
        }
        // 默认isCa为false
        ret.setIsCa(FlagType.NO.getCode());
        SopDateUtils.dateTransform(item, ret);
        ret.setResType(item.getResourceType());
        ret.setResPoolType(SopResourcePool.getDesc(item.getResourcePool()));
        ret.setObsProjectType(item.getProjectType());
        // 没有数据
        ret.setBgName(item.getBgName());
        ret.setCustomBgName(item.getCustomBgName());
        ret.setDeptName(item.getDeptName());
        ret.setPlanProductName(item.getPlanProductName());
        ret.setCustomhouseTitle(item.getAreaType());
        ret.setCountryName(item.getCountry());
        ret.setCityName(item.getCity());
        ret.setCmdbCampusName(item.getCampus());
        ret.setCmdbModuleName(item.getModule());
        // 采购没有可用区间
//        ret.setTxyZoneName(item.getTxyZoneName());
        ret.setPhyDeviceFamily(item.getDeviceFamily());
        ret.setPhyDeviceType(item.getDeviceType());
        ret.setIsHedge(item.getIsHedging());
        ret.setIsExecuted(isExecuted ? FlagType.YES.getCode() : FlagType.NO.getCode());
        ret.setBusinessType(item.getBusinessType());
        // 用obs业务类型存上游业务类型，该字段不做清理
        ret.setObsBusinessType(item.getBusinessType());
        ret.setHasHedged(item.getIsValidHedging());
        ret.setAssetId(item.getAssetCode());
        ret.setReturnReasonType(item.getReasonClass());
        ret.setReturnTag(item.getAllocationMemo());
        ret.setOriReturnTag(item.getInitAllocationMemo());
        ret.setReturnUsedYear(item.getReturnUsedYear());
        // 云退回
        if (isExecuted){
            ret.setIsCloudReturn(IsCloudReturn.getCode(item.getIsCloudReturn()));
        }
        // 执行状态
        ret.setReturnExecStatus(ReturnExecStatus.getByExecType(executedType));
        // 订单id
        ret.setOrderId(item.getOrderId());
        ret.setJsonText(null);
        cleaning(ret);
        return ret;
    }
}
