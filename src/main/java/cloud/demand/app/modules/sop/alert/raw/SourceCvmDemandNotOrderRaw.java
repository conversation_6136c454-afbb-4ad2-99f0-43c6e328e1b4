package cloud.demand.app.modules.sop.alert.raw;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import cloud.demand.app.modules.sop.alert.entity.SourceDemand;
import cloud.demand.app.modules.sop.enums.ExecutedType;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.pugwoo.dbhelper.DBHelper;

import java.util.ArrayList;
import java.util.List;

/** cvm 需求未结单的数据 */
public class SourceCvmDemandNotOrderRaw extends AbstractSourceRaw<SourceDemand> {


    public SourceCvmDemandNotOrderRaw(SopQueryParam param) {
        super(param);
    }

    @Override
    public List<SopCoreNumAlterDo> transForm(List<SourceDemand> list) {
        List<SopCoreNumAlterDo> ret = new ArrayList<>();
        for (SourceDemand sourceDemand : list) {
            // 已执行
            SopCoreNumAlterDo exeItem = new SopCoreNumAlterDo();
            exeItem.setBusinessType(sourceDemand.getBusinessType());
            exeItem.setCoreNum(sourceDemand.getExeCoreNum());
            exeItem.setIsExecuted(ExecutedType.EXECUTED.getLocalCode());
            // 未执行
            SopCoreNumAlterDo nonExeItem = new SopCoreNumAlterDo();
            nonExeItem.setBusinessType(sourceDemand.getBusinessType());
            nonExeItem.setCoreNum(sourceDemand.getNonExeCoreNum());
            nonExeItem.setIsExecuted(ExecutedType.NOT_EXECUTED.getLocalCode());
            ret.add(exeItem);
            ret.add(nonExeItem);
        }
        return ret;
    }

    @Override
    public List<SourceDemand> getRaw() {
        String versionDate = param.getVersionDate();
        String monthFirstDay = SopDateUtils.getMonthFirstDay(versionDate); // 月首
        // 取当天的数据（offset_demand_remain_snapshot时间和 sop 同步）
        return getDbHelper().getRaw(getDbClass(),getSql(),
                param.getCloudBgName(),
                versionDate,
                monthFirstDay);
    }



    @Override
    public DBHelper getDbHelper() {
        return DBList.yuntiDBHelper;
    }


    @Override
    public String getSql() {
        return ORMUtils.getSql("/sql/sop/alert/source_demand_not_order_cvm.sql");
    }

    @Override
    public Class<SourceDemand> getDbClass() {
        return SourceDemand.class;
    }
}
