package cloud.demand.app.modules.sop.enums;

import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FlagType {
    YES(1,"是",true),
    NO(0,"否",false),
    ;

    public static final String EMPTY = Constant.EMPTY_VALUE_STR;

    private final Integer code;
    private final String desc;

    private final boolean bol;

    public static int getCode(boolean flag){
        return flag ? YES.getCode() : NO.getCode();
    }

    public static String getDesc(Integer code){
        return Objects.equal(code , YES.getCode()) ? YES.getDesc() : NO.getDesc();
    }

    public static String getDesc(boolean flag){
        return flag ? YES.getDesc() : NO.getDesc();
    }

    public static FlagType getByDesc(String isCoreComponents) {
        for (FlagType value : values()) {
            if (Objects.equal(isCoreComponents, value.getDesc())){
                return value;
            }
        }
        return null;
    }
}
