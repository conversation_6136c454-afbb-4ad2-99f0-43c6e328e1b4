package cloud.demand.app.modules.sop.service.task.dwd_task.physical;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.domain.SopCommonReq;
import cloud.demand.app.modules.sop.domain.http.SopTransformRes;
import cloud.demand.app.modules.sop.domain.http.SopTransformResList;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportTransformationDeviceDO;
import cloud.demand.app.modules.sop.enums.*;
import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

/**
 * 物理机改造预测 DWD 接口
 */
@Service
@Slf4j
public class PhysicalTransformationTask extends DWDTaskService {

    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.PHYSICAL_NEW_TRANSFORMATION;
    }

    @SneakyThrows
    @Override
    // 这个 taskLog 是唯一没法抽象的东西，只能自己写好这个字符串，规则："sopCreateDWDData@" + 类名
    @TaskLog(taskName = "sopCreateDWDData@PhysicalTransformationTask")
    public void createDWDData(String version) {
        //这里只需要写怎么从 API 接口里拉取数据，并写入到 CK 的对于 DWD 表取即可，无需关心定时任务逻辑，已经抽象分离了
        StopWatch stopWatch = new StopWatch("物理机改造预测");

        stopWatch.start("1. 情况ck版本分区数据");

        delete(version, DwdSopReportTransformationDeviceDO.class);

        stopWatch.stop();

        stopWatch.start("2. 请求上游api获取改造数据");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);

        stopWatch.stop();

        // 起始id
        AtomicLong startId = new AtomicLong(CkDBUtils.startId());

        LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());

        stopWatch.start("2.1. 查询上游数据");

        List<String> codeList = SopCodeVersionEnum.getCodeList();

        for (String codeVersion : codeList) {
            doProcessWithCodeVersion(version,dateVersion,startId,statTime,codeVersion);
        }

        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    private void doProcessWithCodeVersion(String version, LocalDate dateVersion, AtomicLong startId, LocalDateTime statTime, String codeVersion){
        // 原始机型改造数据
        doProcess(version, dateVersion, startId, statTime, true, codeVersion);
        // 目标机型改造数据
        doProcess(version,  dateVersion, startId, statTime, false, codeVersion);
    }

    private void doProcess(String version, LocalDate dateVersion, AtomicLong startId, LocalDateTime statTime, boolean isSource, String codeVersion) {
        SopCommonReq req = SopCommonReq.builder().version(version).dateVersion(SopDateUtils.dateVersion(dateVersion)).build();

        // 上游代码版本可能和sop的不统一，这里做一下转换
        String upStreamCode = SopCodeVersionEnum.code2UpStreamCode(codeVersion);

        // 设置代码版本
        req.setCodeVersion(upStreamCode);

        SopTransformRes sourceRes = isSource ? sopService.getSopSourceTransformByVersion(req) : sopService.getSopTargetTransformByVersion(req);

        long count = 0L;

        if (sourceRes != null) {
            List<SopTransformResList> sourceResExecuted = sourceRes.getExecuted();

            if (!CollectionUtils.isEmpty(sourceResExecuted)) {
                initLocalMap(sourceResExecuted);
                List<DwdSopReportTransformationDeviceDO> saveData = sourceResExecuted.stream()
                        .filter(item -> DataTransformUtil.isPositive(item.getExeAmount()))
                        .map((item) -> transform(item, version, startId, statTime, isSource, true, codeVersion))
                        .collect(Collectors.toList());
                insert(saveData);
                count += saveData.size();
            }

            List<SopTransformResList> sourceResNotExecuted = sourceRes.getNotExecuted();

            if (!CollectionUtils.isEmpty(sourceResNotExecuted)) {
                initLocalMap(sourceResNotExecuted);
                List<DwdSopReportTransformationDeviceDO> saveData = ListUtils.transform(sourceResNotExecuted,
                        (item) -> transform(item, version, startId, statTime, isSource, false, codeVersion));
                insert(saveData);
                count += saveData.size();
            }

        }
        log.info("DWD执行物理机改造 ----> version: [{}], indexDate: [{}], 原/目机标型: [{}], count: [{}]", version, isSource, dateVersion, count);
    }

    private DwdSopReportTransformationDeviceDO transform(SopTransformResList item,
                                                         String version,
                                                         AtomicLong startId,
                                                         LocalDateTime startTime,
                                                         boolean isSource,
                                                         boolean isExecuted,
                                                         String codeVersion) {
        DwdSopReportTransformationDeviceDO ret = new DwdSopReportTransformationDeviceDO();

        // 设置代码版本
        ret.setCodeVersion(codeVersion);

        ret.setId(startId.getAndIncrement());
        ret.setVersion(version);
        ret.setStatTime(startTime);
        ret.setNum(ObjectUtils.defaultIfNull(isExecuted?item.getExeAmount():item.getNonAmount(),BigDecimal.ZERO));
        // isCa默认false
        ret.setIsCa(FlagType.NO.getCode());
        ret.setDeptName(item.getDeptName());
        ret.setSotType(isSource ? SotType.SOURCE.getName() : SotType.TARGET.getName());
        SopDateUtils.dateTransform(item, ret);
        ret.setResType(item.getResourceType());
        ret.setResPoolType(SopResourcePool.getDesc(item.getResourcePool()));
        ret.setObsProjectType(item.getProjectType());
        // 没有数据
//        ret.setBgName(item.getBgName());
        ret.setCustomBgName(item.getCustomBgName());
        ret.setDeptName(item.getDeptName());
        ret.setPlanProductName(item.getPlanProductName());
        ret.setCustomhouseTitle(item.getAreaType());
        ret.setCountryName(item.getCountry());
        ret.setCityName(item.getCity());
        ret.setCmdbCampusName(item.getCampus());
        ret.setCmdbModuleName(item.getModule());
        ret.setTxyZoneName(item.getZone());
        ret.setPhyDeviceFamily(item.getDeviceFamily());
        ret.setPhyDeviceType(item.getDeviceType());
        ret.setIsHedge(item.getIsHedging());
        ret.setIsExecuted(isExecuted ? FlagType.YES.getCode() : FlagType.NO.getCode());
        ret.setBusinessType(item.getBusinessType());
        ret.setObsBusinessType(item.getBusinessType());
        ret.setHasHedged(item.getIsValidHedging());
        ret.setJsonText(null);
        cleaning(ret);
        return ret;
    }
}
