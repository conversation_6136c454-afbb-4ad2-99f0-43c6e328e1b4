package cloud.demand.app.modules.sop.enums.codeVersion.version;


import cloud.demand.app.modules.sop.enums.codeVersion.SopCodeVersionEnum;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/** v2-sop的dwd层执行版本情况 */
@Getter
@AllArgsConstructor
public enum SopCodeVersionDwdEnum {
    // 目前涉及需要版本的上游接口有：
    // 1. getSopSourceTransformByVersion - 改造前目标机型（未执行-D10）
    // 2. getSopTargetTransformByVersion - 改造后目标机型（未执行-S13）
    // 3. getServerReturnByVersion - 物理机退回（未执行-S9）
    // 4. getServerDemandByVersion - 物理机需求（已执行参与对冲-D1,D2,D4,D5）

    // 对应dwd层：
    // 1. PhysicalDemandTask - 物理机需求
    // 2. PhysicalReturnTask - 物理机退回
    // 3. PhysicalTransformationTask - 改造

    /** 物理机需求 */
    PhysicalDemandTask(DwdTaskEnum.PHYSICAL_NEW_DEMAND,
            null,
            SopCodeVersionEnum.V1),

    /** 物理机退回 */
    PhysicalReturnTask(DwdTaskEnum.PHYSICAL_NEW_RETURN,
            SopCodeVersionEnum.V1,
            null),

    /** 改造 */
    PhysicalTransformationTask(DwdTaskEnum.PHYSICAL_NEW_TRANSFORMATION,
            SopCodeVersionEnum.V1,
            null),
    ;

    /** dwd任务枚举 */
    private final DwdTaskEnum dwdTaskEnum;

    /** 是否需要已执行数据,为null则全部版本都要根据版本查 */
    private final SopCodeVersionEnum needExec;

    /** 是否需求未执行数据,为null则全部版本都要根据版本查 */

    private final SopCodeVersionEnum needNonExec;


    /** 是否需要执行 */

    public static SopDoProcessType getDoProcessType(DwdTaskEnum dwdTaskEnum, String code, boolean isExec){
        for (SopCodeVersionDwdEnum value : values()) {
            if (value.dwdTaskEnum.equals(dwdTaskEnum)){
                SopCodeVersionEnum versionEnum = isExec?value.needExec:value.needNonExec;
                if (versionEnum == null){
                    return SopDoProcessType.doAll;
                }else {
                    if (StringUtils.equals(versionEnum.getCode(),code)){
                        return SopDoProcessType.doOne;
                    }else {
                        return SopDoProcessType.notDo;
                    }
                }
            }
        }
        // 默认执行全部
        return SopDoProcessType.doAll;
    }
}
