package cloud.demand.app.modules.sop.domain.http;

import cloud.demand.app.modules.sop.deserializer.SopLocalDateDeserializer;
import cloud.demand.app.modules.sop.deserializer.SopStringDeserializer;
import cloud.demand.app.modules.sop.entity.clean.IM12Amount;
import cloud.demand.app.modules.sop.entity.clean.OriIndexDate;
import cloud.demand.app.modules.sop.entity.clean.OriModZoneInfo;
import cloud.demand.app.modules.sop.entity.clean.OriProductInfo;
import cloud.demand.app.modules.sop.entity.clean.OriZoneCity;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import java.time.LocalDate;
import lombok.Data;

@Data
public class SopStockDetailRes implements OriProductInfo, OriModZoneInfo, OriZoneCity, IM12Amount , OriIndexDate {
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourceType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String resourcePool;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String projectType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String businessType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String customBgName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deptName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String planProductName;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String areaType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String country;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String city;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String campus;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String module;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceFamily;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String instanceModel;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String cvmType;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String deviceType;

    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate date;
    private Integer year;
    private Integer month;
    private Integer week;
    private Integer isHedging;
    private Integer isValidHedging;

    @JsonDeserialize(using = SopLocalDateDeserializer.class)
    private LocalDate dateVersion;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String class1;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String class2;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String class3;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String class4;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String assetCode;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String zone;
    @JsonDeserialize(using = SopStringDeserializer.class)
    private String productName;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m1Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m2Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m3Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m4Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m5Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m6Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m7Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m8Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m9Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m10Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m11Amount;

    @JsonDeserialize(using = SopStringDeserializer.class)
    private String m12Amount;
}
