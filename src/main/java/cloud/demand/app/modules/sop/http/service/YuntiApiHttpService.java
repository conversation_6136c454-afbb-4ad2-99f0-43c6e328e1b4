package cloud.demand.app.modules.sop.http.service;

import cloud.demand.app.modules.sop.domain.YuntiPageReq;
import cloud.demand.app.modules.sop.domain.YuntiVersionReq;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandHedgingDetailResList;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandVersionCodeRes;

import cs.easily.tp.annotation.TpApi;
import cs.easily.tp.annotation.TpBody;
import cs.easily.tp.annotation.TpClient;
import java.util.List;

@TpClient(baseUrl = "${report.yunti-gateway}", prefix = "yunti-demand/webapi/accuracy_intervention", desc = "云梯CVM需求接口", apikey = true, readTimeoutSeconds = 60)
public interface YuntiApiHttpService {
    @TpApi(path = "getVersionList", desc = "自研干预2.0版本列表接口")
    public YuntiCvmDemandVersionCodeRes getVersionList(@TpBody YuntiPageReq req);

    @TpApi(path = "getHedgingDetail", desc = "自研干预2.0下发对冲")
    public List<YuntiCvmDemandHedgingDetailResList> getHedgingDetail(@TpBody YuntiVersionReq req);
}
