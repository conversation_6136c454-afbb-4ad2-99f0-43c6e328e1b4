package cloud.demand.app.modules.sop.service.task.process;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.sop.entity.task.SopDwdTaskDO;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.SimpleAbstractSopProcess;
import com.pugwoo.dbhelper.DBHelper;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@Service
public class SopDwdProcess extends SimpleAbstractSopProcess<SopDwdTaskDO> {
    @Override
    protected DBHelper getDbHelper() {
        return DBList.demandDBHelper;
    }

    @Override
    public SopDwdTaskDO getReadyTask(ITaskEnum taskEnum) {
        // 就绪条件：
        // 1.状态为NEW或者ERROR
        // 2.没有dwd的前置任务
        // 3.dwd同版本没有未完成的
        return DBList.demandDBHelper.getRawOne(SopDwdTaskDO.class,
                "select * from sop_dwd_task dwd " +
                        "where dwd.name = ? and dwd.status in (?)" +
                        "and not exists (select 1 from sop_dwd_task temp where temp.dws_id < dwd.dws_id and temp.version = dwd.version and dwd.name=temp.name and temp.status in (?))" +
                        "and not exists (select 1 from sop_dws_task temp where temp.id < dwd.dws_id and temp.version = dwd.version and temp.status in (?))" +
                        "order by id asc",
                taskEnum.getName(),
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                TaskStatus.RUNNING.getName(),
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName(),TaskStatus.RUNNING.getName()));
    }
}
