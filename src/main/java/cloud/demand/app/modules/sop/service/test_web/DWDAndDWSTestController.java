package cloud.demand.app.modules.sop.service.test_web;

import cloud.demand.app.modules.sop.entity.dws.DwsSopReportMifDO;
import cloud.demand.app.modules.sop.entity.task.SopDwdTaskDO;
import cloud.demand.app.modules.sop.entity.task.SopDwsTaskDO;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.service.task.dwd_task.cvm.CvmDemandTask;
import cloud.demand.app.modules.sop.service.task.dwd_task.cvm.CvmReturnExtTask;
import cloud.demand.app.modules.sop.service.task.dwd_task.cvm.CvmReturnTask;
import cloud.demand.app.modules.sop.service.task.dwd_task.physical.*;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.Dwd2DwsUtil;
import cloud.demand.app.modules.sop.util.TaskUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.RedisHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

@JsonrpcController("/sop/test")
@Slf4j
public class DWDAndDWSTestController {

    @Resource
    private Map<String, DWDTaskService> beanMap;
    @Resource
    RedisHelper redisHelper;
    @Resource
    DBHelper ckcldStdCrpDBHelper;
    @Resource
    CommonDbHelper commonDbHelper;

    private Map<String, String> classMap;

    @PostConstruct
    public void init() {
        classMap = new HashMap<>();

        // cvm
        classMap.put(DwdTaskEnum.CVM_NEW_DEMAND.getName(), getBeanName(CvmDemandTask.class));
        classMap.put(DwdTaskEnum.CVM_NEW_RETURN.getName(), getBeanName(CvmReturnTask.class));
        classMap.put(DwdTaskEnum.CVM_NEW_RETURN_EXT.getName(), getBeanName(CvmReturnExtTask.class));
        // device
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_DEMAND.getName(), getBeanName(PhysicalDemandTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_RETURN.getName(), getBeanName(PhysicalReturnTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_RETURN_EXT.getName(), getBeanName(PhysicalReturnExtTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_PROCUREMENT.getName(), getBeanName(PhysicalProcurementTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_REPLACEMENT.getName(), getBeanName(PhysicalReplacementTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_TRANSFER.getName(), getBeanName(PhysicalTransferTask.class));
        classMap.put(DwdTaskEnum.PHYSICAL_NEW_TRANSFORMATION.getName(), getBeanName(PhysicalTransformationTask.class));
    }

    private String getBeanName(Class<?> clazz) {
        return StringUtils.uncapitalize(clazz.getSimpleName());
    }

    /**
     * 测试用
     */
    @RequestMapping
    public void testDWDAndDWS() {
        for (DwdTaskEnum dwdTask : DwdTaskEnum.values()) {
            SopDwdTaskDO sopDwdTaskDO = TaskUtil.getReadySopDwdTaskDO(dwdTask);
            if (sopDwdTaskDO != null) {
                String version = sopDwdTaskDO.getVersion();
                // 手动获取分布式锁，用于控制同一个数据版本不会被并发执行，只要锁到标记完 dwd 任务为运行中即可
                String namespace = "createDWDData@" + dwdTask;
                String lockUUID = redisHelper.requireLock(namespace,
                        version, 60, true);
                if (lockUUID == null) {
                    // 加锁失败，说明有其他节点在标记运行中，退出
                    continue;
                }
                log.info("SOP {}, version:{}", namespace, version);
                TaskUtil.start(sopDwdTaskDO);
                // 标记完运行中就可以释放了，继续后面的业务逻辑
                redisHelper.releaseLock(namespace, version, lockUUID, true);
                try {
                    // 这里执行具体的创建 DWD 数据版本逻辑
                    DWDTaskService service = beanMap.get(classMap.get(dwdTask.getName()));
                    service.createDWDData(version);
                } catch (Exception e) {
                    log.error("SOP " + namespace + ", version:" + version + " ,error:", e);
                    TaskUtil.error(sopDwdTaskDO, e);
                    throw e;
                }
                TaskUtil.finish(sopDwdTaskDO);
            }
        }

        SopDwsTaskDO sopDwsTaskDO = TaskUtil.getReadySopDwsTaskDO();
        if (sopDwsTaskDO != null) {
            TaskUtil.start(sopDwsTaskDO);
            String version = sopDwsTaskDO.getVersion();
            LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);
            log.info("SOP createDWSData, version:{}", version);
            try {
                CkDBUtils.delete(ckcldStdCrpDBHelper, version, DwsSopReportMifDO.class);
                Dwd2DwsUtil util = new Dwd2DwsUtil();
                util.execSqlWithoutPage(version, dateVersion, ckcldStdCrpDBHelper);
            } catch (Exception e) {
                log.error("SOP createDWSData, version:" + version + " ,error:", e);
                TaskUtil.error(sopDwsTaskDO, e);
                throw e;
            }
            TaskUtil.finish(sopDwsTaskDO);
        }
    }

    @RequestMapping
    public void testADS() {
//        adsTaskService.scheduleCreateADSData();
    }

}
