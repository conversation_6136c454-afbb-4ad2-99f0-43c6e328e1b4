package cloud.demand.app.modules.sop.domain.http;


import cloud.demand.app.modules.sop.domain.IReturn;
import java.util.List;
import lombok.Data;

@Data
public class SopPageRes<T> implements IReturn<List<T>> {
    private Long total;
    private List<T> data;

    @Override
    public boolean isOk() {
        return true;
    }

    @Override
    public List<T> getBody() {
        return data;
    }

    @Override
    public String getError() {
        return null;
    }
}
