package cloud.demand.app.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/** sop 上游资源池类型 */
@Getter
@AllArgsConstructor
public enum SopResourcePool {
    CLOUD("1","公有池"),
    SELF("0","自研池"),
    ;

    private final String code;
    private final String desc;

    public static String getDesc(String code){
        if (StringUtils.isBlank(code)){
            return code;
        }
        for (SopResourcePool value : values()) {
            if (value.getCode().equals(code)){
                return value.getDesc();
            }
        }
        return code;
    }
}
