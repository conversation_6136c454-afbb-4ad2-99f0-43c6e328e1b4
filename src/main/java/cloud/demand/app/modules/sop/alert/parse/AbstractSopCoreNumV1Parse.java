package cloud.demand.app.modules.sop.alert.parse;

import cloud.demand.app.modules.mrpv2.alert.entity.AbstractAlter;
import cloud.demand.app.modules.mrpv2.alert.parse.AbstractAlterParse;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterV1V2Do;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumDiffAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumV1V2DiffAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import cloud.demand.app.modules.sop.enums.ExecutedType;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import org.apache.commons.collections4.map.MultiKeyMap;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class AbstractSopCoreNumV1Parse extends AbstractAlterParse<List<SopCoreNumDiffAlterDo>> {
    private final DBHelper dbHelper;
    protected SopQueryParam queryParam;

    public AbstractSopCoreNumV1Parse(DBHelper dbHelper,
                                     SopQueryParam queryParam) {
        this.dbHelper = dbHelper;
        this.queryParam = queryParam;
    }


    @Override
    protected DBHelper getDbHelper() {
        return dbHelper;
    }

    abstract String getDimType();
    @Override
    public List<? extends AbstractAlter> preParse(List<? extends AbstractAlter> raw) {
        List<SopCoreNumV1V2DiffAlterDo> ret = new ArrayList<>();
        MultiKeyMap<String, SopCoreNumAlterV1V2Do> mapV1 = new MultiKeyMap<>();
        MultiKeyMap<String, SopCoreNumAlterV1V2Do> mapV2 = new MultiKeyMap<>();

        List<SopCoreNumAlterV1V2Do> crpRaw = (List<SopCoreNumAlterV1V2Do>) raw;
        // 同一行的数据通过列的值区分。例如一条数据通过code_version的值区分是哪个版本
        // 区别于AbstractSopCoreNumParse，不同行的数据

        for (SopCoreNumAlterV1V2Do alter : crpRaw) {


            if ("1.0".equals(alter.getCodeVersion())) {
                mapV1.put(alter.getBusinessType(), String.valueOf(alter.getIsExecuted()), alter);
            } else if ("2.0".equals(alter.getCodeVersion())) {
                mapV2.put(alter.getBusinessType(), String.valueOf(alter.getIsExecuted()), alter);
            } else {
                // 未带版本标识的老数据不做处理
            }
//            // 自研业务转内部业务 先不管了
//            if (item.getBusinessType().equals(SopBusinessType.SELF.getDesc())){
//                map.put("内部业务",item.getCodeVersion(),item);
//            }
        }
        mapV1.forEach((key, v1Item) -> {
            // 处理每个键值对

            SopCoreNumAlterV1V2Do v2Item = mapV2.get(v1Item.getBusinessType(), String.valueOf(v1Item.getIsExecuted()));

            SopCoreNumV1V2DiffAlterDo item = transFormDiff(v1Item, v2Item);
            ret.add(item);
        });
        return ret;
    }

    public SopCoreNumV1V2DiffAlterDo transFormDiff(SopCoreNumAlterV1V2Do v1Item,
                                                   SopCoreNumAlterV1V2Do v2Item) {
        SopCoreNumV1V2DiffAlterDo ret = new SopCoreNumV1V2DiffAlterDo();
        ret.setVersion(queryParam.getVersion());
        ret.setVersionDate(queryParam.getVersionDate());
        ret.setBusinessType(v1Item.getBusinessType());
        ret.setDimType(getDimType());
        if (ret.getBusinessType().equals("内部业务")) {
            ret.setBusinessType(SopBusinessType.SELF.getDesc());
        }
        ret.setV1CoreNum(v1Item.getCoreNum());
        ret.setV2CoreNum(v2Item.getCoreNum());
        BigDecimal diffCoreNum = v2Item.getCoreNum().subtract(v1Item.getCoreNum());
        ret.setDiffCoreNum(diffCoreNum);
        if (v1Item.getCoreNum().compareTo(v2Item.getCoreNum())==0) {
            ret.setIsOk(true);
        }else {
            ret.setIsOk(false);
            ret.setErrors(Lists.newArrayList("差异值:"+diffCoreNum));
        }


        ret.setIsExecuted(ExecutedType.getNameByLocalCode(v1Item.getIsExecuted()));

        return ret;
    }

    @Override
    public List<? extends AbstractAlter> getRaw() {
        return getDbHelper().getRaw(getAlterClass(), getSql(), queryParam.getVersion(), queryParam.getStartDate());
    }

    @Override
    protected Class<? extends AbstractAlter> getAlterClass() {
        return SopCoreNumAlterV1V2Do.class;
    }

}
