package cloud.demand.app.modules.sop.alert.raw;

import cloud.demand.app.modules.mrpv2.alert.parse.IAlterRaw;
import cloud.demand.app.modules.sop.alert.entity.SopCoreNumAlterDo;
import cloud.demand.app.modules.sop.alert.entity.SopQueryParam;
import com.pugwoo.dbhelper.DBHelper;
import java.util.List;

public abstract class AbstractSourceRaw<T> implements IAlterRaw<T> {

    protected final SopQueryParam param;


    public AbstractSourceRaw(SopQueryParam param) {
        this.param = param;
    }

    @Override
    public List<T> getRaw() {
        return getDbHelper().getRaw(getDbClass(),getSql(),param.getVersionDate(),param.getStartDate());
    }

    public List<SopCoreNumAlterDo> getAlterRaw(){
        return transForm(getRaw());
    }

    public abstract List<SopCoreNumAlterDo> transForm(List<T> list);

    public abstract DBHelper getDbHelper();

    public abstract String getSql();

    public abstract Class<T> getDbClass();
}
