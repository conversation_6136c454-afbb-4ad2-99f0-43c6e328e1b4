package cloud.demand.app.modules.sop.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum TaskStatus {

    NEW("NEW","初始化",0),
    RUNNING("RUNNING","运行中",2),
    FINISH("FINISH","已完成",1),
    ERROR("ERROR","异常",3),
    MANUAL_STOP("MANUAL_STOP","永久终止",4); // 如果人工修改 DB，不想任务执行的话，写这个

    final private String name;

    final private String desc;

    final private Integer order;

    public static TaskStatus getByName(String name){
        for (TaskStatus value : values()) {
            if (Objects.equals(name,value.getName())){
                return value;
            }
        }
        throw new IllegalArgumentException("未找到对应的枚举值");
    }

    /** 通过名称获取描述 */
    public static String getDescByName(String name){
        for (TaskStatus value : values()) {
            if (Objects.equals(name,value.getName())){
                return value.getDesc();
            }
        }
        return name;
    }

}
