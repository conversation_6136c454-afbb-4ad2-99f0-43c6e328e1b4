package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.model.PlanAccuracyRankingListTableResponse;
import cloud.demand.app.modules.industry_cockpit.service.PlanAccuracyService;
import cloud.demand.app.modules.industry_cockpit.service.RankListingService;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateResp;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/4/26
 */
@Service
public class RankListingServicePredictionAccuracyImpl implements RankListingService {

    @Autowired
    @Qualifier("planAccuracyServicePredictionAccuracyImpl")
    private PlanAccuracyService<ForecastMatchRateResp.Item> predictionAccuracyServiceImpl;

    @Override
    public List<PlanAccuracyRankingListTableResponse.Item> getRankListingData(IndustryCockpoitRequest request, String demandType, String industryType) {

        // 预测准确率 从接口获取
        if (IndustryCockpitConstant.ALL_INDUSTRY.equals(industryType)) {
            // 维度（自带年月，不用加年月维度）：可选枚举：regionName - 地域, industryDept - 行业部门, instanceType - 实例类型, customerShortName - 客户简称
            request.setDims(Arrays.asList("industryDept"));
        }
        if (IndustryCockpitConstant.CUSTOMER.equals(industryType)) {
            if(request.isCustomerDetail()){
                // 维度（自带年月，不用加年月维度）：可选枚举：regionName - 地域, industryDept - 行业部门, instanceType - 实例类型, customerShortName - 客户简称
                request.setDims(Arrays.asList("industryDept","warZoneName", "customerShortName","regionName","instanceType"));
            }else {
                // 维度（自带年月，不用加年月维度）：可选枚举：regionName - 地域, industryDept - 行业部门, instanceType - 实例类型, customerShortName - 客户简称
                request.setDims(Arrays.asList("industryDept","warZoneName", "customerShortName"));
            }

        }
        // 获取两个月的预测准确率数据
        List<ForecastMatchRateResp.Item> predictionAccuracyData = predictionAccuracyServiceImpl.getAccuracyData(request, demandType);
        // 准确率数据
        List<PlanAccuracyRankingListTableResponse.Item> resultDatas = predictionAccuracyData.stream().map(item -> {
            PlanAccuracyRankingListTableResponse.Item dataItem = new PlanAccuracyRankingListTableResponse.Item();
            dataItem.setIndustryDept(item.getIndustryDept());
            dataItem.setWarZone(item.getWarZoneName());
            if (industryType.equals(IndustryCockpitConstant.CUSTOMER)) {
                if (request.isCustomerDetail()) {
                    dataItem.setCustomerShortName(item.getCustomerShortName());
                    dataItem.setRegionName(item.getRegionName());
                    dataItem.setInstanceType(item.getInstanceType());
                }else {
                    dataItem.setCustomerShortName(item.getCustomerShortName());
                }
            }
            if (IndustryCockpitConstant.CORE_DAYS.equals(request.getAccuracyCaliber())) {
                // 核天
                dataItem.setPredictionAccuracy(item.getCoreByDayMatchRate());
                if (null != item.getCoreByDayBillChangeNum()) {
                    dataItem.setBillVariationMonthlyAvg(item.getCoreByDayBillChangeNum());
                } else {
                    dataItem.setBillVariationMonthlyAvg(BigDecimal.ZERO);
                }
            } else if (IndustryCockpitConstant.MONTHLY_AVG.equals(request.getAccuracyCaliber())) {
                // 月均
                dataItem.setPredictionAccuracy(item.getMonthlyMatchRate());
                if (null != item.getMonthlyBillChangeNum()) {
                    dataItem.setBillVariationMonthlyAvg(item.getMonthlyBillChangeNum());
                } else {
                    dataItem.setBillVariationMonthlyAvg(BigDecimal.ZERO);
                }
            }
            if (null != item.getForecastNum()) {
                dataItem.setPredictedQuantity(item.getForecastNum());
            } else {
                dataItem.setPredictedQuantity(BigDecimal.ZERO);
            }
            dataItem.setBillVariationMonthlyAvgSlice(BigDecimal.ZERO);
            dataItem.setW6ForecastNum(ObjectUtils.defaultIfNull(item.getW6ForecastNum(),BigDecimal.ZERO));
            dataItem.setW13ForecastNum(ObjectUtils.defaultIfNull(item.getW13ForecastNum(),BigDecimal.ZERO));
            return dataItem;
        }).collect(Collectors.toList());
        return resultDatas;
    }
}
