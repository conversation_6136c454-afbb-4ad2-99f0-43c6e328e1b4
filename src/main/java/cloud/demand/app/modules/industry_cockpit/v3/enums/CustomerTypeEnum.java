package cloud.demand.app.modules.industry_cockpit.v3.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/12/20 14:20
 */
@Getter
@AllArgsConstructor
public enum CustomerTypeEnum {
    //客户类型 0个人 1 企业  2 政府 3 组织 99 未知
    PERSONAL(0, "个人"),
    ENTERPRISE(1, "企业"),

    GOVERNMENT(2, "政府"),

    ORGANIZATION(3, "组织"),

    UNKNOWN(99, "未知"),
    ;

    private int code;

    private String name;
}
