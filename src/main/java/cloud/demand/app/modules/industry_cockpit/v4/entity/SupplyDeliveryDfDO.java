package cloud.demand.app.modules.industry_cockpit.v4.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;

@Data
@ToString
@Table("dws_supply_delivery_df")
public class SupplyDeliveryDfDO {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 指标类型：订单(共识需求)、供应(大盘满足)、供应(采购满足)、供应(搬迁满足)<br/>Column: [index] */
    @Column(value = "index")
    private String index;

    /** 产品：CVM、GPU、裸金属<br/>Column: [product] */
    @Column(value = "order_category")
    private String orderCategory;

    @Column(value = "product")
    private String product;

    /** 应用角色：CVM、EMR、EKS<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

    /** 时间类型:index=订单(共识需求)|供应(大盘满足),time_type=BEGIN_BUY_DATE|END_BUY_DATE;
  index=供应(采购满足)|供应(搬迁满足),time_type=SUPPLY_TIME<br/>Column: [time_type] */
    @Column(value = "time_type")
    private String timeType;

    /** 订单维度：ORDER_NUMBER-订单号 ADVANCE_WEEK-提前期 */
    @Column(value = "order_dim")
    private String orderDim;

    /** 购买或供应日期<br/>Column: [buy_or_supply_date] */
    @Column(value = "buy_or_supply_date")
    private String buyOrSupplyDate;

    /** 购买或供应年月<br/>Column: [year_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 购买或供应周:W1、W2、W3、W4<br/>Column: [month_week] */
    @Column(value = "month_week")
    private String monthWeek;

    /** 行业<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 通用客户简称<br/>Column: [un_customer_short_name] */
    @Column(value = "un_customer_short_name")
    private String unCustomerShortName;

    /** 境内/境外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @Column(value = "country_name")
    private String countryName;

    /** 地域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 可用区名称<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 机型族<br/>Column: [instance_group] */
    @Column(value = "instance_group")
    private String instanceGroup;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;

    /** 订单状态:CANCELED-取消、PROCESS-流程中、FINISHED-关闭、DRAFT-草稿<br/>Column: [order_status] */
    @Column(value = "order_status")
    private String orderStatus;

    @Column(value = "order_node_code")
    private String orderNodeCode;

    /** 所属订单号<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 所属订单明细id,默认(空值)<br/>Column: [order_number_id] */
    @Column(value = "order_number_id")
    private String orderNumberId;

    /** 单据号，默认'',index=订单(共识需求)表示订单号,index=供应(大盘满足)为(空值),
  index=供应(采购满足)为采购单号，index=供应(搬迁满足)为搬迁单号<br/>Column: [bill_number] */
    @Column(value = "bill_number")
    private String billNumber;

    /** 订单提前期，index=订单(共识需求)有值,默认''<br/>Column: [advance_week] */
    @Column(value = "advance_week")
    private Integer advanceWeek;

    /** 园区类型:主力园区、非主力园区<br/>Column: [main_zone_type] */
    @Column(value = "main_zone_type")
    private String mainZoneType;

    /** 新旧机型枚举：新机型，旧机型<br/>Column: [generation_instance_type] */
    @Column(value = "generation_instance_type")
    private String generationInstanceType;

    /** 主力机型枚举：主力机型，非主力机型<br/>Column: [main_instance_type] */
    @Column(value = "main_instance_type")
    private String mainInstanceType;

    /** cpu核数，单位：核<br/>Column: [cpu_count] */
    @Column(value = "cpu_count")
    private Integer cpuCount;

    /** gpu卡数,卡<br/>Column: [gpu_count] */
    @Column(value = "gpu_count")
    private Integer gpuCount;


}