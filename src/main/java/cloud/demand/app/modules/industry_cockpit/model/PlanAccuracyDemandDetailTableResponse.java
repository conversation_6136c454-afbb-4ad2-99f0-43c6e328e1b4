package cloud.demand.app.modules.industry_cockpit.model;

import cloud.demand.app.modules.industry_cockpit.entity.DemandRankingDetailVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 计划准确率 需求明细
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
@Data
public class PlanAccuracyDemandDetailTableResponse {

    private List<Item> data;

    @Data
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    public static class Item implements IPlanAccuracyGroupKey {
        private String category; // 分类

        private int categorySort; // 分类排序
        private String industryDept; // 行业
        private String warZone; // 战区
        private String customerShortName; // 客户
        private String instanceType; // 请求参数mergeInstanceType=是，返回合并实例类型，mergeInstanceType=否，返回单个实例类型

        private String instanceTypes; // 请求参数mergeInstanceType=是，返回单个实例类型集合，mergeInstanceType=否，返回null
        private String regionName; // 地域

        private BigDecimal w6ForecastNum; // 提前6周预测
        private BigDecimal w13ForecastNum; // 提前13周预测
        private BigDecimal predictedQuantityWeighted; // 预测量 532加权版本
        private BigDecimal billVariation; // 月均计费变化量 月均或者核天 看条件决定

        private BigDecimal billVariationMonthlyAvgSlice;// 月切计费变化量
        private BigDecimal predictionAccuracyWeighted; // 预测准确率 532加权版本

        private BigDecimal orderField; // 排序字段
    }

    public static Item generateItem(DemandRankingDetailVO demandRankingDetailVO) {
        Item item = new Item();
        item.setCategory(demandRankingDetailVO.getCategory());
        item.setIndustryDept(demandRankingDetailVO.getIndustryDept());
        item.setWarZone(demandRankingDetailVO.getWarZoneName());
        item.setCustomerShortName(demandRankingDetailVO.getCustomerShortName());
        item.setInstanceType(demandRankingDetailVO.getInstanceType());
        item.setRegionName(demandRankingDetailVO.getRegionName());
        item.setW6ForecastNum(demandRankingDetailVO.getW6ForecastNum());
        item.setW13ForecastNum(demandRankingDetailVO.getW13ForecastNum());
        item.setPredictedQuantityWeighted(demandRankingDetailVO.getV2AvgForecastNum());
        item.setBillVariation(demandRankingDetailVO.getChangeBillNum());
        item.setPredictionAccuracyWeighted(demandRankingDetailVO.getForecastMatchRate());
        return item;
    }

    public static Item generateItem(PlanAccuracyRankingListTableResponse.Item sourceItem,BigDecimal targetPredictionAccuracy) {
        sourceItem.initNullField();

        Item item = new Item();

        item.setIndustryDept(sourceItem.getIndustryDept());
        item.setWarZone(sourceItem.getWarZone());
        item.setCustomerShortName(sourceItem.getCustomerShortName());
        item.setInstanceType(sourceItem.getInstanceType());
        item.setRegionName(sourceItem.getRegionName());
        item.setW6ForecastNum(sourceItem.getW6ForecastNum());
        item.setW13ForecastNum(sourceItem.getW13ForecastNum());
        item.setPredictedQuantityWeighted(sourceItem.getPredictedQuantity());
        item.setBillVariation(sourceItem.getBillVariationMonthlyAvg());
        item.setPredictionAccuracyWeighted(sourceItem.getPredictionAccuracy());

        targetPredictionAccuracy = targetPredictionAccuracy.abs();
        if(sourceItem.getPredictedQuantity().abs().compareTo(BigDecimal.ZERO) > 0 && sourceItem.getPredictionAccuracy().compareTo(targetPredictionAccuracy) >= 0){
            item.setCategory("预测较优");
            item.setCategorySort(3);
        }else if((sourceItem.getPredictedQuantity().abs().compareTo(BigDecimal.ZERO) > 0 || sourceItem.getBillVariationMonthlyAvg().abs().compareTo(BigDecimal.ZERO) > 0)
                && sourceItem.getPredictionAccuracy().compareTo(targetPredictionAccuracy) < 0
                && sourceItem.getPredictedQuantity().abs().compareTo(sourceItem.getBillVariationMonthlyAvg().abs()) < 0){
            item.setCategory("预测不足");
            item.setCategorySort(2);
        }else if((sourceItem.getPredictedQuantity().abs().compareTo(BigDecimal.ZERO) > 0 || sourceItem.getBillVariationMonthlyAvg().compareTo(targetPredictionAccuracy) > 0)
                && sourceItem.getPredictionAccuracy().abs().compareTo(targetPredictionAccuracy) < 0
                && (sourceItem.getPredictedQuantity().abs().compareTo(sourceItem.getBillVariationMonthlyAvg().abs()) > 0 || sourceItem.getW6ForecastNum().abs().compareTo(sourceItem.getBillVariationMonthlyAvgSlice().abs()) > 0)){
            item.setCategory("执行不足");
            item.setCategorySort(1);
        }else {
            item.setCategory("未知类型");
            item.setCategorySort(0);
        }

        if(item.getCategory().equals("预测较优")){
            item.setOrderField(item.getBillVariation().abs().multiply(item.getPredictionAccuracyWeighted()));
        }else if(item.getCategory().equals("预测不足")){
            item.setOrderField(item.getBillVariation().abs().subtract(item.getPredictedQuantityWeighted()));
        }else if(item.getCategory().equals("执行不足")){
            item.setOrderField(item.getW6ForecastNum().abs().subtract(item.getBillVariation()));
        }
        return item;
    }

}
