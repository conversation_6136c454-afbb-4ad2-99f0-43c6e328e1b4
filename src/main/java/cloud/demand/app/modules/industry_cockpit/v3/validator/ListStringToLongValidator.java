package cloud.demand.app.modules.industry_cockpit.v3.validator;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.List;

// 自定义校验器
public class ListStringToLongValidator implements ConstraintValidator<ValidListStringToLong, List<String>> {
    @Override
    public boolean isValid(List<String> value, ConstraintValidatorContext context) {
        if (value == null) {
            return true; // 可以根据需求决定是否允许 null
        }
        for (String str : value) {
            try {
                Long.parseLong(str);
            } catch (NumberFormatException e) {
                return false;
            }
        }
        return true;
    }
}