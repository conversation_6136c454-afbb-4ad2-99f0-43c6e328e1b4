package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.modules.order.dto.OrderTagResultItemDto;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.pugwoo.wooutils.collect.ListUtils;

import java.util.List;

/**
 * <AUTHOR>
 */

public class OrderTagConverter implements Converter<List> {
    @Override
    public Class supportJavaTypeKey() {
        return String.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public List convertToJavaData(CellData cellData,
                                       ExcelContentProperty excelContentProperty,
                                       GlobalConfiguration globalConfiguration) throws Exception {
        return ListUtils.newArrayList();
    }

    @Override
    public CellData convertToExcelData(List value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        StringBuilder sb = new StringBuilder();
        value.forEach(item -> {
            OrderTagResultItemDto dto = (OrderTagResultItemDto) item;
            sb.append(dto.getChineseName()).append(":").append(dto.getCore()).append(";");
        });
        return new CellData<>(sb.toString());
    }

}
