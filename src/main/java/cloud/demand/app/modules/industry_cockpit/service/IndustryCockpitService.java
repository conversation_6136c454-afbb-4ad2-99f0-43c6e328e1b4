package cloud.demand.app.modules.industry_cockpit.service;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.ChartDataItemVO;
import cloud.demand.app.modules.industry_cockpit.entity.DemandTrendVO;
import cloud.demand.app.modules.industry_cockpit.entity.ScaleChangeVO;
import cloud.demand.app.modules.industry_cockpit.entity.ServiceLevelChartVO;
import cloud.demand.app.modules.industry_cockpit.entity.TargetVO;
import cloud.demand.app.modules.industry_cockpit.model.DemandTrendsTableResponse;
import cloud.demand.app.modules.industry_cockpit.model.IPlanAccuracyGroupKey;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.model.NetGrowthForecastChartResponse;
import cloud.demand.app.modules.industry_cockpit.model.PlanAccuracyChartResponse;
import cloud.demand.app.modules.industry_cockpit.model.PlanAccuracyDemandDetailTableResponse;
import cloud.demand.app.modules.industry_cockpit.model.PlanAccuracyRankingListTableResponse;
import cloud.demand.app.modules.industry_cockpit.model.ServiceLevelChartResponse;
import cloud.demand.app.modules.industry_cockpit.model.ServiceLevelRankingListTableResponse;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.domain.ForecastMatchRateResp;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.order.dto.resp.PerformanceSummaryResp;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 行业驾驶舱服务实现
 *
 * <AUTHOR>
 * @since 2024/4/22
 */
@Service
@Slf4j
public class IndustryCockpitService {

    @Resource
    private DBHelper demandDBHelper;
    @Autowired
    @Qualifier("serviceLevelServiceImpl")
    private ServiceLevelService serviceLevelService;

    @Autowired
    private PerformanceRateService performanceRateService;

    @Autowired
    @Qualifier("planAccuracyServicePredictionAccuracyImpl")
    private PlanAccuracyService<ForecastMatchRateResp.Item> predictionAccuracyServiceImpl;

    @Autowired
    protected PermissionService permissionService;

    @Autowired
    @Qualifier("growthForecastServiceAchieveImpl")
    private GrowthForecastService<ScaleChangeVO> growthForecastServiceAchieveImpl;
    @Autowired
    @Qualifier("growthForecastServiceUnAchieveImpl")
    private GrowthForecastService<ScaleChangeVO> growthForecastServiceUnAchieveImpl;

    @Autowired
    @Qualifier("inventoryBillScaleServiceDailyImpl")
    private InventoryBillScaleService<ChartDataItemVO> inventoryBillScaleServiceDailyImpl;
    @Autowired
    @Qualifier("inventoryBillScaleServiceMonthlyImpl")
    private InventoryBillScaleService<ChartDataItemVO> inventoryBillScaleServiceMonthlyImpl;

    @Autowired
    @Qualifier("rankListingServicePredictionAccuracyImpl")
    private RankListingService rankListingServicePredictionAccuracyImpl;
    @Autowired
    @Qualifier("rankListingServicePredictionChangeImpl")
    private RankListingService rankListingServicePredictionChangeImpl;

    @Autowired
    @Qualifier("rankingDetailServiceBaseImpl")
    private RankingDetailService rankingDetailServiceBaseImpl;

    @Autowired
    @Qualifier("planAccuracyServiceTargetImpl")
    private PlanAccuracyService planAccuracyServiceTargetImpl;

    @Autowired
    @Qualifier("demandTrendsTableServiceImpl")
    private DemandTrendsTableService demandTrendsTableServiceImpl;
    @Autowired
    @Qualifier("demandTrendsLastYearTableServiceImpl")
    private DemandTrendsTableService demandTrendsLastYearTableServiceImpl;

    /**
     * 获取服务水平
     *
     * @param requestBody 查询条件
     * @return 服务水平图表数据
     */
    public ServiceLevelChartResponse getServiceLevel(IndustryCockpoitRequest requestBody) {
        List<ServiceLevelChartVO> serviceLevelChart = this.serviceLevelService.getServiceLevelChartFromOrder(requestBody);
        return ServiceLevelChartResponse.build(serviceLevelChart);
    }

    /**
     * 获取服履约率
     *
     * @param requestBody 查询条件
     * @return PerformanceSummaryResp
     */
    public PerformanceSummaryResp queryPerformanceSummary(IndustryCockpoitRequest requestBody) {
        return performanceRateService.queryPerformanceSummary(requestBody);
    }

    /**
     * 获取计划准确率
     *
     * @param request    查询条件
     * @param demandType 需求类型：新增&弹性 退回
     * @return 计划准确率图表数据
     */
    public PlanAccuracyChartResponse getPlanAccuracy(IndustryCockpoitRequest request, String demandType) {
        int demandYear = StringUtils.isEmpty(request.getDemandYear()) ? LocalDate.now().getYear() : Integer.parseInt(request.getDemandYear());
        if(demandYear < LocalDate.now().getYear()){
            demandYear = LocalDate.now().getYear();
            request.setDemandYear(String.valueOf(demandYear));
        }
        PlanAccuracyChartResponse result = new PlanAccuracyChartResponse();
        // 准确率口径：核天 月均
        String accuracyCaliber = request.getAccuracyCaliber();
        String originMonthType = request.getMonthType();
        String currentMonth = "";
        String previousMonth = "";

        if(demandYear == LocalDate.now().getYear()){
            if (originMonthType.equals(IndustryCockpitConstant.PREVIOUS_MONTH)) {
                // 上月
                currentMonth = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
                previousMonth = LocalDate.now().minusMonths(2).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            } else if (originMonthType.equals(IndustryCockpitConstant.CURRENT_MONTH)) {
                // 本月
                currentMonth = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM"));
                previousMonth = LocalDate.now().minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            }
        }else if (demandYear < LocalDate.now().getYear()){
            if (originMonthType.equals(IndustryCockpitConstant.PREVIOUS_MONTH)) {
                // 上月
                currentMonth = LocalDate.of(demandYear, 11, 1).format(DateTimeFormatter.ofPattern("yyyy-MM"));;
                previousMonth = LocalDate.of(demandYear, 10, 1).format(DateTimeFormatter.ofPattern("yyyy-MM"));;
            } else if (originMonthType.equals(IndustryCockpitConstant.CURRENT_MONTH)) {
                // 本月
                currentMonth = LocalDate.of(demandYear, 12, 1).format(DateTimeFormatter.ofPattern("yyyy-MM"));;
                previousMonth = LocalDate.of(demandYear, 11, 1).format(DateTimeFormatter.ofPattern("yyyy-MM"));;
            }
        }else{
            throw new BizException("当前年份不支持查询");
        }

        // 用于lambada表达式的变量需要用final
        final String finalCurrentMonth = currentMonth;
        final String finalPreviousMonth = previousMonth;

        // 调用接口获取预测准确率数据 直接获取24个点的数据 然后从这个24个点中获取两个点出来计算环比
        request.setMonthType(IndustryCockpitConstant.EARLY_LAST_YEAR);
        List<ForecastMatchRateResp.Item> predictionAccuracyData = predictionAccuracyServiceImpl.getAccuracyData(request, demandType);

        if (!CollectionUtils.isEmpty(predictionAccuracyData)) {
            List<ForecastMatchRateResp.Item> startItemList = predictionAccuracyData.stream().filter(item -> item.getYearMonth().equals(finalPreviousMonth)).collect(Collectors.toList());
            ForecastMatchRateResp.Item startItem = ListUtils.isNotEmpty(startItemList) ? startItemList.get(0) : new ForecastMatchRateResp.Item();

            List<ForecastMatchRateResp.Item> endItemList = predictionAccuracyData.stream().filter(item -> item.getYearMonth().equals(finalCurrentMonth)).collect(Collectors.toList());
            ForecastMatchRateResp.Item endItem = ListUtils.isNotEmpty(endItemList) ? endItemList.get(0) : new ForecastMatchRateResp.Item();
            // 计算环比
            result.calculateChainRatio(Arrays.asList(startItem, endItem), accuracyCaliber, demandType);
            // 设置图表数据
            result.setChartData(predictionAccuracyData, accuracyCaliber, "预测准确率");
            result.setChartData(predictionAccuracyData, accuracyCaliber, "532新版预测量");
            result.setChartData(predictionAccuracyData, accuracyCaliber, StringUtils.equals(request.getQueryRange(),"计费用量") ? "行业计费变化量" : "行业服务变化量");
        }

        List<TargetVO> accuracyData = planAccuracyServiceTargetImpl.getAccuracyData(request, demandType);
        List<ChartDataItemVO> targetChartData = accuracyData.stream().map(item -> {
            ChartDataItemVO chartDataItemVO = new ChartDataItemVO();
            chartDataItemVO.setDataName(LocalDate.parse(item.getYearMonth() + "-01", DateTimeFormatter.ofPattern("yyyy-MM-dd")).format(DateTimeFormatter.ofPattern("yyyyMM")));
            chartDataItemVO.setDataValue(item.getTargetAccuracyRate());
            return chartDataItemVO;
        }).collect(Collectors.toList());

        result.setChartData(targetChartData, "目标");

        return result;
    }

    /**
     * 获取全年净增规模预测
     *
     * @param request 查询条件
     * @return 净增规模预测图表数据
     */
    public NetGrowthForecastChartResponse getNetGrowthForecast(IndustryCockpoitRequest request) {
        NetGrowthForecastChartResponse result = new NetGrowthForecastChartResponse();
        int demandYear = StringUtils.isEmpty(request.getDemandYear()) ? LocalDate.now().getYear() : Integer.parseInt(request.getDemandYear());
        // 获取需求年份上年的实际规模变化量
        List<ScaleChangeVO> lastYearChangeData = ListUtils.newArrayList();
        List<ScaleChangeVO> demandYearChangeData = ListUtils.newArrayList();
        List<ScaleChangeVO> demandYearPredictionDataFromNowOn = ListUtils.newArrayList();
        List<ChartDataItemVO> dailyInventoryBillingScaleData = ListUtils.newArrayList();
        // 每月增量预测
        List<ChartDataItemVO> monthlyPredictionNetIncreaseData = ListUtils.newArrayList();
        if(demandYear == LocalDate.now().getYear()){
            // 需求年=当前年
            // 获取上年的实际规模变化量
            String lastYearStartDay = LocalDate.now().minusYears(1).with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String lastYearEndDay = LocalDate.now().minusYears(1).with(TemporalAdjusters.lastDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            lastYearChangeData = growthForecastServiceAchieveImpl.getGrowthData(request,lastYearStartDay,lastYearEndDay);
            // 获取今年的实际达成规模变化量
            String currentYearStartDay = LocalDate.now().with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String lastMonthEndDay = LocalDate.now().minusMonths(1).with(TemporalAdjusters.lastDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            demandYearChangeData = growthForecastServiceAchieveImpl.getGrowthData(request,currentYearStartDay,lastMonthEndDay);

            // 今年从当天给开始的预测增量
            String currentMonthStartDay = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String currentYearEndDay = LocalDate.now().with(TemporalAdjusters.lastDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            demandYearPredictionDataFromNowOn = growthForecastServiceUnAchieveImpl.getGrowthData(request,currentMonthStartDay,currentYearEndDay);

            //历史规模不含结束时间
            dailyInventoryBillingScaleData = inventoryBillScaleServiceDailyImpl.getInventoryBillingScaleData(request,lastYearStartDay,currentMonthStartDay);

            // 每月增量预测
            monthlyPredictionNetIncreaseData = inventoryBillScaleServiceMonthlyImpl.getInventoryBillingScaleData(request,currentMonthStartDay,currentYearEndDay);

        }else if(demandYear < LocalDate.now().getYear()){
            // 需求年<当前年
            LocalDate demandYearStartDate = LocalDate.of(demandYear, 1, 1);
            // 获取上年的实际规模变化量
            String lastYearStartDay = demandYearStartDate.minusYears(1).with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String lastYearEndDay = demandYearStartDate.minusYears(1).with(TemporalAdjusters.lastDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            lastYearChangeData = growthForecastServiceAchieveImpl.getGrowthData(request,lastYearStartDay,lastYearEndDay);
            // 获取当年的实际达成规模变化量
            String currentYearStartDay = demandYearStartDate.with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            String lastMonthEndDay = demandYearStartDate.with(TemporalAdjusters.lastDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            demandYearChangeData = growthForecastServiceAchieveImpl.getGrowthData(request,currentYearStartDay,lastMonthEndDay);

            //历史规模不含结束时间
            String nextYearStartDay = demandYearStartDate.plusYears(1).with(TemporalAdjusters.firstDayOfYear()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dailyInventoryBillingScaleData = inventoryBillScaleServiceDailyImpl.getInventoryBillingScaleData(request,lastYearStartDay,nextYearStartDay);
        }else {
            // 需求年>当前年
            throw new BizException("需求年份不合法");
        }

        // 计算环比数据
        result.calculateYearOnYearRatio(lastYearChangeData, demandYearChangeData, demandYearPredictionDataFromNowOn);
        // 解析图表的数据
        result.parseChartItemData(dailyInventoryBillingScaleData, monthlyPredictionNetIncreaseData);
        // 解析请求中的年份数据
        result.parseYear(request);

        return result;
    }

    /**
     * 获取需求趋势表格
     *
     * @param request    查询条件
     * @param demandType 需求类型：全年净增规模 新增弹性 退回
     * @return 需求趋势表格数据
     */
    public DemandTrendsTableResponse getDemandTrendsTableResponse(IndustryCockpoitRequest request, String demandType) {
        StopWatch stopWatch = new StopWatch("获取需求趋势表格" + request.getDims().toString());
        IndustryCockpoitRequest.VersionInfo predictVersion = request.getPredictVersion();
        IndustryCockpoitRequest.VersionInfo comparedVersion = request.getComparedVersion();
        request.setVersionInfo(predictVersion);

        stopWatch.start("指定版本的数据");
        // 指定版本的数据
        List<DemandTrendVO> predictVersionDemandTrendsTable = this.demandTrendsTableServiceImpl.getDemandTrendsTable(request, demandType);
        request.setVersionInfo(comparedVersion);
        stopWatch.stop();
        stopWatch.start("对比版本的数据");
        // 对比版本的数据
        List<DemandTrendVO> comparedVersionDemandTrendsTable = this.demandTrendsTableServiceImpl.getDemandTrendsTable(request, demandType);
        stopWatch.stop();
        stopWatch.start("上一年的数据");
        // 上一年的数据
        List<DemandTrendVO> lastYearDemandTrendsTable = this.demandTrendsLastYearTableServiceImpl.getDemandTrendsTable(request, demandType);
        stopWatch.stop();

        stopWatch.start("parse");
        DemandTrendsTableResponse response = DemandTrendsTableResponse.parse(predictVersionDemandTrendsTable, comparedVersionDemandTrendsTable, lastYearDemandTrendsTable);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
        return response;
    }

    /**
     * 获取服务水平排行榜
     *
     * @param request    查询条件
     * @param moduleType 模块类型：全行业 客户级
     * @return 服务水平排行榜表格数据
     */
    public ServiceLevelRankingListTableResponse getServiceLevelRankingList(IndustryCockpoitRequest request, String moduleType) {


        return null;
    }

    /**
     * 获取需求准确率排行榜
     *
     * @param request      查询条件
     * @param demandType   需求类型： 新增&弹性 退回
     * @param industryType 行业类型： 全行业 客户级
     * @return 计划准确率需求概览表格数据
     */
    public PlanAccuracyRankingListTableResponse getPlanAccuracyRankingList(IndustryCockpoitRequest request, String demandType, String industryType) {

        auth(request, industryType); // 权限处理

        List<String> industryDept = request.getIntersection(request.getIndustryDept(),request.getAuthedIndustryDept()); // 行业部门
        List<String> warZone = request.getIntersection(request.getWarZone(),request.getAuthedWarZoneName()); // 战区
        List<String> customer = request.getIntersection(request.getCustomerShortNames(),request.getAuthedCustomerShortName()); // 客户

        // 过滤了行业部门,且为全行业查询
        boolean needFilterIndustryDept = ListUtils.isNotEmpty(industryDept) && IndustryCockpitConstant.ALL_INDUSTRY.equals(industryType);
        boolean needFilterCustomer = (ListUtils.isNotEmpty(industryDept) || ListUtils.isNotEmpty(warZone) || ListUtils.isNotEmpty(customer)) && IndustryCockpitConstant.CUSTOMER.equals(industryType);
        //if (IndustryCockpitConstant.ALL_INDUSTRY.equals(industryType)) {
        request.setIndustryDept(ListUtils.newArrayList()); // 设置空行业查询
        request.setAuthedIndustryDept(ListUtils.newArrayList()); // 设置空行业查询
        request.setWarZone(ListUtils.newArrayList());// 设置空战区查询
        request.setAuthedWarZoneName(ListUtils.newArrayList());// 设置空战区查询
        request.setCustomerShortNames(ListUtils.newArrayList());
        request.setAuthedCustomerShortName(ListUtils.newArrayList());
        //}

        // 由于排名只受准确率影响,所以变化量过不过滤行业都可以,准确率内存过滤就行
        List<List<PlanAccuracyRankingListTableResponse.Item>> allList = PageQueryUtils.waitAll(30,
                // 预测准确率排行榜数据(不过滤行业)
                () -> rankListingServicePredictionAccuracyImpl.getRankListingData(request, demandType, industryType),
                // 预测变化量排行榜数据(不过滤查询行业,但是过滤权限行业,即用户权限还是在方法内部处理)
                () -> rankListingServicePredictionChangeImpl.getRankListingData(request, demandType, industryType));

        List<PlanAccuracyRankingListTableResponse.Item> predictionAccuracyRankListingData = allList.get(0);
        List<PlanAccuracyRankingListTableResponse.Item> predictionChangeRankListingData = allList.get(1);

        predictionAccuracyRankListingData.addAll(predictionChangeRankListingData);
        Map<String, List<PlanAccuracyRankingListTableResponse.Item>> collect = null;
        if (IndustryCockpitConstant.CUSTOMER.equals(industryType)) {
            collect = predictionAccuracyRankListingData.stream()
                    .filter(item -> item.getIndustryDept() != null)
                    .map(item -> {
                        item.setIndustryDeptAndCustomerShortName(item.getIndustryDept() + ":" + item.getWarZone() + ":" + item.getCustomerShortName());
                        return item;
                    })
                    .collect(Collectors.groupingBy(PlanAccuracyRankingListTableResponse.Item::getIndustryDeptAndCustomerShortName, Collectors.toList()));
        } else {
            collect = predictionAccuracyRankListingData.stream()
                    .filter(item -> item.getIndustryDept() != null)
                    .collect(Collectors.groupingBy(PlanAccuracyRankingListTableResponse.Item::getIndustryDept, Collectors.toList()));
        }

        List<PlanAccuracyRankingListTableResponse.Item> realResultData = new ArrayList<>();
        for (Map.Entry<String, List<PlanAccuracyRankingListTableResponse.Item>> entry : collect.entrySet()) {
            // String industryDept = entry.getKey();
            List<PlanAccuracyRankingListTableResponse.Item> industryDeptData = entry.getValue();
            PlanAccuracyRankingListTableResponse.Item item = industryDeptData.get(0);
            for (int i = 1; i < industryDeptData.size(); i++) {
                item = item.combine(industryDeptData.get(i));
            }
            realResultData.add(item);
        }

        // 客户级别的排行设置准入门槛
        // 行业级别不过滤行业筛选,这里内存过滤来展示排名
        if (IndustryCockpitConstant.CUSTOMER.equals(industryType)) {
            realResultData = filterRank(realResultData);
        }
        //else {
        // 先排序(按照准确率降序)
        realResultData = realResultData.stream().sorted((o1, o2) -> {
            if (o1.getPredictionAccuracy() == null) {
                return 1;
            }
            if (o2.getPredictionAccuracy() == null) {
                return -1;
            }
            return o2.getPredictionAccuracy().compareTo(o1.getPredictionAccuracy());
        }).collect(Collectors.toList());
        // 设置排名
        AtomicLong rank = new AtomicLong(1);

        realResultData.forEach(item -> item.setRank(rank.getAndIncrement()));
        if(IndustryCockpitConstant.CUSTOMER.equals(industryType)){
            // 客户级别的排行设置部门内部排名
            Map<String,AtomicLong> map = realResultData.stream().map(PlanAccuracyRankingListTableResponse.Item::getIndustryDept).distinct()
                    .collect(Collectors.toMap(k -> k, v -> new AtomicLong(1)));
            realResultData.forEach(item -> item.setRank2(map.get(item.getIndustryDept()).getAndIncrement()));
        }

        if (needFilterIndustryDept) {
            // 内存过滤行业部门
            Set<String> industrySet = new HashSet<>(industryDept);
            // 最后过滤
            realResultData.forEach(item -> {
                // 非查询部门,除了准确率设置为 null
                if (!industrySet.contains(item.getIndustryDept())) {
                    item.setW13ForecastNum(null);
                    item.setW6ForecastNum(null);
                    item.setBillVariationMonthlyAvg(null);
                    item.setBillVariationMonthlyAvgSlice(null);
                    item.setPredictedQuantity(null);
                }
            });
        }
        //}
        if(needFilterCustomer){
            // 客户级别的过滤客户
            Set<String> industrySet = new HashSet<>(industryDept);
            if(industrySet.size() > 0){
                realResultData = realResultData.stream().filter(item -> industrySet.contains(item.getIndustryDept())).collect(Collectors.toList());
            }
            Set<String> warZoneSet = new HashSet<>(warZone);
            if(warZoneSet.size() > 0){
                realResultData = realResultData.stream().filter(item -> warZoneSet.contains(item.getWarZone())).collect(Collectors.toList());
            }
            Set<String> customerSet = new HashSet<>(customer);
            if(customerSet.size() > 0){
                realResultData = realResultData.stream().filter(item -> customerSet.contains(item.getCustomerShortName())).collect(Collectors.toList());
            }
        }
        PlanAccuracyRankingListTableResponse result = new PlanAccuracyRankingListTableResponse();
        result.setData(realResultData);
        return result;
    }

    private void auth(IndustryCockpoitRequest request, String industryType) {
        boolean isAllIndustryDept = IndustryCockpitConstant.ALL_INDUSTRY.equals(industryType);
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        Boolean adminUserOrNot = permissionService.checkIsAdmin(userNameWithSystem);
        List<String> authedIndustryDept = new ArrayList<>();
        List<String> authedWarZoneName = new ArrayList<>();
        List<String> authedCustomerShortName = new ArrayList<>();
        List<String> authedProduct = new ArrayList<>();
        request.setAuthedIndustryDept(authedIndustryDept);
        request.setAuthedWarZoneName(authedWarZoneName);
        request.setAuthedProduct(authedProduct);
        request.setAuthedCustomerShortName(authedCustomerShortName);
        if (!adminUserOrNot) {
            // 如果不是管理员 需要判断是否有权限看数据
            UserPermissionDto permissionByUserAndRole = permissionService.getPermissionByUserAndRole(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(), userNameWithSystem);
            if (null == permissionByUserAndRole) {
                // 如果没有权限则加一个空值
                authedIndustryDept.add(Constant.EMPTY_VALUE);
            } else {
                // 如果有权限就需要处理权限
                // 是否全行业
                Boolean isAllIndustry = permissionByUserAndRole.getIsAllIndustry();
                if (isAllIndustry) {
                    return;
                } else {
                    // 获取行业信息
                    List<String> industry = permissionByUserAndRole.getIndustry();
                    if (!CollectionUtils.isEmpty(industry)) {
                        authedIndustryDept.addAll(industry);
                    }
                }
                // 全行业的话这里直接跳过
                if (isAllIndustryDept) {
                    request.setIndustryType(IndustryCockpitConstant.ALL_INDUSTRY);
                    return;
                }
                // 是否全部战区
                Boolean isAllWarZone = permissionByUserAndRole.getIsAllWarZone();
                if (isAllWarZone) {
                    return;
                } else {
                    // 获取战区信息
                    List<String> warZone = permissionByUserAndRole.getWarZone();
                    if (!CollectionUtils.isEmpty(warZone)) {
                        authedWarZoneName.addAll(warZone);
                    }
                }
                // 是否全部客户
                Boolean isAllCustomer = permissionByUserAndRole.getIsAllCustomer();
                if (isAllCustomer) {
                    return;
                } else {
                    List<String> customer = permissionByUserAndRole.getCustomer();
                    if (!CollectionUtils.isEmpty(customer)) {
                        authedCustomerShortName.addAll(customer);
                    }
                }
                // 是否全部产品
                Boolean isAllProduct = permissionByUserAndRole.getIsAllProduct();
                if (isAllProduct) {
                    return;
                } else {
                    // 获取产品信息
                    List<String> product = permissionByUserAndRole.getProduct();
                    if (!CollectionUtils.isEmpty(product)) {
                        authedProduct.addAll(product);
                    }
                }
            }
        }
    }


    /**
     * 计入客户级排行榜的条件：|计费变化量(月切片)| ≥ 2000核，或者 |提前6周预测量| ≥ 2000核，或者 |提前13周预测量| ≥ 2000核，或者 |532预测量| ≥ 2000核
     *
     * @param realResultData 排名
     */
    private List<PlanAccuracyRankingListTableResponse.Item> filterRank(List<PlanAccuracyRankingListTableResponse.Item> realResultData) {
        if (ListUtils.isEmpty(realResultData)) {
            return realResultData;
        }
        Predicate<PlanAccuracyRankingListTableResponse.Item> filter = item -> {
            BigDecimal w6ForecastNum = item.getW6ForecastNum(); // 提前6周
            BigDecimal w13ForecastNum = item.getW13ForecastNum(); // 提前13周
//            BigDecimal billVariationMonthlyAvg = item.getBillVariationMonthlyAvg(); // 月均
            BigDecimal billVariationMonthlyAvgSlice = item.getBillVariationMonthlyAvgSlice(); // 月切
            BigDecimal predictedQuantity = item.getPredictedQuantity(); // 532预测量
            BigDecimal base = new BigDecimal("2000"); // 准入核数
            return absCompare(w6ForecastNum, base) || absCompare(w13ForecastNum, base) || absCompare(billVariationMonthlyAvgSlice, base)
                    || absCompare(predictedQuantity, base);
        };
        return realResultData.stream().filter(filter).collect(Collectors.toList());
    }

    /**
     * 绝对值比较
     *
     * @param val    被比较值
     * @param target 比较值
     * @return 被比较值的绝对值是否 >= 比较值的
     */
    private boolean absCompare(BigDecimal val, BigDecimal target) {
        if (SoeCommonUtils.isNullOrZone(target)) {
            return true;
        } else if (target.compareTo(BigDecimal.ZERO) < 0) {
            target = target.abs();
        }
        if (SoeCommonUtils.isNullOrZone(val)) {
            return false;
        }
        return val.abs().compareTo(target) >= 0;
    }

    /**
     * 获取需求准确率需求明细数据
     *
     * @param request      查询条件
     * @param demandType   需求类型： 新增&弹性 退回
     * @param industryType 行业类型： 全行业 客户级
     * @return 计划准确率需求明细表格数据
     */
    public PlanAccuracyDemandDetailTableResponse getPlanAccuracyDemandDetail(IndustryCockpoitRequest request, String demandType, String industryType) {
        //标识为客户级别的需求明细，下面的方法分组查询会到 regionName、instanceType粒度
        request.setCustomerDetail(true);
        auth(request, industryType); // 权限处理

        Function<IPlanAccuracyGroupKey, String> groupKeyFun = (item) -> StringUtils.joinWith("@", item.getIndustryDept(), item.getWarZone(),
                item.getRegionName(), item.getCustomerShortName(), item.getInstanceType());

        List<PlanAccuracyDemandDetailTableResponse.Item> rankListingDetailData = this.rankingDetailServiceBaseImpl.getRankListingDetailData(request, demandType, industryType);

        setRequestParams(request, rankListingDetailData);

        Map<String, PlanAccuracyRankingListTableResponse.Item> map = this.rankListingServicePredictionChangeImpl.getRankListingData(request, demandType, industryType)
                .stream().collect(Collectors.toMap(groupKeyFun, Function.identity(), (k1, k2) -> k1));
        // 合并机型 -> 机型的映射
        Map<String,String> commonInstanceTypeConfig = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class, "where use_forecast = 1")
                    .stream().collect(Collectors.toMap(Mrpv2CommonInstanceTypeConfigDO::getCommonInstanceType,Mrpv2CommonInstanceTypeConfigDO::getInstanceTypes));

        for (PlanAccuracyDemandDetailTableResponse.Item item : rankListingDetailData) {
            if(StringUtils.equals(request.getMergeInstanceType(),"是")){
                String instanceTypes = commonInstanceTypeConfig.getOrDefault(item.getInstanceType(),item.getInstanceType());
                item.setInstanceTypes(instanceTypes);
            }
            PlanAccuracyRankingListTableResponse.Item temp = map.get(groupKeyFun.apply(item));
            if (Objects.isNull(temp)) {
                item.setBillVariationMonthlyAvgSlice(BigDecimal.ZERO);
            } else {
                item.setBillVariationMonthlyAvgSlice(temp.getBillVariationMonthlyAvgSlice());
            }
        }
        PlanAccuracyDemandDetailTableResponse planAccuracyDemandDetailTableResponse = new PlanAccuracyDemandDetailTableResponse();
        planAccuracyDemandDetailTableResponse.setData(rankListingDetailData);

        return planAccuracyDemandDetailTableResponse;
    }

    private static void setRequestParams(IndustryCockpoitRequest request, List<PlanAccuracyDemandDetailTableResponse.Item> rankListingDetailData) {
        List<String> industryDeptList = ListUtils.newArrayList();
        List<String> warZoneList = ListUtils.newArrayList();
        List<String> regionNameList = ListUtils.newArrayList();
        List<String> customerShortNameList = ListUtils.newArrayList();
        List<String> instanceTypeList = ListUtils.newArrayList();
        for (PlanAccuracyDemandDetailTableResponse.Item item : rankListingDetailData) {
            industryDeptList.add(item.getIndustryDept());
            warZoneList.add(item.getWarZone());
            regionNameList.add(item.getRegionName());
            customerShortNameList.add(item.getCustomerShortName());
            instanceTypeList.add(item.getInstanceType());
        }
        request.setIndustryDept(industryDeptList.stream().distinct().collect(Collectors.toList()));
        request.setWarZone(warZoneList.stream().distinct().collect(Collectors.toList()));
        request.setRegionName(regionNameList.stream().distinct().collect(Collectors.toList()));
        request.setCustomerShortNames(customerShortNameList.stream().distinct().collect(Collectors.toList()));
        request.setInstanceType(instanceTypeList.stream().distinct().collect(Collectors.toList()));
    }
}
