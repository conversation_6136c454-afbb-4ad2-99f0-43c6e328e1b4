package cloud.demand.app.modules.industry_cockpit.v4.model.excel;

import cloud.demand.app.modules.industry_cockpit.v4.enums.SupplyDeliveryExportDimFieldEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/2/26 19:31
 */
@Data
public class ExportSupplyDeliveryVO {

    private List<List<String>> headList;

    private List<List<Object>> dataList;

    public static ExportSupplyDeliveryVO build(List<String> dims,List<SupplyDeliveryModel> models,String startStatTime,String endStatTime) {
        ExportSupplyDeliveryVO vo = new ExportSupplyDeliveryVO();
        vo.setHeadList(buildHeadList(dims, startStatTime, endStatTime));
        vo.setDataList(buildDataList(dims, models, startStatTime, endStatTime));
        return vo;
    }

    private static List<List<String>> buildHeadList(List<String> dims,String startTime, String endStatTime) {
        List<List<String>> headList = ListUtils.newArrayList();
        for(String dim : dims) {
            String fieldName = SupplyDeliveryExportDimFieldEnum.getNameByField(dim);
            if(StringUtils.isNotEmpty(fieldName)){
                headList.add(ListUtils.newArrayList(fieldName));
            }
        }
        headList.add(ListUtils.newArrayList("供需类型"));
        headList.add(ListUtils.newArrayList("指标类型"));
        headList.add(ListUtils.newArrayList("单据"));
        headList.add(ListUtils.newArrayList("需求提前期"));
        headList.add(ListUtils.newArrayList("指标年"));
        headList.add(ListUtils.newArrayList("指标月"));
        headList.add(ListUtils.newArrayList("指标周"));
        headList.add(ListUtils.newArrayList("指标日期"));
        if(StringUtils.isNotEmpty(startTime)) {
            headList.add(ListUtils.newArrayList("指标值-"+startTime));
        }
        if(StringUtils.isNotEmpty(endStatTime)) {
            headList.add(ListUtils.newArrayList("指标值-"+endStatTime));
        }
        if(StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endStatTime)) {
            headList.add(ListUtils.newArrayList("指标值-变化量"));
        }
        return headList;
    }

    private static List<List<Object>> buildDataList(List<String> dims,List<SupplyDeliveryModel> data,String startTime, String endStatTime) {
        List<List<Object>> rows = ListUtils.newArrayList();

        for(SupplyDeliveryModel item : data) {
            List<Object> row = ListUtils.newArrayList();
            for(String dim : dims) {
                Field[] fields = SupplyDeliveryModel.class.getDeclaredFields();
                for(Field field : fields) {
                    field.setAccessible(true);
                    if(field.getName().equals(dim)) {
                        try {
                            row.add(field.get(item));
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                        }
                    }
                }
            }
            row.add(item.getSupplyOrDemand());
            row.add(item.getIndexType());
            row.add(item.getBillNumber());
            row.add(item.getAdvanceWeek());
            row.add(item.getYear());
            row.add(item.getMonth());
            row.add(item.getWeek());
            row.add(item.getDate());
            if(StringUtils.isNotEmpty(startTime)) {
                row.add(item.getStartNumber());
            }
            if(StringUtils.isNotEmpty(endStatTime)) {
                row.add(item.getEndNumber());
            }
            if(StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endStatTime)) {
                row.add(item.getDiffNumber());
            }
            rows.add(row);
        }
        return rows;
    }

}
