package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder.SopWhere;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/** 实例族 */
public class InstanceFamilyParse implements IWhereParser {
    @Override
    public void parse(ORMUtils.WhereContent content, SopWhereBuilder.SopWhere sopWhere, Object t) {
        List<String> instanceFamilyList = (List<String>) sopWhere.getV();
        if (ListUtils.isNotEmpty(instanceFamilyList)){
            Map<String, Set<String>> map = SpringUtil.getBean(DictService.class)
                    .getInstanceFamily2TypeMap();
            List<String> instanceType = new ArrayList<>();
            for (String instanceFamily : instanceFamilyList) {
                Set<String> instanceTypeList = map.get(instanceFamily);
                if (ListUtils.isNotEmpty(instanceTypeList)){
                    instanceType.addAll(instanceTypeList);
                }
            }
            content.andInIfValueNotEmpty("instance_type", instanceType);
        }
    }

    @Override
    public void parseSQL(WhereSQL content, SopWhere sopWhere, Object t) {
        List<String> instanceFamilyList = (List<String>) sopWhere.getV();
        if (ListUtils.isNotEmpty(instanceFamilyList)){
            Map<String, Set<String>> map = SpringUtil.getBean(DictService.class)
                    .getInstanceFamily2TypeMap();
            List<String> instanceType = new ArrayList<>();
            for (String instanceFamily : instanceFamilyList) {
                Set<String> instanceTypeList = map.get(instanceFamily);
                if (ListUtils.isNotEmpty(instanceTypeList)){
                    instanceType.addAll(instanceTypeList);
                }
            }
            content.andIf(ListUtils.isNotEmpty(instanceType),"instance_type in (?)", instanceType);
        }
    }
}
