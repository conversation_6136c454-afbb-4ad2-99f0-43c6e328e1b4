package cloud.demand.app.modules.industry_cockpit.service;

import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;

import java.util.List;

/**
 * 存量规模 服务定义
 *
 * <AUTHOR>
 * @since 2024/4/25
 */
public interface InventoryBillScaleService<T> {

    /**
     * 获取存量规模
     *
     * @param request 查询条件
     * @return 存量规模
     */
    List<T> getInventoryBillingScaleData(IndustryCockpoitRequest request, String startDate, String endDate);

}
