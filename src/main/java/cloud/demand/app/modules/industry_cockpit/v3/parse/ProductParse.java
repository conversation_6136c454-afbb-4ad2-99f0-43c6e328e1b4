package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.enums.ProductEnum;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:32
 */
public class ProductParse implements IWhereParser {
    @Override
    public void parse(ORMUtils.WhereContent content, SopWhereBuilder.SopWhere sopWhere, Object t) {
        String product = (String) sopWhere.getV();
        if (StringUtils.equals(product, ProductEnum.CVM_CPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.CPU, ListUtils.newArrayList(IndustryCockpitV3Constant.LOWER_CVM));
        }
        if (StringUtils.equals(product, ProductEnum.BARE_METAL_CPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.CPU, ListUtils.newArrayList(IndustryCockpitV3Constant.BARE_METAL));
        }
        if (StringUtils.equals(product, ProductEnum.CPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.CPU, ListUtils.newArrayList(IndustryCockpitV3Constant.LOWER_CVM, IndustryCockpitV3Constant.BARE_METAL));
        }
        if (StringUtils.equals(product, ProductEnum.CVM_GPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.GPU, ListUtils.newArrayList(IndustryCockpitV3Constant.LOWER_CVM));
        }
        if (StringUtils.equals(product, ProductEnum.BARE_METAL_GPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.GPU, ListUtils.newArrayList(IndustryCockpitV3Constant.BARE_METAL));
        }
        if (StringUtils.equals(product, ProductEnum.GPU.getName())) {
            fillWhereContent(content, IndustryCockpitV3Constant.GPU, ListUtils.newArrayList(IndustryCockpitV3Constant.LOWER_CVM, IndustryCockpitV3Constant.BARE_METAL));
        }
    }

    private void fillWhereContent(ORMUtils.WhereContent content, String cpuOrGpu, List<String> bizTypes) {
        content.addAnd("cpu_or_gpu = ? and biz_type in (?)", cpuOrGpu, bizTypes);
    }
}
