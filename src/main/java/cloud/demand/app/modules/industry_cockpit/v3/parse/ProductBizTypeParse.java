package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/8/14 11:32
 */
public class ProductBizTypeParse implements IWhereParser {
    @Override
    public void parse(ORMUtils.WhereContent content, SopWhereBuilder.SopWhere sopWhere, Object t) {
        String product = (String) sopWhere.getV();
        if (StringUtils.contains(product,IndustryCockpitV3Constant.CPU)) {
            content.addAnd(" cpu_or_gpu = ? ", IndustryCockpitV3Constant.CPU);
        }else {
            content.addAnd(" cpu_or_gpu = ? ", IndustryCockpitV3Constant.GPU);
        }
    }
}
