package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/8/15 11:19
 */
@Data
public class HistoricalScaleDailyHistogramVO {

    @Column("profile_type")
    private String profileType;//分布类型

    @Column("instance_model")
    private String instanceModel;//实例model

    @Column("amount")
    private BigDecimal amount;// 实例数量

    @Column("specifications")
    private String specifications;//规格

}
