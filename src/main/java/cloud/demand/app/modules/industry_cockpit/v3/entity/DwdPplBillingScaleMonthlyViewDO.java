package cloud.demand.app.modules.industry_cockpit.v3.entity;

import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

import java.time.LocalDate;

@Data
@ToString
@Table("dwd_ppl_billing_scale_monthly_view")
public class DwdPplBillingScaleMonthlyViewDO {

    /** 分区键，代表数据版本<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 客户范围：1-内部,0-外部<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Integer isInner;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 原行业部门<br/>Column: [origin_industry_dept] */
    @Column(value = "origin_industry_dept")
    private String originIndustryDept;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** crp战区<br/>Column: [crp_war_zone] */
    @Column(value = "crp_war_zone")
    private String crpWarZone;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 通用客户简称<br/>Column: [un_customer_short_name] */
    @Column(value = "un_customer_short_name")
    private String unCustomerShortName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 合并实例类型<br/>Column: [un_instance_type] */
    @Column(value = "un_instance_type")
    private String unInstanceType;

    /** GPU卡型<br/>Column: [gpu_card_type] */
    @Column(value = "gpu_card_type")
    private String gpuCardType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地域<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 当月净增计费用量（核数）<br/>Column: [diff_billcpu] */
    @Column(value = "diff_billcpu")
    private Double diffBillcpu;

    /** 当月平均计费用量（核数）<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 当月净增服务用量（核数）<br/>Column: [diff_freecpu] */
    @Column(value = "diff_freecpu")
    private Double diffFreecpu;

    /** 当月平均服务用量（核数）<br/>Column: [cur_freecpu] */
    @Column(value = "cur_freecpu")
    private Double curFreecpu;

    /** 当月平均计费用量（卡数）<br/>Column: [cur_billgpu] */
    @Column(value = "cur_billgpu")
    private Double curBillgpu;

    /** 当月净增计费用量（卡数）<br/>Column: [diff_billgpu] */
    @Column(value = "diff_billgpu")
    private Double diffBillgpu;

    /** 当月平均服务用量（卡数）<br/>Column: [cur_freegpu] */
    @Column(value = "cur_freegpu")
    private Double curFreegpu;

    /** 当月净增服务用量（卡数）<br/>Column: [diff_freegpu] */
    @Column(value = "diff_freegpu")
    private Double diffFreegpu;

    /** appId<br/>Column: [app_id] */
    @Column(value = "app_id")
    private Long appId;

    /** uin<br/>Column: [uin] */
    @Column(value = "uin")
    private Long uin;

    /** approle<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

    /** CPU/GPU<br/>Column: [cpu_or_gpu] */
    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    /** cvm/baremetal<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 客户类型
     * @com.cloud.demand.app.modules.industry_cockpit.v3.enums.CustomerType
     * */

    @Column(value = "customer_type")
    private Integer customerType;// 客户类型，枚举：0-个人 1-企业  2-政府 3-组织 99 未知 -1未知
}