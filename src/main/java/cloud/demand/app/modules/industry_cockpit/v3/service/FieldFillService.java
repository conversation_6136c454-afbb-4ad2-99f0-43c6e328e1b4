package cloud.demand.app.modules.industry_cockpit.v3.service;

import cloud.demand.app.modules.industry_cockpit.v3.model.req.HistoricalScaleMonthlyReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.IInstanceFamilyDimReq;
import cloud.demand.app.modules.industry_cockpit.v3.model.req.IInstanceFamilyReq;
import java.util.List;

/** 字段填充 */
public interface FieldFillService {
    /** 填充实例族 */
    public String fillInstanceFamilyDim(IInstanceFamilyDimReq req, String sql, List<String> fieldNames);

    public String fillInstanceFamily(IInstanceFamilyReq req, List<String> dims ,String sql, List<String> fieldNames);

    public String fillInstanceFamily(IInstanceFamilyReq req, List<String> dims ,String sql, List<String> fieldNames, String field);

    public String fillShowCustomerShortNameDim(HistoricalScaleMonthlyReq req, String sql, List<String> fieldNames);

    public String fillShowCustomerShortName(HistoricalScaleMonthlyReq req, List<String> dims ,String sql, List<String> fieldNames);
}
