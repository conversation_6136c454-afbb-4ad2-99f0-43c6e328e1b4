package cloud.demand.app.modules.industry_cockpit.process.enums;

import cloud.demand.app.modules.industry_cockpit.process.field.IBillNumber;
import cloud.demand.app.modules.industry_cockpit.process.field.IOrderDim;
import cloud.demand.app.modules.industry_cockpit.process.field.ITimeDim;
import cloud.demand.app.modules.soe.model.fields.FieldInfo;
import cloud.demand.app.modules.soe.model.fields.ICountryName;
import cloud.demand.app.modules.soe.model.fields.ICustomhouseTitle;
import cloud.demand.app.modules.soe.model.fields.IGenerationInstanceType;
import cloud.demand.app.modules.soe.model.fields.IIndustryDept;
import cloud.demand.app.modules.soe.model.fields.IInstanceGroup;
import cloud.demand.app.modules.soe.model.fields.IInstanceType;
import cloud.demand.app.modules.soe.model.fields.IMainInstanceType;
import cloud.demand.app.modules.soe.model.fields.IMainZoneType;
import cloud.demand.app.modules.soe.model.fields.IRegionName;
import cloud.demand.app.modules.soe.model.fields.IUnCustomerShortName;
import cloud.demand.app.modules.soe.model.fields.IZoneName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import yunti.boot.exception.ITException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/** 字段枚举 */
@Getter
@AllArgsConstructor
public enum SupplyDeliveryIndexFieldEnum  {
    industryDept("部门",o -> ((IIndustryDept)o).getIndustryDept()),
    unCustomerShortName("客户简称",o -> ((IUnCustomerShortName)o).getUnCustomerShortName()),
    customhouseTitle("境内外",o -> ((ICustomhouseTitle)o).getCustomhouseTitle()),
    countryName("境内外",o -> ((ICountryName)o).getCountryName()),
    regionName("地域",o -> ((IRegionName)o).getRegionName()),
    zoneName("区域",o -> ((IZoneName)o).getZoneName()),
    instanceGroup("实例族",o -> ((IInstanceGroup)o).getInstanceGroup()),
    instanceType("实例类型",o -> ((IInstanceType)o).getInstanceType()),
    mainZoneType("主力园区",o -> ((IMainZoneType)o).getMainZoneType()),
    generationInstanceType("新旧机型",o -> ((IGenerationInstanceType)o).getGenerationInstanceType()),
    mainInstanceType("主力机型",o -> ((IMainInstanceType)o).getMainInstanceType()),
    billNumber("单据编号",o-> ((IBillNumber)o).getBillNumber()),
    orderDim("订单维度",o-> ((IOrderDim)o).getOrderDim()),
    timeDim("时间维度",o -> ((ITimeDim)o).getTimeDim()),
    ;

    private final String name;

    private final Function<Object,Object> fieldGetter;

    private final static Map<String,Function<Object,Object>> fieldMap = Arrays.stream(values())
            .collect(Collectors.toMap(Enum::name, SupplyDeliveryIndexFieldEnum::getFieldGetter));

    /** 字段集合 */
    public static List<FieldInfo> getFields(){
        List<FieldInfo> fieldInfos = new ArrayList<>();
        for (SupplyDeliveryIndexFieldEnum value : SupplyDeliveryIndexFieldEnum.values()) {
            fieldInfos.add(new FieldInfo(value.name(), value.getName()));
        }
        return fieldInfos;
    }

    /** 根据字段名称获取字段值 */
    public static <T> Object getValue(String fieldName,T t){
        Function<Object, Object> fieldGetter = fieldMap.get(fieldName);
        if (fieldGetter == null){
            throw new ITException(String.format("字段找不到，字段名称：【%s】", ObjectUtils.defaultIfNull(fieldName,"null")));
        }
        return fieldGetter.apply(t);
    }
}
