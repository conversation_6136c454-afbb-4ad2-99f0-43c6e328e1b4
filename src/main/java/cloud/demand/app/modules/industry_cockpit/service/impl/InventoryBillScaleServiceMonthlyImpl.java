package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.ChartDataItemVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.InventoryBillScaleService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 存量规模 - 每月存量规模 实现
 *
 * <AUTHOR>
 * @since 2024/4/25
 */
@Service
public class InventoryBillScaleServiceMonthlyImpl implements InventoryBillScaleService<ChartDataItemVO> {

    @Resource
    private DBHelper ckcldDBHelper;

    @Autowired
    @Qualifier("authServiceCvmCbsProductImpl")
    private AuthService cvmCbsProductAuthServiceImpl;

    @Override
    public List<ChartDataItemVO> getInventoryBillingScaleData(IndustryCockpoitRequest request, String startDate, String endDate) {


        WhereSQL predictionNetIncreaseMonthlyWhereSql = cvmCbsProductAuthServiceImpl.getAuthWhereSql();
        WhereSQL predictionVersionWhere = cvmCbsProductAuthServiceImpl.getAuthWhereSql();
        if (null == predictionNetIncreaseMonthlyWhereSql || null == predictionVersionWhere) {
            return Collections.emptyList();
        }
        String predictionNetIncreaseMonthlyFixWhere = "";
        if (IndustryCockpitConstant.INTERVENED.equals(request.getIsComd())) {
            // 已干预
            predictionNetIncreaseMonthlyFixWhere = "and is_comd != 1 and ((industry_dept != '中长尾' AND source NOT IN ('FORECAST','APPLY_AUTO_FILL_LONGTAIL')) or (industry_dept = '中长尾' and source IN ('FORECAST')))";
        } else {
            // 未干预
            predictionNetIncreaseMonthlyFixWhere = "and source in ('IMPORT','APPLY_AUTO_FILL','FORECAST') and demand_type in ('NEW', 'ELASTIC', 'RETURN')";
        }
        if ("CVM".equals(request.getProduct())) {
            predictionNetIncreaseMonthlyWhereSql.and("product in (?) ", "CVM&CBS");
        }

        predictionNetIncreaseMonthlyWhereSql.and("begin_buy_date >= ? ", startDate);
        predictionNetIncreaseMonthlyWhereSql.and("begin_buy_date <= ? ", endDate);
        predictionVersionWhere.and("begin_buy_date >= ? ", startDate);
        predictionVersionWhere.and("begin_buy_date <= ? ", endDate);
        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            predictionNetIncreaseMonthlyWhereSql.and("industry_dept in (?) ", request.getIndustryDept()); // 行业
        }

        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            predictionNetIncreaseMonthlyWhereSql.and("war_zone in (?) ", request.getWarZone()); // 战区
        }

        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            predictionNetIncreaseMonthlyWhereSql.and("common_customer_short_name in (?) ", request.getCustomerShortNames()); // 客户
        }

        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            predictionNetIncreaseMonthlyWhereSql.and("customhouse_title in (?) ", request.getCustomhouseTitle()); // 境内外
        }

        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            if (!StringUtils.isEmpty(request.getMergeInstanceType()) && "是".equals(request.getMergeInstanceType())) {
                predictionNetIncreaseMonthlyWhereSql.and("common_instance_type in (?) ", request.getInstanceType()); // 实例类型
            } else {
                predictionNetIncreaseMonthlyWhereSql.and("instance_type in (?) ", request.getInstanceType()); // 实例类型
            }
        }

        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            predictionNetIncreaseMonthlyWhereSql.and("region_name in (?) ", request.getRegionName()); // 地域
        }

        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            predictionNetIncreaseMonthlyWhereSql.and("zone_name in (?) ", request.getZoneName()); // 可用区
        }

        // 设置预测版本
        request.setVersionInfo(request.getPredictVersion());
        if (null == request.getVersionInfo()) {
            // 预测版本为空
            predictionVersionWhere.and("1 = 0 ");
        } else {
            IndustryCockpoitRequest.VersionInfo versionInfo = request.getVersionInfo();
            String versionCode = versionInfo.getVersionCode();
            String startMonth = versionInfo.getStartMonth();
            String endMonth = versionInfo.getEndMonth();
            // 最新版排除指定版本的数据
            WhereSQL orWhereSQL = new WhereSQL();
            orWhereSQL.or("toYYYYMM(begin_buy_date) < ? ", startMonth);
            orWhereSQL.or("toYYYYMM(begin_buy_date) > ? ", endMonth);
            predictionNetIncreaseMonthlyWhereSql.and(orWhereSQL);
            predictionNetIncreaseMonthlyWhereSql.and(" year(begin_buy_date) = ? ", request.getDemandYear());
            // 指定版本加上该批数据
            predictionVersionWhere.and("version_code = ? ", versionCode);
            predictionVersionWhere.and("toYYYYMM(begin_buy_date) >= ? ", startMonth);
            predictionVersionWhere.and("toYYYYMM(begin_buy_date) <= ? ", endMonth);
            predictionVersionWhere.and(" year(begin_buy_date) = ? ", request.getDemandYear());
            if ("CVM".equals(request.getProduct())) {
                predictionVersionWhere.and("product in (?) ", "CVM&CBS");
            }
            if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
                predictionVersionWhere.and("industry_dept in (?)", request.getIndustryDept());
            }
            if (!CollectionUtils.isEmpty(request.getWarZone())) {
                predictionVersionWhere.and("war_zone in (?)", request.getWarZone());
            }
            if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
                predictionVersionWhere.and("common_customer_short_name in (?)", request.getCustomerShortNames());
            }
            if (!CollectionUtils.isEmpty(request.getInstanceType())) {
                if (!StringUtils.isEmpty(request.getMergeInstanceType()) && "是".equals(request.getMergeInstanceType())) {
                    predictionVersionWhere.and("common_instance_type in (?)", request.getInstanceType());
                } else {
                    predictionVersionWhere.and("instance_type in (?)", request.getInstanceType());
                }
            }
            if (!CollectionUtils.isEmpty(request.getRegionName())) {
                predictionVersionWhere.and("region_name in (?)", request.getRegionName());
            }
            if (!CollectionUtils.isEmpty(request.getZoneName())) {
                predictionVersionWhere.and("zone_name in (?)", request.getZoneName());
            }
        }

        // 获取去年的实际规模变化量 /sql/industry_cockpit/8_prediction_net_increase_monthly.sql
        String inventoryBillingScaleSql = ORMUtils.getSql("/sql/industry_cockpit/8_prediction_net_increase_monthly.sql");
        inventoryBillingScaleSql = inventoryBillingScaleSql.replace("${FIX_WHERE}", predictionNetIncreaseMonthlyFixWhere);
        inventoryBillingScaleSql = inventoryBillingScaleSql.replace("${FILTER}", predictionNetIncreaseMonthlyWhereSql.getSQL());
        inventoryBillingScaleSql = inventoryBillingScaleSql.replace("${VERSION_PREDICTION_FILTER}", predictionVersionWhere.getSQL());
        // 参数合并
        Object[] historyParams = predictionNetIncreaseMonthlyWhereSql.getParams();
        Object[] predictionVersionParams = predictionVersionWhere.getParams();
        Object[] paramAll = new Object[historyParams.length + predictionVersionParams.length];
        System.arraycopy(historyParams, 0, paramAll, 0, historyParams.length);
        System.arraycopy(predictionVersionParams, 0, paramAll, historyParams.length, predictionVersionParams.length);

        // 获取上一年的实际规模变化量
        return ckcldDBHelper.getRaw(ChartDataItemVO.class, inventoryBillingScaleSql, paramAll);

    }
}
