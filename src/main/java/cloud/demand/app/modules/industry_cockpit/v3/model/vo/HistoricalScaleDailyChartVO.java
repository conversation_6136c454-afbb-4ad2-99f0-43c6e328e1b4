package cloud.demand.app.modules.industry_cockpit.v3.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/8/15 11:19
 */
@Data
public class HistoricalScaleDailyChartVO {

    @Column("stat_time")
    private String statTime;//统计时间

    @Column("new_amount")
    private BigDecimal newAmount;//新增

    @Column("return_amount")
    private BigDecimal returnAmount;//退回

    @Column("change_amount")
    private BigDecimal changeAmount;//净增

    @Column("cur_amount")
    private BigDecimal curAmount;//计费或服务规模
}
