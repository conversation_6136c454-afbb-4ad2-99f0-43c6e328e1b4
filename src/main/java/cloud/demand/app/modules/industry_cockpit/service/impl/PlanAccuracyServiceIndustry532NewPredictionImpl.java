package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.constant.IndustryCockpitConstant;
import cloud.demand.app.modules.industry_cockpit.entity.ChartDataItemVO;
import cloud.demand.app.modules.industry_cockpit.model.IndustryCockpoitRequest;
import cloud.demand.app.modules.industry_cockpit.service.AuthService;
import cloud.demand.app.modules.industry_cockpit.service.PlanAccuracyService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 获取532新版的预测量
 * 生效的查询条件：产品、年度、行业、战区、客户、境内外、实例类型、地域、可用区、预测量口径（干预前 干预后） 新增弹性或者退回
 *
 * <AUTHOR>
 * @since 2024/4/23
 */
@Service
public class PlanAccuracyServiceIndustry532NewPredictionImpl implements PlanAccuracyService<ChartDataItemVO> {

    @Resource
    private DBHelper ckcldDBHelper;
    @Autowired
    @Qualifier("authServiceCvmCbsProductImpl")
    private AuthService cvmCbsProductAuthServiceImpl;
    /**
     * 获取532预测量数据
     *
     * @param request    查询条件
     * @param demandType 需求类型 枚举值：新增&弹性 退回 净增
     * @return 532新版预测数据
     */
    @Override
    public List<ChartDataItemVO> getAccuracyData(IndustryCockpoitRequest request, String demandType) {

        WhereSQL whereSQL = cvmCbsProductAuthServiceImpl.getAuthWhereSql();
        if (null == whereSQL) {
            return Collections.emptyList();
        }
        String chartStartDate = "";
        if (StringUtils.isEmpty(request.getDemandYear())) {
            String year = LocalDate.now().minusYears(1).format(DateTimeFormatter.ofPattern("yyyy"));
            chartStartDate = LocalDate.of(Integer.valueOf(year), 1, 1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            chartStartDate = LocalDate.of(Integer.valueOf(request.getDemandYear()) - 1, 1, 1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        String chartEndDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        whereSQL.and("begin_buy_date >= ? ", chartStartDate);
        whereSQL.and("begin_buy_date <= ? ", chartEndDate);
        if ("CVM".equals(request.getProduct())) {
            whereSQL.and("product in (?) ", "CVM&CBS");
        }
        if (IndustryCockpitConstant.NEW_OR_ELASTIC.equals(demandType)) {
            whereSQL.and("demand_type in (?) ", Arrays.asList("NEW", "ELASTIC")); // 需求类型
        } else if (IndustryCockpitConstant.RETURN.equals(demandType)) {
            whereSQL.and("demand_type in (?) ", Arrays.asList("RETURN")); // 需求类型
        }
        if (!CollectionUtils.isEmpty(request.getIndustryDept())) {
            whereSQL.and("industry_dept in (?) ", request.getIndustryDept()); // 行业
        }

        if (!CollectionUtils.isEmpty(request.getWarZone())) {
            whereSQL.and("war_zone in (?) ", request.getWarZone()); // 战区
        }

        if (!CollectionUtils.isEmpty(request.getCustomerShortNames())) {
            whereSQL.and("common_customer_short_name in (?) ", request.getCustomerShortNames()); // 客户
        }

        if (!CollectionUtils.isEmpty(request.getCustomhouseTitle())) {
            whereSQL.and("customhouse_title in (?) ", request.getCustomhouseTitle()); // 境内外
        }

        if (!CollectionUtils.isEmpty(request.getInstanceType())) {
            if (!StringUtils.isEmpty(request.getMergeInstanceType()) && "是".equals(request.getMergeInstanceType())) {
                whereSQL.and("common_instance_type in (?) ", request.getInstanceType()); // 实例类型
            } else {
                whereSQL.and("instance_type in (?) ", request.getInstanceType()); // 实例类型
            }
        }

        if (!CollectionUtils.isEmpty(request.getRegionName())) {
            whereSQL.and("region_name in (?) ", request.getRegionName()); // 地域
        }

        if (!CollectionUtils.isEmpty(request.getZoneName())) {
            whereSQL.and("zone_name in (?) ", request.getZoneName()); // 可用区
        }
        // 以下是固定的查询条件
        String fixSql = "";
        if (StringUtils.isEmpty(request.getIsComd()) || request.getIsComd().equals(IndustryCockpitConstant.NO_INTERVENTION)) {
            // 未干预
            fixSql = "and source in ('IMPORT','APPLY_AUTO_FILL','FORECAST')";
        }
        if (!StringUtils.isEmpty(request.getIsComd()) && request.getIsComd().equals(IndustryCockpitConstant.INTERVENED)) {
            // 已干预
            fixSql = "and is_comd != 1 and ((industry_dept != '中长尾' AND source NOT IN ('FORECAST','APPLY_AUTO_FILL_LONGTAIL')) or (industry_dept = '中长尾' and source IN ('FORECAST')))";
        }

        String sql = ORMUtils.getSql("/sql/industry_cockpit/3_industry_532_new_prediction_data.sql");
        sql = sql.replace("${FIX_WHERE}", fixSql);
        sql = sql.replace("${FILTER}", whereSQL.getSQL());

        List<ChartDataItemVO> raw = ckcldDBHelper.getRaw(ChartDataItemVO.class, sql, whereSQL.getParams());

        return raw;
    }
}
