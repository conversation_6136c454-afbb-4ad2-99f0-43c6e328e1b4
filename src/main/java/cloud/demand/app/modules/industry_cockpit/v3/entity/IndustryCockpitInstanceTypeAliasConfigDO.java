package cloud.demand.app.modules.industry_cockpit.v3.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("industry_cockpit_instance_type_alias_config")
public class IndustryCockpitInstanceTypeAliasConfigDO {

    /** 主键<br/>Column: [id] */
    @JsonIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 合并实例类型<br/>Column: [un_instance_type] */
    @Column(value = "un_instance_type")
    private String unInstanceType;

    /** 实例类型别名<br/>Column: [instance_type_alias] */
    @Column(value = "instance_type_alias")
    private String instanceTypeAlias;

    /** CPU类型<br/>Column: [cpu_type] */
    @Column(value = "cpu_type")
    private String cpuType;

    /** 是否参与预测：是/否<br/>Column: [prediction] */
    @Column(value = "prediction")
    private String prediction;

}