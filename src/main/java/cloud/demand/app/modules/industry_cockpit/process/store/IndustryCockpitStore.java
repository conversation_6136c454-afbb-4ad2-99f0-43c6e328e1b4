package cloud.demand.app.modules.industry_cockpit.process.store;

/**
 * 驾驶舱仓库注册
 *
 * <AUTHOR>
 * @since 2025/2/14 11:02
 */

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.process.enums.SupplyDeliveryIndexEnum;
import cloud.demand.app.modules.industry_cockpit.process.item.SupplyDeliveryIndexItem;
import cloud.demand.app.modules.industry_cockpit.v4.entity.SupplyDeliveryDfDO;
import cloud.demand.app.modules.industry_cockpit.v4.model.req.SupplyDeliveryReq;
import cloud.demand.app.modules.industry_cockpit.v4.model.vo.SupplyDeliveryVO;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@StoreRegisterClient
public class IndustryCockpitStore {

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @StoreRegister(name = "supply_delivery_init", desc = "供应查询初始化参数")
    public List<SupplyDeliveryIndexItem> supplyDeliveryInit(Map<String, Object> params) {
        SupplyDeliveryReq req = (SupplyDeliveryReq) params.get("req");
        List<String> orderDimList = getOrderNumberList(req);
        req.setOrderNumber(orderDimList);
        return null;
    }

    @StoreRegister(name = "order_demand", desc = "订单(共识需求)")
    public List<SupplyDeliveryIndexItem> orderDemand(Map<String, Object> params) {
        SupplyDeliveryReq req = (SupplyDeliveryReq) params.get("req");
        if (ListUtils.isEmpty(req.getOrderNumber())) {
            return ListUtils.newArrayList();
        }
        List<SupplyDeliveryVO> supplyDeliveryVOList = getSupplyDeliveryList(req, "需求@" + SupplyDeliveryIndexEnum.order_demand.getName());
        return ListUtils.transform(supplyDeliveryVOList, SupplyDeliveryIndexItem::transform);
    }

    @StoreRegister(name = "supply_match_satisfy", desc = "供应@大盘满足")
    public List<SupplyDeliveryIndexItem> supplyMatchSatisfy(Map<String, Object> params) {
        SupplyDeliveryReq req = (SupplyDeliveryReq) params.get("req");
        if (ListUtils.isEmpty(req.getOrderNumber())) {
            return ListUtils.newArrayList();
        }
        SupplyDeliveryReq clone = req.cloneReqForSupply();
        List<SupplyDeliveryVO> supplyDeliveryVOList = getSupplyDeliveryList(clone, SupplyDeliveryIndexEnum.supply_match_satisfy.getName());
        return ListUtils.transform(supplyDeliveryVOList, SupplyDeliveryIndexItem::transform);
    }

    @StoreRegister(name = "supply_match_buy", desc = "供应@采购满足")
    public List<SupplyDeliveryIndexItem> supplyMatchBuy(Map<String, Object> params) {
        SupplyDeliveryReq req = (SupplyDeliveryReq) params.get("req");
        if (ListUtils.isEmpty(req.getOrderNumber())) {
            return ListUtils.newArrayList();
        }
        SupplyDeliveryReq clone = req.cloneReqForSupply();
        List<SupplyDeliveryVO> supplyDeliveryVOList = getSupplyDeliveryList(clone, SupplyDeliveryIndexEnum.supply_match_buy.getName());
        return ListUtils.transform(supplyDeliveryVOList, SupplyDeliveryIndexItem::transform);
    }

    @StoreRegister(name = "supply_match_move", desc = "供应@搬迁满足")
    public List<SupplyDeliveryIndexItem> supplyMatchMove(Map<String, Object> params) {
        SupplyDeliveryReq req = (SupplyDeliveryReq) params.get("req");
        if (ListUtils.isEmpty(req.getOrderNumber())) {
            return ListUtils.newArrayList();
        }
        SupplyDeliveryReq clone = req.cloneReqForSupply();
        List<SupplyDeliveryVO> supplyDeliveryVOList = getSupplyDeliveryList(clone, SupplyDeliveryIndexEnum.supply_match_move.getName());
        return ListUtils.transform(supplyDeliveryVOList, SupplyDeliveryIndexItem::transform);
    }


    private List<SupplyDeliveryVO> getSupplyDeliveryList(SupplyDeliveryReq req, String index) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, SupplyDeliveryDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();
        whereContent.addAnd("index in (?) ", index);
        String sql = ORMUtils.getSql("/sql/industry_cockpit/v4/2_supply_delivery_stat_time.sql");

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());
        sql = SimpleSqlBuilder.doReplace(sql, "timeDim", req.getTimeDim());
        sql = SimpleSqlBuilder.doReplace(sql, "orderDim", req.getOrderDim());
        sql = SimpleSqlBuilder.doReplace(sql, "excel", SoeCommonUtils.toStringBo(req.isExcel()));
        List<String> fieldNames = Arrays.stream(SupplyDeliveryDfDO.class.getDeclaredFields()).map(item -> item.getName()).collect(Collectors.toList());
        sql = SimpleSqlBuilder.buildDims(sql, new HashSet<>(fieldNames), req.getDims());

        return ckcldStdCrpDBHelper.getRaw(SupplyDeliveryVO.class, sql, whereContent.getParams());
    }

    private List<String> getOrderNumberList(SupplyDeliveryReq req) {
        SopWhereBuilder sopWhereBuilder = new SopWhereBuilder(req, SupplyDeliveryDfDO.class);
        ORMUtils.WhereContent whereContent = sopWhereBuilder.where();
        whereContent.addAnd("index in (?) ", "需求@" + SupplyDeliveryIndexEnum.order_demand.getName());
        String sql = "select distinct order_number from std_crp.dws_supply_delivery_df ${where} ";

        sql = SimpleSqlBuilder.doReplace(sql, "where", whereContent.getSql());

        return ckcldStdCrpDBHelper.getRaw(String.class, sql, whereContent.getParams());
    }

}
