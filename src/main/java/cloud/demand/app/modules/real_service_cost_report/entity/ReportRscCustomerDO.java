package cloud.demand.app.modules.real_service_cost_report.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("real_service_cost_report_industry_route_prod_type_customer")
public class ReportRscCustomerDO extends EvaluationIndex{

    /** 月份<br/>Column: [data_month] */
    @Column(value = "data_month")
    @ExcelProperty(value = "年月")
    private String yearMonth;

    /** 行业/通路名称<br/>Column: [industry_route_name] */
    @Column(value = "industry_route_name")
    @ExcelProperty(value = "行业/通路名称")
    private String industryDept;

    /** 客户简称-CRP<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    @ExcelProperty(value = "客户简称")
    private String customerShortName;

}