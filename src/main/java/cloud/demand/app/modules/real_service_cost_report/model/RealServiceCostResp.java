package cloud.demand.app.modules.real_service_cost_report.model;

import cloud.demand.app.modules.real_service_cost_report.entity.EvaluationIndex;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscCustomerDO;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscIndustryDO;
import cloud.demand.app.modules.real_service_cost_report.entity.ReportRscMonthDO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Objects;

@Data
public class RealServiceCostResp {
    @ExcelProperty(value = "年月")
    private String yearMonth;
    @ExcelProperty(value = "行业/通路名称")
    private String industryDept;
    @ExcelProperty(value = "客户简称")
    private String customerShortName;
    @ExcelProperty(value = "机型类别")
    private String staticsCategory;
    @ExcelProperty(value = "B/A")
    private Double BA;
    @ExcelProperty(value = "C/A")
    private Double CA;
    @ExcelProperty(value = "D/A")
    private Double DA;
    @ExcelProperty(value = "E1/A")
    private Double e1A;
    @ExcelProperty(value = "E2/A")
    private Double e2A;
    @ExcelProperty(value = "E/A")
    private Double EA;
    @ExcelProperty(value = "(B+C+D+E)/A")
    private Double bcdeA;
    @ExcelProperty(value = "订单如期退还核数（月累计）")
    private Double scheculerReturnCpu;
    @ExcelProperty(value = "订单提前退还核数（月累计）")
    private Double preReturnCpu;
    @ExcelProperty(value = "计费月均核（A）-包年包月")
    private Double prepayBillA;
    @ExcelProperty(value = "计费月均核（A）-按量计费")
    private Double postpayBillA;
    @ExcelProperty(value = "计费月均核（A）-全量")
    private Double totalBillA;

    @ExcelIgnore
    private BigDecimal bMonthlyUsage;

    @ExcelIgnore
    private BigDecimal dMonthlyUsage;

    @ExcelProperty(value = "B（未履约闲置核心数）")
    private BigDecimal b_MonthlyUsage;

    @ExcelProperty(value = "D（订单如期退回再周转核心数）")
    private BigDecimal d_MonthlyUsage;

    @ExcelProperty(value = "'E1（有报备的订单提前退回再周转核心数）")
    private BigDecimal e1MonthlyUsage;

    @ExcelProperty(value = "E2（无报备的订单提前退回再周转核心数）")
    private BigDecimal e2MonthlyUsage;


    @ExcelIgnore
    private BigDecimal eMonthlyUsage;

    @ExcelProperty(value = "E（订单提前退回再周转核心数）")
    private BigDecimal e_monthlyUsage;

    @ExcelProperty(value = "准确率激励金额")
    private BigDecimal incentiveCost;

    @ExcelProperty(value = "预测准确率-新增原始核天")
    private BigDecimal coreByDayMatchRate;
    @ExcelProperty(value = "最新版退回预测核心数")
    private Integer returnPredictedCoreNumber;
    @ExcelProperty(value = "上个月_年月")
    private String preYearMonth;
    @ExcelProperty(value = "上个月_B/A")
    private Double preBA;
    @ExcelProperty(value = "上个月_C/A")
    private Double preCA;
    @ExcelProperty(value = "上个月_D/A")
    private Double preDA;
    @ExcelProperty(value = "上个月_E1/A")
    private Double preE1A;
    @ExcelProperty(value = "上个月_E2/A")
    private Double preE2A;
    @ExcelProperty(value = "上个月_E/A")
    private Double preEA;
    @ExcelProperty(value = "上个月_(B+C+D+A)/A")
    private Double preBcdeA;
    @ExcelProperty(value = "上个月_订单如期退还核数（月累计）")
    private Double preScheculerReturnCpu;
    @ExcelProperty(value = "上个月_订单提前退还核数（月累计）")
    private Double prePreReturnCpu;
    @ExcelProperty(value = "上个月_计费月均核（A）-包年包月")
    private Double prePrepayBillA;
    @ExcelProperty(value = "上个月_计费月均核（A）-按量计费")
    private Double prePostpayBillA;
    @ExcelProperty(value = "上个月_计费月均核（A）-全量")
    private Double preTotalBillA;
    @ExcelProperty(value = "上个月_预测准确率-新增原始核天")
    private BigDecimal preCoreByDayMatchRate;
    @ExcelProperty(value = "上个月_最新版退回预测核心数")
    private Integer preReturnPredictedCoreNumber;


    @ExcelProperty(value = "上月_B（未履约闲置核心数）")
    private BigDecimal preBMonthlyUsage;


    @ExcelProperty(value = "上月_D（订单如期退回再周转核心数）")
    private BigDecimal preDMonthlyUsage;


    @ExcelProperty(value = "上月_E1（有报备的订单提前退回再周转核心数）")
    private BigDecimal preE1MonthlyUsage;


    @ExcelProperty(value = "上月_E2（无报备的订单提前退回再周转核心数）")
    private BigDecimal preE2MonthlyUsage;


    @ExcelProperty(value = "上月_E（订单提前退回再周转核心数）")
    private BigDecimal preEMonthlyUsage;

    @ExcelProperty(value = "上月_准确率激励金额")
    private BigDecimal preIncentiveCost;

    public static RealServiceCostResp transform(ReportRscCustomerDO customerDO) {

        RealServiceCostResp resp = transform((EvaluationIndex) customerDO);
        resp.setYearMonth(customerDO.getYearMonth());
        resp.setIndustryDept(customerDO.getIndustryDept());
        resp.setCustomerShortName(customerDO.getCustomerShortName());
        return resp;
    }

    public static RealServiceCostResp transform(ReportRscIndustryDO industryDO) {

        RealServiceCostResp resp = transform((EvaluationIndex) industryDO);
        resp.setYearMonth(industryDO.getYearMonth());
        resp.setIndustryDept(industryDO.getIndustryDept());

        return resp;
    }

    public static RealServiceCostResp transform(ReportRscMonthDO monthDO) {

        RealServiceCostResp resp = transform((EvaluationIndex) monthDO);
        resp.setYearMonth(monthDO.getYearMonth());
        return resp;
    }

    @NotNull
    public static RealServiceCostResp transform(EvaluationIndex index) {

        RealServiceCostResp resp = new RealServiceCostResp();
        resp.setStaticsCategory(index.getStaticsCategory());
        resp.setBA(index.getBA());
        resp.setCA(index.getCA());
        resp.setDA(index.getDA());
        resp.setE1A(index.getE1A());
        resp.setE2A(index.getE2A());
        resp.setEA(index.getEA());
        resp.setBcdeA(index.getBcdeA());
        resp.setScheculerReturnCpu(index.getScheculerReturnCpu());
        resp.setPreReturnCpu(index.getPreReturnCpu());
        resp.setPrepayBillA(index.getPrepayBillA());
        resp.setPostpayBillA(index.getPostpayBillA());
        resp.setTotalBillA(index.getTotalBillA());
        resp.setBMonthlyUsage(index.getBMonthlyUsage());
        resp.setB_MonthlyUsage(index.getBMonthlyUsage());
        resp.setDMonthlyUsage(index.getDMonthlyUsage());
        resp.setD_MonthlyUsage(index.getDMonthlyUsage());
        resp.setE1MonthlyUsage(index.getE1MonthlyUsage());
        resp.setE2MonthlyUsage(index.getE2MonthlyUsage());
        resp.setEMonthlyUsage(index.getEMonthlyUsage());
        resp.setE_monthlyUsage(index.getEMonthlyUsage());
        if(Objects.nonNull(index.getIncentiveCost())){
            resp.setIncentiveCost(new BigDecimal(Math.abs(index.getIncentiveCost().intValue())));
        }
        resp.setCoreByDayMatchRate(index.getCoreByDayMatchRate());
        resp.setReturnPredictedCoreNumber(index.getReturnPredictedCoreNumber());
        resp.setPreYearMonth(index.getPreYearMonth());
        resp.setPreBA(index.getPreBA());
        resp.setPreCA(index.getPreCA());
        resp.setPreDA(index.getPreDA());
        resp.setPreE1A(index.getPreE1A());
        resp.setPreE2A(index.getPreE2A());
        resp.setPreEA(index.getPreEA());
        resp.setPreBcdeA(index.getPreBcdeA());
        resp.setPreScheculerReturnCpu(index.getPreScheculerReturnCpu());
        resp.setPrePreReturnCpu(index.getPrePreReturnCpu());
        resp.setPrePrepayBillA(index.getPrePrepayBillA());
        resp.setPrePostpayBillA(index.getPrePostpayBillA());
        resp.setPreTotalBillA(index.getPreTotalBillA());
        resp.setPreCoreByDayMatchRate(index.getPreCoreByDayMatchRate());
        resp.setPreReturnPredictedCoreNumber(index.getPreReturnPredictedCoreNumber());
        resp.setPreBMonthlyUsage(index.getPreBMonthlyUsage());
        resp.setPreDMonthlyUsage(index.getPreDMonthlyUsage());
        resp.setPreE1MonthlyUsage(index.getPreE1MonthlyUsage());
        resp.setPreE2MonthlyUsage(index.getPreE2MonthlyUsage());
        resp.setPreEMonthlyUsage(index.getPreEMonthlyUsage());
        if(Objects.nonNull(index.getPreIncentiveCost())){
            resp.setPreIncentiveCost(new BigDecimal(Math.abs(index.getPreIncentiveCost().intValue())));
        }
        return resp;
    }
}
