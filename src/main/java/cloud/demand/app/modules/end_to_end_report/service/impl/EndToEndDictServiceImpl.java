package cloud.demand.app.modules.end_to_end_report.service.impl;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.impl.DictServiceImpl;
import cloud.demand.app.modules.end_to_end_report.service.EndToEndDictService;
//import cloud.demand.app.modules.p2p.ppl13week.entity.YunxiaoInstanceConfigDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class EndToEndDictServiceImpl implements EndToEndDictService {

    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper yuntiDBHelper;

    @Override
    @HiSpeedCache(expireSecond = 600)
    public Map<String, List<String>> queryInstanceType2DeviceTypeMap() {
        //  用云霄的中英文名称切换
//        List<YunxiaoInstanceConfigDO> all = demandDBHelper.getAll(YunxiaoInstanceConfigDO.class);
//        Map<String, List<String>> yunxiaoMap = ListUtils.toMapList(all, o -> o.getInstanceType(), o -> o.getInstanceTypeName());

        //  用中文名去表里匹配该实例类型下的设备类型列表
//        List<CloudDemandCsigDeviceExtendInfoDO> resourceConfig
//                = yuntiDBHelper.getAll(CloudDemandCsigDeviceExtendInfoDO.class);
//        Map<String, List<String>> instance2DeviceTypeMap =
//                ListUtils.toMapList(resourceConfig, o -> o.getInstanceType(), o -> o.getDeviceType());
        DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
        Map<String, List<String>> instance2DeviceTypeMap = dictService.getCsigInstanceTypeToDeviceTypeMap();
        return instance2DeviceTypeMap;
//        Map<String, List<String>> resultMap = new HashMap<>();
//
//        for (Map.Entry<String, List<String>> entry : yunxiaoMap.entrySet()) {
//            String instanceType = entry.getKey();
//            Set<String> deviceTypeSet = Lang.set();
//            for (String instanceTypeName : entry.getValue()) {
//                List<String> deviceTypeList = instance2DeviceTypeMap.get(instanceTypeName);
//                if (ListUtils.isNotEmpty(deviceTypeList)){
//                    deviceTypeSet.addAll(deviceTypeList);
//                }
//            }
//            resultMap.put(instanceType, ListUtils.toList(deviceTypeSet));
//        }
//
//        return resultMap;
    }
}
