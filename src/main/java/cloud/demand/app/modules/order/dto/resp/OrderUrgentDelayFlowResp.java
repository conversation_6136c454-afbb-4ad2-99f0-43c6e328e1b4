package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import com.pugwoo.wooutils.json.JSON;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import yunti.boot.exception.BizException;

@Data
public class OrderUrgentDelayFlowResp extends OrderInSubFlowBase {

    // 变更原因
    private String reason;

    // 修改前-开始购买日期
    private LocalDate beginBuyDateNormal;

    // 修改前-结束购买日期
    private LocalDate endBuyDateNormal;

    // 修改后-开始购买日期
    private LocalDate beginBuyDateModified;

    // 修改后-结束购买日期
    private LocalDate endBuyDateModified;

    // 加急延期天数，加急为负数，延期为正数，修改后开始购买日期 - 修改前的开始购买日期
    private int urgentDelayDays;

    private OrderUrgentDelayReq req;

    public OrderUrgentDelayFlowResp(OrderInSubFlowBase flowOrder) {
        BeanUtils.copyProperties(flowOrder, this);
        valueSetAndCheck();
    }

    private void valueSetAndCheck() {
        if (this.getFlowData() == null || this.getFlowData().getFlowInfo() == null) {
            throw BizException.makeThrow("获取子流程信息为空");
        }
        FlowInfoDO flow = this.getFlowData().getFlowInfo();
        if (!OrderFlowEnum.ORDER_URGENT_DELAY.getCode().equals(flow.getFlowCode())) {
            throw BizException.makeThrow("查询到的流程信息非加急延期子流程，流程流水号【%s】", flow.getFlowNo());
        }

        if (this.getOriginalOrder() == null) {
            throw BizException.makeThrow("获取指定加急延期子流程【%s】中原始订单信息为空", flow.getFlowNo());
        }
        if (this.getNewestOrder() == null) {
            throw BizException.makeThrow("获取指定加急延期子流程【%s】中最新订单信息为空", flow.getFlowNo());
        }

        this.reason = flow.getFlowRemark();

        this.beginBuyDateNormal = this.getOriginalOrder().getBeginBuyDate();
        this.endBuyDateNormal = this.getOriginalOrder().getEndBuyDate();

        this.beginBuyDateModified = this.getNewestOrder().getBeginBuyDate();
        this.endBuyDateModified = this.getNewestOrder().getEndBuyDate();

        if (this.beginBuyDateNormal != null && this.beginBuyDateModified != null) {
            long value = this.beginBuyDateNormal.until(this.beginBuyDateModified, ChronoUnit.DAYS);
            this.urgentDelayDays = (int) value;
        }

        this.req = JSON.parse(flow.getExtFields1(), OrderUrgentDelayReq.class);
    }

}
