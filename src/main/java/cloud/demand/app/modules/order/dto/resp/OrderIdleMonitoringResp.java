package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.entity.OrderInfoDO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/9/23 17:21
 */
@Data
public class OrderIdleMonitoringResp {

    /**
     * 订单关闭时间
     */
    private LocalDate closeDate;

    /**
     * 订单结束购买时间
     */
    private LocalDate endBuyDate;

    /**
     * 已闲置天数
     */
    private long idleDays;

    /**
     * 履约率
     */
    private BigDecimal buyRate;

    /**
     * 闲置核心数
     */
    private Integer idleCores;

    /**
     * 最晚订单关闭时间
     */
    private LocalDate  latestOrderClosingDate;


    private List<OrderIdleMonitoringItemVO> currentItems;

    private List<OrderIdleMonitoringItemVO> historyItems;

    public static OrderIdleMonitoringResp builder(List<OrderIdleMonitoringItemVO> raw, OrderInfoDO orderInfoDO, BigDecimal buyRate) {
        OrderIdleMonitoringResp resp = new OrderIdleMonitoringResp();
        resp.setCloseDate(orderInfoDO.getCloseDate());
        resp.setEndBuyDate(orderInfoDO.getEndBuyDate());

        resp.setBuyRate(buyRate);

        LocalDate maxDate = raw.get(raw.size() - 1).getStatTime();
        List<OrderIdleMonitoringItemVO> currentItems = raw.stream().filter(item -> item.getStatTime().equals(maxDate)).collect(Collectors.toList());
        resp.setCurrentItems(currentItems);

        Integer idleCores = currentItems.stream().mapToInt(OrderIdleMonitoringItemVO::getBuyOrMoveIdleCore).sum();
        resp.setIdleCores(idleCores);

        LocalDate endDate = Objects.isNull(resp.getCloseDate()) ? LocalDate.now() : resp.getCloseDate();
        resp.setIdleDays(Math.max(ChronoUnit.DAYS.between(orderInfoDO.getEndBuyDate(), endDate) - 14, 0));
        resp.setLatestOrderClosingDate(orderInfoDO.getLatestOrderClosingTime());

        resp.setHistoryItems(raw);

        return resp;
    }

}
