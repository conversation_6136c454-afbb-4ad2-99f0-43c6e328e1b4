package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import io.vavr.Tuple2;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("deliver_quota_record_item")
public class DeliverQuotaRecordItemDO extends BaseDO {

    /**
     * 下发记录id<br/>Column: [deliver_id]
     */
    @Column(value = "deliver_id")
    private Long deliverId;

    /**
     * 需求年<br/>Column: [demand_year]
     */
    @Column(value = "demand_year")
    private Integer demandYear;

    /**
     * 需求月<br/>Column: [demand_month]
     */
    @Column(value = "demand_month")
    private Integer demandMonth;

    /**
     * 需求年月<br/>Column: [demand_year_month]
     */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /**
     * uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    @Column(value = "app_id")
    private String appId;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "customer_name")
    private String customerName;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 计费类型<br/>Column: [bill_type]
     */
    @Column(value = "bill_type")
    private String billType;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 配额类型<br/>Column: [quota_type]
     */
    @Column(value = "quota_type")
    private String quotaType;

    /**
     * 需求总核心数<br/>Column: [total_core]
     */
    @Column(value = "total_core")
    private Integer totalCore;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @Column(value = "instance_num")
    private Integer instanceNum;

    /**
     * 采购满足核心数<br/>Column: [buy_satisfy_core]
     */
    @Column(value = "buy_satisfy_core")
    private Integer buySatisfyCore;

    /**
     * 库存满足核心数<br/>Column: [stock_satisfy_core]
     */
    @Column(value = "stock_satisfy_core")
    private Integer stockSatisfyCore;

    /**
     * 配额管控枚举： 核心管控：规格管控<br/>Column: [quota_manage_type]
     */
    @Column(value = "quota_manage_type")
    private String quotaManageType;

    /**
     * 可购买实例数额度<br/>Column: [instance_num_quota]
     */
    @Column(value = "instance_num_quota")
    private Integer instanceNumQuota;

    /**
     * 可购买核心数额度<br/>Column: [core_quota]
     */
    @Column(value = "core_quota")
    private Integer coreQuota;

    @Column(value = "deliver_quota_num")
    private Integer deliverQuotaNum;

    /**
     * 订单id列表 ;分割<br/>Column: [order_number_ids]
     */
    @Column(value = "order_number_ids")
    private String orderNumberIds;

    /**
     * 数据来源<br/>Column: [source]
     */
    @Column(value = "source")
    private String source;


    public Integer getQuotaNum() {
        // 规格管控取实例数配额 反之取核心数配额
        return this.getQuotaManageType().equals("规格管控") ?
                this.getInstanceNumQuota() : this.getCoreQuota();

    }

    public String getBillTypeName() {
        return YunxiaoPayModeEnum.getNameByCode(this.getBillType());
    }


    public Integer getCoreToQuotaNum(){
        if (this.getQuotaManageType().equals("规格管控")){
            Tuple2<Integer, Integer> tuple = P2PInstanceModelParse.parseInstanceModel(this.getQuotaType());
            return this.getInstanceNumQuota() * tuple._1;
        }else {
            return this.getCoreQuota();
        }
    }
    
}