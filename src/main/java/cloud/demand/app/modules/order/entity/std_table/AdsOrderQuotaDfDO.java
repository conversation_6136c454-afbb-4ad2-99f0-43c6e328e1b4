package cloud.demand.app.modules.order.entity.std_table;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ads_order_quota_df")
public class AdsOrderQuotaDfDO {

    /**
     * 统计日期<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /**
     * 插入时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * appId<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 需求年月<br/>Column: [demand_year_month]
     */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /**
     * 需求年<br/>Column: [demand_year]
     */
    @Column(value = "demand_year")
    private Integer demandYear;

    /**
     * 需求月<br/>Column: [demand_month]
     */
    @Column(value = "demand_month")
    private Integer demandMonth;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @Column(value = "instance_type")
    private String instanceType;

    /**
     * 计费模式<br/>Column: [pay_mode]
     */
    @Column(value = "pay_mode")
    private String payMode;

    /**
     * 配额Id<br/>Column: [quota_id]
     */
    @Column(value = "quota_id")
    private String quotaId;

    /**
     * 配额Key<br/>Column: [quota_key]
     */
    @Column(value = "quota_key")
    private String quotaKey;

    /**
     * 配额单位<br/>Column: [quota_unit]
     */
    @Column(value = "quota_unit")
    private String quotaUnit;

    /**
     * 已使用配额<br/>Column: [quota_usage]
     */
    @Column(value = "quota_usage")
    private Integer quotaUsage;

    /**
     * 云霄月初配额数<br/>Column: [yunxiao_month_beginning_quota_num]
     */
    @Column(value = "yunxiao_month_beginning_quota_num")
    private Integer yunxiaoMonthBeginningQuotaNum;

    /**
     * 云霄当天配额数<br/>Column: [yunxiao_current_quota_num]
     */
    @Column(value = "yunxiao_current_quota_num")
    private Integer yunxiaoCurrentQuotaNum;

    /**
     * crp建议配额数<br/>Column: [crp_suggest_quota_num]
     */
    @Column(value = "crp_suggest_quota_num")
    private Integer crpSuggestQuotaNum;

    /**
     * crp已下发配额数<br/>Column: [crp_deliver_quota_num]
     */
    @Column(value = "crp_deliver_quota_num")
    private Integer crpDeliverQuotaNum;

    /**
     * 是否采购机型<br/>Column: [is_purchase_instance_type]
     */
    @Column(value = "is_purchase_instance_type")
    private Boolean isPurchaseInstanceType;


    /**
     * 核心转换: 云霄已使用配额<br/>Column: [core_to_quota_usage]
     */
    @Column(value = "core_to_quota_usage")
    private Integer coreToQuotaUsage;

    /**
     * 核心转换: 云霄月初配额<br/>Column: [core_to_yunxiao_month_beginning_quota_num]
     */
    @Column(value = "core_to_yunxiao_month_beginning_quota_num")
    private Integer coreToYunxiaoMonthBeginningQuotaNum;

    /**
     * 核心转换: 云霄当前配额<br/>Column: [core_to_yunxiao_current_quota_num]
     */
    @Column(value = "core_to_yunxiao_current_quota_num")
    private Integer coreToYunxiaoCurrentQuotaNum;

    /**
     * 核心转换: CRP建议当前配额<br/>Column: [core_to_crp_suggest_quota_num]
     */
    @Column(value = "core_to_crp_suggest_quota_num")
    private Integer coreToCrpSuggestQuotaNum;

    /**
     * 核心转换: CRP下发配额<br/>Column: [core_to_crp_deliver_quota_num]
     */
    @Column(value = "core_to_crp_deliver_quota_num")
    private Integer coreToCrpDeliverQuotaNum;


}