package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class OrderInfoWithDetailDTO extends OrderInfoDO {

    private List<OrderItemDO> itemList;

    public OrderInfoWithDetailDTO(){
    }

    public OrderInfoWithDetailDTO(OrderInfoDO order, List<OrderItemDO> itemList) {
        BeanUtils.copyProperties(order, this);
        this.itemList = itemList;
    }

}
