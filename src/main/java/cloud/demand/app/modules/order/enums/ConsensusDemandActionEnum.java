package cloud.demand.app.modules.order.enums;


import lombok.Getter;

@Getter
public enum ConsensusDemandActionEnum {

    CREATE_PLAN_WITHOUT_CONSENSUS("CREATE_PLAN_WITHOUT_CONSENSUS", "创建方案-免共识"),

    CREATE_PLAN_WITH_CONSENSUS("CREATE_PLAN_WITH_CONSENSUS", "创建方案-需共识"),

    MODIFY_PLAN_WITHOUT_CONSENSUS("MODIFY_PLAN_WITHOUT_CONSENSUS", "修改方案-免共识"),

    MODIFY_PLAN_WITH_CONSENSUS("MODIFY_PLAN_WITH_CONSENSUS", "修改方案-需共识"),

    PRE_DEDUCT("PRE_DEDUCT", "产品预扣");

    private String code;

    private String name;

    ConsensusDemandActionEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

}
