package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.constant.FlowStatusEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;
import cloud.demand.app.modules.flow.req.FlowStartReq;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.convert.NoticeParamsUtil;
import cloud.demand.app.modules.order.dto.req.GlobalApprovalReq;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.OrderFlowStartReq;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithFlowDTO;
import cloud.demand.app.modules.order.dto.resp.SubProcessResp;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderNumberPrefixEnum;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSubFlowNoPrefixEnum;
import cloud.demand.app.modules.order.enums.PplProductToOrderProductMapEnum;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderFlowService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryProcessService;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Service(value = "orderOperateServiceForDatabase")
public class OrderOperateServiceForDataBase extends AbstractOrderOperateService {

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private FlowService flowService;

    @Resource
    private DictService dictService;

    @Resource
    private PplIndustryProcessService industryProcessService;

    @Resource(name = "supplyPlanOperateServiceForDataBase")
    private SupplyPlanOperateService supplyPlanOperateService;

    @Resource(name = "orderOperateServiceForDatabase")
    private OrderOperateService self;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Resource
    private PermissionService permissionService;

    @Override
    public boolean support(String product, String appRole, String orderCategory) {
        return PplProductToOrderProductMapEnum.DATABASE.getOrderCategory().equals(orderCategory);
    }

    @Override
    @Transactional("demandTransactionManager")
    public OrderInfoWithDetailDTO saveNewOrderDraft(OrderSaveReq req) {
        // 检查是否存在订单号，不存在表示新增草稿
        if (Strings.isNotBlank(req.getOrderNumber())) {
            throw BizException.makeThrow(
                    "操作失败，保存新订单草稿要求订单号为空，新订单草稿订单号将由程序按规则自动生成");
        }
        req.paramsCheckDatabase();

        String user = LoginUtils.getUserNameWithSystem();
        String orderNumber = orderCommonService.generateOrderNumber(
                OrderSourceEnum.PPL_TRANSFORM.getCode().equals(req.getOrderSource()) ? OrderNumberPrefixEnum.ON
                        : OrderNumberPrefixEnum.OE);

        // 启动订单主流程
        FlowStartReq startReq = new FlowStartReq();
        startReq.setBizId(orderNumber);
        startReq.setFlowInitiator(user);
        startReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        startReq.setFlowNo(orderNumber);
        startReq.setFlowRemark("首次保存草稿，订单主流程开启");
        FlowNodeRecordWithFlowInfoDTO flowData = orderFlowService.startMainFlow(startReq);

        // 完成了保存草稿操作，推进主流程到需求提出节点
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(null);
        valueSetReq.setOperateEvent("用户保存草稿");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(flowData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        // 保存草稿，设置返回值 1 表示从开始节点到需求提交节点
        valueSetReq.setNodeReturn(1);
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setOrderCreator((orderNormal) -> {
            // 需要新增的草稿
            OrderInfoDO order = req.toOrderInfoDO();
            order.setSubmitUser(user);
            order.setOrderNumber(orderNumber);
            order.setAvailableStatus(OrderAvailableStatusEnum.AVAILABLE.getCode());
            order.setOrderStatus(OrderStatusEnum.DRAFT.getCode());
            return order;
        });
        valueSetReq.setOrderItemsCreator((normal, newOrder) -> {
            // 需要新增的草稿明细
            List<OrderItemDO> itemList = req.toOrderItemDOList();
            for (OrderItemDO item : itemList) {
                // 使用订单明细对应的订单主体信息数据
                item.setOrderInfoId(newOrder.getId());
                item.setOrderNumber(newOrder.getOrderNumber());
                // 保存草稿时 肯定不预扣
                item.setIsPreDeduct(null);
                // 订单明细号生成新的
                String newOrderNumberId = orderCommonService.generateOrderItemNumber(newOrder.getOrderNumber());
                item.setOrderNumberId(newOrderNumberId);
            }
            return itemList;
        });
        OrderInfoWithFlowDTO result = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
        return result;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public OrderInfoWithDetailDTO modifyOrderDraft(OrderSaveReq req) {
        // 检查是否存在订单号，存在订单号表示修改草稿
        if (Strings.isBlank(req.getOrderNumber())) {
            throw BizException.makeThrow("操作失败，修改订单草稿时订单号不能为空");
        }
        req.paramsCheckDatabase();

        // 查询数据库中此订单号是否存在，不存在则无法操作
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, req.getOrderNumber())
                .andEqual(OrderInfoDO::getOrderStatus, OrderStatusEnum.DRAFT.getCode())
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderInfoDO normalOrder = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
        if (normalOrder == null) {
            throw BizException.makeThrow("操作失败，未能查询到订单草稿数据，无法修改，订单号：【%s】", req.getOrderNumber());
        }
        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(req.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || !OrderNodeCodeEnum.node_order_propose.getCode().equals(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，仅需求提出节点时可以修改草稿，订单号：【%s】", req.getOrderNumber());
        }

        // 完成了修改操作，将主流程推进（修改草稿操作这里实际上是节点自旋，推进流程实际上还是在需求提交节点，因为在提交需求之前可以多次修改草稿）
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(normalOrder);
        valueSetReq.setOperateEvent("用户修改草稿");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(normalOrder.getOrderNumber());
        // 修改草稿，设置返回值 999 表示在需求提交节点自旋一次
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        String user = LoginUtils.getUserNameWithSystem();
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setOperateName("修改草稿");
        // 这里可以生成修改的概览信息
        valueSetReq.setOperateRemark(user + "修改了订单草稿");
        valueSetReq.setOrderCreator((orderNormal) -> {
            // 修改草稿实际上是新增一条订单草稿记录，并将原草稿设置为失效状态，新草稿设置为生效状态
            OrderInfoDO order = req.toOrderInfoDO();
            order.checkForbidModifyField(orderNormal);
            order.setSubmitUser(normalOrder.getSubmitUser());
            order.setLevelName(normalOrder.getLevelName());
            order.setAvailableStatus(OrderAvailableStatusEnum.AVAILABLE.getCode());
            order.setOrderStatus(OrderStatusEnum.DRAFT.getCode());
            return order;
        });
        valueSetReq.setOrderItemsCreator((normal, newOrder) -> {
            // 草稿明细
            List<OrderItemDO> itemList = req.toOrderItemDOList();
            for (OrderItemDO item : itemList) {
                // 使用订单明细对应的订单主体信息数据
                item.setOrderInfoId(newOrder.getId());
                item.setOrderNumber(newOrder.getOrderNumber());
                if (Strings.isBlank(item.getOrderNumberId())) {
                    // 不存在订单明细号时，生成新的
                    String newOrderNumberId = orderCommonService.generateOrderItemNumber(newOrder.getOrderNumber());
                    item.setOrderNumberId(newOrderNumberId);
                }
            }
            return itemList;
        });
        OrderInfoWithFlowDTO result = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
        return result;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public OrderInfoWithDetailDTO submitDraft(String orderNumber, String operateName) {
        // 查询数据库中此订单号是否存在，不存在则无法操作
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                .andEqual(OrderInfoDO::getOrderStatus, OrderStatusEnum.DRAFT.getCode())
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderInfoDO order = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
        if (order == null) {
            throw BizException.makeThrow("操作失败，未能查询到订单草稿数据，无法提交，订单号：【%s】", orderNumber);
        }

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || !OrderNodeCodeEnum.node_order_propose.getCode().equals(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，仅需求提出节点时可以提交草稿，订单号：【%s】", orderNumber);
        }

        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("用户提交草稿");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(order.getOrderNumber());
        // 提交草稿。设置返回值 5 推进流程到满足方式评估节点
        valueSetReq.setNodeReturn(5);
        valueSetReq.setOperateName(operateName);
        valueSetReq.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = order.copyNewToInsert();
            // 新生成的订单状态为进行中
            newOrder.setOrderStatus(OrderStatusEnum.PROCESS.getCode());
            Date date = new Date();
            newOrder.setSubmitTime(date);
            newOrder.setLastUpdateTime(date);
            // 提交订单之后，对应的共识前原始订单id就要设置为空
            // 共识前原始订单id 。
            // 例原始订单A：共识第一次变为B，则共识前原始订单id为A；
            // 然后用户修改订单为B，则共识前原始订单id为B；
            // 然后共识第二次，则共识前原始订单id为B 。
            newOrder.setBeforeConsensusNormalId(null);
            // 提交订单时，将驳回日志id清空
            newOrder.setRefuseLogId(null);
            return newOrder;
        });
        String user = LoginUtils.getUserNameWithSystem();
        valueSetReq.setOperateUser(user);
        OrderInfoWithFlowDTO orderInfoWithFlowDTO = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 发送订单提交消息通知
        String noticeUser = orderInfoWithFlowDTO.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(orderInfoWithFlowDTO);
        dictService.eventNotice(CrpEventEnum.order_submit_database.getCode(), null, null, noticeTemplate, noticeUser);

        // 供应方案待制定消息通知 （for 新订单）
        dictService.eventNotice(CrpEventEnum.order_supply_plan_wait_create_new_order_database.getCode(),
                null, null, noticeTemplate, orderInfoWithFlowDTO.getCurrentProcessor());

        return orderInfoWithFlowDTO;
    }

    @Override
    @Transactional("demandTransactionManager")
    public OrderInfoWithDetailDTO saveAndSubmitDraft(OrderSaveReq req) {
        OrderInfoDO order;
        if (Strings.isBlank(req.getOrderNumber())) {
            order = self.saveNewOrderDraft(req);
        } else {
            order = self.modifyOrderDraft(req);
        }
        return self.submitDraft(order.getOrderNumber(), null);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 3000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void pushToFollowing(String orderNumber) {
        // 获取主流程生效的订单信息
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            return;
        }
        // 获取当前主流程节点信息
        FlowNodeRecordWithFlowInfoDTO flowData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowData == null) {
            return;
        }
        // 仅交付供应节点 且 到达了开始购买日期 可以 推进至 履约跟踪
        if (LocalDate.now().isBefore(order.getBeginBuyDate())
                || !OrderNodeCodeEnum.node_order_supply.getCode().equals(flowData.getNodeCode())) {
            return;
        }
        // 存在进行中的子流程，则不能推进至 履约跟踪
        FlowNodeRecordWithFlowInfoDTO subFlow = flowService.queryProcessingSubFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (subFlow != null && subFlow.getFlowInfo() != null) {
            return;
        }
        // 推进至 履约跟踪
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateUser("system");
        valueSetReq.setOperateEvent("到达开始购买日期");
        valueSetReq.setBizId(orderNumber);
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        valueSetReq.setNodeCode(flowData.getNodeCode());
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        OrderInfoWithFlowDTO flowDTO = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 发送订单进入履约跟踪通知
        String noticeUser = flowDTO.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(flowDTO);
        dictService.eventNotice(CrpEventEnum.order_into_following.getCode(), null, null, noticeTemplate, noticeUser);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 3000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public Optional<String> pushToOrderClose(String orderNumber) {
        // 获取主流程生效的订单信息
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            return Optional.of(StrUtil.format("【{}】未查询到生效的订单信息", orderNumber));
        }
        // 获取当前主流程节点信息
        FlowNodeRecordWithFlowInfoDTO flowData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowData == null) {
            return Optional.of(StrUtil.format("【{}】未查询到进行中的订单信息", orderNumber));
        }
        // 仅履约跟踪节点 且 到达了结束购买日期+1 可以 推进至 订单关闭
        if (LocalDate.now().plusDays(1).isBefore(order.getEndBuyDate())
                || !OrderNodeCodeEnum.node_order_following.getCode().equals(flowData.getNodeCode())) {
            return Optional.of(StrUtil.format(
                    "【{}】，仅履约跟踪节点 且 到达了结束购买日期+1 可以 推进至 订单关闭，当前节点【{}】，订单结束购买日期【{}】",
                    orderNumber, flowData.getNodeName(), order.getEndBuyDate()));
        }

        String loginUser = LoginUtils.getUserNameWithSystem();
        // 推进至 订单关闭
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateUser(loginUser);
        valueSetReq.setOperateEvent("到达结束购买日期后一天");
        valueSetReq.setBizId(orderNumber);
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        valueSetReq.setNodeCode(flowData.getNodeCode());
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 订单关闭，结束所有子流程
        orderCommonOperateService.stopProcessingSubFlow(orderNumber, "订单关闭，未完结的子流程自动终止", false);

        // 重新获取主流程生效的订单信息，在上面的结束子流程操作中可能会对主流程订单信息进行操作
        OrderDetailResp mainOrder = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());

        // 推进至流程结束
        OrderFlowNodeValueSetReq over = new OrderFlowNodeValueSetReq();
        over.setOrder(mainOrder);
        over.setOperateUser(loginUser);
        over.setOperateEvent("订单结束");
        over.setBizId(orderNumber);
        // 这条操作日志不显示在订单详情主流程中
        over.setNotShowLogInMainFlow(true);
        over.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        over.setNodeCode(mainOrder.getOrderNodeCode());
        over.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        over.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = orderNormal.copyNewToInsert();
            newOrder.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            newOrder.setCloseDate(LocalDate.now());
            newOrder.setCloseUser(loginUser);
            newOrder.setCloseReason("系统自动关闭");
            return newOrder;
        });
        OrderInfoWithFlowDTO flowDTO = orderFlowService.nodeRecordSetReturnValueAndPushFlow(over);

        // 发送订单关闭通知
        String noticeUser = flowDTO.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(flowDTO);
        dictService.eventNotice(CrpEventEnum.order_close.getCode(), null, null, noticeTemplate, noticeUser);
        return Optional.empty();
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void withdrawOrder(String orderNumber) {
        // 查询数据库中此订单号是否存在，不存在则无法操作
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                .andEqual(OrderInfoDO::getOrderStatus, OrderStatusEnum.PROCESS.getCode())
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderDetailResp order = demandDBHelper.getOne(OrderDetailResp.class, where.getSql(), where.getParams());
        if (order == null) {
            throw BizException.makeThrow("操作失败，未能查询到订单数据，无法撤回，订单号：【%s】", orderNumber);
        }

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null ||!OrderNodeCodeEnum.node_order_match_way.getCode().equals(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，仅在 满足方式评估 时可以撤回，订单号：【%s】", orderNumber);
        }

        String user = LoginUtils.getUserNameWithSystem();
        // 仅订单提交人/负责人和本行业接口人可撤回
        if (!Objects.equals(order.getSubmitUser(), user) && !Objects.equals(order.getArchitect(), user)
                && !permissionService.checkIsIndustryOperate(user, order.getIndustryDept())) {
            throw BizException.makeThrow("操作失败，仅订单提交人/订单负责人【%s】和本行业接口人可撤回订单，当前用户【%s】，订单号：【%s】",
                    order.getSubmitUser(), user, orderNumber);
        }

        // 撤回关闭对应子流程
        orderCommonOperateService.stopProcessingSubFlow(orderNumber, "用户撤回订单,结束关联子流程", false);

        // 撤回处理 供应方案相关业务表，生效中的供应方案版本设置为过期
        supplyPlanOperateService.availableVersionOverdue(orderNumber);

        // 重新获取主流程生效的订单信息，在上面的结束子流程操作中可能会对主流程订单信息进行操作
        OrderDetailResp mainOrder = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());

        // 推进流程，设置返回值为0，表示驳回
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(mainOrder);
        valueSetReq.setOperateEvent("用户撤回订单");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(mainOrder.getOrderNumber());
        // 跳过可处理人判断，订单提交人可撤回订单
        valueSetReq.setSystem(true);
        // 撤回的返回值 为 2
        valueSetReq.setNodeReturn(2);
        valueSetReq.setOperateUser(user);

        // 设置valueSetReq的OrderCreator属性，用于更新order表中的orderStatus字段
        valueSetReq.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = orderNormal.copyNewToInsert();
            newOrder.setOrderStatus(OrderStatusEnum.DRAFT.getCode());
            newOrder.setLevelName("低优先级");
            return newOrder;
        });

        // 流程推进
        OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // ppl释放转单状态
        industryProcessService.cancelOrderCallBack(orderNumber);

        // 发送订单撤回通知
        String noticeUser = orderFlow.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(orderFlow);
        dictService.eventNotice(CrpEventEnum.order_withdraw.getCode(), null, null, noticeTemplate, noticeUser);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void stopProcessingSubFlow(String orderNumber, String operateEvent) {
        orderCommonOperateService.stopProcessingSubFlow(orderNumber, operateEvent, false);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void startCancelOrderSubFlow(String orderNumber, String flowRemark) {

        SubProcessResp subProcessResp = orderCommonService.queryProcessingSubProcess(orderNumber);
        if (subProcessResp.getFlowInfoDO() != null && subProcessResp.getFlowInfoDO().getFlowCode()
                .equals(OrderFlowEnum.DATABASE_ORDER_CANCEL.getCode())) {
            throw new BizException("当前已存在进行中的取消子流程，无法再次取消!");
        }

        // 先去取消进行中的子流程。
        stopProcessingSubFlow(orderNumber, "用户发起取消流程，取消进行中的子流程");

        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                .andEqual(OrderInfoDO::getOrderStatus, OrderStatusEnum.PROCESS.getCode())
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderInfoDO order = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
        if (order == null) {
            throw BizException.makeThrow("操作失败，未能查询到订单数据，无法取消，订单号：【%s】", orderNumber);
        }

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || (!OrderNodeCodeEnum.node_order_supply.getCode().equals(nodeData.getNodeCode())
                && !OrderNodeCodeEnum.node_order_following.getCode().equals(nodeData.getNodeCode()))) {
            throw BizException.makeThrow("操作失败，仅在 交付供应/履约跟踪 时可以取消，订单号：【%s】", orderNumber);
        }

        String user = LoginUtils.getUserNameWithSystem();
        String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(orderNumber, OrderSubFlowNoPrefixEnum.C);

        // 启动取消订单子流程
        OrderFlowStartReq startReq = new OrderFlowStartReq();
        startReq.setBizId(orderNumber);
        startReq.setFlowInitiator(user);
        startReq.setFlowCode(OrderFlowEnum.DATABASE_ORDER_CANCEL.getCode());
        startReq.setFlowNo(orderSubFlowNo);
        startReq.setFlowRemark(flowRemark);
        startReq.setParentFlowNo(orderNumber);

        startReq.setOrder(order);
        OrderInfoWithFlowDTO orderInfoWithFlowDTO = orderFlowService.startSubFlow(startReq);

        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(orderInfoWithFlowDTO);
        valueSetReq.setOperateEvent("用户发起取消订单");
        valueSetReq.setFlowCode(OrderFlowEnum.DATABASE_ORDER_CANCEL.getCode());
        valueSetReq.setNodeCode(orderInfoWithFlowDTO.getOrderNodeCode());
        valueSetReq.setBizId(startReq.getBizId());
        // 流程推进
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        valueSetReq.setOperateUser(user);

        // 流程推进
        OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 发送取消子流程待审批通知
        String noticeUser = orderFlow.getCurrentProcessor();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(orderFlow);
        dictService.eventNotice(CrpEventEnum.order_cancel_wait.getCode(), null, null, noticeTemplate, noticeUser);
    }

    @Override
    public void cancelOrderDone(String orderNumber, String operateName) {

        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);

        // 取消订单流程走完业务操作， 将主流程推为取消
        // 推进流程 取消的返回值 为 3 取消流程
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("取消订单流程通过，取消订单");
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(order.getOrderNodeCode());
        valueSetReq.setBizId(orderNumber);
        // 取消的返回值 为 3
        valueSetReq.setNodeReturn(3);
        valueSetReq.setOperateUser("system");
        valueSetReq.setNotShowLogInMainFlow(Boolean.TRUE);
        valueSetReq.setSystem(true);

        // 1、流程推进
        final OrderInfoWithFlowDTO cancelFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 2、处理供应方案，生效中的供应方案版本设置为过期
        supplyPlanOperateService.availableVersionOverdue(orderNumber);

        // 4、ppl回调，释放转单状态
        industryProcessService.cancelOrderCallBack(orderNumber);

        //     --------      订单已关闭节点       ------

        // 4、到达订单已关闭节点，再推进一次，更新order表中的orderStatus字段
        OrderDetailResp order2 = orderCommonService.queryOrderDetail(orderNumber);
        OrderFlowNodeValueSetReq valueSetReq2 = new OrderFlowNodeValueSetReq();
        valueSetReq2.setOrder(order2);
        valueSetReq2.setOperateEvent("取消订单");
        valueSetReq2.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq2.setNodeCode(order2.getOrderNodeCode());
        valueSetReq2.setBizId(orderNumber);
        // 流程正常推进
        valueSetReq2.setNodeReturn(FlowNodeDefaultReturnValueEnum.PASS.getCode());
        // 订单取消子流程发起人作为操作人
        valueSetReq2.setOperateUser(cancelFlow.getFlowData().getFlowInfo().getFlowInitiator());
        valueSetReq2.setOperateName(operateName);
        valueSetReq2.setNotShowLogInMainFlow(StringUtils.isNotBlank(operateName) ? Boolean.FALSE : Boolean.TRUE);

        // 设置valueSetReq的OrderCreator属性，用于更新order表中的orderStatus字段
        valueSetReq2.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = order2.copyNewToInsert();
            newOrder.setOrderStatus(OrderStatusEnum.CANCELED.getCode());
            return newOrder;
        });

        // 流程推进
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq2);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void cancelOrderApproval(GlobalApprovalReq req) {
        OrderDetailResp order = orderCommonService.getOrderDetail(req.getOrderNumber(),
                OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());

        String user = LoginUtils.getUserNameWithSystem();
        req.setApprovalUser(user);
        req.setOrder(order);
        req.setOperateEvent("用户审批需求");

        // 通用审批
        OrderInfoWithFlowDTO approvalRes = orderFlowService.approval(req);

        String noticeUser = approvalRes.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(approvalRes);
        if (!req.isApprovalResult()) {
            // 发送订单取消审批驳回通知
            dictService.eventNotice(CrpEventEnum.order_cancel_reject.getCode(), null, null, noticeTemplate, noticeUser);
            return;
        }

        // 检查是否是流程最后一个节点，如果是，看看有没有对应的业务操作
        if (orderFlowService.checkIsLastNode(req.getOrderNumber(), req.getFlowCode())) {
            FlowInfoDO subFlow = demandDBHelper.getOne(FlowInfoDO.class,
                    "where biz_id = ? and flow_code = ? and flow_status = ?",
                    req.getOrderNumber(), req.getFlowCode(), FlowStatusEnum.PROCESSING.getCode());
            self.bizOperate(req.getOrderNumber(), req.getFlowCode(), subFlow);
            // 发送订单成功取消通知
            dictService.eventNotice(CrpEventEnum.order_cancel_pass.getCode(), null, null, noticeTemplate, noticeUser);
        }
    }

    @Override
    public void bizOperate(String orderNumber, String flowCode, FlowInfoDO subFlow) {

        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode().equals(flowCode)
                        ? OrderAvailableStatusEnum.AVAILABLE.getCode()
                        : OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());

        // 如果是主流程直接返回
        if (OrderFlowEnum.ORDER_MAIN.getCode().equals(flowCode)) {
            return;
        }

        // 当前为最后一个节点，先将子流程推进
        OrderFlowNodeValueSetReq subProcessValueSetReq = new OrderFlowNodeValueSetReq();
        subProcessValueSetReq.setOrder(order);
        subProcessValueSetReq.setOperateEvent(OrderFlowEnum.getNameByCode(flowCode) + "-子流程结束");
        subProcessValueSetReq.setFlowCode(flowCode);
        subProcessValueSetReq.setNodeCode(order.getOrderNodeCode());
        subProcessValueSetReq.setBizId(orderNumber);
        // 正常扭转 return为1
        subProcessValueSetReq.setNodeReturn(1);
        subProcessValueSetReq.setOperateUser("system");
        subProcessValueSetReq.setNotShowLogInMainFlow(Boolean.TRUE);

        // 各子流程对应的业务操作。
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(subProcessValueSetReq);
        if (OrderFlowEnum.DATABASE_ORDER_CANCEL.getCode().equals(flowCode)) {
            self.cancelOrderDone(subFlow.getBizId(), null);
        }
    }
}
