package cloud.demand.app.modules.order.entity.std_table;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
@Table("aws_consensus_order_performance_track_df")
public class AwsConsensusOrderPerformanceTrackDfDO {

    /**
     * 数据日期<br/>Column: [stat_time]
     */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区<br/>Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 产品<br/>Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * app_id<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * 订单类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    /**
     * 通用实例类型<br/>Column: [common_instance_type]
     */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 订单编号
     */
    @Column(value = "order_number")
    private String orderNumber;

    /**
     * 订单节点
     */
    @Column(value = "order_node_code")
    private String orderNodeCode;

    /**
     * 通用客户简称<br/>Column: [common_customer_name]
     */
    @Column(value = "common_customer_name")
    private String commonCustomerName;

    /**
     * 实例类型
     */
    @Column(value = "instance_type")
    private String instanceType;


    /**
     * 是否新机型
     */
    @Column(value = "is_new_instance_type")
    private Boolean isNewInstanceType;

    /**
     * 境内外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;


    /**
     * 地域
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区code<br/>Column: [zone]
     */
    @Column(value = "zone")
    private String zone;

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 开始统计履约时间
     */
    @Column(value = "start_cal_performance_track_date")
    private LocalDate startCalPerformanceTrackDate;

    /**
     * 结束统计履约时间
     */
    @Column(value = "finish_cal_performance_track_date")
    private LocalDate finishCalPerformanceTrackDate;


    /**
     * 需求总核心数
     */
    @Column(value = "demand_total_core")
    private Integer demandTotalCore;

    /**
     * 满足核心数
     */
    @Column(value = "satisfy_core")
    private BigDecimal satisfyCore;

    /**
     * 期初服务规模
     */
    @Column(value = "start_service_core")
    private BigDecimal startServiceCore;

    /**
     * 期末服务规模
     */
    @Column(value = "end_service_core")
    private BigDecimal endServiceCore;

    /**
     * 峰值服务规模
     */
    @Column(value = "max_service_core")
    private BigDecimal maxServiceCore;

    /**
     * 服务规模存量
     */
    @Column(value = "current_service_core")
    private BigDecimal currentServiceCore;

    /**
     * 原始的实际购买量
     * MAX((max_service_core-start_service_core),0)
     */
    @Column(value = "original_buy_total_core")
    private BigDecimal originalBuyTotalCore;

    /**
     * 实际购买量
     * MAX((max_service_core-start_service_core),0)
     */
    @Column(value = "buy_total_core")
    private BigDecimal buyTotalCore;

    /**
     * 待履约量
     */
    @Column(value = "wait_buy_total_core")
    private BigDecimal waitBuyTotalCore;

    /**
     * 履约率
     * MIN((buyTotalCore/satisfyCore),1)
     */
    @Column(value = "buy_rate")
    private BigDecimal buyRate;





    /**
     * 需求总卡数 GPU卡数
     */
    @Column(value = "demand_total_gpu")
    private Integer demandTotalGpu;

    /**
     * 满足卡数 GPU卡数
     */
    @Column(value = "satisfy_gpu")
    private BigDecimal satisfyGpu;

    /**
     * 期初服务规模 GPU卡数
     */
    @Column(value = "start_service_gpu")
    private BigDecimal startServiceGpu;

    /**
     * 期末服务规模 GPU卡数
     */
    @Column(value = "end_service_gpu")
    private BigDecimal endServiceGpu;

    /**
     * 峰值服务规模 GPU卡数
     */
    @Column(value = "max_service_gpu")
    private BigDecimal maxServiceGpu;

    /**
     * 服务规模存量 GPU卡数
     */
    @Column(value = "current_service_gpu")
    private BigDecimal currentServiceGpu;

    /**
     * 原始的实际购买量 GPU卡数
     * MAX((max_service_gpu-start_service_gpu),0)
     */
    @Column(value = "original_buy_total_gpu")
    private BigDecimal originalBuyTotalGpu;

    /**
     * 实际购买量 GPU卡数
     * MAX((max_service_gpu-start_service_gpu),0)
     */
    @Column(value = "buy_total_gpu")
    private BigDecimal buyTotalGpu;

    /**
     * 待履约量 GPU卡数
     */
    @Column(value = "wait_buy_total_gpu")
    private BigDecimal waitBuyTotalGpu;

    /**
     * 履约率 GPU卡数
     * MIN((buyTotalGpu/satisfyGpu),1)
     */
    @Column(value = "buy_rate_gpu")
    private BigDecimal buyRateGpu;



    // 全部初始化为0
    public static AwsConsensusOrderPerformanceTrackDfDO createInit(List<? extends OrderConsensusDemandDetailDO> demandDetail,
            OrderInfoDO order, String dataCommonCustomerName, Map<String, String> instanceTypeMap,
            List<String> newGenerationInstanceType, LocalDate dataDate, Map<String, StaticZoneDO> zoneMap) {
        if (ListUtils.isEmpty(demandDetail)) {
            return null;
        }
        OrderConsensusDemandDetailDO first = demandDetail.get(0);
        AwsConsensusOrderPerformanceTrackDfDO awsOrderPerformanceTrackDfDO = new AwsConsensusOrderPerformanceTrackDfDO();;

        BigDecimal satisfyCore = NumberUtils.sum(demandDetail, OrderConsensusDemandDetailDO::getSatisfiedCpuNum);
        awsOrderPerformanceTrackDfDO.setSatisfyCore(satisfyCore);
        BigDecimal satisfyGpu = NumberUtils.sum(demandDetail, OrderConsensusDemandDetailDO::getSatisfiedGpuNum);
        awsOrderPerformanceTrackDfDO.setSatisfyGpu(satisfyGpu);

        awsOrderPerformanceTrackDfDO.setStatTime(dataDate);
        awsOrderPerformanceTrackDfDO.setOrderNumber(order.getOrderNumber());
        awsOrderPerformanceTrackDfDO.setOrderNodeCode(order.getOrderNodeCode());
        awsOrderPerformanceTrackDfDO.setIndustryDept(order.getIndustryDept());
        awsOrderPerformanceTrackDfDO.setWarZone(order.getWarZone());
        awsOrderPerformanceTrackDfDO.setCustomerShortName(order.getCustomerShortName());
        awsOrderPerformanceTrackDfDO.setProduct(order.getProduct());
        awsOrderPerformanceTrackDfDO.setCustomerUin(order.getCustomerUin());
        awsOrderPerformanceTrackDfDO.setAppId(order.getAppId());
        awsOrderPerformanceTrackDfDO.setOrderType(
                OrderTypeEnum.getNameByCode(order.getOrderType()));
        awsOrderPerformanceTrackDfDO.setCommonInstanceType(
                instanceTypeMap.get(first.getDemandInstanceType()));
        awsOrderPerformanceTrackDfDO.setIsNewInstanceType(
                newGenerationInstanceType.contains(first.getDemandInstanceType()));
        awsOrderPerformanceTrackDfDO.setCustomhouseTitle(first.getDemandCustomhouseTitle());

        LocalDate beginBuyDate = demandDetail.get(0).getConsensusBeginBuyDate();
        LocalDate endBuyDate = DateUtils.max(demandDetail, OrderConsensusDemandDetailDO::getConsensusEndBuyDate);
        awsOrderPerformanceTrackDfDO.setBeginBuyDate(beginBuyDate);
        awsOrderPerformanceTrackDfDO.setEndBuyDate(endBuyDate);

        awsOrderPerformanceTrackDfDO.setCommonCustomerName(dataCommonCustomerName);

        awsOrderPerformanceTrackDfDO.setZoneName(first.getDemandZoneName());
        StaticZoneDO zoneDO = zoneMap.get(first.getDemandZoneName());
        if (zoneDO != null) {
            awsOrderPerformanceTrackDfDO.setRegionName(zoneDO.getRegionName());
            awsOrderPerformanceTrackDfDO.setRegion(zoneDO.getApiRegion());
            awsOrderPerformanceTrackDfDO.setZone(zoneDO.getZone());
        }
        awsOrderPerformanceTrackDfDO.setInstanceType(first.getDemandInstanceType());

        awsOrderPerformanceTrackDfDO.setStartCalPerformanceTrackDate(beginBuyDate);
        awsOrderPerformanceTrackDfDO.setFinishCalPerformanceTrackDate(endBuyDate);
        BigDecimal totalCore = NumberUtils.sum(demandDetail, OrderConsensusDemandDetailDO::getConsensusDemandCpuNum);
        awsOrderPerformanceTrackDfDO.setDemandTotalCore(totalCore.intValue());
        BigDecimal totalGpu = NumberUtils.sum(demandDetail, OrderConsensusDemandDetailDO::getConsensusDemandGpuNum);
        awsOrderPerformanceTrackDfDO.setDemandTotalGpu(totalGpu.intValue());

        awsOrderPerformanceTrackDfDO.setMaxServiceCore(new BigDecimal(0));
        awsOrderPerformanceTrackDfDO.setBuyTotalCore(new BigDecimal(0));
        awsOrderPerformanceTrackDfDO.setCurrentServiceCore(new BigDecimal(0));
        awsOrderPerformanceTrackDfDO.setBuyRate(new BigDecimal(0));
        awsOrderPerformanceTrackDfDO.setEndServiceCore(new BigDecimal(0));

        // 设置原始值
        awsOrderPerformanceTrackDfDO.setOriginalBuyTotalCore(new BigDecimal(0));

        // 设置最终值
        awsOrderPerformanceTrackDfDO.setBuyTotalCore(awsOrderPerformanceTrackDfDO.getOriginalBuyTotalCore());

        // 设置未履约量
        awsOrderPerformanceTrackDfDO.setWaitBuyTotalCore(new BigDecimal(0));
        return awsOrderPerformanceTrackDfDO;
    }

    public void calcRealServiceCore(Map<LocalDate, BigDecimal> statTimeMap, Boolean isNewDemand) {
        if (ListUtils.isEmpty(statTimeMap)) {
            return;
        }
        // 设置服务水平信息 - 期初、期末、当天、峰值 的规模量
        this.setStartServiceCore(statTimeMap.getOrDefault(this.getStartCalPerformanceTrackDate(), BigDecimal.ZERO));

        this.setEndServiceCore(statTimeMap.getOrDefault(this.getFinishCalPerformanceTrackDate(), BigDecimal.ZERO));

        this.setCurrentServiceCore(statTimeMap.getOrDefault(LocalDate.now().minusDays(1), BigDecimal.ZERO));

        this.setMaxServiceCore(getMax(statTimeMap, startCalPerformanceTrackDate, finishCalPerformanceTrackDate));

        this.setBuyTotalCore(
                getBuyTotal(statTimeMap, isNewDemand, startCalPerformanceTrackDate, finishCalPerformanceTrackDate));

        // 记录原始服务水平信息
        this.setOriginalBuyTotalCore(this.getBuyTotalCore());


        // 计算未履约量 最小值为0
        this.setWaitBuyTotalCore(
                Collections.max(ListUtils.newArrayList(BigDecimal.ZERO,
                        this.getSatisfyCore().subtract(this.getBuyTotalCore())))
        );

        if (this.getSatisfyCore() != null && this.getSatisfyCore().compareTo(BigDecimal.ZERO) > 0) {
            this.setBuyRate(
                    NumberUtils.divide(this.getBuyTotalCore(), this.getSatisfyCore(), 4)
                            .min(BigDecimal.ONE));
        }
    }

    public void calcRealServiceGpu(Map<LocalDate, BigDecimal> statTimeGpuMap, Boolean isNewDemand) {
        if (ListUtils.isEmpty(statTimeGpuMap)) {
            return;
        }
        // 设置服务水平信息 - 期初、期末、当天、峰值 的规模量
        this.setStartServiceGpu(statTimeGpuMap.getOrDefault(this.getStartCalPerformanceTrackDate(), BigDecimal.ZERO));

        this.setEndServiceGpu(statTimeGpuMap.getOrDefault(this.getFinishCalPerformanceTrackDate(), BigDecimal.ZERO));

        this.setCurrentServiceGpu(statTimeGpuMap.getOrDefault(LocalDate.now().minusDays(1), BigDecimal.ZERO));

        this.setMaxServiceGpu(getMax(statTimeGpuMap, startCalPerformanceTrackDate, finishCalPerformanceTrackDate));

        this.setBuyTotalGpu(
                getBuyTotal(statTimeGpuMap, isNewDemand, startCalPerformanceTrackDate, finishCalPerformanceTrackDate));

        // 记录原始服务水平信息
        this.setOriginalBuyTotalGpu(this.getBuyTotalGpu());


        // 计算未履约量 最小值为0
        this.setWaitBuyTotalGpu(
                Collections.max(ListUtils.newArrayList(BigDecimal.ZERO,
                        this.getSatisfyGpu().subtract(this.getBuyTotalGpu())))
        );

        if (this.getSatisfyGpu() != null && this.getSatisfyGpu().compareTo(BigDecimal.ZERO) > 0) {
            this.setBuyRateGpu(
                    NumberUtils.divide(this.getBuyTotalGpu(), this.getSatisfyGpu(), 4)
                            .min(BigDecimal.ONE));
        }
    }

    private BigDecimal getMax(Map<LocalDate, BigDecimal> statTimeMap, LocalDate start, LocalDate end) {
        if (statTimeMap.isEmpty()) {
            return new BigDecimal(0);
        }

        if (LocalDate.now().isBefore(start) || LocalDate.now().equals(start)) {
            // 如果没到期初： 直接为0
            return new BigDecimal(0);
        }

        Map<LocalDate, BigDecimal> rangeMap = statTimeMap.entrySet().stream()
                .filter(entry -> entry.getKey().isAfter(start.minusDays(1))
                        && entry.getKey().isBefore(end.plusDays(1)))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        BigDecimal max = new BigDecimal(0);
        if (ListUtils.isNotEmpty(rangeMap)) {
            max = Collections.max(rangeMap.values());
        }
        return max;
    }

    private BigDecimal getBuyTotal(Map<LocalDate, BigDecimal> statTimeMap, Boolean isNewDemand,
            LocalDate start, LocalDate end) {
        if (statTimeMap.isEmpty()) {
            return new BigDecimal(0);
        }

        if (LocalDate.now().isBefore(start) || LocalDate.now().equals(start)) {
            // 如果没到期初： 直接为0
            return new BigDecimal(0);
        }

        if (LocalDate.now().isBefore(end) || LocalDate.now().equals(end)) {
            // 如果没到期末： 直接取当天
            end = LocalDate.now().minusDays(1);
        }

        BigDecimal result;
        if (isNewDemand) {
            // 新增需求：Max(存量服务规模(结束日期) - 存量服务规模(开始日期),0)
            BigDecimal endScale = statTimeMap.getOrDefault(end, BigDecimal.ZERO);
            BigDecimal startScale = statTimeMap.getOrDefault(start, BigDecimal.ZERO);
            result = endScale.subtract(startScale);
            result = Collections.max(ListUtils.newArrayList(result, BigDecimal.ZERO));
        } else {
            // 弹性需求：Max(峰值存量规模[开始～结束]-存量服务规模(开始日期),0)
            BigDecimal max = getMax(statTimeMap, start, end);
            // 获取开始统计履约时间的规模
            BigDecimal startScale = statTimeMap.getOrDefault(start, BigDecimal.ZERO);
            result = max.subtract(startScale);
            result = Collections.max(ListUtils.newArrayList(result, new BigDecimal(0)));
        }
        return result;
    }
}
