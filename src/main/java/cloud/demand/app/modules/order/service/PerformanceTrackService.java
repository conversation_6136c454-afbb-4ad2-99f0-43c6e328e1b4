package cloud.demand.app.modules.order.service;

import cloud.demand.app.modules.order.dto.OrderDayScaleDTOWithoutAppRole;
import cloud.demand.app.modules.order.dto.PerformanceTrackDfDTO;
import cloud.demand.app.modules.order.dto.req.CycleElasticPerformanceTrendReq;
import cloud.demand.app.modules.order.dto.req.ModifyPerformanceTrackDTO;
import cloud.demand.app.modules.order.dto.req.OrderDayScaleReq;
import cloud.demand.app.modules.order.dto.req.PerformanceTrackReq;
import cloud.demand.app.modules.order.dto.resp.CycleElasticPerformanceResp;
import cloud.demand.app.modules.order.dto.resp.CycleElasticPerformanceResp.BuyItem;
import cloud.demand.app.modules.order.dto.resp.CycleElasticPerformanceTrendResp;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderPerformanceTrackDetailResp;
import cloud.demand.app.modules.order.dto.resp.PerformanceSummaryResp;
import cloud.demand.app.modules.order.entity.OrderPerformanceTrackModifyRecordDO;
import cloud.demand.app.modules.order.entity.std_table.AwsOrderPerformanceTrackDfDO;
import cloud.demand.app.web.model.common.DownloadBean;
import java.time.LocalDate;
import java.util.List;
import yunti.boot.web.jsonrpc.JsonrpcParam;

public interface PerformanceTrackService {

    void refreshDemandFollow();


    OrderPerformanceTrackDetailResp queryOrderPerformanceTrackDetail(String orderNumber);

    List<AwsOrderPerformanceTrackDfDO> queryOrderPerformanceTrackList(String orderNumber);


    List<OrderDayScaleDTOWithoutAppRole> queryOrderDayScale(@JsonrpcParam OrderDayScaleReq req);

    DownloadBean orderTrackExport(List<OrderDetailResp> orders, String fileNamePrefix);

    void modifyPerformanceTrack(ModifyPerformanceTrackDTO modifyDTO);

    List<OrderPerformanceTrackModifyRecordDO> queryModifyPerformanceTrack(String orderNumber, String region,
            String instanceType);

    List<PerformanceTrackDfDTO> queryPerformanceTrackList(PerformanceTrackReq req);


    /**
     * 查询履约跟踪汇总数据
     *
     * @param req
     */
    PerformanceSummaryResp queryPerformanceSummary(PerformanceTrackReq req);

    /**
     *  共识需求履约量计算 <br/>
     * @param minEndBuyDate 只计算订单结束购买日期大于等于minEndBuyDate的订单，可以为null，默认值为当天往前推90天
     */
    void refreshConsensusDemandFollow(LocalDate minEndBuyDate);

    /**
     *  周期性弹性订单共识需求履约量计算 <br/>
     * @param minEndBuyDate 只计算订单结束购买日期大于等于minEndBuyDate的订单，可以为null，默认值为当天往前推90天
     */
    void refreshCycleElasticConsensusDemandFollow(LocalDate minEndBuyDate);

    /**
     *  周期性弹性订单共识需求履约量计算 <br/>
     * @param orderNumberList 需要重新计算的订单号
     */
    void refreshCycleElasticConsensusDemandFollowByOrders(List<String> orderNumberList);

    /**
     *  查询周期性弹性订单履约数据 <br/>
     */
    CycleElasticPerformanceResp queryCycleElasticPerformance(String orderNumber);

    /**
     *  查询周期性弹性订单履约趋势数据 <br/>
     * @param req 订单号不能为空
     */
    CycleElasticPerformanceTrendResp queryCycleElasticPerformanceTrend(CycleElasticPerformanceTrendReq req);

    /**
     *  获取周期性弹性订单的履约数据，可用区、实例类型维度
     */
    List<BuyItem> queryCycleElasticBuyItem(String orderNumber);

    /**
     *  是否已经计算过周期性弹性订单的共识需求履约量
     */
    boolean hasCalcCycleElasticTrack(String orderNumber);

}
