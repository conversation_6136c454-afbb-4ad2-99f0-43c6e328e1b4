package cloud.demand.app.modules.order.dto.resp;

import cloud.demand.app.modules.order.dto.resp.supply.process.OrderSupplyProcessVO;
import cloud.demand.app.modules.order.dto.resp.supply.process.ProcessItemVO;
import cloud.demand.app.modules.order.dto.resp.supply.process.SubProcessVO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;

@Data
public class OrderSupplyProgressResp {

    private String orderNumber;

    private Long orderInfoId;

    private String product;

    // 核心数、卡数、内存量、存储量、实例数
    private int totalCoreOrGpu;

    private List<SupplyPlanSummary> plans;

    private LocalDate beginBuyDate;

    private Long supplyVersionId;

    private String supplyVersionRemark;

    // 星云主单与对应的 Q 单号的映射关系，key为星云主单号
    // 供应明细中supplyBizId对应的实际Q单、M单号列表，key为供应明细中supplyBizId
    private Map<String, List<String>> supplyBizIdToListMap;


    @Data
    public static class SupplyPlanSummary extends OrderSupplyPlanDO {

        private List<OrderSupplyPlanDetailWithSatisfyDTO> details;

        // 核心数、卡数、内存量、存储量、实例数
        private int totalCoreOrGpu;

        private List<String> instanceTypes;

        private List<String> zoneNames;

        // Q单、M单，不会有星云主单号，已经将星云主单号转成Q单放进来了
        private List<String> processBizIds;

        // key为星云主单或Q单或M单，value为该业务单号的对应供应方案的核心数
        private Map<String, Integer> bizIdTotalCoreOrGpuMap;

        // 资源交付进度（根据erp接口返回的数据加工生成）
        private List<SupplyProgress> progress = new ArrayList<>();

        // erp 接口返回的资源准备进度
        private List<OrderSupplyProcessVO> normalProcess = new ArrayList<>();

        // 最晚的预计交付日期,为 null 时表示预计交付日期不明确
        private LocalDate lastExpectDeliveryDate;

        // 最晚的预计交付日期所交付的核心数
        private Integer lastExpectDeliveryNum;

        // 订单的开始购买日期，用于和最晚的预计交付日期对比，来获取是否延期/延期多久
        private LocalDate beginBuyDate;

        // 资源准备链路信息查询的异常消息
        private String supplyErrorMsg;

        // 实际交付日期，为 null 时表示暂未交付完成
        private LocalDate actualSupplyDate;


        /**
         * 从供应方案生成
         */
        public static SupplyPlanSummary copy(OrderSupplyPlanWithDetailDTO input) {
            SupplyPlanSummary res = new SupplyPlanSummary();
            BeanUtils.copyProperties(input, res, "details");
            if (ListUtils.isEmpty(input.getDetails())) {
                res.details = new ArrayList<>();
                return res;
            } else {
                res.details = new ArrayList<>();
                for (OrderSupplyPlanDetailDO detail : input.getDetails()) {
                    OrderSupplyPlanDetailWithSatisfyDTO detailSatisfy = new OrderSupplyPlanDetailWithSatisfyDTO();
                    BeanUtils.copyProperties(detail, detailSatisfy);
                    res.details.add(detailSatisfy);
                }
            }
            boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(res.getProduct());
            boolean isDataBase = Ppl13weekProductTypeEnum.DATABASE.getName().equals(res.getProduct());
            int totalCoreOrGpu;
            if (isGpu) {
                totalCoreOrGpu = res.getDetails().stream()
                        .mapToInt(value -> value.getSupplyGpuNum() == null ? 0 : value.getSupplyGpuNum()).sum();
            } else if (isDataBase) {
                totalCoreOrGpu = res.getDetails().stream()
                        .mapToInt(value -> value.getSupplyInstanceNum() == null ? 0 : value.getSupplyInstanceNum())
                        .sum();
            } else {
                totalCoreOrGpu = res.getDetails().stream().mapToInt(OrderSupplyPlanDetailDO::getSupplyCoreNum).sum();
            }
            res.setTotalCoreOrGpu(totalCoreOrGpu);

            Set<String> itemInstanceTypes = new HashSet<>();
            Set<String> itemZoneNames = new HashSet<>();
            Map<String, Integer> bizIdTotalCoreOrGpuMap = new HashMap<>();
            for (OrderSupplyPlanDetailDO detail : input.getDetails()) {
                if (detail == null) {
                    continue;
                }
                if (Strings.isNotBlank(detail.getSupplyBizId())) {
                    // 暂时只用核心数
                    int core = detail.getSupplyCoreNum() == null ? 0 : detail.getSupplyCoreNum();
                    if (isGpu) {
                        core = detail.getSupplyGpuNum() == null ? 0 : detail.getSupplyGpuNum();
                    } else if (isDataBase) {
                        core = detail.getSupplyInstanceNum() == null ? 0 : detail.getSupplyInstanceNum();
                    }
                    Integer bizIdCores = bizIdTotalCoreOrGpuMap.get(detail.getSupplyBizId());
                    bizIdCores = bizIdCores == null ? core : (bizIdCores + core);
                    bizIdTotalCoreOrGpuMap.put(detail.getSupplyBizId(), bizIdCores);
                }
                if (Strings.isNotBlank(detail.getSupplyInstanceType())) {
                    itemInstanceTypes.add(detail.getSupplyInstanceType());
                }
                if (Strings.isNotBlank(detail.getSupplyZoneName())) {
                    itemZoneNames.add(detail.getSupplyZoneName());
                }
            }
            res.setInstanceTypes(new ArrayList<>(itemInstanceTypes));
            res.setZoneNames(new ArrayList<>(itemZoneNames));
            res.setBizIdTotalCoreOrGpuMap(bizIdTotalCoreOrGpuMap);
            return res;
        }

        /**
         * 从 erp 获取到交付进度之后，做一些处理，生成前端页面需要的交付进度、最晚的预计交付日期. <br/>
         * 并且 为方案明细的 最晚预计交付日期 赋值. <br/>
         */
        public void progressHandler() {
            Map<String, List<String>> instanceTypeMap = ListUtils.toMapList(getDetails(),
                    OrderSupplyPlanDetailDO::getSupplyBizId, OrderSupplyPlanDetailDO::getSupplyInstanceType);
            // 当前满足方式中没有资源满足链路信息的业务单号，如果某个业务单号没有资源准备链路，则表示最晚预计交付日期不明确
            final List<String> noSourceBizIds = new ArrayList<>(this.processBizIds);
            // Q单、M单对应的最晚预计交付日期
            Map<String, LocalDate> itemLastDateMap = new HashMap<>();
            for (OrderSupplyProcessVO process : this.normalProcess) {
                if (process == null) {
                    continue;
                }

                Tuple2<LocalDate, Integer> itemLastDate = process.parseGetLastExpectDeliveryDate();
                if (itemLastDate != null) {
                    // OrderSupplyPlanDO 层级的 最晚的预计交付日期
                    if (lastExpectDeliveryDate == null) {
                        lastExpectDeliveryDate = itemLastDate._1;
                        lastExpectDeliveryNum = itemLastDate._2;
                    } else if (lastExpectDeliveryDate.isBefore(itemLastDate._1)) {
                        lastExpectDeliveryDate = itemLastDate._1;
                        lastExpectDeliveryNum = itemLastDate._2;
                    }
                }
                itemLastDateMap.put(process.getOrderId(), itemLastDate == null ? null : itemLastDate._1);

                if (ListUtils.isNotEmpty(process.getProcess())) {
                    for (ProcessItemVO processItemVO : process.getProcess()) {
                        if (processItemVO == null || ListUtils.isEmpty(processItemVO.getSubProcess())) {
                            continue;
                        }
                        // 交付进度
                        SupplyProgress item = new SupplyProgress();
                        item.setSupplyBizId(process.getOrderId());
                        List<String> instanceTypeList = instanceTypeMap.get(process.getOrderId());
                        if (ListUtils.isNotEmpty(instanceTypeList)) {
                            item.setInstanceTypes(instanceTypeList.stream().distinct().collect(Collectors.toList()));
                        }
                        item.setSubProcessList(processItemVO.getSubProcess());
                        // 该业务单号存在资源满足链路信息
                        noSourceBizIds.remove(item.getSupplyBizId());
                        this.progress.add(item);
                    }
                }
            }

            if (ListUtils.isNotEmpty(noSourceBizIds)) {
                // 存在业务单号没有资源准备链路，则最晚预计交付日期不明确
                lastExpectDeliveryDate = null;
                lastExpectDeliveryNum = null;
            }
            for (OrderSupplyPlanDetailWithSatisfyDTO detail : this.getDetails()) {
                if (Strings.isBlank(detail.getSupplyBizId())) {
                    // 存在业务单号为空，则最晚预计交付日期不明确
                    lastExpectDeliveryDate = null;
                    lastExpectDeliveryNum = null;
                    detail.setSupplyTime(null);
                } else {
                    if (ListUtils.isEmpty(detail.getActualSupplyBizIdList())) {
                        LocalDate lastSupplyDate = itemLastDateMap.get(detail.getSupplyBizId());
                        detail.setSupplyTime(lastSupplyDate);
                    } else {
                        LocalDate lastSupplyDate = null; // 最晚预计交付日期
                        boolean anyNoSupplyDate = false; // 是否存在没有预计交付日期的Q单
                        for (String qBizId : detail.getActualSupplyBizIdList()) {
                            LocalDate itemLastSupplyDate = itemLastDateMap.get(qBizId);
                            if (itemLastSupplyDate == null) {
                                anyNoSupplyDate = true;
                                break;
                            } else {
                                if (lastSupplyDate == null) {
                                    lastSupplyDate = itemLastSupplyDate;
                                } else if (lastSupplyDate.isBefore(itemLastSupplyDate)) {
                                    lastSupplyDate = itemLastSupplyDate;
                                }
                            }
                        }
                        if (anyNoSupplyDate) {
                            detail.setSupplyTime(null);
                        } else {
                            detail.setSupplyTime(lastSupplyDate);
                        }
                    }
                }
            }
            handlerProcessNumber();
        }

        /**
         * 资源准备链路中的核心数不要大于总核心数，1 核也不行， 原始数据可能会有 1 核的误差
         */
        private void handlerProcessNumber() {
            int total = 0;
            for (SupplyProgress a : this.progress) {
                if (a == null || ListUtils.isEmpty(a.getSubProcessList())) {
                    continue;
                }
                for (SubProcessVO subProcessVO : a.getSubProcessList()) {
                    if (subProcessVO == null || subProcessVO.getNumber() == null) {
                        continue;
                    }
                    total += subProcessVO.getNumber();
                    int diff = total - this.totalCoreOrGpu;
                    if (diff > 0) {
                        // 资源准备链路中的核心数不要大于总核心数，1 核也不行， 原始数据可能会有 1 核的误差
                        int newNumber = subProcessVO.getNumber() - diff;
                        subProcessVO.setNumber(Math.max(newNumber, 0));
                    }
                }
            }
        }

    }

    @Data
    public static class SupplyProgress {

        // 供应业务订单，Q单、M单
        private String supplyBizId;

        // 供应了哪些机型
        private List<String> instanceTypes;

        // 供应进度
        private List<SubProcessVO> subProcessList;

    }

}
