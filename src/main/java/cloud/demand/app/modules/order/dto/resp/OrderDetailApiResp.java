package cloud.demand.app.modules.order.dto.resp;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class OrderDetailApiResp {

    /**
     * 业务订单号，一个订单的唯一标识<br/>Column: [order_number]
     */
    private String orderNumber; //

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    private String industryDept;   //

    /**
     * 战区
     * Column: [war_zone]
     */
    private String warZone;   //

    /**
     * 产品
     * Column: [product]
     */
    private String product; //

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    private String customerShortName;   //

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    private String customerName;   //

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    private String customerUin;   //

    /**
     * APPID<br/>Column: [app_id]
     */
    private String appId;   //

    /**
     * 订单分类<br/>Column: [order_category]
     * CVM、GPU、裸金属、FPGA
     */
    private String orderCategory; //

    /**
     * 应用角色<br/>Column: [app_role]
     */
    private String appRole;  //

    /**
     * 单据类型<br/>Column: [order_type]
     */
    private String orderType;  //

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    private LocalDate beginBuyDate;   //

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    private LocalDate endBuyDate;   //

    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    private LocalTime beginElasticDate;   //

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    private LocalTime endElasticDate;   //

    /**
     * 项目名称<br/>Column: [project_name]
     */
    private String projectName;   //

    /**
     * 需求详情<br/>Column: [demand_detail]
     */
    private String demandDetail;   //

    /**
     * 提单人<br/>Column: [submit_user]
     */
    private String submitUser;   //

    /**
     * 架构师，订单负责人<br/>Column: [architect]
     */
    private String architect;   //



    /**
     * 订单优先级，默认低优先级，低优先级，中优先级，高优先级<br/>Column: [level_name]
     */
    private String levelName;   //

    /**
     * 提单时间
     * <br/>Column: [submit_time]
     */
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date submitTime;   //


    /**
     * 订单明细号，一个订单明细的唯一标识<br/>
     * Column: [order_number_id]
     */
    private String orderNumberId;


    /**
     * 计费模式<br/>Column: [bill_type]
     */
    private String billType;   //

    /**
     * 地域code<br/>Column: [region]
     */
    private String region;   //

    /**
     * 地域名称<br/>Column: [region_name]
     */
    private String regionName;   //

    /**
     * 可用区code<br/>Column: [zone]
     */
    private String zone;   //

    /**
     * 可用区名称<br/>Column: [zone_name]
     */
    private String zoneName;   //


    /**
     * 实例类型<br/>Column: [instance_type]
     */
    private String instanceType;   //

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    private String instanceModel;   //

    /**
     * 系统盘磁盘类型<br/>Column: [system_disk_type]
     */
    private String systemDiskType;

    /**
     * 系统盘磁盘容量，单位G<br/>Column: [system_disk_storage]
     */
    private Integer systemDiskStorage;

    /**
     * 系统盘磁盘块数<br/>Column: [system_disk_num]
     */
    private Integer systemDiskNum;

    /**
     * 数据盘磁盘类型<br/>Column: [data_disk_type]
     */
    private String dataDiskType;

    /**
     * 数据盘磁盘容量，单位G<br/>Column: [data_disk_storage]
     */
    private Integer dataDiskStorage;

    /**
     * 数据盘磁盘块数<br/>Column: [data_disk_num]
     */
    private Integer dataDiskNum;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    private Integer instanceNum;   //

    /**
     * 单台核心数<br/>Column: [cpu_num]
     */
    private Integer cpuNum;   //

    /**
     * 内存数<br/>Column: [memory]
     */
    private Integer memory;   //

    /**
     * 单台卡数<br/>Column: [gpu_num]
     */
    private Integer gpuNum;   //

    /**
     * 总核心数<br/>Column: [total_core]
     */
    private Integer totalCore;

    /**
     * 总磁盘数<br/>Column: [total_disk]
     */
    private Integer totalDisk;

    /**
     * gpu型号 <br/>Column: [gpuType]
     */
    private String gpuType;   //


    /**
     * gpu总卡数 <br/>Column: [totalGpuNum]
     */
    private Integer totalGpuNum;


    private String orderTypeName;

    // 开始购买年月
    private YearMonth beginBuyYearMonth;

    // 结束购买年月
    private YearMonth endBuyYearMonth;

    private String systemDiskTypeName;   //

    private String dataDiskTypeName;   //

    // 系统盘总大小，实例数量*单台系统盘大小
    private Integer systemTotalDisk;   //

    // 单台数据盘大小，单台数据盘块数*单数据盘磁盘容量
    private Integer dataDiskOneInstanceStorage;   //

    // 数据盘总大小，实例数量*单台数据盘大小
    private Integer dataTotalDisk;   //

    // 申请预扣实例数
    private int preDeductNum;   //

    // 申请预扣核心数
    private int preDeductCore;   //

    // 实际预扣实例数
    private int actualPreDeductNum;   //

    // 实际预扣核心数
    private int actualPreDeductCore;   //

    // 计费模式名称
    private String billTypeName;   //

    // 备选可用区名称，多个可用区用,分隔
    private String otherZoneNamesJoin;   //

    // 备选实例规格，多个实例规格用,分隔
    private String otherInstanceModelsJoin;   //

    // 供应方案-大盘满足核心数
    private Integer satisfySupplyCore;   //

    // 供应方案-采购满足核心数
    private Integer buySupplyCore;   //

    // 供应方案-搬迁满足核心数
    private Integer moveSupplyCore;   //

    // 订单满足度-订单满足总核心数
    private BigDecimal orderItemMatchedTotalCore;   //

    // 订单满足度-满足率
    private BigDecimal orderItemMatchedRate;   //

    // 计划预扣卡数
    private Integer preDeductGpuNum;   //

    // 实际预扣卡数
    private Integer actualPreDeductGpuNum;   //

    /**
     * 订单状态, DRAFT(草稿), PROCESS(进行中), FINISHED(已完结)，CANCELED(已取消) <br/>
     */
    private String orderStatus;

    public static List<OrderDetailApiResp> from(List<OrderDetailResp> orderList) {
        List<OrderDetailApiResp> list = new ArrayList<>();
        for (OrderDetailResp order : orderList) {
            for (OrderItemDTO item : order.getItemList()) {
                OrderDetailApiResp resp = OrderDetailApiResp.from(item, order);
                list.add(resp);
            }
        }
        return list;
    }

    private static OrderDetailApiResp from(OrderItemDTO item, OrderDetailResp order) {
        OrderDetailApiResp resp = new OrderDetailApiResp();
        BeanUtils.copyProperties(item, resp);
        BeanUtil.copyProperties(order, resp);
        return resp;
    }

}
