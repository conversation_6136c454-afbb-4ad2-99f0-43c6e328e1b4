package cloud.demand.app.modules.order.service;

import cloud.demand.app.modules.order.dto.req.OrderChangeLabelReq;
import cloud.demand.app.modules.order.dto.req.OrderChangeLabelReq.OneChangeReq;
import cloud.demand.app.modules.order.dto.req.PplOrder2XyPurchaseOrderReq;
import cloud.demand.app.modules.order.enums.OrderRiskLevelEnum;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface OrderCommonOperateService {

    /**
     * 修改订单的提交人和负责人
     */
    void modifyOrderSubmitAndArchitect(List<String> orderNumbers, String submit, String architect,
            Set<String> addOrderFollower);

    /**
     * 订单订阅
     *
     * @param orderNumber 订单号
     */
    void subscribe(String orderNumber);

    /**
     * 取消订单订阅
     *
     * @param orderNumber 订单号
     */
    void cancelSubscribe(String orderNumber);

    /**
     * 同步进行中生效的订单明细的最晚共识开始购买日期
     */
    void syncOrderItemsLateConsensusBeginBuyDate();

    /**
     * 更新风险级别
     *
     * @param orderNumber 订单号 非空
     * @param riskLevel 风险级别
     */
    void updateOrderRiskLevel(String orderNumber, OrderRiskLevelEnum riskLevel);

    /**
     * 通过查询数据库中生效的供应方案来更新生效中订单的风险级别
     *
     * @param orderNumber 订单号
     */
    void updateOrderRiskLevelBySupplyPlan(String orderNumber);

    /**
     * 停止进行中的订单子流程，会根据入参判断是否需要进行权限判断（当前操作人为提单人时才能操作）<br/><br/>
     * ！！！！ 调用此方法后，如果还有后续业务操作，请重新获取最新的订单信息，因为此方法内部可能会生成新的主流程订单信息  <br/>
     * ！！！！ 调用此方法后，如果还有后续业务操作，请重新获取最新的订单信息，因为此方法内部可能会生成新的主流程订单信息  <br/>
     * ！！！！ 调用此方法后，如果还有后续业务操作，请重新获取最新的订单信息，因为此方法内部可能会生成新的主流程订单信息  <br/>
     *
     * @param needCheckAuth 是否需要校验操作权限，false表示不用校验
     */
    void stopProcessingSubFlow(String orderNumber, String operateEvent, boolean needCheckAuth);

    /**
     * 删除草稿
     */
    void deleteDraft(String orderNumber);

    /**
     * 重置订单明细的最晚共识开始购买日期
     *
     * @param orderNumber 订单号 非空
     */
    void resetOrderItemsLateConsensusBeginBuyDate(String orderNumber);

    void bindXyPurchaseForPplOrder(PplOrder2XyPurchaseOrderReq req);

    /**
     * 修改订单标签,批量
     */
    void modifyOrderLabel(OrderChangeLabelReq req);

    /**
     * 运维接口 - 将订单开始购买结束日期 同步到 供应方案共识开始结束日期，再刷新共识需求
     * @param orderNumberSql 获取待处理订单号的SQL
     */
    Map<String, Object> opsConsensusBeginEndBuyDate(String orderNumberSql);

    /**
     * 运维接口 - 将订单开始购买结束日期 同步到 供应方案共识开始结束日期，再刷新共识需求
     * @param orderNumber 订单号
     */
    void opsConsensusBeginEndBuyDateOnOrder(String orderNumber);

    /**
     * 运维接口，为所有生效的共识需求添加原始开始与结束购买时间
     */
    void synConsensusOrderDate();


    /**
     * 运维接口 - 刷新填写供应方案节点的审批人
     */
    void refreshOrderMatchWayProcessor();

    /**
     * 运维接口，为所有状态为订单取消的订单生成共识需求
     */
    void genConsensusDemandForOrderCancel();

}
