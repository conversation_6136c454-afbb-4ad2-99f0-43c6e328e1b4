package cloud.demand.app.modules.order.dto.resp.service_level;

import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import lombok.Data;
import org.springframework.beans.BeanUtils;

@Data
public class OfficialWebsiteBuyAndSoldOutDTO extends OfficialWebsiteBuyDTO {

    /** 可售规格数。售罄率=售罄的规格数/可售规格数 */
    private BigDecimal soldTotal;

    /** 售罄的规格数。售罄率=售罄的规格数/可售规格数 */
    private BigDecimal soldOutTotal;

    // 售罄率
    private String soldOutRate;

    // 计算官网服务水平
    private String serviceLevel;

    public static List<OfficialWebsiteBuyAndSoldOutDTO> merge(List<OfficialWebsiteBuyDTO> buyList,
            List<OfficialWebsiteSoldOutDTO> soldOutList) {
        List<OfficialWebsiteBuyAndSoldOutDTO> res = new ArrayList<>();
        Map<String, OfficialWebsiteSoldOutDTO> soldMap = ListUtils.toMap(soldOutList,
                OfficialWebsiteBuyAndSoldOutDTO::toKey, Function.identity());
        if (ListUtils.isEmpty(buyList) && ListUtils.isEmpty(soldOutList)) {
            return res;
        }
        if (ListUtils.isEmpty(buyList)) {
            return convertFrom(soldOutList);
        }
        for (OfficialWebsiteBuyDTO buyDTO : buyList) {
            OfficialWebsiteBuyAndSoldOutDTO item = new OfficialWebsiteBuyAndSoldOutDTO();
            BeanUtils.copyProperties(buyDTO, item);
            String key = toKey(item);
            OfficialWebsiteSoldOutDTO sold = soldMap.remove(key);
            if (sold != null) {
                item.setSoldTotal(sold.getSoldTotal());
                item.setSoldOutTotal(sold.getSoldOutTotal());
                item.setSoldOutRate(sold.getSoldOutRate());
            }
            item.calculateServiceLevel();
            res.add(item);
        }
        List<OfficialWebsiteSoldOutDTO> noMatchSoldList = new ArrayList<>(soldMap.values());
        if (ListUtils.isEmpty(noMatchSoldList)) {
            List<OfficialWebsiteBuyAndSoldOutDTO> list = convertFrom(noMatchSoldList);
            res.addAll(list);
        }
        return res;
    }

    private void calculateServiceLevel() {
        // 购买成功率
        BigDecimal buyRate = BigDecimal.ZERO;
        if (getApplyCore() != null && BigDecimal.ZERO.compareTo(getApplyCore()) != 0) {
            if (getSuccessBuyCore() != null) {
                buyRate = getSuccessBuyCore().divide(getApplyCore(), 4, RoundingMode.HALF_UP);
            }
            buyRate = buyRate.min(BigDecimal.ONE);
        }

        // 售罄率
        BigDecimal soldRate = BigDecimal.ZERO;
        if (getSoldTotal() != null && BigDecimal.ZERO.compareTo(getSoldTotal()) != 0) {
            if (getSoldOutTotal() != null) {
                soldRate = getSoldOutTotal().divide(getSoldTotal(), 4, RoundingMode.HALF_UP);
            }
            soldRate = soldRate.min(BigDecimal.ONE);
        }

        // 官网服务水平 = 70% * 购买成功率 + 30% *（1 - 售罄率）
        BigDecimal serviceLevelRate = BigDecimal.valueOf(0.7).multiply(buyRate)
                .add(BigDecimal.valueOf(0.3).multiply(BigDecimal.ONE.subtract(soldRate)));
        BigDecimal oneHundred = new BigDecimal(100);
        this.serviceLevel = oneHundred.multiply(serviceLevelRate).setScale(2, RoundingMode.HALF_UP) + "%";
    }

    private static String toKey(OfficialWebsiteBuyDTO item) {
        return String.format("%s-%s-%s-%s-%s-%s", item.getCustomhouseTitle(), item.getRegionName(),
                item.getNewInstanceType(), item.getInstanceType(), item.getYearMonth(), item.getZoneName());
    }

    private static String toKey(OfficialWebsiteSoldOutDTO item) {
        return String.format("%s-%s-%s-%s-%s-%s", item.getCustomhouseTitle(), item.getRegionName(),
                item.getNewInstanceType(), item.getInstanceType(), item.getYearMonth(), item.getZoneName());
    }

    private static List<OfficialWebsiteBuyAndSoldOutDTO> convertFrom(List<OfficialWebsiteSoldOutDTO> soldOutList) {
        List<OfficialWebsiteBuyAndSoldOutDTO> res = new ArrayList<>();
        if (ListUtils.isEmpty(soldOutList)) {
            return res;
        }
        for (OfficialWebsiteSoldOutDTO sold : soldOutList) {
            OfficialWebsiteBuyAndSoldOutDTO item = new OfficialWebsiteBuyAndSoldOutDTO();
            BeanUtils.copyProperties(sold, item);
            item.calculateServiceLevel();
            res.add(item);
        }
        return res;
    }

}
