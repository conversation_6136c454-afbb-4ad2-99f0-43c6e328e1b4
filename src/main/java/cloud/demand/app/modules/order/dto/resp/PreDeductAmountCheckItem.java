package cloud.demand.app.modules.order.dto.resp;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class PreDeductAmountCheckItem {

    /** 实例类型 */
    private String instanceType;

    /** 可用区 */
    private String zoneName;

    /** 共识需求数（单位：核、卡） */
    private BigDecimal demandAmount = BigDecimal.ZERO;

    /** 历史已预扣数（单位：核、卡） */
    private BigDecimal existPreDeductAmount = BigDecimal.ZERO;

    /** 本次申请预扣数（单位：核、卡） */
    private BigDecimal reqPreDeductAmount = BigDecimal.ZERO;

    /** 本次预扣是否超量 */
    private boolean overSize = false;

    private boolean gpuProduct = false;

    public String toErrorMessage() {
        if (this.overSize) {
            if (this.gpuProduct) {
                return String.format("您本次申请的“%s+%s”预扣资源量已达到上限，无法继续预扣。"
                                + "共识需求总卡数：【%s】，历史有效预扣总卡数：【%s】，本次提交的预扣卡数：【%s】。",
                        this.instanceType, this.zoneName, this.demandAmount,
                        this.existPreDeductAmount, this.reqPreDeductAmount);
            } else {
                return String.format("您本次申请的“%s+%s”预扣资源量已达到上限，无法继续预扣。"
                                + "共识需求总核心数：【%s】，历史有效预扣总核心数：【%s】，本次提交的预扣核心数：【%s】",
                        this.instanceType, this.zoneName, this.demandAmount,
                        this.existPreDeductAmount, this.reqPreDeductAmount);
            }
        }
        return null;
    }

}
