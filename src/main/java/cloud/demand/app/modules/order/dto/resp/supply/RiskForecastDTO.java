package cloud.demand.app.modules.order.dto.resp.supply;

import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO.DeliverItem;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderItemDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.Function;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;

@Data
public class RiskForecastDTO {

    private LocalDate beginBuyDate;

    private String instanceType;

    private String zoneName;

    /**
     * 计划供应总量。可以是核心数、卡数、实例数等指标
     */
    private int total = 0;

    /** 预计满足缺口 */
    private int waitSatisfyNumForExpect = 0;

    /** 实际满足缺口 */
    private int waitSatisfyNumForActual = 0;

    /** 模拟对冲满足缺口 */
    private int waitSimulateNum = 0;

    /** 累计预扣满足缺口 */
    private int waitPreDeductNum = 0;

    private List<DeliverRecordDTO> deliverRecords = new ArrayList<>();

    private List<OrderSupplyPlanDetailDO> supplyDetails = new ArrayList<>();

    /** 预计满足 信息 */
    private List<RiskForecastItem> expectRiskList = new ArrayList<>();

    /** 实际满足 信息 */
    private List<RiskForecastItem> actualRiskList = new ArrayList<>();

    /**  模拟对冲满足信息（大盘满足时） */
    private List<RiskForecastItem> simulateRiskList = new ArrayList<>();

    /**  累计预扣满足信息（大盘满足时） */
    private List<RiskForecastItem> preDeductRiskList = new ArrayList<>();

    /** 有效预扣量 */
    private int validPreDeductNum = 0;

    /** 有效预扣的预扣明细 */
    private List<PreDeductOrderItemDO> validPreDeductList = new ArrayList<>();

    /** 是否根据预扣数据生成，当预扣信息未关联到供应方案时，需要根据预扣信息生成此风险预估数据，此时此字段值为 true，{@link #total}值为0 */
    private boolean createByPreDeduct = false;

    /**
     * 计划预扣量的分配规则：<br/>
     * 1、主key：实例类型+可用区 <br/>
     * 2、优先级：预扣开始购买时间，匹配开始购买时间，时间从小到大匹配<br/>
     * 3、匹配的计划预扣量不得超过计划供应量<br/>
     * 4、预扣量匹配不到供应的，添加无计划供应量的风险预估数据（前端展示为预扣未关联供应），并设置累计预扣满足信息<br/>
     * @param list 风险预估信息
     * @param items 订单的有效预扣明细
     * @param indexFuc 有效预扣指标函数（核心数/卡数）
     * @param actualIndexFuc 实际预扣指标函数（核心数/卡数）
     * @param isSatisfyType 是否大盘满足时，当大盘满足时，重新计算实际满足：大盘实际满足 = MAX(模拟对冲满足, 累计预扣满足)
     */
    public static void matchAddValidPreDeduct(List<RiskForecastDTO> list, List<PreDeductOrderItemDO> items,
            Function<PreDeductOrderItemDO, Number> indexFuc, Function<PreDeductOrderItemDO, Number> actualIndexFuc,
            boolean isSatisfyType) {
        if (ListUtils.isEmpty(list) || ListUtils.isEmpty(items)) {
            return;
        }
        List<PreDeductItem> preDeductItems = PreDeductItem.from(items, indexFuc, actualIndexFuc);
        Map<String, List<PreDeductItem>> mapList = ListUtils.toMapList(preDeductItems,
                o-> Strings.join("_", o.getInstanceType(), o.getZoneName()), Function.identity());
        ListUtils.sortAscNullLast(list, RiskForecastDTO::getBeginBuyDate);
        A:for (RiskForecastDTO dto : list) {
            String key = Strings.join("_", dto.getInstanceType(), dto.getZoneName());
            List<PreDeductItem> itemsForDto = mapList.get(key);
            if (ListUtils.isEmpty(itemsForDto)) {
                continue;
            }
            boolean emptyDeductRisk = ListUtils.isEmpty(dto.getPreDeductRiskList());
            ListUtils.sortAscNullLast(itemsForDto, PreDeductOrderItemDO::getStartPreDeductDate);
            for (PreDeductItem item : itemsForDto) {
                calcValidNum(item, dto);
                calcActualNum(item, dto, emptyDeductRisk);
            }
            // 大盘满足时，重新计算实际满足：大盘实际满足 = MAX(模拟对冲满足, 累计预扣满足)
            if (isSatisfyType) {
                dto.reCalcActual();
            }
        }
        Map<String, RiskForecastDTO> tmp = new HashMap<>();
        mapList.forEach((key, values) -> {
            if (ListUtils.isEmpty(values)) {
                return;
            }
            // 预扣量匹配不到供应的，添加无计划供应量的风险预估数据（前端展示为预扣未关联供应）
            for (PreDeductItem item : values) {
                if (item == null || item.getValidNum() <= 0)  {
                    continue;
                }
                RiskForecastDTO dto = tmp.get(key);
                if (dto == null) {
                    dto = new RiskForecastDTO();
                    dto.setBeginBuyDate(item.getStartPreDeductDate());
                    dto.setInstanceType(item.getInstanceType());
                    dto.setZoneName(item.getZoneName());
                    dto.setTotal(0);
                    dto.setWaitSatisfyNumForExpect(0);
                    dto.setWaitSatisfyNumForActual(0);
                    dto.setWaitSimulateNum(0);
                    dto.setWaitPreDeductNum(0);
                    dto.setValidPreDeductNum(0);
                    dto.setCreateByPreDeduct(true);
                    tmp.put(key, dto);
                    list.add(dto);
                }
                if (item.getValidNum() > 0) {
                    dto.setValidPreDeductNum(item.getValidNum() + dto.getValidPreDeductNum());
                    dto.getValidPreDeductList().add(item);
                }
                if (item.getActualNum() > 0) {
                    // 累计预扣满足信息
                    item.addToPreDeductRisk(dto);
                }
            }
        });
    }

    private void reCalcActual() {
        // 大盘满足时，重新计算实际满足：大盘实际满足 = MAX(模拟对冲满足, 累计预扣满足)
        int normalActual = NumberUtils.sum(this.simulateRiskList, RiskForecastItem::getTruthNum).intValue();
        int totalPreDeduct = NumberUtils.sum(this.preDeductRiskList, RiskForecastItem::getTruthNum).intValue();
        if (normalActual < totalPreDeduct) {
            this.actualRiskList = this.preDeductRiskList;
            this.setWaitSatisfyNumForActual(total - totalPreDeduct);
        }
    }

    private static void calcActualNum(PreDeductItem item, RiskForecastDTO dto, boolean emptyDeductRisk) {
        // 实际预扣量缺口
        int res = dto.getTotal()- dto.currentActualPreDeductNum();
        // 分配的实际预扣量
        int actualPreDeductSatisfyNum = Math.min(res, item.actualNum);
        if (actualPreDeductSatisfyNum > 0) {
            // 计算出剩余未分配的实际预扣量
            item.actualNum = item.actualNum - actualPreDeductSatisfyNum;
            if (emptyDeductRisk) {
                // 如果没有累计预扣信息，这里补充累计预扣满足信息
                item.addToPreDeductRisk(dto, actualPreDeductSatisfyNum);
            }
        }
    }

    private int currentActualPreDeductNum() {
        if (ListUtils.isEmpty(this.preDeductRiskList)) {
            return 0;
        }
        BigDecimal res = NumberUtils.sum(this.preDeductRiskList, RiskForecastItem::getTruthNum);
        return res == null ? 0 : res.intValue();
    }

    private static void calcValidNum(PreDeductItem item, RiskForecastDTO dto) {
        if (item == null || item.getValidNum() <= 0)  {
            return;
        }
        int res = dto.getTotal()- dto.getValidPreDeductNum();
        if (res <= 0) {
            return;
        }
        int preDeductRes = Math.min(item.getValidNum(), res);
        item.setValidNum(item.getValidNum() - preDeductRes);
        dto.setValidPreDeductNum(dto.getValidPreDeductNum() + preDeductRes);
        dto.getValidPreDeductList().add(item);
    }

    public static List<RiskForecastDTO> from(List<OrderSupplyPlanDetailDO> details,
            Function<OrderSupplyPlanDetailDO, Integer> indexFuc, OrderInfoDO order,
            List<DeliverRecordDTO> allDelivers) {
        if (ListUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        if (order == null) {
            throw new IllegalArgumentException("order is null");
        }

        Map<String, RiskForecastDTO> map = new HashMap<>();
        for (OrderSupplyPlanDetailDO item : details) {
            if (item == null) {
                continue;
            }
            LocalDate beginBuyDate = item.getConsensusBeginBuyDate() == null
                    ? order.getBeginBuyDate() : item.getConsensusBeginBuyDate();
            String key = key(item, beginBuyDate);
            RiskForecastDTO record = map.get(key);
            if (record == null) {
                record = new RiskForecastDTO();
                record.setBeginBuyDate(beginBuyDate);
                record.setInstanceType(item.getSupplyInstanceType());
                record.setZoneName(item.getSupplyZoneName());
            }
            record.getSupplyDetails().add(item);
            Integer indexItem = indexFuc.apply(item);
            indexItem = indexItem == null ? 0 : indexItem;
            record.setTotal(Integer.sum(indexItem, record.getTotal()));
            record.setWaitSatisfyNumForExpect(record.getTotal());
            record.setWaitSatisfyNumForActual(record.getTotal());
            record.setWaitSimulateNum(record.getTotal());
            record.setWaitPreDeductNum(record.getTotal());
            map.put(key, record);
        }
        List<RiskForecastDTO> res = ListUtils.isEmpty(map) ? new ArrayList<>() : new ArrayList<>(map.values());
        resolveDeliverRecords(allDelivers, res);
//        // 模拟对冲满足的 待满足量 需要减去 预扣量
//        for (RiskForecastDTO dto : res) {
//            dto.setWaitSimulateNum(dto.getWaitSatisfyNumForExpect() - (dto.getTotal() - dto.getWaitPreDeductNum()));
//            if (dto.getWaitSimulateNum() < 0) {
//                dto.setWaitSimulateNum(0);
//            }
//        }
        return res;
    }

    private static String key(OrderSupplyPlanDetailDO item, LocalDate beginBuyDate) {
        return String.format("%s_%s_%s", beginBuyDate == null ? "null" : beginBuyDate.toString(),
                item.getSupplyInstanceType(), item.getSupplyZoneName());
    }

    private static void resolveDeliverRecords(List<DeliverRecordDTO> allDelivers, List<RiskForecastDTO> forecasts) {
        ListUtils.sortAscNullLast(forecasts, RiskForecastDTO::getBeginBuyDate);
        Map<String, List<DeliverRecordDTO>> map = ListUtils.toMapList(allDelivers, RiskForecastDTO::keyForDeliver,
                Function.identity());
        List<SatisfyItem> satisfyExpectList = SatisfyItem.fromDeliverRecords(allDelivers,
                DeliverRecordDTO::getExpectDeliverList);
        Map<String, List<SatisfyItem>> expectMap = ListUtils.toMapList(satisfyExpectList,
                RiskForecastDTO::keyForDeliver, Function.identity());
        List<SatisfyItem> satisfyActualList = SatisfyItem.fromDeliverRecords(allDelivers,
                DeliverRecordDTO::getActualDeliverList);
        Map<String, List<SatisfyItem>> actualMap = ListUtils.toMapList(satisfyActualList,
                RiskForecastDTO::keyForDeliver, Function.identity());
        List<SatisfyItem> simulateList = SatisfyItem.fromDeliverRecords(allDelivers,
                DeliverRecordDTO::getSimulateList);
        Map<String, List<SatisfyItem>> simulateMap = ListUtils.toMapList(simulateList,
                RiskForecastDTO::keyForDeliver, Function.identity());
        List<SatisfyItem> preDeductList = SatisfyItem.fromDeliverRecords(allDelivers,
                DeliverRecordDTO::getPreDeductList);
        Map<String, List<SatisfyItem>> preDeductMap = ListUtils.toMapList(preDeductList,
                RiskForecastDTO::keyForDeliver, Function.identity());
        for (RiskForecastDTO dto : forecasts) {
            String key = keyForDeliver(dto);
            List<DeliverRecordDTO> deliver = map.get(key);
            if (ListUtils.isNotEmpty(deliver)) {
                dto.getDeliverRecords().addAll(deliver);
            }

            List<SatisfyItem> expect = expectMap.get(key);
            if (ListUtils.isNotEmpty(expect)) {
                ListUtils.sortAscNullLast(expect, SatisfyItem::getDate);
                for (SatisfyItem satisfyItem : expect) {
                    // 特殊case】如果同个分组有多个时间，
                    // 处理原则：按照共识开始购买时间从小到大排列，共识开始时间小的优先分配实际到货、时间较早的“预计到货日期”，再往后分
//                    RiskForecastItem item = RiskForecastItem.createForExpect(satisfyItem, dto);
                    RiskForecastItem item = RiskForecastItem.create(satisfyItem, dto,
                            RiskForecastDTO::getWaitSatisfyNumForExpect,
                            RiskForecastDTO::setWaitSatisfyNumForExpect);
                    if (item != null) {
                        dto.getExpectRiskList().add(item);
                    }
                }
            }

            List<SatisfyItem> actual = actualMap.get(key);
            if (ListUtils.isNotEmpty(actual)) {
                ListUtils.sortAscNullLast(actual, SatisfyItem::getDate);
                for (SatisfyItem satisfyItem : actual) {
                    // 特殊case】如果同个分组有多个时间，
                    // 处理原则：按照共识开始购买时间从小到大排列，共识开始时间小的优先分配实际到货、时间较早的“预计到货日期”，再往后分
//                    RiskForecastItem item = RiskForecastItem.createForActual(satisfyItem, dto);
                    RiskForecastItem item = RiskForecastItem.create(satisfyItem, dto,
                            RiskForecastDTO::getWaitSatisfyNumForActual,
                            RiskForecastDTO::setWaitSatisfyNumForActual);
                    if (item != null) {
                        dto.getActualRiskList().add(item);
                    }
                }
            }

//            List<SatisfyItem> preDeduct = preDeductMap.get(key);
//            if (ListUtils.isNotEmpty(preDeduct)) {
//                ListUtils.sortAscNullLast(preDeduct, SatisfyItem::getDate);
//                for (SatisfyItem satisfyItem : preDeduct) {
//                    // 特殊case】如果同个分组有多个时间，
//                    // 处理原则：按照共识开始购买时间从小到大排列，共识开始时间小的优先分配实际到货、时间较早的“预计到货日期”，再往后分
//                    RiskForecastItem item = RiskForecastItem.create(satisfyItem, dto,
//                            RiskForecastDTO::getWaitPreDeductNum,
//                            RiskForecastDTO::setWaitPreDeductNum);
//                    if (item != null) {
//                        dto.getPreDeductRiskList().add(item);
//                    }
//                }
//            }

            List<SatisfyItem> simulate = simulateMap.get(key);
            if (ListUtils.isNotEmpty(simulate)) {
                ListUtils.sortAscNullLast(simulate, SatisfyItem::getDate);
                for (SatisfyItem satisfyItem : simulate) {
                    // 特殊case】如果同个分组有多个时间，
                    // 处理原则：按照共识开始购买时间从小到大排列，共识开始时间小的优先分配实际到货、时间较早的“预计到货日期”，再往后分
                    RiskForecastItem item = RiskForecastItem.create(satisfyItem, dto,
                            RiskForecastDTO::getWaitSimulateNum,
                            RiskForecastDTO::setWaitSimulateNum);
                    if (item != null) {
                        dto.getSimulateRiskList().add(item);
                    }
                }
            }
        }
    }

    private static String keyForDeliver(DeliverRecordDTO deliver) {
        return  String.format("%s_%s", deliver.getInstanceType(), deliver.getZoneName());
    }

    private static String keyForDeliver(SatisfyItem satisfyItem) {
        return  String.format("%s_%s", satisfyItem.getInstanceType(), satisfyItem.getZoneName());
    }

    private static String keyForDeliver(RiskForecastDTO forecast) {
        return  String.format("%s_%s", forecast.getInstanceType(), forecast.getZoneName());
    }

    @Data
    @NoArgsConstructor
    public static class SatisfyItem extends DeliverItem {

        private String instanceType;

        private String zoneName;

        public static List<SatisfyItem> fromDeliverRecords(List<DeliverRecordDTO> records,
                Function<DeliverRecordDTO, List<DeliverItem>> itemsFunc)  {
            if (ListUtils.isEmpty(records)) {
                return new ArrayList<>();
            }
            List<SatisfyItem> list = new ArrayList<>();
            for (DeliverRecordDTO record : records) {
                List<DeliverItem> items = itemsFunc.apply(record);
                if (ListUtils.isEmpty(items)) {
                    continue;
                }
                for (DeliverItem item : items) {
                    if (item == null) {
                        continue;
                    }
                    SatisfyItem satisfyItem = from(record, item);
                    list.add(satisfyItem);
                }
            }
            return list;
        }

        private static SatisfyItem from(DeliverRecordDTO record, DeliverItem item)  {
            SatisfyItem res = new SatisfyItem();
            res.setNum(item.getNum());
            res.setDate(item.getDate());
            res.setInstanceType(record.getInstanceType());
            res.setZoneName(record.getZoneName());
            return res;
        }

    }


    @Data
    @NoArgsConstructor
    public static class RiskForecastItem extends SatisfyItem {

        private LocalDate beginBuyDate;

        private String tag;

        private int truthNum;


        private static RiskForecastItem create(SatisfyItem input, RiskForecastDTO forecast,
                Function<RiskForecastDTO, Integer> waitNumGetter,
                BiConsumer<RiskForecastDTO, Integer> waitNumSetter) {
            Integer waitNum = waitNumGetter.apply(forecast);
            waitNum = waitNum == null ? 0 : waitNum;
            if (waitNum <= 0 || input.getNum() <= 0) {
                // 没有剩余交付量，或者没有剩余需求量，直接返回null
                return null;
            }
            RiskForecastItem res = new RiskForecastItem();
            res.setBeginBuyDate(forecast.getBeginBuyDate());
            res.setInstanceType(forecast.getInstanceType());
            res.setZoneName(forecast.getZoneName());
            res.setNum(input.getNum());
            res.setDate(input.getDate());
            if (input.getNum() >= waitNum) {
                res.setTruthNum(waitNum);
                input.setNum(input.getNum() - waitNum);
                waitNumSetter.accept(forecast, 0); // 如 forecast.setWaitSimulateNumForActual(0);
            } else {
                res.setTruthNum(input.getNum());
                // 如 forecast.setWaitSimulateNumForActual(waitNum - input.getNum());
                waitNumSetter.accept(forecast, waitNum - input.getNum());
                input.setNum(0);
            }
            if (res.getBeginBuyDate().isBefore(res.getDate())) {
                // 开始购买日期早于交付日期，表示交付延期
                res.setTag("延期");
            } else {
                res.setTag("正常");
            }
            return res;
        }

    }

    @Data
    public static class SatisfyInfo {

        @Column("date")
        private LocalDate date;

        @Column("zone_name")
        private String zoneName;

        @Column("instance_type")
        private String instanceType;

        @Column("num")
        private BigDecimal num;

        @Column("pre_deduct_num")
        private BigDecimal preDeductNum;

        public DeliverItem toForNum(DeliverRecordDTO item) {
            if (Objects.equals(item.getZoneName(), zoneName) && Objects.equals(item.getInstanceType(), instanceType)) {
                return new DeliverItem(date, num.intValue());
            }
            return null;
        }

        public DeliverItem toForPreDeductNum(DeliverRecordDTO item) {
            if (Objects.equals(item.getZoneName(), zoneName) && Objects.equals(item.getInstanceType(), instanceType)) {
                return new DeliverItem(date, preDeductNum.intValue());
            }
            return null;
        }

        public DeliverItem toForSimulateNum(DeliverRecordDTO item) {
            if (Objects.equals(item.getZoneName(), zoneName) && Objects.equals(item.getInstanceType(), instanceType)) {
                // 模拟对冲满足： 到达开始购买日期时计算的满足度，移除其中的预扣数据
                BigDecimal simulateNum = this.num.subtract(this.preDeductNum);
                return new DeliverItem(date, simulateNum.intValue());
            }
            return null;
        }

    }

    @Data
    public static class PreDeductItem extends PreDeductOrderItemDO {

        /** 有效预扣量（计划预扣量）匹配后的剩余可用资源量 */
        private int validNum = 0;

        /** 实际预扣量匹配后的剩余可用资源量 */
        private int actualNum = 0;

        public PreDeductItem(PreDeductOrderItemDO item, Function<PreDeductOrderItemDO, Number> indexFuc,
                Function<PreDeductOrderItemDO, Number> actualIndexFuc) {
            BeanUtils.copyProperties(item, this);
            Number index = indexFuc.apply(item);
            this.validNum = index == null ? 0 : index.intValue();
            Number actualIndex = actualIndexFuc.apply(item);
            this.actualNum = actualIndex == null ? 0 : actualIndex.intValue();
        }

        public static List<PreDeductItem> from(List<PreDeductOrderItemDO> items,
                Function<PreDeductOrderItemDO, Number> indexFuc,
                Function<PreDeductOrderItemDO, Number> actualIndexFuc) {
            if (ListUtils.isEmpty(items)) {
                return new ArrayList<>();
            }
            List<PreDeductItem> list = new ArrayList<>();
            for (PreDeductOrderItemDO item : items) {
                PreDeductItem res = new PreDeductItem(item, indexFuc, actualIndexFuc);
                list.add(res);
            }
            return list;
        }

        /**
         *  预扣计划量未关联到供应的，添加到对应累计预扣满足信息
         * @param forecast 未关联到供应的预扣信息生成的风险预估对象
         */
        public void addToPreDeductRisk(RiskForecastDTO forecast) {
            if (forecast == null) {
                return;
            }
            if (forecast.getPreDeductRiskList() == null) {
                forecast.setPreDeductRiskList(new ArrayList<>());
            }
            RiskForecastItem item;
            if (forecast.getPreDeductRiskList().isEmpty()) {
                item = new RiskForecastItem();
                item.setBeginBuyDate(forecast.getBeginBuyDate());
                item.setInstanceType(forecast.getInstanceType());
                item.setZoneName(forecast.getZoneName());
                item.setNum(0);
                item.setDate(forecast.getBeginBuyDate());
                item.setTruthNum(0);
                item.setTag("正常");
                forecast.getPreDeductRiskList().add(item);
            } else {
                item = forecast.getPreDeductRiskList().get(0);
            }
            item.setNum(item.getNum() + actualNum);
            item.setTruthNum(item.getTruthNum() + actualNum);
        }

        /**
         *  预扣计划量到供应的，原风险预估信息没有累计预扣满足信息的，添加累计预扣满足信息
         * @param forecast 关联到供应的预扣信息生成的风险预估对象，且没有累计预扣满足信息的（需要在调用方进行判断）
         * @param actualPreDeductSatisfyNum 匹配到此风险预估对象的 实际预扣量 部分
         */
        public void addToPreDeductRisk(RiskForecastDTO forecast, int actualPreDeductSatisfyNum)  {
            if (forecast == null) {
                return;
            }
            if (forecast.getPreDeductRiskList() == null) {
                forecast.setPreDeductRiskList(new ArrayList<>());
            }
            RiskForecastItem item;
            if (forecast.getPreDeductRiskList().isEmpty()) {
                item = new RiskForecastItem();
                item.setBeginBuyDate(forecast.getBeginBuyDate());
                item.setInstanceType(forecast.getInstanceType());
                item.setZoneName(forecast.getZoneName());
                item.setNum(0);
                item.setDate(forecast.getBeginBuyDate());
                item.setTruthNum(0);
                item.setTag("正常");
                forecast.getPreDeductRiskList().add(item);
            } else {
                item = forecast.getPreDeductRiskList().get(0);
            }
            // 分配的实际预扣量
            item.setNum(item.getNum() + actualPreDeductSatisfyNum);
            item.setTruthNum(item.getTruthNum() + actualPreDeductSatisfyNum);
        }

    }

}
