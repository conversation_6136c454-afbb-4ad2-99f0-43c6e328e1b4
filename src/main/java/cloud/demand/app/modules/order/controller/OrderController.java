package cloud.demand.app.modules.order.controller;


import cloud.demand.app.common.excel.LocalDateStringConverter;
import cloud.demand.app.common.excel.LocalTimeStringConverter;
import cloud.demand.app.common.excel.YearMonthStringConverter;
import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO;
import cloud.demand.app.modules.order.dto.req.ConsensusDemandDetailReq;
import cloud.demand.app.modules.order.dto.req.ConsensusDetailReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemForCreateChoose;
import cloud.demand.app.modules.order.dto.req.GlobalApprovalReq;
import cloud.demand.app.modules.order.dto.req.OneOrderDetailForExportReq;
import cloud.demand.app.modules.order.dto.req.OperatePreDeductOrderReq;
import cloud.demand.app.modules.order.dto.req.OrderAutoPreDeductReq;
import cloud.demand.app.modules.order.dto.req.OrderChangeLabelReq;
import cloud.demand.app.modules.order.dto.req.OrderChangeSubmitAndArchitectReq;
import cloud.demand.app.modules.order.dto.req.OrderDetailReq;
import cloud.demand.app.modules.order.dto.req.OrderElasticConfigReq;
import cloud.demand.app.modules.order.dto.req.OrderInputYearMonthReq;
import cloud.demand.app.modules.order.dto.req.OrderItemPlacementGroupModifyReq;
import cloud.demand.app.modules.order.dto.req.OrderLogIdReq;
import cloud.demand.app.modules.order.dto.req.OrderNumberWithIdsReq;
import cloud.demand.app.modules.order.dto.req.OrderOperateReq;
import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq;
import cloud.demand.app.modules.order.dto.req.PplOrder2XyPurchaseOrderReq;
import cloud.demand.app.modules.order.dto.req.PreDeductDelayReq;
import cloud.demand.app.modules.order.dto.req.QueryPplOrderItemForXyReq;
import cloud.demand.app.modules.order.dto.req.QueryRoleUsersForOrderReq;
import cloud.demand.app.modules.order.dto.req.StartCancelFlowReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanIdReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanSaveReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.OrderItemDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderLogJoinFlowRecordDTO;
import cloud.demand.app.modules.order.dto.resp.PreDeductAmountCheckItem;
import cloud.demand.app.modules.order.dto.resp.QueryPplOrderItemForXyResp;
import cloud.demand.app.modules.order.dto.resp.SubProcessResp;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderLabelEnum;
import cloud.demand.app.modules.order.enums.OrderLabelReasonEnum;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.PerformanceTrackService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderAdvanceWeekFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemPreDeductFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemSupplyPlanFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/order")
@Slf4j
public class OrderController {

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private OrderOperateService orderOperateService;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private SupplyPlanOperateService supplyPlanOperateService;

    @Resource
    private FillerService fillerService;

    @Resource
    private DictService dictService;

    @Resource
    private PerformanceTrackService performanceTrackService;

    /**
     * 订单列表通用查询接口
     */
    @RequestMapping
    public Object queryOrder(@JsonrpcParam OrderQueryReq req) {
        return orderCommonService.queryOrder(req);
    }

    /**
     * 订单列表通用分页查询接口
     */
    @RequestMapping
    public Object pageOrder(@JsonrpcParam OrderQueryReq req) {
        return orderCommonService.pageOrder(req);
    }

    /**
     * 订单概览查询接口
     */
    @RequestMapping
    public Object queryOrderSummary(@JsonrpcParam OrderQueryReq req) {
        return orderCommonService.queryOrderSummary(req);
    }

    /**
     * 订单列表详情导出
     */
    @RequestMapping
    public DownloadBean orderListExport(@JsonrpcParam OrderQueryReq req) {
        List<OrderDetailResp> orders = orderCommonService.queryOrder(req);
        return orderDetailExport(orders, "订单列表详情导出");
    }
    

    /**
     * 单个订单详情导出
     */
    @RequestMapping
    public DownloadBean oneOrderDetailExport(@JsonrpcParam OneOrderDetailForExportReq req) {
        OrderDetailResp order;
        if (req.getNeedBeforeConsensus() == null || !req.getNeedBeforeConsensus()) {
            order = orderCommonService.queryOrderDetail(req.getOrderNumber());
        } else {
            order = orderCommonService.queryOrderDetailBeforeConsensus(req.getOrderNumber(), null);
        }
        return orderDetailExport(ListUtils.newArrayList(order), req.getOrderNumber() + "订单详情导出");
    }

    private DownloadBean orderDetailExport(List<OrderDetailResp> orders, String fileNamePrefix) {
        List<OrderItemDetailResp> data = OrderDetailResp.toItemDetailResp(orders);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/order/order_detail_export.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .registerConverter(new YearMonthStringConverter("yyyy-MM"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        List<OrderItemDTO> allItem = ListUtils.transform(data, OrderItemDetailResp::getItemInfo);
        fillerService.fill(allItem, OrderItemPreDeductFiller.class);
        fillerService.fill(allItem, OrderItemSupplyPlanFiller.class);
        fillerService.fill(allItem, OrderAdvanceWeekFiller.class);

        if (ListUtils.isEmpty(data)) {
            List<OrderItemDTO> item = new ArrayList<>();
            List<OrderDetailResp> order = new ArrayList<>();
            excelWriter.fill(new FillWrapper("item", item), writeSheet)
                    .fill(new FillWrapper("order", order), writeSheet);
        } else {
            // 分片进行excel数据的填充，
            // 解决多个fill进行excel数据填充时（若一次性填充的数据条数大于500时），部分数据被覆盖为空的bug，
            // 原因见 https://zhiyan.woa.com/requirement/6756/bug/#/cloudrm-3027?tab=info&story_tab=info
            BatchUtil.syncBatchExec(data, 400, batch -> {
                List<OrderItemDTO> item = new ArrayList<>();
                List<OrderDetailResp> order = new ArrayList<>();
                for (OrderItemDetailResp datum : batch) {
                    item.add(datum.getItemInfo());
                    order.add(datum.getOrderInfo());
                }
                excelWriter
                        .fill(new FillWrapper("item", item), writeSheet)
                        .fill(new FillWrapper("order", order), writeSheet);
            });
        }

        excelWriter.finish();
        String fileName = fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    /**
     * 共识前订单详情查询接口
     */
    @RequestMapping
    public Object queryOrderDetailBeforeConsensus(@JsonrpcParam OrderDetailReq req) {
        OrderDetailResp resp = orderCommonService.queryOrderDetailBeforeConsensus(req.getOrderNumber(),
                req.getFlowNo());
        if (resp == null) {
            throw BizException.makeThrow("订单不存在，订单号【%s】", req.getOrderNumber());
        }
        return resp;
    }


    /**
     * 订单详情查询接口
     */
    @RequestMapping
    public Object queryOrderDetail(@JsonrpcParam OrderDetailReq req) {
        OrderDetailResp resp;
        if (StringUtils.isNotBlank(req.getFlowNo())) {
            resp = orderCommonService.queryOrderDetailCheckDataAuth(req.getOrderNumber(), req.getFlowNo());
        } else {
            resp = orderCommonService.queryOrderDetailCheckDataAuth(req.getOrderNumber());
        }
        if (resp == null) {
            throw BizException.makeThrow("订单不存在，订单号【%s】", req.getOrderNumber());
        }
        return resp;
    }

    /**
     * 查询订单变化过程
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object queryOrderChangeRecord(@JsonrpcParam OrderDetailReq req) {
        return orderCommonService.queryOrderChangeRecord(req.getOrderNumber());
    }

    /**
     * 查询流程节点信息
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object queryProcessLine(@JsonrpcParam OrderDetailReq req) {
        if (StringUtils.isBlank(req.getFlowNo())) {
            throw new BizException("缺失流程号flowNo,无法查询流程节点信息");
        }
        return orderCommonService.queryProcessLine(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 查询订单操作日志
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object queryOrderLog(@JsonrpcParam OrderDetailReq req) {
        return orderCommonService.queryOrderLog(req.getOrderNumber(), req.getFlowNo());
    }


    /**
     * 查询进行中的子流程
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object queryProcessingSubProcess(@JsonrpcParam OrderDetailReq req) {
        return orderCommonService.queryProcessingSubProcess(req.getOrderNumber());
    }


    /**
     * 撤回订单
     */
    @RequestMapping
    public Object withdrawOrder(@JsonrpcParam OrderOperateReq req) {
        orderOperateService.withdrawOrder(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 保存新订单草稿
     */
    @RequestMapping
    public Object saveNewOrderDraft(@JsonrpcParam OrderSaveReq req) {
        return orderOperateService.saveNewOrderDraft(req);
    }

    /**
     * 修改草稿
     */
    @RequestMapping
    public Object modifyOrderDraft(@JsonrpcParam OrderSaveReq req) {
        return orderOperateService.modifyOrderDraft(req);
    }

    /**
     * 提交草稿
     */
    @RequestMapping
    public Object submitDraft(@JsonrpcParam OrderSaveReq req) {
        return orderOperateService.submitDraft(req.getOrderNumber(), null);
    }

    /**
     * 保存并提交，复合操作，具体逻辑如下 <br/>
     * 订单号为空时，先调用{@link #saveNewOrderDraft(OrderSaveReq)}，再调用{@link OrderOperateService#submitDraft(String, String)}
     * <br/>
     * 订单号不为空时，先调用{@link #modifyOrderDraft(OrderSaveReq)}，再调用{@link OrderOperateService#submitDraft(String, String)}
     */
    @RequestMapping
    public Object saveAndSubmitDraft(@JsonrpcParam OrderSaveReq req) {
        return orderOperateService.saveAndSubmitDraft(req);
    }

    /**
     * 删除草稿
     */
    @RequestMapping
    public Object deleteDraft(@JsonrpcParam OrderOperateReq req) {
        orderCommonOperateService.deleteDraft(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }


    /**
     * 前端通用审批方法
     */
    @RequestMapping
    public Object approvalDemandInMainFlow(@JsonrpcParam GlobalApprovalReq req) {
        if (OrderFlowEnum.ORDER_UPDATE_DEMAND.getCode().equals(req.getFlowCode())
                || OrderFlowEnum.ORDER_URGENT_DELAY.getCode().equals(req.getFlowCode())) {
            // 订单修改、加急延期的云运管审批
            orderOperateService.modifyOrderApproval(req);
        } else if (OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode().equals(req.getFlowCode())) {
            // 供应方案制定的 cbs 审批
            supplyPlanOperateService.cbsApproval(req);
        } else if (OrderFlowEnum.ORDER_CANCEL.getCode().equals(req.getFlowCode())
                || OrderFlowEnum.DATABASE_ORDER_CANCEL.getCode().equals(req.getFlowCode())) {
            // 取消订单的 云运管审批
            orderOperateService.cancelOrderApproval(req);
        } else if (OrderFlowEnum.ORDER_WITHHOLDING.getCode().equals(req.getFlowCode())
                || OrderFlowEnum.EKS_ORDER_WITHHOLDING.getCode().equals(req.getFlowCode())
                || OrderFlowEnum.EMR_ORDER_WITHHOLDING.getCode().equals(req.getFlowCode())) {
            // 发起预扣的云运管审批
            orderOperateService.preDeductOrderApproval(req);
        } else {
            orderOperateService.approvalDemandInMainFlow(req);
        }
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 停止当前进行中的子流程
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object stopProcessingSubFlow(@JsonrpcParam GlobalApprovalReq req) {
        if (StringUtils.isEmpty(req.getOrderNumber())) {
            throw new BizException("订单号不能为空");
        }
        SubProcessResp subProcessResp = orderCommonService.queryProcessingSubProcess(req.getOrderNumber());
        if (!subProcessResp.getIsExist()) {
            throw new BizException("当前无进行中的子流程");
        }
        orderCommonOperateService.stopProcessingSubFlow(req.getOrderNumber(), "用户撤回进行中的子流程", true);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 发起取消流程
     */
    @RequestMapping
    public Object startCancelOrderSubFlow(@JsonrpcParam StartCancelFlowReq req) {
        orderOperateService.startCancelOrderSubFlow(req.getOrderNumber(), req.getFlowRemark());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 发起预扣流程
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object createNewPreDeductOrder(@JsonrpcParam CreatePreDeductPlanReq req) {
        preDeductOrderService.createNewPreDeductOrder(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 取消预扣流程
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object cancelPreDeductOrder(@JsonrpcParam OperatePreDeductOrderReq req) {
        if (ListUtils.isEmpty(req.getReservationFormIdList())) {
            throw new BizException("预扣单云霄ID不能为空");
        }
        preDeductOrderService.cancelPreDeductOrder(req.getOrderNumber(), req.getReservationFormIdList());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 查询已生效的预扣详情
     *
     * @param req
     * @return
     */
    @RequestMapping
    public Object queryPreDeductOrderDetail(@JsonrpcParam OperatePreDeductOrderReq req) {
        return preDeductOrderService.queryPreDeductOrderDetail(req.getOrderNumber(), req.getPreDeductPlanNumber());
    }

    @RequestMapping
    public Object createYunxiaoPreDeductOrder() {
        preDeductOrderService.createYunxiaoPreDeductOrder();
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object syncYunxiaoPreDeductOneOrder(@JsonrpcParam OrderDetailReq req) {
        preDeductOrderService.syncYunxiaoPreDeductOneOrder(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }


    @RequestMapping
    public Object queryConsensusList(@JsonrpcParam ConsensusDetailReq req) {
        return supplyPlanQueryService.queryConsensusList(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 加急延期子流程详细信息
     */
    @RequestMapping
    public Object urgentDelayFlowInfo(@JsonrpcParam OrderDetailReq req) {
        return orderCommonService.urgentDelayFlowInfo(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 订单修改子流程详细信息
     */
    @RequestMapping
    public Object orderModifyFlowInfo(@JsonrpcParam OrderDetailReq req) {
        return orderCommonService.orderModifyFlowInfo(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 修改订单发起
     */
    @RequestMapping
    public Object modifyOrder(@JsonrpcParam OrderSaveReq req) {
        orderOperateService.modifyOrder(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 加急延期发起
     */
    @RequestMapping
    public Object urgentDelay(@JsonrpcParam OrderUrgentDelayReq req) {
        orderOperateService.urgentDelay(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 供应方案待分配数据
     */
    @RequestMapping
    public Object demandDetailForWaitSupply(@JsonrpcParam OrderDetailReq req) {
        return supplyPlanQueryService.demandDetailForWaitSupply(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 供应方案以及交付进度
     */
    @RequestMapping
    public Object supplyProgress(@JsonrpcParam OrderDetailReq req) {
        return supplyPlanOperateService.supplyProgressAndHandler(req.getOrderNumber(), req.getFlowNo());
    }

    /**
     * 保存供应方案
     */
    @RequestMapping
    public Object saveSupplyPlan(@JsonrpcParam SupplyPlanSaveReq req) {
        supplyPlanOperateService.saveSupplyPlan(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 提交供应方案
     */
    @RequestMapping
    public Object submitSupplyPlan(@JsonrpcParam SupplyPlanSaveReq req) {
        return supplyPlanOperateService.submitSupplyPlan(req);
    }

    /**
     * 删除供应方案明细
     */
    @RequestMapping
    public Object deleteSupplyPlanDetail(@JsonrpcParam OrderNumberWithIdsReq req) {
        supplyPlanOperateService.deleteSupplyPlanDetail(req.getIds(), req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 删除供应方案（满足方式）
     */
    @RequestMapping
    public Object deleteSupplyPlan(@JsonrpcParam OrderNumberWithIdsReq req) {
        supplyPlanOperateService.deleteSupplyPlan(req.getIds(), req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 批量共识
     */
    @RequestMapping
    public Object batchConsensus(@JsonrpcParam ConsensusReq req) {
        supplyPlanOperateService.batchConsensus(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 共识完成
     */
    @RequestMapping
    public Object consensusOver(@JsonrpcParam OrderOperateReq req) {
        supplyPlanOperateService.consensusOver(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 供应方案版本变更记录
     */
    @RequestMapping
    public Object planHistoryVersionList(@JsonrpcParam OrderOperateReq req) {
        return supplyPlanQueryService.planVersionList(req.getOrderNumber());
    }

    /**
     * 根据供应方案明细id修改供应方案明细的业务单号
     */
    @RequestMapping
    public Object updateSupplyPlanDetailBizId(@JsonrpcParam SupplyPlanMatchWayDTO req) {
        supplyPlanOperateService.updateSupplyPlanDetailBizId(req.getDetails(), req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 将供应方案的完成到供应交付
     */
    @RequestMapping
    public Object supplyPlanFlowExec(@JsonrpcParam OrderOperateReq req) {
        supplyPlanOperateService.supplyPlanFlowExec(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 根据订单日志id查询单个订单日志
     */
    @RequestMapping
    public OrderLogJoinFlowRecordDTO queryOneLog(@JsonrpcParam OrderLogIdReq req) {
        return orderCommonService.queryOneLog(req.getLogId());
    }

    /**
     * 根据订单记录查询方案制定的可处理人
     */
    @RequestMapping
    public Object queryPlanProcessorByOrderRecord(@JsonrpcParam OrderDetailReq req) {
        return supplyPlanQueryService.queryPlanProcessorByOrderRecord(req.getOrderNumber());
    }

    /**
     * 供应方案修改子流程 - 方案修改提交前的检查，返回待共识信息
     */
    @RequestMapping
    public Object beforeSubmitCheckForModifyPlan(@JsonrpcParam SupplyPlanSaveReq req) {
        return supplyPlanOperateService.beforeSubmitCheckForModifyPlan(req);
    }

    /**
     * 供应方案修改子流程 - 方案修改提交
     */
    @RequestMapping
    public Object submitPlanForModifyPlan(@JsonrpcParam SupplyPlanSaveReq req) {
        supplyPlanOperateService.submitPlanForModifyPlan(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 查询带订单明细信息的生效的方案明细信息
     */
    @RequestMapping
    public Object getAvailableDetailWithPlan(@JsonrpcParam OrderOperateReq req) {
        return supplyPlanQueryService.getAvailableSupplyDetailWithOrderItem(req.getOrderNumber());
    }

    /**
     * 提交产品预扣
     */
    @RequestMapping
    public Object createProductPreDeduct(@JsonrpcParam CreatePreDeductPlanReq req) {
        preDeductOrderService.createProductPreDeduct(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 前端查询生效中订单的某些角色处理人
     */
    @RequestMapping
    public Object queryRoleUsersForOrder(@JsonrpcParam QueryRoleUsersForOrderReq req) {
        return orderCommonService.queryRoleUsersForOrder(req);
    }

    /**
     * 自动预扣设置的修改
     */
    @RequestMapping
    public Object modifyAutoPreDeduct(@JsonrpcParam OrderAutoPreDeductReq req) {
        orderOperateService.modifyAutoPreDeduct(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 预扣延期
     */
    @RequestMapping
    public Object preDeductDelay(@JsonrpcParam PreDeductDelayReq req) {
        req.setOperator(LoginUtils.getUserNameWithSystem());
        preDeductOrderService.preDeductDelay(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 订单订阅
     */
    @RequestMapping
    public Object subscribe(@JsonrpcParam OrderDetailReq req) {
        orderCommonOperateService.subscribe(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 取消订阅
     */
    @RequestMapping
    public Object cancelSubscribe(@JsonrpcParam OrderDetailReq req) {
        orderCommonOperateService.cancelSubscribe(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 查询主力机型
     */
    @RequestMapping
    public Object queryMainInstanceType() {
        return dictService.queryMainInstanceType();
    }

    /**
     * 查询主力园区
     */
    @RequestMapping
    public Object queryMainZone() {
        return dictService.queryMainZone();
    }

    /**
     * 处理迁移数据
     */
    @RequestMapping
    public Object createHistoryPreDeductPlan(@JsonrpcParam OrderDetailReq req) {
        preDeductOrderService.createHistoryPreDeductPlan(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 订单关闭
     */
    @RequestMapping
    public Object pushToOrderClose(@JsonrpcParam OrderDetailReq req) {
        Optional<String> res = orderOperateService.pushToOrderClose(req.getOrderNumber());
        if (res.isPresent()) {
            // 抛出订单关闭失败的原因
            throw BizException.makeThrow(res.get());
        }
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 采购单可关联的订单列表-带明细
     */
    @RequestMapping
    public Object queryPplOrderItemForXy(@JsonrpcParam QueryPplOrderItemForXyReq req) {
        List<QueryPplOrderItemForXyResp> result = orderCommonService.queryPplOrderItemForXy(req);
        if (ListUtils.isNotEmpty(req.getIds()) && ListUtils.isEmpty(result)) {
            throw new BizException("指定的供应方案明细id不存在或者已经失效，请重新指定");
        }
        return result;
    }


    /**
     * 订单关联采购单信息
     */
    @RequestMapping
    public Object bindXyPurchaseForPplOrder(@JsonrpcParam PplOrder2XyPurchaseOrderReq req) {
        orderCommonOperateService.bindXyPurchaseForPplOrder(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 补扣
     */
    @RequestMapping
    public Object completePreDeductForOrder(@JsonrpcParam OrderDetailReq req) {
        preDeductOrderService.completePreDeductForOrder(req.getOrderNumber());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 修改订单明细的置放群组
     */
    @RequestMapping
    public Object modifyPlacementGroup(@JsonrpcParam OrderItemPlacementGroupModifyReq req) {
        orderOperateService.modifyPlacementGroup(req);
        return ImmutableMap.of("result", "ok");
    }

    /**
     *修改订单提交人和负责人
     */
    @RequestMapping
    public Object modifyOrderSubmitAndArchitect(@JsonrpcParam OrderChangeSubmitAndArchitectReq req) {
        if (ListUtils.isEmpty(req.getOrderNumbers())) {
            throw new BizException("修改订单数为0");
        }
        if (StringTools.isBlank(req.getSubmit()) && StringTools.isBlank(req.getArchitect()) && ListUtils.isEmpty(req.getAddOrderFollower())) {
            throw new BizException("移交后提单人与负责人均为空");
        }
        orderCommonOperateService.modifyOrderSubmitAndArchitect(req.getOrderNumbers(), req.getSubmit(), req.getArchitect(),req.getAddOrderFollower());
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object modifyOrderLabel(@JsonrpcParam @Valid OrderChangeLabelReq req) {
        if (ListUtils.isEmpty(req.getOrderNumbers())) {
            throw new BizException("修改订单数为0");
        }
        orderCommonOperateService.modifyOrderLabel(req);
        return ImmutableMap.of("result", "ok");
    }




    /**
     * 查询版本录入月份
     */
    @RequestMapping
    public Object queryInputVersionYearMonth(@JsonrpcParam OrderInputYearMonthReq req) {
        return orderCommonService.queryInputVersionYearMonth(req);
    }

    /**
     * 交付记录跟踪信息、风险预估信息，支持大盘满足、采购满足、搬迁满足
     */
    @RequestMapping
    public Object deliverTrackAndRiskForecast(@JsonrpcParam SupplyPlanIdReq req) {
        return supplyPlanQueryService.deliverTrackAndRiskForecast(req.getPlanId());
    }

    /**
     * 供应方案制定时，预览交付记录跟踪信息、风险预估信息，支持大盘满足、采购满足、搬迁满足
     */
    @RequestMapping
    public Object previewDeliverTrackAndRiskForecast(@JsonrpcParam SupplyPlanMatchWayDTO req) {
        return supplyPlanQueryService.deliverTrackAndRiskForecast(req);
    }

    /**
     * 查询订单最新的共识需求明细
     */
    @RequestMapping
    public Object queryConsensusDemandDetail(@JsonrpcParam ConsensusDemandDetailReq req) {

        return supplyPlanQueryService.getAvailableConsensusDemandDetail(req.getOrderNumber());
    }

    /**
     * 用户申请预扣时的可勾选进行预扣的数据，根据供应方案（相当于共识需求）生成、预扣数据生成
     */
    @RequestMapping
    public List<PreDeductItemForCreateChoose> preDeductItemForCreateChoose(@JsonrpcParam OrderDetailReq req) {
        return supplyPlanQueryService.preDeductItemForCreateChoose(req.getOrderNumber(),false);
    }

    /**
     * 根据创建预扣的入参信息和当前数据库中的预扣信息，来对比共识需求，判断实例类型、可用区维度下总预扣量是否超共识需求量
     */
    @RequestMapping
    public List<PreDeductAmountCheckItem> checkPreDeductAmount(@JsonrpcParam CreatePreDeductPlanReq req) {
        return preDeductOrderService.checkPreDeductAmount(req);
    }

    /**
     * 修改订单的预扣备选uin
     */
    @RequestMapping
    public Object modifyOrderShareUin(@JsonrpcParam OrderSaveReq req) {
        orderOperateService.modifyOrderShareUin(req.getOrderNumber(), req.getShareUinList());
        return ImmutableMap.of("result", "ok");
    }

    /**
     * 云霄共享预扣uin 列表
     */
    @RequestMapping
    public Object shareUinList(@JsonrpcParam OrderSaveReq req) {
        return preDeductOrderService.shareUinList(req.getCustomerUin(), req.getIndustryDept());
    }

    /**
     * 查询弹性预扣阶段
     */
    @RequestMapping
    public Object generateElasticCycleConfig(@JsonrpcParam OrderElasticConfigReq req) {
        return orderCommonService.generateElasticPreDeductDTO(req.getElasticType(),req.getBeginBuyDate(),req.getEndBuyDate(),
                req.getBeginElasticDate(),req.getEndElasticDate(),
                req.getBeginElasticCycle(),req.getEndElasticCycle(),true);
    }
}
