package cloud.demand.app.modules.order.dto.resp.supply;

import java.util.List;
import lombok.Data;

@Data
public class DeliverTrackAndRiskForecastDTO {

    private List<DeliverRecordDTO> deliverRecords;

    private List<RiskForecastDTO> riskForecasts;

    public static DeliverTrackAndRiskForecastDTO from(List<DeliverRecordDTO> deliverRecords,
            List<RiskForecastDTO> riskForecasts) {
        DeliverTrackAndRiskForecastDTO result = new DeliverTrackAndRiskForecastDTO();
        result.setDeliverRecords(deliverRecords);
        result.setRiskForecasts(riskForecasts);
        return result;
    }

}
