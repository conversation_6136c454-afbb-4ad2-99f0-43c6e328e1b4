package cloud.demand.app.modules.order.dto.resp.satisfy;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class IndustrySatisfyGapDTO {

    @Column("industry_dept")
    private String industryDept;

    @Column("customer_short_name")
    private String customerShortName;

    @Column("all_project_name")
    private String allProjectName;

    @Column("all_instance_type")
    private String allInstanceType;

    @Column("all_zone_name")
    private String allZoneName;

    @Column("gap_order_count")
    private Integer gapOrderCount;

    @Column("total_gap_core")
    private BigDecimal totalGapCore;

}
