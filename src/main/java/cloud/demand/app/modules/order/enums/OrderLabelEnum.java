package cloud.demand.app.modules.order.enums;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.order.dto.req.IOrderLabelReq;
import cloud.demand.app.modules.order.entity.BasOrderLabelReasonDO;
import cloud.demand.app.modules.order.service.OrderCommonService;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

/**
 * 订单标签枚举类，表：【bas_order_label_info】【bas_order_label_reason】
 * */
@AllArgsConstructor
@Getter
public enum OrderLabelEnum {
    NO_TAG("不打标","",true,false),
    ;
    /** 标签，默认(空值) */
    private final String label;

    private final String desc;

    private final boolean isEnable;
    /** 是否需要原因 */
    private final boolean needReason;


    /** 获取展示的标签 */
    public static List<OrderLabelItem> getAllLabels() {
        List<OrderLabelItem> ret = SpringUtil.getBean(OrderCommonService.class).getOrderLabels().stream()
                .map(item -> new OrderLabelItem(item.getLabel(), item.getDesc(), item.getIsEnable(),
                        ListUtils.isNotEmpty(item.getReasonDOList()),item.getReasonDOList())).collect(Collectors.toList());
        ret.add(new OrderLabelItem(NO_TAG.getLabel(), NO_TAG.getDesc(), NO_TAG.isEnable, false, null));
        return ret;
    }

    /**
     * 校验标签/原因是否合法
     * @param req 请求参数
     */
    public static void checkAndFill(IOrderLabelReq req) {
        String orderLabel = req.getOrderLabel();
        String labelReason = req.getLabelReason();
        if (StringUtils.isBlank(orderLabel)){
            throw new BizException("订单标签不能为空");
        }

        List<OrderLabelItem> labels = getAllLabels();
        Map<String, OrderLabelItem> map = ListUtils.toMap(labels, OrderLabelItem::getLabel,
                item -> item);

        if (!map.containsKey(orderLabel)){
            throw new BizException(String.format("订单标签【%s】不存在",orderLabel));
        }
        OrderLabelItem orderLabelItem = map.get(orderLabel);
        if (BooleanUtils.isNotTrue(orderLabelItem.getIsEnable())){
            throw new BizException(String.format("订单标签【%s】已被禁用",orderLabel));
        }
        if (BooleanUtils.isTrue(orderLabelItem.getNeedReason())){
            if (StringUtils.isBlank(labelReason)){
                throw new BizException("订单标签【"+orderLabel+"】需要原因");
            }
            if (ListUtils.isNotEmpty(orderLabelItem.getReasonList())){
                BasOrderLabelReasonDO reasonDO = orderLabelItem.getReasonList().stream()
                        .filter(item -> Objects.equal(item.getReason(), labelReason)).findFirst().orElse(null);
                if (reasonDO == null){
                    throw new BizException("订单标签【"+orderLabel+"】原因不存在");
                }
                if (BooleanUtils.isNotTrue(reasonDO.getIsEnable())){
                    throw new BizException("订单标签【"+orderLabel+"】原因已被禁用");
                }
            }
        }
        req.setOrderLabelItem(orderLabelItem);
    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class OrderLabelItem {
        /** 标签，默认(空值) */
        private String label;

        private String desc;

        private Boolean isEnable;
        /** 是否需要原因 */
        private Boolean needReason;

        private List<BasOrderLabelReasonDO> reasonList;
    }
}
