package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.constant.FlowStatusEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.operation_view.inventory_health.enums.YuxniaoGridStatusEnum;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.convert.NoticeParamsUtil;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig.PreDeductPlanTime;
import cloud.demand.app.modules.order.dto.GridOccupyTimeDTO;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemDTO;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemForCreateChoose;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.OrderFlowStartReq;
import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq;
import cloud.demand.app.modules.order.dto.req.OrderUrgentDelayReq.UrgentDelayItem;
import cloud.demand.app.modules.order.dto.req.PreDeductDelayReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithFlowDTO;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.OrderLogResp;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.PreDeductAmountCheckItem;
import cloud.demand.app.modules.order.dto.resp.PreDeductOrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.PreDeductPlanDTO;
import cloud.demand.app.modules.order.dto.resp.PreDeductPlanResp;
import cloud.demand.app.modules.order.dto.resp.ShareUinResp;
import cloud.demand.app.modules.order.entity.GridOccupyTimeDO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.entity.OrderLogDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderItemDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderLogDO;
import cloud.demand.app.modules.order.entity.PreDeductPlanDO;
import cloud.demand.app.modules.order.entity.std_table.AwsOrderGridDetailDfDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSubFlowNoPrefixEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import cloud.demand.app.modules.order.enums.PplProductToOrderProductMapEnum;
import cloud.demand.app.modules.order.enums.PreDeductOrderStatusEnum;
import cloud.demand.app.modules.order.enums.PreDeductPlanStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderFlowService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.order.service.function.impl.DefaultOrderItemCreator;
import cloud.demand.app.modules.order.service.function.impl.EksPreDeductCurrentProcessorCreator;
import cloud.demand.app.modules.order.service.function.impl.OrderCategoryToProductCurrentProcessorCreator;
import cloud.demand.app.modules.p2p.industry_demand.dto.UinNameDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreatePreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreatePreDeductOrderResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.DestroyPreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GetPreDeductOrderDetailResultResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GridQueryResp.Grid;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductData;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductRenewalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryPreDeductOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.RenewalPreDeductResp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoAppRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.sop_device.utils.LocalDateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PreDeductOrderServiceImpl implements PreDeductOrderService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private OrderOperateService orderOperateService;

    @Resource
    private FlowService flowService;

    @Resource
    private YunxiaoAPIService yunxiaoAPIService;

    @Resource
    private DictService dictService;

    @Resource
    private PplDictService ppldictService;

    @Resource
    private TaskLogService taskLogService;

    @Resource
    private SupplyPlanOperateService supplyPlanOperateService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private DefaultOrderItemCreator defaultOrderItemCreator;

    @Resource
    EksPreDeductCurrentProcessorCreator eksPreDeductCurrentProcessorCreator;

    @Resource
    OrderCategoryToProductCurrentProcessorCreator orderCategoryToProductCurrentProcessorCreator;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;


    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    static ExecutorService executor = Executors.newFixedThreadPool(10);

    // 订单屏蔽云霄校验可用区
    private static DynamicProperty<String> orderBanZone = DynamicProperty.create("order_ban_zone", "na-ashburn-1;na-ashburn-2");


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 500,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void createNewPreDeductOrder(CreatePreDeductPlanReq req) {
        String mainOrderNumber = req.getOrderNumber();
        final String user = LoginUtils.getUserNameWithSystem();

        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(mainOrderNumber);
        if (orderDetailResp == null) {
            throw BizException.makeThrow("操作失败，订单号【%s】不存在", mainOrderNumber);
        }

        if (OrderTypeEnum.ELASTIC.getCode().equals(orderDetailResp.getOrderType())
                && OrderElasticType.getWeekMonthElasticType().contains(orderDetailResp.getElasticType())){
            throw BizException.makeThrow("周弹性、月弹性需求，仅允许系统自动发起预扣");
        }

        // 有订单查询权限的都可以发起预扣
        orderCommonService.checkOrderQueryAuth(orderDetailResp);

        // 预扣前检查
        checkPreDeducted(orderDetailResp, req);
        // 可用区、实例类型维度下有效预扣总量不能超过共识需求总量
        checkPreDeductFromDemand(req);
        // GPU产品无需预扣审批子流程
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(orderDetailResp.getProduct());

        // 方案生效后需要将订单信息回写到主流程中，主流程当前节点自旋一次来记录子流程完结和订单信息回写主流程的操作
        OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
        spinReq.setOrder(orderDetailResp);
        String operate = isGpu ? "用户发起预扣" : "用户发起预扣子流程";
        spinReq.setOperateEvent(operate);
        spinReq.setOperateName(operate);
        spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        spinReq.setNodeCode(orderDetailResp.getOrderNodeCode());
        spinReq.setBizId(mainOrderNumber);
        // 设置返回值 999 表示主流程在当前节点自旋一次
        spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        spinReq.setOperateUser(user);
        OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);

        String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(mainOrderNumber,
                OrderSubFlowNoPrefixEnum.R);

        // 记录预扣发起日志
        Set<String> preDeductPlanNumberList = new HashSet<>();
        preDeductPlanNumberList.add(orderSubFlowNo);
        logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);

        OrderDetailResp newOrderDetailResp = orderCommonService.queryOrderDetail(mainOrderNumber);

        Tuple2<PreDeductPlanDO, List<PreDeductOrderItemDO>> res;
        if (isGpu) {
            // GPU产品，插入预扣数据, 预扣数据直接生效，没有预扣子流程
            res = insertPreDeductPlanAndDetails(req, orderSubFlowNo, user,
                    PreDeductOrderStatusEnum.WAIT_CREATING, PreDeductPlanStatusEnum.VALID, true);
        } else {
            // 非GPU产品，插入预扣数据, 预扣数据仍在CRP流程中
            res = insertPreDeductPlanAndDetails(req, orderSubFlowNo, user,
                    PreDeductOrderStatusEnum.PROCESSING, PreDeductPlanStatusEnum.PROCESSING, true);
        }

        if (!isGpu) {
            // 非GPU产品，启动创建预扣订单子流程
            startPreDeductSubFlow(orderSubFlowNo, newOrderDetailResp, res._2,
                    false, user, req.getFlowRemark());
        }
    }

    private void checkPreDeductFromDemand(CreatePreDeductPlanReq req) {
        List<PreDeductAmountCheckItem> checkItems = checkPreDeductAmount(req);
        List<String> errors = new ArrayList<>();
        checkItems.forEach(item -> {
            String msg = item.toErrorMessage();
            if (StringUtils.isNotBlank(msg)) {
                errors.add(msg);
            }
        });
        if (errors.size() > 0) {
            throw BizException.makeThrow(String.join(" \n ", errors));
        }
    }

    /**
     * 预扣检查
     *
     * @param resp
     * @param req
     */
    public void checkPreDeducted(OrderDetailResp resp, CreatePreDeductPlanReq req) {
        if (!PplProductToOrderProductMapEnum.canPreDeductAppRoles().contains(resp.getAppRole())) {
            throw BizException.makeThrow("操作失败，当前订单【%s】的应用角色【%s】不支持预扣",
                    resp.getOrderNumber(), resp.getAppRole());
        }
        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(resp.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || (!OrderNodeCodeEnum.node_order_supply.getCode().equals(nodeData.getNodeCode())
                && !OrderNodeCodeEnum.node_order_following.getCode().equals(nodeData.getNodeCode()))) {
            throw BizException.makeThrow("操作失败，仅在 交付供应 或 履约跟踪时 可以预扣，订单号：【%s】",
                    resp.getOrderNumber());
        }

        if (req.getEndPreDeductDate() == null || req.getStartPreDeductDate() == null) {
            throw BizException.makeThrow("操作失败，预扣开始结束日期不能为空，订单号：【%s】",
                    resp.getOrderNumber());
        }

        if (req.getEndPreDeductDate().isBefore(req.getStartPreDeductDate())) {
            throw BizException.makeThrow("操作失败，预扣开始结束日期不能颠倒，订单号：【%s】",
                    resp.getOrderNumber());
        }

        // 预扣开始结束日期不能在订单结束购买日期之后
        if (req.getStartPreDeductDate().isAfter(resp.getEndBuyDate())
                || req.getEndPreDeductDate().isAfter(resp.getEndBuyDate())) {
            throw BizException.makeThrow(
                    "操作失败，预扣开始结束日期不能在订单结束购买日期之后，订单号：【%s】，订单结束购买日期【%s】",
                    resp.getOrderNumber(), resp.getEndBuyDate());
        }

        long days = req.getStartPreDeductDate().until(req.getEndPreDeductDate(), ChronoUnit.DAYS);

        if (OrderTypeEnum.NEW.getCode().equals(resp.getOrderType()) ||
              OrderElasticType.ONCE_TIME.getTypeName().equals(resp.getElasticType())){
            // 只有普通订单 或 一次性弹性才有预扣天数限制
            if (days >= 30) {
                throw BizException.makeThrow(
                        "操作失败，预扣天数必须小于30天，订单号：【%s】，预扣开始日期【%s】，预扣结束日期【%s】",
                        resp.getOrderNumber(), req.getStartPreDeductDate(), req.getEndPreDeductDate());
            }
        }

        if (ListUtils.isEmpty(req.getItemList())) {
            throw BizException.makeThrow("操作失败，预扣明细不能为空，订单号：【%s】", resp.getOrderNumber());
        }
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(resp.getProduct());
        for (PreDeductItemDTO dto : req.getItemList()) {
            if (dto.getInstanceNum() == null || dto.getInstanceNum()  <= 0) {
                throw BizException.makeThrow("操作失败，预扣实例数不能小于0，订单号：【%s】", resp.getOrderNumber());
            }
            if (isGpu) {
                if (dto.getTotalGpuNum() == null || dto.getTotalGpuNum().intValue()  <= 0) {
                    throw BizException.makeThrow("操作失败，预扣卡数不能小于0，订单号：【%s】", resp.getOrderNumber());
                }
                int totalGpu = dto.getInstanceNum() * (dto.getGpuNum() == null ? 0 : dto.getGpuNum());
                if (dto.getTotalGpuNum().intValue() != totalGpu) {
                    throw BizException.makeThrow("操作失败，预扣卡数与实例数不匹配，订单号：【%s】", resp.getOrderNumber());
                }
            } else {
                if (dto.getTotalCore() == null || dto.getTotalCore()  <= 0) {
                    throw BizException.makeThrow("操作失败，预扣核心数不能小于0，订单号：【%s】", resp.getOrderNumber());
                }
                int totalCpu = dto.getInstanceNum() * (dto.getCpuNum() == null ? 0 : dto.getCpuNum());
                if (dto.getTotalCore() != totalCpu) {
                    throw BizException.makeThrow("操作失败，预扣核心数与实例数不匹配，订单号：【%s】", resp.getOrderNumber());
                }
            }
        }

        // 针对白名单实例类型的订单明细，调用云霄预扣单创建前的检查接口
        List<OrderItemDO> transform = ListUtils.transform(req.getItemList(), v -> v.toOrderItemDO());
        Vector<String> errors = new Vector<>();
        final CountDownLatch yunxiaoCheck = yunXiaoCheck(resp, transform, errors);

        // 检查预扣备选uin
        checkShareUin(resp.getCustomerUin(), req.getShareUinList(), resp.getIndustryDept());

        try {
            // 云霄异步检查是否完成
            boolean flag = yunxiaoCheck.await(3, TimeUnit.SECONDS);
        } catch (Exception e) {
            // non
        }
        if (ListUtils.isNotEmpty(errors)) {
            throw BizException.makeThrow(String.join("   \n", errors));
        }
    }

    @Override
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 500,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void createPreDeductOrderDone(String preDeductPlanNumber) {
        // 1.查询预扣计划
        PreDeductPlanDTO preDeductPlan = demandDBHelper.getOne(PreDeductPlanDTO.class,
                "where pre_deduct_plan_number = ?", preDeductPlanNumber);
        if (preDeductPlan == null) {
            throw BizException.makeThrow("预扣计划不存在，预扣计划号：【%s】", preDeductPlanNumber);
        }

        preDeductPlan.setStatus(PreDeductPlanStatusEnum.VALID.getCode());
        for (PreDeductOrderItemDO preDeductOrderItemDO : preDeductPlan.getItemList()) {
            // CRP预扣单审批通过时，有效预扣量与申请量一致
            preDeductOrderItemDO.setTotalValidCount(preDeductOrderItemDO.getInstanceNum());
            preDeductOrderItemDO.setTotalValidCpuCount(preDeductOrderItemDO.getTotalCore());
            preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.WAIT_CREATING.getCode());
        }

        demandDBHelper.update(preDeductPlan);
        demandDBHelper.update(preDeductPlan.getItemList());

        // 订单明细已预扣判断条件：严格判断：即需求维度，实例规格+可用区+计费模式，匹配预扣维度，实例规格+可用区+计费模式
        Set<String> conditions = preDeductPlan.getItemList().stream()
                .map(o -> String.join("_", o.getInstanceModel(), o.getZoneName(), o.getBillType()))
                .collect(Collectors.toSet());

        // ---- 订单自旋 插入日志 ------
        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(preDeductPlan.getOrderNumber());
        OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
        spinReq.setOrder(orderDetailResp);
        spinReq.setOperateEvent("预扣子流程完成");
        spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        spinReq.setNodeCode(orderDetailResp.getOrderNodeCode());
        spinReq.setBizId(preDeductPlan.getOrderNumber());
        // 流程自旋
        spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        spinReq.setOperateUser("system");
        spinReq.setNotShowLogInMainFlow(Boolean.TRUE);
        spinReq.setOrderItemsCreator(
                (normal, newOrder) -> {
                    List<OrderItemDTO> itemList = orderDetailResp.getItemList();
                    List<OrderItemDO> result = OrderItemDTO.toItemDO(itemList);
                    for (OrderItemDO item : result) {
                        item.clearSomeValueForInsertNew(newOrder);
                        // 订单明细已预扣判断条件：严格判断：即需求维度，实例规格+可用区+计费模式，匹配预扣维度，实例规格+可用区+计费模式
                        String dimension = String.join("_", item.getInstanceModel(), item.getZoneName(),
                                item.getBillType());
                        if (conditions.contains(dimension)) {
                            item.setIsPreDeduct(Boolean.TRUE);
                        }
                    }
                    return result;
                });

        // 流程推进
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);

        if (OrderTypeEnum.ELASTIC.getCode().equals(orderDetailResp.getOrderType())
                && OrderElasticType.getWeekMonthElasticType().contains(orderDetailResp.getElasticType())){
            // 如果是周弹性/月弹性，则需要将未来还存在的剩余预扣计划创建出来。
            initWeekMonthOtherPreDeduct(orderCommonService.queryOrderDetail(preDeductPlan.getOrderNumber()));

        }

        // 延迟发起云霄预扣
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> preDeductOrderService.createYunXiaoPreDeductForOrder(orderDetailResp.getOrderNumber()),
                5, TimeUnit.SECONDS);
    }


    public void initWeekMonthOtherPreDeduct(OrderDetailResp order){

        ElasticCycleConfig elasticCycleConfig = orderCommonService.generateElasticPreDeductDTO(order);
        if (elasticCycleConfig == null || ListUtils.isEmpty(elasticCycleConfig.getPreDeductPlanTimeList())){
            return;
        }
        String mainOrderNumber = order.getOrderNumber();
        List<PreDeductItemForCreateChoose> list = supplyPlanQueryService.preDeductItemForCreateChoose(order.getOrderNumber(),
                OrderTypeEnum.ELASTIC.getCode().equals(order.getOrderType())
                        && OrderElasticType.getWeekMonthElasticType().contains(order.getElasticType()));
        for (int i = 1; i < elasticCycleConfig.getPreDeductPlanTimeList().size(); i++) {

            String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(mainOrderNumber,
                    OrderSubFlowNoPrefixEnum.R);

            PreDeductPlanTime preDeductPlanTime = elasticCycleConfig.getPreDeductPlanTimeList().get(i);

            // 系统自动插入CRP流程中的预扣数据
            insertPreDeductPlanAndDetailsByConsensusOrder(order, orderSubFlowNo, list, true,
                    preDeductPlanTime.getStartPreDeductDate().minusDays(1),
                    preDeductPlanTime.getEndPreDeductDate(),true);

        }

        FlowNodeRecordWithFlowInfoDTO currentNodeData = flowService.queryProcessingFlowNode(
                mainOrderNumber, OrderFlowEnum.ORDER_MAIN.getCode());

        // 主流程自旋一次，记录产品预扣操作，将被预扣的订单明细打标
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("产品预扣");
        valueSetReq.setFlowCode(currentNodeData.getFlowInfo().getFlowCode());
        valueSetReq.setNodeCode(currentNodeData.getNodeCode());
        valueSetReq.setBizId(mainOrderNumber);
        valueSetReq.setOperateRemark("系统自动创建剩余弹性预扣计划");
        // 主流程节点自旋一次
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser("system");

        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

    }

    @Override
    @TaskLog(taskName = "createYunxiaoPreDeductOrder")
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void createYunxiaoPreDeductOrder() {
        // 1.查询出所有需要创建预扣的所有item
        List<PreDeductOrderItemDO> preDeductOrderItemDOList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where status = ?", PreDeductOrderStatusEnum.WAIT_CREATING.getCode());
        if (ListUtils.isEmpty(preDeductOrderItemDOList)) {
            return;
        }

        Set<String> orderNumberList = ListUtils.toSet(preDeductOrderItemDOList, PreDeductOrderItemDO::getOrderNumber);
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        for (String orderNumber : orderNumberList) {
            try {
                preDeductOrderService.createYunXiaoPreDeductForOrder(orderNumber);
            } catch (Exception e) {
                // 内部已有告警和任务日志记录，此处无需处理异常
            }
        }

    }

    @Override
    @TaskLog(taskName = "createYunXiaoPreDeductForOrder")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = false)
    public void createYunXiaoPreDeductForOrder(String orderNumber) {
        List<PreDeductOrderItemDO> preDeductOrderItemDOList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where status = ? and order_number = ? ",
                PreDeductOrderStatusEnum.WAIT_CREATING.getCode(), orderNumber);
        if (ListUtils.isEmpty(preDeductOrderItemDOList)) {
            return;
        }
        // 2.关联查出预扣计划
        List<String> preDeductOrderList = preDeductOrderItemDOList.stream()
                .map(PreDeductOrderItemDO::getPreDeductPlanNumber)
                .distinct().collect(Collectors.toList());
        List<PreDeductPlanDO> planList = demandDBHelper.getAll(PreDeductPlanDO.class,
                "where pre_deduct_plan_number in (?) and order_number = ? ",
                preDeductOrderList, orderNumber);
        Map<String, PreDeductPlanDO> planMap = planList.stream()
                .collect(Collectors.toMap(PreDeductPlanDO::getPreDeductPlanNumber, v -> v));
        OrderInfoDO order = demandDBHelper.getOne(OrderInfoDO.class,
                "where order_number = ? and available_status = ?",
                orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            return;
        }

        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        for (PreDeductOrderItemDO preDeductOrderItemDO : preDeductOrderItemDOList) {
            PreDeductPlanDO planDO = planMap.get(preDeductOrderItemDO.getPreDeductPlanNumber());
            if (planDO != null) {
                try {
                    preDeductOrderService.createYunXiaoPreDeductForOrder(planDO, order, preDeductOrderItemDO);
                } catch (Exception e) {
                    taskLogService.genRunWarnLog(
                            "createYunxiaoPreDeduct", "createYunXiaoPreDeductForOrder", e.getMessage());
                    if (e.getMessage() != null && e.getMessage().contains("触发流控")) {
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException ex) {
                            throw new RuntimeException(ex);
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    public void createYunXiaoPreDeductForOrder(PreDeductPlanDO planDO, OrderInfoDO orderInfoDO,
            PreDeductOrderItemDO preDeductOrderItemDO) {
        CreatePreDeductOrderReq req = CreatePreDeductOrderReq.buildPreDeductOrderReq(planDO, orderInfoDO,
                preDeductOrderItemDO);
        CreatePreDeductOrderResp preDeductOrder = yunxiaoAPIService.createPreDeductOrder(req);
        if (!preDeductOrder.getSuccess()) {
            AlarmRobotUtil.doAlarm("createPreDeductOrder",
                    "预扣订单创建失败，crp预扣Id：" + preDeductOrderItemDO.getId() +
                            " 错误信息：" + preDeductOrder.getErrorMsg(), null,
                    false);
            throw new BizException("预扣订单创建失败，crp预扣Id：" + preDeductOrderItemDO.getId() +
                    " 错误信息：" + preDeductOrder.getErrorMsg());
        }
        preDeductOrderItemDO.setStatus(preDeductOrder.getStatus());
        preDeductOrderItemDO.setReservationFormId(preDeductOrder.getId());
        demandDBHelper.update(preDeductOrderItemDO);
    }


    @Override
    @TaskLog(taskName = "syncYunxiaoPreDeductOrder")
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void syncYunxiaoPreDeductOrder() {

        // 获取需要同步信息的订单
        String sql = "select DISTINCT order_number from pre_deduct_order_item "
                + " where deleted = 0 and start_pre_deduct_date > ? "
                + " and status not in (?) and reservation_form_id is not null";

        List<String> orderNumberList = demandDBHelper.getRaw(String.class, sql,
                LocalDate.now().minusDays(180),
                Arrays.asList(PreDeductOrderStatusEnum.PROCESSING.getCode(),
                        PreDeductOrderStatusEnum.WAIT_CREATING.getCode(),
                        PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode(),
                        PreDeductOrderStatusEnum.DESTROYED.getCode(),
                        PreDeductOrderStatusEnum.CLOSED.getCode(), PreDeductOrderStatusEnum.REJECTED.getCode()));

        if (ListUtils.isEmpty(orderNumberList)) {
            return;
        }

        for (String orderNumber : orderNumberList) {
            PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
            try {
                preDeductOrderService.syncYunxiaoPreDeductOneOrder(orderNumber);
            } catch (Exception e) {
                // 异常原因之一可能是云霄接口限频报错
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
            }
        }

    }

    @Override
    @TaskLog(taskName = "syncYunxiaoPreDeductOneOrder")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = false, waitLockMillisecond = 1000)
    public void syncYunxiaoPreDeductOneOrder(String orderNumber) {
        try {
            // 只查询这三个需要的字段，其他字段不查询，防止下面更新不需要的字段
            String sql = "select id, reservation_form_id, status, total_reserved_count, total_reserved_cpu_count from pre_deduct_order_item "
                    + " where deleted = 0 "
                    + " and status not in (?) and reservation_form_id is not null and order_number = ? ";

            // 1.查询出所有需要同步预扣信息的所有item
            List<PreDeductOrderItemDO> needSyncItem = demandDBHelper.getRaw(PreDeductOrderItemDO.class, sql,
                    Arrays.asList(PreDeductOrderStatusEnum.PROCESSING.getCode(),
                            PreDeductOrderStatusEnum.WAIT_CREATING.getCode(),
                            PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode(),
                            PreDeductOrderStatusEnum.CLOSED.getCode(),
                            PreDeductOrderStatusEnum.REJECTED.getCode()),
                    orderNumber);
            if (ListUtils.isEmpty(needSyncItem)) {
                return;
            }

            QueryPreDeductOrderListReq req = new QueryPreDeductOrderListReq();
            req.setPageSize(needSyncItem.size());
            req.setReservationFormId(
                    needSyncItem.stream().map(PreDeductOrderItemDO::getReservationFormId).distinct().collect(
                            Collectors.toList()));
            List<PreDeductData> preDeductDataList = yunxiaoAPIService.queryPreDeductOrderList(req);
            if (ListUtils.isEmpty(preDeductDataList)) {
                return;
            }
            Map<Integer, PreDeductData> formIdToPreDeductData = preDeductDataList.stream()
                    .collect(Collectors.toMap(PreDeductData::getId, v -> v));

            // 全部预扣成功的预扣单 id
            List<Long> allSuccess = new ArrayList<>();
            boolean anyUpdateSuccess = false;
            // 部分预扣成功的预扣单 id
            List<Long> partSuccess = new ArrayList<>();
            boolean anyUpdatePartSuccess = false;
            // 预扣失败的预扣单 id
            List<Long> fail = new ArrayList<>();
            boolean anyUpdateFail = false;

            List<String> actualList = ListUtils.newArrayList(PreDeductOrderStatusEnum.DESTROYED.getCode(),
                    PreDeductOrderStatusEnum.DESTROY_PENDING.getCode(),
                    PreDeductOrderStatusEnum.PARTIAL_DESTROYED.getCode(),
                    PreDeductOrderStatusEnum.FAILED.getCode(),
                    PreDeductOrderStatusEnum.CLOSED.getCode());

            for (PreDeductOrderItemDO preDeductOrderItemDO : needSyncItem) {
                PreDeductData preDeductData = formIdToPreDeductData.get(preDeductOrderItemDO.getReservationFormId());
                if (preDeductData != null) {
                    if (!Objects.equals(preDeductData.getStatus(), preDeductOrderItemDO.getStatus())) {
                        // 订单系统中预扣单状态需要更新时
                        if (PreDeductOrderStatusEnum.CREATED.getCode().equals(preDeductData.getStatus())) {
                            // 需要更新为预扣成功时
                            allSuccess.add(preDeductOrderItemDO.getId());
                            anyUpdateSuccess = true;
                        } else if (PreDeductOrderStatusEnum.PARTIAL_CREATED.getCode().equals(preDeductData.getStatus())) {
                            // 需要更新为部分预扣成功时
                            partSuccess.add(preDeductOrderItemDO.getId());
                            anyUpdatePartSuccess = true;
                        } else if (PreDeductOrderStatusEnum.FAILED.getCode().equals(preDeductData.getStatus())) {
                            // 需要更新为预扣失败时
                            fail.add(preDeductOrderItemDO.getId());
                            anyUpdateFail = true;
                        }
                    }
                    // 更新同步过来的信息。
                    preDeductOrderItemDO.setStatus(preDeductData.getStatus());
                    preDeductOrderItemDO.setCreatedCount(preDeductData.getCreatedCount());
                    preDeductOrderItemDO.setCreatedRate(preDeductData.getCreatedRate());
                    preDeductOrderItemDO.setPurchasedCount(preDeductData.getPurchasedCount());
                    preDeductOrderItemDO.setPurchasedRate(preDeductData.getPurchasedRate());
                    preDeductOrderItemDO.setExtendedPurchasedCount(preDeductData.getExtendedPurchasedCount());
                    preDeductOrderItemDO.setExtendedPurchasedRate(preDeductData.getExtendedPurchasedRate());
                }

                GetPreDeductOrderDetailResultResp preDeductOrderDetailResult = yunxiaoAPIService.getPreDeductOrderDetailResult(
                        preDeductOrderItemDO.getReservationFormId().toString());
                if (preDeductOrderDetailResult != null) {
                    preDeductOrderItemDO.setTotalReservedCount(preDeductOrderDetailResult.getTotalReservedCount());
                    preDeductOrderItemDO.setTotalReservedCpuCount(preDeductOrderDetailResult.getTotalReservedCpuCount());
                    preDeductOrderItemDO.setMatchRate(preDeductOrderItemDO.getMatchRate());
                }
                if (actualList.contains(preDeductOrderItemDO.getStatus())) {
                    // 云霄预扣单结束时，有效预扣量与实际预扣量一致
                    preDeductOrderItemDO.setTotalValidCount(preDeductOrderItemDO.getTotalReservedCount());
                    preDeductOrderItemDO.setTotalValidCpuCount(preDeductOrderItemDO.getTotalReservedCpuCount());
                }
            }
            demandDBHelper.update(needSyncItem);

            Set<Long> ids = ListUtils.toSet(needSyncItem, BaseDO::getId);
            WhereSQL where = new WhereSQL();
            where.and(" id in (?)", ids);
            List<PreDeductOrderItemDO> resultList  = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                    where.getSQL(), where.getParams());

            // 全部预扣成功的预扣单
            List<PreDeductOrderItemDO> allSuccessList = new ArrayList<>();
            // 部分预扣成功的预扣单
            List<PreDeductOrderItemDO> partSuccessList = new ArrayList<>();
            // 预扣失败的预扣单
            List<PreDeductOrderItemDO> failList = new ArrayList<>();
            for (PreDeductOrderItemDO itemDO : resultList) {
                if (anyUpdateSuccess && allSuccess.contains(itemDO.getId())) {
                    allSuccessList.add(itemDO);
                }
                if (anyUpdatePartSuccess && partSuccess.contains(itemDO.getId())) {
                    partSuccessList.add(itemDO);
                }
                if (anyUpdateFail && fail.contains(itemDO.getId())) {
                    failList.add(itemDO);
                }
            }

            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
            String noticeUser = order.getNoticeUser();
            if (anyUpdateSuccess) {
                // 发送预扣成功的通知
                yunXiaoPreDeductNotice(allSuccessList, order, CrpEventEnum.order_yun_xiao_pre_deduct_success, noticeUser);
            }
            if (anyUpdatePartSuccess) {
                // 发送部分预扣成功的通知
                yunXiaoPreDeductNotice(partSuccessList, order, CrpEventEnum.order_yun_xiao_pre_deduct_part_success, noticeUser);
            }
            if (anyUpdateFail) {
                // 发送预扣失败的通知
                yunXiaoPreDeductNotice(failList, order, CrpEventEnum.order_yun_xiao_pre_deduct_fail, noticeUser);
            }
        } catch (Exception e) {
            String msg = "同步订单【{}】的云霄预扣信息异，错误消息：{}";
            msg = StrUtil.format(msg, orderNumber,ExceptionUtil.getMessage(e));
            AlarmRobotUtil.doAlarm("syncYunxiaoPreDeductOneOrder",
                    msg, null, false);
            throw e;
        }
    }

    private void yunXiaoPreDeductNotice(List<PreDeductOrderItemDO> list, OrderInfoDO order,
            CrpEventEnum event, String noticeUser) {
        Map<String, List<PreDeductOrderItemDO>> map = ListUtils.toMapList(list,
                PreDeductOrderItemDO::getPreDeductPlanNumber, Function.identity());
        map.forEach((preDeductPlanNumber, preDeductList) -> {
            Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForYunXiaoPreDeduct(order,
                    preDeductList, preDeductPlanNumber);
            dictService.eventNotice(event.getCode(), null, null, noticeTemplate, noticeUser);
        });
    }

    @Override
    public void destroyPreDeductOrder() {
        String sql = "select order_number from pre_deduct_order_item "
                + " where deleted = 0 and status = ? ";
        List<String> orderNumberList = demandDBHelper.getRaw(String.class,
                sql, PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode());
        if (ListUtils.isEmpty(orderNumberList)) {
            return;
        }
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        for (String orderNumber : orderNumberList) {
            try {
                preDeductOrderService.destroyYunXiaoPreDeductForOrder(orderNumber);
            } catch (Exception e) {
                // 内部已有告警和任务日志记录，此处无需处理异常
            }
        }

    }

    @Override
    @TaskLog(taskName = "destroyYunXiaoPreDeductForOrder")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = false)
    public void destroyYunXiaoPreDeductForOrder(String orderNumber) {
        String sql = "select id, reservation_form_id from pre_deduct_order_item "
                + " where deleted = 0 and status = ? and order_number = ? ";
        List<PreDeductOrderItemDO> needDestroyItem = demandDBHelper.getRaw(PreDeductOrderItemDO.class,
                sql, PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode(), orderNumber);
        if (ListUtils.isEmpty(needDestroyItem)) {
            return;
        }
        Set<Long> ids = new HashSet<>();
        for (PreDeductOrderItemDO preDeductOrderItemDO : needDestroyItem) {
            try {
                // 调云霄接口取消预扣
                DestroyPreDeductOrderReq req = new DestroyPreDeductOrderReq();
                req.setReservationFormId(Arrays.asList(preDeductOrderItemDO.getReservationFormId()));
                req.setOperator(LoginUtils.getUserNameWithSystem());
                CreatePreDeductOrderResp createPreDeductOrderResp = yunxiaoAPIService.destroyPreDeductOrder(req);
                if (!createPreDeductOrderResp.getSuccess()) {
                    throw new BizException(createPreDeductOrderResp.getErrorMsg());
                }
                preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.DESTROYED.getCode());
                demandDBHelper.update(preDeductOrderItemDO);
                ids.add(preDeductOrderItemDO.getId());
            } catch (Exception e) {
                AlarmRobotUtil.doAlarm("destroyYunXiaoPreDeductForOrder",
                        "取消预扣失败，预扣单id: " + preDeductOrderItemDO.getReservationFormId() + "错误信息"
                                + e.getMessage(), null, false);
                taskLogService.genRunWarnLog(
                        "destroyPreDeductOrder", "destroyYunXiaoPreDeductForOrder", e.getMessage());
            }
        }

        if (ListUtils.isNotEmpty(ids)) {
            // 发送预扣销毁通知
            WhereSQL where = new WhereSQL();
            where.and(" id in (?)", ids);
            List<PreDeductOrderItemDO> destoryList  = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                    where.getSQL(), where.getParams());
            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
            if (order != null) {
                String noticeUser = order.getNoticeUser();
                yunXiaoPreDeductNotice(destoryList, order, CrpEventEnum.order_yun_xiao_pre_deduct_destroy, noticeUser);
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 500,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void cancelPreDeductOrder(String orderNumber, List<Integer> reservationFormIdList) {

        List<PreDeductOrderItemDO> preDeductOrderItemList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where order_number = ? and reservation_form_id in (?)", orderNumber, reservationFormIdList);
        if (ListUtils.isEmpty(reservationFormIdList)) {
            return;
        }
        // ---- 订单自旋 插入日志 ------
        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(orderNumber);
        // 有订单查询权限的都可以取消预扣
        orderCommonService.checkOrderQueryAuth(orderDetailResp);

        OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
        spinReq.setOrder(orderDetailResp);
        spinReq.setOperateEvent("取消预扣");
        spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        spinReq.setNodeCode(orderDetailResp.getOrderNodeCode());
        spinReq.setBizId(orderNumber);
        // 流程自旋
        spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        spinReq.setOperateUser(LoginUtils.getUserNameWithSystem());
        spinReq.setOperateName("取消预扣,云霄预扣单Id为:" + Strings.join(";", reservationFormIdList));

        OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);

        // 记录预扣取消日志
        Set<String> preDeductPlanNumberList = ListUtils.toSet(preDeductOrderItemList,
                PreDeductOrderItemDO::getPreDeductPlanNumber);
        logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);

        // 更新状态为销毁中
        for (PreDeductOrderItemDO preDeductOrderItemDO : preDeductOrderItemList) {
            if (LocalDate.now().isBefore(preDeductOrderItemDO.getStartPreDeductDate())) {
                preDeductOrderItemDO.setPreDeductDay(0);
            } else if (LocalDateUtil.AfterOrEqual(LocalDate.now(), preDeductOrderItemDO.getStartPreDeductDate())
                    && LocalDateUtil.BeforeOrEqual(LocalDate.now(), preDeductOrderItemDO.getEndPreDeductDate())) {
                // 如果取消预扣时间在 开始预扣-结束预扣 中间
                preDeductOrderItemDO.setEndPreDeductDate(LocalDate.now());
                preDeductOrderItemDO.setPreDeductDay(Long.valueOf(preDeductOrderItemDO.getStartPreDeductDate()
                        .until(preDeductOrderItemDO.getEndPreDeductDate(), ChronoUnit.DAYS)).intValue());
            } else {
                throw new BizException("当前已超过结束预扣时间，无法取消预扣");
            }
            preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode());
        }
        demandDBHelper.update(preDeductOrderItemList);

        // 发送预扣取消通知 需通知所有审批过的审批人
        OrderLogResp orderLogResp = orderCommonService.queryOrderLog(orderDetailResp.getOrderNumber(), null);
        List<String> historyUser = orderLogResp.getOrderLogDOList().stream()
                .filter(v -> v.getFlowNodeRecordDO() != null)
                .map(v -> v.getFlowNodeRecordDO().getOperateUser()).distinct().collect(
                        Collectors.toList());
        String noticeUser = orderDetailResp.getNoticeUser() + ";" + Strings.join(";", historyUser);
        for (String preDeductPlanNumber : preDeductPlanNumberList) {
            Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrderPreDeduct(orderFlow,
                    preDeductOrderItemList, preDeductPlanNumber);
            dictService.eventNotice(CrpEventEnum.order_pre_deduct_cancel.getCode(),
                    null, null, noticeTemplate, noticeUser);
        }

        // 延迟发起云霄预扣销毁
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> preDeductOrderService.destroyYunXiaoPreDeductForOrder(orderNumber),
                5, TimeUnit.SECONDS);
    }

    @Override
    public PreDeductPlanResp queryPreDeductOrderDetail(String orderNumber, String preDeductPlanNumber) {
        PreDeductPlanResp resp = new PreDeductPlanResp();

        WhereContent whereContent = new WhereContent();
        whereContent.andEqualIfValueNotEmpty(PreDeductOrderItemDTO::getOrderNumber, orderNumber);
        whereContent.andEqualIfValueNotEmpty(PreDeductOrderItemDTO::getPreDeductPlanNumber, preDeductPlanNumber);

        List<PreDeductOrderItemDTO> itemList = demandDBHelper.getAll(PreDeductOrderItemDTO.class, whereContent.getSql(),
                whereContent.getParams());

        List<Integer> reservationFormIdList = itemList.stream().map(PreDeductOrderItemDTO::getReservationFormId).distinct()
                .collect(Collectors.toList());
        List<Grid> grids = yunxiaoAPIService.queryGridList(reservationFormIdList);
        Map<Integer, List<Grid>> formIdToGridList = grids.stream().collect(Collectors.groupingBy(Grid::getFormId));

        List<String> preDeductPlanNumberList = itemList.stream()
                .filter(v -> v.getStatus().equals(PreDeductOrderStatusEnum.PROCESSING.getCode()))
                .map(PreDeductOrderItemDTO::getPreDeductPlanNumber).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(preDeductPlanNumberList)) {
            preDeductPlanNumber = preDeductPlanNumberList.get(0);
        }
        OrderDetailResp subFlowOrder = orderCommonService.queryOrderDetail(orderNumber, preDeductPlanNumber);

        List<String> preDeductStatus = Arrays.asList(PreDeductOrderStatusEnum.APPROVAL_PENDING.getCode(),
                PreDeductOrderStatusEnum.WAITING.getCode(), PreDeductOrderStatusEnum.CREATE_PENDING.getCode(),
                PreDeductOrderStatusEnum.PARTIAL_CREATED.getCode(), PreDeductOrderStatusEnum.CREATED.getCode()
        );

        // 看哪些预扣计划号有对应的子流程，没有子流程的预扣计划号都是产品预扣
        List<String> preDeductPlanNumbers = ListUtils.transform(itemList, PreDeductOrderItemDO::getPreDeductPlanNumber);
        Set<String> preDeductFlowNoSet = flowService.queryFlowNoSet(preDeductPlanNumbers);
        for (PreDeductOrderItemDTO preDeductOrderItemDTO : itemList) {
            // 设置当前审批人
            if (subFlowOrder != null && preDeductOrderItemDTO.getPreDeductPlanNumber()
                    .equals(subFlowOrder.getFlowNo())) {
                preDeductOrderItemDTO.setCurrentProcessor(subFlowOrder.getCurrentProcessor());
            }
            if (preDeductFlowNoSet.contains(preDeductOrderItemDTO.getPreDeductPlanNumber())) {
                // 用户申请的预扣都是走子流程
                preDeductOrderItemDTO.setPreDeductCreator("用户申请");
            } else {
                // 产品预扣没有走子流程
                preDeductOrderItemDTO.setPreDeductCreator("产品预扣");
                if (preDeductOrderItemDTO.getPlanDO() != null) {
                    if (preDeductOrderItemDTO.getPlanDO().getUserApply() != null
                            && preDeductOrderItemDTO.getPlanDO().getUserApply()) {
                        preDeductOrderItemDTO.setPreDeductCreator("用户申请");
                    }
                }
            }
            // 设置预扣天数
            if (preDeductStatus.contains(preDeductOrderItemDTO.getStatus())) {
                // 如果是这些预扣状态 计算预扣时间
                if (LocalDate.now().isBefore(preDeductOrderItemDTO.getStartPreDeductDate())) {
                    preDeductOrderItemDTO.setPreDeductDay(0);
                } else if (LocalDateUtil.AfterOrEqual(LocalDate.now(), preDeductOrderItemDTO.getStartPreDeductDate())
                        && LocalDateUtil.BeforeOrEqual(LocalDate.now(), preDeductOrderItemDTO.getEndPreDeductDate())) {
                    preDeductOrderItemDTO.setPreDeductDay(Long.valueOf(preDeductOrderItemDTO.getStartPreDeductDate()
                            .until(LocalDate.now(), ChronoUnit.DAYS)).intValue());
                } else if (LocalDate.now().isAfter(preDeductOrderItemDTO.getEndPreDeductDate())) {
                    preDeductOrderItemDTO.setPreDeductDay(Long.valueOf(preDeductOrderItemDTO.getStartPreDeductDate()
                            .until(preDeductOrderItemDTO.getEndPreDeductDate(), ChronoUnit.DAYS)).intValue());
                }
            }
            // 预扣状态名称
            preDeductOrderItemDTO.setStatusName(
                    PreDeductOrderStatusEnum.getNameByCode(preDeductOrderItemDTO.getStatus()));
            preDeductOrderItemDTO.someDataSetBySelf();

            // 设置预扣块聚合信息
            List<Grid> formGrids = formIdToGridList.get(preDeductOrderItemDTO.getReservationFormId());
            if (ListUtils.isNotEmpty(formGrids)) {

                List<Grid> aliveList = formGrids.stream()
                        .filter(v -> !v.isDestroyed()).collect(
                                Collectors.toList());

                List<Grid> idleList = formGrids.stream()
                        .filter(v -> YuxniaoGridStatusEnum.IDLE.getCode().equals(v.getGridStatus())).collect(
                                Collectors.toList());

                List<Grid> occupiedList = formGrids.stream()
                        .filter(v -> YuxniaoGridStatusEnum.OCCUPIED.getCode().equals(v.getGridStatus())).collect(
                                Collectors.toList());

                if (ListUtils.isNotEmpty(idleList)) {
                    preDeductOrderItemDTO.setCurrentIdleInstanceNum(idleList.size());
                    preDeductOrderItemDTO.setCurrentIdleCpuNum(idleList.size() * preDeductOrderItemDTO.getCpuNum());
                    preDeductOrderItemDTO.setCurrentIdleGpuNum(idleList.size() * preDeductOrderItemDTO.getGpuNum());
                }

                if (ListUtils.isNotEmpty(occupiedList)) {
                    preDeductOrderItemDTO.setCurrentOccupiedInstanceNum(occupiedList.size());
                    preDeductOrderItemDTO.setCurrentOccupiedCpuNum(
                            occupiedList.size() * preDeductOrderItemDTO.getCpuNum());
                    preDeductOrderItemDTO.setCurrentOccupiedGpuNum(
                            occupiedList.size() * preDeductOrderItemDTO.getGpuNum());
                }

                if (ListUtils.isNotEmpty(aliveList)) {
                    preDeductOrderItemDTO.setCurrentAliveInstanceNum(aliveList.size());
                    preDeductOrderItemDTO.setCurrentAliveCpuNum(aliveList.size() * preDeductOrderItemDTO.getCpuNum());
                    preDeductOrderItemDTO.setCurrentAliveGpuNum(aliveList.size() * preDeductOrderItemDTO.getGpuNum());
                }

            }

        }
        resp.setElasticCycleConfig(orderCommonService.generateElasticPreDeductDTO(orderCommonService.queryOrderDetail(orderNumber)));
        resp.setList(itemList);
        return resp;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void stopProcessingPreDeductOrderFlow(FlowInfoDO flow, String orderNumber, String operateEvent) {

        FlowInfoDO newestFlow = flowService.queryFlowInfoById(flow == null ? null : flow.getId());
        if (newestFlow != null && newestFlow.getFlowStatus().equals(FlowStatusEnum.PROCESSING.getCode())) {
            // 通用停止子流程
            OrderInfoWithFlowDTO orderFlow = orderFlowService.stopProcessingSubFlow(orderNumber, operateEvent);
            // 记录停止预扣子流程日志
            Set<String> preDeductPlanNumberList = new HashSet<>();
            preDeductPlanNumberList.add(flow.getFlowNo());
            logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);
        }

        // 对预扣计划表相关的表做操作。
        PreDeductPlanDTO one = demandDBHelper.getOne(PreDeductPlanDTO.class, "where pre_deduct_plan_number = ?",
                flow.getFlowNo());
        one.setStatus(PreDeductPlanStatusEnum.PRECESSING_CANCEL.getCode());
        demandDBHelper.update(one);

        for (PreDeductOrderItemDO preDeductOrderItemDO : one.getItemList()) {
            // CRP预扣单审批驳回时，有效预扣量改为0
            preDeductOrderItemDO.setTotalValidCount(0);
            preDeductOrderItemDO.setTotalValidCpuCount(0);
            preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.PRECESSING_CANCEL.getCode());
        }
        demandDBHelper.update(one.getItemList());

        // 当前主流程生效的订单
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderDetailResp order = demandDBHelper.getOne(OrderDetailResp.class, where.getSql(), where.getParams());

        // 有效预扣数据
        List<PreDeductOrderItemDO> deductList = queryPreDeductItems(orderNumber, PreDeductOrderStatusEnum.VALID_STATUS);
        // 订单明细已预扣判断条件：严格判断：即需求维度，实例规格+可用区+计费模式，匹配预扣维度，实例规格+可用区+计费模式
        Set<String> conditions = ListUtils.toSet(deductList,
                o -> String.join("_", o.getInstanceModel(), o.getZoneName(), o.getBillType()));

        // 主流程自旋
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent(operateEvent);
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(order.getOrderNodeCode());
        valueSetReq.setBizId(flow.getBizId());
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        String user = LoginUtils.getUserNameWithSystem();
        valueSetReq.setOperateUser(user);
        // 设置valueSetReq的OrderCreator属性，用于更新order表中的orderStatus字段
        valueSetReq.setOrderItemsCreator(
                (normal, newOrder) -> {
                    List<OrderItemDTO> itemList = order.getItemList();
                    List<OrderItemDO> result = OrderItemDTO.toItemDO(itemList);
                    for (OrderItemDO item : itemList) {
                        item.clearSomeValueForInsertNew(newOrder);
                        // 订单明细已预扣判断条件：严格判断：即需求维度，实例规格+可用区+计费模式，匹配预扣维度，实例规格+可用区+计费模式
                        String dimension = String.join("_", item.getInstanceModel(), item.getZoneName(),
                                item.getBillType());
                        item.setIsPreDeduct(conditions.contains(dimension));
                    }
                    return result;
                });
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
    }

    @Override
    public void cancelOrderRelevantPreDeductOrder(String orderNumber) {
        List<PreDeductPlanDO> planDOList = demandDBHelper.getAll(PreDeductPlanDO.class, "where order_number = ?",
                orderNumber);
        if (ListUtils.isEmpty(planDOList)) {
            demandDBHelper.executeRaw("update pre_deduct_plan set status = ? where deleted = 0 and id in (?)",
                    PreDeductPlanStatusEnum.DESTROY.getCode(),
                    planDOList.stream().map(PreDeductPlanDO::getId).collect(Collectors.toList()));
        }
//        List<PreDeductOrderItemDO> preDeductItemList = demandDBHelper.getAll(PreDeductOrderItemDO.class,
//                "where order_number = ? and status not in (?)",
//                orderNumber, Arrays.asList(
//                        PreDeductOrderStatusEnum.PRECESSING_CANCEL.getCode(),
//                        PreDeductOrderStatusEnum.DESTROY_PENDING.getCode(),
//                        PreDeductOrderStatusEnum.DESTROYED.getCode(),
//                        PreDeductOrderStatusEnum.DESTROY_FAILED.getCode(),
//                        PreDeductOrderStatusEnum.PARTIAL_DESTROYED.getCode()
//                ));
        // 还未创建的就取消了， 那直接修改为审批过程中取消， 有效预扣量 = 实际预扣量
        demandDBHelper.executeRaw("update pre_deduct_order_item set status = ?, "
                        + " total_valid_count = total_reserved_count , total_valid_cpu_count = total_reserved_cpu_count "
                        + "where deleted = 0 and order_number = ? and status = ?",
                PreDeductOrderStatusEnum.PRECESSING_CANCEL.getCode(), orderNumber,
                PreDeductOrderStatusEnum.WAIT_CREATING.getCode());
        // 其他的非销毁状态的， 修改为等待销毁，有效预扣量 = 实际预扣量
        demandDBHelper.executeRaw(
                "update pre_deduct_order_item set status = ? ,"
                        + "  total_valid_count = total_reserved_count , total_valid_cpu_count = total_reserved_cpu_count "
                        + "where deleted = 0 and order_number = ? and status not in (?)",
                PreDeductOrderStatusEnum.WAIT_DESTROYING.getCode(), orderNumber,
                Arrays.asList(
                        PreDeductOrderStatusEnum.PRECESSING_CANCEL.getCode(),
                        PreDeductOrderStatusEnum.DESTROY_PENDING.getCode(),
                        PreDeductOrderStatusEnum.DESTROYED.getCode(),
                        PreDeductOrderStatusEnum.DESTROY_FAILED.getCode(),
                        PreDeductOrderStatusEnum.PARTIAL_DESTROYED.getCode()
                ));

        // 延迟发起云霄预扣销毁
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> preDeductOrderService.destroyYunXiaoPreDeductForOrder(orderNumber),
                5, TimeUnit.SECONDS);
    }


    @Override
    public CountDownLatch yunXiaoCheck(OrderInfoDO order, List<? extends OrderItemDO> items, Vector<String> errors) {
//        // 实例规格为白名单时，待检查的订单明细数据
//        List<OrderItemDO> waitCheckItems = inWhiteListInstance(order, items);
        CountDownLatch downLatch = new CountDownLatch(items.size());
        // 调用云霄预扣单创建前的检查接口
        for (OrderItemDO checkItem : items) {
            executor.execute(() -> yunXiaoCheckItem(order, checkItem, downLatch, errors));
        }
        return downLatch;
    }

    private void yunXiaoCheckItem(OrderInfoDO order, OrderItemDO checkItem,
            CountDownLatch downLatch, Vector<String> errors) {
        try {
            List<String> banZones = new ArrayList<>(Arrays.asList(orderBanZone.get().split(";")));
            if (banZones.contains(checkItem.getZone())){
                // 如果在黑名单区域，不进行云霄预扣
                return;
            }
            CreatePreDeductOrderReq checkReq = CreatePreDeductOrderReq.createForOrderCheck(order, checkItem);
            CreatePreDeductOrderResp createPreDeductOrderResp = yunxiaoAPIService.beforePreDeductForOrderCheck(
                    checkReq);
            if (createPreDeductOrderResp.getSuccess().equals(Boolean.FALSE)) {
                errors.add("【" + createPreDeductOrderResp.getErrorMsg() + "】");
            }
        } catch (Exception e) {
            errors.add("调用云霄检查接口失败" + e.getMessage());
        } finally {
            downLatch.countDown();
        }
    }

    @Override
    public PreDeductPlanDTO queryPreDeductPlanAndDetail(String orderNumber, String preDeductPlanNumber) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PreDeductPlanDTO::getOrderNumber, orderNumber);
        whereContent.andEqual(PreDeductPlanDTO::getPreDeductPlanNumber, preDeductPlanNumber);
        return demandDBHelper.getOne(PreDeductPlanDTO.class, whereContent.getSql(),
                whereContent.getParams());
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void createProductPreDeduct(CreatePreDeductPlanReq req) {
        String mainOrderNumber = req.getOrderNumber();
        final String user = LoginUtils.getUserNameWithSystem();
        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(mainOrderNumber);

        if (OrderTypeEnum.ELASTIC.getCode().equals(orderDetailResp.getOrderType())
                && OrderElasticType.getWeekMonthElasticType().contains(orderDetailResp.getElasticType())){
            throw BizException.makeThrow("周弹性、月弹性需求，仅允许系统自动发起预扣");
        }

        // 有订单查询权限的都可以发起预扣
        orderCommonService.checkOrderQueryAuth(orderDetailResp);

        // 获取供应方案明细
        List<OrderSupplyPlanDetailWithPlanDTO> supplys = supplyPlanQueryService.getAvailableDetailWithPlan(mainOrderNumber);
        // 根据订单明细填充校验请求参数中的一些值
        checkAndFillForProductPreDeduct(req, supplys);

        // 预扣前检查
        checkPreDeducted(orderDetailResp, req);

        String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(mainOrderNumber,
                OrderSubFlowNoPrefixEnum.R);
        // 插入预扣数据，产品预扣，plan直接生效，item直接等待创建中
        insertPreDeductPlanAndDetails(req, orderSubFlowNo, user,
                PreDeductOrderStatusEnum.WAIT_CREATING, PreDeductPlanStatusEnum.VALID, false);

        FlowNodeRecordWithFlowInfoDTO currentNodeData = flowService.queryProcessingFlowNode(
                mainOrderNumber, OrderFlowEnum.ORDER_MAIN.getCode());

        // 主流程自旋一次，记录产品预扣操作，将被预扣的订单明细打标
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(orderDetailResp);
        valueSetReq.setOperateEvent("产品预扣");
        valueSetReq.setFlowCode(currentNodeData.getFlowInfo().getFlowCode());
        valueSetReq.setNodeCode(currentNodeData.getNodeCode());
        valueSetReq.setBizId(mainOrderNumber);
        valueSetReq.setOperateRemark(req.getFlowRemark());
        // 主流程节点自旋一次
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        // 此处无需给预扣的订单明细打标，在后续的判断是否需要生成供应方案拆单时再打标，
        // 原因是此时打标之后拆单时无法区分订单明细是普通预扣打标,还是产品预扣打标，影响到拆单后生成的订单明细哪个应该打标
        OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 产品预扣信息反写供应方案、拆单
        supplyPlanOperateService.preDeductToSupplyPlan(mainOrderNumber, orderSubFlowNo);

        // 记录预扣日志
        Set<String> preDeductPlanNumberList = new HashSet<>();
        preDeductPlanNumberList.add(orderSubFlowNo);
        logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);

        // 延迟发起云霄预扣
        PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> preDeductOrderService.createYunXiaoPreDeductForOrder(orderDetailResp.getOrderNumber()),
                5, TimeUnit.SECONDS);
    }

    private Tuple2<PreDeductPlanDO, List<PreDeductOrderItemDO>> insertPreDeductPlanAndDetails(CreatePreDeductPlanReq req,
            String orderSubFlowNo, String user, PreDeductOrderStatusEnum status,
            PreDeductPlanStatusEnum planStatus, boolean userApply) {
        String mainOrderNumber = req.getOrderNumber();
        // 创建预扣计划
        PreDeductPlanDO preDeductPlan = new PreDeductPlanDO();
        preDeductPlan.setOrderNumber(mainOrderNumber);  // 主订单号
        preDeductPlan.setPreDeductPlanNumber(orderSubFlowNo); // 预扣计划号
        preDeductPlan.setStatus(planStatus.getCode());
        preDeductPlan.setCreateUser(user);
        preDeductPlan.setStartPreDeductDate(req.getStartPreDeductDate());
        preDeductPlan.setEndPreDeductDate(req.getEndPreDeductDate());
        preDeductPlan.setRemark(req.getFlowRemark());
        preDeductPlan.setUserApply(userApply); // true表示用户申请，false表示产品预扣
        preDeductPlan.shareUinSetByList(req.getShareUinList());
        demandDBHelper.insert(preDeductPlan);

        // 创建预扣单item
        List<PreDeductOrderItemDO> preDeductOrderItemDOList = new ArrayList<>();
        for (PreDeductItemDTO preDeductItemDTO : req.getItemList()) {
            PreDeductOrderItemDO preDeductOrderItemDO = preDeductItemDTO.toPreDeductOrderItemDO();
            preDeductOrderItemDO.setPreDeductPlanNumber(orderSubFlowNo);
            preDeductOrderItemDO.setStatus(status.getCode());
            preDeductOrderItemDO.setCreator(user);
            preDeductOrderItemDO.setStartPreDeductDate(preDeductPlan.getStartPreDeductDate());
            preDeductOrderItemDO.setEndPreDeductDate(preDeductPlan.getEndPreDeductDate());
            preDeductOrderItemDO.setPreDeductDay(0);
            preDeductOrderItemDO.setTotalReservedCount(0);
            preDeductOrderItemDO.setTotalReservedCpuCount(0);
            preDeductOrderItemDO.setMatchRate(0f);
            preDeductOrderItemDOList.add(preDeductOrderItemDO);
        }
        demandDBHelper.insert(preDeductOrderItemDOList);
        return Tuple.of(preDeductPlan, preDeductOrderItemDOList);
    }

    private void checkAndFillForProductPreDeduct(CreatePreDeductPlanReq req,
            List<? extends OrderSupplyPlanDetailDO> supplys) {
        Map<Long, OrderSupplyPlanDetailDO> itemDOMap = ListUtils.toMap(supplys,
                OrderSupplyPlanDetailDO::getId,
                Function.identity());
        for (PreDeductItemDTO deduct : req.getItemList()) {
            if (Strings.isBlank(deduct.getZone()) || Strings.isBlank(deduct.getZoneName())) {
                throw BizException.makeThrow("预扣可用区不能为空");
            }
            if (Strings.isBlank(deduct.getInstanceType()) || Strings.isBlank(deduct.getInstanceModel())) {
                throw BizException.makeThrow("预扣机型/实例规格不能为空");
            }
            if (deduct.getInstanceNum() == null || deduct.getInstanceNum() < 1) {
                throw BizException.makeThrow("预扣台数不能小于1");
            }
            if (deduct.getNormalSupplyPlanDetailId() == null) {
                throw BizException.makeThrow("预扣来源的供应方案明细id不能为空");
            }

            OrderSupplyPlanDetailDO itemDO = itemDOMap.get(deduct.getNormalSupplyPlanDetailId());
            if (itemDO == null) {
                throw BizException.makeThrow("预扣的供应方案明细不存在");
            }
            deduct.setBillType(itemDO.getBillType());
//            if (ListUtils.isNotEmpty(itemDO.getOtherZone())) {
//                deduct.setOtherZoneList(itemDO.getOtherZone());
//            }
//            if (ListUtils.isNotEmpty(itemDO.getOtherInstance())) {
//                deduct.setOtherInstanceList(itemDO.getOtherInstance());
//            }
            deduct.setDataDiskType(itemDO.getDataDiskType());
            deduct.setDataDiskStorage(itemDO.getDataDiskStorage());

            deduct.setSystemDiskStorage(itemDO.getSystemDiskStorage());
            deduct.setSystemDiskType(itemDO.getSystemDiskType());
            deduct.setProduct(itemDO.getProduct());

            deduct.setOrderNumber(itemDO.getOrderNumber());
            deduct.setSystemDiskNum(itemDO.getSystemDiskNum());
            deduct.setDataDiskNum(itemDO.getDataDiskNum());

            int sysStorage = itemDO.getSystemDiskStorage() == null ? 0 : itemDO.getSystemDiskStorage();
            int dataStorage = itemDO.getDataDiskStorage() == null ? 0 : itemDO.getDataDiskStorage();
            int dataNum = itemDO.getDataDiskNum() == null ? 0 : itemDO.getDataDiskNum();
            deduct.setTotalDisk((sysStorage + dataStorage * dataNum) * deduct.getInstanceNum());
            // 其余指标前端传
//            deduct.setTotalGpuNum(deduct.getGpuNum() == null ? null
//                    : BigDecimal.valueOf((long) deduct.getInstanceNum() * deduct.getGpuNum()));
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    @TaskLog(taskName = "autoPreDeduct")
    public void autoPreDeduct(String orderNumber) {
        try {
            OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
            if (order == null || ListUtils.isEmpty(order.getItemList())) {
                log.info("订单{}不存在或不存在订单明细信息", orderNumber);
                return;
            }
            if (!PplProductToOrderProductMapEnum.canPreDeductAppRoles().contains(order.getAppRole())) {
                log.info("订单{}的应用角色{}不支持预扣", order.getOrderNumber(), order.getAppRole());
                return;
            }
            if (order.getAutoPreDeduct() == null || !order.getAutoPreDeduct()) {
                log.info("订单{}不支持自动预扣，order.autoPreDeduct为{}", orderNumber, order.getAutoPreDeduct());
                return;
            }
            if (order.getAutoPreDeductBeginDate() == null || order.getAutoPreDeductEndDate() == null) {
                throw BizException.makeThrow("订单%s已设置自动预扣，但未设置预扣开始/结束时间", orderNumber);
            }
            // 检查预扣备选uin
            checkShareUin(order.getCustomerUin(), order.shareUinListGet(), order.getIndustryDept());
            // 仅交付供应/履约跟踪可以自动预扣
            if (!OrderNodeCodeEnum.node_order_supply.getCode().equals(order.getOrderNodeCode())
                    && !OrderNodeCodeEnum.node_order_following.getCode().equals(order.getOrderNodeCode())) {
                log.info("订单{}当前节点{}不支持自动预扣，仅交付供应/履约跟踪可以自动预扣", orderNumber,
                        order.getOrderNodeCode());
                return;
            }
            List<String> banZones = new ArrayList<>(Arrays.asList(orderBanZone.get().split(";")));
            List<PreDeductItemForCreateChoose> list = supplyPlanQueryService.preDeductItemForCreateChoose(order.getOrderNumber(),
                    OrderTypeEnum.ELASTIC.getCode().equals(order.getOrderType())
                            && OrderElasticType.getWeekMonthElasticType().contains(order.getElasticType()));
            boolean needAutoPreDeduct = false;
            for (PreDeductItemForCreateChoose itemDTO : list) {
                if (itemDTO == null) {
                    continue;
                }
                if (banZones.contains(itemDTO.getZone())){
                    log.info("当前禁用可用区{}，跳过该次预扣",  itemDTO.getZone());
                    continue;
                }
                if (itemDTO.getNotPreDeductInstanceNum() > 0) {
                    needAutoPreDeduct = true;
                    break;
                }
            }
            if (!needAutoPreDeduct) {
                log.info("订单{}已全部预扣，无需再进行自动预扣，跳过", orderNumber);
                return;
            }
            // GPU产品无需预扣审批子流程
            boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct());

            OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
            spinReq.setOrder(order);
            String operate = isGpu ? "系统自动发起预扣" : "系统自动发起预扣子流程";
            spinReq.setOperateEvent(operate);
            spinReq.setOperateName(operate);
            spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
            spinReq.setNodeCode(order.getOrderNodeCode());
            spinReq.setBizId(orderNumber);
            // 设置返回值 999 表示主流程在当前节点自旋一次
            spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            spinReq.setOperateUser("system");
            OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);

            // 创建预扣
            final String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(orderNumber,
                    OrderSubFlowNoPrefixEnum.R);

            // 记录预扣发起日志
            Set<String> preDeductPlanNumberList = new HashSet<>();
            preDeductPlanNumberList.add(orderSubFlowNo);
            logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);

            order = orderCommonService.queryOrderDetail(orderNumber);
            LocalDate autoPreDeductBeginDate = OrderTypeEnum.ELASTIC.getCode().equals(order.getOrderType())
                    && OrderElasticType.getWeekMonthElasticType().contains(order.getElasticType()) ?
                    order.getAutoPreDeductBeginDate().minusDays(1) : order.getAutoPreDeductBeginDate();
            LocalDate autoPreDeductEndDate = order.getAutoPreDeductEndDate();
            // 系统自动插入CRP流程中的预扣数据
            Tuple2<PreDeductPlanDO, List<PreDeductOrderItemDO>> res = insertPreDeductPlanAndDetailsByConsensusOrder(order,
                    orderSubFlowNo, list, true,autoPreDeductBeginDate,autoPreDeductEndDate,false);

            if (!isGpu) {
                // 非GPU产品，启动创建预扣订单子流程
                startPreDeductSubFlow(orderSubFlowNo, order, res._2, true,
                        "system", "系统自动发起预扣");
            } else {
                // 如果是周弹性/月弹性，则需要将未来还存在的剩余预扣计划创建出来。
                initWeekMonthOtherPreDeduct(orderCommonService.queryOrderDetail(orderNumber));
            }
            log.info("订单{}自动预扣发起成功，预扣计划号{}", orderNumber, orderSubFlowNo);
        } catch (Exception e) {
            String msg = StrUtil.format("订单【{}】自动预扣异常【{}】", orderNumber, ExceptionUtil.getMessage(e));
            AlarmRobotUtil.doAlarm("autoPreDeduct", msg, null, false);
            throw e;
        }
    }

    private void startPreDeductSubFlow(String orderSubFlowNo, OrderDetailResp order, List<PreDeductOrderItemDO> items,
            boolean autoPreDeduct, String flowInitiatorUser, String flowRemark) {
        // 启动创建预扣订单子流程
        OrderFlowStartReq startReq = new OrderFlowStartReq();
        startReq.setBizId(order.getOrderNumber());
        startReq.setFlowInitiator(flowInitiatorUser);
        if (order.getAppRole().equals(YunxiaoAppRoleEnum.EKS.getCode())) {
            startReq.setFlowCode(OrderFlowEnum.EKS_ORDER_WITHHOLDING.getCode());
        } else if (order.getAppRole().equals(YunxiaoAppRoleEnum.EMR.getCode())) {
            startReq.setFlowCode(OrderFlowEnum.EMR_ORDER_WITHHOLDING.getCode());
        } else {
            startReq.setFlowCode(OrderFlowEnum.ORDER_WITHHOLDING.getCode());
        }
        startReq.setFlowNo(orderSubFlowNo);
        startReq.setFlowRemark(flowRemark);
        startReq.setParentFlowNo(order.getOrderNumber());
        startReq.setOrder(order);

        if (order.getAppRole().equals(YunxiaoAppRoleEnum.EKS.getCode())
                || order.getAppRole().equals(YunxiaoAppRoleEnum.EMR.getCode())) {
            startReq.setProcessorCreator(eksPreDeductCurrentProcessorCreator);
        }
        final OrderInfoWithFlowDTO flowOrder = orderFlowService.startSubFlow(startReq);
        // 发送预扣待审批通知
        String noticeUser = flowOrder.getCurrentProcessor();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrderPreDeduct(flowOrder,
                items, orderSubFlowNo);
        if (autoPreDeduct) {
            // 发起自动预扣消息提醒中的操作人取提单人
            noticeTemplate.put("operator", order.getSubmitUser());
        }
        dictService.eventNotice(CrpEventEnum.order_pre_deduct_wait.getCode(),
                null, null, noticeTemplate, noticeUser);
    }

    private Tuple2<PreDeductPlanDO, List<PreDeductOrderItemDO>> insertPreDeductPlanAndDetailsByConsensusOrder(
            OrderDetailResp order, String orderSubFlowNo, List<PreDeductItemForCreateChoose> list, boolean userApply,
            LocalDate startDate,LocalDate endDate,Boolean notNeedAudit) {
        if (list == null || list.isEmpty()) {
            throw BizException.makeThrow("待预扣明细为空");
        }
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct());
        List<String> banZones = new ArrayList<>(Arrays.asList(orderBanZone.get().split(";")));
        // 创建预扣计划
        PreDeductPlanDO preDeductPlan = new PreDeductPlanDO();
        preDeductPlan.setOrderNumber(order.getOrderNumber());  // 主订单号
        preDeductPlan.setPreDeductPlanNumber(orderSubFlowNo); // 预扣计划号
        preDeductPlan.setStatus(PreDeductPlanStatusEnum.PROCESSING.getCode());
        if (isGpu || notNeedAudit) {
            // GPU产品 或 notNeedAudit 无需预扣审批子流程
            preDeductPlan.setStatus(PreDeductPlanStatusEnum.VALID.getCode());
        }
        preDeductPlan.setCreateUser("system");

        preDeductPlan.setStartPreDeductDate(startDate);
        long days = startDate.until(endDate, ChronoUnit.DAYS);
        if (days >= 30 || days <= -30) {
            // 云霄预扣限制必须小于30天
            endDate = endDate.plusDays(29);
        }
        preDeductPlan.setEndPreDeductDate(endDate);
        preDeductPlan.setRemark(order.getAutoPreDeductRemark());
        preDeductPlan.setUserApply(userApply); // true表示用户申请，false表示产品预扣
        preDeductPlan.shareUinSetByList(order.shareUinListGet());
        demandDBHelper.insert(preDeductPlan);

        // 创建预扣单item
        List<PreDeductOrderItemDO> preDeductOrderItemDOList = new ArrayList<>();
        for (PreDeductItemForCreateChoose item : list) {
            if (item == null || item.getNotPreDeductInstanceNum() <= 0) {
                continue;
            }
            if (banZones.contains(item.getZone())){
                log.info("在当前禁用可用区{}，跳过该次预扣", item.getZone());
                continue;
            }
            PreDeductOrderItemDO preDeductOrderItemDO = item.toPreDeductOrderItemDO();
            preDeductOrderItemDO.setInstanceNum(item.getNotPreDeductInstanceNum());
            preDeductOrderItemDO.setTotalValidCount(preDeductOrderItemDO.getInstanceNum());
            preDeductOrderItemDO.setTotalCore(item.getNotPreDeductInstanceNum() * item.getCpuNum());
            preDeductOrderItemDO.setTotalValidCpuCount(preDeductOrderItemDO.getTotalCore());
            preDeductOrderItemDO.setTotalGpuNum(new BigDecimal(item.getNotPreDeductInstanceNum() * item.getGpuNum()));
            preDeductOrderItemDO.setPreDeductPlanNumber(orderSubFlowNo);
            preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.PROCESSING.getCode());
            if (isGpu || notNeedAudit) {
                // GPU产品 或 无需预扣审批子流程
                preDeductOrderItemDO.setStatus(PreDeductOrderStatusEnum.WAIT_CREATING.getCode());
            }
            preDeductOrderItemDO.setCreator("system");
            preDeductOrderItemDO.setStartPreDeductDate(preDeductPlan.getStartPreDeductDate());
            preDeductOrderItemDO.setEndPreDeductDate(preDeductPlan.getEndPreDeductDate());
            preDeductOrderItemDO.setPreDeductDay(0);
            preDeductOrderItemDO.setTotalReservedCount(0);
            preDeductOrderItemDO.setTotalReservedCpuCount(0);
            preDeductOrderItemDO.setMatchRate(0f);
            preDeductOrderItemDOList.add(preDeductOrderItemDO);
        }
        if (ListUtils.isEmpty(preDeductOrderItemDOList)) {
            throw BizException.makeThrow("预扣明细为空");
        }
        demandDBHelper.insert(preDeductOrderItemDOList);
        return Tuple.of(preDeductPlan, preDeductOrderItemDOList);
    }

    @Override
    public void autoPreDeductDelay(LocalDate date) {
        WhereSQL where = new WhereSQL();
        where.and("flow_code = ?", OrderFlowEnum.ORDER_URGENT_DELAY.getCode());
        where.and("flow_status = ? ", FlowStatusEnum.DONE.getCode());
        // 2025-03-22 之前的延期子流程不处理，因为之前还没有 autoPreDeductDelaySuccess 参数
        LocalDate minDate = LocalDate.of(2025, 3, 22);
        if (date.isBefore(minDate)) {
            date = minDate;
        }
        where.and("update_time >= ?", date);
        where.and("ext_fields_1 is not null");
        List<FlowInfoDO> list = demandDBHelper.getAll(FlowInfoDO.class, where.getSQL(), where.getParams());
        if (ListUtils.isEmpty(list)) {
            return;
        }
        list.forEach(v -> preDeductDelayForOrderDelay(v.getBizId(), v.getId()));
    }

    @Override
    public void preDeductDelayForOrderDelay(String orderNumber, Long delayFlowId) {
        try {
            PreDeductOrderService service = SpringUtil.getBean(PreDeductOrderService.class);
            ((PreDeductOrderServiceImpl) service).preDeductDelayForOrderDelayOperate(orderNumber, delayFlowId);
        } catch (Exception e) {
            String msg = "订单延期引起的预扣续期操作失败，订单号【{}】，订单延期子流程id【{}】，错误消息【{}】";
            msg = StrUtil.format(msg, orderNumber, delayFlowId, e.getMessage());
            AlarmRobotUtil.doAlarm("preDeductDelayForOrderDelay",
                    msg, null, false);
            taskLogService.genRunWarnLog("preDeductDelayForOrderDelay",
                    "preDeductDelayForOrderDelay", msg);
        }
    }

    /**
     * 订单延期引起的预扣续期操作
     *
     * @param orderNumber 订单号
     * @param delayFlowId 订单延期子流程id
     */
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void preDeductDelayForOrderDelayOperate(String orderNumber, Long delayFlowId) {
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            return;
        }
        FlowInfoDO delayFlow = flowService.queryFlowInfoById(delayFlowId);
        if (delayFlow == null || !OrderFlowEnum.ORDER_URGENT_DELAY.getCode().equals(delayFlow.getFlowCode())) {
            return;
        }
        // 加急延期子流程的扩展字段：加急延期发起的请求参数
        String extFields1 = delayFlow.getExtFields1();
        if (StringUtils.isBlank(extFields1)) {
            return;
        }

        OrderUrgentDelayReq delayReq = JSON.parse(extFields1, OrderUrgentDelayReq.class);
        if (delayReq == null || delayReq.getAutoPreDeductDelay() == null || !delayReq.getAutoPreDeductDelay()) {
            return;
        }
        if (delayReq.getAutoPreDeductDelaySuccess() != null && delayReq.getAutoPreDeductDelaySuccess()) {
            // 已完成自动续期
            return;
        }

        // 开启了自动续期，则自动进行预扣续期
        // 获取待处理的预扣明细
        List<PreDeductOrderItemDO> deductItems = filterByConsensusDim(delayReq.getItems(), orderNumber);
        if (ListUtils.isEmpty(deductItems)) {
            return;
        }
        List<Long> ids = new ArrayList<>();
        deductItems.removeIf(item -> {
            if (PreDeductOrderStatusEnum.CAN_DELAY_STATUS.contains(item.getStatus())) {
                // 可续期的预扣单过滤
                ids.add(item.getId());
                return false;
            }
            return true;
        });
        if (ListUtils.isEmpty(deductItems)) {
            return;
        }
        PreDeductDelayReq req = new PreDeductDelayReq();
        req.setOrderNumber(orderNumber);
        req.setOrderDelay(true);
        req.setRemark(delayFlow.getFlowRemark());
        req.setOperator(delayFlow.getFlowInitiator());
        req.setPreDeductItemIds(ids);
        LocalDate endBuyDate = LocalDate.parse(delayReq.getEndBuyDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        req.setEndPreDeductDate(endBuyDate);
        // 预扣续期
        preDeductDelay(deductItems, req, order);

        // 自动预扣延期完成
        delayReq.setAutoPreDeductDelaySuccess(true);
        demandDBHelper.executeRaw(
                "update flow_info set ext_fields_1 = ?, update_time = update_time where id = ?",
                JSON.toJson(delayReq), delayFlowId);
    }

    private List<PreDeductOrderItemDO> filterByConsensusDim(List<UrgentDelayItem> list, String orderNumber) {
        List<PreDeductOrderItemDO> deductItems = queryByIdAndOrderNumber(orderNumber, null);
        if (ListUtils.isEmpty(list)) {
            return deductItems;
        }
        List<PreDeductOrderItemDO> res = new ArrayList<>();
        Map<String, List<PreDeductOrderItemDO>> map = ListUtils.toMapList(deductItems,
                o -> String.join("-", o.getInstanceType(), o.getZoneName()), Function.identity());
        // 获取指定维度的预扣明细
        for (UrgentDelayItem delayItem : list) {
            List<PreDeductOrderItemDO> items = map.get(
                    String.join("-", delayItem.getInstanceType(), delayItem.getZoneName()));
            if (ListUtils.isNotEmpty(items)) {
                res.addAll(items);
            }
        }
        return res;
    }


    /**
     * 预扣续期
     *
     * @param deductItems 待续期的预扣明细信息
     * @param req 预扣续期请求参数
     * @param order 当前主流程生效的订单信息
     */
    private void preDeductDelay(List<PreDeductOrderItemDO> deductItems, PreDeductDelayReq req, OrderDetailResp order) {
        List<PreDeductOrderItemDO> syncYunXiao = new ArrayList<>();
        Set<String> preDeductPlanNumberList = new HashSet<>();
        List<Integer> reservationFormIdList = new ArrayList<>();
        List<Long> notSyncYunXiaoIds = new ArrayList<>();
        for (PreDeductOrderItemDO item : deductItems) {
            if (!PreDeductOrderStatusEnum.CAN_DELAY_STATUS.contains(item.getStatus())) {
                // 可续期的预扣单判断
                String failReason = StrUtil.format("仅支持对【{}】状态的预扣单进行续期。订单号：{}",
                        PreDeductOrderStatusEnum.CAN_DELAY_STATUS_NAME, item.getOrderNumber());
                // 续期失败通知
                sendDelayFailNotice(deductItems, req.getOrderNumber(), failReason);
                throw BizException.makeThrow(failReason);
            }
            item.setEndPreDeductDate(req.getEndPreDeductDate());
            preDeductPlanNumberList.add(item.getPreDeductPlanNumber());
            if (item.getReservationFormId() == null) {
                notSyncYunXiaoIds.add(item.getId());
            } else {
                reservationFormIdList.add(item.getReservationFormId());
                syncYunXiao.add(item);
            }
        }

        // 有云霄预扣单号，则调用云霄的预扣延期
        if (ListUtils.isNotEmpty(syncYunXiao)) {
            // 调用云霄接口
            RenewalPreDeductResp resp = yunXiaoPreDeductDelay(syncYunXiao, req);
            if (resp == null || resp.getSuccess() == null || !resp.getSuccess()) {
                String failReason = resp == null ? "云霄预扣续期接口失败" : "云霄预扣续期接口失败。" + resp.getErrorMsg();
                // 续期失败通知
                sendDelayFailNotice(syncYunXiao, req.getOrderNumber(), failReason);
                throw BizException.makeThrow(failReason);
            }
        }
        // 更新预扣明细的预扣结束时间
        demandDBHelper.update(deductItems);

        String user = Strings.isBlank(req.getOperator()) ? LoginUtils.getUserNameWithSystem() : req.getOperator();
        String operateName = delayOperateContent(reservationFormIdList, notSyncYunXiaoIds, user, req);

        // 自旋写订单操作日志
        OrderInfoWithFlowDTO orderFlow = delayFlowSpin(user, order, req, operateName);
        // 写预扣操作日志
        logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);
    }

    private void sendDelayFailNotice(List<PreDeductOrderItemDO> syncYunXiao, String orderNumber, String failReason) {
        try {
            if (ListUtils.isEmpty(syncYunXiao) || Strings.isBlank(orderNumber)) {
                return;
            }
            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
            if (order == null) {
                return;
            }
            String noticeUser = order.getNoticeUser();
            Map<String, List<PreDeductOrderItemDO>> map = ListUtils.toMapList(syncYunXiao,
                    PreDeductOrderItemDO::getPreDeductPlanNumber, Function.identity());
            map.forEach((k, v) -> {
                Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForYunXiaoPreDeduct(order, v, k);
                noticeTemplate.put("failReason", failReason);
                dictService.eventNotice(CrpEventEnum.order_pre_deduct_delay_fail.getCode(),
                        null, null, noticeTemplate, noticeUser);
            });
        } catch (Exception e) {
            log.error("发送预扣续期失败通知失败", e);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void preDeductDelay(PreDeductDelayReq req) {
        String orderNumber = req.getOrderNumber();
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            throw BizException.makeThrow("订单%s不存在", orderNumber);
        }
        // 获取待处理的预扣明细
        List<PreDeductOrderItemDO> deductItems = queryByIdAndOrderNumber(orderNumber, req.getPreDeductItemIds());
        if (ListUtils.isEmpty(deductItems)) {
            throw BizException.makeThrow("订单号【%s】没有查询到待续期的预扣明细，预扣明细id：%s",
                    orderNumber, req.getPreDeductItemIds());
        }
        // 预扣续期
        preDeductDelay(deductItems, req, order);
    }

    private OrderInfoWithFlowDTO delayFlowSpin(String user, OrderDetailResp order, PreDeductDelayReq req,
            String operateName) {
        OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
        spinReq.setOrder(order);
        spinReq.setOperateEvent(req.isOrderDelay() ? "订单延期" : "预扣续期");
        spinReq.setOperateName(operateName);
        spinReq.setOperateRemark(req.getRemark());
        spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        spinReq.setNodeCode(order.getOrderNodeCode());
        spinReq.setBizId(order.getOrderNumber());
        // 设置返回值 999 表示主流程在当前节点自旋一次
        spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        spinReq.setOperateUser(user);
        return orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);
    }

    private String delayOperateContent(List<Integer> reservationFormIdList, List<Long> notSyncYunXiaoIds,
            String user, PreDeductDelayReq req) {
        String yunXiaoIdMsg = "";
        if (ListUtils.isNotEmpty(reservationFormIdList)) {
            yunXiaoIdMsg = StrUtil.format("云霄预扣id【{}】", reservationFormIdList);
        }
        String crpIdMsg = "";
        if (ListUtils.isNotEmpty(notSyncYunXiaoIds)) {
            crpIdMsg = StrUtil.format("crp预扣id【{}】", notSyncYunXiaoIds);
        }
        return StrUtil.format("【{}】将{} {}续期至【{}】",
                user, yunXiaoIdMsg, crpIdMsg, req.getEndPreDeductDate());
    }

    private RenewalPreDeductResp yunXiaoPreDeductDelay(List<PreDeductOrderItemDO> yunXiaoItems,
            PreDeductDelayReq delayReq) {
        if (ListUtils.isEmpty(yunXiaoItems)) {
            return null;
        }
        final LocalDate endPreDeductDate = delayReq.getEndPreDeductDate();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Set<Integer> yunXiaoIds = ListUtils.toSet(yunXiaoItems, PreDeductOrderItemDO::getReservationFormId);
        PreDeductRenewalReq req = new PreDeductRenewalReq();
        req.setReservationFormId(new ArrayList<>(yunXiaoIds));
        req.setDestroyTime(LocalDateTime.of(endPreDeductDate, LocalTime.MAX).format(formatter));
        req.setReason(Strings.isBlank(delayReq.getRemark()) ? "预扣续期" : delayReq.getRemark());
//        req.setCreator(delayReq.getOperator());
        return yunxiaoAPIService.renewalPreDeduct(req);
    }

    private List<PreDeductOrderItemDO> queryByIdAndOrderNumber(String orderNumber, List<Long> ids) {
        if (Strings.isBlank(orderNumber)) {
            return Collections.emptyList();
        }
        WhereContent where = new WhereContent()
                .andEqual(PreDeductOrderItemDO::getOrderNumber, orderNumber)
                .andInIfValueNotEmpty(PreDeductOrderItemDO::getId, ids);
        return demandDBHelper.getAll(PreDeductOrderItemDO.class, where.getSql(), where.getParams());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void logPreDeduct(OrderLogDO orderLog, Set<String> preDeductPlanNumberList) {
        if (orderLog == null || ListUtils.isEmpty(preDeductPlanNumberList)) {
            return;
        }
        List<PreDeductOrderLogDO> list = new ArrayList<>();
        for (String s : preDeductPlanNumberList) {
            PreDeductOrderLogDO log = PreDeductOrderLogDO.createForInsert(orderLog, s);
            if (log != null) {
                list.add(log);
            }
        }
        demandDBHelper.insert(list);
    }

    @Override
    public void preDeductDeadlineNotice(int days) {
        // 1、0<预扣结束日期-当前日期<= days天
        // 2、预扣单状态为：已创建、部分创建
        WhereSQL where = new WhereSQL();
        where.and("status in (?)",
                ListUtils.newArrayList(PreDeductOrderStatusEnum.CREATED.getCode(),
                        PreDeductOrderStatusEnum.PARTIAL_CREATED.getCode()));
        LocalDate now = LocalDate.now();
        where.and("end_pre_deduct_date <= ?", now.plusDays(days));
        where.and("end_pre_deduct_date > ?", now);
        List<PreDeductOrderItemDO> list = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                where.getSQL(), where.getParams());
        if (ListUtils.isEmpty(list)) {
            return;
        }

        Set<String> orderNumbers = ListUtils.toSet(list, PreDeductOrderItemDO::getOrderNumber);

        LocalDate statTime = DBList.ckcldStdCrpDBHelper.getRawOne(LocalDate.class,
                "select max(stat_time) from std_crp.aws_order_performance_track_df");
        if (statTime != null) {
            // 3、订单履约率<100%
            String sql = "select order_number from std_crp.aws_order_performance_track_df "
                    + " where stat_time = ? and buy_rate < 1 and order_number in (?)";
            List<String> availableOrderNumbers = DBList.ckcldStdCrpDBHelper
                    .getRaw(String.class, sql, statTime, orderNumbers);
            list.removeIf(item -> !availableOrderNumbers.contains(item.getOrderNumber()));
        }

        // 过滤掉已取消的订单
        String cancelOrderSql = "select  DISTINCT order_number  from order_info "
                + "where deleted = 0 and available_status = ? and order_status = ? and order_number in (?)";
        List<String> cancelOrderNumbers = demandDBHelper.getRaw(String.class, cancelOrderSql,
                OrderAvailableStatusEnum.AVAILABLE.getCode(), OrderStatusEnum.CANCELED.getCode(), orderNumbers);
        list.removeIf(item -> cancelOrderNumbers.contains(item.getOrderNumber()));

        if (ListUtils.isEmpty(list)) {
            return;
        }

        Map<String, Set<String>> orderNumberPlanMap = new HashMap<>();
        Map<String, List<PreDeductOrderItemDO>> preDeductMap = new HashMap<>();
        Map<String, Integer> coreMap = new HashMap<>();
        for (PreDeductOrderItemDO itemDO : list) {
            Set<String> preDeductPlanNumberSet = orderNumberPlanMap.computeIfAbsent(itemDO.getOrderNumber(),
                    k -> new HashSet<>());
            preDeductPlanNumberSet.add(itemDO.getPreDeductPlanNumber());

            List<PreDeductOrderItemDO> preDeductList = preDeductMap.computeIfAbsent(itemDO.getPreDeductPlanNumber(),
                    k -> new ArrayList<>());
            preDeductList.add(itemDO);

            Integer core = coreMap.computeIfAbsent(itemDO.getPreDeductPlanNumber(), k -> 0);
            core = core + itemDO.getTotalCore();
            coreMap.put(itemDO.getPreDeductPlanNumber(), core);
        }

        List<OrderInfoDO> orders = orderCommonService.queryAvailableOrders(new ArrayList<>(orderNumberPlanMap.keySet()));
        Map<String, OrderInfoDO> orderMap = ListUtils.toMap(orders, OrderInfoDO::getOrderNumber, Function.identity());
        for (Entry<String, Set<String>> entry : orderNumberPlanMap.entrySet()) {
            String orderNumber = entry.getKey();
            Set<String> preDeductPlanNumberSet = entry.getValue();
            OrderInfoDO order = orderMap.get(orderNumber);
            if (order == null) {
                continue;
            }
            // 发送预扣即将到期通知
            String noticeUser = order.getNoticeUser();
            for (String preDeductPlanNumber : preDeductPlanNumberSet) {
                List<PreDeductOrderItemDO> preDeductList = preDeductMap.get(preDeductPlanNumber);
                Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForYunXiaoPreDeduct(order,
                        preDeductList, preDeductPlanNumber);
                noticeTemplate.put("preDeductTotalCore", coreMap.get(preDeductPlanNumber));
                dictService.eventNotice(CrpEventEnum.order_pre_deduct_deadline.getCode(),
                        null, null, noticeTemplate, noticeUser);
            }
        }
    }

    @Override
    public void createHistoryPreDeductPlan(String orderNumber) {
        String mainOrderNumber = orderNumber;
        final String user = LoginUtils.getUserNameWithSystem();

        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(mainOrderNumber);

        String orderSubFlowNo = orderFlowService.generateOrderSubFlowNo(mainOrderNumber,
                OrderSubFlowNoPrefixEnum.R);

        CreatePreDeductPlanReq createPreDeductPlanReq = new CreatePreDeductPlanReq();
        createPreDeductPlanReq.setCreateUser("system");
        createPreDeductPlanReq.setStartPreDeductDate(orderDetailResp.getBeginBuyDate());
        createPreDeductPlanReq.setEndPreDeductDate(orderDetailResp.getEndBuyDate());
        createPreDeductPlanReq.setOrderNumber(mainOrderNumber);

        List<PreDeductItemDTO> itemList = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderDetailResp.getItemList()) {
            PreDeductItemDTO preDeductItemDTO = new PreDeductItemDTO();
            BeanUtils.copyProperties(orderItemDTO, preDeductItemDTO);
            itemList.add(preDeductItemDTO);
        }
        createPreDeductPlanReq.setItemList(itemList);

        insertPreDeductPlanAndDetails(createPreDeductPlanReq, orderSubFlowNo, user,
                PreDeductOrderStatusEnum.HISTORY_DATA_MOVING, PreDeductPlanStatusEnum.VALID, false);

    }

    @Override
    public void completePreDeduct() {
        // 获取需要补扣的订单
        WhereSQL where = completePreDeductWhere(null);
        List<String> orderNumbers = demandDBHelper.getRaw(String.class,
                "select DISTINCT order_number  from pre_deduct_order_item " + where.getSQL(),
                where.getParams());
        for (String orderNumber : orderNumbers) {
            try {
                PreDeductOrderService preDeductOrderService = SpringUtil.getBean(PreDeductOrderService.class);
                preDeductOrderService.completePreDeductForOrder(orderNumber);
            } catch (Exception e) {
                // non
            }
        }
    }

    @Override
    @TaskLog(taskName = "completePreDeductForOrder")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            waitLockMillisecond = 1000, customExceptionMessage = "当前订单正在操作中，请稍后再试")
    public void completePreDeductForOrder(String orderNumber) {
        try {
            // 获取订单信息
            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE.getCode());
            // 仅进行中的订单可以补扣
            if (order == null || !OrderStatusEnum.PROCESS.getCode().equals(order.getOrderStatus())) {
                return;
            }
            // 获取需要补扣的预扣信息
            WhereSQL where = completePreDeductWhere(orderNumber);
            List<PreDeductOrderItemDO> list = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                    where.getSQL(), where.getParams());
            if (ListUtils.isEmpty(list)) {
                return;
            }

            Set<Integer> yunXiaoIds = ListUtils.toSet(list, PreDeductOrderItemDO::getReservationFormId);
            if (ListUtils.isEmpty(yunXiaoIds)) {
                return;
            }
            List<Integer> yunXiaoIdList = new ArrayList<>(yunXiaoIds);
            // 发起云霄补扣
            BatchUtil.syncBatchExec(yunXiaoIdList, 5,
                    batch -> yunxiaoAPIService.preDeductOrderReserve(batch));
            // 补扣日志
            String user = LoginUtils.getUserNameWithSystem();
            Set<String> preDeductPlanNumberList = ListUtils.toSet(list, PreDeductOrderItemDO::getPreDeductPlanNumber);
            logCompletePreDeductForOrder(user, order, yunXiaoIds, preDeductPlanNumberList);
        } catch (Exception e) {
            // 告警通知
            AlarmRobotUtil.doAlarm("completePreDeduct",
                    "orderNumber【" + orderNumber + "】。" + ExceptionUtil.getMessage(e),
                    null, false);
            throw e;
        }
    }

    private WhereSQL completePreDeductWhere(String orderNumber) {
        // 获取部分创建、创建失败的预扣信息
        WhereSQL where = new WhereSQL()
                .and(" status in (?) ", ListUtils.newArrayList(
                        PreDeductOrderStatusEnum.PARTIAL_CREATED.getCode(),
                        PreDeductOrderStatusEnum.FAILED.getCode()))
                .and(" end_pre_deduct_date >= ? ", LocalDate.now())
                .and(" reservation_form_id is not null ")
                .and(" deleted = 0 ");
        if (Strings.isNotBlank(orderNumber)) {
            where.and(" order_number = ? ", orderNumber);
        }
        return where;
    }

    private void logCompletePreDeductForOrder(String user, OrderInfoDO order, Set<Integer> yunXiaoIds,
            Set<String> preDeductPlanNumberList)  {
        try {
            String operateName = StrUtil.format("【{}】发起补扣，云霄预扣id【{}】", user, yunXiaoIds);
            OrderFlowNodeValueSetReq spinReq = new OrderFlowNodeValueSetReq();
            spinReq.setOrder(order);
            spinReq.setOperateEvent("预扣补扣");
            spinReq.setOperateName(operateName);
            spinReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
            spinReq.setNodeCode(order.getOrderNodeCode());
            spinReq.setBizId(order.getOrderNumber());
            // 系统自动补扣不显示日志
            spinReq.setNotShowLogInMainFlow("system".equals(user));
            // 设置返回值 999 表示主流程在当前节点自旋一次
            spinReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            spinReq.setOperateUser(user);
            OrderInfoWithFlowDTO orderFlow = orderFlowService.nodeRecordSetReturnValueAndPushFlow(spinReq);
            // 写预扣操作日志
            logPreDeduct(orderFlow.getOrderLog(), preDeductPlanNumberList);
        } catch (Exception e) {
            // 告警通知
            AlarmRobotUtil.doAlarm("logCompletePreDeductForOrder",
                    "orderNumber【" + order.getOrderNumber() + "】写补扣日志失败。" + ExceptionUtil.getMessage(e),
                    null, false);
        }
    }

    @Override
    public void preDeductAuditOverTimeNotice() {
        try {
            // 查询出待预扣审批的数据
            WhereSQL where = new WhereSQL();
            where.and("available_status = ?", OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            where.and("order_node_code = ?", OrderNodeCodeEnum.product_approval.getCode());
            where.and("order_status = ?", OrderStatusEnum.PROCESS.getCode());
            List<OrderInfoDO> orders = demandDBHelper.getAll(OrderInfoDO.class, where.getSQL(), where.getParams());
            if (ListUtils.isEmpty(orders)) {
                return;
            }

            Set<String> orderNumbers = ListUtils.toSet(orders, OrderInfoDO::getOrderNumber);
            // 再筛选出超时未审批的
            String sql = "select DISTINCT order_number from pre_deduct_plan "
                    + " where order_number in (?) and status = ? and create_time <= ? ";
            List<String> overTimeOrders = demandDBHelper.getRaw(String.class, sql,
                    orderNumbers, PreDeductPlanStatusEnum.PROCESSING.getCode(), LocalDateTime.now().minusDays(2));
            if (ListUtils.isEmpty(overTimeOrders)) {
                return;
            }
            orders.removeIf(t -> !overTimeOrders.contains(t.getOrderNumber()));
            if (ListUtils.isEmpty(orders)) {
                return;
            }

            Set<Long> orderInfoIds = orders.stream().map(OrderInfoDO::getId).collect(Collectors.toSet());
            where = new WhereSQL();
            where.and("order_info_id in (?)", orderInfoIds);
            List<OrderItemDO> orderItems = demandDBHelper.getAll(OrderItemDO.class, where.getSQL(), where.getParams());

            Map<String, List<OrderItemDO>> itemsMap = ListUtils.toMapList(orderItems,
                    OrderItemDO::getOrderNumber, Function.identity());

            List<String> mainAuditors = orderCommonService.orderMainAuditor();
            List<String> list = ListUtils.newArrayList(OrderFlowEnum.EKS_ORDER_WITHHOLDING.getCode(),
                    OrderFlowEnum.EMR_ORDER_WITHHOLDING.getCode(),
                    OrderFlowEnum.ORDER_WITHHOLDING.getCode());
            for (OrderInfoDO order : orders) {
                List<OrderItemDO> itemList = itemsMap.get(order.getOrderNumber());
                FlowNodeRecordWithFlowInfoDTO flowData = flowService.queryProcessingSubFlowNode(order.getOrderNumber(),
                        OrderFlowEnum.ORDER_MAIN.getCode());
                if (flowData == null || flowData.getFlowInfo() == null
                        || !list.contains(flowData.getFlowInfo().getFlowCode())) {
                    continue;
                }

                OrderInfoWithFlowDTO flowOrder = new OrderInfoWithFlowDTO(
                        new OrderInfoWithDetailDTO(order, itemList), flowData, null);
                // 发送预扣子流程 产品审批 超时通知
                String noticeUser = flowOrder.getNoticeUser() + ";" + flowOrder.getCurrentProcessor();
                Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(flowOrder);
                NoticeParamsUtil.addMainAuditor(noticeTemplate, flowOrder, mainAuditors);
                dictService.eventNotice(CrpEventEnum.order_audit_over_time.getCode(),
                        null, null, noticeTemplate, noticeUser);
            }
        } catch (Exception e) {
            String msg = StrUtil.format("订单预扣超时未审批通知异常。{}", ExceptionUtil.getMessage(e));
            AlarmRobotUtil.doAlarm("preDeductAuditOverTimeNotice", msg, null, false);
            log.error(msg, e);
        }
    }

    @Override
    public List<PreDeductOrderItemDO> queryPreDeductItems(String orderNumber, List<String> status) {
        WhereSQL where = new WhereSQL();
        where.and(" order_number = ? ", orderNumber);
        where.and(" status in (?) ", status);
        return demandDBHelper.getAll(PreDeductOrderItemDO.class, where.getSQL(), where.getParams());
    }

    @Override
    public List<PreDeductAmountCheckItem> checkPreDeductAmount(CreatePreDeductPlanReq req) {
        List<PreDeductAmountCheckItem> result = new ArrayList<>();
        // 1. 获取当前有效的预扣信息
        String preDeductSql = "select total_core,total_gpu_num,instance_type,zone_name, "
                + " gpu_num , total_valid_count , total_valid_cpu_count from pre_deduct_order_item "
                + " where deleted = 0 and order_number = ? and status in (?)";
        List<PreDeductOrderItemDO> preDeductItems = demandDBHelper.getRaw(PreDeductOrderItemDO.class,
                preDeductSql, req.getOrderNumber(), PreDeductOrderStatusEnum.VALID_STATUS);

        // 2. 获取共识需求信息
        String demandSql = "select consensus_demand_cpu_num,consensus_demand_gpu_num,demand_instance_type,demand_zone_name "
                + " from order_consensus_demand_detail "
                + " where deleted = 0 and order_number = ? and available_status = 'available' ";
        List<OrderConsensusDemandDetailDO> demandDetails = demandDBHelper.getRaw(OrderConsensusDemandDetailDO.class,
                demandSql, req.getOrderNumber());

        // 3. 按照实例类型+可用区维度分组
        Map<String, List<PreDeductOrderItemDO>> preDeductMap = ListUtils.toMapList(preDeductItems,
                o -> String.join("_", o.getInstanceType(), o.getZoneName()), Function.identity());
        Map<String, List<OrderConsensusDemandDetailDO>> demandMap = ListUtils.toMapList(demandDetails,
                o -> String.join("_", o.getDemandInstanceType(), o.getDemandZoneName()), Function.identity());
        Map<String, List<PreDeductItemDTO>> reqItemMap = ListUtils.toMapList(req.getItemList(),
                o -> String.join("_", o.getInstanceType(), o.getZoneName()), Function.identity());

        // 4. 检查每个维度下的预扣量是否超过共识需求量
        reqItemMap.forEach((key, values) -> {
            PreDeductAmountCheckItem checkItem = new PreDeductAmountCheckItem();
            List<PreDeductOrderItemDO> existPreDeducts = preDeductMap.getOrDefault(key, new ArrayList<>());
            List<OrderConsensusDemandDetailDO> demands = demandMap.getOrDefault(key, new ArrayList<>());
            PreDeductItemDTO oneReqItem = values.get(0);

            // 设置基本信息
            checkItem.setInstanceType(oneReqItem.getInstanceType());
            checkItem.setZoneName(oneReqItem.getZoneName());

            // 计算GPU数量或CPU核心数
            if (oneReqItem.getTotalGpuNum() != null && oneReqItem.getTotalGpuNum().compareTo(BigDecimal.ZERO) > 0) {
                // GPU场景
                BigDecimal existPreDeductGpuNum = NumberUtils.sum(existPreDeducts,
                        PreDeductOrderItemDO::totalGpuValidCountGetter);
                BigDecimal reqGpuNum = NumberUtils.sum(values, PreDeductItemDTO::getTotalGpuNum);
                BigDecimal demandGpuNum = NumberUtils.sum(demands, OrderConsensusDemandDetailDO::getConsensusDemandGpuNum);

                checkItem.setExistPreDeductAmount(existPreDeductGpuNum);
                checkItem.setReqPreDeductAmount(reqGpuNum);
                checkItem.setDemandAmount(demandGpuNum);
                checkItem.setGpuProduct(true);
            } else {
                // CPU场景
                BigDecimal existPreDeductCpuNum = NumberUtils.sum(existPreDeducts, PreDeductOrderItemDO::getTotalValidCpuCount);
                BigDecimal reqCpuNum = NumberUtils.sum(values, PreDeductItemDTO::getTotalCore);
                BigDecimal demandCpuNum = NumberUtils.sum(demands, OrderConsensusDemandDetailDO::getConsensusDemandCpuNum);

                checkItem.setExistPreDeductAmount(existPreDeductCpuNum);
                checkItem.setReqPreDeductAmount(reqCpuNum);
                checkItem.setDemandAmount(demandCpuNum);
                checkItem.setGpuProduct(false);
            }

            // 判断是否超量
            BigDecimal res = checkItem.getDemandAmount().subtract(checkItem.getExistPreDeductAmount())
                    .subtract(checkItem.getReqPreDeductAmount());
            checkItem.setOverSize(res.compareTo(BigDecimal.ZERO) < 0);

            result.add(checkItem);
        });

        return result;
    }

    @Override
    @TaskLog(taskName = "refreshOrderGridDetailDf2Ck")
    public void refreshOrderGridDetailDf2Ck(LocalDate dataDate) {

        // 仅一天查非销毁状态 且结束预扣时间大于昨天的 预扣单
        // 避免过滤了那些 当天创建当天销毁的预扣单。
        List<PreDeductOrderItemDO> preDeductOrderItemDOS = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where reservation_form_id is not null and "
                        + "(status != ? or end_pre_deduct_date > ?)",
                PreDeductOrderStatusEnum.DESTROYED.getCode(),
                LocalDate.now().minusDays(30));

        // 1.基础策略数据表准备
        Map<Integer, PreDeductOrderItemDO> formIdToItem = preDeductOrderItemDOS.stream()
                .collect(Collectors.toMap(PreDeductOrderItemDO::getReservationFormId, v -> v));
        Map<String, StaticZoneDO> allPlanZoneInfosGroupByCode = dictService.getAllPlanZoneInfosGroupByCode();
        Map<String, IndustryDemandIndustryWarZoneDictDO> customerMap = dictService.queryCustomerMap();
        Map<String, PplGpuRegionZoneDO> stringPplGpuRegionZoneDOMap = ppldictService.queryGpuInstanceMap();
        List<String> overseas = ppldictService.queryAllRegionName(true);
        List<OrderInfoDO> orderInfoList = demandDBHelper.getAll(OrderInfoDO.class, "where available_status = ?",
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        List<OrderConsensusDemandDetailDO> consenInfoList = demandDBHelper.getAll(OrderConsensusDemandDetailDO.class, "where available_status = ?",
                OrderAvailableStatusEnum.AVAILABLE.getCode()); // 共识信息
        Map<String, OrderInfoDO> orderInfoDOMap = orderInfoList.stream().collect(Collectors.toMap(OrderInfoDO::getOrderNumber, v -> v));

        Map<String, List<OrderConsensusDemandDetailDO>> consenInfoDOMap = consenInfoList.stream().collect(Collectors.groupingBy(item->
                String.join("@",item.getOrderNumber(),item.getDemandZoneName(),item.getDemandInstanceType()))); // 按订单号，实例类型，可用区聚合
        consenInfoDOMap.forEach((k,v)->{
            v.sort(Comparator.comparing(OrderConsensusDemandDetailDO::getConsensusBeginBuyDate)); // 按照共识起始购买时间升序，取第一个匹配的
        });

        // 2.分批去云霄查预扣块数据
        List<Integer> list = new ArrayList<>(formIdToItem.keySet());
        List<Grid> gridList = Collections.synchronizedList(new ArrayList<>());
        BatchUtil.syncBatchExec(list,100,
                (batch,processed) -> {
                    double percent = (processed * 100.0) / list.size();
                    log.info("refreshOrderGridDetailDf2Ck-当前进度: {}", String.format("%.2f%%", percent));
                    gridList.addAll(yunxiaoAPIService.queryGridList(batch));
                });

        LocalDateTime now = LocalDateTime.now();

        List<GridOccupyTimeDO> gridBuyTimeList = demandDBHelper.getAll(GridOccupyTimeDO.class);

        // 预扣单Id + 预扣块Id 作为唯一键
        Map<String, GridOccupyTimeDO> gridIdToTime = gridBuyTimeList.stream()
                .collect(Collectors.toMap(o -> Strings.join("@",o.getReservationFormId(),o.getGridId().toString())
                        , v -> v));
        // 3.组装数据
        List<AwsOrderGridDetailDfDO> result = new ArrayList<>();
        for (Grid grid : gridList) {
            AwsOrderGridDetailDfDO awsOrderGridDetailDfDO = new AwsOrderGridDetailDfDO();
            awsOrderGridDetailDfDO.setStatTime(dataDate);
            awsOrderGridDetailDfDO.setCreateTime(now);

            // 3.1 填充预扣计划信息
            awsOrderGridDetailDfDO.setReservationFormId(grid.getFormId());
            PreDeductOrderItemDO preDeductOrderItemDO = formIdToItem.get(grid.getFormId());
            awsOrderGridDetailDfDO.setOrderNumber(preDeductOrderItemDO.getOrderNumber());
            awsOrderGridDetailDfDO.setReservationFormBeginBuyDate(preDeductOrderItemDO.getStartPreDeductDate());
            awsOrderGridDetailDfDO.setReservationFormEndBuyDate(preDeductOrderItemDO.getEndPreDeductDate());
            awsOrderGridDetailDfDO.setPlanRegion(preDeductOrderItemDO.getRegion());
            awsOrderGridDetailDfDO.setPlanRegionName(preDeductOrderItemDO.getRegionName());
            awsOrderGridDetailDfDO.setPlanZoneName(preDeductOrderItemDO.getZoneName());
            awsOrderGridDetailDfDO.setPlanZone(preDeductOrderItemDO.getZone());
            awsOrderGridDetailDfDO.setPlanInstanceType(preDeductOrderItemDO.getInstanceType());
            awsOrderGridDetailDfDO.setPlanInstanceModel(preDeductOrderItemDO.getInstanceModel());


            // 3.2 填充订单信息
            OrderInfoDO orderInfoDO = orderInfoDOMap.get(preDeductOrderItemDO.getOrderNumber());
            if (orderInfoDO != null){
                awsOrderGridDetailDfDO.setIndustryDept(orderInfoDO.getIndustryDept());
                awsOrderGridDetailDfDO.setCustomerShortName(orderInfoDO.getCustomerShortName());
                awsOrderGridDetailDfDO.setCustomerUin(orderInfoDO.getCustomerUin());
                awsOrderGridDetailDfDO.setOrderType(orderInfoDO.getOrderType());
                awsOrderGridDetailDfDO.setOrderCategory(orderInfoDO.getOrderCategory());
                awsOrderGridDetailDfDO.setAppRole(orderInfoDO.getAppRole());
                awsOrderGridDetailDfDO.setWarZone(orderInfoDO.getWarZone());
                awsOrderGridDetailDfDO.setProjectName(orderInfoDO.getProjectName());
                awsOrderGridDetailDfDO.setOrderSource(orderInfoDO.getOrderSource());
                awsOrderGridDetailDfDO.setOrderNodeCode(orderInfoDO.getOrderNodeCode());

                IndustryDemandIndustryWarZoneDictDO industryDemandIndustryWarZoneDictDO = customerMap.get(
                        orderInfoDO.getCustomerShortName());
                awsOrderGridDetailDfDO.setCommonCustomerShortName(industryDemandIndustryWarZoneDictDO != null
                        ? industryDemandIndustryWarZoneDictDO.getCommonCustomerName()
                        : orderInfoDO.getCustomerShortName());
            }

            // 3.2-2 填充订单共识信息(共识起始购买时间)
            String consensusKey = String.join("@", preDeductOrderItemDO.getOrderNumber(), preDeductOrderItemDO.getZoneName(), preDeductOrderItemDO.getInstanceType());
            List<OrderConsensusDemandDetailDO> consensusDemandDetailDO = consenInfoDOMap.get(consensusKey);
            if (ListUtils.isNotEmpty(consensusDemandDetailDO)){
                awsOrderGridDetailDfDO.setConsensusBeginBuyDate(consensusDemandDetailDO.get(0).getConsensusBeginBuyDate());
            }

            // 3.3填充地域信息
            awsOrderGridDetailDfDO.setZone(grid.getReservedZone());
            StaticZoneDO staticZoneDO = allPlanZoneInfosGroupByCode.get(grid.getReservedZone());
            if (staticZoneDO != null){
                awsOrderGridDetailDfDO.setZoneName(staticZoneDO.getZoneName());
                awsOrderGridDetailDfDO.setRegion(staticZoneDO.getRegion());
                awsOrderGridDetailDfDO.setRegionName(staticZoneDO.getRegionName());
                if (overseas.contains(staticZoneDO.getRegionName())){
                    awsOrderGridDetailDfDO.setCustomhouseTitle("境外");
                }else {
                    awsOrderGridDetailDfDO.setCustomhouseTitle("境内");
                }
            }

            // 3.4填充预扣机型信息
            awsOrderGridDetailDfDO.setInstanceModel(grid.getReservedInstanceType());
            PplGpuRegionZoneDO pplGpuRegionZoneDO =
                    stringPplGpuRegionZoneDOMap.get(grid.getReservedInstanceType().replaceAll("-[^-]*$",""));
            if (pplGpuRegionZoneDO != null){
                awsOrderGridDetailDfDO.setGpuType(pplGpuRegionZoneDO.getGpuType());
                awsOrderGridDetailDfDO.setGridGpu(pplGpuRegionZoneDO.getGpuNum());
            }

            // 3.5块原始信息赋值
            awsOrderGridDetailDfDO.setGridId(grid.getGridId());
            awsOrderGridDetailDfDO.setAppId(grid.getAppId());
            awsOrderGridDetailDfDO.setDestroyed(grid.isDestroyed());
            awsOrderGridDetailDfDO.setGridStatus(grid.getGridStatus());
            awsOrderGridDetailDfDO.setGridCreateDate(grid.getCreateDate());
            awsOrderGridDetailDfDO.setYear(grid.getCreateDate().getYear());
            awsOrderGridDetailDfDO.setMonth(grid.getCreateDate().getMonth().getValue());
            awsOrderGridDetailDfDO.setGridModifiedDate(grid.getModifiedDate());
            awsOrderGridDetailDfDO.setGridCpuNum(grid.getCpu());
            awsOrderGridDetailDfDO.setGridMemory(grid.getMemory());
            awsOrderGridDetailDfDO.setIsAvailableGrid(
                    YuxniaoGridStatusEnum.getValidGridStatus().contains(grid.getGridStatus()) ?
                    Boolean.TRUE : Boolean.FALSE);
            awsOrderGridDetailDfDO.setTotalUseDay(awsOrderGridDetailDfDO.getIsAvailableGrid()
                            ? Math.toIntExact(DateUtils.calculateDayDifference(now,grid.getCreateDate()))
                            : Math.toIntExact(DateUtils.calculateDayDifference(grid.getModifiedDate(),grid.getCreateDate()))
                    );

            String key = Strings.join("@", grid.getFormId(), grid.getGridId());
            GridOccupyTimeDO gridOccupyTimeDO = gridIdToTime.get(key);

            // 如果是空闲的情况
            if (YuxniaoGridStatusEnum.IDLE.getCode().equals(grid.getGridStatus())){
                awsOrderGridDetailDfDO.setWaitBuyHour(
                        Math.toIntExact(DateUtils.calculateHourDifference(LocalDateTime.now(),grid.getCreateDate())));
            }

            // 如果当前是占用的情况
            if (YuxniaoGridStatusEnum.OCCUPIED.getCode().equals(grid.getGridStatus())){
                if (gridOccupyTimeDO == null) {
                    awsOrderGridDetailDfDO.setWaitBuyHour(
                            Math.toIntExact(DateUtils.calculateHourDifference(grid.getModifiedDate(),grid.getCreateDate())));
                    GridOccupyTimeDO newGrid = new GridOccupyTimeDO();
                    newGrid.setReservationFormId(grid.getFormId());
                    newGrid.setGridId(grid.getGridId());
                    newGrid.setOccupyTime(grid.getModifiedDate());
                    newGrid.setWaitBuyHour(awsOrderGridDetailDfDO.getWaitBuyHour());
                    gridIdToTime.put(key,newGrid);
                } else {
                    awsOrderGridDetailDfDO.setOccupyTime(gridOccupyTimeDO.getOccupyTime());
                    awsOrderGridDetailDfDO.setWaitBuyHour(gridOccupyTimeDO.getWaitBuyHour());
                }
                awsOrderGridDetailDfDO.setBuyHour(
                        Math.toIntExact(DateUtils.calculateHourDifference(LocalDateTime.now(),grid.getModifiedDate())));
            }

            // 如果是销毁/删除状态
            if (YuxniaoGridStatusEnum.DESTROYED.getCode().equals(grid.getGridStatus())
            || YuxniaoGridStatusEnum.DELETED.getCode().equals(grid.getGridStatus())){

                if (gridOccupyTimeDO != null){
                    awsOrderGridDetailDfDO.setOccupyTime(gridOccupyTimeDO.getOccupyTime());
                    awsOrderGridDetailDfDO.setWaitBuyHour(gridOccupyTimeDO.getWaitBuyHour());

                    // 购买时长 = 最后修改时间 - 购买时间
                    awsOrderGridDetailDfDO.setBuyHour(
                            Math.toIntExact(DateUtils.calculateHourDifference(grid.getModifiedDate()
                                    ,awsOrderGridDetailDfDO.getOccupyTime())));
                } else{
                    awsOrderGridDetailDfDO.setWaitBuyHour(0);
                    awsOrderGridDetailDfDO.setBuyHour(0);
                    if (grid.getCreateDate().isAfter(LocalDateTime.of(2025,3,1,0,0,0))){
                        // 如果状态已经被销毁了， 但是没有统计到他的购买记录， 说明在一天内买了又退了， 视作无效购买
                        // 这种情况  预扣空闲时间 当做所有时间
                        awsOrderGridDetailDfDO.setWaitBuyHour(
                                Math.toIntExact(DateUtils.calculateHourDifference(grid.getModifiedDate(),grid.getCreateDate())));
                    }

                }

            }

            result.add(awsOrderGridDetailDfDO);
        }

        // 不在范围内的数据查历史数据
        List<AwsOrderGridDetailDfDO> history = ckcldStdCrpDBHelper.getAll(AwsOrderGridDetailDfDO.class, "where stat_time = ? "
                + "and reservation_form_id not in (?)", dataDate.minusDays(1), formIdToItem.keySet());
        Boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
        if (ListUtils.isEmpty(history) && !isTest){
            AlarmRobotUtil.doAlarm("refreshOrderGridDetailDf2Ck",
                    "预扣块数据缺失前一天数据,缺失数据日期：" + dataDate.minusDays(1),
                    Arrays.asList("oliverychen"), false);
            throw BizException.makeThrow("预扣块数据缺失前一天数据,缺失数据日期：" + dataDate.minusDays(1));
        }

        for (AwsOrderGridDetailDfDO awsOrderGridDetailDfDO : history) {
            awsOrderGridDetailDfDO.setStatTime(dataDate);
            awsOrderGridDetailDfDO.setCreateTime(now);
        }
        result.addAll(history);


        // 4 插入数据
        ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.aws_order_grid_detail_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                dataDate);
        ckcldStdCrpDBHelper.insertBatchWithoutReturnId(result);


        List<GridOccupyTimeDO> newBuyGrid = gridIdToTime.values().stream().filter(v -> v.getId() == null)
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(newBuyGrid)){
            demandDBHelper.insertBatchWithoutReturnId(newBuyGrid);
        }
    }

    @Override
    public void firstInitGridBuyTimeDO() {

        Map<String, GridOccupyTimeDO> map = new HashMap<>();
        List<String> raw = DBList.ckcldStdCrpDBHelper.getRaw(String.class,
                "select distinct stat_time from aws_order_grid_detail_df order by stat_time");
        for (String statTime : raw) {
            List<GridOccupyTimeDTO> all = DBList.ckcldStdCrpDBHelper.getRaw(GridOccupyTimeDTO.class,
                    "select reservation_form_id,grid_id,grid_modified_date as occupy_time,floor((grid_modified_date - grid_create_date)/3600) as wait_buy_hour"
                            + " from std_crp.aws_order_grid_detail_df where stat_time = ? and grid_status = ?",
                    statTime, YuxniaoGridStatusEnum.OCCUPIED.getCode());

            for (GridOccupyTimeDTO dto : all) {
                GridOccupyTimeDO gridOccupyTimeDO = new GridOccupyTimeDO();
                BeanUtils.copyProperties(dto, gridOccupyTimeDO);

                map.putIfAbsent(
                        Strings.join("@",dto.getReservationFormId(),dto.getGridId()),
                        gridOccupyTimeDO);
            }
        }

        demandDBHelper.insertBatchWithoutReturnId(map.values());
    }

    @Override
    @TaskLog(taskName = "weekMonthElasticPreDeduct")
    public void weekMonthElasticPreDeduct() {
        OrderQueryReq req = new OrderQueryReq();
        req.setOrderStatus(Arrays.asList(OrderStatusEnum.PROCESS.getCode()));
        req.setElasticTypeList(OrderElasticType.getWeekMonthElasticType());
        List<OrderDetailResp> orderDetailRespList = orderCommonService.queryOrder(req);
        if (ListUtils.isEmpty(orderDetailRespList)){
            return;
        }
        List<String> orderNumberList = orderDetailRespList.stream().map(OrderDetailResp::getOrderNumber)
                .collect(Collectors.toList());

        // 查出部分创建 和 创建失败的 周弹性、月弹性订单进行补扣。
        List<PreDeductOrderItemDO> all = demandDBHelper.getAll(PreDeductOrderItemDO.class,
                "where order_number in (?) and status in (?)",
                orderNumberList, Arrays.asList(PreDeductOrderStatusEnum.PARTIAL_CREATED.getCode()
                , PreDeductOrderStatusEnum.FAILED.getCode()));

        List<Integer> yunXiaoIdList = all.stream().map(PreDeductOrderItemDO::getReservationFormId).distinct()
                .collect(Collectors.toList());
        BatchUtil.syncBatchExec(yunXiaoIdList, 5,
                batch -> yunxiaoAPIService.preDeductOrderReserve(batch));
    }

    @Override
    public void checkShareUin(String orderUin, List<String> shareUinList, String industryDept) {
        if (ListUtils.isEmpty(shareUinList)) {
            return;
        }
        if (StringUtils.isBlank(orderUin)) {
            throw new IllegalArgumentException("orderUin is required");
        }
        Set<String> uinSet = new HashSet<>(shareUinList);
        uinSet.add(orderUin);
        // 订单uin、备选uin 不能重复
        if (shareUinList.size() + 1 != uinSet.size()) {
            throw BizException.makeThrow("存在重复uin数据");
        }
        List<UinNameDTO> uinNameDTOList = dictService.queryCustomerByUin(new ArrayList<>(uinSet), industryDept);
        Map<String, UinNameDTO> map = ListUtils.toMap(uinNameDTOList, UinNameDTO::getUin, Function.identity());
        UinNameDTO order = map.get(orderUin);
        if (order == null) {
            throw BizException.makeThrow("未能查询到订单uin信息，uin: 【%s】， 行业部门：【%s】", orderUin, industryDept);
        }
        // 备选uin的客户简称 是否 与订单uin的客户简称 一致
        boolean sameName;
        // 备选uin的通用客户简称 是否 与订单uin的通用客户简称 一致
        boolean sameCommonName;
        for (String shareUin : shareUinList) {
            sameName = false;
            sameCommonName = false;
            UinNameDTO uinNameDTO = map.get(shareUin);
            if (uinNameDTO == null) {
                throw BizException.makeThrow("未能查询到预扣备选uin信息，uin: 【%s】， 行业部门：【%s】",
                        shareUin, industryDept);
            } else {
                if (Objects.equals(order.getCustomerShortName(), uinNameDTO.getCustomerShortName())) {
                    sameName = true;
                }
                if (Objects.equals(order.getCommonCustomerName(), uinNameDTO.getCommonCustomerName())) {
                    sameCommonName = true;
                }
            }
            if (!sameName && !sameCommonName) {
                throw BizException.makeThrow("预扣备选uin的客户简称或者通用客户简称必须要与订单uin的相同， "
                        + "行业部门：【%s】， 订单uin：【%s】， 订单uin的客户简称：【%s】，订单uin的通用客户简称：【%s】，"
                        + "备选uin：【%s】，备选uin的客户简称：【%s】，备选uin的通用客户简称：【%s】",
                        industryDept, orderUin, order.getCustomerShortName(), order.getCommonCustomerName(),
                        shareUin, uinNameDTO.getCustomerShortName(), uinNameDTO.getCommonCustomerName());
            }
        }
        List<String> yunXiaoUinList = yunxiaoAPIService.queryUinShareList();
        if (ListUtils.isEmpty(yunXiaoUinList)) {
            throw BizException.makeThrow("云霄共享预扣uin配置列表为空，不能使用预扣备选uin");
        }
        for (String s : uinSet) {
            if (!yunXiaoUinList.contains(s)) {
                throw BizException.makeThrow("uin【%s】不在云霄预扣共享预扣uin配置列表中", s);
            }
        }
    }

    @Override
    public ShareUinResp shareUinList(String uin, String industryDept) {
        List<String> shareUinList = yunxiaoAPIService.queryUinShareList();
        if (!shareUinList.contains(uin)) {
            return new ShareUinResp();
        }
        List<UinNameDTO> uinNameDTOList = dictService.queryCustomerByUin(shareUinList, industryDept);
        Map<String, UinNameDTO> map = ListUtils.toMap(uinNameDTOList, UinNameDTO::getUin, Function.identity());
        UinNameDTO order = map.get(uin);
        if (order == null) {
            throw BizException.makeThrow("未能查询到订单uin信息，uin: 【%s】， 行业部门：【%s】", uin, industryDept);
        }
        ShareUinResp resp = new ShareUinResp();
        resp.setOrderUin(order);
        for (UinNameDTO uinNameDTO : uinNameDTOList) {
            if (uinNameDTO == null || Objects.equals(uinNameDTO.getUin(), uin)) {
                continue;
            }
            if (Objects.equals(order.getCustomerShortName(), uinNameDTO.getCustomerShortName())
                    || Objects.equals(order.getCommonCustomerName(), uinNameDTO.getCommonCustomerName())) {
                resp.getValid().add(uinNameDTO);
            } else {
                resp.getInvalid().add(uinNameDTO);
            }
        }
        return resp;
    }



}
