package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.convert.NoticeParamsUtil;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO.PlanListData;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanSaveReq;
import cloud.demand.app.modules.order.dto.resp.CheckSubmitResultDTO;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.OrderInfoWithFlowDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyProgressResp;
import cloud.demand.app.modules.order.dto.resp.supply.process.OrderSupplyProcessVO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.entity.OrderLogDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderRiskLevelEnum;
import cloud.demand.app.modules.order.enums.PplProductToOrderProductMapEnum;
import cloud.demand.app.modules.order.enums.SupplyPlanVersionStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderFlowService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.order.service.function.impl.DefaultOrderItemCreator;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.xy_purchase_order.entiy.DeviceApplyDO;
import cloud.demand.app.modules.xy_purchase_order.enums.XYPurchaseOrderTypeEnum;
import cloud.demand.app.modules.xy_purchase_order.service.PurchaseOrderService;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Service(value = "supplyPlanOperateServiceForDataBase")
@Log4j
public class SupplyPlanOperateServiceForDataBase extends AbstractSupplyPlanOperateService {

    @Resource(name = "supplyPlanOperateServiceForDataBase")
    private SupplyPlanOperateService self;

    @Resource(name = "supplyPlanOperateServiceImpl")
    private SupplyPlanOperateService cvmGpuImpl;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private FlowService flowService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private DefaultOrderItemCreator defaultOrderItemCreator;

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private DictService dictService;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Override
    public boolean support(OrderInfoDO order) {
        return PplProductToOrderProductMapEnum.DATABASE.getOrderCategory().equals(order.getOrderCategory());
    }

    @Override
    @Transactional("demandTransactionManager")
    public OrderSupplyPlanVersionDO getOrCreateWaitCommitVersion(String orderNumber, FlowInfoDO supplyPlanFlow) {
        return getOrCreateWaitCommitVersionWithRemark(orderNumber, null);
    }

    private OrderSupplyPlanVersionDO getOrCreateWaitCommitVersionWithRemark(String orderNumber, String remark) {
        if (Strings.isBlank(orderNumber)) {
            return null;
        }
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                .andEqual(OrderSupplyPlanVersionDO::getStatus, SupplyPlanVersionStatusEnum.wait_commit.getCode());
        OrderSupplyPlanVersionDO res = demandDBHelper
                .getOne(OrderSupplyPlanVersionDO.class, where.getSql(), where.getParams());
        if (res != null) {
            if (remark != null && !Objects.equals(remark, res.getRemark())) {
                res.setRemark(remark);
                demandDBHelper.update(res);
            }
            return res;
        }

        res = new OrderSupplyPlanVersionDO();
        res.setStatus(SupplyPlanVersionStatusEnum.wait_commit.getCode());
        res.setOrderNumber(orderNumber);
        res.setRemark(remark);

        OrderInfoDO order = orderCommonService
                .getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            throw BizException.makeThrow("未能查询到主流程中生效的订单信息，无法创建供应方案版本数据，订单号【%s】",
                    orderNumber);
        }
        // 这里使用主流程号
        res.setSupplyPlanFlowNo(order.getFlowNo());
        res.setOrderInfoId(order.getId());
        // 获取当前生效的版本
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        if (version != null) {
            res.setPreviousAvailableVersionId(version.getId());
        }
        demandDBHelper.insert(res);
        return res;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void saveSupplyPlan(SupplyPlanSaveReq req) {
        if (req == null || Strings.isBlank(req.getOrderNumber()) || ListUtils.isEmpty(req.getWays())) {
            return;
        }

        // 当前流程节点
        FlowNodeRecordWithFlowInfoDTO nodeData = flowService.queryProcessingFlowNode(req.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        List<String> canModifyNodeCodes = ListUtils.newArrayList(OrderNodeCodeEnum.node_order_match_way.getCode(),
                OrderNodeCodeEnum.node_order_supply.getCode(),
                OrderNodeCodeEnum.node_order_following.getCode());
        if (nodeData == null || !canModifyNodeCodes.contains(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，订单号【%s】，当前节点【%s】不允许保存供应方案",
                    req.getOrderNumber(), nodeData == null ? "" : nodeData.getNodeName());
        }
        // 可处理人
        List<String> processors = supplyPlanQueryService.queryPlanProcessorByOrderRecord(req.getOrderNumber());
        String user = LoginUtils.getUserNameWithSystem();
        if (ListUtils.isNotEmpty(processors) && !processors.contains(user)) {
            throw BizException.makeThrow("您不在此订单【%s】的满足方式评估处理人当中，当前登录用户【%s】，可处理人【%s】",
                    req.getOrderNumber(), user, JSON.toJson(processors));
        }

        // 当前待提交供应方案版本
        OrderSupplyPlanVersionDO version = getOrCreateWaitCommitVersionWithRemark(req.getOrderNumber(), req.getRemark());

        // 先获取数据库中当前版本所有满足方式
        List<OrderSupplyPlanDO> allSupplyPlan = supplyPlanQueryService.getAllSupplyPlan(version.getId(),
                req.getOrderNumber());
        List<Long> planIds = ListUtils.transform(allSupplyPlan, BaseDO::getId);

        OrderDetailResp order = orderCommonService
                .getOrderDetail(req.getOrderNumber(), OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.selfHandler();

        // 获取数据库中当前版本的所有供应方案明细
        WhereContent where = new WhereContent().andIn(OrderSupplyPlanDetailDO::getPlanId, planIds);
        List<OrderSupplyPlanDetailDO> allDetail = demandDBHelper
                .getAll(OrderSupplyPlanDetailDO.class, where.getSql(), where.getParams());

        // 解析入参中需要 insert、update 的满足方式数据和方案明细数据，并检查供应机型可用区在提单范围，为共识状态赋值
        PlanListData waitHandler = SupplyPlanMatchWayDTO
                .convertAndCompare(req.getWays(), order, allDetail, allSupplyPlan, version, user);
//        waitHandler.commonCheck();

        // 需要 insert 的满足方式
        if (ListUtils.isNotEmpty(waitHandler.getInsertPlans())) {
            demandDBHelper.insert(waitHandler.getInsertPlans());
        }
        // 需要 update 的满足方式
        if (ListUtils.isNotEmpty(waitHandler.getUpdatePlans())) {
            demandDBHelper.update(waitHandler.getUpdatePlans());
        }

        Set<String> bizIds = new HashSet<>();
        // 星云主单们
        List<String> xyIds = new ArrayList<>();
        // 需要 insert 的方案明细
        if (ListUtils.isNotEmpty(waitHandler.getInsertDetails())) {
            // 上面 insert 供应方案之后 会给 OrderSupplyPlanDO 的 id 字段赋值，在此处再给 planId 字段赋值
            waitHandler.getInsertDetails().forEach(item -> {
                item.setPlanId(item.getPlan().getId());
                if (Strings.isNotBlank(item.getSupplyBizId())) {
                    if (XYPurchaseOrderTypeEnum.ORDER.getCode().equals(item.getSupplyBizType())) {
                        xyIds.add(item.getSupplyBizId());
                    } else {
                        bizIds.addAll(item.supplyBizIdListGet());
                    }
                }
            });
            demandDBHelper.insert(waitHandler.getInsertDetails());
        }
        // 需要 update 的方案明细
        if (ListUtils.isNotEmpty(waitHandler.getUpdateDetails())) {
            // 上面 insert 供应方案之后 会给 OrderSupplyPlanDO 的 id 字段赋值，在此处再给 planId 字段赋值
            waitHandler.getUpdateDetails().forEach(item -> {
                item.setPlanId(item.getPlan().getId());
                if (Strings.isNotBlank(item.getSupplyBizId())) {
                    if (XYPurchaseOrderTypeEnum.ORDER.getCode().equals(item.getSupplyBizType())) {
                        xyIds.add(item.getSupplyBizId());
                    } else {
                        bizIds.addAll(item.supplyBizIdListGet());
                    }
                }
            });
            demandDBHelper.update(waitHandler.getUpdateDetails());
        }

        Map<String, String> bizId2OrderNumber =
                supplyPlanQueryService.getOrderNumberBySupplyBizId(
                        Stream.concat(bizIds.stream(), xyIds.stream()).collect(Collectors.toList()));
        // 校验填写的业务单号是否存在
        if (ListUtils.isNotEmpty(bizIds)) {
            Map<String, OrderSupplyProcessVO> maps = supplyPlanQueryService.callErpForSupplyProcess(
                    new ArrayList<>(bizIds), null);
            for (String bizId : bizIds) {
                OrderSupplyProcessVO supplyProcess = maps.get(bizId);
                if (supplyProcess == null) {
                    throw BizException.makeThrow("您填写的业务单号【%s】不存在，请检查", bizId);
                }
                if (bizId.startsWith("Q")) {
                    String oldOrderNumber = bizId2OrderNumber.get(bizId);
                    if (StringUtils.isNotBlank(oldOrderNumber) && !oldOrderNumber.equals(req.getOrderNumber())) {
                        throw BizException.makeThrow("单个Q单号仅允许关联一个订单，您填写的Q单号【%s】已经关联到订单【%s】", bizId, oldOrderNumber);
                    }
                }
            }
        }
        if (ListUtils.isNotEmpty(xyIds)) {
            Map<String, DeviceApplyDO> deviceApplyDOMap = purchaseOrderService.buildDeviceApplyMap(xyIds);
            for (String xyId : xyIds) {
                DeviceApplyDO deviceApplyDO = deviceApplyDOMap.get(xyId);
                if (deviceApplyDO == null) {
                    throw BizException.makeThrow("单个供应方案明细仅支持填写单个星云订单号，您填写的星云主单号【%s】不存在，请检查", xyId);
                }
                String oldOrderNumber = bizId2OrderNumber.get(xyId);
                if (StringUtils.isNotBlank(oldOrderNumber) && !oldOrderNumber.equals(req.getOrderNumber())) {
                    throw BizException.makeThrow("单个星云订单号仅允许关联一个订单，您填写的星云主单号【%s】已经关联到订单【%s】", xyId, oldOrderNumber);
                }
            }
        }

        // 保存供应方案后 更新一下供应方案处理人
        order.currentProcessorValueSet(orderCommonService.queryOrderOperateUser(IndustryDemandAuthRoleEnum.PRODUCT_PRINCIPAL.getCode(),order));
        demandDBHelper.update(order);

    }

    @Override
    public CheckSubmitResultDTO checkIsAllowSubmit(SupplyPlanSaveReq req,OrderFlowEnum orderFlowEnum) {

        CheckSubmitResultDTO checkSubmitResultDTO = new CheckSubmitResultDTO();

        // 当前订单数据
        OrderDetailResp order = orderCommonService
                .getOrderDetail(req.getOrderNumber(), OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.selfHandler();
        // 当前供应版本
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getNewestVersion(req.getOrderNumber(), order.getFlowNo());

        if (version == null){
            checkSubmitResultDTO.setIsAllowSubmit(Boolean.FALSE);
            checkSubmitResultDTO.setErrorMsg("尚未制定供应方案");
            return checkSubmitResultDTO;
        }

        // 获取供应方案明细
        List<OrderSupplyPlanDetailWithPlanDTO> allDetails = supplyPlanQueryService.getListDetailWithPlanByVersionId(
                version.getId(), req.getOrderNumber());

        try {
            checkDemandSupplyOverForSubmit(order,allDetails);
        } catch (Exception e){
            checkSubmitResultDTO.setIsAllowSubmit(Boolean.FALSE);
            checkSubmitResultDTO.setErrorMsg(e.getMessage());
        }
        return checkSubmitResultDTO;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public CheckSubmitResultDTO submitSupplyPlan(SupplyPlanSaveReq req) {
        // 保存数据
        saveSupplyPlan(req);

        CheckSubmitResultDTO checkSubmitResultDTO = checkIsAllowSubmit(req,null);
        if (!checkSubmitResultDTO.getIsAllowSubmit()){
            // 如果未满足提交条件，则仅保存直接返回
            return checkSubmitResultDTO;
        }

        // 当前流程节点
        FlowNodeRecordWithFlowInfoDTO nodeData = flowService.queryProcessingFlowNode(req.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        List<String> canModifyNodeCodes = ListUtils.newArrayList(OrderNodeCodeEnum.node_order_match_way.getCode(),
                OrderNodeCodeEnum.node_order_supply.getCode(),
                OrderNodeCodeEnum.node_order_following.getCode());
        if (nodeData == null || !canModifyNodeCodes.contains(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，订单号【%s】，当前节点【%s】不允许提交供应方案",
                    req.getOrderNumber(), nodeData == null ? "" : nodeData.getNodeName());
        }
        // 满足方式评估节点
        boolean isOrderMatchWay = OrderNodeCodeEnum.node_order_match_way.getCode().equals(nodeData.getNodeCode());

        // 当前订单数据
        OrderDetailResp order = orderCommonService
                .getOrderDetail(req.getOrderNumber(), OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.selfHandler();

        // 当前供应版本
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getNewestVersion(req.getOrderNumber(), order.getFlowNo());
        // 判断当前是否待提交待版本
        if (version == null
                || !SupplyPlanVersionStatusEnum.wait_commit.getCode().equals(version.getStatus())) {
            throw BizException.makeThrow("操作失败，当前供应方案版本非待提交状态，"
                            + "订单号【%s】，当前供应方案版本【%s】，当前供应方案版本状态【%s】",
                    req.getOrderNumber(),
                    version == null ? "" : version.getId(), version == null ? "" : version.getStatus());
        }

        List<String> processors = supplyPlanQueryService.queryPlanProcessorByOrderRecord(req.getOrderNumber());
        String user = LoginUtils.getUserNameWithSystem();
        if (ListUtils.isNotEmpty(processors) && !processors.contains(user)) {
            throw BizException.makeThrow("您不在此订单【%s】的方案制定处理人当中，当前登录用户【%s】，可处理人【%s】",
                    req.getOrderNumber(), user, JSON.toJson(processors));
        }

        // 获取供应方案明细
        List<OrderSupplyPlanDetailWithPlanDTO> allDetails = supplyPlanQueryService.getListDetailWithPlanByVersionId(
                version.getId(), req.getOrderNumber());

        // 必须要将订单的所有资源分配完之后才能提交供应方案
        checkDemandSupplyOverForSubmit(order, allDetails);

        // 先将原生效的方案设置为过期
        availableVersionOverdue(req.getOrderNumber());
        // 再将当前待处理的方案版本状态变更为生效中
        version.setStatus(SupplyPlanVersionStatusEnum.available.getCode());
        version.setSubmitUser(user);
        version.setSubmitTime(new Date());
        version.setRemark(req.getRemark());
        demandDBHelper.update(version);

//        PlanListData.commonCheck(allDetails);
        // 获取最晚共识开始购买日期
        Map<String, LocalDate> dateMap = OrderSupplyPlanDetailDO.lateConsensusBeginBuyDateMap(allDetails, order);

        // 当前主流程节点
        FlowNodeRecordDO mainNodeData = flowService.queryProcessingFlowNode(req.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());

        OrderFlowNodeValueSetReq pushFlowReq = new OrderFlowNodeValueSetReq();
        pushFlowReq.setOrder(order);
        pushFlowReq.setOperateEvent("满足方式评估通过");
        pushFlowReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        pushFlowReq.setNodeCode(mainNodeData.getNodeCode());
        pushFlowReq.setBizId(req.getOrderNumber());
        pushFlowReq.setNodeReturn(1);
        pushFlowReq.setOperateUser(user);
        pushFlowReq.setSystem(true);
        pushFlowReq.setOrderItemsCreator((normal, newOrder) -> {
            List<OrderItemDO> newItems = defaultOrderItemCreator.create(normal, newOrder);
            for (OrderItemDO newItem : newItems) {
                // 最晚共识开始购买日期 赋值
                LocalDate lateDate = dateMap.get(newItem.getOrderNumberId());
                newItem.setLateConsensusBeginBuyDate(lateDate);
            }
            return newItems;
        });
        if (isOrderMatchWay) {
            // 满足度方式评估节点，方案制定完成，推进至交付供应
            pushFlowReq.setOperateEvent("满足方式评估通过");
            pushFlowReq.setNodeReturn(1);
        } else {
            // 方案修改完成，在当前节点自旋一次
            pushFlowReq.setOperateEvent("方案修改完成");
            pushFlowReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        }
        final OrderInfoWithFlowDTO flowData = orderFlowService.nodeRecordSetReturnValueAndPushFlow(pushFlowReq);

        // 发送供应方案制定完成的消息通知
        String noticeUser = flowData.getNoticeUser();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(flowData);
        dictService.eventNotice(CrpEventEnum.supply_way_done.getCode(), null, null,
                noticeTemplate, noticeUser);
        // 供应方案生效后，延迟更新订单风险级别
        SupplyPlanOperateServiceImpl.scheduledExecutor.schedule(
                () -> orderCommonOperateService.updateOrderRiskLevelBySupplyPlan(req.getOrderNumber()),
                5, TimeUnit.SECONDS);

        return checkSubmitResultDTO;
    }

    private void checkDemandSupplyOverForSubmit(OrderDetailResp demand,
            List<? extends OrderSupplyPlanDetailDO> allDetail) {
        Map<String, List<OrderSupplyPlanDetailDO>> planDetailMap = ListUtils.toMapList(allDetail,
                OrderSupplyPlanDetailDO::getOrderNumberId, Function.identity());

        // 检查存储、内存是否分配完成
        for (OrderItemDO itemDO : demand.getItemList()) {
            if (itemDO == null) {
                continue;
            }
            BigDecimal totalStorage = itemDO.getTotalDatabaseStorage() == null
                    ? BigDecimal.ZERO : itemDO.getTotalDatabaseStorage();
            int totalMemory = itemDO.getTotalMemory() == null ? 0 : itemDO.getTotalMemory();
            List<OrderSupplyPlanDetailDO> plans = planDetailMap.get(itemDO.getOrderNumberId());
            if (ListUtils.isEmpty(plans)) {
                throw BizException.makeThrow(
                        "订单的所有内存量、存储量分配完之后才能提交供应方案");
            }
            int supplyTotalMemory = 0;
            BigDecimal supplyTotalStorage = BigDecimal.ZERO;
            for (OrderSupplyPlanDetailDO detailDO : plans) {
                if (detailDO == null) {
                    continue;
                }
                if (detailDO.getSupplyTotalMemory() != null) {
                    supplyTotalMemory += detailDO.getSupplyTotalMemory();
                }
                if (detailDO.getSupplyTotalDatabaseStorage() != null) {
                    supplyTotalStorage = supplyTotalStorage.add(detailDO.getSupplyTotalDatabaseStorage());
                }
            }
            // 待分配内存量
            int waitMemory = totalMemory - supplyTotalMemory;
            // 待分配存储量
            BigDecimal waitStorage = totalStorage.subtract(supplyTotalStorage);
            // 待分配的内存量、存储量的绝对值，不能超过需求量的10%
            if (Math.abs(waitMemory) > totalMemory * 0.1
                    || waitStorage.abs().compareTo(totalStorage.multiply(new BigDecimal("0.1"))) > 0) {
                throw BizException.makeThrow("待分配的内存量或存储量绝对值不能超过需求量的10%");
            }
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[1]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void deleteSupplyPlanDetail(List<Long> ids, String orderNumber) {
        if (ListUtils.isEmpty(ids) || Strings.isBlank(orderNumber)) {
            return;
        }
        // 当前流程节点
        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || !OrderNodeCodeEnum.node_order_match_way.getCode().equals(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，仅满足方式评估节点时可以删除供应方案明细，订单号【%s】，当前节点【%s】【%s】",
                    orderNumber, nodeData == null ? "" : nodeData.getNodeCode(),
                    nodeData == null ? "" : nodeData.getNodeName());
        }
        ids.removeIf(Objects::isNull);
        if (ListUtils.isEmpty(ids)) {
            return;
        }
        // 可处理人
        List<String> processors = supplyPlanQueryService.queryPlanProcessorByOrderRecord(orderNumber);
        String user = LoginUtils.getUserNameWithSystem();
        if (ListUtils.isNotEmpty(processors) && !processors.contains(user)) {
            throw BizException.makeThrow("您不在此订单【%s】的满足方式评估处理人当中，当前登录用户【%s】，可处理人【%s】",
                    orderNumber, user, JSON.toJson(processors));
        }
        // 逻辑删除，更新操作人
        String sql = "update order_supply_plan_detail set deleted = 1, update_time = now(), "
                + " supply_modify_time = now(), supply_modify_user = ? "
                + " where id in (?) and deleted = 0 and order_number = ? ";
        demandDBHelper.executeRaw(sql, user, ids, orderNumber);


        // 删除供应方案明细后 更新一下供应方案处理人
        OrderDetailResp order = orderCommonService
                .getOrderDetail(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.currentProcessorValueSet(orderCommonService.queryOrderOperateUser(IndustryDemandAuthRoleEnum.PRODUCT_PRINCIPAL.getCode(),order));
        demandDBHelper.update(order);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[1]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void deleteSupplyPlan(List<Long> ids, String orderNumber) {
        if (ListUtils.isEmpty(ids) || Strings.isBlank(orderNumber)) {
            return;
        }
        ids.removeIf(Objects::isNull);
        if (ListUtils.isEmpty(ids)) {
            return;
        }
        // 当前流程节点
        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null || !OrderNodeCodeEnum.node_order_match_way.getCode().equals(nodeData.getNodeCode())) {
            throw BizException.makeThrow("操作失败，仅满足方式评估节点时可以删除供应方案，订单号【%s】，当前节点【%s】【%s】",
                    orderNumber, nodeData == null ? "" : nodeData.getNodeCode(),
                    nodeData == null ? "" : nodeData.getNodeName());
        }
        // 可处理人
        List<String> processors = supplyPlanQueryService.queryPlanProcessorByOrderRecord(orderNumber);
        String user = LoginUtils.getUserNameWithSystem();
        if (ListUtils.isNotEmpty(processors) && !processors.contains(user)) {
            throw BizException.makeThrow("您不在此订单【%s】的满足方式评估处理人当中，当前登录用户【%s】，可处理人【%s】",
                    orderNumber, user, JSON.toJson(processors));
        }
        // 获取数据库中对应的供应方案明细
        WhereContent where = new WhereContent().andIn(OrderSupplyPlanDetailDO::getPlanId, ids);
        List<OrderSupplyPlanDetailDO> allDetail = demandDBHelper
                .getAll(OrderSupplyPlanDetailDO.class, where.getSql(), where.getParams());

        // 逻辑删除方案数据，更新操作人
        String sql = "update order_supply_plan set deleted = 1, update_time = now(), "
                + " modify_user = ?, modify_time = now() "
                + " where id in (?) and deleted = 0 and order_number = ? ";
        demandDBHelper.executeRaw(sql, user, ids, orderNumber);

        List<Long> detailIds = ListUtils.transform(allDetail, BaseDO::getId);
        // 删除明细数据
        deleteSupplyPlanDetail(detailIds, orderNumber);
    }

    /**
     * 最新的供应方案版本设置为过期
     */
    @Override
    @Transactional("demandTransactionManager")
    public OrderSupplyPlanVersionDO availableVersionOverdue(String orderNumber) {
        OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
        if (version == null) {
            return null;
        }
        version.setStatus(SupplyPlanVersionStatusEnum.overdue.getCode());
        demandDBHelper.update(version);
        //共识需求明细转化为由订单明细生成
        SupplyPlanOperateServiceImpl bean = SpringUtil.getBean(SupplyPlanOperateServiceImpl.class);
        List<StaticZoneDO> zoneAll = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zoneAll, StaticZoneDO::getZoneName, o -> o);
        bean.genFullConsensusDemandByOrder(orderNumber, zoneMap);
        // 生效的供应方案过期后，订单风险级别设置为 评估中
        orderCommonOperateService.updateOrderRiskLevel(orderNumber, OrderRiskLevelEnum.assessing);
        // 生效的供应方案过期后，订单明细最晚共识后开始购买日期 重置 为 订单开始购买日期
        orderCommonOperateService.resetOrderItemsLateConsensusBeginBuyDate(orderNumber);
        return version;
    }

    @Override
    public OrderSupplyProgressResp supplyProgressAndHandler(String orderNumer, String supplyPlanFlowNo) {
        return cvmGpuImpl.supplyProgressAndHandler(orderNumer, supplyPlanFlowNo);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void submitPlanForModifyPlan(SupplyPlanSaveReq req) {
        // 移除所有id，方案修改 的提交，都是需要新增的方案数据（需要在新的方案版本下新增的数据）
        req.removeAllIdForPlanModifySubmit();
        // 提交供应方案
        submitSupplyPlan(req);
    }

    @Override
    public void supplyCreateOverTimeNotice() {
        // 找出所有处于供应满足方式评估节点的数据库订单
        String sql = ORMUtils.getSql("/sql/order/supply/database_supply_over_time.sql");
        List<OrderLogDO> logs = demandDBHelper.getRaw(OrderLogDO.class, sql);

        for (OrderLogDO logDO : logs) {
            try {
                LocalDateTime createTime = DateUtils.toLocalDateTime(logDO.getCreateTime());
                // 超过24小时，发送供应方案制定超时通知
                if (createTime.isBefore(LocalDateTime.now().minusHours(24))) {
                    supplyCreateOverTimeNoticeOne(logDO);
                }
            } catch (Exception e) {
                String msg = StrUtil.format("【{}】数据库供应方案超24小时未制定，发送超时通知异常。{}",
                        logDO.getOrderNumber(), ExceptionUtil.getMessage(e));
                AlarmRobotUtil.doAlarm("supplyCreateOverTimeNoticeOne", msg, null, false);
                log.error(msg, e);
            }
        }
    }

    private void supplyCreateOverTimeNoticeOne(OrderLogDO logDO) {
        WhereSQL where = new WhereSQL();
        where.and("order_info_id = ?", logDO.getOrderInfoId());
        List<OrderItemDO> itemList = demandDBHelper.getAll(OrderItemDO.class, where.getSQL(), where.getParams());
        OrderInfoDO order = demandDBHelper.getByKey(OrderInfoDO.class, logDO.getOrderInfoId());
        FlowNodeRecordWithFlowInfoDTO flowData = flowService.queryProcessingFlowNode(logDO.getOrderNumber(),
                OrderFlowEnum.ORDER_MAIN.getCode());
        OrderInfoWithFlowDTO flowOrder = new OrderInfoWithFlowDTO(
                new OrderInfoWithDetailDTO(order, itemList), flowData, logDO);
        // 发送供应方案制定超时通知
        String noticeUser = flowOrder.getCurrentProcessor();
        Map<String, Object> noticeTemplate = NoticeParamsUtil.toTemplateParamsForOrder(flowOrder);
        dictService.eventNotice(CrpEventEnum.order_supply_plan_wait_create_over_time_database.getCode(),
                null, null, noticeTemplate, noticeUser);
    }
}
