package cloud.demand.app.modules.order.dto.req;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteSoldOutDTO.OfficialWebsiteSoldOutDTODetail;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderItemMatchCore;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order_report.parse.UinTypeWhereParse;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoAppRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cn.hutool.core.map.MapBuilder;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;
import org.nutz.lang.Strings;
import yunti.boot.exception.BizException;

/** 服务水平看板查询条件 */
@Data
public class ServiceLevelQueryReq {

    /** 产品：CVM&CBS  弹性MapReduce  EKS官网 , 对官网服务水平不生效 */
    private String product;

    private YearMonth beginMonth;

    private YearMonth endMonth;

    /** 计费模式名称，对官网服务水平不生效 */
    private List<String> billType;

    private List<String> industryDeptList;

    /** 行业战区，对官网售罄数据不生效 */
    private List<String> warZones;

    /** 客户简称，对官网售罄数据不生效 */
    private List<String> customerShortNames;

    /** 通用客户简称，对官网售罄数据不生效 */
    private List<String> commonCustomerShortNames;

    /** 境内/境外 */
    private String customhouseTitle;

    /** 短的地域名称 */
    private List<String> regionNames;

    private List<String> instanceTypes;

    /** 是否为新机型，true表示是新机型，false表示旧机型 */
    private Boolean newOlderInstanceType;

    /** 是否主力园区，true表示是主力园区，false表示非主力园区 */
    private Boolean mainZone;

    /** 是否主力机型，true表示是主力机型，false表示非主力机型 */
    private Boolean mainInstanceType;

    /** 订单标签
     * @see cloud.demand.app.modules.order.enums.OrderLabelEnum
     * */
    private List<String> orderLabel;

    /** 客户类型：0 - 内部客户，1 - 外部客户 */
    private List<Integer> uinType;

    /** 订单状态：默认为 履约跟踪、订单关闭 */
    private List<String> orderNodeCodeList = ListUtils.newArrayList(
            OrderNodeCodeEnum.node_order_following.getCode(),
            OrderNodeCodeEnum.node_order_close.getCode());

    /** 控制预测提前期分段范围的参数，可以不传有与原型一致的默认值 */
    private Map<Integer, String> advanceWeekRangeMap = new MapBuilder<Integer, String>(new HashMap<>())
            .put(13, "提前13周").put(9, "9～12周").put(6, "6～8周").put(0, "5周内").build();

    public static final List<String> PRODUCT_LIST = ListUtils.newArrayList(Ppl13weekProductTypeEnum.CVM.getName(),
            Ppl13weekProductTypeEnum.EMR.getName(), Ppl13weekProductTypeEnum.EKS.getName());



    public void paramsCheck() {
        if (beginMonth == null || endMonth == null) {
            throw new BizException("查询月份不能为空");
        }
        if (product != null) {
            // CVM&CBS    弹性MapReduce    EKS官网
            if (!PRODUCT_LIST.contains(product)) {
                throw new BizException("产品类型不合法");
            }
        }
        if (ListUtils.isNotEmpty(billType)) {
            for (String s : billType) {
                if (!YunxiaoPayModeEnum.nameList().contains(s)) {
                    throw new BizException("计费模式不合法");
                }
            }
        }
        if (customhouseTitle != null) {
            // 境内    境外
            if (!ListUtils.newArrayList("境内", "境外").contains(customhouseTitle)) {
                throw new BizException("境内/境外不合法");
            }
        }
        // 仅允许查询24年及以后的数据
        if (beginMonth.getYear() < 2024) {
            throw new BizException("仅允许查询2024年及以后的数据");
        }
        // 最多允许查询6个月的数据
        long months = beginMonth.until(endMonth, ChronoUnit.MONTHS);
        /*if (months > 5 || months < -5) {
            throw new BizException("最多允许查询6个月的数据");
        }*/
    }

    public WhereSQL toWhereForOfficialWebsiteBuyData() {
        WhereSQL where = new WhereSQL();
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        // 取时间范围内的所有切片
        where.and(" stat_time >= ? and stat_time <= ? ", begin, end);
        where.and(" buy_date >= ? and buy_date <= ? ", begin, end);
        if (ListUtils.isNotEmpty(industryDeptList)) {
            where.and(" industry_dept in (?)", industryDeptList);
        }
        if (ListUtils.isNotEmpty(regionNames)) {
            where.and(" region_name in (?)", regionNames);
        }
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" gins_family in (?)", instanceTypes);
        }
        if (Strings.isNotBlank(customhouseTitle)) {
            where.and(" customhouse_title = ? ", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(customerShortNames)) {
            where.and(" customer_short_name in (?)", customerShortNames);
        }
        if (ListUtils.isNotEmpty(warZones)) {
            where.and(" war_zone_name in (?)", warZones);
        }

        return where;
    }

    public WhereSQL toWhereForOfficialWebsiteSoldOutData() {
        WhereSQL where = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" instance_family in (?)", instanceTypes);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        where.and(" version >= ? and version <= ? ", begin.format(formatter), end.format(formatter));
        return where;
    }

    public List<OfficialWebsiteSoldOutDTODetail> filterForOfficialWebsiteSoldOutData(
            List<OfficialWebsiteSoldOutDTODetail> input) {
        List<OfficialWebsiteSoldOutDTODetail> res = new ArrayList<>();
        if (ListUtils.isEmpty(input)) {
            return res;
        }
        boolean filterRegionName = ListUtils.isNotEmpty(regionNames);
        boolean filterCustomhouseTitle = customhouseTitle != null;
        boolean filterNewOlderInstanceType = newOlderInstanceType != null;
        boolean filterMainZone = mainZone != null;
        boolean filterMainInstanceType = mainInstanceType != null;
        for (OfficialWebsiteSoldOutDTODetail item : input) {
            if (filterCustomhouseTitle) {
                if (!Objects.equals(customhouseTitle, item.getCustomhouseTitle())) {
                    continue;
                }
            }
            if (filterNewOlderInstanceType) {
                if (!Objects.equals(newOlderInstanceType, item.getNewInstanceType())) {
                    continue;
                }
            }
            if (filterRegionName) {
                if (!regionNames.contains(item.getRegionName())) {
                    continue;
                }
            }
            if (filterMainZone) {
                if (!Objects.equals(mainZone, item.getMainZone())) {
                    continue;
                }
            }
            if (filterMainInstanceType) {
                if (!Objects.equals(mainInstanceType, item.getMainInstanceType())) {
                    continue;
                }
            }
            res.add(item);
        }
        return res;
    }

    public WhereSQL toWhereForOrderServiceLevelDetail(boolean separateNewOldData, boolean userOldData) {
        WhereSQL where = new WhereSQL();
        if (ListUtils.isNotEmpty(industryDeptList)) {
            where.and(" a.industry_dept in (?)", industryDeptList);
        }
//        // 不使用订单中的长的地域名称来查询
//        if (ListUtils.isNotEmpty(regionNames)) {
//            where.and(" b.region_name in (?)", regionNames);
//        }
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" b.instance_type in (?)", instanceTypes);
        }
        if (ListUtils.isNotEmpty(customerShortNames)) {
            where.and(" a.customer_short_name in (?)", customerShortNames);
        }
        if (ListUtils.isNotEmpty(warZones)) {
            where.and(" a.war_zone in (?)", warZones);
        }
        if (ListUtils.isNotEmpty(billType)) {
            List<String> billTypeCodes = new ArrayList<>();
            for (String s : billType) {
                YunxiaoPayModeEnum payMode = YunxiaoPayModeEnum.getByName(s);
                if (payMode == null) {
                    throw new BizException("计费模式不合法");
                }
                billTypeCodes.add(payMode.getCode());
            }
            where.and(" b.bill_type in (?) ", billTypeCodes);
        }
        if (product != null) {
            if (Ppl13weekProductTypeEnum.CVM.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.CVM.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.CVM.getName());
            } else if (Ppl13weekProductTypeEnum.EMR.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.EMR.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.EMR.getName());
            } else if (Ppl13weekProductTypeEnum.EKS.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.EKS.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.EKS.getName());
            }
        }
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        if (separateNewOldData) {
            // 新旧数据区分开来查询
            if (userOldData) {
                YearMonth split = YearMonth.of(2025, 1);
                if (endMonth.isAfter(split)) {
                    end = split.atEndOfMonth();
                }
                if (begin.isAfter(end)) {
                    return null;
                }
                // 旧，25年1月份及1月之前的数据
                where.and(" a.begin_buy_date >= ? and a.begin_buy_date <= ? ", begin, end);
            } else {
                YearMonth split = YearMonth.of(2025, 2);
                if (!beginMonth.isAfter(split)) {
                    begin = split.atDay(1);
                }
                if (begin.isAfter(end)) {
                    return null;
                }
                // 新，25年2月份及2月之后的数据
                where.and(" a.begin_buy_date >= ? and a.begin_buy_date <= ? ", begin, end);
            }
        } else {
            // 不区分新旧数据
            where.and(" a.begin_buy_date >= ? and a.begin_buy_date <= ? ", begin, end);
        }
        // 必须要 到达开始购买时间
        where.and(" a.begin_buy_date <= ? ", LocalDate.now());
        where.and(" a.deleted = 0 and b.deleted = 0 and a.available_status = 'available' ");
        where.and(" a.order_status in ('PROCESS', 'FINISHED') ");
        if (ListUtils.isEmpty(orderNodeCodeList)) {
            // 默认： 履约跟踪、订单关闭
            where.and(" a.order_node_code in ('node_order_following', 'node_order_close') ");
        } else {
            where.and(" a.order_node_code in (?) ", orderNodeCodeList);
        }

        // 订单标签过滤
        if (ListUtils.isNotEmpty(orderLabel)){
            where.and("a.order_label in (?)", orderLabel);
        }
        // 客户类型过滤，0 - 内部客户，1 - 外部客户
        if (ListUtils.isNotEmpty(uinType)){
            WhereSQL whereSQL = UinTypeWhereParse.getWhereSQL(uinType, "a.customer_uin");
            if (whereSQL != null){
                where.and(whereSQL);
            }
        }
        // 报表位数过滤，表示该订单是否需要被服务水平统计
//        where.and("a.report_bit_num & 2 != 0");

        return where;
    }

    public List<OrderItemMatchCore> filterForOrderServiceLevelDetail(List<OrderItemMatchCore> input) {
        List<OrderItemMatchCore> res = new ArrayList<>();
        if (ListUtils.isEmpty(input)) {
            return res;
        }
        boolean filterRegionName = ListUtils.isNotEmpty(regionNames);
        boolean filterCustomhouseTitle = customhouseTitle != null;
        boolean filterNewOlderInstanceType = newOlderInstanceType != null;
        boolean filterMainZone = mainZone != null;
        boolean filterMainInstanceType = mainInstanceType != null;
        boolean filterCommonCustomerShortName = ListUtils.isNotEmpty(commonCustomerShortNames);
        for (OrderItemMatchCore item : input) {
            if (filterRegionName) {
                if (!regionNames.contains(item.getRegionName())) {
                    continue;
                }
            }
            if (filterCustomhouseTitle) {
                if (!Objects.equals(customhouseTitle, item.getCustomhouseTitle())) {
                    continue;
                }
            }
            if (filterNewOlderInstanceType) {
                if (!Objects.equals(newOlderInstanceType, item.getNewInstanceType())) {
                    continue;
                }
            }
            if (filterMainZone) {
                if (!Objects.equals(mainZone, item.getMainZone())) {
                    continue;
                }
            }
            if (filterMainInstanceType) {
                if (!Objects.equals(mainInstanceType, item.getMainInstanceType())) {
                    continue;
                }
            }
            if (filterCommonCustomerShortName) {
                if (!commonCustomerShortNames.contains(item.getCommonCustomerShortName())) {
                    continue;
                }
            }
            res.add(item);
        }
        return res;
    }

}
