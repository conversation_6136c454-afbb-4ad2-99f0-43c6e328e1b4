package cloud.demand.app.modules.order.dto.req;

import cloud.demand.app.modules.order.entity.CrpPplOrderTagConfigDO;
import cloud.demand.app.modules.order.enums.OrderAdvanceWeekEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@Slf4j
public class CalculateOrderTagReq {

    // 订单id
    private String orderNumber;

    // 订单明细id
    private String orderNumberId;

    // uin
    private String uin;

    // 提单时间
    private LocalDate orderCreateDate;

    // 订单明细最后修改时间
    private LocalDate orderItemCreateDate;

    // 开始购买时间
    private LocalDate beginBuyDate;

    // PPL需求年
    private Integer year;

    // PPL需求月
    private Integer month;

    // 产品
    private String orderCategory;

    // 应用角色
    private String appRole;

    // 行业部门
    private String industryDept;

    // 通用客户简称
    private String commonCustomerShortName;

    // 通用实例类型
    private String commonInstanceType;

    // 地域
    private String regionName;

    // 单据核心数
    private Integer orderCore;

    // 打标使用的一些阶段数据
    // 订单提前周（根据提单时间）
    private Integer advanceWeek;
    // 订单明细提前周（根据订单明细最后修改时间）
    private Integer itemAdvanceWeek;
    // 主key
    private String tagKey;
    // 开始购买日期的落点
    private Integer delta;
    // 是否特殊用户配置的标志
    private Boolean ifByUin;


    public void generateMajorKey() {
        this.tagKey = String.join("@",
                this.getYear() + (this.getMonth() >= 10 ? "-" : "-0") + this.getMonth(),
                this.getCommonInstanceType(), this.getRegionName(), this.getOrderCategory(),
                this.getAppRole(), this.getIndustryDept(),
                this.getCommonCustomerShortName());
    }

    public void generateMajorKeyWithConfig(CrpPplOrderTagConfigDO crpPplOrderTagConfigDO) {
        String month = this.getMonth() >= 10 ? "-" : "-0";
        String yearMonth = this.getYear() + month + this.getMonth();
        String orderCategory = this.getOrderCategory();
        String commonInstanceType = this.getCommonInstanceType();
        String regionName = this.getRegionName();
        String appRole = crpPplOrderTagConfigDO.getAppRole();
        String industryDept = StringUtils.isEmpty(crpPplOrderTagConfigDO.getIndustryDept()) ? "PASS-KEY" : crpPplOrderTagConfigDO.getIndustryDept();
        String commonCustomerShortName = StringUtils.isEmpty(crpPplOrderTagConfigDO.getCommonCustomerShortName()) ? "PASS-KEY" : crpPplOrderTagConfigDO.getCommonCustomerShortName();

        this.tagKey = Stream.of(yearMonth, commonInstanceType, regionName, orderCategory, appRole, industryDept, commonCustomerShortName)
                .filter(s -> !"PASS-KEY".equals(s))
                .collect(Collectors.joining("@"));
    }

    public void commonCalculate() {
        long gapDays = ChronoUnit.DAYS.between(this.orderCreateDate, this.beginBuyDate);
        OrderAdvanceWeekEnum targetAdvanceWeek = OrderAdvanceWeekEnum.getByGapDays(gapDays);
        this.advanceWeek = targetAdvanceWeek.getAdvanceWeek();

        gapDays = ChronoUnit.DAYS.between(this.orderItemCreateDate, this.beginBuyDate);
        targetAdvanceWeek = OrderAdvanceWeekEnum.getByGapDays(gapDays);
        this.itemAdvanceWeek = targetAdvanceWeek.getAdvanceWeek();

        int dayOfMonth = beginBuyDate.getDayOfMonth();
        this.delta = dayOfMonth / 7;
    }
}
