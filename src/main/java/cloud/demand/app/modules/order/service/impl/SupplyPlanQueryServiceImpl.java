package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig.PreDeductPlanTime;
import cloud.demand.app.modules.order.dto.SupplyPlanMatchWayDTO;
import cloud.demand.app.modules.order.dto.req.CreatePreDeductPlanReq.PreDeductItemForCreateChoose;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq.OtherInstance;
import cloud.demand.app.modules.order.dto.req.OrderSaveReq.OtherZone;
import cloud.demand.app.modules.order.dto.resp.ConsensusDemandDetailDTO;
import cloud.demand.app.modules.order.dto.resp.ConsensusDemandDetailDTO.InstanceModelAndOtherInfo;
import cloud.demand.app.modules.order.dto.resp.OrderDemandDetailForWaitSupplyResp;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.OrderItemWithSupplyDetail;
import cloud.demand.app.modules.order.dto.resp.OrderItemWithSupplyDetailDTO;
import cloud.demand.app.modules.order.dto.resp.OrderLogResp;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.SupplyDetailWithOrderItemDTO;
import cloud.demand.app.modules.order.dto.resp.SupplyPlanVersionVO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverRecordDTO.DeliverItem;
import cloud.demand.app.modules.order.dto.resp.supply.DeliverTrackAndRiskForecastDTO;
import cloud.demand.app.modules.order.dto.resp.supply.RiskForecastDTO;
import cloud.demand.app.modules.order.dto.resp.supply.RiskForecastDTO.SatisfyInfo;
import cloud.demand.app.modules.order.dto.resp.supply.process.ActualSupplDateGetter;
import cloud.demand.app.modules.order.dto.resp.supply.process.MoveResp;
import cloud.demand.app.modules.order.dto.resp.supply.process.MoveResp.MoveInfo;
import cloud.demand.app.modules.order.dto.resp.supply.process.OrderSupplyProcessVO;
import cloud.demand.app.modules.order.dto.resp.supply.process.QueryOrderSupplyResp;
import cloud.demand.app.modules.order.dto.resp.supply.process.QuotaSupplyDetailResp;
import cloud.demand.app.modules.order.dto.resp.supply.process.QuotaSupplyDetailResp.QuotaSupplyDetailVO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderItemDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cloud.demand.app.modules.order.entity.PreDeductOrderItemDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyConsensusStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import cloud.demand.app.modules.order.enums.PreDeductOrderStatusEnum;
import cloud.demand.app.modules.order.enums.SupplyPlanVersionStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.order.service.function.impl.DefaultOrderCurrentProcessorCreator;
import cloud.demand.app.modules.order.service.function.impl.OrderCategoryToProductCurrentProcessorCreator;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.xy_purchase_order.enums.XYPurchaseOrderTypeEnum;
import cloud.demand.app.modules.xy_purchase_order.service.PurchaseOrderService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Log4j
public class SupplyPlanQueryServiceImpl implements SupplyPlanQueryService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private JsonrpcClient jsonrpcClient;

    @Resource
    private DefaultOrderCurrentProcessorCreator defaultOrderCurrentProcessorCreator;

    @Resource
    private PurchaseOrderService purchaseOrderService;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private PermissionService permissionService;

    @Resource
    private DictService dictService;

    @Resource
    private OrderCategoryToProductCurrentProcessorCreator orderCategoryToProductCurrentProcessorCreator;

    private Supplier<String> supplyProcessUrl = DynamicProperty.create("erp.api.tcres.supply.url", "");

    private Supplier<String> deliveryTransparencyUrl = DynamicProperty.create("erp.api.delivery.transparency.supply.url", "");

    @Override
    public OrderSupplyPlanVersionDO getWaitingHandleVersion(String orderNumber) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                .andIn(OrderSupplyPlanVersionDO::getStatus, SupplyPlanVersionStatusEnum.waitingHandleStatusCodes())
                .orderDesc("id");
        return demandDBHelper.getOne(OrderSupplyPlanVersionDO.class, where.getSql(), where.getParams());
    }

    @Override
    public OrderSupplyPlanVersionDO getNewestVersion(String orderNumber, String supplyPlanFlowNo) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                .andEqual(OrderSupplyPlanVersionDO::getSupplyPlanFlowNo, supplyPlanFlowNo)
                .orderDesc("id");
        return demandDBHelper.getOne(OrderSupplyPlanVersionDO.class, where.getSql(), where.getParams());
    }

    @Override
    public OrderSupplyPlanVersionDO getAvailableVersion(String orderNumber) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                .andEqual(OrderSupplyPlanVersionDO::getStatus, SupplyPlanVersionStatusEnum.available.getCode())
                .orderDesc("id");
        return demandDBHelper.getOne(OrderSupplyPlanVersionDO.class, where.getSql(), where.getParams());
    }

    @Override
    public List<OrderSupplyPlanDO> getAllSupplyPlan(long planVersionId, String orderNumber) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanDO::getVersionId, planVersionId)
                .andEqual(OrderSupplyPlanDO::getOrderNumber, orderNumber);
        return demandDBHelper.getAll(OrderSupplyPlanDO.class, where.getSql(), where.getParams());
    }

    @Override
    public List<OrderSupplyPlanDetailWithPlanDTO> getListDetailWithPlan(List<Long> detailIds) {
        if (ListUtils.isEmpty(detailIds)) {
            return new ArrayList<>();
        }
        List<Long> ids = ListUtils.filter(detailIds, Objects::nonNull);
        WhereContent where = new WhereContent()
                .andIn(OrderSupplyPlanDetailDO::getId, ids);
        return demandDBHelper.getAll(OrderSupplyPlanDetailWithPlanDTO.class, where.getSql(), where.getParams());
    }

    @Override
    public List<OrderSupplyPlanDetailWithPlanDTO> getListDetailWithPlanByVersionId(Long versionId, String orderNumber) {
        List<OrderSupplyPlanDO> allPlan = getAllSupplyPlan(versionId, orderNumber);
        if (ListUtils.isEmpty(allPlan)) {
            return new ArrayList<>();
        }
        List<Long> planIds = allPlan.stream().filter(Objects::nonNull).map(OrderSupplyPlanDO::getId)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(planIds)) {
            return new ArrayList<>();
        }
        WhereContent where = new WhereContent()
                .andIn(OrderSupplyPlanDetailDO::getPlanId, planIds)
                .andEqual(OrderSupplyPlanDetailDO::getOrderNumber, orderNumber);
        return demandDBHelper.getAll(OrderSupplyPlanDetailWithPlanDTO.class, where.getSql(), where.getParams());
    }

    @Override
    public List<OrderSupplyPlanWithDetailDTO> getAllSupplyPlanWithDetails(long planVersionId, String orderNumber) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanDO::getVersionId, planVersionId)
                .andEqual(OrderSupplyPlanDO::getOrderNumber, orderNumber);
        return demandDBHelper.getAll(OrderSupplyPlanWithDetailDTO.class, where.getSql(), where.getParams());
    }

    @Override
    public Map<String, List<OrderSupplyPlanDetailDO>> getSupplyPlanDetailBySupplyBizId(List<String> bizIds) {
        if (ListUtils.isEmpty(bizIds)) {
            return Collections.emptyMap();
        }

        String sql = " select ospd.* " +
                " from order_info oi" +
                " join order_supply_plan_version ospv on oi.order_number = ospv.order_number" +
                " join order_supply_plan osp on ospv.id = osp.version_id" +
                " join order_supply_plan_detail ospd on osp.id = ospd.plan_id";
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" oi.deleted = 0 and ospv.deleted = 0 and osp.deleted = 0 and ospd.deleted = 0");
        whereSQL.and(" oi.available_status = ? ", OrderAvailableStatusEnum.AVAILABLE.getCode());
        whereSQL.and(" oi.order_status = ? ", OrderStatusEnum.PROCESS.getCode());
        whereSQL.and(" ospv.status = ?", OrderAvailableStatusEnum.AVAILABLE.getCode());
        whereSQL.and(" osp.match_type_name = ?", OrderSupplyPlanMatchTypeEnum.BUY.getName());

        for (String bizId : bizIds) {
            whereSQL.and(" ospd.supply_biz_id like ?", "%"+bizId+"%");
        }

        List<OrderSupplyPlanDetailDO> supplyPlanDetailDOS = demandDBHelper.getRaw(OrderSupplyPlanDetailDO.class, sql + whereSQL.getSQL(), whereSQL.getParams());

        Map<String, List<OrderSupplyPlanDetailDO>> result = new HashMap<>();
        for (OrderSupplyPlanDetailDO supplyPlanDetailDO : supplyPlanDetailDOS) {
            for (String s : supplyPlanDetailDO.getSupplyBizId().split(",")) {
                List<OrderSupplyPlanDetailDO> aDefault = result.getOrDefault(s, new ArrayList<>());
                aDefault.add(supplyPlanDetailDO);
                result.put(s, aDefault);
            }
        }

        return result;
    }

    @Override
    public List<OrderSupplyPlanDetailDO> querySupplyPlanDetailByOrderNumber(String matchTypeCode,
            List<String> orderNumberList) {

        List<OrderSupplyPlanDetailDO> all = demandDBHelper.getRaw(OrderSupplyPlanDetailDO.class,
                "select a.* from order_supply_plan_detail a\n"
                        + "left join order_supply_plan b on a.plan_id = b.id and b.deleted = 0 and b.match_type = ? \n"
                        + "left join order_supply_plan_version c on b.version_id = c.id and c.deleted = 0 and c.status = 'available'\n"
                        + "where a.deleted = 0 and b.id is not null and c.id is not null\n"
                        + "and a.order_number in (?)", matchTypeCode, orderNumberList);
        if (ListUtils.isEmpty(all)){
            return new ArrayList<>();
        }
        return all;
    }

    @Override
    public Map<String, String> getOrderNumberBySupplyBizId(List<String> bizIds) {
        Map<String, List<OrderSupplyPlanDetailDO>> supplyPlanDetailMap = getSupplyPlanDetailBySupplyBizId(bizIds);
        HashMap<String, String> result = new HashMap<>();
        for (Map.Entry<String, List<OrderSupplyPlanDetailDO>> entry : supplyPlanDetailMap.entrySet()) {
            List<OrderSupplyPlanDetailDO> detailDOS = entry.getValue();
            if (ListUtils.isNotEmpty(detailDOS)) {
                result.put(entry.getKey(), detailDOS.get(0).getOrderNumber());
            }
        }
        return result;
    }


    /**
     *  获取 Q单 交付明细
     */
    @Override
    public List<QuotaSupplyDetailVO> queryQuotaDetail(String bizId) {
        String url = deliveryTransparencyUrl.get();
//        url = "http://11.134.145.151/delivery-transparency/api";  // 本地调试数据时用的生产环境地址
        if (Strings.isBlank(url)) {
            throw BizException.makeThrow(
                    "未在配置中心配置获取Q单明细的erp接口地址，配置key【erp.api.delivery.transparency.supply.url】");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("quotaId", bizId);
        Map<String, Integer> page = new HashMap<>();
        page.put("start", 0);
        page.put("size", 5000);
        params.put("page", page);
        QuotaSupplyDetailResp obj = jsonrpcClient.jsonrpc("queryQuotaDetail")
                .uri(url)
                .noInjectServletRequest()
                .apiKey("tcres")
                .params(params)
                .postForObject(QuotaSupplyDetailResp.class);
        return obj == null ? null : obj.getData();
    }


    /**
     *  获取 M单 交付明细
     */
    @Override
    public List<MoveInfo> querySmoveDetail(String bizId) {
        String url = deliveryTransparencyUrl.get();
//        url = "http://11.134.145.151/delivery-transparency/api"; // 本地调试数据时用的生产环境地址
        if (Strings.isBlank(url)) {
            throw BizException.makeThrow(
                    "未在配置中心配置获取M单明细的erp接口地址，配置key【erp.api.delivery.transparency.supply.url】");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("orderId", bizId);
        MoveResp obj = jsonrpcClient.jsonrpc("querySmoveDetail")
                .uri(url)
                .noInjectServletRequest()
                .apiKey("tcres")
                .params(params)
                .postForObject(MoveResp.class);
        return obj == null ? null : obj.getData();
    }


    /**
     * 从 erp 接口获取资源准备进度
     *
     * @param bizOrders erp 的业务单（Q单、M单，不能是星云主单）
     */
    @Override
    public Map<String, OrderSupplyProcessVO> callErpForSupplyProcess(List<String> bizOrders, Integer supplyCore) {
        if (ListUtils.isEmpty(bizOrders)) {
            return new HashMap<>();
        }
        Set<String> subOrders = new HashSet<>(bizOrders);
        String url = supplyProcessUrl.get();
//        url = "http://11.177.131.104/api/tcres/supply";
        if (Strings.isBlank(url)) {
            throw BizException.makeThrow(
                    "未在配置中心配置获取资源准备进度的erp接口地址，配置key【erp.api.tcres.supply.url】");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("subOrders", subOrders);
        if (supplyCore != null && supplyCore > 0) {
            params.put("supplyCore", supplyCore);
        }
        QueryOrderSupplyResp obj = jsonrpcClient.jsonrpc("queryOrderSupply")
                .uri(url)
                .noInjectServletRequest()
                .apiKey("tcres")
                .params(params)
                .postForObject(QueryOrderSupplyResp.class);
        if (obj == null || ListUtils.isEmpty(obj.getData())) {
            return new HashMap<>();
        }
        return ListUtils.toMap(obj.getData(), OrderSupplyProcessVO::getOrderId, Function.identity());
    }

    @Override
    public OrderDemandDetailForWaitSupplyResp demandDetailForWaitSupply(String orderNumber, String supplyPlanFlowNo) {
        if (Strings.isBlank(orderNumber)) {
            throw BizException.makeThrow("订单号【%s】不能为空", orderNumber);
        }

        OrderSupplyPlanVersionDO version = null;
        if (Strings.isNotBlank(supplyPlanFlowNo)) {
            // 获取当前方案版本
            version = getNewestVersion(orderNumber, supplyPlanFlowNo);
        } else {
            // 获取当前生效的方案版本
            version = getAvailableVersion(orderNumber);
        }
        OrderDetailResp order;
        List<OrderSupplyPlanDetailDO> supplyDetails = new ArrayList<>();
        if (version != null) {
            // 获取供应方案明细
            List<OrderSupplyPlanWithDetailDTO> plans = getAllSupplyPlanWithDetails(version.getId(), orderNumber);
            if (ListUtils.isNotEmpty(plans)) {
                plans.forEach(item -> supplyDetails.addAll(item.getDetails()));
            }
        }
        if (Strings.isBlank(supplyPlanFlowNo)) {
            // 获取当前主流程生效的订单信息
            order = orderCommonService.getOrderDetail(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE.getCode());
        } else {
            // 获取当前子流程生效的订单信息
            order = orderCommonService.getOrderDetail(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            if (order == null && version != null) {
                // 获取供应方案版本中的订单信息
                order = orderCommonService.getOrderDetailById(version.getOrderInfoId());
            }
        }
        if (order == null) {
            throw BizException.makeThrow(
                    "未能获取到供应方案版本中的订单信息或者子流程中生效的订单信息，供应方案制定子流程号【%s】，订单号【%s】",
                    supplyPlanFlowNo, orderNumber);
        }

        OrderDemandDetailForWaitSupplyResp resp = new OrderDemandDetailForWaitSupplyResp();
        resp.setOrderNumber(orderNumber);
        if (ListUtils.isEmpty(order.getItemList())) {
            return resp;
        }
        order.selfHandler();
        List<OrderItemWithSupplyDetailDTO> details = new ArrayList<>();
        for (OrderItemDTO itemDO : order.getItemList()) {
            if (itemDO == null) {
                continue;
            }
            OrderItemWithSupplyDetailDTO detail = OrderItemWithSupplyDetailDTO.create(itemDO, supplyDetails);
            details.add(detail);
        }
        resp.setDetails(details);
        resp.calcAndSet();

        return resp;
    }

    @Override
    public List<OrderItemDTO> demandDetailForWaitSupplyForDataBase(OrderInfoDO orderInfoDO) {
        // 当前订单数据
        List<OrderItemDTO> all = demandDBHelper.getAll(OrderItemDTO.class, "where order_info_id = ?",
                orderInfoDO.getId());
        // 当前供应版本
        OrderSupplyPlanVersionDO version = getNewestVersion(orderInfoDO.getOrderNumber(), orderInfoDO.getFlowNo());

        // 获取供应方案明细
        List<OrderSupplyPlanDetailWithPlanDTO> allDetails = version == null ?
                new ArrayList<>() : getListDetailWithPlanByVersionId(
                version.getId(), orderInfoDO.getOrderNumber());

        Map<String, List<OrderSupplyPlanDetailDO>> planDetailMap = ListUtils.toMapList(allDetails,
                OrderSupplyPlanDetailDO::getOrderNumberId, Function.identity());

        // 检查存储、内存是否分配完成
        for (OrderItemDTO itemDO : all) {
            itemDO.setIsAlreadySatisfy(Boolean.TRUE);
            BigDecimal totalStorage = itemDO.getTotalDatabaseStorage() == null
                    ? BigDecimal.ZERO : itemDO.getTotalDatabaseStorage();
            int totalMemory = itemDO.getTotalMemory() == null ? 0 : itemDO.getTotalMemory();
            List<OrderSupplyPlanDetailDO> plans = planDetailMap.get(itemDO.getOrderNumberId());
            if (ListUtils.isEmpty(plans)) {
                itemDO.setIsAlreadySatisfy(Boolean.FALSE);
                continue;
            }
            int supplyTotalMemory = 0;
            BigDecimal supplyTotalStorage = BigDecimal.ZERO;
            for (OrderSupplyPlanDetailDO detailDO : plans) {
                if (detailDO == null) {
                    continue;
                }
                if (detailDO.getSupplyTotalMemory() != null) {
                    supplyTotalMemory += detailDO.getSupplyTotalMemory();
                }
                if (detailDO.getSupplyTotalDatabaseStorage() != null) {
                    supplyTotalStorage = supplyTotalStorage.add(detailDO.getSupplyTotalDatabaseStorage());
                }
            }
            // 待分配内存量
            int waitMemory = totalMemory - supplyTotalMemory;
            // 待分配存储量
            BigDecimal waitStorage = totalStorage.subtract(supplyTotalStorage);
            // 待分配的内存量、存储量的绝对值，不能超过需求量的10%
            if (Math.abs(waitMemory) > totalMemory * 0.1
                    || waitStorage.abs().compareTo(totalStorage.multiply(new BigDecimal("0.1"))) > 0) {
                itemDO.setIsAlreadySatisfy(Boolean.FALSE);
            }
        }
        return all;
    }


    @Override
    public List<OrderItemWithSupplyDetail> queryConsensusList(String orderNumber, String supplyPlanFlowNo) {
        OrderSupplyPlanVersionDO newestVersion = getNewestVersion(orderNumber, supplyPlanFlowNo);
        if (newestVersion == null || newestVersion.getStatus()
                .equals(SupplyPlanVersionStatusEnum.wait_commit.getCode())) {
            return new ArrayList<>();
        }

        List<OrderSupplyPlanWithDetailDTO> planList = demandDBHelper.getAll(OrderSupplyPlanWithDetailDTO.class,
                "where version_id = ? ", newestVersion.getId());
        List<String> needShowConsensusStatus = Arrays.asList(OrderSupplyConsensusStatusEnum.wait_consensus.getCode(),
                OrderSupplyConsensusStatusEnum.refuse_consensus.getCode(),
                OrderSupplyConsensusStatusEnum.pass_consensus.getCode());
        Map<String, List<OrderSupplyPlanDetailDO>> orderNumberIdToDetail = new HashMap<>();
        List<String> consensusOrderNumberIdList = new ArrayList<>();
        for (OrderSupplyPlanWithDetailDTO plan : planList) {
            List<String> consensusOrderNumberId = plan.getDetails().stream()
                    .filter(v -> needShowConsensusStatus.contains(v.getConsensusStatus()))
                    .map(OrderSupplyPlanDetailDO::getOrderNumberId).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(consensusOrderNumberId)) {
                consensusOrderNumberIdList.addAll(consensusOrderNumberId);
                Map<String, List<OrderSupplyPlanDetailDO>> map = plan.getDetails().stream()
                        .collect(Collectors.groupingBy(OrderSupplyPlanDetailDO::getOrderNumberId));
                map.forEach((k, v) -> {
                    if (orderNumberIdToDetail.get(k) == null) {
                        orderNumberIdToDetail.put(k, v);
                    } else {
                        List<OrderSupplyPlanDetailDO> orderSupplyPlanDetailDOS = orderNumberIdToDetail.get(k);
                        orderSupplyPlanDetailDOS.addAll(v);
                        orderNumberIdToDetail.put(k, orderSupplyPlanDetailDOS);
                    }
                });
            }
        }

        OrderDetailResp orderDetailResp = orderCommonService.queryOrderDetail(orderNumber, supplyPlanFlowNo);
        List<OrderItemWithSupplyDetail> result = new ArrayList<>();
        for (OrderItemDTO orderItemDTO : orderDetailResp.getItemList()) {
            if (consensusOrderNumberIdList.contains(orderItemDTO.getOrderNumberId())) {
                OrderItemDTO.buildDTO(orderItemDTO);
                OrderItemWithSupplyDetail orderItemWithSupplyDetail = BeanUtil.copyProperties(orderItemDTO,
                        OrderItemWithSupplyDetail.class);
                List<OrderSupplyPlanDetailDO> orderSupplyPlanDetailDOS = orderNumberIdToDetail.get(
                        orderItemDTO.getOrderNumberId());
                orderItemWithSupplyDetail.setPlanDetailDOList(orderSupplyPlanDetailDOS);
                result.add(orderItemWithSupplyDetail);
            }
        }
        return result;
    }

    @Override
    public List<SupplyPlanVersionVO> planVersionList(String orderNumber) {
        WhereContent where = new WhereContent()
                .andEqual(OrderSupplyPlanVersionDO::getOrderNumber, orderNumber)
                // 供应方案历史版本中，只有拒绝共识和已过期的版本，待提交、待共识、生效中都是当前版本
                // 不显示待提交的版本
                .andNotEqual(OrderSupplyPlanVersionDO::getStatus,SupplyPlanVersionStatusEnum.wait_commit.getCode())
                .orderDesc("id");
        List<SupplyPlanVersionVO> result = demandDBHelper.getAll(SupplyPlanVersionVO.class,
                where.getSql(), where.getParams());
        result.forEach(SupplyPlanVersionVO::createVirtualVersionCode);
        return result;
    }

    @Override
    public List<String> queryPlanProcessorByOrderRecord(String orderNumber) {
        if (Strings.isBlank(orderNumber)) {
            return new ArrayList<>();
        }
        OrderInfoDO main = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (main == null) {
            return new ArrayList<>();
        }
        OrderInfoDO order;
        if (Ppl13weekProductTypeEnum.DATABASE.getName().equals(main.getProduct())) {
            WhereContent where = new WhereContent()
                    .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                    .andEqual(OrderInfoDO::getOrderNodeCode, OrderNodeCodeEnum.node_order_match_way.getCode())
                    .orderDesc("id");
            order = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
            if (order == null) {
                // 若没有满足方式评估节点的订单信息，则使用默认的处理人生成器来获取 满足方式评估节点的可处理人
                order = main;
                return defaultOrderCurrentProcessorCreator.getProcessorByFlowNodeAndOrder(
                        OrderNodeCodeEnum.node_order_match_way.getCode(),
                        OrderFlowEnum.ORDER_MAIN.getCode(), order);
            }
        } else {
            WhereContent where = new WhereContent()
                    .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                    .andEqual(OrderInfoDO::getOrderNodeCode, OrderNodeCodeEnum.node_cvm_create_plan.getCode())
                    .orderDesc("id");
            order = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
            if (order == null) {
                // 若没有供应方案制定节点的订单信息，则使用默认的处理人生成器来获取 供应方案制定节点的可处理人
                order = main;
                return defaultOrderCurrentProcessorCreator.getProcessorByFlowNodeAndOrder(
                        OrderNodeCodeEnum.node_cvm_create_plan.getCode(),
                        OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode(), order);
            }
        }

        List<String> res = order.currentProcessorListGet();
        // 云运管可以处理供应方案
        List<String> others = permissionService.queryUserNameByRoleAndIndustry(
                IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE, order.getIndustryDept());
        if (ListUtils.isEmpty(others)) {
            return res;
        }
        Set<String> set = new HashSet<>(res);
        set.addAll(others);
        return new ArrayList<>(set);
    }

    @Override
    public OrderSupplyPlanVersionDO queryVersionById(Long versionId) {
        if (versionId == null) {
            return null;
        }
        return demandDBHelper.getByKey(OrderSupplyPlanVersionDO.class, versionId);
    }

    @Override
    public List<OrderSupplyPlanDetailWithPlanDTO> getAvailableDetailWithPlan(String orderNumber) {
        OrderSupplyPlanVersionDO version = getAvailableVersion(orderNumber);
        if (version == null) {
            return new ArrayList<>();
        }
        return getListDetailWithPlanByVersionId(version.getId(), orderNumber);
    }

    @Override
    public List<SupplyDetailWithOrderItemDTO> getAvailableSupplyDetailWithOrderItem(String orderNumber) {
        OrderSupplyPlanVersionDO version = getAvailableVersion(orderNumber);
        if (version == null) {
            return new ArrayList<>();
        }
        List<OrderSupplyPlanDO> allPlan = getAllSupplyPlan(version.getId(), orderNumber);
        if (ListUtils.isEmpty(allPlan)) {
            return new ArrayList<>();
        }
        List<Long> planIds = allPlan.stream().filter(Objects::nonNull).map(OrderSupplyPlanDO::getId)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(planIds)) {
            return new ArrayList<>();
        }
        WhereContent where = new WhereContent()
                .andIn(OrderSupplyPlanDetailDO::getPlanId, planIds)
                .andEqual(OrderSupplyPlanDetailDO::getOrderNumber, orderNumber);
        List<SupplyDetailWithOrderItemDTO> res = demandDBHelper.getAll(SupplyDetailWithOrderItemDTO.class,
                where.getSql(), where.getParams());
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            throw BizException.makeThrow("未能获取到生效中的订单信息，订单号【%s】", orderNumber);
        }

        Map<String, OrderItemDTO> map = ListUtils.toMap(order.getItemList(), OrderItemDO::getOrderNumberId,
                Function.identity());
        for (SupplyDetailWithOrderItemDTO detail : res) {
            String orderNumberId = Strings.isBlank(detail.getAfterConsensusOrderNumberId())
                    ? detail.getOrderNumberId() : detail.getAfterConsensusOrderNumberId();
            OrderItemDTO orderItem = map.get(orderNumberId);
            if (orderItem != null) {
                detail.setOrderItem(orderItem);
            }
        }
        return res;
    }

    @Override
    public List<PreDeductItemForCreateChoose> preDeductItemForCreateChoose(String orderNumber,
            boolean isWeekMonthElastic) {
        // 获取当前有效的供应方案明细
        List<OrderSupplyPlanDetailWithPlanDTO> supplyDetails = getAvailableDetailWithPlan(orderNumber);

        // 获取当前的有效预扣明细
        // 如果是周弹性/月弹性的预扣创建,不用管历史预扣的 直接返回空list
        List<PreDeductOrderItemDO> preDeducts =
                isWeekMonthElastic ? new ArrayList<>() :
                preDeductOrderService.queryPreDeductItems(orderNumber, PreDeductOrderStatusEnum.VALID_STATUS);

        // 移除有效预扣量小于0的
        preDeducts.removeIf(o -> o == null || o.getTotalValidCount() <= 0);
        // 将预扣明细与供应方案明细进行关联，生成可供选择的待预扣项
        return PreDeductItemForCreateChoose.from(supplyDetails, preDeducts);
    }

    @Override
    public List<DeliverRecordDTO> deliverTrack(Long planId) {
        OrderSupplyPlanWithDetailDTO plan = queryPlanWithDetail(planId);
        return deliverTrack(plan);
    }

    private List<DeliverRecordDTO> deliverTrack(OrderSupplyPlanWithDetailDTO plan) {
        if (plan == null) {
            return new ArrayList<>();
        }
        List<String> needMatchType = ListUtils.newArrayList(OrderSupplyPlanMatchTypeEnum.BUY.getCode(),
                OrderSupplyPlanMatchTypeEnum.MOVE.getCode());
        if (!needMatchType.contains(plan.getMatchType())) {
            return new ArrayList<>();
        }
        List<OrderSupplyPlanDetailDO> details = plan.getDetails();
        if (ListUtils.isEmpty(details)) {
            return new ArrayList<>();
        }

        // 获取所有绑定的星云主单
        List<String> xyPurchaseOrders = new ArrayList<>();
        for (OrderSupplyPlanDetailDO item : details) {
            if (item == null) {
                continue;
            }
            if (XYPurchaseOrderTypeEnum.ORDER.getCode().equals(item.getSupplyBizType())) {
                xyPurchaseOrders.add(item.getSupplyBizId());
            }
        }

        // GPU使用供应卡数指标，其他使用供应核心数指标
        Function<OrderSupplyPlanDetailDO, Integer> indexFuc = indexFunc(plan.getProduct());

        if (OrderSupplyPlanMatchTypeEnum.BUY.getCode().equals(plan.getMatchType())) {
            // 加载这些主单2Q单的映射
            Map<String, List<String>> xyPurchaseOrder2QuotaOrder = purchaseOrderService.buildXYPurchaseOrder2QuotaOrder(xyPurchaseOrders);
            // 使用采购满足供应明细、星云主单与采购单对应关系构建结果
            List<DeliverRecordDTO> res = DeliverRecordDTO.fromSupplyDetails(details, xyPurchaseOrder2QuotaOrder, indexFuc);
            // Q单的预计交付信息、实际交付信息
            resolveQuotaSupplyDetail(res);
            return res;
        }
        // 使用搬迁满足供应明细构建结果
        List<DeliverRecordDTO> res = DeliverRecordDTO.fromSupplyDetails(details, null, indexFuc);
        // M单的预计交付信息、实际交付信息
        resolveMoveSupplyDetail(res);
        return res;
    }

    private OrderSupplyPlanWithDetailDTO queryPlanWithDetail(Long planId) {
        return demandDBHelper.getOne(OrderSupplyPlanWithDetailDTO.class, "where id = ? ", planId);
    }

    private void resolveMoveSupplyDetail(List<DeliverRecordDTO> res) {
        for (DeliverRecordDTO item : res) {
            // 获取预计交付日期
            resolveExpectDeliverMap(item);
            // 获取M单实际交付信息
            resolveActualDeliverMap(item, this::querySmoveDetail);
        }
    }


    private void resolveExpectDeliverMap(DeliverRecordDTO item) {
        int total = item.getTotal();
        if (item.getExpectDeliverList() == null) {
            item.setExpectDeliverList(new ArrayList<>());
        }
        // 获取预计交付信息
        Map<String, OrderSupplyProcessVO> processVOMap = callErpForSupplyProcess(item.getTruthBizIds(),
                total);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        for (OrderSupplyProcessVO value : processVOMap.values()) {
            if (ListUtils.isNotEmpty(value.getAllExpectDeliveryTime())) {
                value.getAllExpectDeliveryTime().forEach((date, deliveryNum) -> {
                    LocalDate localDate;
                    try {
                        localDate = LocalDate.parse(date, formatter);
                    } catch (Exception e) {
                        // 日期不存在，忽略
                        return;
                    }
                    deliveryNum = deliveryNum == null ? 0 : deliveryNum;
                    item.getExpectDeliverList().add(new DeliverRecordDTO.DeliverItem(localDate, deliveryNum));
                });
            }
        }
        // 将预计交付数据按日期合并排序
        item.mergeAndSortExpectDeliverList();

        // 预计交付的核心数/卡数等指标
        int expectTotal = item.getExpectDeliverList().stream().mapToInt(DeliverRecordDTO.DeliverItem::getNum).sum();
        // 解决预计交付数量的gap
        int expectGap = expectTotal - total;
        if (expectGap != 0 && ListUtils.isNotEmpty(item.getExpectDeliverList())) {
            int factor = expectGap > 0 ? 1 : -1;
            int loop = 0;
            while (expectGap != 0) {
                loop++;
                for (DeliverItem deliverItem : item.getExpectDeliverList()) {
                    if (factor > 0 && deliverItem.getNum() <= 1) {
                        continue;
                    }
                    deliverItem.setNum(deliverItem.getNum() - factor);
                    expectGap = expectGap - factor;
                    if (expectGap == 0) {
                        break;
                    }
                }
                if (loop > 200) {
                    // 兜底防止死循环，超出200次，直接退出
                    break;
                }
            }
        }
    }

    private void resolveQuotaSupplyDetail(List<DeliverRecordDTO> res) {
        for (DeliverRecordDTO item : res) {
            // 获取预计交付日期
            resolveExpectDeliverMap(item);
            // 获取Q单实际交付信息
            resolveActualDeliverMap(item, this::queryQuotaDetail);
        }
    }

    /**
     * 通过Q单号、M单号获取实际交付信息，将实际交付信息填充到{@link DeliverRecordDTO}中
     * @param item 交付跟踪信息，主要提供Q单号、M单号、核心数/卡数，并将实际交付信息填充到其中
     * @param querySupplyFuc 查询交付明细信息的函数
     */
    private <T extends ActualSupplDateGetter> void resolveActualDeliverMap(DeliverRecordDTO item,
            Function<String, List<T>> querySupplyFuc) {
        resolveActualDelivers(item.getActualDeliverList(), querySupplyFuc, item.getTruthBizIds(), item.getTotal());
    }

    /**
     * 通过Q单号、M单号获取实际交付信息，将实际交付信息填充到{@link DeliverItem}列表中
     * @param actualDeliverList 实际交付信息结果集，可以为null/空列表
     * @param querySupplyFuc 查询交付明细信息的函数
     * @param truthBizIds 业务ID列表（Q单号或M单号）
     * @param total 总量。可以是核心数、卡数、实例数等指标
     */
    @Override
    public <T extends ActualSupplDateGetter> List<DeliverItem> resolveActualDelivers(List<DeliverItem> actualDeliverList,
            Function<String, List<T>> querySupplyFuc, List<String> truthBizIds, int total) {
        if (actualDeliverList == null) {
            actualDeliverList = new ArrayList<>();
        }
        int deliverNum = 0; // 实际交付台数
        int allNum = 0; // 应交付台数
        for (String truthBizId : truthBizIds) {
            // 获取交付明细信息
            List<T> supplyList = querySupplyFuc.apply(truthBizId);
            if (ListUtils.isNotEmpty(supplyList)) {
                for (ActualSupplDateGetter quota : supplyList) {
                    LocalDate deliveryDate = quota.actualSupplDateGet(); // 单条数据实际交付日期
                    int itemNum = quota.needSupplyNum(); // 单条数据应交付数量
                    allNum += itemNum;
                    if (deliveryDate != null) {
                        // 有交付日期，该条数据记录已交付
                        deliverNum += itemNum;
                        actualDeliverList.add(new DeliverRecordDTO.DeliverItem(deliveryDate, itemNum));
                    }
                }
            }
        }
        if (allNum == 0) {
            // 没有交付明细时，直接返回
            return actualDeliverList;
        }

        // 将实际交付数据按日期合并排序
        DeliverRecordDTO.mergeAndSort(actualDeliverList);

        BigDecimal rate = NumberUtils.divide(total, allNum, 4);
        int deliverTotal = 0;
        for (DeliverItem deliver : actualDeliverList) {
            int num = deliver.getNum();
            // 台数按比例转指标数（核心数/卡数等指标）
            int deliverItem = rate.multiply(new BigDecimal(num)).intValue();
            deliverTotal += deliverItem;
            deliver.setNum(deliverItem);
        }
        if (deliverNum == allNum) {
            // 全部交付时，看看转指标数（核心数/卡数等指标）之后是否存在gap并解决
            int gap = deliverTotal - total;
            if (gap != 0) {
                int factor = gap > 0 ? 1 : -1;
                int loop = 0;
                while (gap != 0) {
                    loop++;
                    for (DeliverItem deliverItem : actualDeliverList) {
                        if (factor > 0 && deliverItem.getNum() <= 1) {
                            continue;
                        }
                        deliverItem.setNum(deliverItem.getNum() - factor);
                        gap = gap - factor;
                        if (gap == 0) {
                            break;
                        }
                    }
                    if (loop > 200) {
                        // 兜底防止死循环，超出200次，直接退出
                        break;
                    }
                }
            }
        }
        return actualDeliverList;
    }

    @Override
    public DeliverTrackAndRiskForecastDTO deliverTrackAndRiskForecast(Long planId)  {
        OrderSupplyPlanWithDetailDTO plan = queryPlanWithDetail(planId);
        if (plan == null || ListUtils.isEmpty(plan.getDetails())) {
            return new DeliverTrackAndRiskForecastDTO();
        }
        OrderInfoDO order = orderCommonService.queryOrderDetail(plan.getOrderNumber());
        if (order == null) {
            throw BizException.makeThrow("订单不存在，订单号【%s】", plan.getOrderNumber());
        }
        return deliverTrackAndRiskForecast(plan, order);
    }

    private DeliverTrackAndRiskForecastDTO deliverTrackAndRiskForecast(OrderSupplyPlanWithDetailDTO plan,
            OrderInfoDO order) {
        List<DeliverRecordDTO> deliverRecords;
        boolean isSatisfyType;
        if (OrderSupplyPlanMatchTypeEnum.SATISFY.getCode().equals(plan.getMatchType())) {
            deliverRecords = deliverRecordForSatisfy(plan, order);
            isSatisfyType = true;
        } else {
            isSatisfyType = false;
            deliverRecords = deliverTrack(plan);
        }
        Function<OrderSupplyPlanDetailDO, Integer> indexFunc = indexFunc(plan.getProduct());
        // 风险预估计算
        List<RiskForecastDTO> risk = RiskForecastDTO.from(plan.getDetails(), indexFunc, order, deliverRecords);
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(plan.getProduct());

        List<PreDeductOrderItemDO> preDeducts = preDeductOrderService.queryPreDeductItems(order.getOrderNumber(),
                PreDeductOrderStatusEnum.VALID_STATUS);

        // 对弹性预扣做处理，只取当前有效周期预扣
        preDeducts = filterValidPreDeduct(order,preDeducts);

        // 风险预估数据匹配预扣数据
        RiskForecastDTO.matchAddValidPreDeduct(risk, preDeducts,
                isGpu ? PreDeductOrderItemDO::totalGpuValidCountGetter : PreDeductOrderItemDO::getTotalValidCpuCount,
                isGpu ? PreDeductOrderItemDO::totalGpuActualCountGetter : PreDeductOrderItemDO::getTotalReservedCpuCount,
                isSatisfyType);
        return DeliverTrackAndRiskForecastDTO.from(deliverRecords, risk);
    }

    /**
     * 对弹性预扣做处理，只取当前有效周期预扣
     * @param order
     * @param preDeducts
     */
    public List<PreDeductOrderItemDO> filterValidPreDeduct(OrderInfoDO order,List<PreDeductOrderItemDO> preDeducts){
        ElasticCycleConfig elasticCycleConfig = orderCommonService.generateElasticPreDeductDTO(order);
        if (elasticCycleConfig == null || elasticCycleConfig.getCurrentPreDeductPlan() == null
        || ListUtils.isEmpty(preDeducts)) {
            return preDeducts;
        }
        LocalDate endPreDeductDate = elasticCycleConfig.getCurrentPreDeductPlan().getEndPreDeductDate();
        preDeducts = preDeducts.stream().filter(v ->
                        cloud.demand.app.common.utils.DateUtils.
                                localDateIsLessThanTo(v.getStartPreDeductDate(), endPreDeductDate)
                                && cloud.demand.app.common.utils.DateUtils.
                                localDateIsGreaterThanOrEqualTo(v.getEndPreDeductDate(), endPreDeductDate))
                .collect(Collectors.toList());
        return preDeducts;
    }

    @Override
    public DeliverTrackAndRiskForecastDTO deliverTrackAndRiskForecast(SupplyPlanMatchWayDTO planData) {
        OrderDetailResp order = orderCommonService.queryOrderDetail(planData.getOrderNumber());
        if (order == null) {
            throw BizException.makeThrow("订单不存在，订单号【%s】", planData.getOrderNumber());
        }
        OrderSupplyPlanWithDetailDTO plan = planData.toSupplyPlanWithDetailDTO(order);
        return deliverTrackAndRiskForecast(plan, order);
    }

    private List<DeliverRecordDTO> deliverRecordForSatisfy(OrderSupplyPlanWithDetailDTO plan, OrderInfoDO order) {
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(order.getProduct())) {
            // GPU暂未计算大盘满足的满足度信息
            return new ArrayList<>();
        }
        if (plan.getId() == null || !OrderSupplyPlanMatchTypeEnum.SATISFY.getCode().equals(plan.getMatchType())) {
            return new ArrayList<>();
        }
        List<Long> detailIds = ListUtils.transform(plan.getDetails(), OrderSupplyPlanDetailDO::getId);
        detailIds.removeIf(Objects::isNull);
        if (ListUtils.isEmpty(detailIds)) {
            return new ArrayList<>();
        }


        //预计满足数据:最晚的模拟对冲数据
        String forecastSql = ORMUtils.getSql("/sql/order/supply/satisfy_forecast_group_data.sql");
        List<OrderItemSatisfyDTO> orginList = demandDBHelper.getRaw(OrderItemSatisfyDTO.class, forecastSql,
                order.getOrderNumber());
        if (ListUtils.isEmpty(orginList)) {
            // 没有对冲数据，表示还未计算大盘满足的满足度，直接返回空列表
            return new ArrayList<>();
        }
        Map<String, List<OrderItemSatisfyDTO>> mapList = ListUtils.toMapList(orginList, o -> String.join("@",
                o.getZoneName(), o.getInstanceType(), DateUtils.formatDate(o.getBeginBuyDate())), o -> o);

        List<RiskForecastDTO.SatisfyInfo> forecast = new ArrayList<>();
        for (Entry<String, List<OrderItemSatisfyDTO>> entry : mapList.entrySet()) {
            List<OrderItemSatisfyDTO> value = entry.getValue();
            value.sort((o1, o2) -> DateUtils.formatDate(o2.getBeginBuyDate()).compareTo(DateUtils.formatDate(o1.getBeginBuyDate())));
            OrderItemSatisfyDTO orderItemSatisfyDTO = value.get(0);
            SatisfyInfo satisfyInfo = new SatisfyInfo();
            satisfyInfo.setDate(orderItemSatisfyDTO.getBeginBuyDate());
            satisfyInfo.setInstanceType(orderItemSatisfyDTO.getInstanceType());
            satisfyInfo.setZoneName(orderItemSatisfyDTO.getZoneName());
            satisfyInfo.setPreDeductNum(orderItemSatisfyDTO.getPreDeductNum());
            satisfyInfo.setNum(orderItemSatisfyDTO.getNum());
            forecast.add(satisfyInfo);
        }

        // 模拟对冲满足
        String actualSql = ORMUtils.getSql("/sql/order/supply/satisfy_actual_group_data.sql");
        List<RiskForecastDTO.SatisfyInfo> actualList = demandDBHelper.getRaw(RiskForecastDTO.SatisfyInfo.class,
                actualSql, order.getOrderNumber(), detailIds);

        //累计预扣满足
        String deductSql = ORMUtils.getSql("/sql/order/supply/satisfy_deduct_group_data.sql");
        List<RiskForecastDTO.SatisfyInfo> deductList = demandDBHelper.getRaw(RiskForecastDTO.SatisfyInfo.class,
                deductSql, order.getOrderNumber());

        //大盘实际满足：MAX(模拟对冲满足, 累计预扣满足)
        List<RiskForecastDTO.SatisfyInfo> finalActualList = new ArrayList<>();
        Map<String, SatisfyInfo> actualMap = ListUtils.toMap(actualList,
                o -> String.join("@", o.getZoneName(), o.getInstanceType(), DateUtils.formatDate(o.getDate())), o -> o);
        Map<String, SatisfyInfo> deductMap = ListUtils.toMap(deductList,
                o -> String.join("@", o.getZoneName(), o.getInstanceType(), DateUtils.formatDate(o.getDate())), o -> o);
        Set<String> keySet = new HashSet<>();
        if (ListUtils.isNotEmpty(actualMap.keySet())) {
            keySet.addAll(actualMap.keySet());
        }
        if (ListUtils.isNotEmpty(deductMap.keySet())) {
            keySet.addAll(deductMap.keySet());
        }
        for (String key : keySet) {
            SatisfyInfo actual = actualMap.get(key);
            SatisfyInfo deduct = deductMap.get(key);
            if (actual != null || deduct != null) {
                SatisfyInfo satisfyInfo = new SatisfyInfo();
                if (actual == null) {
                    satisfyInfo.setDate(deduct.getDate());
                    satisfyInfo.setNum(deduct.getPreDeductNum());
                    satisfyInfo.setInstanceType(deduct.getInstanceType());
                    satisfyInfo.setZoneName(deduct.getZoneName());
                    satisfyInfo.setPreDeductNum(BigDecimal.ZERO);
                }else if (deduct == null) {
                    satisfyInfo.setDate(actual.getDate());
                    satisfyInfo.setNum(actual.getNum());
                    satisfyInfo.setInstanceType(actual.getInstanceType());
                    satisfyInfo.setZoneName(actual.getZoneName());
                    satisfyInfo.setPreDeductNum(actual.getPreDeductNum());
                }else {
                    BigDecimal big = deduct.getPreDeductNum().compareTo(actual.getNum()) > 0 ?
                            deduct. getPreDeductNum(): actual.getNum();
                    satisfyInfo.setDate(actual.getDate());
                    satisfyInfo.setNum(big);
                    satisfyInfo.setInstanceType(actual.getInstanceType());
                    satisfyInfo.setZoneName(actual.getZoneName());
                    satisfyInfo.setPreDeductNum(BigDecimal.ZERO);
                }
                finalActualList.add(satisfyInfo);
            }
        }


        List<DeliverRecordDTO> result = DeliverRecordDTO.fromSupplyDetails(plan.getDetails(),
                null, indexFunc(plan.getProduct()));
        for (DeliverRecordDTO item : result) {
            // 最晚的对冲数据作为预计满足
            if (ListUtils.isNotEmpty(forecast)) {
                for (SatisfyInfo expectSatisfy : forecast) {
                    DeliverItem deliverItem = expectSatisfy.toForNum(item);
                    if (deliverItem != null) {
                        item.getExpectDeliverList().add(deliverItem);
                    }
                }
            }

            if (ListUtils.isNotEmpty(actualList)) {
                for (SatisfyInfo satisfyInfo : actualList) {
                    if (satisfyInfo.getDate() == null) {
                        // 供应方案命中没有共识开始购买日期时，使用订单的开始购买日期
                        satisfyInfo.setDate(order.getBeginBuyDate());
                    }
                    // 模拟对冲满足
                    DeliverItem simulateItem = satisfyInfo.toForSimulateNum(item);
                    if (simulateItem != null) {
                        item.getSimulateList().add(simulateItem);
                    }
                }
            }

            //累计预扣满足
            if (ListUtils.isNotEmpty(deductList)) {
                for (SatisfyInfo satisfyInfo : deductList) {
                    DeliverItem preDeductItem = satisfyInfo.toForPreDeductNum(item);
                    if (preDeductItem != null) {
                        item.getPreDeductList().add(preDeductItem);
                    }
                }
            }

            //大盘实际满足
            if (ListUtils.isNotEmpty(finalActualList)) {
                for (SatisfyInfo satisfyInfo : finalActualList) {
                    DeliverItem actualItem = satisfyInfo.toForNum(item);
                    if (actualItem != null) {
                        item.getActualDeliverList().add(actualItem);
                    }

                }
            }

        }
        return result;
    }

    @Data
    public static class OrderItemSatisfyDTO {

        @Column("instance_type")
        private String instanceType;

        @Column("zone_name")
        private String zoneName;

        @Column("begin_buy_date")
        private LocalDate beginBuyDate;

        @Column("calc_date")
        private LocalDate calcDate;

        @Column("num")
        private BigDecimal num;

        @Column("pre_deduct_num")
        private BigDecimal preDeductNum;
    }

    private Function<OrderSupplyPlanDetailDO, Integer> indexFunc(String product) {
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(product);
        // GPU使用供应卡数指标，其他使用供应核心数指标
        return isGpu ? OrderSupplyPlanDetailDO::getSupplyGpuNum : OrderSupplyPlanDetailDO::getSupplyCoreNum;
    }

    @Override
    public List<ConsensusDemandDetailDTO> getAvailableConsensusDemandDetail(String orderNumber) {
        OrderDetailResp order = orderCommonService.queryOrderDetail(orderNumber);
        if (order == null) {
            throw BizException.makeThrow("未能获取到生效中的订单信息，订单号【%s】", orderNumber);
        }
        //获取此订单下最新的共识需求
        List<OrderConsensusDemandDetailDO> demandDetails = demandDBHelper.getAll(OrderConsensusDemandDetailDO.class,
                "where order_number = ? and available_status = ?", orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (ListUtils.isEmpty(demandDetails)) {
            return new ArrayList<>();
        }
        // 获取有效预扣信息
        List<PreDeductOrderItemDO> preDeducts = preDeductOrderService.queryPreDeductItems(orderNumber,
                PreDeductOrderStatusEnum.VALID_STATUS);
        //查询此订单下最新生效的供应方案明细
        OrderSupplyPlanVersionDO version = getAvailableVersion(orderNumber);
        //不存在供应方案，从订单明细里获取备选机型等信息
        if (version == null) {
            List<OrderItemDTO> itemList = order.getItemList();
            for (OrderItemDTO item : itemList) {
                List<OtherInstance> otherInstance = item.getOtherInstanceList();
                List<OtherZone> otherZone = item.getOtherZoneList();
                if (ListUtils.isNotEmpty(otherZone)) {
                    otherZone.sort(Comparator.comparing(OtherZone::getName));
                }
                if (ListUtils.isNotEmpty(otherInstance)) {
                    for (OtherInstance instance : otherInstance) {
                        List<String> otherInstanceModel = instance.getOtherInstanceModel();
                        if (ListUtils.isNotEmpty(otherInstanceModel)) {
                            otherInstanceModel.sort(String::compareTo);
                        }
                    }
                    otherInstance.sort((o1, o2) -> {
                        if (o1.getOtherInstanceType().equals(o2.getOtherInstanceType())) {
                            return o1.getOtherInstanceType().compareTo(o2.getOtherInstanceType());
                        }
                        String model1 = "";
                        String model2 = "";
                        if (ListUtils.isNotEmpty(o1.getOtherInstanceModel())) {
                            model1 = String.join("@", o1.getOtherInstanceModel());
                        }
                        if (ListUtils.isNotEmpty(o2.getOtherInstanceModel())) {
                            model2 = String.join("@", o2.getOtherInstanceModel());
                        }
                        return model1.compareTo(model2);
                    });
                }
            }
            List<ConsensusDemandDetailDTO> result = new ArrayList<>();
            for (OrderConsensusDemandDetailDO demandDetail : demandDetails) {
                ConsensusDemandDetailDTO item = new ConsensusDemandDetailDTO();
                BeanUtils.copyProperties(demandDetail, item);
                List<OrderItemDTO> collect = itemList.stream()
                        .filter(o -> o.getInstanceType().equals(demandDetail.getDemandInstanceType())).collect(
                                Collectors.toList());
                Map<String, List<OrderItemDTO>> detailMap = ListUtils.toMapList(collect,
                        o -> String.join("#", o.getInstanceModel(), genOtherZoneString(o.getOtherZoneList()),
                                genOtherInstanceString(o.getOtherInstanceList())), o -> o);
                List<InstanceModelAndOtherInfo> infoList = new ArrayList<>();
                for (Entry<String, List<OrderItemDTO>> entry : detailMap.entrySet()) {
                    InstanceModelAndOtherInfo info = new InstanceModelAndOtherInfo();
                    info.setInstanceModel(entry.getValue().get(0).getInstanceModel());
                    info.setOtherZone(entry.getValue().get(0).getOtherZoneList());
                    info.setOtherInstance(entry.getValue().get(0).getOtherInstanceList());
                    info.setDemandNum(NumberUtils.sum(entry.getValue(), OrderItemDO::getInstanceNum).intValue());
                    infoList.add(info);
                }
                item.setInfo(infoList);
                result.add(item);
            }
            // 匹配分配预扣信息
            ConsensusDemandDetailDTO.addPreDeduct(result, preDeducts);
            return result;
        }
        List<OrderSupplyPlanWithDetailDTO> details = getAllSupplyPlanWithDetails(version.getId(), orderNumber);
        List<OrderSupplyPlanDetailDO> detailList = new ArrayList<>();
        for (OrderSupplyPlanWithDetailDTO detail : details) {
            if (ListUtils.isNotEmpty(detail.getDetails())) {
                detailList.addAll(detail.getDetails());
            }
        }
        //将供应方案中的规格与备选可用区信息与共识需求合并
        for (OrderSupplyPlanDetailDO item : detailList) {
            List<OtherZone> otherZone = item.getOtherZone();
            List<OtherInstance> otherInstance = item.getOtherInstance();
            if (ListUtils.isNotEmpty(otherZone)) {
                otherZone.sort(Comparator.comparing(OtherZone::getName));
            }
            if (ListUtils.isNotEmpty(otherInstance)) {
                for (OtherInstance instance : otherInstance) {
                    List<String> otherInstanceModel = instance.getOtherInstanceModel();
                    if (ListUtils.isNotEmpty(otherInstanceModel)) {
                        otherInstanceModel.sort(String::compareTo);
                    }
                }
                otherInstance.sort((o1, o2) -> {
                    if (o1.getOtherInstanceType().equals(o2.getOtherInstanceType())) {
                        return o1.getOtherInstanceType().compareTo(o2.getOtherInstanceType());
                    }
                    String model1 = "";
                    String model2 = "";
                    if (ListUtils.isNotEmpty(o1.getOtherInstanceModel())) {
                        model1 = String.join("@", o1.getOtherInstanceModel());
                    }
                    if (ListUtils.isNotEmpty(o2.getOtherInstanceModel())) {
                        model2 = String.join("@", o2.getOtherInstanceModel());
                    }
                    return model1.compareTo(model2);
                });
            }
        }
        List<ConsensusDemandDetailDTO> result = new ArrayList<>();
        for (OrderConsensusDemandDetailDO demandDetail : demandDetails) {
            ConsensusDemandDetailDTO item = new ConsensusDemandDetailDTO();
            BeanUtils.copyProperties(demandDetail, item);
            List<OrderSupplyPlanDetailDO> collect = detailList.stream()
                    .filter(o -> o.getSupplyInstanceType().equals(demandDetail.getDemandInstanceType())).collect(
                            Collectors.toList());
            Map<String, List<OrderSupplyPlanDetailDO>> detailMap = ListUtils.toMapList(collect,
                    o -> String.join("#", o.getSupplyInstanceModel(), genOtherZoneString(o.getOtherZone()),
                            genOtherInstanceString(o.getOtherInstance())), o -> o);
            List<InstanceModelAndOtherInfo> infoList = new ArrayList<>();
            for (Entry<String, List<OrderSupplyPlanDetailDO>> entry : detailMap.entrySet()) {
                InstanceModelAndOtherInfo info = new InstanceModelAndOtherInfo();
                info.setInstanceModel(entry.getValue().get(0).getSupplyInstanceModel());
                info.setOtherZone(entry.getValue().get(0).getOtherZone());
                info.setOtherInstance(entry.getValue().get(0).getOtherInstance());
                info.setDemandNum(NumberUtils.sum(entry.getValue(), OrderSupplyPlanDetailDO::getSupplyInstanceNum).intValue());
                infoList.add(info);
            }
            item.setInfo(infoList);
            result.add(item);
        }
        // 匹配分配预扣信息
        ConsensusDemandDetailDTO.addPreDeduct(result, preDeducts);
        return result;
    }

    public String genOtherInstanceString(List<OtherInstance> instance) {
        if (ListUtils.isEmpty(instance)) {
            return "";
        }
        List<String> collect = instance.stream()
                .map(o -> o.getOtherInstanceType() + ":" + String.join("@", o.getOtherInstanceModel())).collect(
                        Collectors.toList());
        return String.join(";", collect);
    }

    public String genOtherZoneString(List<OtherZone> zone) {
        if (ListUtils.isEmpty(zone)) {
            return "";
        }
        return zone.stream().map(OtherZone::getName).collect(Collectors.joining("@"));

    }
}
