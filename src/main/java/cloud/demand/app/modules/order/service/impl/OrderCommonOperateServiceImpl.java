package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.flow.constant.FlowNodeDefaultReturnValueEnum;
import cloud.demand.app.modules.flow.dto.FlowNodeRecordWithFlowInfoDTO;
import cloud.demand.app.modules.flow.entity.FlowInfoDO;
import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;
import cloud.demand.app.modules.flow.service.FlowService;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.dto.req.OrderChangeLabelReq;
import cloud.demand.app.modules.order.dto.req.OrderChangeLabelReq.OneChangeReq;
import cloud.demand.app.modules.order.dto.req.OrderFlowNodeValueSetReq;
import cloud.demand.app.modules.order.dto.req.PplOrder2XyPurchaseOrderReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanWithDetailDTO;
import cloud.demand.app.modules.order.dto.resp.SubProcessResp;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanVersionDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderFlowEnum;
import cloud.demand.app.modules.order.enums.OrderLabelEnum;
import cloud.demand.app.modules.order.enums.OrderLabelEnum.OrderLabelItem;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderReportBiteEnum;
import cloud.demand.app.modules.order.enums.OrderRiskLevelEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import cloud.demand.app.modules.order.enums.SupplyPlanVersionStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonOperateService;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.order.service.OrderFlowService;
import cloud.demand.app.modules.order.service.OrderOperateService;
import cloud.demand.app.modules.order.service.OrderSatisfyRateService;
import cloud.demand.app.modules.order.service.PreDeductOrderService;
import cloud.demand.app.modules.order.service.SupplyPlanOperateService;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.order.service.function.impl.DefaultOrderCreator;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Service
public class OrderCommonOperateServiceImpl implements OrderCommonOperateService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private OrderFlowService orderFlowService;

    @Resource
    private FlowService flowService;

    @Resource
    private OrderCommonOperateService orderCommonOperateService;

    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private DefaultOrderCreator defaultOrderCreator;

    @Resource
    private PermissionService permissionService;

    @Resource
    private PreDeductOrderService preDeductOrderService;

    @Resource
    private SupplyPlanOperateService supplyPlanOperateService;

    @Resource
    private OrderSatisfyRateService orderSatisfyRateService;

    @Override
    @Transactional("demandTransactionManager")
    public void modifyOrderSubmitAndArchitect(List<String> orderNumbers, String submit,
            String architect,Set<String> addOrderFollower) {
        String user = LoginUtils.getUserNameWithSystem();
        WhereSQL condition = new WhereSQL();
        condition.and("order_number in (?)", orderNumbers);
//        condition.and("order_status = ?", OrderStatusEnum.PROCESS.getCode());
        condition.and("available_status = ?", OrderAvailableStatusEnum.AVAILABLE.getCode());
        List<OrderDetailResp> all = demandDBHelper.getAll(OrderDetailResp.class, condition.getSQL(),
                condition.getParams());
        if (ListUtils.isEmpty(all) || all.size() < orderNumbers.size()) {
            throw BizException.makeThrow("订单列表参数中存在状态不处于进行中的订单");
        }
        List<String> finalOrder = all.stream().map(OrderInfoDO::getOrderNumber).collect(Collectors.toList());
        List<String> industryDepts = all.stream().map(OrderInfoDO::getIndustryDept).collect(Collectors.toList());
        //只有admin和当前行业的行业接口人有权限修改订单提交人和负责人
        if (!permissionService.checkIsAdmin(user) &&
                !permissionService.checkIsContainSpecificIndustry(user, industryDepts)) {
            throw BizException.makeThrow("当前用户非管理员或者当前订单列表存在非此行业接口人包含的行业, 用户：【%s】", user);
        }
        OrderCommonOperateServiceImpl service = SpringUtil.getBean(OrderCommonOperateServiceImpl.class);
        for (String orderNumber : finalOrder) {
            service.modifyOrderSubmitAndArchitectSingle(orderNumber, submit, architect,addOrderFollower);
        }
    }

    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void modifyOrderSubmitAndArchitectSingle(String orderNumber, String submit,
            String architect,Set<String> addOrderFollower) {
        String user = LoginUtils.getUserNameWithSystem();
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        order.selfHandler();

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNodeWithNull(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        String beforeSubmit = order.getSubmitUser();
        String beforeArch = order.getArchitect();
        String beforeFollower = order.getOrderFollower();
        List<String> beforeFollowerList = order.getOrderFollowerList();

        String afterSubmit;
        String afterArch;
        String afterOrderFollower;
        if (!StringTools.isBlank(submit)) {
            afterSubmit = submit;
        }else {
            afterSubmit = beforeSubmit;
        }

        if (!StringTools.isBlank(architect)) {
            afterArch = architect;
        }else {
            afterArch = beforeArch;
        }

        if (ListUtils.isNotEmpty(addOrderFollower)){
            addOrderFollower.addAll(beforeFollowerList);
            afterOrderFollower = Strings.join(";",addOrderFollower);
        }else {
            afterOrderFollower = beforeFollower;
        }

        String operateName = StrUtil.format("【{}】修改了订单【{}】的提单人与负责人， 提单人从【{}】变更为【{}】, 负责人从【{}】变更为【{}】,"
                + "订单关注人从【{}】变更为【{}】", user, orderNumber, beforeSubmit, afterSubmit, beforeArch, afterArch,
                beforeFollower != null ? beforeFollower : "",afterOrderFollower);
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("修改提单人和负责人");
        valueSetReq.setOperateName(operateName);
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData == null ? null : nodeData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setSystem(true);
        valueSetReq.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
            newOrder.setSubmitUser(afterSubmit);
            newOrder.setArchitect(afterArch);
            newOrder.setOrderFollower(afterOrderFollower);
            return newOrder;
        });
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
        FlowNodeRecordWithFlowInfoDTO flowInfo = flowService.queryProcessingSubFlowNode(
                orderNumber, OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowInfo != null) {
            //存在子流程，需将子流程的订单信息同步修改
            OrderInfoDO subOrder = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            OrderFlowNodeValueSetReq sub = new OrderFlowNodeValueSetReq();
            sub.setOrder(subOrder);
            sub.setOperateEvent("修改提单人和负责人");
            sub.setOperateName(operateName);
            sub.setFlowCode(flowInfo.getFlowInfo().getFlowCode());
            sub.setNodeCode(flowInfo.getNodeCode());
            sub.setBizId(orderNumber);
            sub.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            sub.setOperateUser(user);
            sub.setSystem(true);
            sub.setNotShowLogInMainFlow(true);
            sub.setOrderCreator((orderNormal) -> {
                OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
                newOrder.setSubmitUser(afterSubmit);
                newOrder.setArchitect(afterArch);
                newOrder.setOrderFollower(afterOrderFollower);
                return newOrder;
            });
            orderFlowService.nodeRecordSetReturnValueAndPushFlow(sub);
        }
    }

    @Override
    @Transactional(transactionManager = "demandTransactionManager")
    public void modifyOrderLabel(OrderChangeLabelReq req) {
        // 请求校验
        OrderLabelEnum.checkAndFill(req);
        List<String> orderNumbers = req.getOrderNumbers();
        String user = LoginUtils.getUserNameWithSystem();
        WhereSQL condition = new WhereSQL();
        condition.and("order_number in (?)", orderNumbers);
//        condition.and("order_status = ?", OrderStatusEnum.PROCESS.getCode());
        condition.and("available_status = ?", OrderAvailableStatusEnum.AVAILABLE.getCode());
        List<OrderDetailResp> all = demandDBHelper.getAll(OrderDetailResp.class, condition.getSQL(),
                condition.getParams());
        if (ListUtils.isEmpty(all) || all.size() < orderNumbers.size()) {
            throw BizException.makeThrow("订单列表参数中存在状态不处于进行中的订单");
        }
        List<String> finalOrder = all.stream().map(OrderInfoDO::getOrderNumber).collect(Collectors.toList());
        List<String> industryDepts = all.stream().map(OrderInfoDO::getIndustryDept).collect(Collectors.toList());
        //只有admin和当前行业的行业接口人有权限修改订单提交人和负责人
        if (!permissionService.checkIsAdmin(user) &&
                !permissionService.checkIsContainSpecificIndustry(user, industryDepts)) {
            throw BizException.makeThrow("当前用户非管理员或者当前订单列表存在非此行业接口人包含的行业, 用户：【%s】", user);
        }
        OrderCommonOperateServiceImpl bean = SpringUtil.getBean(OrderCommonOperateServiceImpl.class);
        for (String orderNumber : finalOrder) {
            bean.modifyOneOrderLabel(req.toOne(orderNumber));
        }
    }

    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0].orderNumber",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void modifyOneOrderLabel(OneChangeReq req){
        String orderNumber = req.getOrderNumber();
        String label = req.getOrderLabel();
        OrderLabelItem orderLabelItem = req.getOrderLabelItem();
        String reason = BooleanUtils.isTrue(orderLabelItem.getNeedReason()) ? req.getLabelReason() : null;
        String remark = BooleanUtils.isTrue(orderLabelItem.getNeedReason()) ? req.getLabelRemark() : null;
        List<String> reportNameList = req.getReportNameList();
        Integer reportBitNum = OrderReportBiteEnum.computeBitNum(reportNameList, label);

        String user = LoginUtils.getUserNameWithSystem();
        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());

        LocalDateTime now = LocalDateTime.now();

        List<String> oldReportNameList = order.getReportNameList();

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNodeWithNull(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());

        String operateName = StrUtil.format("【{}】修改了订单【{}】的订单标签， 订单标签从【{}】变更为【{}】，标签原因从【{}】变更为【{}】，统计报表从【{}】变更为【{}】",
                user, orderNumber,
                ObjectUtils.defaultIfNull(order.getOrderLabel(), OrderLabelEnum.NO_TAG.getLabel()), label,
                ObjectUtils.defaultIfNull(order.getLabelReason(), ""), ObjectUtils.defaultIfNull(reason, ""),
                oldReportNameList == null ? "":String.join(",",oldReportNameList), reportNameList == null ? "":String.join(",", reportNameList));
        String operateRemark = StrUtil.format("标签备注从【{}】变更为【{}】",
                ObjectUtils.defaultIfNull(order.getLabelRemark(), ""), ObjectUtils.defaultIfNull(remark,""));
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("修改订单标签");
        valueSetReq.setOperateName(operateName);
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData == null ? null : nodeData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setSystem(true);
        valueSetReq.setOperateRemark(operateRemark);
        valueSetReq.setOrderCreator((orderNormal) -> {
            OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
            newOrder.setOrderLabel(label);
            newOrder.setLabelReason(reason);
            newOrder.setLabelRemark(remark);
            newOrder.setReportBitNum(reportBitNum);
            newOrder.setLabelUpdateUser(user);
            newOrder.setLabelUpdateTime(now);
            return newOrder;
        });
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);
        FlowNodeRecordWithFlowInfoDTO flowInfo = flowService.queryProcessingSubFlowNode(
                orderNumber, OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowInfo != null) {
            //存在子流程，需将子流程的订单信息同步修改
            OrderInfoDO subOrder = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            OrderFlowNodeValueSetReq sub = new OrderFlowNodeValueSetReq();
            sub.setOrder(subOrder);
            sub.setOperateEvent("修改订单标签");
            sub.setOperateName(operateName);
            sub.setFlowCode(flowInfo.getFlowInfo().getFlowCode());
            sub.setNodeCode(flowInfo.getNodeCode());
            sub.setBizId(orderNumber);
            sub.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            sub.setOperateUser(user);
            sub.setSystem(true);
            sub.setNotShowLogInMainFlow(true);
            sub.setOperateRemark(operateRemark);
            sub.setOrderCreator((orderNormal) -> {
                OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
                newOrder.setOrderLabel(label);
                newOrder.setLabelReason(reason);
                newOrder.setLabelRemark(remark);
                newOrder.setReportBitNum(reportBitNum);
                newOrder.setLabelUpdateUser(user);
                newOrder.setLabelUpdateTime(now);
                return newOrder;
            });
            orderFlowService.nodeRecordSetReturnValueAndPushFlow(sub);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void subscribe(String orderNumber) {
        String user = LoginUtils.getUserNameWithSystem();
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            throw BizException.makeThrow("订单不存在或未提交，订单号【%s】", orderNumber);
        }
        // 订阅订单，需要有订单查询权限
        orderCommonService.checkOrderQueryAuth(order);
        List<String> followerList = order.orderFollowerListGet();
        if (followerList.contains(user)) {
            return;
        }
        // 添加为订单关注人
        followerList.add(user);

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null) {
            throw BizException.makeThrow("订单【%s】不在进行中，无法订阅", orderNumber);
        }
        String operateName = StrUtil.format("【{}】订阅了订单【{}】", user, orderNumber);
        // 修改订单关注人
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("订阅订单");
        valueSetReq.setOperateName(operateName);
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        // 修改订单，设置返回值 999 表示在需求提交节点自旋一次
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setSystem(true); // 跳过可处理人判断
        valueSetReq.setOrderCreator((orderNormal) -> {
            // 订单关注人修改
            OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
            newOrder.orderFollowerListSet(followerList);
            return newOrder;
        });
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        FlowNodeRecordWithFlowInfoDTO flowInfo = flowService.queryProcessingSubFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowInfo != null) {
            // 如果存在进行中的子流程，需要将子流程中生效的订单信息的订单关注人进行同步更新
            OrderInfoDO subOrder = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            OrderFlowNodeValueSetReq sub = new OrderFlowNodeValueSetReq();
            sub.setOrder(subOrder);
            sub.setOperateEvent("订阅订单");
            sub.setFlowCode(flowInfo.getFlowInfo().getFlowCode());
            sub.setNodeCode(flowInfo.getNodeCode());
            sub.setBizId(orderNumber);
            sub.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            sub.setOperateUser(user);
            sub.setOperateName(operateName);
            sub.setSystem(true); // 跳过可处理人判断
            sub.setNotShowLogInMainFlow(true); // 这里子流程的修改不显示日志
            sub.setOrderCreator((orderNormal) -> {
                // 订单关注人修改
                OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
                newOrder.orderFollowerListSet(followerList);
                return newOrder;
            });
            orderFlowService.nodeRecordSetReturnValueAndPushFlow(sub);
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void cancelSubscribe(String orderNumber) {
        String user = LoginUtils.getUserNameWithSystem();
        OrderDetailResp order = orderCommonService.getOrderDetail(orderNumber,
                OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            throw BizException.makeThrow("订单不存在或未提交，订单号【%s】", orderNumber);
        }
        List<String> followerList = order.orderFollowerListGet();
        if (!followerList.contains(user)) {
            return;
        }
        // 从订单关注人中移除
        followerList.remove(user);

        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (nodeData == null) {
            throw BizException.makeThrow("订单【%s】不在进行中，无法取消订阅", orderNumber);
        }
        String operateName = StrUtil.format("【{}】取消订阅，订单号【{}】", user, orderNumber);
        // 修改订单关注人
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("取消订阅");
        valueSetReq.setOperateName(operateName);
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        // 修改订单，设置返回值 999 表示在需求提交节点自旋一次
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(true);
        valueSetReq.setSystem(true); // 跳过可处理人判断
        valueSetReq.setOrderCreator((orderNormal) -> {
            // 订单关注人修改
            OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
            newOrder.orderFollowerListSet(followerList);
            return newOrder;
        });
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        FlowNodeRecordWithFlowInfoDTO flowInfo = flowService.queryProcessingSubFlowNode(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        if (flowInfo != null) {
            // 如果存在进行中的子流程，需要将子流程中生效的订单信息的订单关注人进行同步更新
            OrderInfoDO subOrder = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
            OrderFlowNodeValueSetReq sub = new OrderFlowNodeValueSetReq();
            sub.setOrder(subOrder);
            sub.setOperateEvent("取消订阅");
            sub.setFlowCode(flowInfo.getFlowInfo().getFlowCode());
            sub.setNodeCode(flowInfo.getNodeCode());
            sub.setBizId(orderNumber);
            sub.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
            sub.setOperateUser(user);
            sub.setOperateName(operateName);
            sub.setSystem(true); // 跳过可处理人判断
            sub.setNotShowLogInMainFlow(true); // 这里子流程的修改不显示日志
            sub.setOrderCreator((orderNormal) -> {
                // 订单关注人修改
                OrderInfoDO newOrder = defaultOrderCreator.create(orderNormal);
                newOrder.orderFollowerListSet(followerList);
                return newOrder;
            });
            orderFlowService.nodeRecordSetReturnValueAndPushFlow(sub);
        }
    }

    @Override
    public void syncOrderItemsLateConsensusBeginBuyDate() {
        List<String> orders = orderCommonService.getAllAvailableProcessOrderNumbers();
        for (String orderNumber : orders) {
            try {
                OrderOperateService orderOperateService = SpringUtil.getBean(OrderOperateService.class);
                orderOperateService.syncOrderItemsLateConsensusBeginBuyDate(orderNumber);
            } catch (Exception e) {
                // non
            }
        }
    }

    @Override
    public void updateOrderRiskLevel(String orderNumber, OrderRiskLevelEnum riskLevel) {
        String sql = "update order_info set risk_level =  ? "
                + " where order_number = ? "
                + " and available_status != 'not_available' and deleted = 0 ";
        demandDBHelper.executeRaw(sql, riskLevel == null ? null : riskLevel.getRiskName(), orderNumber);
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 3000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void updateOrderRiskLevelBySupplyPlan(String orderNumber) {
        try {
            OrderSupplyPlanVersionDO version = supplyPlanQueryService.getAvailableVersion(orderNumber);
            if (version == null) {
                // 无货期的提示评估中
                updateOrderRiskLevel(orderNumber, OrderRiskLevelEnum.assessing);
                return;
            }
            List<OrderSupplyPlanWithDetailDTO> plans = supplyPlanQueryService.getAllSupplyPlanWithDetails(
                    version.getId(), orderNumber);
            if (plans.isEmpty()) {
                // 无货期的提示评估中
                updateOrderRiskLevel(orderNumber, OrderRiskLevelEnum.assessing);
                return;
            }

            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE.getCode());
            OrderRiskLevelEnum res = null;
            LocalDate now = LocalDate.now();
            for (OrderSupplyPlanWithDetailDTO plan : plans) {
                if (ListUtils.isEmpty(plan.getDetails())) {
                    continue;
                }
                if (Objects.equals(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode(), plan.getMatchType())) {
                    if (res == null || res.getScore() <= OrderRiskLevelEnum.satisfy.getScore()) {
                        // 如果是完全大盘满足的， 目前阶段提示大盘满足
                        res = OrderRiskLevelEnum.satisfy;
                        continue;
                    }
                }
                for (OrderSupplyPlanDetailDO detail : plan.getDetails()) {
                    OrderRiskLevelEnum current;
                    LocalDate beginBuyDate = detail.getConsensusBeginBuyDate() == null
                            ? order.getBeginBuyDate() : detail.getConsensusBeginBuyDate();
                    if (detail.getSupplyTime() == null) {
                        // 没有预计交付日期，表示无货期，评估中
                        current = OrderRiskLevelEnum.assessing;
                    } else if (beginBuyDate.isAfter(detail.getSupplyTime())) {
                        // 如果预计交付时间早于开始购买时间（共识后开始购买时间），系统要提示无风险。
                        current = OrderRiskLevelEnum.no_risk;
                    } else {
                        // 如果预计交付时间晚于开始购买时间（共识后开始购买时间），系统要提示高风险。
                        current = OrderRiskLevelEnum.high_risk;
                    }
                    if (detail.getActualSupplyDate() != null && !detail.getActualSupplyDate().isAfter(now)) {
                        // 如果实际交付日期不为空，且在今天之前，则当前方案明细已交付
                        current = OrderRiskLevelEnum.delivered;
                    }
                    if (res == null || res.getScore() < current.getScore()) {
                        // 取风险度最高的风险级别
                        res = current;
                    }
                }
            }
            updateOrderRiskLevel(orderNumber, res);
        } catch (Exception e) {
            // 告警通知
            AlarmRobotUtil.doAlarm("updateOrderRiskLevelBySupplyPlan",
                    ExceptionUtil.getMessage(e), null, false);
            throw e;
        }
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void stopProcessingSubFlow(String orderNumber, String operateEvent, boolean needCheckAuth) {
        // 获取进行中的子流程
        SubProcessResp subProcessResp = orderCommonService.queryProcessingSubProcess(orderNumber);
        if (!subProcessResp.getIsExist()) {
            return;
        }
        if (needCheckAuth) {
            // 仅当提单人/订单负责人以及对应行业接口人可以操作
            OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber,
                    OrderAvailableStatusEnum.AVAILABLE.getCode());
            if (order == null) {
                throw BizException.makeThrow("操作失败，未能查询到生效的订单信息，订单号：【%s】", orderNumber);
            }
            String user = LoginUtils.getUserNameWithSystem();
            if (!Objects.equals(user, order.getSubmitUser()) && !Objects.equals(user, order.getArchitect()) && !permissionService.checkIsIndustryOperate(user, order.getIndustryDept())) {
                throw BizException.makeThrow("操作失败，仅提单人/订单负责人【%s】以及对应行业接口人可以停止子流程，当前操作人【%s】，订单号：【%s】",
                        order.getSubmitUser(), user, orderNumber);
            }
        }
        FlowInfoDO processingSubFlow = subProcessResp.getFlowInfoDO();
        if (OrderFlowEnum.ORDER_SUPPLY_PLAN_FORMULATE.getCode().equals(processingSubFlow.getFlowCode())) {
            // 强制停止供应方案制定子流程
            supplyPlanOperateService.stopProcessingSupplyPlanFlow(processingSubFlow, orderNumber, operateEvent);
        } else if (OrderFlowEnum.ORDER_WITHHOLDING.getCode().equals(processingSubFlow.getFlowCode())
                || OrderFlowEnum.EKS_ORDER_WITHHOLDING.getCode().equals(processingSubFlow.getFlowCode())
                || OrderFlowEnum.EMR_ORDER_WITHHOLDING.getCode().equals(processingSubFlow.getFlowCode())) {
            // 强制停止预扣子流程
            preDeductOrderService.stopProcessingPreDeductOrderFlow(processingSubFlow, orderNumber, operateEvent);
        } else if (OrderFlowEnum.ORDER_SUPPLY_PLAN_MODIFY.getCode().equals(processingSubFlow.getFlowCode())) {
            // 强制停止供应方案修改子流程
            supplyPlanOperateService.stopProcessingSupplyPlanModifyFlow(processingSubFlow, orderNumber, operateEvent);
        } else {
            // 只需要对订单、订单明细表做操作时，使用通用的强制停止
            orderFlowService.stopProcessingSubFlow(orderNumber, operateEvent);
        }
    }


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void deleteDraft(String orderNumber) {
        // 查询数据库中此订单号是否存在，不存在则无法操作
        WhereContent where = new WhereContent()
                .andEqual(OrderInfoDO::getOrderNumber, orderNumber)
                .andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE.getCode());
        OrderInfoDO normalOrder = demandDBHelper.getOne(OrderInfoDO.class, where.getSql(), where.getParams());
        if (normalOrder == null) {
            throw BizException.makeThrow("操作失败，未能查询到生效中的订单数据，无需删除，订单号：【%s】", orderNumber);
        }
        if (!OrderStatusEnum.DRAFT.getCode().equals(normalOrder.getOrderStatus())) {
            throw BizException.makeThrow("操作失败，当前订单数据非草稿状态，无法删除，订单号：【%s】", orderNumber);
        }
        stopProcessingSubFlow(orderNumber, "删除草稿，自动终止所有流程", true);
        orderFlowService.forceStopMainFlow(orderNumber, "删除草稿，自动终止所有流程");
        // 删除生效中的订单
        demandDBHelper.delete(OrderInfoDO.class, where.getSql(), where.getParams());
    }

    @Override
    public void resetOrderItemsLateConsensusBeginBuyDate(String orderNumber) {
        String sql = " update order_item a "
                + " left join order_info b on a.order_info_id = b.id and a.order_number = b.order_number "
                + " set a.late_consensus_begin_buy_date = b.begin_buy_date "
                + " where a.order_number = ? and b.available_status != 'not_available' "
                + " and a.deleted = 0 and b.deleted = 0 ";
        demandDBHelper.executeRaw(sql, orderNumber);
    }

    @Override
    @Transactional(transactionManager = "demandTransactionManager")
    public void bindXyPurchaseForPplOrder(PplOrder2XyPurchaseOrderReq req) {
        // 采购单建了还没提交事务落库，校验不到
//        DeviceApplyDO deviceApplyDO = purchaseOrderService.getDeviceApplyByOrderId(req.getXyPurchaseOrderId());
//        if (deviceApplyDO == null) {
//            throw new BizException(String.format("采购单%s不存在", req.getXyPurchaseOrderId()));
//        }
        List<OrderSupplyPlanDetailDO> orderSupplyPlanDetailDOS = demandDBHelper.getAll(OrderSupplyPlanDetailDO.class,
                "where id in (?)",
                req.getPplOrderSupplyPlanDetailId());
        if (orderSupplyPlanDetailDOS == null || orderSupplyPlanDetailDOS.size() != req.getPplOrderSupplyPlanDetailId().size()) {
            throw new BizException("所选供应方案明细已经发生变更，请重新绑定");
        }
        Set<Long> planIds = orderSupplyPlanDetailDOS.stream().map(OrderSupplyPlanDetailDO::getPlanId).collect(Collectors.toSet());
        List<OrderSupplyPlanDO> orderSupplyPlanDOS = demandDBHelper.getAll(OrderSupplyPlanDO.class, "where id in (?)", planIds);
        if (orderSupplyPlanDOS == null || orderSupplyPlanDOS.size() != planIds.size()) {
            throw new BizException("所选供应方案已经发生变更，请重新绑定");
        }
        Set<Long> versionId = new HashSet<>();
        for (OrderSupplyPlanDO item : orderSupplyPlanDOS) {
            if (!OrderSupplyPlanMatchTypeEnum.BUY.getName().equals(item.getMatchTypeName())) {
                throw new BizException("所选供应明细不是【采购满足】，请重新绑定");
            }
            versionId.add(item.getVersionId());
        }
        if (versionId.size() != 1) {
            throw new BizException("所选供应方案版本已经发生变更，请重新绑定");
        }
        OrderSupplyPlanVersionDO versionDO = demandDBHelper.getOne(OrderSupplyPlanVersionDO.class, "where id = ?", versionId.iterator().next());
        if (versionDO == null || !SupplyPlanVersionStatusEnum.available.getCode().equals(versionDO.getStatus())) {
            throw new BizException("所选供应方案版本已经发生变更，请重新绑定");
        }
        OrderInfoDO orderInfoDO = demandDBHelper.getOne(OrderInfoDO.class, "where order_number = ? and available_status = ? and order_status = ?"
                , versionDO.getOrderNumber(), OrderAvailableStatusEnum.AVAILABLE.getCode(), OrderStatusEnum.PROCESS.getCode());
        if (orderInfoDO == null ||  !OrderNodeCodeEnum.canBindXYPurchaseOrderCode.contains(orderInfoDO.getOrderNodeCode())) {
            throw new BizException("所选订单已经发生变更，请重新绑定");
        }
        // 终于可以绑定了

        // 但是我们还需对业务信息校验一下
        Set<String> distinctZone = new HashSet<>();
        Set<String> distinctInstanceCount = new HashSet<>();
        for (OrderSupplyPlanDetailDO item : orderSupplyPlanDetailDOS) {
            distinctZone.add(item.getSupplyZoneName());
            distinctInstanceCount.add(item.getSupplyInstanceType());
        }
        if (distinctZone.size() > 1) {
            throw new BizException("一个采购主单仅支持绑定同一个订单下的同一个供应园区的明细");
        }
        if (distinctInstanceCount.size() > 2) {
            throw new BizException("一个采购主单仅支持绑定同一个订单下的最多2个不同供应实例类型的明细");
        }

        // 之前绑定的取消
        List<OrderSupplyPlanDetailDO> supplyPlanDetailDOS = demandDBHelper.getAll(OrderSupplyPlanDetailDO.class,
                "where supply_biz_id like ?", "%" + req.getXyPurchaseOrderId() + "%");
        supplyPlanDetailDOS.forEach(item -> {
            String[] bizIds = item.getSupplyBizId().split(",");
            List<String> collect = Arrays.stream(bizIds)
                    .filter(bizId -> !req.getXyPurchaseOrderId().equals(bizId.trim()))
                    .collect(Collectors.toList());
            item.setSupplyBizId(String.join(",", collect));
        });

        if (req.getXyPurchaseOrderId().startsWith("Q")) {
            // Q单，一个明细可以绑定多个Q单，把这个Q单追加上去
            orderSupplyPlanDetailDOS.forEach(item -> {
                if (StringUtils.isBlank(item.getSupplyBizId())) {
                    item.setSupplyBizId(req.getXyPurchaseOrderId());
                } else {
                    item.setSupplyBizId(item.getSupplyBizId() + ";" + req.getXyPurchaseOrderId());
                }
            });
            demandDBHelper.update(orderSupplyPlanDetailDOS);
        } else {
            // 主单，一个明细只能绑定这一个主单
            demandDBHelper.executeRaw("update order_supply_plan_detail set supply_biz_type=0,supply_biz_id=? where id in (?)",
                    req.getXyPurchaseOrderId(), req.getPplOrderSupplyPlanDetailId());
        }
    }



    @Override
    public Map<String, Object> opsConsensusBeginEndBuyDate(String orderNumberSql) {
        List<String> orderNumberList = demandDBHelper.getRaw(String.class, orderNumberSql);
        if (ListUtils.isEmpty(orderNumberList)) {
            return new HashMap<>();
        }
        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failList = new ArrayList<>();
        Map<String, Object> failInfo = new HashMap<>();
        OrderCommonOperateService orderCommonOperateService = SpringUtil.getBean(OrderCommonOperateService.class);
        for (String orderNumber : orderNumberList) {
            try {
                orderCommonOperateService.opsConsensusBeginEndBuyDateOnOrder(orderNumber);
                successList.add(orderNumber);
            } catch (Exception e) {
                failList.add(orderNumber);
                failInfo.put(orderNumber, ExceptionUtil.getMessage(e));
            }
        }
        result.put("successList", successList);
        result.put("failList", failList);
        result.put("failInfo", failInfo);
        return result;
    }

    @Override
    @Transactional(transactionManager = "demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_LOCK_NAMESPACE, keyScript = "args[0]",
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "操作失败，订单正在被其他人处理中，请稍后重试")
    public void opsConsensusBeginEndBuyDateOnOrder(String orderNumber) {
        OrderInfoDO order = orderCommonService.getOrderInfo(orderNumber, OrderAvailableStatusEnum.AVAILABLE.getCode());
        if (order == null) {
            throw BizException.makeThrow("操作失败，未能查询到生效中的订单数据，订单号：【%s】", orderNumber);
        }
        List<OrderSupplyPlanDetailWithPlanDTO> supplyDetails = supplyPlanQueryService.getAvailableDetailWithPlan(orderNumber);
        if (supplyDetails.isEmpty()) {
            throw BizException.makeThrow("操作失败，未能查询到生效中的供应方案明细数据，订单号：【%s】", orderNumber);
        }
        Map<Long, Map<String, LocalDate>> normal = new HashMap<>();
        // 修改供应方案共识开始结束日期
        supplyDetails.forEach(item -> {
            Map<String, LocalDate> map = new HashMap<>();
            map.put("normalBegin", item.getConsensusBeginBuyDate());
            map.put("normalEnd", item.getConsensusEndBuyDate());
            normal.put(item.getId(), map);
            item.setConsensusBeginBuyDate(order.getBeginBuyDate());
            item.setConsensusEndBuyDate(order.getEndBuyDate());
        });
        demandDBHelper.update(supplyDetails);

        // 操作留痕，写订单日志
        FlowNodeRecordDO nodeData = flowService.queryProcessingFlowNodeWithNull(orderNumber,
                OrderFlowEnum.ORDER_MAIN.getCode());
        String user = LoginUtils.getUserNameWithSystem();
        String operateName = StrUtil.format("【{}】根据订单开始结束购买日期修改了【{}】供应方案的共识开始结束购买日期",
                user, orderNumber);
        OrderFlowNodeValueSetReq valueSetReq = new OrderFlowNodeValueSetReq();
        valueSetReq.setOrder(order);
        valueSetReq.setOperateEvent("修改供应方案共识开始结束购买日期");
        valueSetReq.setOperateName(operateName);
        valueSetReq.setOperateRemark(JSON.toJSONString(normal));
        valueSetReq.setFlowCode(OrderFlowEnum.ORDER_MAIN.getCode());
        valueSetReq.setNodeCode(nodeData == null ? null : nodeData.getNodeCode());
        valueSetReq.setBizId(orderNumber);
        valueSetReq.setNodeReturn(FlowNodeDefaultReturnValueEnum.SPIN.getCode());
        valueSetReq.setOperateUser(user);
        valueSetReq.setOrderModify(false);
        valueSetReq.setNotShowLogInMainFlow(true);
        valueSetReq.setSystem(true);
        orderFlowService.nodeRecordSetReturnValueAndPushFlow(valueSetReq);

        // 计算采购、搬迁满足量
        orderSatisfyRateService.refreshSatisfyForNotSatisfyMatchOneOrder(orderNumber);
        // 供应方案明细满足量
        orderSatisfyRateService.calcSupplyPlanDetailMatchCore(orderNumber);

        // 重新生成共识需求
        supplyPlanOperateService.genOrderConsensusBySupplyDetail(orderNumber);
    }

    @Override
    public void synConsensusOrderDate() {
        //获取所有生效的共识需求
        List<OrderConsensusDemandDetailDO> all = demandDBHelper.getAll(OrderConsensusDemandDetailDO.class,
                "where available_status = 'available'");
        List<String> orderIds = all.stream().map(OrderConsensusDemandDetailDO::getOrderNumber)
                .distinct().collect(Collectors.toList());
        //获取对应订单的原始开始与结束购买时间
        String sql = "select a.order_number, a.begin_buy_date as begin_buy_date, a.end_buy_date as end_buy_date from order_info as a\n"
                + "    join (select order_number, max(id) as max_id from order_info where order_node_code = 'node_order_propose' group by order_number)\n"
                + "    as b on a.id = b.max_id and a.order_number = b.order_number where a.order_number in (?)";
        List<OrderDateDTO> orderDates = demandDBHelper.getRaw(OrderDateDTO.class, sql, orderIds);
        Map<String, OrderDateDTO> dateMap = ListUtils.toMap(orderDates, OrderDateDTO::getOrderNumber, o -> o);
        for (OrderConsensusDemandDetailDO item : all) {
            OrderDateDTO orderDateDTO = dateMap.get(item.getOrderNumber());
            if (orderDateDTO != null) {
                item.setOrderBeginBuyDate(orderDateDTO.getBeginBuyDate());
                item.setOrderEndBuyDate(orderDateDTO.getEndBuyDate());
            }
        }
        demandDBHelper.update(all);
    }

    @Data
    public static class OrderDateDTO {

        @Column("order_number")
        private String orderNumber;

        @Column("begin_buy_date")
        private LocalDate beginBuyDate;

        @Column("end_buy_date")
        private LocalDate endBuyDate;
    }
    @Override
    public void refreshOrderMatchWayProcessor() {
        WhereContent orderWhereContent = new WhereContent();
        orderWhereContent.andEqual(OrderInfoDO::getOrderStatus, OrderStatusEnum.PROCESS.getCode());
        orderWhereContent.andEqual(OrderInfoDO::getAvailableStatus, OrderAvailableStatusEnum.AVAILABLE_SUB_PROCESS.getCode());
        orderWhereContent.andEqual(OrderInfoDO::getOrderNodeCode, OrderNodeCodeEnum.node_cvm_create_plan.getCode());
        // 获取订单
        List<OrderDetailResp> orderInfoDOList = demandDBHelper.getAll(OrderDetailResp.class, orderWhereContent.getSql(),
                orderWhereContent.getParams());

        for (OrderDetailResp orderDetailResp : orderInfoDOList) {
            // 保存供应方案后 更新一下供应方案处理人
            orderDetailResp.currentProcessorValueSet(orderCommonService.queryOrderOperateUser(
                    IndustryDemandAuthRoleEnum.PRODUCT_PRINCIPAL.getCode(),orderDetailResp));
            demandDBHelper.update(orderDetailResp);
        }
    }

    @Override
    public void genConsensusDemandForOrderCancel() {
        List<String> cancelOrder = demandDBHelper.getRaw(String.class,
                "select distinct order_number from order_info where deleted = 0 and available_status = 'available' and order_status = 'CANCELED'");
        List<StaticZoneDO> zoneAll = cdCommonDbHelper.getAll(StaticZoneDO.class);
        Map<String, StaticZoneDO> zoneMap = ListUtils.toMap(zoneAll, StaticZoneDO::getZoneName, o -> o);
        SupplyPlanOperateServiceImpl impl = SpringUtil.getBean(SupplyPlanOperateServiceImpl.class);
        for (String order : cancelOrder) {
            try{
                impl.genFullConsensusDemandByOrder(order, zoneMap);
            }catch (Exception e) {
                //告警处理
                String msg = "订单【{}】生成共识需求明细异常【{}】";
                AlarmRobotUtil.doAlarm("ops-genConsensusDemandForOrderCancel",
                        StrUtil.format(msg, order, ExceptionUtil.getMessage(e)),
                        null, false);
            }
        }

    }

}
