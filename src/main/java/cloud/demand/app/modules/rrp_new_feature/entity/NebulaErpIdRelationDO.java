package cloud.demand.app.modules.rrp_new_feature.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.sql.Timestamp;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("nebula_erp_id_relation")
public class NebulaErpIdRelationDO {

    /**
     * 关系id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 星云ID<br/>Column: [nebula_id]
     */
    @Column(value = "nebula_id")
    private Long nebulaId;

    /**
     * 星云预测是否是全年预测<br/>Column: [nebula_is_full_year]
     */
    @Column(value = "nebula_is_full_year")
    private Boolean nebulaIsFullYear;

    /**
     * ERP ID<br/>Column: [erp_id]
     */
    @Column(value = "erp_id")
    private Long erpId;

    /**
     * 关系建立时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Timestamp createTime;

}
