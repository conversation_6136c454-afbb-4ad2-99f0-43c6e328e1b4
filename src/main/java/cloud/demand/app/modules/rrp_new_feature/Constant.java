package cloud.demand.app.modules.rrp_new_feature;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * rrp 常用枚举值
 */
public class Constant {
    public static final Map<Integer, String> DEMAND_FLOW_STEP_ID_2_NAME_MAP;
    public static final Map<String, Integer> DEMAND_FLOW_STEP_NAME_2_ID_MAP;

    public static final String emptyValue = "(空值)";

    static {
        Map<Integer, String> demandFlowStepId2NameMap = new HashMap<>();
        demandFlowStepId2NameMap.put(0, "系统生成");
        demandFlowStepId2NameMap.put(1, "录入中");
        demandFlowStepId2NameMap.put(2, "产品审批");
        demandFlowStepId2NameMap.put(3, "资源组审批");
        demandFlowStepId2NameMap.put(4, "完成待关闭");
        demandFlowStepId2NameMap.put(5, "关闭");
        demandFlowStepId2NameMap.put(6, "产品拒绝");
        demandFlowStepId2NameMap.put(7, "资源组拒绝");
        demandFlowStepId2NameMap.put(8, "资源总监审批");
        DEMAND_FLOW_STEP_ID_2_NAME_MAP = Collections.unmodifiableMap(demandFlowStepId2NameMap);

        Map<String, Integer> demandFlowStepName2IdMap = new HashMap<>();
        demandFlowStepName2IdMap.put("系统生成", 0);
        demandFlowStepName2IdMap.put("录入中", 1);
        demandFlowStepName2IdMap.put("产品审批", 2);
        demandFlowStepName2IdMap.put("资源组审批", 3);
        demandFlowStepName2IdMap.put("完成待关闭", 4);
        demandFlowStepName2IdMap.put("关闭", 5);
        demandFlowStepName2IdMap.put("产品拒绝", 6);
        demandFlowStepName2IdMap.put("资源组拒绝", 7);
        demandFlowStepName2IdMap.put("资源总监审批", 8);
        DEMAND_FLOW_STEP_NAME_2_ID_MAP = Collections.unmodifiableMap(demandFlowStepName2IdMap);
    }
}
