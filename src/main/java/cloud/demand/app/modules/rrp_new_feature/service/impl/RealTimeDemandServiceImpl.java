package cloud.demand.app.modules.rrp_new_feature.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.rrp.RRPDemandItemDO;
import cloud.demand.app.entity.rrp.RRPDemandItemWithVersionDO;
import cloud.demand.app.entity.rrp.RRPFlowDO;
import cloud.demand.app.entity.rrp.RRPVersionDO;
import cloud.demand.app.entity.shuttle.DeviceApplyDO;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.cvmjxc.model.BusinessTypeDTO;
import cloud.demand.app.modules.cvmjxc.service.others.RrpBaseInfoService;
import cloud.demand.app.modules.p2p.product_demand.entity.DemandReasonConfigDO;
import cloud.demand.app.modules.rrp_new_feature.entity.IdSum;
import cloud.demand.app.modules.rrp_new_feature.entity.Month2VersionIdDO;
import cloud.demand.app.modules.rrp_new_feature.entity.Product13WeekDemandRealTimeItemDO;
import cloud.demand.app.modules.rrp_new_feature.entity.RealTimeExecuteDO;
import cloud.demand.app.modules.rrp_new_feature.entity.W13Industry2ProductPRecordDO;
import cloud.demand.app.modules.rrp_new_feature.service.AutoDeliveryDemandForecastService;
import cloud.demand.app.modules.rrp_new_feature.service.RealTimeDemandService;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.enums.DemandFlowStepEnum;
import cloud.demand.app.modules.rrp_remake.model.YearMonth;
import cloud.demand.app.modules.rrp_remake.service.DictService;
import cloud.demand.app.modules.sop.http.domain.robot.MarkdownMsgBody;
import cloud.demand.app.modules.sop.http.service.RtxRobotHttpService;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.tencent.rainbow.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.util.StringUtils;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service("realTimeDemandService")
public class RealTimeDemandServiceImpl implements RealTimeDemandService {

    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper demand145DBHelper;
    @Resource
    DBHelper rrpDBHelper;
    @Resource
    DBHelper shuttleDBHelper;
    @Resource
    RrpBaseInfoService rrpBaseInfoService;
    @Resource
    AutoDeliveryDemandForecastService autoDeliveryDemandForecastService;
    @Resource
    TaskLogService taskLogService;
    @Resource
    private RtxRobotHttpService rtxRobotHttpService;
    @Resource
    DictService dictService;

    private static final Supplier<String> crpPhysicalDemandRobot = DynamicProperty.create("crp.physical.demand.robot", "2999f202-5045-4aa1-897b-cd460c65e518");

    private Map<String, RRPDemandItemWithVersionDO> buildLatestMap() {
        // 找到所有有效的版本预测，并按版本逆序排列
        String rrpSql = "select p.*, c.id as version_id "
                + "from product_info p "
                + "left join flow_info f on f.id = p.flowId "
                + "left join rrp_config c on c.id = f.rrp_config_id "
                + "where (p.backup is null or p.backup = '') "
                + "and f.currentStep not in (?) "
                + "order by c.id desc";
        List<RRPDemandItemWithVersionDO> rrpDemandItemDOS = rrpDBHelper.getRaw(RRPDemandItemWithVersionDO.class,
                rrpSql, Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY));
        if (CollectionUtils.isEmpty(rrpDemandItemDOS)) {
            // 如果有效的版本预测列表为空，需要回滚掉软删除操作，因为空的情况是异常情况
            throw new ITException("rebuild real time table check failed: available version item table is empty");
        }
        log.info("查询到了{}条版本预测数据用于重建", rrpDemandItemDOS.size());
        // 通过实时预测表和版本预测表的对照key，构建一个map用于匹配最新的版本预测
        // 同版本同key的预测要合并数值
        Map<String, RRPDemandItemWithVersionDO> latestMap = new HashMap<>();
        rrpDemandItemDOS.forEach(i -> {
            String key = i.matchRealTimeKey();
            RRPDemandItemWithVersionDO latestDO = latestMap.get(key);
            if (latestDO == null) {
                // 没找到同key的情况下，把i传入进去
                if (i.getCurplanAdjust() == null) {
                    i.setCurplanAdjust(0);
                }
                latestMap.put(key, i);
            } else {
                // 找到同key的情况下，看看是否是同一个版本
                if (latestDO.getVersionId().equals(i.getVersionId())) {
                    // 同一个版本录了重复key的，做合并需求量数值操作
                    latestDO.setAmount(latestDO.getAmount() + i.getAmount());
                    int curPlanAdjust = i.getCurplanAdjust() == null ? 0 : i.getCurplanAdjust();
                    latestDO.setCurplanAdjust(latestDO.getCurplanAdjust() + curPlanAdjust);
                }
                // 不是同一个版本的，忽略掉旧版本的信息（i是旧的）
            }
        });
        return latestMap;
    }

    @Override
    public Map<String, Object> verify() {
        StopWatch stopWatch = new StopWatch("verify");
        stopWatch.start("buildLatestMap");
        // 利用重建的技术，把标准表格load出来
        Map<String, RRPDemandItemWithVersionDO> latestMap = buildLatestMap();
        // 加载目前的实时表
        stopWatch.stop();
        stopWatch.start("buildRealTimeMap");
        List<Product13WeekDemandRealTimeItemDO> realTimeItemDOS = demandDBHelper.getAll(
                Product13WeekDemandRealTimeItemDO.class);
        Map<String, Product13WeekDemandRealTimeItemDO> realTimeMap = ListUtils.toMap(realTimeItemDOS,
                Product13WeekDemandRealTimeItemDO::realTimeKey, Function.identity());
        stopWatch.stop();
        stopWatch.start("compare");
        Map<String, List<Long>> sameRealTimeMap = new HashMap<>();
        StringJoiner sameRealTimeSJ = new StringJoiner("\n");
        realTimeItemDOS.forEach(e -> {
            String key = e.realTimeKey() + "^" + e.getFromVersionId();
            List<Long> ids = sameRealTimeMap.getOrDefault(key, new ArrayList<>());
            ids.add(e.getId());
            sameRealTimeMap.put(key, ids);
        });
        sameRealTimeMap.forEach((k, v) -> {
            if (v.size() > 1) {
                sameRealTimeSJ.add((k + "^" + v.stream().map(String::valueOf).collect(Collectors.joining("^"))).replace("^", "\t"));
            }
        });
        Set<String> allKey = new HashSet<>(latestMap.keySet());
        allKey.addAll(realTimeMap.keySet());
        StringJoiner missRealTimeSJ = new StringJoiner("\n");
        StringJoiner redundantRealTimeSJ = new StringJoiner("\n");
        StringJoiner notMatchVersionSJ = new StringJoiner("\n");
        StringJoiner notMatchDemandNumSJ = new StringJoiner("\n");
        for (String key : allKey) {
            Product13WeekDemandRealTimeItemDO realTimeItemDO = realTimeMap.get(key);
            RRPDemandItemWithVersionDO latestDO = latestMap.get(key);
            if (realTimeItemDO == null && latestDO != null) {
                String hint = key + "^" + latestDO.getVersionId() + "^" + ((long) latestDO.getAmount()
                        + latestDO.getCurplanAdjust());
                missRealTimeSJ.add(hint.replace("^", "\t"));
            } else if (realTimeItemDO != null && latestDO == null) {
                if (!realTimeItemDO.getDemandNum().equals(0L)) {
                    String hint = key + "^" + realTimeItemDO.getFromVersionId() + "^" + realTimeItemDO.getId();
                    redundantRealTimeSJ.add(hint.replace("^", "\t"));
                }
            } else if (realTimeItemDO != null) {
                if (!realTimeItemDO.getFromVersionId().equals(latestDO.getVersionId().longValue())) {
                    String hint = key + "^" + realTimeItemDO.getFromVersionId() + "^" + latestDO.getVersionId() + "^"
                            + realTimeItemDO.getId() + "^" + ((long) latestDO.getAmount()
                            + latestDO.getCurplanAdjust());
                    notMatchVersionSJ.add(hint.replace("^", "\t"));
                } else {
                    Long latestAmount = ((long) latestDO.getAmount() + latestDO.getCurplanAdjust());
                    if (!realTimeItemDO.getDemandNum().equals(latestAmount)) {
                        String hint = key + "^" + realTimeItemDO.getDemandNum() + "^" + latestAmount + "^"
                                + realTimeItemDO.getFromVersionId()
                                + "^" + realTimeItemDO.getId();
                        notMatchDemandNumSJ.add(hint.replace("^", "\t"));
                    }
                }
            }
        }
        stopWatch.stop();
        stopWatch.start("loadResult");
        boolean hasError = false;
        Map<String, Object> result = new HashMap<>();
        if (!StringUtils.isEmpty(missRealTimeSJ.toString())) {
            result.put("missRealTime", missRealTimeSJ.toString());
            hasError = true;
        }
        if (!StringUtils.isEmpty(sameRealTimeSJ.toString())) {
            result.put("sameRealTime", sameRealTimeSJ.toString());
            hasError = true;
        }
        if (!StringUtils.isEmpty(redundantRealTimeSJ.toString())) {
            result.put("redundantRealTime", redundantRealTimeSJ.toString());
            hasError = true;
        }
        if (!StringUtils.isEmpty(notMatchVersionSJ.toString())) {
            result.put("notMatchVersion", notMatchVersionSJ.toString());
            hasError = true;
        }
        if (!StringUtils.isEmpty(notMatchDemandNumSJ.toString())) {
            result.put("notMatchDemandNum", notMatchDemandNumSJ.toString());
            hasError = true;
        }
        if (hasError) {
            log.info("检测到错误:\n{}", result);
        }
        result.put("hasError", hasError);
        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
        return result;
    }

    @Override
    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", waitLockMillisecond = 1000 * 60 * 5, throwExceptionIfNotGetLock = false)
    public void autoFix(Map<String, Object> verifyResult) {
        if (!(boolean) verifyResult.get("hasError")) {
            log.info("没有错误，不需要自动修复");
            return;
        }
        String missRealTime = (String) verifyResult.get("missRealTime");
        String sameRealTime = (String) verifyResult.get("sameRealTime");
        String redundantRealTime = (String) verifyResult.get("redundantRealTime");
        String notMatchVersion = (String) verifyResult.get("notMatchVersion");
        String notMatchDemandNum = (String) verifyResult.get("notMatchDemandNum");

        // 1.处理missRealTime
        if (!StringUtils.isEmpty(missRealTime)) {
            log.info("开始处理missRealTime");
            List<Product13WeekDemandRealTimeItemDO> missRealTimeItemDOS = new ArrayList<>();
            String[] missRealTimeLines = missRealTime.split("\n");
            for (String missRealTimeLine : missRealTimeLines) {
                String[] fileds = missRealTimeLine.split("\t");
                Product13WeekDemandRealTimeItemDO itemDO = new Product13WeekDemandRealTimeItemDO();
                itemDO.setDeleted(false);
                itemDO.setCreator("system_fix");
                itemDO.setUpdater("system_fix");
                itemDO.setDemandYearMonth(fileds[0]);
                itemDO.setPlanProduct(fileds[1]);
                itemDO.setBusinessType(fileds[2]);
                itemDO.setRegion(fileds[3]);
                itemDO.setZone(fileds[4]);
                itemDO.setCampus(fileds[5]);
                itemDO.setDeviceType(fileds[6]);
                itemDO.setProjectType(fileds[7]);
                itemDO.setIndustry(fileds[8]);
                itemDO.setCustomerName(fileds[9]);
                itemDO.setReasonType(fileds[10]);
                itemDO.setReason(fileds[11]);
                itemDO.setDemandType(fileds[12]);
                itemDO.setIsAutoAppend("true".equals(fileds[13]));
                itemDO.setObsProjectType(fileds[14]);
                itemDO.setWeek(Integer.valueOf(fileds[15]));
                itemDO.setFromVersionId(Long.valueOf(fileds[16]));
                itemDO.setDemandNum(Long.valueOf(fileds[17]));
                missRealTimeItemDOS.add(itemDO);
            }
            demandDBHelper.insertBatchWithoutReturnId(missRealTimeItemDOS);
            log.info("处理missRealTime完成");
        }

        // 2.处理sameRealTime
        if (!StringUtils.isEmpty(sameRealTime)) {
            log.info("开始处理sameRealTime");
            String[] sameRealTimeLines = sameRealTime.split("\n");
            List<Long> ids = new ArrayList<>();
            for (String sameRealTimeLine : sameRealTimeLines) {
                String[] fileds = sameRealTimeLine.split("\t");
                for (int i = 17; i < fileds.length - 1; i++) {
                    ids.add(Long.valueOf(fileds[i]));
                }
            }
            demandDBHelper.delete(Product13WeekDemandRealTimeItemDO.class, "where id in (?)", ids);
            log.info("处理sameRealTime完成");
        }

        // 3.处理redundantRealTime
        if (!StringUtils.isEmpty(redundantRealTime)) {
            log.info("开始处理redundantRealTime");
            String[] redundantRealTimeLines = redundantRealTime.split("\n");
            List<Long> ids = new ArrayList<>();
            for (String redundantRealTimeLine : redundantRealTimeLines) {
                String[] fileds = redundantRealTimeLine.split("\t");
                Long id = Long.valueOf(fileds[fileds.length - 1]);
                ids.add(id);
            }
            demandDBHelper.delete(Product13WeekDemandRealTimeItemDO.class, "where id in (?)", ids);
            log.info("处理redundantRealTime完成");
        }

        // 4.处理notMatchVersion
        if (!StringUtils.isEmpty(notMatchVersion)) {
            log.info("开始处理notMatchVersion");
            String[] notMatchVersionLines = notMatchVersion.split("\n");
            List<Product13WeekDemandRealTimeItemDO> notMatchVersionItemDOS = new ArrayList<>();
            for (String notMatchVersionLine : notMatchVersionLines) {
                String[] fileds = notMatchVersionLine.split("\t");
                Product13WeekDemandRealTimeItemDO itemDO = new Product13WeekDemandRealTimeItemDO();
                itemDO.setId(Long.valueOf(fileds[fileds.length - 2]));
                itemDO.setFromVersionId(Long.valueOf(fileds[fileds.length - 3]));
                notMatchVersionItemDOS.add(itemDO);
            }
            demand145DBHelper.update(notMatchVersionItemDOS);
            log.info("处理notMatchVersion完成");
        }

        // 5.处理notMatchDemandNum
        if (!StringUtils.isEmpty(notMatchDemandNum)) {
            log.info("开始处理notMatchDemandNum");
            String[] notMatchDemandNumLines = notMatchDemandNum.split("\n");
            List<Product13WeekDemandRealTimeItemDO> notMatchDemandNumItemDOS = new ArrayList<>();
            for (String notMatchDemandNumLine : notMatchDemandNumLines) {
                String[] fileds = notMatchDemandNumLine.split("\t");
                Product13WeekDemandRealTimeItemDO itemDO = new Product13WeekDemandRealTimeItemDO();
                itemDO.setId(Long.valueOf(fileds[fileds.length - 1]));
                itemDO.setDemandNum(Long.valueOf(fileds[fileds.length - 3]));
                notMatchDemandNumItemDOS.add(itemDO);
            }
            demandDBHelper.update(notMatchDemandNumItemDOS);
            log.info("处理notMatchDemandNum完成");
        }

    }

    @Transactional("demandTransactionManager")
    @Override
    public void rebuild() {
        try {
            // 先清除（软删除）掉实时表（delete不是truncate）
            int deleteNum = demandDBHelper.delete(Product13WeekDemandRealTimeItemDO.class,
                    "where 1 = 1");
            log.info("重建过程中软删除掉了{}条数据", deleteNum);
            Map<String, RRPDemandItemWithVersionDO> latestMap = buildLatestMap();
            // DO转换，版本预测转换成实时预测
            List<Product13WeekDemandRealTimeItemDO> realTimeItemDOS = latestMap.values()
                    .stream()
                    .map(e -> {
                        Product13WeekDemandRealTimeItemDO itemDO = Product13WeekDemandRealTimeItemDO.copy(e);
                        itemDO.setCreator("system");
                        itemDO.setUpdater("system");
                        return itemDO;
                    })
                    .collect(Collectors.toList());
            int insertNum = demandDBHelper.insertBatchWithoutReturnId(realTimeItemDOS);
            log.info("重建后，有{}条实时预测记录", insertNum);
        } catch (Exception e) {
            log.error("rebuild ex:", e);
            rtxRobotHttpService.sendMarkdown(new MarkdownMsgBody(
                            String.format("<@jackycjchen>环境【%s】实时表重建rebuild方法执行失败" +
                                            "错误信息：" + e.getMessage() +
                                            "\n详细堆栈信息如下:\n" + ExceptionUtil.stacktraceToString(e),
                                    ObjectUtils.defaultIfNull(EnvUtils.getStage(), "测试"))
                    ),
                    crpPhysicalDemandRobot.get());
            throw e;
        }
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void syncChangeToRealTime(Collection<Long> versionItemIds,
                                     String username, Map<String, Object> context) {
        log.info("syncChangeToRealTime versionItemIds: {}, username: {}", versionItemIds, username);
        try {
            StopWatch stopWatch = new StopWatch("syncChangeToRealTime");
            if (CollectionUtils.isEmpty(versionItemIds)) {
                return;
            }
            stopWatch.start("getDemandVersionDOById");
            // 先拉取这些id
            String rrpSql = "select p.*, f.rrp_config_id as version_id "
                    + "from product_info p "
                    + "left join flow_info f on f.id = p.flowId "
                    + "where (p.backup is null or p.backup = '') "
                    + "and f.currentStep not in (?) "
                    + "and p.id in (?) ";
            List<RRPDemandItemWithVersionDO> rrpDOS = rrpDBHelper.getRaw(RRPDemandItemWithVersionDO.class, rrpSql,
                    Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY), versionItemIds);
            stopWatch.stop();
            if (CollectionUtils.isEmpty(rrpDOS)) {
                return;
            }
            stopWatch.start("buildSumRrpSql");
            // 扩展出这些id对象的同key同版本数据，把重复值sum进来
            StringJoiner sj = new StringJoiner(",", "(", ")");
            for (RRPDemandItemWithVersionDO e : rrpDOS) {
                String part = e.sameVersionSqlPart();
                sj.add(part);
            }
            stopWatch.stop();
            stopWatch.start("getDemandVersionDOBySum");
            // 因为之前查的flowId都是非拒绝状态的，所以此时不用在flow_info表做where查询
            String sumRrpSql = "select p.id,\n"
                    + "       p.flowId,\n"
                    + "       p.product,\n"
                    + "       p.mod_business_type_name,\n"
                    + "       p.region,\n"
                    + "       p.zone,\n"
                    + "       p.campus,\n"
                    + "       p.deviceType,\n"
                    + "       p.month,\n"
                    + "       sum(ifnull(p.amount, 0))         amount,\n"
                    + "       p.proj_set_name,\n"
                    + "       p.updateTime,\n"
                    + "       p.updator,\n"
                    + "       p.backup,\n"
                    + "       p.customer_name,\n"
                    + "       p.product_remark,\n"
                    + "       p.resource_remark,\n"
                    + "       sum(ifnull(p.curplan_adjust, 0)) curplan_adjust,\n"
                    + "       p.assigned_product,\n"
                    + "       p.industry,\n"
                    + "       p.reason_type,\n"
                    + "       p.reason,\n"
                    + "       p.demand_type,\n"
                    + "       p.auto_append_id,\n"
                    + "       f.rrp_config_id                  version_id,\n"
                    + "       p.obs_project_type,\n"
                    + "       p.week\n"
                    + "from product_info p\n"
                    + "         left join flow_info f on p.flowId = f.id\n"
                    + "where (p.backup is null or p.backup = '')\n"
                    + " and " + RRPDemandItemWithVersionDO.getUnionWhere() + " in "
                    + sj
                    + "\ngroup by p.month, p.product, p.mod_business_type_name,\n"
                    + "         p.region, p.zone, p.campus,\n"
                    + "         p.deviceType, p.proj_set_name,\n"
                    + "         p.industry, p.customer_name,\n"
                    + "         p.reason_type, p.reason, p.demand_type, p.auto_append_id, if(auto_append_id > 0, true, false), p.flowId, p.obs_project_type, p.week";
            List<RRPDemandItemWithVersionDO> sumRrpDOS = rrpDBHelper.getRaw(RRPDemandItemWithVersionDO.class,
                    sumRrpSql);
            stopWatch.stop();
            stopWatch.start("buildRrpMap");
            // 现在得到了同版本同key汇总后的DO列表了，可以去查实时表进行比对
            Map<String, RRPDemandItemWithVersionDO> rrpMap = sumRrpDOS.stream()
                    .collect(Collectors.toMap(RRPDemandItemWithVersionDO::matchRealTimeKey,
                            Function.identity(),
                            (r1, r2) -> {
                                // 这个key会比原来少一个flowId，所以会出现同key的情况，这时候把低版本的key先忽略掉
                                if (r1.getVersionId() >= r2.getVersionId()) {
                                    return r1;
                                } else {
                                    return r2;
                                }
                            }));
            stopWatch.stop();
            stopWatch.start("buildRealTimeSql");
            sj = new StringJoiner(",", "(", ")");
            for (RRPDemandItemWithVersionDO e : rrpMap.values()) {
                String part = e.matchRealSqlPart();
                sj.add(part);
            }
            stopWatch.stop();
            stopWatch.start("getRealTimeDO");
            List<Product13WeekDemandRealTimeItemDO> realTimeDOs = demandDBHelper.getAll(
                    Product13WeekDemandRealTimeItemDO.class,
                    "where " + Product13WeekDemandRealTimeItemDO.getUnionWhere() + " in " + sj);
            stopWatch.stop();
            stopWatch.start("buildRealTimeMap");
            Map<String, Product13WeekDemandRealTimeItemDO> realTimeMap = realTimeDOs
                    .stream()
                    .collect(Collectors.toMap(Product13WeekDemandRealTimeItemDO::realTimeKey,
                            Function.identity(),
                            // 这里不会重复
                            (r1, r2) -> r1));
            stopWatch.stop();
            // 实时表有无同key的，无则直接插入，有则看版本号是否比实时表的要高，高和相同的情况就更新，低不做操作
            stopWatch.start("loadProductMaxVersionIdMap");
            // 加载每个产品最大的有效的versionId
            String filterFakeFlowIdsSql = "";
            if (!CollectionUtils.isEmpty(context)) {
                Collection<Long> fakeRejectIds = (Collection<Long>) context.get("fakeRejectIds");
                if (!CollectionUtils.isEmpty(fakeRejectIds)) {
                    StringJoiner sj0 = new StringJoiner(",");
                    fakeRejectIds.forEach(f -> {
                        sj0.add(String.valueOf(f));
                    });
                    // 过滤假驳回id
                    filterFakeFlowIdsSql = "and id not in (" + sj0 + ")\n";
                }
            }
            Map<String, Long> productMaxVersionIdMap = rrpDBHelper.getRaw(RRPFlowDO.class,
                            "select product, max(rrp_config_id) rrp_config_id\n"
                                    + "from flow_info\n"
                                    + "where currentStep not in (?)\n"
                                    + filterFakeFlowIdsSql
                                    + "group by product", Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY))
                    .stream()
                    .collect(Collectors.toMap(RRPFlowDO::getProduct,
                            e -> e.getRrpConfigId().longValue(),
                            (r1, r2) -> {
                                // 不会进来这里，但这里是一个兜底行为
                                if (r1 > r2) {
                                    return r1;
                                } else {
                                    return r2;
                                }
                            }));
            stopWatch.stop();
            // 实时表有无同key的，无则直接插入，有则看版本号是否比实时表的要高，高和相同的情况就更新，低不做操作
            // 调一个带事务的接口
            stopWatch.start("transaction");
            RealTimeDemandService realTimeDemandService = (RealTimeDemandService) SpringUtil.getBean(
                    "realTimeDemandService");
            realTimeDemandService.baseSyncChangeToRealTime(rrpMap, realTimeMap, username,
                    productMaxVersionIdMap,
                    context);
            stopWatch.stop();
            log.info("{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("syncChangeToRealTime ex:", e);
            rtxRobotHttpService.sendMarkdown(new MarkdownMsgBody(
                            String.format("<@jackycjchen>环境【%s】同步变更到实时表syncChangeToRealTime方法执行失败" +
                                            "错误信息：" + e.getMessage() +
                                            "\n详细堆栈信息如下:\n" + ExceptionUtil.stacktraceToString(e),
                                    ObjectUtils.defaultIfNull(EnvUtils.getStage(), "测试"))
                    ),
                    crpPhysicalDemandRobot.get());
            throw e;
        }
    }

    @Transactional("demandTransactionManager")
    @Override
    public void baseSyncChangeToRealTime(
            Map<String, RRPDemandItemWithVersionDO> rrpMap,
            Map<String, Product13WeekDemandRealTimeItemDO> realTimeMap, String username,
            Map<String, Long> productMaxVersionIdMap, Map<String, Object> context) {
        Map<String, Long> month2VersionIdMap = ListUtils.toMap(demandDBHelper.getAll(Month2VersionIdDO.class),
                Month2VersionIdDO::getMonth, e -> e.getVersionId().longValue());
        Set<Product13WeekDemandRealTimeItemDO> insertSet = new HashSet<>();
        Set<Product13WeekDemandRealTimeItemDO> updateSet = new HashSet<>();
        for (Entry<String, RRPDemandItemWithVersionDO> entry : rrpMap.entrySet()) {
            String key = entry.getKey();
            RRPDemandItemWithVersionDO versionItemDO = entry.getValue();
            Product13WeekDemandRealTimeItemDO realTimeDO = realTimeMap.get(key);
            if (realTimeDO == null) {
                realTimeDO = Product13WeekDemandRealTimeItemDO.copy(versionItemDO);
                realTimeDO.setCreator(username);
                realTimeDO.setUpdater(username);
                // 这里先不insert到数据库，就当做内存是数据库就行
                insertSet.add(realTimeDO);
                realTimeMap.put(key, realTimeDO);
            } else {
                // ** 实时预测的versionId不一定还是有效的最高的(因为驳回流程会调这个函数，使得这种情况出现)，这里拿一下这个产品的最高的versionId当作参与比较的最高id，拿不到就用实时预测的versionId
                Long productMaxVersionId = productMaxVersionIdMap.getOrDefault(versionItemDO.getPlanProduct(),
                        realTimeDO.getFromVersionId());
                // ** 实时预测的versionId不一定还是有效的最高的(因为驳回流程会调这个函数，使得这种情况出现)，这里拿一下这个月份的最高的versionId当作参与比较的最高id，拿不到就用实时预测的versionId
                Long monthMaxVersionId = month2VersionIdMap.getOrDefault(versionItemDO.getYearMonthText(),
                        realTimeDO.getFromVersionId());
                Long maxVersionId = Math.min(productMaxVersionId, monthMaxVersionId);
                if (versionItemDO.getVersionId() >= maxVersionId) {
                    long newDemandNum = (long) versionItemDO.getAmount() + versionItemDO.getCurplanAdjust();
                    if (newDemandNum != realTimeDO.getDemandNum() || !realTimeDO.getFromVersionId()
                            .equals(versionItemDO.getVersionId().longValue())) {
                        // 数量或版本号不等于的时候才发起一次IO，IO的代价比内存中比较高太多了
                        realTimeDO.setFromVersionId(versionItemDO.getVersionId().longValue());
                        realTimeDO.setUpdater(username);
                        realTimeDO.setDemandNum(newDemandNum);
                        realTimeDO.setUpdateTime(null);
                        // 这里先不update到数据库，就当做内存是数据库就行
                        if (!insertSet.contains(realTimeDO)) {
                            // 最后分 2 条路刷数据库，一部分插入，一部分更新
                            updateSet.add(realTimeDO);
                        }
                    }
                } else {
                    log.info("id={}的版本号{}比实时的{}低, 不更新", versionItemDO.getId(),
                            versionItemDO.getVersionId(),
                            maxVersionId);
                }
            }
        }
        if (!CollectionUtils.isEmpty(insertSet)) {
            if (!CollectionUtils.isEmpty(context) && context.containsKey("key2PPLIDMap")) {
                // 后面要用到 id，不能批量插入
                demandDBHelper.insert(insertSet);
            } else {
                demandDBHelper.insertBatchWithoutReturnId(insertSet);
            }
        }
        if (!CollectionUtils.isEmpty(updateSet)) {
            List<List<Product13WeekDemandRealTimeItemDO>> subListList = ListUtils.partition(updateSet, 1000);
            for (int i = 0; i < subListList.size(); i++) {
                demand145DBHelper.update(subListList.get(i));
            }
        }
        if (!CollectionUtils.isEmpty(context)) {
            Map<String, Set<String>> key2PPLIDMap = (Map<String, Set<String>>) context.get("key2PPLIDMap");
            if (!CollectionUtils.isEmpty(key2PPLIDMap)) {
                String key2PPLIDIndustryVersion = (String) context.get("key2PPLIDIndustryVersion");
                String key2PPLIDIndustryName = (String) context.get("key2PPLIDIndustryName");
                Integer key2PPLIDRrpConfigId = (Integer) context.get("key2PPLIDRrpConfigId");
                String key2PPLIDUpperPassId = (String) context.get("key2PPLIDUpperPassId");
                // 先清除上一次传递的结果
                RealTimeDemandService realTimeDemandService = (RealTimeDemandService) SpringUtil.getBean(
                        "realTimeDemandService");
                realTimeDemandService.clearUpToDownPassRecord(key2PPLIDIndustryVersion, key2PPLIDRrpConfigId,
                        key2PPLIDIndustryName);
                // 记录PPL ID与实时预测的关系
                List<W13Industry2ProductPRecordDO> recordDOS = new ArrayList<>();
                key2PPLIDMap.forEach((pplId, realTimeKeySet) -> realTimeKeySet.forEach(realTimeKey -> {
                    W13Industry2ProductPRecordDO recordDO = new W13Industry2ProductPRecordDO();
                    Product13WeekDemandRealTimeItemDO realTimeItemDO = realTimeMap.get(realTimeKey);
                    if (realTimeItemDO != null) {
                        recordDO.setPRealTimeId(realTimeItemDO.getId());
                        recordDO.setPplId(pplId);
                        recordDO.setUsername(username);
                        recordDO.setUniquePassId(String.join("^", key2PPLIDIndustryVersion,
                                String.valueOf(key2PPLIDRrpConfigId), key2PPLIDIndustryName));
                        recordDO.setUpperPassId(key2PPLIDUpperPassId);
                        recordDOS.add(recordDO);
                    }
                }));
                if (!CollectionUtils.isEmpty(recordDOS)) {
                    demandDBHelper.insertBatchWithoutReturnId(recordDOS);
                }
            }
        }
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void syncReleaseFlowEvent(Collection<Long> releaseFlowIds, String username) {
        if (CollectionUtils.isEmpty(releaseFlowIds)) {
            return;
        }
        log.info("syncReleaseFlowEvent releaseFlowIds: {}, username: {}", releaseFlowIds, username);
        try {
            StopWatch stopWatch = new StopWatch("syncReleaseFlowEvent");
            stopWatch.start("getReleaseFlow");
            List<RRPFlowDO> releaseFlowDOS = rrpDBHelper.getAll(RRPFlowDO.class, "where id in (?)", releaseFlowIds);
            stopWatch.stop();
            stopWatch.start("checkReleaseFlow");
            if (CollectionUtils.isEmpty(releaseFlowDOS)) {
                log.warn("找不到被释放的流程，id={}", releaseFlowIds);
                return;
            }
            stopWatch.stop();
            // 释放流程后，要先清理一遍这个流程的实时预测，随后在用旧版本同步,但没法做事务管理（与锁冲突了，这块靠监控补充这一块的欠缺，一般这段代码也不出错误）
            stopWatch.start("callBaseClearRealTimeItem");
            baseClearRealTimeItem(releaseFlowDOS.stream().map(
                    e -> new ClearRealTimeItemDTO(e.getRrpConfigId().longValue(), e.getProduct())
            ).collect(Collectors.toList()));
            stopWatch.stop();
            stopWatch.start("getProductLatestAvailableChangeId");
            // 找每个被释放的流程的上一个有效流程的版本预测id
            List<Long> changeIds = rrpDBHelper.getRaw(Long.class,
                    "select id\n"
                            + "from product_info\n"
                            + "where flowId in (select max(id)\n"
                            + "                 from (select product, id\n"
                            + "                       from flow_info\n"
                            + "                       where product in (?)\n"
                            + "                         and currentStep not in (?)\n"
                            + "                         and id not in (?)\n"
                            + "                       ) t\n"
                            + "                 group by product)\n"
                            + "  and (backup is null or backup = '')",
                    releaseFlowDOS.stream().map(RRPFlowDO::getProduct).collect(Collectors.toList()),
                    Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY),
                    releaseFlowIds);
            stopWatch.stop();
            stopWatch.start("checkAvailableVersionExist");
            if (CollectionUtils.isEmpty(changeIds)) {
                log.info("释放流程({})后，已经没有有效的其他流程了", releaseFlowIds);
                stopWatch.stop();
                log.info("{}", stopWatch.prettyPrint());
                return;
            }
            stopWatch.stop();
            stopWatch.start("callSyncChangeIds");
            // ** 非AOP方式调用syncChangeToRealTime，入参是被释放后，上一个没被释放的流程id
            Map<String, Object> context = new HashMap<>();
            context.put("fakeRejectIds", releaseFlowIds);
            syncChangeToRealTime(changeIds, username, context);
            stopWatch.stop();
            log.info("{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("syncReleaseFlowEvent ex:", e);
            rtxRobotHttpService.sendMarkdown(new MarkdownMsgBody(
                            String.format("<@jackycjchen>环境【%s】同步变更到实时表syncReleaseFlowEvent方法执行失败" +
                                            "错误信息：" + e.getMessage() +
                                            "\n详细堆栈信息如下:\n" + ExceptionUtil.stacktraceToString(e),
                                    ObjectUtils.defaultIfNull(EnvUtils.getStage(), "测试"))
                    ),
                    crpPhysicalDemandRobot.get());
            throw e;
        }
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void syncReleaseFlowEvent(Long releaseFlowId, String username) {
        syncReleaseFlowEvent(ListUtils.newArrayList(releaseFlowId), username);
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void syncFlowStatus(Long flowId, String username, Map<String, Object> context) {
        syncFlowStatus(ListUtils.newArrayList(flowId), username, context);
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void syncFlowStatus(Collection<Long> flowIds, String username, Map<String, Object> context) {
        try {
            // ** 调用syncChangeToRealTime不要用aop的方式，syncChangeToRealTime的锁不支持重入，利用同一线程调用的能力重入即可
            StopWatch stopWatch = new StopWatch("syncFlowStatus");
            stopWatch.start("getFlow");
            List<RRPFlowDO> flowDOs = rrpDBHelper.getAll(RRPFlowDO.class,
                    "where id in (?) and currentStep not in (?)",
                    flowIds, Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY));
            stopWatch.stop();
            stopWatch.start("checkFlow");
            if (CollectionUtils.isEmpty(flowDOs)) {
                // 不一定要调用syncChangeToRealTime，得看这个flowId是不是最新有效的flowId
                return;
            }
            // 获取有效流程的id
            flowIds = flowDOs.stream()
                    .map(RRPFlowDO::getId)
                    .filter(Objects::nonNull)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
            stopWatch.stop();
            stopWatch.start("getVersionItemIdsBelongThisFlow");
            List<Long> versionItemIds = rrpDBHelper.getRaw(Long.class,
                    "select id from product_info where flowId in (?)", flowIds);
            stopWatch.stop();
            stopWatch.start("callSyncChangeToRealTime");
            syncChangeToRealTime(versionItemIds, username, context);
            stopWatch.stop();
            log.info("{}", stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("syncFlowStatus ex:", e);
            rtxRobotHttpService.sendMarkdown(new MarkdownMsgBody(
                            String.format("<@jackycjchen>环境【%s】同步变更到实时表syncFlowStatus方法执行失败" +
                                            "错误信息：" + e.getMessage() +
                                            "\n详细堆栈信息如下:\n" + ExceptionUtil.stacktraceToString(e),
                                    ObjectUtils.defaultIfNull(EnvUtils.getStage(), "测试"))
                    ),
                    crpPhysicalDemandRobot.get());
            throw e;
        }
    }

    @Override
    public void baseClearRealTimeItem(Long versionId, String planProduct) {
        baseClearRealTimeItem(ListUtils.newArrayList(new ClearRealTimeItemDTO(versionId, planProduct)));
    }

    @Override
    public void baseClearRealTimeItem(List<ClearRealTimeItemDTO> dtos) {
        StopWatch stopWatch = new StopWatch("baseClearRealTimeItem");
        stopWatch.start("clear");
        demandDBHelper.updateAll(Product13WeekDemandRealTimeItemDO.class, "set demand_num = 0, from_version_id = 0",
                "where (from_version_id, plan_product) in (?)",
                dtos.stream().map(
                        d -> new Object[]{d.getVersionId(), d.getPlanProduct()}
                ).collect(Collectors.toList()));
        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
    }

    @Synchronized(namespace = "REAL-TIME-13-SYNC-CHANGE", throwExceptionIfNotGetLock = false)
    @Override
    public void clearRealTimeItem(Long flowId, String username) {
        StopWatch stopWatch = new StopWatch("clearRealTimeItem");
        stopWatch.start("getFlow");
        RRPFlowDO flowDO = rrpDBHelper.getOne(RRPFlowDO.class, "where id = ?", flowId);
        if (flowDO != null) {
            // 非aop方式调用
            baseClearRealTimeItem(flowDO.getRrpConfigId().longValue(), flowDO.getProduct());
        }
        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
    }

    @Override

    @Transactional("demandTransactionManager")
    public void clearUpToDownPassRecord(String industryVersion, Integer rrpConfigId, String industryName) {
        demandDBHelper.delete(W13Industry2ProductPRecordDO.class,
                "where unique_pass_id = ?",
                String.join("^", industryVersion,
                        String.valueOf(rrpConfigId), industryName));
    }

    @Override
    @Transactional("demandTransactionManager")
    public void clearUpToDownPassRecord(String industryVersion, Integer rrpConfigId) {
        demandDBHelper.delete(W13Industry2ProductPRecordDO.class,
                "where unique_pass_id like '%^" + rrpConfigId + "^%'");
    }

    @Override
    public void transactionSyncFromOrderId(Set<String> orderIdSet,
                                           Map<String, OrderIdExecutedRelation> orderIdExecutedRelationMap) {
        if (!CollectionUtils.isEmpty(orderIdSet)) {
            // 先清空这些子单的执行关系
            demandDBHelper.delete(RealTimeExecuteDO.class, "where order_id in (?)", orderIdSet);
        }
        // 再构建星云的执行关系
        if (!CollectionUtils.isEmpty(orderIdExecutedRelationMap)) {
            List<RealTimeExecuteDO> realTimeExecuteDOS = new ArrayList<>();
            for (String orderId : orderIdExecutedRelationMap.keySet()) {
                OrderIdExecutedRelation orderIdExecutedRelation = orderIdExecutedRelationMap.get(orderId);
                Set<Long> nebulaIdList = orderIdExecutedRelation.getNebulaIdList();
                Map<Long, Integer> totalNumMap = orderIdExecutedRelation.getTotalNumMap();
                for (Long nebulaId : nebulaIdList) {
                    Integer num = totalNumMap.get(nebulaId);
                    if (num == null || num == 0) {
                        continue;
                    }
                    RealTimeExecuteDO realTimeExecuteDO = new RealTimeExecuteDO();
                    realTimeExecuteDO.setOrderId(orderId);
                    realTimeExecuteDO.setRealTimeId(nebulaId);
                    realTimeExecuteDO.setExecuteNum(num);
                    realTimeExecuteDO.setExecuteTime(orderIdExecutedRelation.getTime());
                    realTimeExecuteDO.setExecuteUser(orderIdExecutedRelation.getUser());
                    realTimeExecuteDOS.add(realTimeExecuteDO);
                }
            }
            demandDBHelper.insertBatchWithoutReturnId(realTimeExecuteDOS);
        }
    }

    public void minusSystemNotExecutedNum(Set<Long> possibleSystemMinusIds) {
        // 把实时预测对应的版本预测找出来（实时预测识别不了传递、提交阶段、继承等补预测这种系统预测）
        // 如果版本预测有系统补全的数据，那就要列入考虑范围内
        // 最多能减至多少预测量=max（执行量，人工预测量）
        // 换句话说，只能调减版本预测中是系统补全的那一块的预测
        if (!CollectionUtils.isEmpty(possibleSystemMinusIds)) {
            // 重查一遍实时预测
            List<Product13WeekDemandRealTimeItemDO> realTimeItemDOS = demandDBHelper.getAll(
                    Product13WeekDemandRealTimeItemDO.class, "where id in (?)", possibleSystemMinusIds);
            // 查出这些id对应的预测量
            List<IdSum> executedInfoList = demandDBHelper.getRaw(IdSum.class,
                    "select real_time_id, sum(execute_num) e "
                            + "from real_time_execute where real_time_id in (?) and deleted = 0 "
                            + "group by real_time_id", possibleSystemMinusIds);
            Map<Long, Long> executedInfoMap =
                    ListUtils.toMap(executedInfoList, IdSum::getRealTimeId, IdSum::getSum);
            Set<Long> checkIds = new HashSet<>();
            for (Product13WeekDemandRealTimeItemDO realTimeItemDO : realTimeItemDOS) {
                Long id = realTimeItemDO.getId();
                if (checkIds.contains(id)) {
                    continue;
                }
                // 查一下这个ID所对应的执行量是多少
                Long executedNum = executedInfoMap.getOrDefault(id, 0L);
                // 对比实时预测的需求量和执行量
                Long demandNum = realTimeItemDO.getDemandNum();
                if (demandNum > executedNum) {
                    // 如果需求量比执行量大，说明有未执行量，得看系统补全的那一部分的量是多少
                    long nonExecutedNum = demandNum - executedNum;
                    // 查出相应的版本预测
                    List<RRPDemandItemDO> rrpDemandItemDOS = rrpDBHelper.getRaw(RRPDemandItemDO.class,
                            realTimeItemDO.findVersionItemSql());
                    // 只把系统补全的那一块预测拉出来（可调整范围）
                    long systemDemandNum = 0L;
                    List<RRPDemandItemDO> systemList = new ArrayList<>();
                    for (RRPDemandItemDO rrpDemandItemDO : rrpDemandItemDOS) {
                        if (rrpDemandItemDO.getSource().equals("系统补全")) {
                            systemList.add(rrpDemandItemDO);
                            systemDemandNum += rrpDemandItemDO.getAmount();
                            if (rrpDemandItemDO.getCurplanAdjust() != null) {
                                systemDemandNum += rrpDemandItemDO.getCurplanAdjust();
                            }
                        }
                    }
                    // 能够减少的量 = min（系统的需求量，未执行量）
                    long canMinusNum = Math.min(systemDemandNum, nonExecutedNum);
                    long copyCanMinusNum = canMinusNum;
                    for (int i = 0; canMinusNum > 0 && i < systemList.size(); i++) {
                        RRPDemandItemDO item = systemList.get(i);
                        long itemNum = item.getAmount();
                        if (item.getCurplanAdjust() != null) {
                            itemNum += item.getCurplanAdjust();
                        }
                        // 能够从本条目拿走的数量 = min（剩余要减少的量，本条目的量）
                        long thisItemTakeNum = Math.min(canMinusNum, itemNum);
                        canMinusNum -= thisItemTakeNum;
                        if (item.getCurplanAdjust() == null) {
                            item.setCurplanAdjust((int) -thisItemTakeNum);
                        } else {
                            item.setCurplanAdjust(item.getCurplanAdjust() - (int) thisItemTakeNum);
                        }
                        String hint = "存在改单减小的情况";
                        String memo = item.getResourceRemark();
                        if (memo == null) {
                            memo = hint;
                        } else if (!memo.contains(hint)) {
                            memo = memo + ";" + hint;
                        }
                        item.setResourceRemark(memo);
                        // 更新回去版本预测
                        rrpDBHelper.update(item);
                    }
                    // 更新实时预测
                    demandDBHelper.executeRaw("update product_13_week_demand_real_time_item "
                            + "set demand_num = demand_num - ? where id = ?", copyCanMinusNum, id);
                }
                checkIds.add(id);
            }
        }
    }

    public void cleanOrder(List<DeviceApplyDO> deviceApplyDOS) {
        deviceApplyDOS.forEach(d -> {
            String obsProjectType = d.getObsProjectType();
            if ("常规项目".equals(obsProjectType)) {
                d.setDemandType("常规");
            } else if ("短租项目".equals(obsProjectType)) {
                d.setDemandType("短租");
            } else {
                d.setDemandType("项目");
            }
            if (StringUtil.isEmpty(d.getModBusinessType())) {
                d.setModBusinessType("腾讯云-公有云-通用");
            } else {
                if (!d.getModBusinessType().startsWith("自研")
                        && !d.getModBusinessType().startsWith("腾讯云-")) {
                    d.setModBusinessType("腾讯云-" + d.getModBusinessType());
                }
            }
            Date expectDeliveryDate = d.getExpectDeliveryDate();
            ResPlanHolidayWeekDO weekDO = dictService.getWeekByDateGoMemory(DateUtils.formatDate(expectDeliveryDate));
            Integer week = weekDO.getWeek();
            // 这里不会找不到，weekDO 不会为null
            // 如果 weekDO 的年月和需求年月不匹配，则使用需求年月的第一周
            if (!weekDO.yearMonthStr().equals(d.getPlanMonth())) {
                week = dictService.getHolidayWeekByYearMonth(new YearMonth(d.getPlanMonth(), null, null)).get(0);
            }
            d.setDemandWeek(week);
        });
    }

    public void eatSyncFromOrderId(List<String> orderIds, boolean isR, Map<String, Object> context) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        Set<String> orderIdSet = new HashSet<>(orderIds);
        // 添加驳回条件，考虑驳回的情况
        // 查出对应的星云采购单
        List<DeviceApplyDO> deviceApplyDOS = shuttleDBHelper.getAll(DeviceApplyDO.class,
                "where sub_id in (?) and order_type = ? and status not in (0, 3, 5, 8000127) order by sub_id desc",
                orderIdSet, isR ? 100 : 1);
        cleanOrder(deviceApplyDOS);
        // notExistOrders 是本次查询不存在的 R单 集合了（代表被驳回了）
        Set<String> notExistOrders = new HashSet<>();
        Set<String> existOrderIds = deviceApplyDOS.stream().map(DeviceApplyDO::getSubId).collect(Collectors.toSet());
        for (String originOrderId : orderIdSet) {
            if (!existOrderIds.contains(originOrderId)) {
                notExistOrders.add(originOrderId);
            }
        }
        Set<Long> possibleSystemMinusIds = new HashSet<>();
        // 改单的话，加载这些订单的所扣的id和数量，进行后续的挪动预测
        // existRealTimeExecuteDOS 是存在的单据列表，本次传入的单号参数有重合的话，会被记录，当做改单识别记录
        List<RealTimeExecuteDO> existRealTimeExecuteDOS = demandDBHelper.getAll(RealTimeExecuteDO.class,
                "where order_id in (?)", orderIdSet);
        Map<String, List<RealTimeExecuteDO>> existRealTimeExecuteMap = ListUtils.toMapList(existRealTimeExecuteDOS,
                RealTimeExecuteDO::getOrderId, Function.identity());
        // 查不到的订单号，就是被撤回了，撤回了也算改单减小的情况
        if (!CollectionUtils.isEmpty(deviceApplyDOS)) {
            // 查实时预测的时候，用模糊的字段去查
            List<Object[]> params = new ArrayList<>();
            for (DeviceApplyDO deviceApplyDO : deviceApplyDOS) {
                params.add(deviceApplyDO.rQuerySqlArgs());
            }
            List<Product13WeekDemandRealTimeItemDO> realTimeItemDOS = demandDBHelper.getAll(
                    Product13WeekDemandRealTimeItemDO.class,
                    "where (demand_year_month, plan_product, device_type) in (?) order by is_auto_append asc",
                    params);
            // 加载版本辅助表，只允许匹配这个月份的对应版本的预测
            List<Month2VersionIdDO> month2VersionIdDOS = demandDBHelper.getAll(Month2VersionIdDO.class);
            Map<String, Integer> monthVersionIdMap = ListUtils.toMap(month2VersionIdDOS, Month2VersionIdDO::getMonth,
                    Month2VersionIdDO::getVersionId);
            if (!CollectionUtils.isEmpty(realTimeItemDOS)) {
                realTimeItemDOS.removeIf(e -> {
                    Integer versionId = monthVersionIdMap.get(e.getDemandYearMonth());
                    if (versionId == null) {
                        return true;
                    }
                    return !versionId.equals(e.getFromVersionId().intValue());
                });
            }
            // 加载这些id的执行量，但不包括本次的orderId
            Map<Long, Long> eachIdExecuteNumMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(realTimeItemDOS)) {
                Set<Long> realTimeIds = new HashSet<>();
                for (Product13WeekDemandRealTimeItemDO realTimeItemDO : realTimeItemDOS) {
                    realTimeIds.add(realTimeItemDO.getId());
                }
                List<RealTimeExecuteDO> eachIdExecuteDOS = demandDBHelper.getAll(RealTimeExecuteDO.class,
                        "where real_time_id in (?) and order_id not in (?)", realTimeIds, orderIds);
                if (!CollectionUtils.isEmpty(eachIdExecuteDOS)) {
                    for (RealTimeExecuteDO realTimeExecuteDO : eachIdExecuteDOS) {
                        long num = eachIdExecuteNumMap.getOrDefault(realTimeExecuteDO.getRealTimeId(), 0L);
                        eachIdExecuteNumMap.put(realTimeExecuteDO.getRealTimeId(),
                                num + realTimeExecuteDO.getExecuteNum());
                    }
                }
            }
            // 用全字段匹配 + 模糊匹配的两层方式，过滤出对应的实时预测（需求月份对应的版本id的）
            Map<String, String> regionCountryMap = autoDeliveryDemandForecastService.buildZoneAreaCountryMap();
            Map<String, String> obsProjectType2MixProjectTypeMap = autoDeliveryDemandForecastService.buildObsProjectType2MixProjectTypeMap();
            int maxLevel = 7;
            Map<String, List<Product13WeekDemandRealTimeItemDO>> fuzzyRealTimeMap =
                    ListUtils.toMapList(realTimeItemDOS, e -> e.fuzzyMatchRKey(maxLevel, regionCountryMap, obsProjectType2MixProjectTypeMap), e -> e);
            // 匹配结果
            // 需要自动追加的
            Map<String, MissRealTimeItem> autoAppendMap = new HashMap<>();
            // 改单后，需要减少原值的
            List<RealTimeExecuteDO> minusList = new ArrayList<>();
            // 订单扣除关系记录
            Map<String, OrderIdExecutedRelation> orderIdExecutedRelationMap = new HashMap<>();
            for (DeviceApplyDO deviceApplyDO : deviceApplyDOS) {
                int thisOrderLeftNum = deviceApplyDO.getTotalNum();
                if (thisOrderLeftNum <= 0) {
                    // 小于等于0也不用自动追加了
                    continue;
                }
                List<Product13WeekDemandRealTimeItemDO> fuzzyRealTimeList =
                        fuzzyRealTimeMap.get(deviceApplyDO.rLevelKey(maxLevel, regionCountryMap, obsProjectType2MixProjectTypeMap));
                // 如果是改单的情况，除了这个单之前扣过的预测id可以再一次分配订单量，其他的不能进行匹配，只能自动追加
                // 改单：能参与匹配的id，重新分配订单量，分出来的结果有多余的（会产生未执行量）不用减少，不够分的，会自动追加
                // 不能参与匹配的id，等会要删预测，代表直接删需求量，不会产生新的未执行量
                List<RealTimeExecuteDO> hasExecuted = existRealTimeExecuteMap.get(deviceApplyDO.getSubId());
                if (!CollectionUtils.isEmpty(hasExecuted)) {
                    Set<Long> realTimeIds = new HashSet<>();
                    Set<Long> matchIds = new HashSet<>();
                    for (RealTimeExecuteDO realTimeExecuteDO : hasExecuted) {
                        realTimeIds.add(realTimeExecuteDO.getRealTimeId());
                    }
                    // 扣过的id才能在fuzzy list里
                    if (!CollectionUtils.isEmpty(fuzzyRealTimeList)) {
                        fuzzyRealTimeList.removeIf(e -> {
                            if (!realTimeIds.contains(e.getId())) {
                                // 没扣过的id,在fuzzy list 踢出
                                return true;
                            } else {
                                // 包含的id 保留
                                // 扣过的ID里，如果在本次的fuzzy list里，那最后要检查这些ID是不是系统生成的，如果是，要检查有没有未执行量，有要删掉
                                possibleSystemMinusIds.add(e.getId());
                                matchIds.add(e.getId());
                            }
                            return false;
                        });
                    }
                    for (RealTimeExecuteDO realTimeExecuteDO : hasExecuted) {
                        if (!matchIds.contains(realTimeExecuteDO.getRealTimeId())) {
                            // 扣过的ID里，如果ID不在本次的fuzzy list里，那就要减掉预测量
                            minusList.add(realTimeExecuteDO);
                        }
                    }
                }
                String orderId = deviceApplyDO.getSubId();
                if (!CollectionUtils.isEmpty(fuzzyRealTimeList)) {
                    // 全key
                    // 模糊key：
                    Set<Long> takeIds = new HashSet<>();
                    for (int level = 0; level <= maxLevel; level++) {
                        if (thisOrderLeftNum <= 0) {
                            break;
                        }
                        // 从严格到宽松过一遍所有list
                        for (Product13WeekDemandRealTimeItemDO realTimeItemDO : fuzzyRealTimeList) {
                            if (thisOrderLeftNum <= 0) {
                                break;
                            }
                            Long realTimeId = realTimeItemDO.getId();
                            if (takeIds.contains(realTimeId)) {
                                continue;
                            }
                            String rKey = realTimeItemDO.fuzzyMatchRKey(level, regionCountryMap, obsProjectType2MixProjectTypeMap);
                            String oKey = deviceApplyDO.rLevelKey(level, regionCountryMap, obsProjectType2MixProjectTypeMap);
                            if (rKey.equals(oKey)) {
                                // 匹配到了，就不用匹配其他优先级了，因为一个预测不能重复匹配多个优先级
                                takeIds.add(realTimeId);
                                long executedNum = eachIdExecuteNumMap.getOrDefault(realTimeId, 0L);
                                long nonExecutedNum = realTimeItemDO.getDemandNum() - executedNum;
                                nonExecutedNum = Math.max(0, nonExecutedNum);
                                long canTakeNum = Math.min(thisOrderLeftNum, nonExecutedNum);
                                if (canTakeNum <= 0) {
                                    continue;
                                }
                                // 这个预测id拿了预测，更新它的执行量和这个单的剩余量
                                thisOrderLeftNum -= (int) canTakeNum;
                                eachIdExecuteNumMap.put(realTimeId, executedNum + canTakeNum);
                                OrderIdExecutedRelation orderIdExecutedRelation =
                                        orderIdExecutedRelationMap.get(orderId);
                                // 以及把它加入到执行记录结果集
                                if (orderIdExecutedRelation == null) {
                                    orderIdExecutedRelation = new OrderIdExecutedRelation(orderId,
                                            new HashSet<>(), new HashMap<>(), deviceApplyDO.getCreator(),
                                            deviceApplyDO.getCreateTime());
                                    orderIdExecutedRelationMap.put(orderId, orderIdExecutedRelation);
                                }
                                orderIdExecutedRelation.getNebulaIdList().add(realTimeId);
                                int origin = orderIdExecutedRelation.getTotalNumMap()
                                        .getOrDefault(realTimeId, 0);
                                orderIdExecutedRelation.getTotalNumMap().put(realTimeId,
                                        (int) (origin + canTakeNum));
                            }
                        }
                    }
                }
                if (thisOrderLeftNum > 0) {
                    // 还有剩余量，剩余量加入到自动追加列表
                    MissRealTimeItem missRealTimeItem = autoAppendMap.get(orderId);
                    if (missRealTimeItem == null) {
                        missRealTimeItem = new MissRealTimeItem(orderId, null, thisOrderLeftNum,
                                deviceApplyDO.getCreator(),
                                deviceApplyDO.getCreateTime());
                        autoAppendMap.put(orderId, missRealTimeItem);
                    } else {
                        missRealTimeItem.setNum(missRealTimeItem.getNum() + thisOrderLeftNum);
                    }
                }
            }
            // 实时预测足够的部分进行扣除，不够的部分就要自动追加预测
            // 先自动追加，补充结果集
            if (!CollectionUtils.isEmpty(autoAppendMap)) {
                autoAppend(autoAppendMap, context);
                for (Entry<String, MissRealTimeItem> entry : autoAppendMap.entrySet()) {
                    MissRealTimeItem missRealTimeItem = entry.getValue();
                    String orderId = missRealTimeItem.getOrderId();
                    OrderIdExecutedRelation orderIdExecutedRelation = orderIdExecutedRelationMap.get(orderId);
                    if (orderIdExecutedRelation == null) {
                        Map<Long, Integer> myTotalNumMap = new HashMap<>();
                        myTotalNumMap.put(missRealTimeItem.getRealTimeId(), missRealTimeItem.getNum());
                        Set<Long> nebulaIdList = new HashSet<>();
                        nebulaIdList.add(missRealTimeItem.getRealTimeId());
                        orderIdExecutedRelation = new OrderIdExecutedRelation(orderId, nebulaIdList,
                                myTotalNumMap, missRealTimeItem.getUser(), missRealTimeItem.getTime());
                        orderIdExecutedRelationMap.put(orderId, orderIdExecutedRelation);
                    } else {
                        orderIdExecutedRelation.getNebulaIdList().add(missRealTimeItem.getRealTimeId());
                        Integer originNum = orderIdExecutedRelation.getTotalNumMap()
                                .getOrDefault(missRealTimeItem.getRealTimeId(), 0);
                        originNum += missRealTimeItem.getNum();
                        orderIdExecutedRelation.getTotalNumMap()
                                .put(missRealTimeItem.getRealTimeId(), originNum);
                    }
                    long executeNum = eachIdExecuteNumMap.getOrDefault(missRealTimeItem.getRealTimeId(), 0L);
                    executeNum += missRealTimeItem.getNum();
                    eachIdExecuteNumMap.put(missRealTimeItem.getRealTimeId(), executeNum);
                }
            }
            // 改单挪动预测
            if (!CollectionUtils.isEmpty(minusList)) {
                minusByModifyOrder(minusList);
            }
            // 再记录执行关系
            transactionSyncFromOrderId(orderIdSet, orderIdExecutedRelationMap);
        } else {
            // 订单列表为空，代表这些单子被撤回了，删除一下执行关系即可(返回未执行量给星云，但不传递给ERP）
            demandDBHelper.delete(RealTimeExecuteDO.class,
                    "where order_id in (?)", orderIdSet);
        }
        if (!CollectionUtils.isEmpty(notExistOrders)) {
            for (String orderId : notExistOrders) {
                List<RealTimeExecuteDO> executeDOS = existRealTimeExecuteMap.get(orderId);
                if (!CollectionUtils.isEmpty(executeDOS)) {
                    for (RealTimeExecuteDO ddo : executeDOS) {
                        possibleSystemMinusIds.add(ddo.getRealTimeId());
                    }
                }
            }
        }
        // 再检查改单改小的情况
        minusSystemNotExecutedNum(possibleSystemMinusIds);
    }

    private void minusByModifyOrder(List<RealTimeExecuteDO> needToMinusList) {
        // 多出来的已扣的nebula id，要减小预测，代表改单挪动
        // 根据realTime id 聚合list，因为可能多个订单同时扣一个realTime
        Map<Long, MinusGroupByItem> minusGroupByItemMap = new HashMap<>();
        for (RealTimeExecuteDO realTimeExecuteDO : needToMinusList) {
            MinusGroupByItem exist = minusGroupByItemMap.get(realTimeExecuteDO.getRealTimeId());
            if (exist == null) {
                exist = new MinusGroupByItem();
                exist.setRealTimeId(realTimeExecuteDO.getRealTimeId());
                exist.setSumNum(realTimeExecuteDO.getExecuteNum());
                exist.setOrderIdText(realTimeExecuteDO.getOrderId());
                minusGroupByItemMap.put(realTimeExecuteDO.getRealTimeId(), exist);
            } else {
                exist.setSumNum(exist.getSumNum() + realTimeExecuteDO.getExecuteNum());
                exist.setOrderIdText(String.join(";", exist.getOrderIdText(),
                        realTimeExecuteDO.getOrderId()));
            }

        }
        // 先调减实时预测
        List<Product13WeekDemandRealTimeItemDO> realTimeItemDOS = demandDBHelper.getAll(
                Product13WeekDemandRealTimeItemDO.class,
                "where id in (?)", minusGroupByItemMap.keySet());
        Map<Long, Product13WeekDemandRealTimeItemDO> realTimeItemDOMap = ListUtils.toMap(realTimeItemDOS,
                BaseDO::getId, Function.identity());
        for (Long realTimeId : minusGroupByItemMap.keySet()) {
            MinusGroupByItem minusGroupByItem = minusGroupByItemMap.get(realTimeId);
            Integer needToMinusNumTotal = minusGroupByItem.getSumNum();
            demandDBHelper.executeRaw("update product_13_week_demand_real_time_item "
                            + "set demand_num = demand_num - ? where id = ?", needToMinusNumTotal,
                    realTimeId);
            // 减少版本预测
            Product13WeekDemandRealTimeItemDO realTimeItemDO = realTimeItemDOMap.get(realTimeId);
            if (realTimeItemDO == null) {
                continue;
            }
            List<RRPDemandItemDO> rrpDemandItemDOS = rrpDBHelper.getRaw(RRPDemandItemDO.class,
                    realTimeItemDO.findVersionItemSql());
            if (!CollectionUtils.isEmpty(rrpDemandItemDOS)) {
                // 依次减少
                for (RRPDemandItemDO rrpDemandItemDO : rrpDemandItemDOS) {
                    if (needToMinusNumTotal == 0) {
                        break;
                    }
                    int curplanAdjust =
                            rrpDemandItemDO.getCurplanAdjust() == null ? 0 : rrpDemandItemDO.getCurplanAdjust();
                    int demandNum = rrpDemandItemDO.getAmount() + curplanAdjust;
                    int canMinusNum = Math.min(demandNum, needToMinusNumTotal);
                    rrpDemandItemDO.setCurplanAdjust(curplanAdjust - canMinusNum);
                    String resourceRemark = rrpDemandItemDO.getResourceRemark();
                    String memo = "由于[" + minusGroupByItem.getOrderIdText() + "]的改单，预测量共减少了" + canMinusNum;
                    if (org.apache.commons.lang3.StringUtils.isBlank(resourceRemark)) {
                        rrpDemandItemDO.setResourceRemark(memo);
                    } else {
                        String newMemo = resourceRemark + ";" + memo;
                        rrpDemandItemDO.setResourceRemark(newMemo.substring(0, Math.min(newMemo.length(), 2048)));
                    }
                    needToMinusNumTotal -= canMinusNum;
                }
            }
            rrpDBHelper.update(rrpDemandItemDOS);
        }
    }

    @Synchronized(namespace = "SYNC_ORDER_FOR_REAL", waitLockMillisecond = 60000, throwExceptionIfNotGetLock = false)
    public void innerSyncFromOrderId(List<String> orderIds, boolean isR, Map<String, Object> context) {
        eatSyncFromOrderId(orderIds, isR, context);
    }

    public Object extractContext(Map<String, Object> context, String key) {
        if (context == null) {
            return null;
        } else {
            return context.get(key);
        }
    }

    @Override
    public void autoAppend(Map<String, MissRealTimeItem> missRealTimeItemMap, Map<String, Object> context) {
        if (CollectionUtils.isEmpty(missRealTimeItemMap)) {
            return;
        }
        List<MissRealTimeItem> missRealTimeItems = new ArrayList<>(missRealTimeItemMap.values());
        Set<String> subIds = missRealTimeItems.stream().map(MissRealTimeItem::getOrderId)
                .collect(Collectors.toSet());
        // 找子单信息进行信息补全
        List<DeviceApplyDO> deviceApplyDOS = shuttleDBHelper.getAll(DeviceApplyDO.class,
                "where sub_id in (?)", subIds);
        cleanOrder(deviceApplyDOS);
        if (CollectionUtils.isEmpty(deviceApplyDOS)) {
            // 找不到子单则无法补齐
            return;
        }
        Map<String, DeviceApplyDO> deviceApplyDOMap = deviceApplyDOS.stream()
                .collect(Collectors.toMap(DeviceApplyDO::getSubId,
                        Function.identity(),
                        (e1, e2) -> e2));
        // 先查出当前有的实时预测
        StringJoiner sj = new StringJoiner(",", "(", ")");
        List<BusinessTypeDTO> businessTypeDTOS = rrpBaseInfoService.getRawPlanProductToBusiness();
        List<DemandReasonConfigDO> demandReasonConfigDOs = rrpDBHelper.getAll(DemandReasonConfigDO.class);
        Map<String, String> demandReasonConfigDOMap = demandReasonConfigDOs.stream()
                .collect(Collectors.toMap(DemandReasonConfigDO::getReason, DemandReasonConfigDO::getReasonType,
                        (e1, e2) -> e1));
        for (DeviceApplyDO deviceApplyDO : deviceApplyDOS) {
            String part = deviceApplyDO.matchRealSqlPart(true, demandReasonConfigDOMap, businessTypeDTOS);
            sj.add(part);
        }
        List<Product13WeekDemandRealTimeItemDO> realTimeDOs = demandDBHelper.getAll(
                Product13WeekDemandRealTimeItemDO.class,
                "where " + Product13WeekDemandRealTimeItemDO.getUnionWhere() + " in " + sj);
        Map<String, Product13WeekDemandRealTimeItemDO> existItemMap = realTimeDOs.stream()
                .collect(Collectors.toMap(Product13WeekDemandRealTimeItemDO::realTimeKey,
                        Function.identity(), (e1, e2) -> e1));
        Map<String, Product13WeekDemandRealTimeItemDO> insertItemMap = new HashMap<>();
        Map<String, Product13WeekDemandRealTimeItemDO> updateItemMap = new HashMap<>();
        List<RRPDemandItemDO> versionInsertList = new ArrayList<>();
        List<Month2VersionIdDO> month2VersionIdDOS = demandDBHelper.getAll(Month2VersionIdDO.class);
        Map<String, Integer> month2VersionIdDOMap = ListUtils.toMap(month2VersionIdDOS,
                Month2VersionIdDO::getMonth, Month2VersionIdDO::getVersionId);
        List<Integer> rrpConfigIds = new ArrayList<>();
        for (MissRealTimeItem missRealTimeItem : missRealTimeItems) {
            DeviceApplyDO deviceApplyDO = deviceApplyDOMap.get(missRealTimeItem.getOrderId());
            if (deviceApplyDO == null) {
                continue;
            }
            Integer fromVersionId = month2VersionIdDOMap.get(deviceApplyDO.getPlanMonth());
            if (fromVersionId == null) {
                continue;
            }
            rrpConfigIds.add(fromVersionId);
        }

        List<RRPFlowDO> rrpFlowDOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(rrpConfigIds)) {
            rrpFlowDOS = rrpDBHelper.getAll(RRPFlowDO.class,
                    "where rrp_config_id in (?)", rrpConfigIds);
        }
        Map<String, RRPFlowDO> flowDOMap = rrpFlowDOS.stream()
                .collect(Collectors.toMap(e -> e.getProduct() + "@" + e.getRrpConfigId(), Function.identity(),
                        (e1, e2) -> e1));
        Set<Integer> versionChangeFlow = new HashSet<>();
        for (MissRealTimeItem missRealTimeItem : missRealTimeItems) {
            DeviceApplyDO deviceApplyDO = deviceApplyDOMap.get(missRealTimeItem.getOrderId());
            if (deviceApplyDO == null) {
                continue;
            }
            Integer fromVersionId = month2VersionIdDOMap.getOrDefault(deviceApplyDO.getPlanMonth(), 0);
            RRPFlowDO rrpFlowDO = flowDOMap.get(deviceApplyDO.getPlanProduct() + "@" + fromVersionId);
            Object needSubmitObj = extractContext(context, "needSubmit");
            boolean needSubmit = needSubmitObj != null ? (Boolean) needSubmitObj : false;
            boolean noNeedRealTime = rrpFlowDO != null
                    && DemandFlowStepEnum.getById(rrpFlowDO.getCurrentStep()).getCh().contains("拒绝")
                    && !needSubmit;
            if (fromVersionId == 0) {
                // 找不到版本存放的情况下，跳过
                continue;
            }
            String projectType = "非自研上云";
            if (businessTypeDTOS != null && !businessTypeDTOS.isEmpty()) {
                for (BusinessTypeDTO businessTypeDTO : businessTypeDTOS) {
                    if (businessTypeDTO.isZysy(deviceApplyDO.getPlanProduct(),
                            deviceApplyDO.getBusiness1(), deviceApplyDO.getBusiness2(),
                            deviceApplyDO.getBusiness3())) {
                        projectType = "自研上云";
                    }
                }
            }
            String reasonType = demandReasonConfigDOMap.getOrDefault(deviceApplyDO.getPurReason(), "系统任务");
            // 生成实时预测
            String matchRealTimeKey = deviceApplyDO.matchRealTimeKey(true, reasonType,
                    projectType);
            Product13WeekDemandRealTimeItemDO existItem = insertItemMap.get(matchRealTimeKey);
            if (existItem == null) {
                existItem = updateItemMap.get(matchRealTimeKey);
            }
            if (existItem == null) {
                // 先查下实时预测有没有自动追加的条目（有更新，无新增）
                existItem = existItemMap.get(matchRealTimeKey);
            }
            if (existItem == null) {
                existItem = Product13WeekDemandRealTimeItemDO.copy(deviceApplyDO, true,
                        reasonType, projectType);
                existItem.setErpIds(new HashSet<>());
                existItem.setCreator(missRealTimeItem.getUser());
                existItem.setUpdater(missRealTimeItem.getUser());
                existItem.setDemandNum(missRealTimeItem.getNum().longValue());
                existItem.setFromVersionId(fromVersionId.longValue());
                if (!noNeedRealTime) {
                    insertItemMap.put(matchRealTimeKey, existItem);
                }
                // 设置回id
                missRealTimeItem.setRealTimeItemDO(existItem);
                missRealTimeItem.setRealTimeId(existItem.getId());
            } else {
                // 更新数值
                if (existItem.getErpIds() == null) {
                    existItem.setErpIds(new HashSet<>());
                }
                existItem.setDemandNum(existItem.getDemandNum() + missRealTimeItem.getNum());
                missRealTimeItem.setRealTimeId(existItem.getId());
                // debug 设置回ID，重复设置保障不出错
                missRealTimeItem.setRealTimeItemDO(existItem);
                if (!noNeedRealTime) {
                    updateItemMap.put(matchRealTimeKey, existItem);
                }
            }
            if (missRealTimeItem.getErpId() != null) {
                existItem.getErpIds().add(missRealTimeItem.getErpId().longValue());
            }
            // 在版本预测也追加一条
            if (rrpFlowDO == null) {
                rrpFlowDO = new RRPFlowDO();
                rrpFlowDO.setFlowCreator(missRealTimeItem.getUser());
                rrpFlowDO.setCurrentStep(0);
                rrpFlowDO.setStartTime(missRealTimeItem.getTime());
                rrpFlowDO.setEndTime(missRealTimeItem.getTime());
                rrpFlowDO.setApprover("xingyun_admin");
                rrpFlowDO.setUpdateTime(missRealTimeItem.getTime());
                rrpFlowDO.setProduct(deviceApplyDO.getPlanProduct());
                rrpFlowDO.setRrpConfigId(fromVersionId);
                rrpFlowDO.setUpdator(missRealTimeItem.getUser());
                rrpDBHelper.insert(rrpFlowDO);
                flowDOMap.put(deviceApplyDO.getPlanProduct() + "@" + fromVersionId, rrpFlowDO);
            }
            if (!existItem.getFromVersionId().equals(fromVersionId.longValue())) {
                versionChangeFlow.add(rrpFlowDO.getId());
            }
            RRPDemandItemDO rrpDemandItemDO = RRPDemandItemDO.copy(deviceApplyDO);
            rrpDemandItemDO.setFlowId(rrpFlowDO.getId());
            rrpDemandItemDO.setProjSetName(projectType);
            rrpDemandItemDO.setUpdateTime(missRealTimeItem.getTime());
            rrpDemandItemDO.setUpdator(missRealTimeItem.getUser());
            String remark = "超预测执行，根据子单号[" + deviceApplyDO.getSubId() + "]补齐";
            rrpDemandItemDO.setAmount(missRealTimeItem.getNum());
            rrpDemandItemDO.setProductRemark(remark);
            rrpDemandItemDO.setResourceRemark(remark);
            rrpDemandItemDO.setCurplanAdjust(0);
            rrpDemandItemDO.setReasonType(reasonType);
            rrpDemandItemDO.setReason(deviceApplyDO.getPurReason());
            rrpDemandItemDO.setSource("系统补全");
            rrpDemandItemDO.setExistItem(existItem);
            versionInsertList.add(rrpDemandItemDO);
        }
        // DB操作
        // 实时预测
        if (!CollectionUtils.isEmpty(insertItemMap)) {
            demandDBHelper.insert(insertItemMap.values());
        }
        if (!CollectionUtils.isEmpty(updateItemMap)) {
            List<List<Product13WeekDemandRealTimeItemDO>> subListList = ListUtils.partition(
                    new ArrayList<>(updateItemMap.values()),
                    1000);
            for (int i = 0; i < subListList.size(); i++) {
                demand145DBHelper.update(subListList.get(i));
            }
        }
        if (!CollectionUtils.isEmpty(versionInsertList)) {
            for (RRPDemandItemDO versionInsertItem : versionInsertList) {
                versionInsertItem.setAutoAppendId(versionInsertItem.getExistItem().getId());
            }
            rrpDBHelper.insertBatchWithoutReturnId(versionInsertList);
        }
        if (!CollectionUtils.isEmpty(versionChangeFlow)) {
            for (Integer id : versionChangeFlow) {
                syncFlowStatus(id.longValue(), "xingyun_admin", null);
            }
        }
    }

    @Override
    public void syncOrderExecuted(Collection<String> orderIds, Map<String, Object> context) {
        if (!CollectionUtils.isEmpty(orderIds)) {
            try {
                // 区分Q单和R单
                List<String> qs = new ArrayList<>();
                List<String> rs = new ArrayList<>();
                for (String orderId : orderIds) {
                    if (orderId.startsWith("Q")) {
                        qs.add(orderId);
                    } else if (orderId.startsWith("R")) {
                        rs.add(orderId);
                    }
                }
                // 这里不能投递到消息队列，因为前台线程要等待结果操作完
                // 先同步Q单，再同步R单
                if (!CollectionUtils.isEmpty(qs)) {
                    innerSyncFromOrderId(qs, false, context);
                }
                if (!CollectionUtils.isEmpty(rs)) {
                    innerSyncFromOrderId(rs, true, context);
                }
            } catch (Exception e) {
                log.error("【扣预测/补预测】执行失败", e);
                rtxRobotHttpService.sendMarkdown(new MarkdownMsgBody(
                                String.format("<@jackycjchen>环境【%s】【扣预测/补预测】syncOrderExecuted方法执行失败" +
                                                "错误信息：" + e.getMessage() +
                                                "\n详细堆栈信息如下:\n" + ExceptionUtil.stacktraceToString(e),
                                        ObjectUtils.defaultIfNull(EnvUtils.getStage(), "测试"))
                        ),
                        crpPhysicalDemandRobot.get());
                throw e;
            }
        }
    }

    @Override
    @Synchronized(namespace = "REFRESH-ORDER-EXECUTED", waitLockMillisecond = 10, throwExceptionIfNotGetLock = false)
    public void scheduleRefreshCurVersionOrderExecuted(boolean addThisMonth) {
        RRPVersionDO thisMonthVersionDO = null;
        String thisMonth = null;
        if (addThisMonth) {
            thisMonth = DateUtils.format(new Date(), "yyyy-MM");
            // 这里要借助月份-版本辅助表，找到应该获取哪一个版本
            Month2VersionIdDO month2VersionIdDO = demandDBHelper.getOne(Month2VersionIdDO.class,
                    "where month = ? limit 1", thisMonth);
            if (month2VersionIdDO != null) {
                thisMonthVersionDO = rrpDBHelper.getOne(RRPVersionDO.class,
                        "where id = ? limit 1", month2VersionIdDO.getVersionId());
            }
        }
        // 定时调度，重算执行关系以及补预测
        // 先获得当前版本
        RRPVersionDO rrpVersionDO = rrpDBHelper.getOne(RRPVersionDO.class,
                "where status = 0 order by id desc limit 1");
        if (rrpVersionDO == null) {
            log.info("当前没有开放的版本，返回");
            return;
        } else {
            // 获取当前扣除的单据列表和实时预测对应的单据列表
            List<String> time = autoDeliveryDemandForecastService.buildRequiredMonths(rrpVersionDO);
            Set<String> monthSet = new HashSet<>(time);
            Set<String> orderIds = autoDeliveryDemandForecastService
                    .offer13WeekOriginAndExecutedOrderList(rrpVersionDO.getId().longValue(), null, monthSet);
            if (!CollectionUtils.isEmpty(orderIds)) {
                autoDeliveryDemandForecastService.clearSystemCompleteForecast(monthSet,
                        rrpVersionDO.getId().longValue());
                // 删除当前的扣减关系
                autoDeliveryDemandForecastService.clearOrderIdsExecutedRecords(orderIds);

                // 过滤下orderIds，有可能之前扣过预测，但是后来改了需求月份
                // 但是为啥要在这里过滤呢？因为上面确实需要删除之前的扣减关系，删除后就不用再拿着补预测了
                List<DeviceApplyDO> rawDeviceApplyDOS = shuttleDBHelper.getAll(DeviceApplyDO.class,
                        " where plan_month not in (?) and sub_id in (?)", monthSet, orderIds);
                for (DeviceApplyDO deviceApplyDO : rawDeviceApplyDOS) {
                    orderIds.remove(deviceApplyDO.getSubId());
                }

                // 同步预测
                syncOrderExecuted(orderIds, null);
            }
        }
        if (thisMonthVersionDO != null && !thisMonthVersionDO.getId().equals(rrpVersionDO.getId())) {
            // 获取当前扣除的单据列表和实时预测对应的单据列表
            Set<String> monthSet = new HashSet<>();
            monthSet.add(thisMonth);
            Set<String> orderIds = autoDeliveryDemandForecastService
                    .offer13WeekOriginAndExecutedOrderList(thisMonthVersionDO.getId().longValue(), null, monthSet);
            if (!CollectionUtils.isEmpty(orderIds)) {
                autoDeliveryDemandForecastService.clearSystemCompleteForecast(monthSet,
                        thisMonthVersionDO.getId().longValue());
                // 删除当前的扣减关系
                autoDeliveryDemandForecastService.clearOrderIdsExecutedRecords(orderIds);

                // 过滤下orderIds，有可能之前扣过预测，但是后来改了需求月份
                // 但是为啥要在这里过滤呢？因为上面确实需要删除之前的扣减关系，删除后就不用再拿着补预测了
                List<DeviceApplyDO> rawDeviceApplyDOS = shuttleDBHelper.getAll(DeviceApplyDO.class,
                        " where plan_month != ? and sub_id in (?)", thisMonth, orderIds);
                for (DeviceApplyDO deviceApplyDO : rawDeviceApplyDOS) {
                    orderIds.remove(deviceApplyDO.getSubId());
                }

                // 同步预测
                syncOrderExecuted(orderIds, null);
            }
        }
    }

    @Override
    @TaskLog(taskName = "scheduleRefreshCurVersionOrderExecuted")
    @Scheduled(cron = "0 19 0/2 * * ?")
    public void scheduleRefreshCurVersionOrderExecuted() {
        SpringUtil.getBean(RealTimeDemandService.class).scheduleRefreshCurVersionOrderExecuted(true);
    }

    @Scheduled(cron = "0 0/10 * * * ?")
    public void refreshVersionTableScheduled() {
        SpringUtil.getBean(RealTimeDemandService.class).refreshVersionTable();
    }

    @Override
    @Synchronized(waitLockMillisecond = 0, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "refreshVersionTable", timeout = 3600)
    @Transactional("demandTransactionManager")
    public void refreshVersionTable() {
        // 获取所有版本
        List<RrpConfigDO> rrpVersionDOList = rrpDBHelper.getAll(RrpConfigDO.class,
                "order by id asc");
        Map<String, RrpConfigDO> idMap = new HashMap<>();
        // 顺序执行，这样后面的版本能把最新的月份覆盖掉id
        for (RrpConfigDO rrpConfigDO : rrpVersionDOList) {
            List<String> demandMonthStrListWithAutoExtend = rrpConfigDO.demandMonthStrListWithAutoExtend();
            for (String demandMonth : demandMonthStrListWithAutoExtend) {
                idMap.put(demandMonth, rrpConfigDO);
            }
        }
        List<Month2VersionIdDO> month2VersionIdDOS = new ArrayList<>();
        for (String month : idMap.keySet()) {
            RrpConfigDO rrpConfigDO = idMap.get(month);
            if (rrpConfigDO == null) {
                continue;
            }
            Month2VersionIdDO month2VersionIdDO = new Month2VersionIdDO();
            month2VersionIdDO.setVersionId(Math.toIntExact(rrpConfigDO.getId()));
            month2VersionIdDO.setVersionName(rrpConfigDO.getPlanVersion());
            month2VersionIdDO.setMonth(month);
            month2VersionIdDOS.add(month2VersionIdDO);
        }
        demandDBHelper.delete(Month2VersionIdDO.class, "where 1 = 1");
        demandDBHelper.insertBatchWithoutReturnId(month2VersionIdDOS);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ClearRealTimeItemDTO {

        private Long versionId;
        private String planProduct;
    }

    @Data
    static class MinusGroupByItem {

        private Long realTimeId;
        private Integer sumNum;
        private String orderIdText;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderIdExecutedRelation {

        private String orderId;
        private Set<Long> nebulaIdList;
        private Map<Long, Integer> totalNumMap;
        private String user;
        private Date time;
    }

    @Data
    public static class MissRealTimeItem {

        private String orderId;
        private Integer erpId;
        private Integer num;
        private Long realTimeId;
        private String user;
        private Date time;

        private Product13WeekDemandRealTimeItemDO realTimeItemDO;

        public MissRealTimeItem(String orderId, Integer erpId, Integer num, String user, Date time) {
            this.orderId = orderId;
            this.erpId = erpId;
            this.num = num;
            this.user = user;
            this.time = time;
        }

        public Long getRealTimeId() {
            if (realTimeItemDO != null) {
                return realTimeItemDO.getId();
            } else {
                return realTimeId;
            }
        }
    }
}
