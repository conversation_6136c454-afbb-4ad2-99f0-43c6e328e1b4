package cloud.demand.app.modules.rrp_new_feature.entity;


import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("res_plan")
public class ResPlanForW13PassDO {

    /**
     * 规划id<br/>Column: [id]
     */
    @Column(value = "id")
    private Integer id;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @Column(value = "demand_type")
    private String demandType;

    /**
     * 项目类型<br/>Column: [project_type]
     */
    @Column(value = "project_type")
    private String projectType;

    @Column(value = "mod_biz_type")
    private String modBizType;

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /**
     * 需求周(周)<br/>Column: [week]
     */
    @Column(value = "week")
    private Integer week;

    /**
     * 规划产品<br/>Column: [plan_product]
     */
    @Column(value = "plan_product")
    private String planProduct;

    /**
     * Campus<br/>Column: [campus]
     */
    @Column(value = "campus")
    private String campus;

    /**
     * 设备类型<br/>Column: [device_type]
     */
    @Column(value = "device_type")
    private String deviceType;

    /**
     * 需求预测总量(汇总)<br/>Column: [demand_num]
     */
    @Column(value = "demand_num")
    private Long demandNum;

    /**
     * 需求预测已执行量<br/>Column: [executed_num]
     */
    @Column(value = "executed_num")
    private Long executedNum;

    /**
     * 需求预测未执行量<br/>Column: [non_executed_num]
     */
    @Column(value = "non_executed_num")
    private Long nonExecutedNum;

    /**
     * 国家<br/>Column: [country]
     */
    @Column(value = "country")
    private String country;

    /**
     * 城市<br/>Column: [city_name]
     */
    @Column(value = "city_name")
    private String cityName;

    /**
     * 云字段 - 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 云字段 - 客户<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    public static ResPlanForW13PassDO copy(ResPlanForW13PassDO other) {
        ResPlanForW13PassDO resPlanForW13PassDO = new ResPlanForW13PassDO();
        resPlanForW13PassDO.setId(other.getId());
        resPlanForW13PassDO.setDemandType(other.getDemandType());
        resPlanForW13PassDO.setProjectType(other.getProjectType());
        resPlanForW13PassDO.setModBizType(other.getModBizType());
        resPlanForW13PassDO.setYear(other.getYear());
        resPlanForW13PassDO.setMonth(other.getMonth());
        resPlanForW13PassDO.setWeek(other.getWeek());
        resPlanForW13PassDO.setPlanProduct(other.getPlanProduct());
        resPlanForW13PassDO.setCampus(other.getCampus());
        resPlanForW13PassDO.setDeviceType(other.getDeviceType());
        resPlanForW13PassDO.setDemandNum(other.getDemandNum());
        resPlanForW13PassDO.setExecutedNum(other.getExecutedNum());
        resPlanForW13PassDO.setNonExecutedNum(other.getNonExecutedNum());
        resPlanForW13PassDO.setCountry(other.getCountry());
        resPlanForW13PassDO.setCityName(other.getCityName());
        resPlanForW13PassDO.setIndustry(other.getIndustry());
        resPlanForW13PassDO.setCustomerName(other.getCustomerName());
        return resPlanForW13PassDO;
    }

    public String getYearMonth() {
        return year + "-" + (month < 10 ? "0" + month : month);
    }

    public String compareKey() {
        return String.join("@", getYearMonth(), getPlanProduct(),
                getCountry(), getCityName(), getCampus(), getDeviceType(),
                getIndustry(), getCustomerName(), getDemandType(), getProjectType(),
                String.valueOf(getWeek()), getModBizType());
    }
}