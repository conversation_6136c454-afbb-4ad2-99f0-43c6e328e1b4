package cloud.demand.app.modules.rrp_new_feature.entity;


import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("res_plan_strategy_demand_project_map")
public class ResPlanStrategyDemandProjectMapDO {

    /** id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 需求类型id<br/>Column: [demand_type_id] */
    @Column(value = "demand_type_id")
    private Integer demandTypeId;

    /** 需求类型名称<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 项目类型id<br/>Column: [project_type_id] */
    @Column(value = "project_type_id")
    private Integer projectTypeId;

    /** 项目类型名称<br/>Column: [project_type] */
    @Column(value = "project_type")
    private String projectType;

    /** 业务部门id<br/>Column: [dept_ids] */
    @Column(value = "dept_ids")
    private String deptIds;

    /** 业务部门名称<br/>Column: [dept_names] */
    @Column(value = "dept_names")
    private String deptNames;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Date updateTime;

    /** 更新人<br/>Column: [update_user] */
    @Column(value = "update_user")
    private String updateUser;

}
