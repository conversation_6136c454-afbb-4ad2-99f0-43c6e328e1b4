package cloud.demand.app.modules.rrp_new_feature.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("real_time_execute")
public class RealTimeExecuteDO {

    /**
     * 主键<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 子单号<br/>Column: [order_id]
     */
    @Column(value = "order_id")
    private String orderId;

    /**
     * 实时预测ID<br/>Column: [real_time_id]
     */
    @Column(value = "real_time_id")
    private Long realTimeId;

    /**
     * 执行数量<br/>Column: [execute_num]
     */
    @Column(value = "execute_num")
    private Integer executeNum;

    /**
     * 执行操作人<br/>Column: [execute_user]
     */
    @Column(value = "execute_user")
    private String executeUser;

    /**
     * 执行时间<br/>Column: [execute_time]
     */
    @Column(value = "execute_time")
    private Date executeTime;

    /**
     * 是否删除，0-否，1-是<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;
}