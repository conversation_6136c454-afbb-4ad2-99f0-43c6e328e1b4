package cloud.demand.app.modules.common.service.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("bas_cmdb_business")
public class ObsBizInfo {

    @Column("bgId")
    private Integer bgId;
    @Column("bgName")
    private String bgName;
    @Column("bgShortName")
    private String bgShortName;
    @Column("deptId")
    private Integer deptId;
    @Column("deptName")
    private String deptName;
    @Column("planProductId")
    private Integer planProductId;
    @Column("planProductName")
    private String planProductName;
    @Column("productId")
    private Integer productId;
    @Column("productName")
    private String productName;
    @Column("business1Id")
    private Integer business1Id;
    @Column("business1Name")
    private String business1Name;
    @Column("business2Id")
    private Integer business2Id;
    @Column("business2Name")
    private String business2Name;
    @Column("business3Id")
    private Integer business3Id;
    @Column("business3Name")
    private String business3Name;

}
