package cloud.demand.app.modules.common.service;

import cloud.demand.app.entity.demand.CdTaskLogDO;
import cloud.demand.app.modules.common.enums.CdTaskLogStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.net.NetUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

@Aspect
@Component
@Slf4j
@Order(0) //请勿小于 0
public class TaskLogAopConfig {

    /**  采用线程池，主线程执行业务代码，遇到监控的逻辑新开线程去执行，保证事务不共用 */
    private ExecutorService pool = Executors.newFixedThreadPool(2);

    @Resource
    private DBHelper demandDBHelper;

    @Around("@annotation(cloud.demand.app.modules.common.service.TaskLog)")
    @SneakyThrows
    public Object aroundMethod(ProceedingJoinPoint joinpoint){

        //  1. 获取注解内的TaskName
        //  获取切点的方法签名
        MethodSignature signature = (MethodSignature)joinpoint.getSignature();
        //  获取方法名称
        Method method = signature.getMethod();
        Object target = joinpoint.getTarget();
        Object[] args = joinpoint.getArgs();
        //  根据方法名 + 形参列表的类型确定唯一方法，再获取到这个方法的注解内值
        Method origin = target.getClass().getDeclaredMethod(signature.getName(), method.getParameterTypes());
        String taskName = origin.getAnnotation(TaskLog.class).taskName();
        Integer timeout = origin.getAnnotation(TaskLog.class).timeout();

        long start = System.currentTimeMillis();
        //  2. 前置处理：插入TaskLogDO对象
        Future<CdTaskLogDO> cdTaskLogDO = pool.submit(() -> generateNewOne(taskName, timeout, args));

        //  3. 调用接口方法(如果有异常，在下面的try-catch块中捕获、记录后再抛出)
        Object result;
        try {
            //  真正调用接口方法
            result = joinpoint.proceed();
        }catch (Exception e){
            //  如果catch到了异常，执行失败并获取对应的报错栈信息，回写Log对象
            StringWriter writer = new StringWriter();
            e.printStackTrace(new PrintWriter(writer, true));
            //  更新异常记录对象
            pool.submit(() -> updateFailedOne(cdTaskLogDO, writer.toString()));
            //  继续抛出异常，监控只做记录
            throw e;
        }

        //  4. 成功调用时记录执行时长，回写DO
        pool.submit(() -> updateSuccessfulOne(cdTaskLogDO, start));

        //  5. 返回调用结果，监控只做记录
        return result;
    }

    /**
     * 插入一条监控记录【隔离外界事务】
     */
//    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public CdTaskLogDO generateNewOne(String taskName, Integer timeout, Object[] args){
        CdTaskLogDO cdTaskLogDO = new CdTaskLogDO();
        cdTaskLogDO.setTaskName(taskName);
        try {
            cdTaskLogDO.setRunIp(String.join(";", NetUtils.getIpv4IPs()));
        } catch (Exception e) {
            cdTaskLogDO.setRunIp("EXCEPTION " + e.getClass().getName());
            log.error("fail to get machine ips", e);
        }
        cdTaskLogDO.setTimeout(Objects.equals(timeout, -1) ? null : timeout);
        cdTaskLogDO.setArgs(JSON.toJson(args));
        cdTaskLogDO.setStatus(CdTaskLogStatusEnum.NEW.getCode());
        cdTaskLogDO.setCostMs(0);
        String traceId = MDC.get("requestId");
        cdTaskLogDO.setTraceId(traceId == null ? "" : traceId);
        demandDBHelper.insertWithNull(cdTaskLogDO);
        return cdTaskLogDO;
    }

    /**
     * 更新执行失败的记录【隔离外界事务】
     */
//    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateFailedOne(Future<CdTaskLogDO> future, String errorMsg) {
        CdTaskLogDO one;
        try {
            one = future.get();
            one.setStatus(CdTaskLogStatusEnum.FAIL.getCode());
            one.setErrorMsg(errorMsg);
            demandDBHelper.update(one);
        } catch (Exception e) {
            log.error("【定时任务执行失败】更新失败", e);
        }
    }

    /**
     * 更新执行成功的记录【隔离外界事务】
     */
//    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateSuccessfulOne(Future<CdTaskLogDO> future, long start) {
        long end = System.currentTimeMillis();
        CdTaskLogDO one;
        try {
            one = future.get();
            one.setCostMs((int)(end - start));
            one.setStatus(CdTaskLogStatusEnum.SUCCESS.getCode());
            demandDBHelper.update(one);
        } catch (Exception e) {
            log.error("【定时任务执行成功】更新失败", e);
        }
    }

}
