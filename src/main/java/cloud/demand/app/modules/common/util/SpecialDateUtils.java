package cloud.demand.app.modules.common.util;

import com.pugwoo.wooutils.collect.ListUtils;
import org.nutz.lang.Lang;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 一些业务上的特殊日期需求工具类
 */
public class SpecialDateUtils {

    /**
     * 获取指定时间区间内每个月的最后一天
     * eg:
     *  param:start: 2023-01-01 end: 2023-04-01
     *  return: [2023-01-31, 2023-02-28, 2023-03-31]
     */
    public static List<String> getLastDateList(Date start, Date end){
        return Lang.list();
    }

    /**
     * 获取指定年月列表中最大月份的最后一天
     * @param yearMonthList [2023-01, 2023-02, 2023-03]
     * @return  2023-03-31
     */
    public static String getCurMonthLastDate(List<String> yearMonthList){
        if (ListUtils.isEmpty(yearMonthList)){
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        YearMonth maxYearMonth = yearMonthList.stream().
                map(o -> YearMonth.parse(o, formatter)).
                max(YearMonth::compareTo).
                orElseThrow(IllegalAccessError::new);
        return maxYearMonth.atEndOfMonth().toString();
    }

    /**
     * 获取指定年月的第一天
     * @param year 2023
     * @param month 5
     * @return "2023-05-01"
     */
    public static String getCurMonthFirstDate(Integer year, Integer month){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Calendar instance = Calendar.getInstance();
            instance.set(Calendar.YEAR, year);
            instance.set(Calendar.MONTH, month - 1);
            instance.set(Calendar.DAY_OF_MONTH, 1);
            return format.format(instance.getTime());

        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取指定年月的最后一天
     * @param year 2023
     * @param month 5
     * @return "2023-05-31"
     */
    public static String getCurMonthLastDate(Integer year, Integer month){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Calendar instance = Calendar.getInstance();
            instance.set(Calendar.YEAR, year);
            instance.set(Calendar.MONTH, month - 1);
            instance.set(Calendar.DAY_OF_MONTH, instance.getActualMaximum(Calendar.DAY_OF_MONTH));
            return format.format(instance.getTime());
        }catch (Exception e){
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 获取指定两个LocalDate时间范围内的所有年份
     */
    public static List<Integer> getTimeIntervalYears(LocalDate begin, LocalDate end){
        List<Integer> result = Lang.list();
        if (begin == null || end == null){
            return result;
        } else {
            int beginYear = begin.getYear();
            int endYear = end.getYear();
            for (int year = beginYear; year <= endYear; year++){
                result.add(year);
            }
        }
        return result;
    }


}
