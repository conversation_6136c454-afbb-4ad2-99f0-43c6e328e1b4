package cloud.demand.app.modules.common.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum GPUCardTypeEnum {

    REASONING("推理"),
    TRAINING("训练"),
    CONSUMPTION("消费"),
    RENDERING("渲染"),
    OTHER("其他");

    private final String code;

    GPUCardTypeEnum(String code) {
        this.code = code;
    }

    public static GPUCardTypeEnum getByCode(String code) {
        for (GPUCardTypeEnum e : GPUCardTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }
}
