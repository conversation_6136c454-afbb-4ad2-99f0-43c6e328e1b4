package cloud.demand.app.modules.common.enums;

import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import lombok.Getter;

/**
 * CDB指标的枚举对象
 */
@Getter
public enum IndicatorCDBEnum {

    //  库存
    INVENTORY_ALL("Q", "库存汇总", "存", "库存汇总", "Q=O+P", 310),
    //  线上库存
    INVENTORY_TOTAL("O", "线上库存汇总", "存", "线上库存汇总", "O=e+g", 311),
    INVENTORY_ONLINE_GOOD("e", "线上好料", "存", "线上库存", "", 312),
    INVENTORY_ONLINE_UGLY("g", "线上呆料", "存", "线上库存", "", 313),
    //  线下库存
    INVENTORY_OFFLINE("P", "线下库存", "存", "线下库存", "", 314),

    // 销售
    //  总售卖
    SALE_ALL("K", "销汇总", "销", "销汇总", "K=a+c", 210),
    SALE_EXTERNAL("a", "外部计费规模", "销", "外部计费规模", "", 211),
    SALE_INTERNAL("c", "内部申领规模", "销", "内部申领规模", "", 212),


    //  其他
    OTHER_PDD_REDUNDANCY("f1", "PDD冗余", "其他", "PDD冗余", "", 410),
    OTHER_EXC_REDUNDANCY("f2", "独享冗余", "其他", "独享冗余", "", 411),
    OTHER_MACHINE_REDUNDANCY("f3", "机型冗余", "其他", "机型冗余", "", 412),
    OTHER_TOTAL_CAP("tm", "总容量", "其他", "总容量", "tm=kc+L", 413),
    OTHER_STORAGE("kc", "存储", "其他", "存储", "", 414),
    OTHER_OTHER("L", "其他设备", "其他", "其他设备", "L=l1+l2+l3", 415),
    OTHER_STANDBY("l1", "备机", "其他", "备机", "", 416),
    OTHER_SUPPORT("l2", "支撑", "其他", "支撑", "", 417),
    OTHER_TEST("l3", "测试", "其他", "测试", "", 418),
    OTHER_NOT_FOR_SALE("NS", "非可售", "其他", "非可售", "NS=tm/2-K-Q", 419),
    OTHER_P2P_RATE("P2PRATE", "端到端利用率(可售)", "其他", "端到端利用率(可售)", "P2PRATE=K/(K+Q)", 420),
    OTHER_P2P_RATE_SA("P2PRATE_SA", "端到端利用率(软件架构)", "其他", "端到端利用率(软件架构)", "P2PRATE_SA=K/(K+Q+f1+f2+f3)", 421);


    /** 指标代号 */
    private final String code;
    /** 指标名称 */
    private final String name;
    /** 指标大类 */
    private final String category;
    /** 指标子类 */
    private final String subCategory;
    /** 指标计算公式 */
    private final String formula;
    /** 指标序号
     * 这里认为每个产品均可分为4大类，进、销、存、其他
     * 编号进：1开头、销 2开头、存 3开头、其他：4开头
     * 每个大类都留一定的冗余空间（即从x10）开始给公共特殊指标，如：物理资源规模
     */
    private final Integer seq;

    IndicatorCDBEnum(String code, String name, String category, String subCategory, String formula, Integer seq){
        this.code = code;
        this.name = name;
        this.category = category;
        this.subCategory = subCategory;
        this.formula = formula;
        this.seq = seq;
    }

    /**
     * 将IndicatorEnum转成ReportCvmJxcDO指标对象,并填充可以直接获取的字段
     */
    public ReportCvmJxcDO toReportCvmJxcDO(){
        ReportCvmJxcDO jxcDO = new ReportCvmJxcDO();
        jxcDO.setIndicatorCode(this.getCode());
        jxcDO.setIndicatorName(this.getName());
        jxcDO.setCategory(this.getCategory());
        jxcDO.setSubCategory(this.getSubCategory());
        jxcDO.setIndicatorFormula(this.getFormula());
        jxcDO.setProductType(ProductTypeEnum.CDB.getCode());
        jxcDO.setSeq(this.getSeq());
        return jxcDO;
    }

}
