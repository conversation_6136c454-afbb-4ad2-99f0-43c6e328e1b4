package cloud.demand.app.modules.common.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum CrpEventEnum {

    /**
     * 统一大版本事件
     */
    VERSION_START_TIME("version_start_time", "版本开启时间"),

    PPL_VERSION_START_TIME("ppl_version_start_time", "PPL13周版本开启时间"),
    PRODUCT_VERSION_START_TIME("product_version_start_time", "产品13周版本开启时间"),
    YEAR_VERSION_START_TIME("year_version_start_time", "全年版本开启时间"),

    PPL_ENTER_DEADLINE("ppl_enter_deadline", "行业PPL截止录入时间"),
    STOCK_SUPPLY_DEADLINE("stock_supply_deadline", "行业PPL下发对冲时间"),

    CVM_STOCK_SUPPLY_DONE("cvm_stock_supply_done", "cvm返回对冲结果"),

    STOCK_SUPPLY_DONE("stock_supply_done", "对冲完成"),
//    PHYSICAL_ENTET_DEADLINE("physical_enter_deadline", "产品物理机预测截止录入时间"),
//    PHYSICAL_PASS_DEADLINE("physical_pass_deadline", "产品物理机传递时间"),
//    YEAR_DEMAND_ENTET_DEADLINE("year_demand_enter_deadline", "产品全年截止录入时间"),
//    YEAR_DEMAND_PASS_DEADLINE("year_demand_pass_deadline", "产品全年传递时间"),
    VERSION_CLOSE_TIME("version_close_time", "版本关闭时间"),
    CHECK_HOLIDAY_VERSION("check_holiday_version", "检查节假日配置"),
    DATA_CHECK_STOCK_SUPPLY_DONE("data_check_stock_supply_done", "行业PPL传递-数据对账"),
//    DATA_CHECK_PHYSICAL_PASS("data_check_physical_pass", "物理机需求传递-数据对账"),
//    DATA_CHECK_YEAR_PASS("data_check_year_pass", "全年需求传递-数据对账"),

    /* 物理机预测录入版本事件 beg */
    DEVICE_DEMAND_VERSION_CREATE("DEVICE_DEMAND_VERSION_CREATE", "物理机需求预测版本创建"),
    DEVICE_DEMAND_VERSION_START("DEVICE_DEMAND_VERSION_START", "物理机需求预测版本开启"),
    DEVICE_DEMAND_VERSION_ENTER_DEADLINE("DEVICE_DEMAND_VERSION_ENTER_DEADLINE", "物理机需求预测版本截止录入"),
    DEVICE_DEMAND_VERSION_APPROVE_DEADLINE("DEVICE_DEMAND_VERSION_APPROVE_DEADLINE", "物理机需求预测版本截止审批"),
    DEVICE_DEMAND_VERSION_REVIEW_DEADLINE("DEVICE_DEMAND_VERSION_REVIEW_DEADLINE", "物理机需求预测版本会审结束"),
    DEVICE_DEMAND_VERSION_PASS_DEADLINE("DEVICE_DEMAND_VERSION_PASS_DEADLINE", "物理机需求预测版本生效"),
    DEVICE_DEMAND_VERSION_CLOSE_TIME("DEVICE_DEMAND_VERSION_CLOSE_TIME", "物理机需求预测版本关闭"),
    /* 物理机预测录入版本事件 end */


    /**
     * 行业PPL通知事件
     */

    REPEAT_APPLIED_ORDER("repeat_applied_order", "预测预约重匹配"),
    SUBMIT_USER_WAIT_COMMIT_NOTICE("submit_user_wait_commit_notice", "架构师待提交通知"),

    INNER_PROCESS_WAIT_AUDIT_NOTICE("inner_process_wait_audit_notice", "行业内部PPL待审批通知(定时任务)"),

    PRE_SUBMIT_PPL_REFUSE("pre_submit_ppl_refuse", "需求沟通环节 PPL被打回"),

    INNER_PROCESS_AUDIT_PPL_REFUSE("inner_process_audit_ppl_refuse", "审批环节 PPL单被拒绝通知"),

    INNER_PROCESS_AUDIT_PPL_MODIFY("inner_process_audit_ppl_modify", "审批环节 PPL明细被调整通知"),

    INNER_PROCESS_ENTER_WAIT_AUDIT_NOTICE("inner_process_enter_wait_audit_notice",
            "行业内部PPL待审批通知(进入审批节点)"),

    INNER_PROCESS_REFUSE_CONFIRM("inner_process_refuse_confirm", "待办中驳回，驳回二次确认通知"),

    INNER_PROCESS_VERSION_FINISH("inner_process_version_finish", "版本完结通知"),

    /**
     * 订单通知事件⌚️
     */
    order_submit("order_submit", "订单提交通知"),

    /**
     * 数据库订单通知事件⌚️
     */
    order_submit_database("order_submit_database", "数据库订单提交通知"),

    order_audit_wait("order_audit_wait", "订单待审批通知"),

    order_audit_pass("order_audit_pass", "订单审批通过(云运管)"),

    order_audit_reject("order_audit_reject", "订单审批驳回"),

    supply_way_done("supply_way_done", "满足方式制定完成"),

    wait_consensus("wait_consensus", "订单待共识通知"),

    consensus_pass("consensus_pass", "订单共识通过"),

    consensus_reject("consensus_reject", "订单共识拒绝"),

    order_into_following("order_into_following", "订单进入履约跟踪"),

    order_close("order_close", "订单关闭"),

    order_modify_wait("order_modify_wait", "订单修改待审批"),

    order_modify_pass("order_modify_pass", "订单修改审批通过"),

    order_modify_reject("order_modify_reject", "订单修改审批驳回"),

    order_cancel_wait("order_cancel_wait", "订单取消待审批"),

    order_cancel_pass("order_cancel_pass", "订单取消审批通过"),

    order_cancel_reject("order_cancel_reject", "订单取消审批驳回"),

    order_urgent_delay_wait_approval("order_urgent_delay_wait_approval", "订单加急延期待审批"),

    order_urgent_delay_approval_pass("order_urgent_delay_approval_pass", "订单加急延期审批通过"),

    order_urgent_delay_approval_refuse("inner_process_version_finish", "订单加急延期审批驳回"),

    order_withdraw("order_withdraw", "订单撤回"),

    order_pre_deduct_wait("order_pre_deduct_wait", "订单预扣待审批"),

    order_pre_deduct_reject("order_pre_deduct_reject", "预扣审批驳回"),

    order_pre_deduct_cancel("order_pre_deduct_cancel", "订单预扣取消"),

    order_pre_deduct_start("order_pre_deduct_start", "订单开始预扣"),

    order_pre_deduct_deadline("order_pre_deduct_deadline", "预扣即将到期"),

    order_pre_deduct_delay_fail("order_pre_deduct_delay_fail", "预扣续期失败"),

    order_supply_plan_wait_create_new_order("order_supply_plan_wait_create_new_order", "订单供应方案待制定（新订单）"),

    order_supply_plan_wait_create_new_order_database("order_supply_plan_wait_create_new_order_database",
            "订单供应方案待制定（新数据库订单）"),

    order_supply_plan_wait_create_over_time_database("order_supply_plan_wait_create_over_time_database",
            "数据库订单供应方案制定超时"),

    order_supply_plan_wait_create_order_modify("order_supply_plan_wait_create_order_modify", "订单供应方案待制定（修改订单）"),

    order_yun_xiao_pre_deduct_success("order_yun_xiao_pre_deduct_success", "预扣成功"),

    order_yun_xiao_pre_deduct_part_success("order_yun_xiao_pre_deduct_part_success", "预扣部分成功"),

    order_yun_xiao_pre_deduct_fail("order_yun_xiao_pre_deduct_fail", "预扣失败"),

    order_yun_xiao_pre_deduct_destroy("order_yun_xiao_pre_deduct_destroy", "预扣销毁"),

    order_audit_over_time("order_audit_over_time", "订单审批超时"),

    order_satisfy_alert_industry("order_satisfy_alert_industry", "订单满足缺口预警-行业级"),

    order_satisfy_alert_global("order_satisfy_alert_global", "订单满足缺口预警-全局"),


    //   中长期版本相关事件 ⬇️
    long_term_version_start_time("long_term_version_start_time", "中长期版本开始时间"),

    long_term_not_submit("long_term_not_submit", "中长期预测未提交"),

    long_term_reject_notice("long_term_reject_notice", "中长期驳回通知"),

    long_term_audit_deadline("long_term_audit_deadline", "行业GM审批截止时间"),

    long_term_version_end_time("long_term_version_end_time", "中长期版本结束时间"),

    //   中长期版本相关事件 ⬆️

    ;


    private final String code;
    private final String name;

    CrpEventEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static CrpEventEnum getByCode(String code) {
        for (CrpEventEnum e : CrpEventEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static CrpEventEnum getByName(String name) {
        for (CrpEventEnum e : CrpEventEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        CrpEventEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getCodeByName(String name) {
        CrpEventEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }

}
