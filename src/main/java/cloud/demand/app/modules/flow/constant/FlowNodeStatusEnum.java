package cloud.demand.app.modules.flow.constant;

import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;

/**
 *  流程节点状态
 * @see FlowNodeRecordDO#getNodeStatus()
 */
public enum FlowNodeStatusEnum {

    PROCESSING("processing","进行中"),

    CHANGING("changing","流转中"),

    DONE("done","已完成");

    private final String code;

    private final String name;

    FlowNodeStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
