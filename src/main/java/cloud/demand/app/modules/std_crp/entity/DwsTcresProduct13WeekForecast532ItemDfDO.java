package cloud.demand.app.modules.std_crp.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("dws_tcres_product_13_week_forecast_532_item_df")
public class DwsTcresProduct13WeekForecast532ItemDfDO {

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 类似于 MYSQL 的自增 ID，靠内存写入，方便下游 dws 宽表分页拉取数据<br/>Column: [id] */
    @Column(value = "id")
    private Long id;

    /** 规划产品名<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** obs项目类型<br/>Column: [obs_project_type] */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /** module业务类型名<br/>Column: [module_business_type_name] */
    @Column(value = "module_business_type_name")
    private String moduleBusinessTypeName;

    /** 境内外<br/>Column: [domestic] */
    @Column(value = "domestic")
    private String domestic;

    /** 物理机Region名<br/>Column: [cmdb_region_name] */
    @Column(value = "cmdb_region_name")
    private String cmdbRegionName;

    /** 物理机Zone名<br/>Column: [cmdb_zone_name] */
    @Column(value = "cmdb_zone_name")
    private String cmdbZoneName;

    /** 物理机campus名<br/>Column: [cmdb_campus_name] */
    @Column(value = "cmdb_campus_name")
    private String cmdbCampusName;

    /** 物理机设备类型名<br/>Column: [device_type_name] */
    @Column(value = "device_type_name")
    private String deviceTypeName;

    /** 需求类型<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 项目类型<br/>Column: [project_type] */
    @Column(value = "project_type")
    private String projectType;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 客户名<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 需求原因，来自字典<br/>Column: [reason_type] */
    @Column(value = "reason_type")
    private String reasonType;

    /** 需求原因细项，来自字典<br/>Column: [reason] */
    @Column(value = "reason")
    private String reason;

    /** 产品需求备注，来自人为填写<br/>Column: [product_demand_remark] */
    @Column(value = "product_demand_remark")
    private String productDemandRemark;

    /** 云运管资源组需求备注，来自人为填写<br/>Column: [comd_demand_remark] */
    @Column(value = "comd_demand_remark")
    private String comdDemandRemark;

    /** 需求年月<br/>Column: [demand_year_month] */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /** 需求周次<br/>Column: [week] */
    @Column(value = "week")
    private Integer week;

    /** 需求年份<br/>Column: [demand_year] */
    @Column(value = "demand_year")
    private Integer demandYear;

    /** 需求年份<br/>Column: [demand_month] */
    @Column(value = "demand_month")
    private Integer demandMonth;

    /** 产品需求数量（台），原始预测值<br/>Column: [original_product_demand_num] */
    @Column(value = "original_product_demand_num")
    private Integer originalProductDemandNum;

    /** 产品需求数量（核心），原始预测值<br/>Column: [original_product_demand_core_num] */
    @Column(value = "original_product_demand_core_num")
    private Integer originalProductDemandCoreNum;

    /** 云运管资源组需求数量（台），原始预测值<br/>Column: [original_comd_demand_num] */
    @Column(value = "original_comd_demand_num")
    private Integer originalComdDemandNum;

    /** 云运管资源组需求数量（核心），原始预测值<br/>Column: [original_comd_demand_core_num] */
    @Column(value = "original_comd_demand_core_num")
    private Integer originalComdDemandCoreNum;

    /** 总计需求数量（台），原始预测值<br/>Column: [original_total_demand_num] */
    @Column(value = "original_total_demand_num")
    private Integer originalTotalDemandNum;

    /** 总计需求数量（核心），原始预测值<br/>Column: [original_total_demand_core_num] */
    @Column(value = "original_total_demand_core_num")
    private Integer originalTotalDemandCoreNum;

    /** 是否是系统补全预测，0-否，1-是<br/>Column: [is_system_auto_append] */
    @Column(value = "is_system_auto_append")
    private Integer isSystemAutoAppend;

    /** 若是系统补全的预测，触发补预测的单据的提单日期，从子单号提取日期（QyyyyMMdd）<br/>Column: [order_date] */
    @Column(value = "order_date")
    private LocalDate orderDate;

    /** 若是系统补全的预测，触发补预测的单据号码<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 需求来源：产品录入、系统补全、行业PPL<br/>Column: [source] */
    @Column(value = "source")
    private String source;

    /** 实际归属13周产品预测版本号的ID<br/>Column: [real_belong_version_id] */
    @Column(value = "real_belong_version_id")
    private Integer realBelongVersionId;

    /** 实际归属13周产品预测版本号<br/>Column: [real_belong_version] */
    @Column(value = "real_belong_version")
    private String realBelongVersion;

    /** 实际归属13周产品预测版本号的版本年份<br/>Column: [real_belong_version_year] */
    @Column(value = "real_belong_version_year")
    private Integer realBelongVersionYear;

    /** 实际归属13周产品预测版本号的版本月份<br/>Column: [real_belong_version_month] */
    @Column(value = "real_belong_version_month")
    private Integer realBelongVersionMonth;

    /** 实际归属13周产品预测流程的ID<br/>Column: [real_belong_demand_flow_id] */
    @Column(value = "real_belong_demand_flow_id")
    private Integer realBelongDemandFlowId;

    /** 实际归属13周产品预测流程的步骤ID<br/>Column: [real_belong_demand_flow_step_id] */
    @Column(value = "real_belong_demand_flow_step_id")
    private Integer realBelongDemandFlowStepId;

    /** 实际归属13周产品预测流程的步骤名<br/>Column: [real_belong_demand_flow_step_name] */
    @Column(value = "real_belong_demand_flow_step_name")
    private String realBelongDemandFlowStepName;

    /** 实际关联13周行业预测版本号（PPL版本号）<br/>Column: [real_relate_13_week_industry_version] */
    @Column(value = "real_relate_13_week_industry_version")
    private String realRelate13WeekIndustryVersion;

    /** 原始底表的字段，暂不清楚含义，近期也不使用，先清洗保存过来<br/>Column: [assigned_product_not_used] */
    @Column(value = "assigned_product_not_used")
    private String assignedProductNotUsed;

    /** 月度权值。若版本月为2月，则预测月为5月时，月度权值为0.5；预测月为4月时，月度权值为0.3；预测月为3月时，月度权值为0.2<br/>Column: [month_weight] */
    @Column(value = "month_weight")
    private BigDecimal monthWeight;

    /** 月度内版本次数权值。计算方式为 1 / 月度内的版本次数<br/>Column: [month_version_count_weight] */
    @Column(value = "month_version_count_weight")
    private BigDecimal monthVersionCountWeight;

    /** 产品需求数量（台），原始预测值*月度权重*月度内版本次数权值<br/>Column: [product_demand_num] */
    @Column(value = "product_demand_num")
    private BigDecimal productDemandNum;

    /** 产品需求数量（核心），原始预测值*月度权重*月度内版本次数权值<br/>Column: [product_demand_core_num] */
    @Column(value = "product_demand_core_num")
    private BigDecimal productDemandCoreNum;

    /** 云运管资源组需求数量（台），原始预测值*月度权重*月度内版本次数权值<br/>Column: [comd_demand_num] */
    @Column(value = "comd_demand_num")
    private BigDecimal comdDemandNum;

    /** 云运管资源组需求数量（核心），原始预测值*月度权重*月度内版本次数权值<br/>Column: [comd_demand_core_num] */
    @Column(value = "comd_demand_core_num")
    private BigDecimal comdDemandCoreNum;

    /** 总计需求数量（台），原始预测值*月度权重*月度内版本次数权值<br/>Column: [total_demand_num] */
    @Column(value = "total_demand_num")
    private BigDecimal totalDemandNum;

    /** 总计需求数量（核心），原始预测值*月度权重*月度内版本次数权值<br/>Column: [total_demand_core_num] */
    @Column(value = "total_demand_core_num")
    private BigDecimal totalDemandCoreNum;

}