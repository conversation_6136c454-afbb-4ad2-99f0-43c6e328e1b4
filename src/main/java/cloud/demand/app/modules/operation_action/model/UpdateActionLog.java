package cloud.demand.app.modules.operation_action.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateActionLog {

    @NotNull
    private Integer id;

    /**
     * 运营动作<br/>Column: [op_action]
     */
    private String opAction;

    /**
     * 运营动作关联的单号<br/>Column: [op_order]
     */
    private String opOrder;

    /**
     * 运营单据创建时间<br/>Column: [op_order_date]
     */
    private String opOrderDate;

    /**
     * 单据状态<br/>Column: [op_order_status]
     */
    private String opOrderStatus;

    /**
     * 物理机型<br/>Column: [op_device_type]
     */
    private String opDeviceType;

    /**
     * 台数<br/>Column: [op_device_num]
     */
    private BigDecimal opDeviceNum;

    /**
     * 核心数<br/>Column: [op_device_core]
     */
    private BigDecimal opDeviceCore;

    /**
     * 期望交付时间（用户时间）<br/>Column: [op_expect_date]
     */
    private LocalDate opExpectDate;

    /**
     * 预计交付时间（erp反馈时间）<br/>Column: [op_sla_date]
     */
    private String opSlaDate;

    /**
     * 实际完成时间（取最后完成时间）<br/>Column: [op_finish_date]
     */
    private LocalDate opFinishDate;

    /**
     * 备注<br/>Column: [op_mark]
     */
    private String opMark;

    /**
     * 备注<br/>Column: [op_goal]
     */
    private String opGoal;
}
