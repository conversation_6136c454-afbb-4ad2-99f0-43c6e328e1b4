package cloud.demand.app.modules.operation_action.service;

import cloud.demand.app.modules.operation_action.entity.InventoryOperationDO;
import cloud.demand.app.modules.operation_action.entity.InventoryReasonDO;
import cloud.demand.app.modules.operation_action.model.*;

import java.util.List;

public interface OperationService {

    /**
     * 查看当前库存视图
     *
     * @param req
     * @return
     */
    List<InventoryDTO> listInventory(FilterReq req);

    /**
     * 查看运营日志
     *
     * @param queryLog
     * @return
     */
    List<InventoryOperationDO> listActionLog(QueryLog queryLog);

    /**
     * 删除运营动作
     */
    void deleteLog(DeleteLogReq req);

    /**
     * 记录运营动作
     */
    void addLog(AddActionLogReq req);

    /**
     * 更新运营动作
     */
    void updateLog(UpdateActionLog req);

    /**
     * 获取具体的运营记录
     *
     * @param id
     * @return
     */
    InventoryOperationDO getActionById(Integer id);

    /**
     * 查询erp单据状态
     *
     * @param req
     * @return
     */
    OrderResp queryErpOrder(OrderReq req);

    /**
     * 查询退回单列表，一个退回单可能有多条
     * @param req
     * @return
     */
    List<ReturnOrderResp> queryReturnOrderList(ReturnOrderReq req);

    /**
     * 添加原因分析
     * @param req
     */
    void addReason(AddReasonReq req);

    /**
     * 更新原因分析
     * @param req
     */
    void updateReason(UpdateReasonReq req);

    /**
     * 删除原因分析
     * @param req
     */
    void deleteReason(DeleteReasonReq req);

    /**
     * 查询原因详情
     * @param req
     * @return
     */
    InventoryReasonDO queryReason(QueryReasonReq req);

    /**
     * 查询原因分类列表
     * @return
     */
    List<String> queryReasonTypes();


    List<InventoryReasonDO> listReason(QueryLog req);
}
