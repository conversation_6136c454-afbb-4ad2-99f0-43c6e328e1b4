package cloud.demand.app.modules.operation_action.model;

import java.math.BigDecimal;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AddActionLogReq {

    /**
     * 可用区<br/>Column: [zone_name]
     */
    private String zoneName;

    /**
     * 机型<br/>Column: [instance_type]
     */
    private String instanceType;

    /**
     * 实际库存a<br/>Column: [act_inv]
     */
    private BigDecimal actInv;

    /**
     * 客户预留b<br/>Column: [reserve_inv]
     */
    private BigDecimal reserveInv;

    /**
     * 空闲库存c=a+b<br/>Column: [idle_inv]
     */
    private BigDecimal idleInv;
    /**
     * 弹性备货额e<br/>Column: [scale_buffer_inv]
     */
    private BigDecimal scaleBufferInv;

    /**
     * 包年包月安全库存f<br/>Column: [sub_safe_inv]
     */
    private BigDecimal subSafeInv;

    /**
     * 包年包月安全库存f-头部<br/>Column: [sub_safe_top_inv]
     */
    private BigDecimal subSafeTopInv;

    /**
     * 包年包月安全库存f-中长尾<br/>Column: [sub_safe_tail_inv]
     */
    private BigDecimal subSafeTailInv;

    /**
     * 安全库存d=e+f<br/>Column: [safe_inv]
     */
    private BigDecimal safeInv;

    /**
     * 缺口g=d-c<br/>Column: [gap_inv]
     */
    private BigDecimal gapInv;

    /**
     * 未来4周需求总量w=x+y<br/>Column: [demand_4w]
     */
    private BigDecimal demand4w;

    /**
     * 未来4周包年包月需求-头部x1<br/>Column: [sub_demand_4w_top]
     */
    private BigDecimal subDemand4wTop;

    /**
     * 未来4周包年包月需求-尾部x2<br/>Column: [sub_demand_4w_tail]
     */
    private BigDecimal subDemand4wTail;

    /**
     * 未来4周弹性需求y<br/>Column: [scale_demand_4w]
     */
    private BigDecimal scaleDemand4w;


    private LocalDate actionDate;
    /**
     * 库存时间
     */
    @NotNull
    private LocalDate invDate;
    /**
     * 运营动作<br/>Column: [op_action]
     */
    @NotNull
    private String opAction;

    /**
     * 运营动作关联的单号<br/>Column: [op_order]
     */
    private String opOrder;

    /**
     * 运营单据创建时间<br/>Column: [op_order_date]
     */
    private String opOrderDate;

    /**
     * 单据状态<br/>Column: [op_order_status]
     */
    private String opOrderStatus;

    /**
     * 物理机型<br/>Column: [op_device_type]
     */
    private String opDeviceType;

    /**
     * 台数<br/>Column: [op_device_num]
     */
    private BigDecimal opDeviceNum;

    /**
     * 核心数<br/>Column: [op_device_core]
     */
    private BigDecimal opDeviceCore;

    /**
     * 期望交付时间（用户时间）<br/>Column: [op_expect_date]
     */
    private LocalDate opExpectDate;

    /**
     * 预计交付时间（erp反馈时间）<br/>Column: [op_sla_date]
     */
    private String opSlaDate;

    /**
     * 实际完成时间（取最后完成时间）<br/>Column: [op_finish_date]
     */
    private LocalDate opFinishDate;

    /**
     * 备注<br/>Column: [op_mark]
     */
    private String opMark;

    /**
     * 运营目标<br/>Column: [op_goal]
     */
    private String opGoal;
}
