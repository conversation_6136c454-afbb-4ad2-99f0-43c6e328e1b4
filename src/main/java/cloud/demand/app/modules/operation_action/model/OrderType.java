package cloud.demand.app.modules.operation_action.model;

public enum OrderType {
    PURCHASE("产品采购"),
    PURCHASE_DELAY("采购延期"),
    PURCHASE_CANCEL("采购取消"),
    MOVE("产品搬迁"),
    MOVE_CANCEL("搬迁取消"),
    RETURN("产品退回"),
    <PERSON>("产品腾挪"),
    Unknown("未知类型");
    String type;

    OrderType(String type) {
        this.type = type;
    }

    public static OrderType get(String val) {
        for (OrderType type : values()) {
            if (type.type.equals(val)) {
                return type;
            }
        }
        return OrderType.Unknown;
    }
}
