package cloud.demand.app.modules.order_report.service.impl;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.mrpv2.enums.GenerationEnum;
import cloud.demand.app.modules.mrpv3.model.clean.ICleanGenerationInstanceType;
import cloud.demand.app.modules.mrpv3.model.clean.ICleanInstanceFamily;
import cloud.demand.app.modules.mrpv3.model.clean.ICleanPurchaseInstanceType;
import cloud.demand.app.modules.mrpv3.model.clean.ICleanUinType;
import cloud.demand.app.modules.mrpv3.service.impl.MrpV3CleanServiceImpl;
import cloud.demand.app.modules.order_report.entity.dict.BasAdvanceWeekScoreDO;
import cloud.demand.app.modules.order_report.entity.dict.BasBillTypeScoreDO;
import cloud.demand.app.modules.order_report.entity.dict.BasHistoricalFulfillmentLevelDO;
import cloud.demand.app.modules.order_report.enums.score.AdvanceWeekEnum;
import cloud.demand.app.modules.order_report.enums.score.HistoricalFulfillmentLevelEnum;
import cloud.demand.app.modules.order_report.enums.score.OrderBillTypeEnum;
import cloud.demand.app.modules.order_report.model.clean.IBillTypeClean;
import cloud.demand.app.modules.order_report.model.score.*;
import cloud.demand.app.modules.order_report.service.BasAdvanceWeekScoreService;
import cloud.demand.app.modules.order_report.service.BasBillTypeScoreService;
import cloud.demand.app.modules.order_report.service.BasHistoricalFulfillmentLevelService;
import cloud.demand.app.modules.order_report.service.OrderDataCleanService;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.FlagType;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/** 订单评分清洗 */
@Service
public class OrderDataCleanServiceImpl implements OrderDataCleanService {

    /** 机型清洗 */
    @Resource
    private MrpV3CleanServiceImpl mrpV3CleanService;

    /** 计费类型得分字典 */
    @Resource
    private BasBillTypeScoreService billTypeScoreService;
    /** 提前周得分字典 */
    @Resource
    private BasAdvanceWeekScoreService advanceWeekScoreService;

    @Resource
    private BasHistoricalFulfillmentLevelService historicalFulfillmentLevelService;

    @Resource
    private SoeCleanService cleanService;

    @Resource
    private SoeCommonService commonService;

    @Override
    public void clean(Object obj) {
        if (obj instanceof IBillTypeClean){
            cleanBillType((IBillTypeClean) obj);
        }
        if (obj instanceof IRegionClean){
            cleanService.cleanRegion((IRegionClean) obj);
        }
        if (obj instanceof ICleanInstanceFamily){
            cleanService.cleanInstanceFamily((ICleanInstanceFamily) obj);
        }
        if (obj instanceof ICleanGenerationInstanceType){
            cleanGenerationInstanceType((ICleanGenerationInstanceType)obj);
        }
        if (obj instanceof ICleanPurchaseInstanceType){
            cleanPurchaseInstanceType((ICleanPurchaseInstanceType)obj);
        }
    }


    /**
     * 清洗采购机型
     * @param obj
     */
    @Override
    public void cleanPurchaseInstanceType(ICleanPurchaseInstanceType obj) {
        Set<String> purchaseInstanceType = mrpV3CleanService.getPurchaseInstanceType();
        String instanceType = obj.getInstanceType();
        boolean contains = purchaseInstanceType.contains(instanceType);
        obj.setPurchaseInstanceType(FlagType.getDesc(contains));
    }

    /**
     * 清洗新旧机型
     * @param obj
     */
    @Override
    public void cleanGenerationInstanceType(ICleanGenerationInstanceType obj) {
        Set<String> generationInstanceType = mrpV3CleanService.getGenerationInstanceType();
        String instanceType = obj.getInstanceType();
        boolean contains = generationInstanceType.contains(instanceType);
        obj.setGenerationInstanceType(GenerationEnum.getByBol(contains));
    }

    @Override
    public void cleanUinType(ICleanUinType obj) {
        String customerUin = obj.getCustomerUin();
        Set<String> allInnerUinType = commonService.getAllInnerUinType();
        if (allInnerUinType.contains(customerUin)){
            obj.setUinType(0);
        }else {
            obj.setUinType(1);
        }
    }

    private static final ThreadLocal<Map<String,String>> billTypeMapLocal = new ThreadLocal<>();

    public Map<String,String> getBillTypeMap(){
        Map<String, String> map = billTypeMapLocal.get();
        if (map == null){
            map = new HashMap<>();
            for (OrderBillTypeEnum value : OrderBillTypeEnum.values()) {
                map.put(value.getCode(),value.getName());
                map.put(value.getName(),value.getName());
            }
            billTypeMapLocal.set(map);
        }
        return map;

    }

    private void cleanBillType(IBillTypeClean obj) {
        String billType = obj.getBillType();
        Map<String, String> billTypeMap = getBillTypeMap();
        String billTypeName = billTypeMap.get(billType);
        if (billTypeName == null && EnvUtils.isProduction()){
            throw new ITException("计费类型字典中不存在计费类型：" + billType);
        }
        obj.setBillType(billTypeName);
    }

    /** 计费得分 */
    private static final ThreadLocal<Map<String,Integer>> billTypeScoreMap = new ThreadLocal<>();

    public Map<String,Integer> getBillTypeScoreMap(){
        Map<String, Integer> ret = billTypeScoreMap.get();
        if (ret == null){
            List<BasBillTypeScoreDO> list = billTypeScoreService.getList();
            ret = ListUtils.toMap(list,BasBillTypeScoreDO::getBillType,BasBillTypeScoreDO::getScore);
            billTypeScoreMap.set(ret);
        }
        return ret;
    }

    /** 提前周得分 */
    private static final ThreadLocal<Map<String,Integer>> advanceWeekScoreMap = new ThreadLocal<>();

    public Map<String,Integer> getAdvanceWeekScoreMap(){
        Map<String, Integer> ret = advanceWeekScoreMap.get();
        if (ret == null){
            List<BasAdvanceWeekScoreDO> list = advanceWeekScoreService.getList();
            ret = ListUtils.toMap(list,BasAdvanceWeekScoreDO::getAdvanceWeek,BasAdvanceWeekScoreDO::getScore);
            advanceWeekScoreMap.set(ret);
        }
        return ret;
    }

    /** 历史履约得分 */
    private static final ThreadLocal<Map<String,Integer>> historicalFulfillmentScoreMap = new ThreadLocal<>();

    public Map<String,Integer> getHistoricalFulfillmentScore(){
        Map<String, Integer> ret = historicalFulfillmentScoreMap.get();
        if (ret == null){
            List<BasHistoricalFulfillmentLevelDO> list = historicalFulfillmentLevelService.getList();
            ret = ListUtils.toMap(list,BasHistoricalFulfillmentLevelDO::getHistoricalFulfillmentLevel,BasHistoricalFulfillmentLevelDO::getScore);
            historicalFulfillmentScoreMap.set(ret);
        }
        return ret;
    }

    /**
     * 计算得分：
     * 1. 计算提前周得分 IAdvanceWeekScore
     * 2. 计算计费类型得分 IBillTypeScore
     * 3. 计算履历得分 IHistoricalFulfillmentScore
     * 4. 计算总得分 ITotalScore
     * */
    @Override
    public void computeScore(Object obj) {
        BigDecimal totalScore = BigDecimal.ZERO;
        if (obj instanceof IAdvanceWeekScore){
            totalScore = totalScore.add(computeAdvanceWeekScore((IAdvanceWeekScore) obj));
        }
        if (obj instanceof IBillTypeScore){
            totalScore = totalScore.add(computeBillTypeScore((IBillTypeScore) obj));
        }
        if (obj instanceof IHistoricalFulfillmentScore){
            totalScore = totalScore.add(computeHistoricalFulfillmentScore((IHistoricalFulfillmentScore) obj));
        }
        if (obj instanceof IHistoricalUnsatisfiedScore){
            totalScore = totalScore.add(computeHistoricalUnsatisfiedScore((IHistoricalUnsatisfiedScore) obj));
        }
        if (obj instanceof ITotalScore){
            ((ITotalScore) obj).setTotalScore(totalScore);
            ((ITotalScore) obj).setAdjTotalScore(totalScore);
        }
    }

    /**
     * 计算历史未满足追加得分
     * */
    private BigDecimal computeHistoricalUnsatisfiedScore(IHistoricalUnsatisfiedScore obj) {
        // todo 历史未满足等待完善
        BigDecimal score = BigDecimal.ZERO;
        obj.setHistoricalUnsatisfiedScore(score);
        return score;
    }

    /**
     * 计算履历得分
     * @param obj
     * @return
     */
    private BigDecimal computeHistoricalFulfillmentScore(IHistoricalFulfillmentScore obj) {
        String level = obj.getHistoricalFulfillmentLevel();
        if (!SoeCommonUtils.isNotBlank(level)){
            level = HistoricalFulfillmentLevelEnum.LOW.getName();
            obj.setHistoricalFulfillmentLevel(level);
        }
        Map<String, Integer> scoreMap = getHistoricalFulfillmentScore();
        Integer score = scoreMap.get(level);
        if (score == null){
            if (!EnvUtils.isProduction()){
                // 非生产没有历史履约等级默认给 0
                score = 0;
            }else {
                throw new ITException("历史履约得分字典中不存在历史履约等级："+level);
            }
        }
        BigDecimal v = BigDecimal.valueOf(score);
        obj.setHistoricalFulfillmentScore(v);
        return v;
    }

    /**
     * 计算计费类型得分
     * @param obj
     * @return
     */
    private BigDecimal computeBillTypeScore(IBillTypeScore obj) {
        Map<String, Integer> map = getBillTypeScoreMap();
        String billType = obj.getBillType();
        Integer score = map.get(billType);
        if (score == null){
            if (!EnvUtils.isProduction()){
                // 非生产没有计费类型默认给 0
                score = 0;
            }else {
                throw new ITException("计费类型得分字典中不存在计费类型："+billType);
            }
        }
        BigDecimal v = BigDecimal.valueOf(score);
        obj.setBillTypeScore(v);
        return v;
    }

    /**
     * 计算加权提前周得分
     * @param obj
     * @return
     */
    private BigDecimal computeAdvanceWeekScore(IAdvanceWeekScore obj) {
        BigDecimal totalCore = BigDecimal.ZERO; // 总核数
        BigDecimal totalScore = BigDecimal.ZERO; // 总得分
        Map<String,BigDecimal> wMap = new HashMap<>();
        // 计算总核数，保存提前周核数
        for (AdvanceWeekEnum value : AdvanceWeekEnum.values()) {
            BigDecimal v = value.getGetter().apply(obj);
            if (!SoeCommonUtils.isNullOrZone(v)){
                totalCore = totalCore.add(v);
                wMap.put(value.getName(),v);
            }
        }
        // 计算加权
        if (totalCore.compareTo(BigDecimal.ZERO) > 0){
            Map<String, Integer> map = getAdvanceWeekScoreMap();
            for (Map.Entry<String, BigDecimal> entry : wMap.entrySet()) {
                String key = entry.getKey();
                Integer score = map.get(key);
                if (score == null){
                    throw new ITException("提前周得分字典中不存在提前周："+key);
                }
                BigDecimal v = BigDecimal.valueOf(score); // 提前周得分
                BigDecimal value = entry.getValue(); // 提前周核数
                // 计算得分权重
                BigDecimal weight = value.multiply(v).divide(totalCore,6, RoundingMode.HALF_UP);
                totalScore = totalScore.add(weight);
            }
        }
        obj.setAdvanceWeekScore(totalScore);
        return totalScore;
    }

}
