package cloud.demand.app.modules.order_report.entity.dict;

import cloud.demand.app.modules.order_report.enums.score.AdvanceWeekEnum;
import cloud.demand.app.modules.soe.entitiy.dict.BasVersionCommonDO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/** 提前周得分 */
@Data
@Table("bas_advance_week_score")
public class BasAdvanceWeekScoreDO extends BasVersionCommonDO {
    /** 自增 id<br/>Column: [id] */
    @ExcelIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 提前周<br/>Column: [advance_week] {@link AdvanceWeekEnum} */
    @Column(value = "advance_week")
    @ExcelProperty(value = "提前周",index = 0)
    private String AdvanceWeek;

    /** 得分<br/>Column: [score] */
    @Column(value = "score")
    @ContentStyle(dataFormat = 1) // 数字格式
    @ExcelProperty(value = "得分",index = 1)
    private Integer score;

}
