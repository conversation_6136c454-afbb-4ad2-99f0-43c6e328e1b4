package cloud.demand.app.modules.order_report.entity.report;

import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Table("order_score_item")
@Data
public class QueryOrderScoreW13ItemVO extends QueryOrderScoreW13ItemDfVO {

    public static QueryOrderScoreW13ItemDfVO transform(QueryOrderScoreW13ItemVO item) {
        QueryOrderScoreW13ItemDfVO ret = new QueryOrderScoreW13ItemDfVO();
        ret.setW0(item.getW0());
        ret.setW1(item.getW1());
        ret.setW2(item.getW2());
        ret.setW3(item.getW3());
        ret.setW4(item.getW4());
        ret.setW5(item.getW5());
        ret.setW6(item.getW6());
        ret.setW7(item.getW7());
        ret.setW8(item.getW8());
        ret.setW9(item.getW9());
        ret.setW10(item.getW10());
        ret.setW11(item.getW11());
        ret.setW12(item.getW12());
        ret.setW13(item.getW13());
        ret.setStatTime(item.getStatTime());
        ret.setId(item.getId());
        ret.setVersion(item.getVersion());
        ret.setProductType(item.getProductType());
        ret.setProduct(item.getProduct());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setWarZone(item.getWarZone());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setUin(item.getUin());
        ret.setAppId(item.getAppId());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setCountryName(item.getCountryName());
        ret.setAreaName(item.getAreaName());
        ret.setRegionName(item.getRegionName());
        ret.setZoneName(item.getZoneName());
        ret.setInstanceFamily(item.getInstanceFamily());
        ret.setPurchaseInstanceType(item.getPurchaseInstanceType());
        ret.setGenerationInstanceType(item.getGenerationInstanceType());
        ret.setInstanceType(item.getInstanceType());
        ret.setYearMonth(item.getYearMonth());
        ret.setBeginBuyDate(item.getBeginBuyDate());
        ret.setEndBuyDate(item.getEndBuyDate());
        ret.setBillType(item.getBillType());
        ret.setOrderNumber(item.getOrderNumber());
        ret.setTotalCore(item.getTotalCore());
        ret.setHistoricalFulfillmentLevel(item.getHistoricalFulfillmentLevel());
        ret.setAdvanceWeekScore(item.getAdvanceWeekScore());
        ret.setBillTypeScore(item.getBillTypeScore());
        ret.setHistoricalFulfillmentScore(item.getHistoricalFulfillmentScore());
        ret.setHistoricalUnsatisfiedScore(item.getHistoricalUnsatisfiedScore());
        ret.setTotalScore(item.getTotalScore());
        ret.setAdjTotalScore(item.getAdjTotalScore());
        ret.setScoreLevel(item.getScoreLevel());
        ret.setAdjScoreLevel(item.getAdjScoreLevel());
        return ret;

    }
}
