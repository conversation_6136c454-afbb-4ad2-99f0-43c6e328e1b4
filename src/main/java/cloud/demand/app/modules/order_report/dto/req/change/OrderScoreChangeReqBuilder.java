package cloud.demand.app.modules.order_report.dto.req.change;

import cloud.demand.app.modules.order_report.enums.OrderChangeStatusEnum;
import com.pugwoo.wooutils.collect.ListUtils;

import java.util.List;
import java.util.stream.Collectors;

/** 构建器 */

public class OrderScoreChangeReqBuilder {
    private OrderScoreChangeCreateReq changeCreateReq;

    public OrderScoreChangeReqBuilder createReq(OrderScoreChangeCreateReq createReq){
        this.changeCreateReq = createReq;
        return this;
    }

    public OrderScoreChangeReq builder(){
        OrderScoreChangeReq changeReq = new OrderScoreChangeReq();
        buildWithCreateReq(changeReq);
        return changeReq;
    }

    private void buildWithCreateReq(OrderScoreChangeReq changeReq){
        if (changeCreateReq != null){
            changeReq.setStatus(ListUtils.newArrayList(OrderChangeStatusEnum.APPROVAL.getName())); // 待审批
            changeReq.setInstanceType(ListUtils.newArrayList(changeCreateReq.getInstanceType())); // 实例类型
            changeReq.setZoneName(ListUtils.newArrayList(changeCreateReq.getZoneName())); // 可用区
            changeReq.setStartYearMonth(changeCreateReq.getYearMonth()); // 年月
            changeReq.setEndYearMonth(changeCreateReq.getYearMonth()); // 年月
            List<OrderScoreChangeCreateReq.Detail> detail = changeCreateReq.getDetail();
            List<String> ids = detail.stream().map(OrderScoreChangeCreateReq.Detail::getId).collect(Collectors.toList());
            changeReq.setDwsId(ids); // dws 的明细 id
        }
    }
}
