package cloud.demand.app.modules.order_report.dto.req.change;

import cloud.demand.app.modules.soe.dto.req.PageReq;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import java.util.List;

@Data
public class OrderScoreChangeReq {
    @SopReportWhere(sql = "`year_month` >= ?")
    private String startYearMonth;

    @SopReportWhere(sql = "`year_month` <= ?")
    private String endYearMonth;

    @SopReportWhere
    private List<String> dwsId;

    @SopReportWhere
    private List<String> orderId;

    @SopReportWhere
    private List<String> status;

    @SopReportWhere
    private List<String> industryDept;

    @SopReportWhere
    private List<String> createUser;

    @SopReportWhere
    private List<String> instanceType;

    @SopReportWhere
    private List<String> zoneName;
    private PageReq page;
}
