package cloud.demand.app.modules.order_report.model;

import cloud.demand.app.modules.order_report.entity.report.QueryOrderScoreItemDfVO;
import cloud.demand.app.modules.order_report.entity.report.QueryOrderScoreW13ItemDfVO;
import cloud.demand.app.modules.sop_device.utils.LocalDateConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class DwsOrderScoreItemModel {
    /** 统计日期<br/>Column: [stat_time] */
    @ExcelProperty(value = "统计日期",converter = LocalDateConverter.class)
    private LocalDate statTime;

    /** 订单号<br/>Column: [order_number] */
    @ExcelProperty("订单号")
    private String orderNumber;

    /** 产品：CVM<br/>Column: [product_type] */
    @ExcelProperty("产品")
    private String productType;

    /** 需求归属产品：CVM&CBS，EKS官网，弹性MapReduce<br/>Column: [product] */
    @ExcelProperty("需求归属产品")
    private String product;

    /** 行业部门<br/>Column: [industry_dept] */
    @ExcelProperty("行业部门")
    private String industryDept;

    /** 战区<br/>Column: [war_zone] */
    @ExcelProperty("战区")
    private String warZone;

    /** 客户简称<br/>Column: [customer_short_name] */
    @ExcelProperty("客户简称")
    private String customerShortName;

    /** uin<br/>Column: [uin] */
    @ExcelProperty("uin")
    private String uin;

    /** app_id<br/>Column: [app_id] */
    @ExcelProperty("appId")
    private String appId;

    /** 境内外：境内，境外<br/>Column: [customhouse_title] */
    @ExcelProperty("境内外")
    private String customhouseTitle;

    /** 国家<br/>Column: [country_name] */
    @ExcelProperty("国家")
    private String countryName;

    /** 区域<br/>Column: [area_name] */
    @ExcelProperty("区域")
    private String areaName;

    /** 地域<br/>Column: [region_name] */
    @ExcelProperty("地域")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @ExcelProperty("可用区")
    private String zoneName;

    /** 机型族<br/>Column: [instance_family] */
    @ExcelProperty("机型族")
    private String instanceFamily;

    /** 实例类型<br/>Column: [instance_type] */
    @ExcelProperty("实例类型")
    private String instanceType;

    /** 预约所在年月<br/>Column: [year_month] */
    @ExcelProperty("预约所在年月")
    private String yearMonth;

    /** 起始购买时间<br/>Column: [begin_buy_date] */
    @ExcelProperty(value = "起始购买时间",converter = LocalDateConverter.class)
    private LocalDate beginBuyDate;

    /** 结束购买时间<br/>Column: [end_buy_date] */
    @ExcelProperty(value = "结束购买时间",converter = LocalDateConverter.class)
    private LocalDate endBuyDate;

    /** 计费类型<br/>Column: [bill_type] */
    @ExcelProperty("计费类型")
    private String billType;


    /** 预约单核心数<br/>Column: [total_core] */
    @ExcelProperty("预约单核心数")
    private BigDecimal totalCore;

    /** 提前0周<br/>Column: [w0] */
    @ExcelProperty("提前0周")
    private BigDecimal w0;

    /** 提前1周<br/>Column: [w1] */
    @ExcelProperty("提前1周")
    @Column(value = "w1")
    private BigDecimal w1;

    /** 提前2周<br/>Column: [w2] */
    @ExcelProperty("提前2周")
    private BigDecimal w2;

    /** 提前3周<br/>Column: [w3] */
    @ExcelProperty("提前3周")
    private BigDecimal w3;

    /** 提前4周<br/>Column: [w4] */
    @ExcelProperty("提前4周")
    private BigDecimal w4;

    /** 提前5周<br/>Column: [w5] */
    @ExcelProperty("提前5周")
    private BigDecimal w5;

    /** 提前6周<br/>Column: [w6] */
    @ExcelProperty("提前6周")
    private BigDecimal w6;

    /** 提前7周<br/>Column: [w7] */
    @ExcelProperty("提前7周")
    private BigDecimal w7;

    /** 提前8周<br/>Column: [w8] */
    @ExcelProperty("提前8周")
    private BigDecimal w8;

    /** 提前9周<br/>Column: [w9] */
    @ExcelProperty("提前9周")
    private BigDecimal w9;

    /** 提前10周<br/>Column: [w10] */
    @ExcelProperty("提前10周")
    private BigDecimal w10;

    /** 提前11周<br/>Column: [w11] */
    @ExcelProperty("提前11周")
    private BigDecimal w11;

    /** 提前12周<br/>Column: [w12] */
    @ExcelProperty("提前12周")
    private BigDecimal w12;

    /** 提前13周<br/>Column: [w13] */
    @ExcelProperty("提前13周")
    private BigDecimal w13;

    /** 历史履约等级<br/>Column: [historical_fulfillment_level] */
    @ExcelProperty("历史履约等级")
    private String historicalFulfillmentLevel;

    /** 提前周得分<br/>Column: [advance_week_score] */
    @ExcelProperty("提前周得分")
    private BigDecimal advanceWeekScore;

    /** 计费类型得分<br/>Column: [bill_type_score] */
    @ExcelProperty("计费类型得分")
    private BigDecimal billTypeScore;

    /** 历史履约得分<br/>Column: [historical_fulfillment_score] */
    @ExcelProperty("历史履约得分")
    private BigDecimal historicalFulfillmentScore;

    /**
     * 历史未满足追加得分
     * */
    @ExcelIgnore
    private BigDecimal historicalUnsatisfiedScore;

    /** 总得分<br/>Column: [total_score] */
    @ExcelProperty("总得分")
    private BigDecimal totalScore;

    /** 调整总得分<br/>Column: [adj_total_score] */
    @ExcelProperty("调整总得分")
    private BigDecimal adjTotalScore;

    public static DwsOrderScoreItemModel transform(QueryOrderScoreW13ItemDfVO item){
        DwsOrderScoreItemModel ret = new DwsOrderScoreItemModel();
        ret.setStatTime(item.getStatTime());
        ret.setOrderNumber(item.getOrderNumber());
        ret.setProductType(item.getProductType());
        ret.setProduct(item.getProduct());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setWarZone(item.getWarZone());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setUin(item.getUin());
        ret.setAppId(item.getAppId());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setCountryName(item.getCountryName());
        ret.setAreaName(item.getAreaName());
        ret.setRegionName(item.getRegionName());
        ret.setZoneName(item.getZoneName());
        ret.setInstanceFamily(item.getInstanceFamily());
        ret.setInstanceType(item.getInstanceType());
        ret.setYearMonth(item.getYearMonth());
        ret.setBeginBuyDate(item.getBeginBuyDate());
        ret.setEndBuyDate(item.getEndBuyDate());
        ret.setBillType(item.getBillType());
        ret.setTotalCore(item.getTotalCore());
        ret.setW0(item.getW0());
        ret.setW1(item.getW1());
        ret.setW2(item.getW2());
        ret.setW3(item.getW3());
        ret.setW4(item.getW4());
        ret.setW5(item.getW5());
        ret.setW6(item.getW6());
        ret.setW7(item.getW7());
        ret.setW8(item.getW8());
        ret.setW9(item.getW9());
        ret.setW10(item.getW10());
        ret.setW11(item.getW11());
        ret.setW12(item.getW12());
        ret.setW13(item.getW13());
        ret.setHistoricalFulfillmentLevel(item.getHistoricalFulfillmentLevel());
        ret.setAdvanceWeekScore(item.getAdvanceWeekScore());
        ret.setBillTypeScore(item.getBillTypeScore());
        ret.setHistoricalFulfillmentScore(item.getHistoricalFulfillmentScore());
        ret.setHistoricalUnsatisfiedScore(item.getHistoricalUnsatisfiedScore());
        ret.setTotalScore(item.getTotalScore());
        ret.setAdjTotalScore(item.getAdjTotalScore());
        return ret;
    }
}
