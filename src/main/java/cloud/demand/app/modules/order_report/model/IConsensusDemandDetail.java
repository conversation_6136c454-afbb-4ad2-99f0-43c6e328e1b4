package cloud.demand.app.modules.order_report.model;

import java.math.BigDecimal;
import java.time.LocalDate;

public interface IConsensusDemandDetail {
    public String getOrderNumber();
    public String getDemandInstanceType();
    public String getDemandZoneName();
    public String getProduct();
    public LocalDate getConsensusBeginBuyDate();
    public LocalDate getConsensusEndBuyDate();
    public BigDecimal getConsensusDemandGpuNum();
    public BigDecimal getConsensusDemandCpuNum();
    public BigDecimal getCumulativePreDeductCpuNum();
    public BigDecimal getCumulativePreDeductGpuNum();
    public void setCumulativePreDeductCpuNum(BigDecimal cumulativePreDeductCpuNum);
    public void setCumulativePreDeductGpuNum(BigDecimal cumulativePreDeductGpuNum);
}
