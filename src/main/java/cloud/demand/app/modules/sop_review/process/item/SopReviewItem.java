package cloud.demand.app.modules.sop_review.process.item;

import cloud.demand.app.modules.soe.dto.item.BigDecimalItem;
import cloud.demand.app.modules.sop_review.enums.fields.ISopReviewField;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;

/** sop需求评审底数 */
@Data
public class SopReviewItem extends SopReviewAbstractReportData implements ISopReviewField {
    @Column(value = "any_business_type")
    private String businessType;

    /** 这里不能加 any，这个字段底数不能用，或者说需要实时判断业务范围，所以要清洗并覆盖原来的底数 */
    @Column(value = "business_range")
    private String businessRange;

    @Column(value = "any_dept_name")
    private String deptName;

    @Column(value = "any_plan_product_name")
    private String planProductName;

    @Column(value = "any_obs_project_type")
    private String obsProjectType;

    @Column(value = "any_resource_type")
    private String resourceType;

    @Column(value = "any_compute_type")
    private String computeType;

    @Column(value = "any_device_type")
    private String deviceType;

    @Column(value = "any_device_family")
    private String deviceFamily;

    @Column(value = "any_instance_family")
    private String instanceFamily;

    @Column(value = "any_instance_type")
    private String instanceType;

    @Column(value = "any_instance_model")
    private String instanceModel;

    @Column(value = "any_resource_pool_type")
    private String resourcePoolType;

    @Column(value = "any_gpu_abbr")
    private String gpuAbbr;

    @Column(value = "any_customhouse_title")
    private String customhouseTitle;

    @Column(value = "any_country_name")
    private String countryName;

    @Column(value = "any_city_name")
    private String cityName;

    @Column(value = "any_index_year_month")
    private String indexYearMonth;


    @Column(value = "total_num")
    private BigDecimal num;

    @Column(value = "total_core_num")
    private BigDecimal coreNum;

    @Column(value = "total_gpu_num")
    private BigDecimal gpuNum;

    /** 逻辑容量<br/>Column: [logic_capacity] */
    @Column(value = "total_logic_capacity")
    private BigDecimal logicCapacity;

    @Column(value = "total_cpu_number")
    private BigDecimal totalCpuNumber;

    @Column(value = "total_memory_number")
    private BigDecimal totalMemoryNumber;

    @Column(value = "total_disk_number")
    private BigDecimal totalDiskNumber;

    /** cpu型号<br/>Column: [cpu_abbr] */
    @Column(value = "any_cpu_abbr")
    private String cpuAbbr;

    /** 内存容量<br/>Column: [memory_volume] */
    @Column(value = "any_memory_volume")
    private Integer memoryVolume;

    /** 硬盘1缩写<br/>Column: [disk_abbr] */
    @Column(value = "any_disk_abbr")
    private String diskAbbr;

    /** 硬盘2缩写<br/>Column: [disk2_abbr] */
    @Column(value = "any_disk2_abbr")
    private String disk2Abbr;

    /** 网卡缩写<br/>Column: [nic_abbr] */
    @Column(value = "any_nic_abbr")
    private String nicAbbr;

    /** SSD1缩写<br/>Column: [ssd_abbr] */
    @Column(value = "any_ssd_abbr")
    private String ssdAbbr;

    /** SSD2缩写<br/>Column: [ssd2_abbr] */
    @Column(value = "any_ssd2_abbr")
    private String ssd2Abbr;

    /** SSD3缩写<br/>Column: [ssd3_abbr] */
    @Column(value = "any_ssd3_abbr")
    private String ssd3Abbr;

    /** 处理器厂家<br/>Column: [cpu_vender] */
    @Column(value = "any_cpu_vender")
    private String cpuVender;

    /** 智能网卡 */
    @Column(value = "any_intellect_network_card_type")
    private String intellectNetworkCardType;

    /** 技术分类 */
    @Column(value = "any_technical_class")
    private String technicalClass;

    /** 核心部件bit */
    @Column(value = "any_core_components_bit")
    private Long coreComponentsBit;

    @Column(value = "any_enum_bit_str")
    private String enumBitStr;

    /** 磁盘1规格 */
    @Column(value = "any_disk_volume")
    private Integer diskVolume;

    /** 磁盘2规格 */
    @Column(value = "any_disk2_volume")
    private Integer disk2Volume;

    /** 核心部件资源量（块） */
    private BigDecimal coreComponentNum;

    /** 核心部件(非 db 字段) */
    private String coreComponents;

    /** 部件字段（非 db 字段） */
    private String componentsField;

    /** 字段枚举（非 db 字段） */
    private String enumValue;

    @Override
    public BigDecimal getValue_() {
        // 台数，核数，gpu 卡数，逻辑容量，核心部件
        return new BigDecimalItem(num,new BigDecimal[]{coreNum,gpuNum,logicCapacity, ObjectUtils.defaultIfNull(coreComponentNum,BigDecimal.ZERO)});
    }

    public SopReviewItem copy(){
        SopReviewItem ret = new SopReviewItem();
        ret.setBusinessType(this.getBusinessType());
        ret.setBusinessRange(this.getBusinessRange());
        ret.setDeptName(this.getDeptName());
        ret.setPlanProductName(this.getPlanProductName());
        ret.setObsProjectType(this.getObsProjectType());
        ret.setResourceType(this.getResourceType());
        ret.setComputeType(this.getComputeType());
        ret.setDeviceType(this.getDeviceType());
        ret.setDeviceFamily(this.getDeviceFamily());
        ret.setInstanceFamily(this.getInstanceFamily());
        ret.setInstanceType(this.getInstanceType());
        ret.setInstanceModel(this.getInstanceModel());
        ret.setResourcePoolType(this.getResourcePoolType());
        ret.setGpuAbbr(this.getGpuAbbr());
        ret.setCustomhouseTitle(this.getCustomhouseTitle());
        ret.setCountryName(this.getCountryName());
        ret.setCityName(this.getCityName());
        ret.setIndexYearMonth(this.getIndexYearMonth());
        ret.setNum(this.getNum());
        ret.setCoreNum(this.getCoreNum());
        ret.setGpuNum(this.getGpuNum());
        ret.setLogicCapacity(this.getLogicCapacity());
        ret.setCpuAbbr(this.getCpuAbbr());
        ret.setMemoryVolume(this.getMemoryVolume());
        ret.setDiskAbbr(this.getDiskAbbr());
        ret.setDisk2Abbr(this.getDisk2Abbr());
        ret.setNicAbbr(this.getNicAbbr());
        ret.setSsdAbbr(this.getSsdAbbr());
        ret.setSsd2Abbr(this.getSsd2Abbr());
        ret.setSsd3Abbr(this.getSsd3Abbr());
        ret.setCpuVender(this.getCpuVender());
        ret.setDiskVolume(this.getDiskVolume());
        ret.setDisk2Volume(this.getDisk2Volume());
        ret.setIntellectNetworkCardType(this.getIntellectNetworkCardType());
        ret.setTechnicalClass(this.getTechnicalClass());
        ret.setCoreComponentsBit(this.getCoreComponentsBit());
        ret.setEnumBitStr(this.getEnumBitStr());
        ret.setCoreComponents(this.getCoreComponents());
        ret.setComponentsField(this.getComponentsField());
        ret.setEnumValue(this.getEnumValue());
        ret.setTotalCpuNumber(this.getTotalCpuNumber());
        ret.setTotalMemoryNumber(this.getTotalMemoryNumber());
        ret.setTotalDiskNumber(this.getTotalDiskNumber());
        ret.setCoreComponentNum(this.getCoreComponentNum());
        return ret;

    }

}
