package cloud.demand.app.modules.sop_review.enums;

import cloud.demand.app.modules.soe.enums.index.ISoeIndexEnum;
import cloud.demand.app.modules.soe.model.index.IndexTree;
import cloud.demand.app.modules.sop_util.enums.IStoreNameEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/** sop 需求评审指标 */
@AllArgsConstructor
@Getter
public enum SopReviewIndexEnum implements ISoeIndexEnum {
    // =============== init ================
    SOP_REVIEW_INT("初始化",SopReviewStoreEnum.SOP_REVIEW_INT,true),

    // =============== 原始需求报表 ================
    ORIGIN_CUR_DATA("原始需求@当前版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND),

    ORIGIN_PRE_DATA("原始需求@上个版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND),

    ORIGIN_DIF_DATA("原始需求@变化量",null), // 当前版本 - 上个版本

    // =============== 原始需求报表V2 ================
    ORIGIN_CUR_V2_NEW_DATA("原始需求@新增@当前版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND_V2),

    ORIGIN_PRE_V2_NEW_DATA("原始需求@新增@上个版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND_V2),

    ORIGIN_DIF_V2_NEW_DATA("原始需求@新增@变化量",null), // 当前版本 - 上个版本

    ORIGIN_CUR_V2_RETURN_DATA("原始需求@退回@当前版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND_V2),

    ORIGIN_PRE_V2_RETURN_DATA("原始需求@退回@上个版本",SopReviewStoreEnum.SOP_ORIGIN_DEMAND_V2),

    ORIGIN_DIF_V2_RETURN_DATA("原始需求@退回@变化量",null), // 当前版本 - 上个版本

    ORIGIN_CUR_V2_NET_DATA("原始需求@净增@当前版本", null),

    ORIGIN_PRE_V2_NET_DATA("原始需求@净增@上个版本",null),

    ORIGIN_DIF_V2_NET_DATA("原始需求@净增@变化量",null), // 当前版本 - 上个版本

    // =============== 公司物理机需求报表 ================

    DEVICE_CUR_DATA("公司物理机需求@当前版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND),

    DEVICE_PRE_DATA("公司物理机需求@上个版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND),

    DEVICE_DIF_DATA("公司物理机需求@变化量",null), // 当前版本 - 上个版本

    // =============== 公司物理机需求报表 V2 ================

    DEVICE_CUR_V2_NEW_DATA("公司物理机需求@新增@当前版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND_V2),

    DEVICE_PRE_V2_NEW_DATA("公司物理机需求@新增@上个版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND_V2),

    DEVICE_DIF_V2_NEW_DATA("公司物理机需求@新增@变化量",null), // 当前版本 - 上个版本

    DEVICE_CUR_V2_RETURN_DATA("公司物理机需求@退回@当前版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND_V2),

    DEVICE_PRE_V2_RETURN_DATA("公司物理机需求@退回@上个版本",SopReviewStoreEnum.SOP_DEVICE_DEMAND_V2),

    DEVICE_DIF_V2_RETURN_DATA("公司物理机需求@退回@变化量",null), // 当前版本 - 上个版本

    DEVICE_CUR_V2_NET_DATA("公司物理机需求@净增@当前版本",null),

    DEVICE_PRE_V2_NET_DATA("公司物理机需求@净增@上个版本",null),

    DEVICE_DIF_V2_NET_DATA("公司物理机需求@净增@变化量",null), // 当前版本 - 上个版本

    // =============== 大盘需求全量报表 ================

    OVERALL_REASON_DATA("大盘需求@需求总量@变化原因",SopReviewStoreEnum.SOP_OVERALL_DEMAND_REASON),

    OVERALL_ALL_CUR_DATA("大盘需求@需求总量@当前版本",null), // 需求总量 = 已执行 + 未执行

    OVERALL_ALL_PRE_DATA("大盘需求@需求总量@上个版本",null),

    OVERALL_ALL_DIF_DATA("大盘需求@需求总量@变化量",null), // 当前版本 - 上个版本

    OVERALL_EXECUTED_CUR_DATA("大盘需求@已执行@当前版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND), // 已执行

    OVERALL_EXECUTED_PRE_DATA("大盘需求@已执行@上个版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND),

    OVERALL_EXECUTED_DIF_DATA("大盘需求@已执行@变化量",null), // 当前版本 - 上个版本

    OVERALL_NOT_EXECUTED_CUR_DATA("大盘需求@未执行@当前版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND), // 未执行

    OVERALL_NOT_EXECUTED_PRE_DATA("大盘需求@未执行@上个版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND),

    OVERALL_NOT_EXECUTED_DIF_DATA("大盘需求@未执行@变化量",null), // 当前版本 - 上个版本

    // =============== 大盘需求全量报表v2 ================

    OVERALL_REASON_V2_DATA("大盘需求@变化原因",SopReviewStoreEnum.SOP_OVERALL_DEMAND_REASON_V2),

    OVERALL_NEW_CUR_DATA("大盘需求@新增@当前版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND_V2), // 新增

    OVERALL_NEW_PRE_DATA("大盘需求@新增@上个版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND_V2),

    OVERALL_NEW_DIF_DATA("大盘需求@新增@变化量",null), // 当前版本 - 上个版本

    OVERALL_RETURN_CUR_DATA("大盘需求@退回@当前版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND_V2), // 退回

    OVERALL_RETURN_PRE_DATA("大盘需求@退回@上个版本",SopReviewStoreEnum.SOP_OVERALL_DEMAND_V2),

    OVERALL_RETURN_DIF_DATA("大盘需求@退回@变化量",null), // 当前版本 - 上个版本

    OVERALL_NET_CUR_DATA("大盘需求@净增@当前版本",null), // 净增 = 新增 - 退回

    OVERALL_NET_PRE_DATA("大盘需求@净增@上个版本",null),

    OVERALL_NET_DIF_DATA("大盘需求@净增@变化量",null), // 当前版本 - 上个版本
    ;

    private final String name; // 指标名称
    private final IStoreNameEnum storeName; // 指标仓库

    private final boolean isHide; // 是否隐藏

    public static List<String> getOriginIndexList(){
        return ListUtils.newArrayList(
                ORIGIN_CUR_V2_NEW_DATA.getName(),
                ORIGIN_CUR_V2_RETURN_DATA.getName(),
                ORIGIN_CUR_V2_NET_DATA.getName(),

                ORIGIN_PRE_V2_NEW_DATA.getName(),
                ORIGIN_PRE_V2_RETURN_DATA.getName(),
                ORIGIN_PRE_V2_NET_DATA.getName(),

                ORIGIN_DIF_V2_NEW_DATA.getName(),
                ORIGIN_DIF_V2_RETURN_DATA.getName(),
                ORIGIN_DIF_V2_NET_DATA.getName()
        );
    }

    public static List<String> getDeviceIndexList(){
        return ListUtils.newArrayList(
                DEVICE_CUR_V2_NEW_DATA.getName(),
                DEVICE_CUR_V2_RETURN_DATA.getName(),
                DEVICE_CUR_V2_NET_DATA.getName(),

                DEVICE_PRE_V2_NEW_DATA.getName(),
                DEVICE_PRE_V2_RETURN_DATA.getName(),
                DEVICE_PRE_V2_NET_DATA.getName(),

                DEVICE_DIF_V2_NEW_DATA.getName(),
                DEVICE_DIF_V2_RETURN_DATA.getName(),
                DEVICE_DIF_V2_NET_DATA.getName()
        );
    }

    SopReviewIndexEnum(String name,IStoreNameEnum storeName){
        this(name,storeName,false);
    }

    public static List<IndexTree> getTree() {
        List<IndexTree> ret = new ArrayList<>();
        for (SopReviewIndexEnum value : values()) {
            if (value.isHide){
                continue;
            }
            String name = value.getName();
            String[] split = name.split("@");
            List<IndexTree> temp = ret;
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                boolean hasNext = i < split.length - 1;
                IndexTree indexTree = temp.stream().filter(item -> item.getName().equals(s)).findFirst().orElse(null);
                if (indexTree == null){
                    indexTree = new IndexTree(s,null,hasNext ? new ArrayList<>():null,0,null);
                    temp.add(indexTree);
                }
                if (hasNext){
                    temp = indexTree.getChildren();
                }else {
                    indexTree.setFullName(name);
                }
            }
        }
        return ret;
    }
}
