package cloud.demand.app.modules.sop_review.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.YearMonth;

@Data
public class SopReviewVersionUpdateReq {

    @NotNull(message = "id不能为空")
    private Long id;

    private String w13StartYearMonth; // 13 周起始年月
    private String w13EndYearMonth; // 13 周结束年月

    private String cvmDemandVersion; // CVM 需求版本

    private String deviceDemandVersion; // 物理机需求版本

    private String cvmReturnVersion; // CVM 退回版本

    private String deviceReturnVersion; // 物理机退回版本

    @NotNull(message = "cas版本不能为空")
    private Long casVersion;
}
