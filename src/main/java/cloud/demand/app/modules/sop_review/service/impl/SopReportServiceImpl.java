package cloud.demand.app.modules.sop_review.service.impl;

import cloud.demand.app.modules.soe.dto.item.ReportItem;
import cloud.demand.app.modules.soe.dto.item.ReportMapIndexItem;
import cloud.demand.app.modules.soe.dto.resp.SoeFieldValue;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewCommonReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewDeviceReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewOriginReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewParamReq;
import cloud.demand.app.modules.sop_review.dto.resp.SoeReviewReportResp;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewVersionDO;
import cloud.demand.app.modules.sop_review.enums.ComponentsFieldEnum;
import cloud.demand.app.modules.sop_review.enums.QueryProcessEnum;
import cloud.demand.app.modules.sop_review.enums.SopReviewIndexEnum;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import cloud.demand.app.modules.sop_review.model.excel.SopReviewDeviceModel;
import cloud.demand.app.modules.sop_review.model.excel.SopReviewOriginModel;
import cloud.demand.app.modules.sop_review.process.SopReviewQueryProcessFactory;
import cloud.demand.app.modules.sop_review.service.SopReportService;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_review.utils.ReportExcelUtils;
import cloud.demand.app.modules.sop_review.utils.ReportExcelUtils.ExcelSheetInfo;
import cloud.demand.app.modules.sop_util.process.QueryProcess;
import cloud.demand.app.modules.sop_util.process.QueryProcessInstance;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.google.common.base.CaseFormat;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.io.ByteArrayInputStream;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiFunction;

@Service
public class SopReportServiceImpl implements SopReportService {

    @Resource
    DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SopReviewDictService dictService;

    @Resource
    private SopReviewQueryProcessFactory factory;

    @Override
    public List<String> queryParams(SopReviewParamReq req) {
        // 查询参数枚举，底数是多少就返回多少个
        // step1：sopVersion 转采购预测版本号
        List<String> version = dictService.getPurchaseVersion(req.getVersion());
        if (ListUtils.isEmpty(version)){
            throw new ITException(String.format("版本对应采购预测版本号不存在，版本【%s】", req.getVersion()));
        }
        String paramType = req.getParamType();
        // 需要特殊处理的 paramType：distAbbr 和 ssdAbbr
        if ("distAbbr".equals(paramType)){
            return queryDistAbbrParams(version);
        }else if ("ssdAbbr".equals(paramType)){
            return querySsdAbbrParams(version);
        }
        paramType = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, paramType); // 改成驼峰下划线
        // step2：查询底数 params
        return ckcldStdCrpDBHelper.getRaw(String.class, "select distinct ${paramType} from std_crp.dws_sop_review_report where version in (?)".replace("${paramType}", paramType)
                , version);
    }

    private List<String> querySsdAbbrParams(List<String> version) {
        List<String> ret = ckcldStdCrpDBHelper.getRaw(String.class, "select distinct concat(ssd_abbr,'@',ssd2_abbr,'@',ssd3_abbr) from std_crp.dws_sop_review_report where version in (?)", version);
        Set<String> set = new HashSet<>();
        for (String s : ret) {
            String[] split = s.split("@");
            for (String s1 : split) {
                if (StringUtils.isNotBlank(s1)){
                    set.add(s1);
                }
            }
        }
        ret = new ArrayList<>(set);
        ret.sort(String::compareTo);
        return ret;
    }

    private List<String> queryDistAbbrParams(List<String> version) {
        List<String> ret = ckcldStdCrpDBHelper.getRaw(String.class, "select distinct concat(dist_abbr,'@',dist_abbr2) from std_crp.dws_sop_review_report where version in (?)", version);
        Set<String> set = new HashSet<>();
        for (String s : ret) {
            String[] split = s.split("@");
            for (String s1 : split) {
                if (StringUtils.isNotBlank(s1)){
                    set.add(s1);
                }
            }
        }
        ret = new ArrayList<>(set);
        ret.sort(String::compareTo);
        return ret;
    }

    @Override
    public SoeReviewReportResp queryOriginReport(SopReviewOriginReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_ORIGIN_DEMAND.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();
        // step 5：填充 w13 数据
        Map<String, Object> runParams = instance.getTempParamsMap();
        buildW13TotalData(data,req,runParams);

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }

    @Override
    public SoeReviewReportResp queryOverallReport(SopReviewOriginReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_OVERALL_DEMAND.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }

    @Override
    public SoeReviewReportResp queryOverallReportV2(SopReviewOriginReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_OVERALL_DEMAND_V2.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }



    private void buildW13TotalData(List<ReportMapIndexItem> data, SopReviewCommonReq req, Map<String, Object> runParams){
        List<String> groupBy = req.getGroupBy();
        // 没有年月直接跳过
        String name = SopReviewFieldsEnum.indexYearMonth.name();
        if (ListUtils.isEmpty(groupBy) || !groupBy.contains(name)){
            return;
        }
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) runParams.get("versionInfo");
        BasSopReviewVersionDO curVersion = null;
        BasSopReviewVersionDO preVersion = null;
        for (BasSopReviewVersionDO basSopReviewVersionDO : versionInfo) {
            if (basSopReviewVersionDO.getVersion().equals(req.getPreVersion())){
                preVersion = basSopReviewVersionDO;
            }
            if (basSopReviewVersionDO.getVersion().equals(req.getCurVersion())){
                curVersion = basSopReviewVersionDO;
            }
        }
        if (curVersion == null || preVersion == null){
            throw new ITException(String.format("版本【%s】或【%s】对应的 Sop Review 底数为空",req.getPreVersion(),req.getCurVersion()));
        }
        String w13StartYearMonth = curVersion.getW13StartYearMonth();
        String w13EndYearMonth = curVersion.getW13EndYearMonth();

        BiFunction<String,Map<String,Object>,Boolean> checkFunc = (k, map) -> {
            String yearMonth = (String)map.get(name);
            // 年月不为空且在区间内
            return StringUtils.isNotBlank(yearMonth) && w13StartYearMonth.compareTo(yearMonth) <= 0 && w13EndYearMonth.compareTo(yearMonth) >= 0;
        };

        for (ReportMapIndexItem datum : data) {
            HashMap<String, Map<String, ReportItem>> extTotal = new HashMap<>();
            extTotal.put("w13Total",ReportMapIndexItem.genExtTotal(datum.getData(), checkFunc));
            datum.setExtTotal(extTotal);
        }
    }

    @Override
    public SoeReviewReportResp queryDeviceReport(SopReviewDeviceReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_DEVICE_DEMAND.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();
        // step 5：填充 w13 数据
        Map<String, Object> runParams = instance.getTempParamsMap();
        buildW13TotalData(data,req,runParams);

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }

    @Override
    public SoeReviewReportResp queryOriginReportV2(SopReviewOriginReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_ORIGIN_DEMAND_V2.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }

    @Override
    public SoeReviewReportResp queryDeviceReportV2(SopReviewDeviceReq req) {
        // step 1：校验维度字段
        req.check(SopReviewFieldsEnum.getFieldNames());

        // step 2：调用查询进程
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_DEVICE_DEMAND_V2.getName());
        SoeReviewReportResp ret = new SoeReviewReportResp();
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        ret.setData((List<ReportMapIndexItem>)instance.getRet());

        // step 3：全量字段
        if (BooleanUtils.isTrue(req.getNeedFields())){
            ret.setFields(SoeFieldValue.buildByMapIndex(ret.getData()));
        }
        // step 4：排序 + 分页
        List<ReportMapIndexItem> data = ret.getData();

        ret.setData(ReportMapIndexItem.sortAndLimit(data,req.getOrderByDim(),req.getOrderByField(),req.getPage()));

        return ret;
    }

    @Override
    public ResponseEntity<InputStreamResource> excelOriginReport(SopReviewOriginReq req) {
//        req.setDims(new ArrayList<>(SopReviewFieldsEnum.getFieldNames())); // 全维度
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_ORIGIN_DEMAND_EXCEL.getName());
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        SopReviewOriginModel summaryModel = new SopReviewOriginModel();
        summaryModel.buildData(instance,req);
        return SoeCommonUtils.toExcelEntity(summaryModel, SopReviewOriginModel.class,"原始需求报表");
    }

    @Override
    public ResponseEntity<InputStreamResource> excelDeviceReport(SopReviewDeviceReq req) {
//        req.setDims(new ArrayList<>(SopReviewFieldsEnum.getFieldNames())); // 全维度
        QueryProcess process = factory.getProcess(QueryProcessEnum.SOP_DEVICE_DEMAND_EXCEL.getName());
        QueryProcessInstance instance = process.processWithInstance(ImmutableMap.of("req", req));
        SopReviewDeviceModel summaryModel = new SopReviewDeviceModel();
        summaryModel.buildData(instance,req);
        return SoeCommonUtils.toExcelEntity(summaryModel, SopReviewDeviceModel.class,"公司物理机需求报表");
    }

    @Override
    public ResponseEntity<InputStreamResource> excelOriginReportV2(SopReviewOriginReq req) {
        SoeReviewReportResp soeReviewReportResp = queryOriginReportV2(req);
        List<ReportMapIndexItem> data = soeReviewReportResp.getData();
        return excelV2(data,  SopReviewIndexEnum.getOriginIndexList(),"原始需求报表");
    }

    @Override
    public ResponseEntity<InputStreamResource> excelDeviceReportV2(SopReviewDeviceReq req) {
        // 非核心部件单位
        req.setCombineCoreComponents(true);
        SoeReviewReportResp soeReviewReportResp = queryDeviceReportV2(req);
        List<ReportMapIndexItem> data = soeReviewReportResp.getData();

        // 核心部件单位
        req.setCombineCoreComponents(false);
        SoeReviewReportResp coreResp = queryDeviceReportV2(req);
        List<ReportMapIndexItem> coreData = coreResp.getData();

        List<ExcelSheetInfo> list = new ArrayList<>();
        Map<String, String> fieldMap = Arrays.stream(SopReviewFieldsEnum.values())
                .collect(Collectors.toMap(SopReviewFieldsEnum::name, SopReviewFieldsEnum::getName));
        List<String> indexList = SopReviewIndexEnum.getDeviceIndexList();

        Map<String, Map<String, String>> fieldAlias = ComponentsFieldEnum.getFieldAlias();
        // 过滤维度字段
        Set<String> dimsSet = req.getDims() == null ? new HashSet<>():new HashSet<>(req.getDims());
        fieldAlias = fieldAlias.entrySet().stream()
                .filter(e -> dimsSet.contains(e.getKey())).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        for (ReportMapIndexItem datum : data) {
            ComponentsFieldEnum.setFieldAlias(datum, fieldAlias);
        }

        for (ReportMapIndexItem datum : coreData) {
            ComponentsFieldEnum.setFieldAlias(datum, fieldAlias);
        }

        Map<String, Integer> indexOrderMap = new HashMap<>();
        for (int i = 0; i < indexList.size(); i++) {
            indexOrderMap.put(indexList.get(i),i);
        }
        String[] numLabels = new String[]{"物理机台","CPU核","GPU卡","逻辑容量PB"};
        for (int i = 0; i < numLabels.length; i++) {
            list.add(ReportExcelUtils.toExcelViewInfo(data, fieldMap, indexOrderMap, i, numLabels[i]));
        }

        list.add(ReportExcelUtils.toExcelViewInfo(coreData, fieldMap, indexOrderMap, 4, "部件片数"));

        ByteArrayInputStream inputStream = ReportExcelUtils.toExcelWithInfo(list, "公司物理机需求报表");
        String filename =
                "公司物理机需求报表" + "-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8)
                        + ".xlsx";
        return new StreamDownloadBean(filename, inputStream);
    }

    private ResponseEntity<InputStreamResource> excelV2(List<ReportMapIndexItem> data,List<String> indexList,String excelName){
        Map<String, String> fieldMap = Arrays.stream(SopReviewFieldsEnum.values())
                .collect(Collectors.toMap(SopReviewFieldsEnum::name, SopReviewFieldsEnum::getName));

        Map<String, Integer> indexOrderMap = new HashMap<>();
        for (int i = 0; i < indexList.size(); i++) {
            indexOrderMap.put(indexList.get(i),i);
        }
        String[] numLabels = new String[]{"物理机台","CPU核","GPU卡","逻辑容量PB"};
        List<ExcelSheetInfo> list = new ArrayList<>();
        for (int i = 0; i < numLabels.length; i++) {
            list.add(ReportExcelUtils.toExcelViewInfo(data, fieldMap, indexOrderMap, i, numLabels[i]));
        }
        ByteArrayInputStream inputStream = ReportExcelUtils.toExcelWithInfo(list, excelName);
        String filename =
                excelName + "-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8)
                        + ".xlsx";
        return new StreamDownloadBean(filename, inputStream);
    }
}
