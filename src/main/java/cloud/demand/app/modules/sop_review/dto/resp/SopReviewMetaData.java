package cloud.demand.app.modules.sop_review.dto.resp;

import cloud.demand.app.modules.soe.enums.index.SoeOverviewIndexEnum;
import cloud.demand.app.modules.soe.model.fields.FieldInfo;
import cloud.demand.app.modules.soe.model.index.IndexTree;
import cloud.demand.app.modules.sop_review.enums.SopReviewChangeReasonTypeEnum;
import cloud.demand.app.modules.sop_review.enums.SopReviewIndexEnum;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.Data;

import java.util.List;

@Data
public class SopReviewMetaData {
    /** 指标信息 */
    private List<IndexTree> indexTrees;

    /** 字段信息 */
    private List<FieldInfo> fieldInfos;

    /** 变化原因类型枚举 */
    private List<String> changeReasonTypes;
    public static SopReviewMetaData build(){
        SopReviewMetaData ret = new SopReviewMetaData();

        // 构建指标树
        ret.setIndexTrees(SopReviewIndexEnum.getTree());

        // 构建字段集合
        ret.setFieldInfos(SopReviewFieldsEnum.getFields());

        ret.setChangeReasonTypes(Arrays.stream(SopReviewChangeReasonTypeEnum.values()).map(
                SopReviewChangeReasonTypeEnum::getName).collect(
                Collectors.toList()));
        return ret;
    }
}
