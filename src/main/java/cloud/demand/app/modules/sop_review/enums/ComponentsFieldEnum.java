package cloud.demand.app.modules.sop_review.enums;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.soe.dto.item.ReportMapIndexItem;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewCoreComponents;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import cloud.demand.app.modules.sop_review.process.item.SopReviewItem;
import cloud.demand.app.modules.sop_review.service.ComponentsFieldService;
import cloud.demand.app.modules.sop_review.service.SopReviewCoreComponentsService;
import cloud.demand.app.modules.sop_review.service.impl.components.CpuVenderServiceImpl;
import cloud.demand.app.modules.sop_review.service.impl.components.DiskVolumeServiceImpl;
import cloud.demand.app.modules.sop_review.service.impl.components.GpuCardTypeServiceImpl;
import cloud.demand.app.modules.sop_review.service.impl.components.IntellectNetworkCardTypeServiceImpl;
import cloud.demand.app.modules.sop_review.service.impl.components.MemoryAbbrServiceImpl;
import cloud.demand.app.modules.sop_review.utils.SopReviewUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 部件字段
 */
@AllArgsConstructor
@Getter
public enum ComponentsFieldEnum {
    intellectNetworkCardType("智能网卡", IntellectNetworkCardTypeServiceImpl.class, SopReviewItem::getNum),
    gpuCardType("GPU卡型", GpuCardTypeServiceImpl.class, SopReviewItem::getGpuNum),
    diskVolume("HDD硬盘", DiskVolumeServiceImpl.class, SopReviewItem::getTotalDiskNumber),
    cpu("CPU平台", CpuVenderServiceImpl.class, SopReviewItem::getTotalCpuNumber),
    memory("内存", MemoryAbbrServiceImpl.class, SopReviewItem::getTotalMemoryNumber),
    ;

    public final String name;
    private final Class<? extends ComponentsFieldService> beanClass;
    private final Function<SopReviewItem, BigDecimal> getNumber;

    /**
     * 获取 name --> bean 的映射
     */
    public static Map<String, ComponentsFieldService> getName2BeanMap() {
        Map<String, ComponentsFieldService> ret = new TreeMap<>();
        for (ComponentsFieldEnum value : values()) {
            ComponentsFieldService bean = SpringUtil.getBean(value.getBeanClass());
            ret.put(value.getName(), bean);
        }
        return ret;
    }

    /**
     * 设置别名
     * @param item
     * @param map
     */
    public static void setFieldAlias(ReportMapIndexItem item, Map<String, Map<String,String>> map){
        if (ListUtils.isEmpty(map)){
            return;
        }
        Map<String, Object> dimMap = item.getDimMap();
        map.forEach((k,v)->{
            Object o = dimMap.get(k);
            if (o != null){
                String alias = v.get(String.valueOf(o));
                if (alias != null){
                    dimMap.put(k, alias);
                }
            }
        });

    }

    /** 获取别名 */
    public static Map<String, Map<String,String>> getFieldAlias() {
        List<BasSopReviewCoreComponents> bit2EntityMap = getBit2EntityMap();
        Map<String, SopReviewFieldsEnum[]> map = new HashMap<>();
        map.put(intellectNetworkCardType.getName(), new SopReviewFieldsEnum[]{SopReviewFieldsEnum.intellectNetworkCardType});
        map.put(gpuCardType.getName(), new SopReviewFieldsEnum[]{SopReviewFieldsEnum.gpuAbbr});
        map.put(diskVolume.getName(), new SopReviewFieldsEnum[]{SopReviewFieldsEnum.diskVolume, SopReviewFieldsEnum.disk2Volume});
        map.put(cpu.getName(), new SopReviewFieldsEnum[]{SopReviewFieldsEnum.cpuVender});
        map.put(memory.getName(), new SopReviewFieldsEnum[]{SopReviewFieldsEnum.memoryVolume});
        Map<String, Map<String,String>> ret = new HashMap<>();
        for (BasSopReviewCoreComponents components : bit2EntityMap) {
            String alias = components.getAlias();
            if (SoeCommonUtils.isNotBlank(alias)){
                // 替换别名
                String componentsField = components.getComponentsField();
                SopReviewFieldsEnum[] sopReviewFieldsEnums = map.get(componentsField);
                for (SopReviewFieldsEnum fieldsEnum : sopReviewFieldsEnums) {
                    ret.computeIfAbsent(fieldsEnum.name(), k -> new HashMap<>()).put(components.getEnumValue(),alias);
                }
            }
        }
        return ret;
    }


    /**
     * 获取 bitNum --> entity 的映射
     * @return
     */
    public static List<BasSopReviewCoreComponents> getBit2EntityMap() {
        List<BasSopReviewCoreComponents> list = SpringUtil.getBean(SopReviewCoreComponentsService.class).getCacheList();
        return list;
    }

    public static Map<String,Long[]> getKey2BitMap() {
        Map<String,Long[]> ret = new HashMap<>();
        List<BasSopReviewCoreComponents> bit2EntityMap = getBit2EntityMap();
        for (BasSopReviewCoreComponents value : bit2EntityMap) {
            ret.put(value.getKey(), value.getBit());
        }
        return ret;
    }

    /** 展示 */
    public static Map<String,List<Long[]>> getShowLabel2BitMap() {
        Map<String,List<Long[]>> ret = new HashMap<>();
        List<BasSopReviewCoreComponents> bit2EntityMap = getBit2EntityMap();
        for (BasSopReviewCoreComponents value : bit2EntityMap) {
            ret.computeIfAbsent(value.getShowLabel(), k -> new ArrayList<>()).add(value.getBit());
        }
        return ret;
    }

    /**
     * 获取实体数组
     * @return
     */
    public static BasSopReviewCoreComponents[] getEntityArray() {
        List<BasSopReviewCoreComponents> bit2EntityMap = getBit2EntityMap();
        BasSopReviewCoreComponents[] ret = new BasSopReviewCoreComponents[bit2EntityMap.size()];
        int index = 0;
        // 降序
        bit2EntityMap.sort(Comparator.comparing(BasSopReviewCoreComponents::getBitNum));
        for (BasSopReviewCoreComponents entry : bit2EntityMap) {
            ret[index ++] = entry;
        }
        return ret;
    }

    /**
     * 获取匹配的 key（部件字段@字段枚举）
     * @param entityArray key 实体数组
     * @param coreComponentsBit 核心部件位
     * @return
     */
    public static List<Tuple2<String,String>> getShowLabel(BasSopReviewCoreComponents[] entityArray, Long coreComponentsBit, String enumBitStr){
        List<Tuple2<String,String>> ret = new ArrayList<>();
        for (BasSopReviewCoreComponents components : entityArray) {
            Long bitNum = components.getBitNum();
            Long enumBitNum = components.getEnumBitNum();
            if (bitNum > coreComponentsBit){
                break;
            }
            if ((bitNum & coreComponentsBit) != 0){
                int bitStrStart = SopReviewUtils.getBitStrStart(bitNum);
                long enumBit = Long.parseLong(enumBitStr.substring(bitStrStart, bitStrStart + 19));
                if ((enumBit & enumBitNum) != 0){
                    ret.add(new Tuple2<>(components.getComponentsField(), components.getShowValue()));
                }
            }
        }
        return ret;
    }

}
