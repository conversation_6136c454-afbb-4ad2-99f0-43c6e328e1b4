package cloud.demand.app.modules.sop_review.service;

import cloud.demand.app.entity.resource.BasStrategeDeviceTypeDO;
import cloud.demand.app.entity.resource.ServerPartsCompositionVO;
import cloud.demand.app.modules.p2p.product_demand.entity.ServerPartsExtendedInfoDO;

import cloud.demand.app.modules.sop.entity.dict.DockerGpuCategoryStrategyDO;
import cloud.demand.app.modules.sop_return.service.impl.SopReturnCommonServiceImpl;
import java.util.Map;

public interface SopReviewCleanService {
    public void clean(Object obj);
    public Map<String, ServerPartsExtendedInfoDO> getServerPartMapLocal();

    public Map<String, BasStrategeDeviceTypeDO> getBasDeviceType();
    public Map<String, ServerPartsCompositionVO> getServerMapLocal();

    public Map<String, DockerGpuCategoryStrategyDO> getServerGpuMapLocal();

    public Map<String, SopReturnCommonServiceImpl.PlanProductInfo> getPlanProductInfoMap();
}
