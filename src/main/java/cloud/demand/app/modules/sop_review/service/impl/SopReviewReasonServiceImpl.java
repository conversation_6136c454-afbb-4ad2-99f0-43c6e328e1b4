package cloud.demand.app.modules.sop_review.service.impl;

import cloud.demand.app.modules.common.enums.ComputeTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewReasonIUReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewReasonIUV2Req;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonDO;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonV2DO;
import cloud.demand.app.modules.sop_review.entity.YuntiDemandReviewMemoDO;
import cloud.demand.app.modules.sop_review.enums.MemoTypeEnum;
import cloud.demand.app.modules.sop_review.enums.SopReviewChangeReasonTypeEnum;
import cloud.demand.app.modules.sop_review.model.req.SopReviewReasonReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewReasonV2Req;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_review.service.SopReviewReasonService;
import com.google.common.base.Objects;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.exception.CasVersionNotMatchException;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Slf4j(topic = "需求评审变化原因")
@Service
public class SopReviewReasonServiceImpl implements SopReviewReasonService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private SopReviewDictService sopReviewDictService;

    @Resource
    private DBHelper yuntidemandDBHelper;


    @Override
    public List<SopReviewChangeReasonDO> getAll(SopReviewReasonReq req) {
        SopWhereBuilder where = new SopWhereBuilder(req, SopReviewChangeReasonDO.class);
        WhereSQL whereSQL = where.whereSQL();
        return demandDBHelper.getAll(SopReviewChangeReasonDO.class, whereSQL.getSQL(),
                whereSQL.getParams());
    }

    @Override
    public List<SopReviewChangeReasonV2DO> getAllV2(SopReviewReasonV2Req req) {
        SopWhereBuilder where = new SopWhereBuilder(req, SopReviewChangeReasonV2DO.class);
        WhereSQL whereSQL = where.whereSQL();
        whereSQL.addOrderBy("`order`","update_time desc");
        return demandDBHelper.getAll(SopReviewChangeReasonV2DO.class, whereSQL.getSQL(),
                whereSQL.getParams());
    }

    @Override
    public List<YuntiDemandReviewMemoDO> getFromYuntiDemandReviewMemo(String yearWeek, MemoTypeEnum memoTypeEnum) {
        Integer[] ywArr = SoeCommonUtils.parseYearWeek(yearWeek);

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("year = ?", ywArr[0]);
        whereSQL.and("week = ?", ywArr[1]);
        whereSQL.and("memo_type = ?", memoTypeEnum.getStatus());
        whereSQL.and("show_sop_flag = 1"); // 只看对 sop 展示的
        whereSQL.addOrderBy("`order`","create_time desc");
        return yuntidemandDBHelper.getAll(YuntiDemandReviewMemoDO.class,whereSQL.getSQL(), whereSQL.getParams());
    }

    @Transactional(transactionManager = "demandTransactionManager")
    @Override
    public void insertOrUpdate(SopReviewReasonIUReq req) {
        List<SopReviewReasonIUReq.Item> items = req.getItems();
        if (ListUtils.isEmpty(items)) {
            log.info("SopReviewReasonServiceImpl.insertOrUpdate:items is empty");
            return;
        }
        List<SopReviewChangeReasonDO> insertList = new ArrayList<>();
        List<SopReviewChangeReasonDO> updateList = new ArrayList<>();
        Map<String, String> versionCur2PreMap = sopReviewDictService.getVersionCur2PreMap();
        for (SopReviewReasonIUReq.Item item : items) {
            SopReviewChangeReasonDO reasonDO = item.toDO();
            check(reasonDO, versionCur2PreMap);
            if (item.getId() == null) {
                insertList.add(reasonDO);
            } else {
                updateList.add(reasonDO);
            }
        }
        if (ListUtils.isNotEmpty(insertList)){
            demandDBHelper.insert(insertList);
        }
        try{
            if (ListUtils.isNotEmpty(updateList)){
                demandDBHelper.update(updateList);
            }
        }catch (CasVersionNotMatchException e){
            throw new BizException("更新的数据已失效，请刷新页面后重试");
        }
    }

    @Transactional(transactionManager = "demandTransactionManager")
    @Override
    public void insertOrUpdateV2(SopReviewReasonIUV2Req req) {
        List<SopReviewReasonIUV2Req.Item> items = req.getItems();
        List<Long> deleteIds = req.getDeleteIds();
        if (ListUtils.isEmpty(items) && ListUtils.isEmpty(deleteIds)) {
            log.info("SopReviewReasonServiceImpl.insertOrUpdateV2:items is empty and deleteIds is empty");
            return;
        }
        List<SopReviewChangeReasonDO> insertList = new ArrayList<>();
        List<SopReviewChangeReasonDO> updateList = new ArrayList<>();

        if (ListUtils.isNotEmpty(items)){
            Map<String, String> versionCur2PreMap = sopReviewDictService.getVersionCur2PreMap();
            for (SopReviewReasonIUV2Req.Item item : items) {
                SopReviewChangeReasonV2DO reasonDO = item.toDO();
                check(reasonDO, versionCur2PreMap);
                if (item.getId() == null) {
                    insertList.add(reasonDO);
                } else {
                    updateList.add(reasonDO);
                }
            }
        }
        // 删除数据
        if (ListUtils.isNotEmpty(deleteIds)){
            demandDBHelper.executeRaw("delete from sop_review_change_reason_v2 where id in (?)",deleteIds);
        }
        // 写入数据
        if (ListUtils.isNotEmpty(insertList)){
            demandDBHelper.insert(insertList);
        }
        // 更新数据
        try{
            if (ListUtils.isNotEmpty(updateList)){
                demandDBHelper.update(updateList);
            }
        }catch (CasVersionNotMatchException e){
            throw new BizException("更新的数据已失效，请刷新页面后重试");
        }
    }

    /**
     * 校验待更新 or 待写入的数据
     * @param reasonDO 数据
     * @param versionCur2PreMap 版本号映射 key-》当前版本号 value-》上一个版本号
     */
    private void check(SopReviewChangeReasonDO reasonDO, Map<String, String> versionCur2PreMap) {
        String curVersion = reasonDO.getCurVersion();
        String preVersion = reasonDO.getPreVersion();

        // 1. 版本号校验 - 非空
        if (curVersion == null || preVersion == null) {
            throw new BizException("当前或者上一个版本号不能为空");
        }
        // 2. 版本号校验 - 版本号关系
        String allowPreVersion = versionCur2PreMap.get(curVersion);
        if (!Objects.equal(allowPreVersion, preVersion)) {
            throw new BizException(
                    "当前版本号不匹配上一个版本号。当前版本号：" + curVersion + "，正确的上一个版本号：" + allowPreVersion);
        }
        // 3. 业务类型校验 - 非空
        String businessType = reasonDO.getBusinessType();
        String businessRange = reasonDO.getBusinessRange();
        if (StringUtils.isBlank(businessType)) {
            throw new BizException("业务类型不能为空");
        }
        if (StringUtils.isBlank(businessRange)) {
            throw new BizException("业务范围不能为空");
        }
        SopBusinessType byDesc = SopBusinessType.getByDesc(businessType);
        if (byDesc == null) {
            throw new BizException("业务类型不正确");
        }
        if (reasonDO instanceof SopReviewChangeReasonV2DO){
            SopReviewChangeReasonV2DO v2 = (SopReviewChangeReasonV2DO)reasonDO;
            String reasonType = v2.getReasonType();
            // 原因分类
            SopReviewChangeReasonTypeEnum byName = SopReviewChangeReasonTypeEnum.getByName(reasonType);
            if (byName == null){
                throw new BizException(String.format("原因类型枚举未定义：【%s】", reasonType));
            }
            // 计算类型
            String computeType = v2.getComputeType();
            try {
                ComputeTypeEnum.valueOf(computeType);
            } catch (IllegalArgumentException e) {
                throw new BizException(String.format("计算类型未定义：【%s】", computeType));
            }
        }else {
            switch (byDesc) {
                case SELF:
                    // 自研业务 - 业务部门不能为空
                    if (StringUtils.isBlank(reasonDO.getDeptName())) {
                        throw new BizException("业务部门不能为空");
                    }
                    break;
                case CLOUD:
                    // 云业务 - 规划产品不能为空
                    if (StringUtils.isBlank(reasonDO.getPlanProductName())) {
                        throw new BizException("规划产品不能为空");
                    }
                    break;
            }
        }

        // 4. 原因 - 非空
        if (StringUtils.isBlank(reasonDO.getReason())) {
            throw new BizException("原因不能为空");
        }
        // 5. 变化量 - 非空
        if (reasonDO.getChangeNum() == null || reasonDO.getChangeCoreNum() == null || reasonDO.getChangeGpuNum() == null || reasonDO.getChangeLogicCapacity() == null) {
            throw new BizException("变化量不能为空");
        }
        if (reasonDO.getId() != null && reasonDO.getCasVersion() == null){
            throw new BizException("更新时，乐观锁不能为空");
        }
    }
}
