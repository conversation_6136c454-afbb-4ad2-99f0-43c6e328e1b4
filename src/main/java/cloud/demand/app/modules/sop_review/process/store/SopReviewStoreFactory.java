package cloud.demand.app.modules.sop_review.process.store;

import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.enums.SopBusinessType;
import cloud.demand.app.modules.sop.enums.SopResourcePool;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewCommonReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewDeviceReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewOriginReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewOriginV2Req;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewVersionDO;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonDO;
import cloud.demand.app.modules.sop_review.entity.SopReviewChangeReasonV2DO;
import cloud.demand.app.modules.sop_review.entity.YuntiDemandReviewMemoDO;
import cloud.demand.app.modules.sop_review.enums.BgProductAliasType;
import cloud.demand.app.modules.sop_review.enums.MemoTypeEnum;
import cloud.demand.app.modules.sop_review.enums.SopReviewDemandType;
import cloud.demand.app.modules.sop_review.enums.SopReviewIndexType;
import cloud.demand.app.modules.sop_review.enums.fields.SopReviewFieldsEnum;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataCommonReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataDeviceReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewDataOriginReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewReasonReq;
import cloud.demand.app.modules.sop_review.model.req.SopReviewReasonV2Req;
import cloud.demand.app.modules.sop_review.process.item.SopReviewItem;
import cloud.demand.app.modules.sop_review.process.item.SopReviewReasonItem;
import cloud.demand.app.modules.sop_review.service.SopGetDataService;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import cloud.demand.app.modules.sop_review.service.SopReviewReasonService;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Comparator;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 仓库
 */
@Component
@StoreRegisterClient
public class SopReviewStoreFactory {

    /** sop数据 */
    @Resource
    private SopGetDataService getDataService;

    /** 字典 */
    @Resource
    private SopReviewDictService dictService;

    /** 变化原因 */
    @Resource
    private SopReviewReasonService reasonService;

    @StoreRegister(name = "sop_review_int", desc = "初始化")
    public void init(Map<String, Object> params) {
        SopReviewCommonReq req = (SopReviewCommonReq) params.get("req"); // 原始请求
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo");
        String preVersion = req.getPreVersion();
        String curVersion = req.getCurVersion();
        List<BasSopReviewVersionDO> sopReviewVersionInfo = dictService.getSopReviewVersionInfo(ListUtils.newList(preVersion,curVersion));
        if (ListUtils.isEmpty(sopReviewVersionInfo)){
            throw new ITException(String.format("版本信息获取失败，当前版本【%s】，上一版本【%s】", curVersion,preVersion));
        }
        versionInfo.addAll(sopReviewVersionInfo);
    }

    @StoreRegister(name = "sop_origin_demand", desc = "sop原始需求")
    public List<SopReviewItem> sopOriginDemand(Map<String, Object> params) {
        SopReviewOriginReq req = (SopReviewOriginReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        SopReviewDataOriginReq storeReq = SopReviewDataOriginReq.transform(req);
        storeReq.setIsExcel(isExcel);
        doCommon(req, versionInfo, isCurrent, storeReq);
        List<SopReviewItem> originDemand = getDataService.getOriginDemand(storeReq);
        dealReturn(req,originDemand);
        return originDemand;
    }

    @StoreRegister(name = "sop_origin_demand_v2", desc = "sop原始需求V2")
    public List<SopReviewItem> sopOriginDemandV2(Map<String, Object> params) {
        SopReviewOriginReq req = (SopReviewOriginReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isNew = (Boolean) params.get("is_new"); // 是否为新增
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        SopReviewDataOriginReq storeReq = SopReviewDataOriginReq.transform(req);
        storeReq.setIsExcel(isExcel);
        String demandType = isNew ? SopReviewDemandType.NEW.getName(): SopReviewDemandType.RETURN.getName();
        String index = SopReviewIndexType.TOTAL.getName(); // 总需求
        doCommon(req, index, demandType , versionInfo, isCurrent, storeReq);
        List<SopReviewItem> originDemand = getDataService.getOriginDemand(storeReq);
//        dealReturn(req,originDemand);
        return originDemand;
    }

    @StoreRegister(name = "sop_device_demand", desc = "sop公司物理机需求")
    public List<SopReviewItem> sopDeviceDemand(Map<String, Object> params) {
        SopReviewDeviceReq req = (SopReviewDeviceReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        SopReviewDataDeviceReq storeReq = SopReviewDataDeviceReq.transform(req);
        storeReq.setIsExcel(isExcel);
        doCommon(req, versionInfo, isCurrent, storeReq);
        List<SopReviewItem> deviceDemand = getDataService.getDeviceDemand(storeReq);
        dealReturn(req,deviceDemand);
        return deviceDemand;
    }

    @StoreRegister(name = "sop_device_demand_v2", desc = "sop公司物理机需求v2")
    public List<SopReviewItem> sopDeviceDemandV2(Map<String, Object> params) {
        SopReviewDeviceReq req = (SopReviewDeviceReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        Boolean isNew = (Boolean) params.get("is_new"); // 是否为新增
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        SopReviewDataDeviceReq storeReq = SopReviewDataDeviceReq.transform(req);
        storeReq.setIsExcel(isExcel);
        String demandType = isNew ? SopReviewDemandType.NEW.getName(): SopReviewDemandType.RETURN.getName();
        String index = SopReviewIndexType.TOTAL.getName(); // 总需求
        doCommon(req, index, demandType , versionInfo, isCurrent, storeReq);
        List<SopReviewItem> deviceDemand = getDataService.getDeviceDemand(storeReq);
        return deviceDemand;
    }

    /** 后面用 v2 的 */
    @Deprecated
    @StoreRegister(name = "sop_overall_demand_reason", desc = "sop大盘全量需求原因")
    public List<SopReviewReasonItem> sopOverallDemandReason(Map<String, Object> params){
        SopReviewOriginReq req = (SopReviewOriginReq) params.get("req"); // 原始请求
        List<String> dims = req.getDims();
        // dims 限定只能是 业务类型 + 业务范围，并且顺序不能变
        if (ListUtils.isEmpty(dims) || dims.size() < 2 ||
                !Objects.equals(dims.get(0), SopReviewFieldsEnum.businessType.name()) ||
                !Objects.equals(dims.get(1), SopReviewFieldsEnum.businessRange.name())){
            return null;
        }else {
            // 多于两个的场景，有两个必要条件
            // 1. 业务类型 + 业务范围唯一筛选
            // 2. 自研业务支持业务部门 + 年月
            // 3. 云业务支持规划产品维度 + 年月
            // 4. 自研上云支持 年月
            if (dims.size() != 2){
                List<String> businessRange = req.getBusinessRange();
                List<String> businessType = req.getBusinessType();
                // 业务类型 + 业务范围唯一筛选
                if (!(ListUtils.isNotEmpty(businessType) && businessType.size() == 1
                        && ListUtils.isNotEmpty(businessRange) && businessRange.size() == 1)){
                    return null;
                }
                String type = businessType.get(0);
                SopBusinessType sopBusinessType = SopBusinessType.getByDesc(type);
                if (sopBusinessType == null){
                    return null;
                }
                boolean isOk = false;
                switch (sopBusinessType){
                    case CLOUD:
                        // 自研支持规划产品维度 + 年月
                        isOk = dims.size() == 4 && dims.get(2).equals(SopReviewFieldsEnum.planProductName.name()) &&
                                dims.get(3).equals(SopReviewFieldsEnum.indexYearMonth.name());
                        break;
                    case SELF:
                        // 自研支持业务部门维度 + 年月
                        isOk = dims.size() == 4 && dims.get(2).equals(SopReviewFieldsEnum.deptName.name()) &&
                                dims.get(3).equals(SopReviewFieldsEnum.indexYearMonth.name());
                        break;
                    default:
                        // 自研上云支持 年月
                        isOk = dims.size() == 3 && dims.get(2).equals(SopReviewFieldsEnum.indexYearMonth.name());
                }
                if (!isOk){
                    return null;
                }
            }
        }
        SopReviewReasonReq reasonReq = new SopReviewReasonReq();
        reasonReq.setCurVersion(req.getCurVersion());
        reasonReq.setPreVersion(req.getPreVersion());
        reasonReq.setBusinessRange(req.getBusinessRange());
        reasonReq.setBusinessType(req.getBusinessType());
        reasonReq.setPlanProductName(req.getPlanProductName());
        reasonReq.setStartYearMonth(SopDateUtils.getYearMonth(req.getStartYearMonth()));
        reasonReq.setEndYearMonth(SopDateUtils.getYearMonth(req.getEndYearMonth()));
        String demandType = req.getDemandType();
        List<String> demandTypeList = new ArrayList<>();
        if (StringUtils.isNotBlank(demandType)){
            switch (demandType){
                case "退回":
                    demandTypeList.add(DemandTypeEnum.RETURN.name());
                    break;
                case "申领":
                    demandTypeList.add(DemandTypeEnum.NEW.name());
                    break;
            }
        }
        reasonReq.setDemandType(demandTypeList);
        List<SopReviewChangeReasonDO> all = reasonService.getAll(reasonReq);
        return ListUtils.transform(all, SopReviewReasonItem::transform);
    }

    @StoreRegister(name = "sop_overall_demand_reason_v2", desc = "sop大盘全量需求原因v2")
    public List<SopReviewReasonItem> sopOverallDemandReasonV2(Map<String, Object> params){
        SopReviewOriginV2Req req = (SopReviewOriginV2Req) params.get("req"); // 原始请求
        List<String> dims = req.getDims();
        // dims 限定只能是 业务类型 + 业务范围，并且顺序不能变
        if (ListUtils.isEmpty(dims) || dims.size() != 2 ||
                !Objects.equals(dims.get(0), SopReviewFieldsEnum.businessType.name()) ||
                !Objects.equals(dims.get(1), SopReviewFieldsEnum.businessRange.name())){
            return null;
        }
        SopReviewReasonV2Req reasonReq = new SopReviewReasonV2Req();
        reasonReq.setComputeType(req.getComputeType());
        reasonReq.setCurVersion(req.getCurVersion());
        reasonReq.setPreVersion(req.getPreVersion());
        reasonReq.setBusinessRange(req.getBusinessRange());
        reasonReq.setBusinessType(req.getBusinessType());
        reasonReq.setYearMonthType(req.getYearMonthType());
//        reasonReq.setStartYearMonth(SopDateUtils.getYearMonth(req.getStartYearMonth()));
//        reasonReq.setEndYearMonth(SopDateUtils.getYearMonth(req.getEndYearMonth()));
//        reasonReq.setShowFlag(ListUtils.newList(FlagType.YES.getCode())); // 展示
        // v2 不看需求类型
        List<SopReviewChangeReasonV2DO> all = reasonService.getAllV2(reasonReq);

        List<SopReviewReasonItem> ret = ListUtils.transform(all, SopReviewReasonItem::transform);

        // 只有云业务，则不需要查询 yunti_demand_review_memo 表
        List<String> businessType = req.getBusinessType();
        if (ListUtils.isNotEmpty(businessType) && !businessType.contains(SopBusinessType.SELF.getDesc())){
            return ret;
        }

        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        BasSopReviewVersionDO basSopReviewVersionDO = versionInfo.stream()
                .filter(item -> item.getVersion().equals(req.getCurVersion()))
                .findFirst()
                .orElse(null);
        BasSopReviewVersionDO preBasSopReviewVersionDO = versionInfo.stream()
                .filter(item -> item.getVersion().equals(req.getPreVersion()))
                .findFirst()
                .orElse(null);
        if (basSopReviewVersionDO != null && preBasSopReviewVersionDO != null){
            LocalDate startDate = basSopReviewVersionDO.getStartDate();
            LocalDate preStartDate = preBasSopReviewVersionDO.getStartDate();
            // 两个版本只能相差 1 周，否则不查询云梯需求原因
            if (!startDate.equals(preStartDate.plusDays(7))){
                return ret;
            }
            String yearWeek = basSopReviewVersionDO.getYearWeek();
            String yearMonthType = req.getYearMonthType();
            MemoTypeEnum memoTypeEnum = MemoTypeEnum.getByName(yearMonthType);
            if (memoTypeEnum != null){
                List<YuntiDemandReviewMemoDO> memoList = reasonService.getFromYuntiDemandReviewMemo(yearWeek, memoTypeEnum);
                if (ListUtils.isNotEmpty(memoList)){
                    Map<String, String> bgAliasMap = dictService.getBgProductAliasMap(BgProductAliasType.BG);
                    List<SopReviewReasonItem> transform = ListUtils.transform(memoList,
                            item -> SopReviewReasonItem.transform(item, bgAliasMap));
                    List<String> businessRange = req.getBusinessRange();
                    if (ListUtils.isNotEmpty(businessRange)){
                        Set<String> set = new HashSet<>(businessRange);
                        transform = transform.stream().filter(item -> set.contains(item.getBusinessRange())).collect(
                                Collectors.toList());
                    }
                    if (ListUtils.isNotEmpty(transform)){
                        ret.addAll(transform);
                    }
                    // 这里没必要排序了，自研来的默认放到大盘后面
//                    ret.sort(Comparator.comparing(SopReviewReasonItem::getOrder));
                }
            }
        }

        return ret;
    }

    @StoreRegister(name = "sop_overall_demand", desc = "sop大盘全量需求")
    public List<SopReviewItem> sopOverallDemand(Map<String, Object> params) {
        SopReviewOriginReq req = (SopReviewOriginReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isExecuted = (Boolean) params.get("is_executed"); // 是否为已执行

        SopReviewDataOriginReq storeReq = SopReviewDataOriginReq.transform(req);
        // 默认筛选
        // 1.资源池：自研池和(空值)
        // 2.剔除部门：算力平台
        storeReq.setResourcePoolType(ListUtils.newList(SopResourcePool.SELF.getDesc(), Constant.EMPTY_VALUE_STR));
        storeReq.setIgnoreDeptName(ListUtils.newList("算力平台"));

        String index = BooleanUtils.isTrue(isExecuted)? SopReviewIndexType.EXECUTED.getName():SopReviewIndexType.NOT_EXECUTED.getName();
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        storeReq.setIsExcel(isExcel);
        doCommon(req, index, versionInfo, isCurrent, storeReq);
        List<SopReviewItem> deviceDemand = getDataService.getOverallDemand(storeReq);
        dealReturn(req,deviceDemand);
        return deviceDemand;
    }

    @StoreRegister(name = "sop_overall_demand_v2", desc = "sop大盘全量需求v2")
    public List<SopReviewItem> sopOverallDemandV2(Map<String, Object> params) {
        SopReviewOriginReq req = (SopReviewOriginReq) params.get("req"); // 原始请求
        Boolean isCurrent = (Boolean) params.get("is_current"); // 是否为当前版本
        Boolean isNew = (Boolean) params.get("is_new"); // 是否为新增

        SopReviewDataOriginReq storeReq = SopReviewDataOriginReq.transform(req);
        // 默认筛选
        // 1.资源池：自研池和(空值)
        // 2.剔除部门：算力平台
        storeReq.setResourcePoolType(ListUtils.newList(SopResourcePool.SELF.getDesc(), Constant.EMPTY_VALUE_STR));
        storeReq.setIgnoreDeptName(ListUtils.newList("算力平台"));
        String index = SopReviewIndexType.TOTAL.getName(); // 总需求
        Boolean isExcel = (Boolean) params.get("is_excel"); // 是否为导出
        List<BasSopReviewVersionDO> versionInfo = (List<BasSopReviewVersionDO>) params.get("versionInfo"); // 版本信息
        storeReq.setIsExcel(isExcel);
        String demandType = isNew ? SopReviewDemandType.NEW.getName(): SopReviewDemandType.RETURN.getName();
        doCommon(req, index, demandType , versionInfo, isCurrent, storeReq);
        List<SopReviewItem> deviceDemand = getDataService.getOverallDemand(storeReq);
        return deviceDemand;
    }

    /**
     * 退回展示正数
     * @param req 请求，用来判断是否为退回
     * @param data 数据集，用来处理退回指标为正数
     */
    private void dealReturn(SopReviewCommonReq req,List<SopReviewItem> data){
        String demandType = req.getDemandType();
        // 这里净增允许为负数，所以这里需要处理退回
        if (Objects.equals(demandType,"退回") && ListUtils.isNotEmpty(data)){
            for (SopReviewItem sopReviewItem : data) {
                // 台数
                BigDecimal num = sopReviewItem.getNum();
                sopReviewItem.setNum(num == null?null:num.abs());
                // 核数
                BigDecimal coreNum = sopReviewItem.getCoreNum();
                sopReviewItem.setCoreNum(coreNum == null?null:coreNum.abs());
                // gpu卡数
                BigDecimal gpuNum = sopReviewItem.getGpuNum();
                sopReviewItem.setGpuNum(gpuNum == null?null:gpuNum.abs());
            }
        }
    }

    private void doCommon(SopReviewCommonReq req, List<BasSopReviewVersionDO> versionInfo, Boolean isCurrent, SopReviewDataCommonReq storeReq) {
        this.doCommon(req,req.getIndexType(), versionInfo, isCurrent, storeReq);
    }

    private void doCommon(SopReviewCommonReq req, String index ,List<BasSopReviewVersionDO> versionInfo, Boolean isCurrent, SopReviewDataCommonReq storeReq) {
        doCommon(req, index, req.getDemandType(), versionInfo, isCurrent, storeReq);
    }

    /**
     * 设置版本号 + 指标
     */
    private void doCommon(SopReviewCommonReq req, String index ,String demandType,List<BasSopReviewVersionDO> versionInfo, Boolean isCurrent, SopReviewDataCommonReq storeReq) {
        String version = BooleanUtils.isTrue(isCurrent) ? req.getCurVersion() : req.getPreVersion();
        if (versionInfo == null) {
            throw new ITException(String.format("该版本对应 sop 版本号不存在，无法查询：【%s】", version));
        }
        BasSopReviewVersionDO basSopReviewVersionDO = versionInfo.stream().filter(item -> item.getVersion().equals(version)).findFirst().orElse(null);
        if (basSopReviewVersionDO == null){
            throw new ITException(String.format("该版本对应 sop 版本号不存在，无法查询：【%s】", version));
        }
        storeReq.setVersionInfo(basSopReviewVersionDO);
        // 如果不是 导出，这里则会限制指标
        if (BooleanUtils.isNotTrue(storeReq.getIsExcel())){
            storeReq.setIndexWithType(index, demandType);
        }
    }

}
