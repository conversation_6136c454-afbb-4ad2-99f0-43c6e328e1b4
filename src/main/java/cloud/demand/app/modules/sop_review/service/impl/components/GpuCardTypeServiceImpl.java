package cloud.demand.app.modules.sop_review.service.impl.components;

import cloud.demand.app.modules.sop.entity.dict.DockerGpuCategoryStrategyDO;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop_review.model.clean.ICleanCoreComponentsBit;
import cloud.demand.app.modules.sop_review.service.ComponentsFieldService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service("gpuCardTypeServiceImpl")
public class GpuCardTypeServiceImpl implements ComponentsFieldService {

    @Resource
    private CommonDbHelper commonDbHelper;


    @Override
    public List<String> getEnumValues() {
        Map<String, DockerGpuCategoryStrategyDO> defaultVersionGpuInfo = commonDbHelper.getDefaultVersionGpuInfo();
        return defaultVersionGpuInfo.values().stream().map(DockerGpuCategoryStrategyDO::getGpuCardCategory).distinct()
                .collect(
                        Collectors.toList());
    }

    @Override
    public boolean hasEnumValues() {
        return true;
    }

    @Override
    public String[] getEnumValue(ICleanCoreComponentsBit bit) {
        return new String[]{bit.getGpuAbbr()};
    }
}
