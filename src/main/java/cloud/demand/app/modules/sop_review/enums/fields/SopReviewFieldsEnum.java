package cloud.demand.app.modules.sop_review.enums.fields;

import cloud.demand.app.modules.soe.model.fields.FieldEnum;
import cloud.demand.app.modules.soe.model.fields.FieldInfo;
import cloud.demand.app.modules.soe.model.fields.IYearMonth;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import yunti.boot.exception.ITException;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@AllArgsConstructor
@Getter
public enum SopReviewFieldsEnum {
    indexYearMonth("年月",(obj)-> ((IIndexYearMonth) obj).getIndexYearMonth()),
    businessType("业务类型", (obj) -> ((IBusinessType) obj).getBusinessType()),
    businessRange("业务范围", (obj) -> ((IBusinessRange) obj).getBusinessRange()),
    deptName("业务部门", (obj) -> ((IDeptName) obj).getDeptName()),
    planProductName("规划产品", (obj) -> ((IPlanProductName) obj).getPlanProductName()),
    obsProjectType("项目类型", (obj) -> ((IObsProjectType) obj).getObsProjectType()),
    resourceType("资源类型", (obj) -> ((IResourceType) obj).getResourceType()),
    computeType("CPU/GPU", (obj) -> ((IComputeType) obj).getComputeType()),
    deviceType("设备类型", (obj) -> ((IDeviceType) obj).getDeviceType()),
    deviceFamily("物理机机型族", (obj) -> ((IDeviceFamily) obj).getDeviceFamily()),
    instanceFamily("CVM机型族", (obj) -> ((IInstanceFamily) obj).getInstanceFamily()),
    instanceType("实例类型", (obj) -> ((IInstanceType) obj).getInstanceType()),
    instanceModel("实例规格", (obj) -> ((IInstanceModel) obj).getInstanceModel()),
    resourcePoolType("CVM资源池", (obj) -> ((IResourcePoolType) obj).getResourcePoolType()),
    gpuAbbr("GPU卡型", (obj) -> ((IGpuAbbr) obj).getGpuAbbr()),
    customhouseTitle("境内外", (obj) -> ((ICustomhouseTitle) obj).getCustomhouseTitle()),
    countryName("国家", (obj) -> ((ICountryName) obj).getCountryName()),
    cityName("城市", (obj) -> ((ICityName) obj).getCityName()),
    cpuVender("CPU平台", (obj) -> ((ICpuVender) obj).getCpuVender()),
    intellectNetworkCardType("智能网卡", (obj) -> ((IIntellectNetworkCardType) obj).getIntellectNetworkCardType()),
    technicalClass("技术分类", (obj) -> ((ITechnicalClass) obj).getTechnicalClass()),
    coreComponents("核心部件", (obj) -> ((ICoreComponents) obj).getCoreComponents()),
    componentsField("部件字段", (obj) -> ((IComponentsField) obj).getComponentsField()),
    enumValue("字段枚举", (obj) -> ((IEnumValue) obj).getEnumValue()),

    // =============== 不展示的维度 ================
    cpuAbbr("cpu型号",(obj) -> ((ICpuAbbr) obj).getCpuAbbr()),
    memoryVolume("内存容量",(obj) -> ((IMemoryVolume) obj).getMemoryVolume()),
    diskAbbr("硬盘1缩写",(obj) -> ((IDiskAbbr) obj).getDiskAbbr()),
    disk2Abbr("硬盘2缩写",(obj) -> ((IDisk2Abbr) obj).getDisk2Abbr()),
    diskVolume("硬盘1规格",(obj) -> ((IDiskVolume) obj).getDiskVolume()),
    disk2Volume("硬盘2规格",(obj) -> ((IDisk2Volume) obj).getDisk2Volume()),
    nicAbbr("网卡缩写",(obj) -> ((INicAbbr) obj).getNicAbbr()),
    ssdAbbr("SSD1缩写",(obj) -> ((ISsdAbbr) obj).getSsdAbbr()),
    ssd2Abbr("SSD2缩写",(obj) -> ((ISsd2Abbr) obj).getSsd2Abbr()),
    ssd3Abbr("SSD3缩写",(obj) -> ((ISsd3Abbr) obj).getSsd3Abbr()),


    ;

    private final String name; // 字段名

    private final Function<Object, Object> fieldGetter; // getter

    private final boolean showAble; // 是否给前端展示

    SopReviewFieldsEnum(String name,Function<Object, Object> fieldGetter){
        this(name,fieldGetter,true);
    }

    private final static Map<String,Function<Object,Object>> fieldMap = Arrays.stream(values())
            .collect(Collectors.toMap(Enum::name, SopReviewFieldsEnum::getFieldGetter));



    /**
     * 取的是 Enum::name
     *
     * @return 字段集合
     */
    public static Set<String> getFieldNames() {
        return Arrays.stream(SopReviewFieldsEnum.values()).map(Enum::name).collect(Collectors.toSet());
    }

    /** 只看展示的维度 */
    public static List<FieldInfo> getFields() {
        List<FieldInfo> ret = new ArrayList<>();
        for (SopReviewFieldsEnum value : values()) {
            if (value.showAble){
                ret.add(new FieldInfo(value.name(), value.getName()));
            }
        }
        return ret;
    }

    /** 根据字段名称获取字段值 */
    public static <T> Object getValue(String fieldName,T t){
        Function<Object, Object> fieldGetter = fieldMap.get(fieldName);
        if (fieldGetter == null){
            throw new ITException(String.format("字段找不到，字段名称：【%s】", ObjectUtils.defaultIfNull(fieldName,"null")));
        }
        return fieldGetter.apply(t);
    }
}
