package cloud.demand.app.modules.sop_util_v2.service;

import cloud.demand.app.modules.sop_util_v2.dto.req.SopCloudDemandReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopGpuReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopIMSDeviceReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopIMSReq;
import cloud.demand.app.modules.sop_util_v2.dto.resp.SopCloudDemandResp;
import cloud.demand.app.modules.sop_util_v2.dto.resp.SopGpuResp;
import cloud.demand.app.modules.sop_util_v2.dto.resp.SopIMSDeviceResp;
import cloud.demand.app.modules.sop_util_v2.dto.resp.SopIMSResp;

public interface SopUtilV2ReportService {

    /** 大盘进销存*/
    SopIMSResp queryIMS(SopIMSReq req);

    /** 大盘物理机采购汇总 */
    SopIMSDeviceResp queryIMSDevice(SopIMSDeviceReq req);

    /** GPU采购交付汇总 */
    SopGpuResp queryGpu(SopGpuReq req);

    /** 云需求 */
    SopCloudDemandResp queryCloudDemand(SopCloudDemandReq req);
}
