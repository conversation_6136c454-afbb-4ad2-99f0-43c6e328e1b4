package cloud.demand.app.modules.sop_util_v2.process.store;

import cloud.demand.app.modules.sop_review.service.SopReviewCommonService;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopGpuReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopIMSDeviceReq;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopPurchaseCommonReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims_device.ForecastNetReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims_device.HostDeliveryReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims_device.OrderPlacedReq;
import cloud.demand.app.modules.sop_util_v2.model.req.ims_device.TotalPurchaseReq;
import cloud.demand.app.modules.sop_util_v2.process.item.SopGpuItem;
import cloud.demand.app.modules.sop_util_v2.process.item.SopIMSDeviceItem;
import cloud.demand.app.modules.sop_util_v2.process.item.SopPurchaseCommonItem;
import cloud.demand.app.modules.sop_util_v2.service.SopGetDataIMSDeviceService;
import cloud.demand.app.modules.sop_util_v2.service.SopUtilV2CleanService;
import cloud.demand.app.modules.sop_util_v2.service.SopUtilV2DictService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Predicate;

/**
 * 仓库
 */
@Component
@StoreRegisterClient
public class SopIMSDeviceAndGpuStoreFactory {

    /**
     * 资源存量
     */
    @Resource
    private SopGetDataIMSDeviceService dataService;
    @Resource
    private SopUtilV2CleanService cleanService;

    @Resource
    private SopUtilV2DictService dictService;

    @StoreRegister(name = "sop_gpu_init", desc = "初始化")
    public void initGpu(Map<String, Object> params) {
        SopGpuReq req = (SopGpuReq) params.get("req");
        List<Predicate<SopGpuItem>> filter = (List<Predicate<SopGpuItem>>)params.get("filter");

        // 过滤条件
        filter.addAll(req.buildFilter());

        // 版本日期转版本号
        Map<String,String> versionMap = (Map<String,String>)params.get("versionMap");
        Set<String> versionList = new HashSet<>(ListUtils.newList(
                req.getStartDeviceVersion(),req.getEndDeviceVersion())
        );
        Map<String, String> versionByDate = dictService.getVersionByDate(versionList);

        for (String versionDate : versionList) {
            String version = versionByDate.get(versionDate);
            if (version == null){
                throw new IllegalArgumentException("cubes物理机版本不存在："+versionDate);
            }
            versionMap.put(versionDate,version);
        }

    }
    @StoreRegister(name = "sop_ims_device_init", desc = "初始化")
    public void initIMSDevice(Map<String, Object> params) {
        SopIMSDeviceReq req = (SopIMSDeviceReq) params.get("req");
        List<Predicate<SopIMSDeviceItem>> filter = (List<Predicate<SopIMSDeviceItem>>)params.get("filter");

        // 过滤条件
        filter.addAll(req.buildFilter());

        // 版本日期转版本号
        Map<String,String> versionMap = (Map<String,String>)params.get("versionMap");
        Set<String> versionList = new HashSet<>(ListUtils.newList(
                req.getStartDeviceVersion(),req.getEndDeviceVersion())
        );
        Map<String, String> versionByDate = dictService.getVersionByDate(versionList);

        for (String versionDate : versionList) {
            String version = versionByDate.get(versionDate);
            if (version == null){
                throw new IllegalArgumentException("cubes物理机版本不存在："+versionDate);
            }
            versionMap.put(versionDate,version);
        }

    }

    @StoreRegister(name = "sop_mis_device_purchase", desc = "采购总量")
    public List<SopPurchaseCommonItem> purchase(Map<String, Object> params) {
        // 入参
        SopPurchaseCommonReq req = (SopPurchaseCommonReq) params.get("req");
        boolean isGpu = BooleanUtils.isTrue((Boolean) params.get("is_gpu"));
        boolean isStart = BooleanUtils.isTrue((Boolean) params.get("is_start"));
        Map<String,String> versionMap = (Map<String,String>) params.get("versionMap");
        List<Predicate<SopPurchaseCommonItem>> filter = (List<Predicate<SopPurchaseCommonItem>>)params.get("filter");
        boolean isPreYear = true; // 是否为上一年

        // 查询参数
        TotalPurchaseReq totalPurchaseReq = TotalPurchaseReq.transform(req,versionMap,isPreYear,isStart);

        List<SopPurchaseCommonItem> ret = new ArrayList<>();

        // 查询采购总量
        if (!isGpu){
            dataService.getAllPurchaseData(totalPurchaseReq).forEach(item->{
                SopIMSDeviceItem retItem = new SopIMSDeviceItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // cvm字段
                retItem.setCpuPlatform(item.getCpuPlatform());
                retItem.setCpuType(item.getCpuType());
                retItem.setDeviceFamily(item.getDeviceFamily());
                retItem.setCoreNum(item.getCoreNum());

                ret.add(retItem);
            });
        }else {
            dataService.getGpuPurchaseData(totalPurchaseReq).forEach(item->{
                SopGpuItem retItem = new SopGpuItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // gpu字段
                retItem.setGpuAbbr(item.getGpuAbbr());
                retItem.setGpuFamily(item.getGpuFamily());
                retItem.setGpuNum(item.getGpuNum());
                ret.add(retItem);
            });
        }

        // 清洗
        cleanAndFilter(ret,filter);

        return ret;
    }

    @StoreRegister(name = "sop_mis_device_order_placed", desc = "已下单量")
    public List<SopPurchaseCommonItem> orderPlaced(Map<String, Object> params) {
        // 入参
        SopPurchaseCommonReq req = (SopPurchaseCommonReq) params.get("req");
        boolean isGpu = BooleanUtils.isTrue((Boolean) params.get("is_gpu"));
        boolean isStart = BooleanUtils.isTrue((Boolean) params.get("is_start"));
        Map<String,String> versionMap = (Map<String,String>) params.get("versionMap");
        List<Predicate<SopPurchaseCommonItem>> filter = (List<Predicate<SopPurchaseCommonItem>>)params.get("filter");
        boolean isPreYear = BooleanUtils.isTrue((Boolean) params.get("is_pre_year"));
        String startYearMonth = null;
        String endYearMonth = null;
        if (isPreYear){
            Integer preYear = req.getPreYear();
            startYearMonth = preYear + "-01";
            endYearMonth = preYear + "-12";
        }

        // 查询参数
        OrderPlacedReq storeReq = OrderPlacedReq.transform(req,versionMap,isStart);

        if (startYearMonth != null){
            storeReq.setStartYearMonth(startYearMonth);
            storeReq.setStartDate(startYearMonth + "-01");
        }
        if (endYearMonth != null){
            storeReq.setEndYearMonth(endYearMonth);
            storeReq.setEndDate(DateUtils.formatDate(LocalDate.parse(endYearMonth + "-01").with(TemporalAdjusters.lastDayOfMonth())));
        }

        List<SopPurchaseCommonItem> ret = new ArrayList<>();

        // 查询已下单量
        if (!isGpu){
            dataService.getAllOrderData(storeReq).forEach(item->{
                SopIMSDeviceItem retItem = new SopIMSDeviceItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // cvm字段
                retItem.setCpuPlatform(item.getCpuPlatform());
                retItem.setCpuType(item.getCpuType());
                retItem.setDeviceFamily(item.getDeviceFamily());
                retItem.setCoreNum(item.getCoreNum());

                ret.add(retItem);
            });
        }else {
            dataService.getGpuOrderData(storeReq).forEach(item->{
                SopGpuItem retItem = new SopGpuItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // gpu字段
                retItem.setGpuAbbr(item.getGpuAbbr());
                retItem.setGpuFamily(item.getGpuFamily());
                retItem.setGpuNum(item.getGpuNum());
                ret.add(retItem);
            });
        }

        // 清洗
        cleanAndFilter(ret,filter);

        return ret;
    }

    @StoreRegister(name = "sop_mis_device_forecast_net", desc = "净预测量")
    public List<SopPurchaseCommonItem> forecastNet(Map<String, Object> params) {
        // 入参
        SopPurchaseCommonReq req = (SopPurchaseCommonReq) params.get("req");
        boolean isGpu = BooleanUtils.isTrue((Boolean) params.get("is_gpu"));
        boolean isStart = BooleanUtils.isTrue((Boolean) params.get("is_start"));
        Map<String,String> versionMap = (Map<String,String>) params.get("versionMap");
        List<Predicate<SopPurchaseCommonItem>> filter = (List<Predicate<SopPurchaseCommonItem>>)params.get("filter");

        // 查询参数
        ForecastNetReq storeReq = ForecastNetReq.transform(req,versionMap,isStart);

        List<SopPurchaseCommonItem> ret = new ArrayList<>();
        // 查询净预测
        if (!isGpu){
            dataService.getAllForecastNetData(storeReq).forEach(item->{
                SopIMSDeviceItem retItem = new SopIMSDeviceItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // cvm字段
                retItem.setCpuPlatform(item.getCpuPlatform());
                retItem.setCpuType(item.getCpuType());
                retItem.setDeviceFamily(item.getDeviceFamily());
                retItem.setCoreNum(item.getCoreNum());

                ret.add(retItem);
            });
        }else {
            dataService.getGpuForecastNetData(storeReq).forEach(item->{
                SopGpuItem retItem = new SopGpuItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // gpu字段
                retItem.setGpuAbbr(item.getGpuAbbr());
                retItem.setGpuFamily(item.getGpuFamily());
                retItem.setGpuNum(item.getGpuNum());
                ret.add(retItem);
            });
        }

        // 清洗
        cleanAndFilter(ret,filter);
        return ret;
    }

    @StoreRegister(name = "sop_mis_device_host_delivery", desc = "已交付量")
    public List<SopPurchaseCommonItem> hostDelivery(Map<String, Object> params) {

        // 入参
        SopPurchaseCommonReq req = (SopPurchaseCommonReq) params.get("req");
        boolean isGpu = BooleanUtils.isTrue((Boolean) params.get("is_gpu"));
        boolean isStart = BooleanUtils.isTrue((Boolean) params.get("is_start"));
        Map<String,String> versionMap = (Map<String,String>) params.get("versionMap");
        List<Predicate<SopPurchaseCommonItem>> filter = (List<Predicate<SopPurchaseCommonItem>>)params.get("filter");

        // 查询参数
        HostDeliveryReq storeReq = HostDeliveryReq.transform(req,versionMap,isStart);

        List<SopPurchaseCommonItem> ret = new ArrayList<>();

        // 查询已交付量
        if (!isGpu){
            dataService.getAllHostDeliveryData(storeReq).forEach(item->{
                SopIMSDeviceItem retItem = new SopIMSDeviceItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // cvm字段
                retItem.setCpuPlatform(item.getCpuPlatform());
                retItem.setCpuType(item.getCpuType());
                retItem.setDeviceFamily(item.getDeviceFamily());
                retItem.setCoreNum(item.getCoreNum());

                ret.add(retItem);
            });
        }else {
            dataService.getGpuHostDeliveryData(storeReq).forEach(item->{
                SopGpuItem retItem = new SopGpuItem();
                // 公共字段
                retItem.setYearMonth(item.getYearMonth());
                retItem.setCustomhouseTitle(item.getCustomhouseName());
                retItem.setCountryName(item.getCountryName());
                retItem.setNum(item.getNum());
                retItem.setDeviceType(item.getDeviceType());
                retItem.setCampus(item.getCampus());

                // gpu字段
                retItem.setGpuAbbr(item.getGpuAbbr());
                retItem.setGpuFamily(item.getGpuFamily());
                retItem.setGpuNum(item.getGpuNum());
                ret.add(retItem);
            });
        }


        // 清洗
        cleanAndFilter(ret,filter);
        return ret;
    }


    /**
     * 清洗
     * @param ret 数据
     */
    private void cleanAndFilter(List<SopPurchaseCommonItem> ret,List<Predicate<SopPurchaseCommonItem>> filter){
        Iterator<SopPurchaseCommonItem> iterator = ret.iterator();
        while(iterator.hasNext()){
            SopPurchaseCommonItem item = iterator.next();
            cleanService.cleanPurchase(item);
            for (Predicate<SopPurchaseCommonItem> predicate : filter) {
                if (!predicate.test(item)){
                    iterator.remove();
                    break;
                }
            }
        }
    }
}
