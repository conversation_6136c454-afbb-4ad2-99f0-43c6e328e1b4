package cloud.demand.app.modules.sop_util_v2.model.req.ims_device;

import cloud.demand.app.modules.soe.model.sql.ColumnParam;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.ITException;

import java.util.List;

@Data
public class CommonReq {
    @ColumnParam("stat_time")
    private String statTime; // 切片时间

    @ColumnParam("hedging_version")
    private String hedgingVersion; // 对冲版本

    @ColumnParam("start_year_month")
    private String startYearMonth;

    @ColumnParam("end_year_month")
    private String endYearMonth;

    @ColumnParam("start_date")
    private String startDate; // 起始时间

    @ColumnParam("end_date")
    private String endDate; // 结束时间

    private List<String> groupBy; // 分组字段

    public void setAndCheckHedgingVersion(String hedgingVersion) {
        setHedgingVersion(hedgingVersion);
        if (StringUtils.isBlank(hedgingVersion)) {
            throw new ITException(String.format("对冲版本号为空, 版本日期：【%s】", getStatTime()));
        }
    }
}
