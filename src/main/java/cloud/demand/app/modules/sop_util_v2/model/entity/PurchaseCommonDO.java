package cloud.demand.app.modules.sop_util_v2.model.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** 采购公共底数 */
@Data
public class PurchaseCommonDO {

    /** 年月 */
    @Column("year_month")
    private String yearMonth;


    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseName;

    /** 国家 */
    @Column("country_name")
    private String countryName;

    @Column("campus")
    private String campus;

    /** 设备类型 */
    @Column("device_type")
    private String deviceType;


    /** 台数 */
    @Column("num")
    private BigDecimal num;
}
