package cloud.demand.app.modules.sop_util_v2.process.item;

import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.sop_util_v2.enums.fields.SopIMSDeviceAndGpuFieldEnum;
import cloud.demand.app.modules.sop_util_v2.model.clean.ICleanCpuInfo;
import lombok.Data;

import java.math.BigDecimal;

/** 采购公共 item */
@Data
public class SopPurchaseCommonItem extends CommonAbstractReportData implements IRegionClean{
    /** 年月 */
    private String yearMonth;

    /** 国家 */
    private String countryName;

    /** 国内外 */
    private String customhouseTitle;

    private String campus;

    /** 设备类型 */
    private String deviceType;

    /** 台数 */
    private BigDecimal num;

    @Override
    public <T> Object getValue(String fieldName, T t) {
        return SopIMSDeviceAndGpuFieldEnum.getValue(fieldName,t);
    }

    @Override
    public BigDecimal getValue_() {
        return num;
    }

    @Override
    public String getAreaName() {
        return null;
    }

    @Override
    public String getRegionName() {
        return null;
    }

    @Override
    public String getZoneName() {
        return null;
    }

    @Override
    public void setRegionName(String regionName) {

    }

    @Override
    public void setAreaName(String areaName) {

    }

    @Override
    public void setZoneName(String zoneName) {

    }
}
