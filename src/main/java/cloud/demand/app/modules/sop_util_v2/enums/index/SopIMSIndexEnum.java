package cloud.demand.app.modules.sop_util_v2.enums.index;

import cloud.demand.app.modules.soe.model.compute.IComputeMethod;
import cloud.demand.app.modules.sop_util.enums.IStoreNameEnum;
import cloud.demand.app.modules.sop_util_v2.enums.Constant;
import cloud.demand.app.modules.sop_util_v2.enums.SopUtilV2StoreEnum;
import com.pugwoo.wooutils.collect.MapUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.function.Supplier;


@AllArgsConstructor
@Getter
public enum SopIMSIndexEnum implements ICommonIndexEnum {

    // =============== 大盘进销存 ================
    // =============== 整体 ================

    IMS_RESOURCE_PRE_YEAR("'整体@' + req.shortPreYear + '年底资源存量'", SopUtilV2StoreEnum.SOP_IMS_RESOURCE, () -> MapUtils.of("is_pre_year_end_day", true)),

    // =============== 开始 + 结束的 SOP 需求评审数据（底层有合并 req，放一起是为了时间上相差很短，减少等待时间） ================

    // =============== 开始 ================
    IMS_FORECAST_ADD_START("'开始值@' + req.shortYear + '年全年增量预测'", SopUtilV2StoreEnum.SOP_IMS_FORECAST, () -> MapUtils.of("is_start", true, "is_add", true)),

    IMS_FORECAST_RETURN_START("'开始值@' + req.shortYear + '年全年退回预测'", SopUtilV2StoreEnum.SOP_IMS_FORECAST, () -> MapUtils.of("is_start", true, "is_add", false)),

    IMS_DEMAND_START("'开始值@' + req.shortYear + '年已申领量'", SopUtilV2StoreEnum.SOP_IMS_DEMAND, () -> MapUtils.of("is_start", true)),

    IMS_RETURN_START("'开始值@' + req.shortYear + '年已退回量'", SopUtilV2StoreEnum.SOP_IMS_RETURN, () -> MapUtils.of("is_start", true)),

    // =============== 结束 ================
    IMS_FORECAST_ADD_END("'结束值@' + req.shortYear + '年全年增量预测'", SopUtilV2StoreEnum.SOP_IMS_FORECAST, () -> MapUtils.of("is_start", false, "is_add", true)),

    IMS_FORECAST_RETURN_END("'结束值@' + req.shortYear + '年全年退回预测'", SopUtilV2StoreEnum.SOP_IMS_FORECAST, () -> MapUtils.of("is_start", false, "is_add", false)),

    IMS_DEMAND_END("'结束值@' + req.shortYear + '年已申领量'", SopUtilV2StoreEnum.SOP_IMS_DEMAND, () -> MapUtils.of("is_start", false)),

    IMS_RETURN_END("'结束值@' + req.shortYear + '年已退回量'", SopUtilV2StoreEnum.SOP_IMS_RETURN, () -> MapUtils.of("is_start", false)),

    // =============== 开始值 ================
    IMS_RESOURCE_CUR_START("'开始值@' + req.shortYear + '年当前资源存量'", SopUtilV2StoreEnum.SOP_IMS_RESOURCE, () -> MapUtils.of("is_start", true)),

    IMS_RESOURCE_DIFF_START("'开始值@' + req.shortYear + '年至今资源净增量'", new SopIMSIndexEnum[]{IMS_RESOURCE_CUR_START, IMS_RESOURCE_PRE_YEAR}, Constant.sub,"当前资源存量 - 上一年底资源存量"),

    IMS_DELIVERY_START("'开始值@' + req.shortYear + '年已交付量'", SopUtilV2StoreEnum.SOP_IMS_DELIVERY, () -> MapUtils.of("is_start", true)),



    // =============== 结束值 ================

    IMS_RESOURCE_CUR_END("'结束值@' + req.shortYear + '年当前资源存量'", SopUtilV2StoreEnum.SOP_IMS_RESOURCE, () -> MapUtils.of("is_start", false)),

    IMS_RESOURCE_DIFF_END("'结束值@' + req.shortYear + '年至今资源净增量'", new SopIMSIndexEnum[]{IMS_RESOURCE_CUR_END, IMS_RESOURCE_PRE_YEAR}, Constant.sub,"当前资源存量 - 上一年底资源存量"),

    IMS_DELIVERY_END("'结束值@' + req.shortYear + '年已交付量'", SopUtilV2StoreEnum.SOP_IMS_DELIVERY, () -> MapUtils.of("is_start", false)),



    // =============== 环比波动量 ================

    IMS_RESOURCE_CUR_DIFF("'环比波动量@' + req.shortYear + '年当前资源存量'",
            new SopIMSIndexEnum[]{IMS_RESOURCE_CUR_END,IMS_RESOURCE_CUR_START}, Constant.sub),

    IMS_RESOURCE_DIFF_DIFF("'环比波动量@' + req.shortYear + '年至今资源净增量'",
            new SopIMSIndexEnum[]{IMS_RESOURCE_DIFF_END,IMS_RESOURCE_DIFF_START}, Constant.sub),

    IMS_FORECAST_ADD_DIFF("'环比波动量@' + req.shortYear + '年全年增量预测'",
            new SopIMSIndexEnum[]{IMS_FORECAST_ADD_END,IMS_FORECAST_ADD_START}, Constant.sub),

    IMS_FORECAST_RETURN_DIFF("'环比波动量@' + req.shortYear + '年全年退回预测'",
            new SopIMSIndexEnum[]{IMS_FORECAST_RETURN_END,IMS_FORECAST_RETURN_START}, Constant.sub),

    IMS_DEMAND_DIFF("'环比波动量@' + req.shortYear + '年已申领量'",
            new SopIMSIndexEnum[]{IMS_DEMAND_END,IMS_DEMAND_START}, Constant.sub),

    IMS_DELIVERY_DIFF("'环比波动量@' + req.shortYear + '年已交付量'",
            new SopIMSIndexEnum[]{IMS_DELIVERY_END,IMS_DELIVERY_START}, Constant.sub),

    IMS_RETURN_DIFF("'环比波动量@' + req.shortYear + '年已退回量'",
            new SopIMSIndexEnum[]{IMS_RETURN_END,IMS_RETURN_START}, Constant.sub),


    // =============== 整体 ================
    /** 预估=当前+全年预测增量+已退回-已申领量-全年预测退回 */
    IMS_RESOURCE_CUR_YEAR("'整体@' + req.shortYear + '年底预估资源存量'",
            new SopIMSIndexEnum[]{IMS_RESOURCE_PRE_YEAR, IMS_FORECAST_ADD_END, IMS_FORECAST_RETURN_END}, Constant.add2AndSub,"上一年底资源存量 + 本年全年增量预测 - 本年全年退回预测"),

    // =============== 同比波动量 ================
    IMS_RESOURCE_PRE_YEAR_DIFF("'同比波动量@同比波动量'",
            new SopIMSIndexEnum[]{IMS_RESOURCE_CUR_YEAR,IMS_RESOURCE_PRE_YEAR}, Constant.sub),

    IMS_RESOURCE_CUR_YEAR_DIFF("'同比波动量@同比增长比率'",
            new SopIMSIndexEnum[]{IMS_RESOURCE_PRE_YEAR_DIFF,IMS_RESOURCE_PRE_YEAR}, Constant.divide),
    ;

    private final String name; // 指标名称
    private final IStoreNameEnum storeName; // 指标仓库

    private final boolean isHide; // 是否隐藏

    private final Supplier<Map<String, Object>> indexParam; // 指标参数

    private final SopIMSIndexEnum[] depIndex; // 依赖指标

    private final IComputeMethod compute; // 指标计算

    private final String desc; // 描述

    SopIMSIndexEnum(String name, SopIMSIndexEnum[] storeName, IComputeMethod compute) {
        this(name, null, false, null, storeName, compute,null);
    }

    SopIMSIndexEnum(String name, SopIMSIndexEnum[] storeName, IComputeMethod compute,String desc) {
        this(name, null, false, null, storeName, compute,desc);
    }

    SopIMSIndexEnum(String name, IStoreNameEnum storeName) {
        this(name, storeName, false, null, null, null,null);
    }

    SopIMSIndexEnum(String name, IStoreNameEnum storeName, boolean isHide) {
        this(name, storeName, isHide, null, null, null,null);
    }

    SopIMSIndexEnum(String name, IStoreNameEnum storeName, Supplier<Map<String, Object>> indexMap) {
        this(name, storeName, false, indexMap, null, null,null);
    }

    SopIMSIndexEnum(String name, IStoreNameEnum storeName, Supplier<Map<String, Object>> indexMap,String desc) {
        this(name, storeName, false, indexMap, null, null,desc);
    }

}
