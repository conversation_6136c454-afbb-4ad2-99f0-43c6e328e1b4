package cloud.demand.app.modules.sop_util_v2.model.req.cloud_demand;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util_v2.dto.req.SopCloudDemandReq;
import cloud.demand.app.modules.sop_util_v2.model.req.AllYearReportCommonReq;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

import java.time.LocalDate;

@Data
public class CloudDemandPurchaseReq extends AllYearReportCommonReq {
    public static CloudDemandPurchaseReq transform(SopCloudDemandReq req, boolean isStart) {
        CloudDemandPurchaseReq ret = new CloudDemandPurchaseReq();
        ret.setStartYearMonth(req.getStartYearMonth());
        ret.setEndYearMonth(req.getEndYearMonth());
        String version = isStart? req.getStartDemandVersion():req.getEndDemandVersion(); // 云需求用需求版本时间
        LocalDate localDate = SoeCommonUtils.minDate(LocalDate.now().plusDays(-1), DateUtils.parseLocalDate(version));
        version = DateUtils.format(localDate);
        ret.setIntStatTime(version.replace("-",""));
        return ret;
    }
}
