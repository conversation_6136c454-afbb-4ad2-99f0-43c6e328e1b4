package cloud.demand.app.modules.operation_view.inventory_health.web;

import cloud.demand.app.modules.operation_view.inventory_health.dto.mck_restock.*;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthMckRestockService;
import cloud.demand.app.web.model.common.Result;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.Map;

@JsonrpcController("/inventory-health")
public class InventoryHealthMckRestockController {
    @Resource
    InventoryHealthMckRestockService mckRestockService;

    @RequestMapping
    public QueryMckRestockResp queryMckRestockReport(@JsonrpcParam QueryMckRestockReq req) {
        return mckRestockService.queryMckRestockReport(req);
    }

    @RequestMapping
    public QueryMckRestockInventoryDetailResp queryMckRestockReportInventoryDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        return mckRestockService.queryMckRestockReportInventoryDetail(req);
    }

    @RequestMapping
    public QueryMckRestockSupplyDetailResp queryMckRestockReportSupplyDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        return mckRestockService.queryMckRestockReportSupplyDetail(req);
    }

    @RequestMapping
    public QueryMckRestockForecastDetailResp queryMckRestockReportForecastDetail(@JsonrpcParam QueryMckRestockForecastReq req) {
        return mckRestockService.queryMckRestockReportForecastDetail(req);
    }

    /**
     * 缺口与运营动作相关联
     */
    @RequestMapping
    public QueryOperationActionDetailResp queryOperationActionDetail(@JsonrpcParam QueryMckRestockDetailReq req) {
        return mckRestockService.queryOperationActionDetail(req);
    }

    /**
     * 人工调整
     */
    @RequestMapping
    public Object mckRestockManualConfig(@JsonrpcParam ConfigureMckRestockManualConfigReq req) {
        return mckRestockService.configureMckRestockManualConfig(req);
    }


    @RequestMapping
    public QueryManualConfigDetailResp queryManualConfigDetail(@JsonrpcParam QueryManualConfigDetailReq req){
        return mckRestockService.queryManualConfigDetail(req);
    }




}
