package cloud.demand.app.modules.operation_view.operation_view2.entity;

import java.util.List;
import lombok.Data;

@Data
public class TCHouseCustomerData {

    /**
     * uin
     */
    private String uin;

    /**
     * appId
     */
    private String appId;

    /**
     * 集群信息
     */
    private InstanceInfo info;

    /**
     * cvm信息
     */
    private List<TotalCvms> cvmInfo;


    @Data
    public static class InstanceInfo{

        /**
         * 集群id
         */
        private String instanceId;

        /**
         * 集群名称
         */
        private String instanceName;

        /**
         * 集群状态
         */
        private String status;

        /**
         * 付费类型
         */
        private String payMode;

        /***
         * 创建时间
         */
        private String createTime;

        /**
         * 可用区
         */
        private String zone;


    }

    @Data
    public static class TotalCvms {

        /**
         * 地域
         */
        private String region;

        /**
         * 实例规格
         */
        private String instanceType;

        /**
         * cpu核数
         */
        private Integer cpuCore;

    }

}

