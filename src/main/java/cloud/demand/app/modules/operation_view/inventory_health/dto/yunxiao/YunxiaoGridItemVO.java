package cloud.demand.app.modules.operation_view.inventory_health.dto.yunxiao;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

@Data
@Table("dws_yunxiao_rubik_grid_df")
public class YunxiaoGridItemVO {
    @Column("instance_type")
    private String instanceType;

    @Column("zone_name")
    private String zoneName;

    @Column(value = "sum_cores", computed = "sum(cores)")
    private Integer sumCores;
}
