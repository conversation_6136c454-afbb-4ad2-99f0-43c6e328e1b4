package cloud.demand.app.modules.operation_view.operation_view2.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 安全库存阈值设置的请求体
 * By机型-可用区
 */
@Data
public class SafetyInvThresholdReq {

    @Valid
    List<Item> data;

    @Data
    @Accessors(chain = true)
    public static class Item {
        //  可用区
        @NotBlank(message = "可用区不能为空")
        private String zoneName;

        //  实例类型
        @NotBlank(message = "实例类型不能为空")
        private String instanceType;

        //  安全库存阈值
        @NotNull(message = "安全库存阈值不能为空")
        private Integer thresholdValue;
    }

}
