package cloud.demand.app.modules.operation_view.inventory_health.service;

import cloud.demand.app.modules.operation_view.inventory_health.dto.future.InventoryHealthFutureReq;
import cloud.demand.app.modules.operation_view.inventory_health.dto.future.InventoryHealthFutureResp;
import cloud.demand.app.modules.operation_view.inventory_health.dto.future.InventoryHealthFutureViewResp;

/**
 * 未来库存相关服务
 */
public interface InventoryHealthFutureService {

    /**
     * 查询库存信息概览数据
     */
    InventoryHealthFutureResp queryInventoryHealthFuture(InventoryHealthFutureReq req);

    /**
     * 查询需求预测-By行业数据
     */
    InventoryHealthFutureViewResp queryIndustryDemandForecastView(InventoryHealthFutureReq req);

    /**
     * 查询未来采购-By行业数据
     */
    InventoryHealthFutureViewResp queryIndustrySupplyPlanView(InventoryHealthFutureReq req);

}
