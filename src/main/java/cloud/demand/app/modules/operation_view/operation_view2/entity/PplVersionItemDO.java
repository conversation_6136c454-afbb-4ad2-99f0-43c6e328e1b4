package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Table("dwd_crp_ppl_item_version_cf")
public class PplVersionItemDO {
    @Column("version_code_date")
    private String versionCodeDate;

    @Column("begin_buy_date")
    private String beginBuyDate;

    @Column("end_buy_date")
    private String endBuyDate;

    @Column("instance_type")
    private String instanceType;

    @Column("customhouse_title")
    private String customhouseTitle;

    @Column("area_name")
    private String areaName;

    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    @Column("source")
    private String source;

    @Column("year")
    private Integer year;

    @Column("month")
    private Integer month;

    @Column(value = "total_core", computed = "sum(total_core)")
    private BigDecimal totalCore;
}
