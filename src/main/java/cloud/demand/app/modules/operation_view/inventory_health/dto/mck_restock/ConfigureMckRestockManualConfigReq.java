package cloud.demand.app.modules.operation_view.inventory_health.dto.mck_restock;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ConfigureMckRestockManualConfigReq {
    @NotNull
    private String date;
    @NotNull
    private String instanceType;
    @NotNull
    private String zoneName;
    @NotNull
    private int year;
    @NotNull
    private int week;

    private List<FieldItem> fields;

    @Data
    public static class FieldItem {
            private String field;
            private BigDecimal value;
            private String reason;
    }
}
