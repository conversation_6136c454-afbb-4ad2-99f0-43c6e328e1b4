package cloud.demand.app.modules.operation_view.operation_view_phase1.job;

import cloud.demand.app.modules.operation_view.operation_view_phase1.service.generate.OperationViewGenDataService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

@Slf4j
@Service
public class OperationViewTask {

    @Autowired
    private OperationViewGenDataService operationViewGenDataService;

    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
//    @Scheduled(cron = "0 20 9 * * ?")
    public void run() {
        log.info("begin GenOperationViewData");
        String yesterday = DateUtils.formatDate(DateUtils.addTime(new Date(), Calendar.DATE, -1));
        operationViewGenDataService.genAllDetailData(yesterday);
        log.info("end GenOperationViewData");
    }


}

