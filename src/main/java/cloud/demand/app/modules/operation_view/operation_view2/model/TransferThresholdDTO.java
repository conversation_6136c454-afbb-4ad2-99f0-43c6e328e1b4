package cloud.demand.app.modules.operation_view.operation_view2.model;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 调用云霄接口的入参
 */
@Data
@Accessors(chain = true)
public class TransferThresholdDTO {

    private String region;

    private String zone;

    //  境内
    private String customhouseTitle;

    //  区域
    private String areaName;

    // 地域
    private String regionName;

    // 可用区
    private String zoneName;

    private String instanceFamily;

    private Integer thresholdValue;

}
