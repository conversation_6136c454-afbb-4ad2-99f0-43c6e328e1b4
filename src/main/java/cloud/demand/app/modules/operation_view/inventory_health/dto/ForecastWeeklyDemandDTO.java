package cloud.demand.app.modules.operation_view.inventory_health.dto;

import lombok.Data;

/**
 * 13周预测净增需求DTO
 */
@Data
public class ForecastWeeklyDemandDTO extends CommonConditionDTO {

    private String date;

    private String customhouseTitle;

    private String areaName;

    private String regionName;

    private String zoneName;

    private String instanceType;

    /**
     * 13周预测需求-净增核心数
     */
    private Integer cores;

    /**
     * 13周预测需求-新增核心数
     */
    private Integer newCores;

    /**
     * 13周预测需求-退回核心数
     */
    private Integer returnCores;
}
