package cloud.demand.app.modules.operation_view.inventory_health.web;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.operation_view.inventory_health.dto.inventory_turnover.InventoryTurnoverReq;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryTurnoverService;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * ITO看板
 */
@Slf4j
@JsonrpcController("/inventory-turnover")
public class InventoryTurnoverController {

    @Resource
    InventoryTurnoverService inventoryTurnoverService;

    @RequestMapping
    public Object queryInventoryTurnoverReport(@JsonrpcParam InventoryTurnoverReq req) {
        if (req == null) {
            throw new WrongWebParameterException("请求参数不能为空");
        }
        return inventoryTurnoverService.queryInventoryTurnoverReport(req);
    }
}
