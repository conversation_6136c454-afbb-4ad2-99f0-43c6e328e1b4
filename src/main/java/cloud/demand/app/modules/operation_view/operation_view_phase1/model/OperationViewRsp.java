package cloud.demand.app.modules.operation_view.operation_view_phase1.model;

import cloud.demand.app.modules.operation_view.operation_view_phase1.vo.ReportOperationViewDetailVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class OperationViewRsp {

    private List<Item> data;

    private Map<String, String> weekInterval;

    @Data
    public static class Item {
        /** 产品类型 */
        private String productType;

        /** 库存材料类型(好差呆) */
        private String materialType;

        /** 线上线下 */
        private String lineType;

        /** 境内外 */
        private String customhouseTitle;

        /** 区域名 */
        private String areaName;

        /** 地域名 */
        private String regionName;

        /** 可用区名 */
        private String zoneName;

        /** 设备类型 */
        private String deviceType;

        /** 机型族 */
        private String deviceFamily;

        /** 线上库存量 */
        private BigDecimal invOnlineNum;

        /** 线下库存量 */
        private BigDecimal invOfflineNum;

        /** 总库存量 */
        private BigDecimal invTotalNum;

        /** 库存金额 */
        private BigDecimal invPrice;

        /** 库存占比 */
        private BigDecimal invRatio;

        /**月均库存，库存周转天数=月均库存/月均销售*/
        private BigDecimal monthAvgInv;

        /**月均销售，库存周转天数=月均库存/月均销售*/
        private BigDecimal monthAvgSale;

        /**最低库存*/
        private BigDecimal invMinNum;

        /** 缓冲库存 */
        private BigDecimal invCacheNum;

        /** 安全库存 */
        private BigDecimal invSafeNum;

        /** 冗余库存 */
        private BigDecimal invRedundancyNum;

        /**服务系数*/
        private BigDecimal serviceNum;

        /**需求标准差*/
        private BigDecimal demandSDNum;

        /**需求量W1*/
        private BigDecimal demandW1Num;

        /**需求量W2*/
        private BigDecimal demandW2Num;

        /**需求量W3*/
        private BigDecimal demandW3Num;

        /**需求量W4*/
        private BigDecimal demandW4Num;

        /**需求量W5*/
        private BigDecimal demandW5Num;

        /**需求量W6*/
        private BigDecimal demandW6Num;

        /**需求量W7*/
        private BigDecimal demandW7Num;

        /**需求量W8*/
        private BigDecimal demandW8Num;

        /**需求量W9*/
        private BigDecimal demandW9Num;

        /**需求量W10*/
        private BigDecimal demandW10Num;

        /**需求量W11*/
        private BigDecimal demandW11Num;

        /**需求量W12*/
        private BigDecimal demandW12Num;

        /**需求量W13*/
        private BigDecimal demandW13Num;

        /**周平均需求*/
        private BigDecimal demandAvgNum;

        /**交付sla*/
        private BigDecimal sla;

        /** CPU规格*/
        private String cpuCategory;

        /** 弹性备货配额 */
        private BigDecimal bufferSafetyInv;

        public static Item transform(ReportOperationViewDetailVO detailDO){
            Item item = new Item();
            item.setProductType(detailDO.getProductType());
            item.setMaterialType(detailDO.getMaterialType());
            item.setCustomhouseTitle(detailDO.getCustomhouseTitle());
            item.setAreaName(detailDO.getAreaName());
            item.setRegionName(detailDO.getRegionName());
            item.setZoneName(detailDO.getZoneName());
            item.setDeviceType(detailDO.getDeviceType());
            item.setDeviceFamily(detailDO.getDeviceFamily());
            item.setCpuCategory(detailDO.getCpuCategory());
            item.setLineType(detailDO.getLineType());
            return item;
        }
    }


}
