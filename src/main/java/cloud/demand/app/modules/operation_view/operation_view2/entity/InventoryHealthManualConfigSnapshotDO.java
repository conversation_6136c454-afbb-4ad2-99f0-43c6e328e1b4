
package cloud.demand.app.modules.operation_view.operation_view2.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 库存健康-安全库存阈值下发云产品 切片表
 */
@Data
@ToString
@Accessors(chain = true)
@Table("inventory_health_manual_config_snapshot")
public class InventoryHealthManualConfigSnapshotDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 任务结束时的时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 数据生成时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 地区名<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 地域名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 安全库存阈值<br/>Column: [threshold] */
    @Column(value = "num")
    private BigDecimal num;

    public static InventoryHealthManualConfigSnapshotDO transform(InventoryHealthManualConfigDO source){
        InventoryHealthManualConfigSnapshotDO inventoryHealthManualConfigSnapshotDO = new InventoryHealthManualConfigSnapshotDO();
        inventoryHealthManualConfigSnapshotDO.setCustomhouseTitle(source.getCustomhouseTitle());
        inventoryHealthManualConfigSnapshotDO.setAreaName(source.getAreaName());
        inventoryHealthManualConfigSnapshotDO.setRegionName(source.getRegionName());
        inventoryHealthManualConfigSnapshotDO.setZoneName(source.getZoneName());
        inventoryHealthManualConfigSnapshotDO.setInstanceType(source.getInstanceType());
        inventoryHealthManualConfigSnapshotDO.setNum(source.getNum());
        return inventoryHealthManualConfigSnapshotDO;

    }

    public static InventoryHealthManualConfigSnapshotDO transform(InventoryHealthManualConfigSnapshotDO source){
        InventoryHealthManualConfigSnapshotDO inventoryHealthManualConfigSnapshotDO = new InventoryHealthManualConfigSnapshotDO();
        inventoryHealthManualConfigSnapshotDO.setCustomhouseTitle(source.getCustomhouseTitle());
        inventoryHealthManualConfigSnapshotDO.setAreaName(source.getAreaName());
        inventoryHealthManualConfigSnapshotDO.setRegionName(source.getRegionName());
        inventoryHealthManualConfigSnapshotDO.setZoneName(source.getZoneName());
        inventoryHealthManualConfigSnapshotDO.setInstanceType(source.getInstanceType());
        inventoryHealthManualConfigSnapshotDO.setNum(source.getNum());
        inventoryHealthManualConfigSnapshotDO.setStatTime(source.getStatTime());
        return inventoryHealthManualConfigSnapshotDO;
    }
}