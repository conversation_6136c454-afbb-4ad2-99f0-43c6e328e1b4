package cloud.demand.app.modules.operation_view.inventory_health.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_new_turnover_inventory_data_log_df")
public class DwsNewTurnoverInventoryDataLogDfDO {

    /** 切片日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private String statTime;

    /** 订单编号<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 订单流程<br/>Column: [node_order_code_name] */
    @Column(value = "node_order_code_name")
    private String nodeOrderCodeName;

    /** 开始购买时间<br/>Column: [begin_buy_date] */
    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;


    /** 共识需求核心数<br/>Column: [total_cores] */
    @Column(value = "total_cores")
    private Integer totalCores;

    /** 已预扣核心数<br/>Column: [actual_pre_deduct_core] */
    @Column(value = "actual_pre_deduct_core")
    private Integer actualPreDeductCore;

    /** 未预扣核心数<br/>Column: [not_pre_deduct_cores] */
    @Column(value = "not_pre_deduct_cores")
    private Integer notPreDeductCores;

}
