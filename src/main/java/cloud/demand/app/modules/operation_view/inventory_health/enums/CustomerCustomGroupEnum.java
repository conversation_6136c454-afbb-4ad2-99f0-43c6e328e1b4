package cloud.demand.app.modules.operation_view.inventory_health.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import org.nutz.lang.Lang;

import java.util.List;
import java.util.Objects;

@Getter
public enum CustomerCustomGroupEnum {

    ALL("ALL", "ALL"),
    LIST("LIST", "名单客户"),                                   //  对历史算法生效
    REPORT("REPORT", "报备客户"),                               //  对历史算法生效
    LIST_REPORT("LIST_REPORT", "名单客户&报备客户"),             //  对历史算法生效
    MEDIUM_LONG_TAIL("MEDIUM_LONG_TAIL", "中长尾客户"),
    HEAD_NOT_ZLKHB("HEAD_NOT_ZLKHB", "头部-非战略客户部客户"),  //  对未来算法生效
    HEAD_ZLKHB("HEAD_ZLKHB", "头部-战略客户部客户");   //  对未来算法生效


    private String code;

    private String name;

    CustomerCustomGroupEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static List<String> getCustomerTabType(CustomerCustomGroupEnum e){
        //  单客户类型
        List<CustomerCustomGroupEnum> simpleList = Lang.list(LIST, REPORT, MEDIUM_LONG_TAIL);
        //  复合客户类型
        List<CustomerCustomGroupEnum> complexList = Lang.list(LIST_REPORT);
        if (simpleList.contains(e)){
            return Lang.list(e.getName());
        }
        if (complexList.contains(e)){
            return Lang.list(e.getName().split("&"));
        }
        if (Objects.equals(ALL, e)){
            return ListUtils.transform(simpleList, o -> o.getName());
        }
        return Lang.list();
    }

    public static CustomerCustomGroupEnum getByCode(String code) {
        for (CustomerCustomGroupEnum e : CustomerCustomGroupEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static CustomerCustomGroupEnum[] getHistoryAlgorithmList(){
        return new CustomerCustomGroupEnum[]{ALL, LIST, REPORT,LIST_REPORT, MEDIUM_LONG_TAIL};
    }



}
