package cloud.demand.app.modules.operation_view.operation_view2.service;

import cloud.demand.app.modules.operation_view.operation_view2.model.InstanceModelManualConfigReq;
import cloud.demand.app.modules.operation_view.operation_view2.model.SafetyInvManualReq;
import cloud.demand.app.web.model.common.Result;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 安全库存阈值相关的service接口
 */
public interface InstanceModelManualConfigService {

    /**
     * 安全库存阈值设置接口
     * @return
     */
    Result setManualValue(InstanceModelManualConfigReq req);

    /**
     * 安全库存阈值配置切片
     */
    void snapshotManualConfig(String date);


    /**
     * 根据日期查询安全库存阈值切片
     * @return Map: k: 可用区+机型 v:对应维度下的阈值
     */
    Map<String, BigDecimal> querySnapshotManual(String statTime);


}
