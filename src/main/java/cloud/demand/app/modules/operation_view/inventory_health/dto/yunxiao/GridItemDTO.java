package cloud.demand.app.modules.operation_view.inventory_health.dto.yunxiao;

import lombok.Data;

@Data
public class GridItemDTO {
    int burstMem;
    int cbsFlag; //:1
    int cpu; // :800
    String cpuMask; //: "[21, 22, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 61, 62, 63, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79]"
    String cpuMode; // :"custom"
    long createTime; // 1618458562000
    long days; //:46
    String deviceClass; // :"VSGPUNVIDIAV100X_3"
    long deviceId; // :414897477
    String disasterRecoverTag; // :"[]"
    int fileFlag; //:0
    long gridId; // :282113
    String gridOwner; // :"**********"
    String gridOwnerName; // :"北京猿力未来科技有限公司"
    String gridOwnerOrganizationName; // :"智慧行业二部"
    String gridOwnerShortName; // :"猿题库"
    String hostIp; // :"*************"
    String hypervisor; //:"kvm"
    long idcId; // :8531
    String innerSwitch; // :"BJ-LS-M101-A13-CE6865-LA25G-01"
    String instanceType; // :"GN10X.2XLARGE40"
    int isReservePackage; // :0
    boolean isSupportCvm; // :true
    boolean isSupportEks; //:true
    int lmFlag; //:1
    String matchMode; // :""
    String matchRule; // :""
    long mem; // :40960
    int netVersion; // :3
    String picoTag; // :""
    long picoTagSize; // :0
    int pinType; // :3
    String pool; // :"qcloud"
    String productCategory; // :""
    long rackId; // :338419
    String reserveMode; // :"reserved_longtime"
    String status; // :"idle"
    String subnet; // :"************"
    String svrVersion; // :"6.0.3"
    long updateTime; // :1689210161000
    String vDieQuota; // :"[]"
    String vNodeQuota; // :"[0, 800]"
    String vendorModel; // :"HUAWEI G560-NV"
    long zoneId; // :800005
}
