package cloud.demand.app.modules.operation_view.inventory_health.enums;

public enum InventoryHealthAlgorithm {
//    static {
//        FUNCTION_MAP.put("historyWeekPeak", Item::getHistoryWeekPeak);
//        FUNCTION_MAP.put("historyWeekDiff", Item::getHistoryWeekDiff);
//        FUNCTION_MAP.put("futureWeekPeak", Item::getFutureWeekPeak);
//    }
    HISTORY_WEEK_PEAK( "historyWeekPeak", "历史周峰"),
    HISTORY_WEEK_DIFF("historyWeekDiff", "历史周需求"),
    FUTURE_WEEK_PEAK("futureWeekPeak", "未来周需求"),
    HISTORY_WEEK_PEAK_FORECAST_WN("historyWeekPeakForecastWN", "MCK历史&预测需求");


    String code;
    String name;

    InventoryHealthAlgorithm(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameFromCode(String code) {
        for (InventoryHealthAlgorithm status : InventoryHealthAlgorithm.values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return null;
    }
}
