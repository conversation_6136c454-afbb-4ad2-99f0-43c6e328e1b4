package cloud.demand.app.modules.report_proxy.modules.task_run_log.service;

import cloud.demand.app.modules.report_proxy.entity.task.ReportTableGraphTaskVO;
import cloud.demand.app.modules.report_proxy.modules.task_run_log.web.TaskController.NoticeGraphReq;
import java.util.List;
import java.util.Map;

/** graph 任务 */
public interface GraphTaskService {

    /** 视图通知 */
    void noticeGraph(NoticeGraphReq req);

    /** 获取 graph 任务（带缓存） */
    List<ReportTableGraphTaskVO> getGraphTaskWithCache();

    /** 根据任务命名空间 + 任务名获取任务信息 */
    ReportTableGraphTaskVO getGraphTaskWithCache(String taskNamespace,String taskName);

}
