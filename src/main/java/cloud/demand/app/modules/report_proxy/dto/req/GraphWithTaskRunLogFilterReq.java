package cloud.demand.app.modules.report_proxy.dto.req;

import java.util.List;
import lombok.Data;

@Data
public class GraphWithTaskRunLogFilterReq extends GraphWithTaskRunLogReq{
    /** 只看固定输出表 */
    private List<String> outputTable;

    /** 只看固定输入表 */
    private List<String> inputTable;

    /** 层级，默认 1 层，过滤限制表关联血缘的层级，比如 level = 1，只会输出outputTable 的来源表和 inputTable 的输出表
     * */
    private Integer level = 1;

    /** 忽略基础信息：表信息，任务信息，日志信息，用于非首次查询，首次查询已经有了就可以忽略来减少 IO */
    private Boolean ignoreBaseInfo;
}
