package cloud.demand.app.modules.report_proxy.dto.resp;

import cloud.demand.app.modules.report_proxy.entity.graph.ReportTableGraphInfoDO;
import cloud.demand.app.modules.report_proxy.entity.table.ReportTableInfoDO;
import cloud.demand.app.modules.report_proxy.entity.task.ReportTableGraphTaskDO;
import cloud.demand.app.modules.report_proxy.entity.vo.TaskRunLogVO;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
public class TaskTableGraphResp {
    /** 图信息 */
    private ReportTableGraphInfoDO graphInfo;

    /** 任务记录 */
    private List<TaskRunLogVO> logList;

    /** 表信息 */
    private List<ReportTableInfoDO> tableList;

    /** 任务信息 */
    private List<ReportTableGraphTaskDO> taskList;

    /** 表边集合 */
    private List<EdgeInfo> tableEdgeList;

    /** 任务边集合 */
    private List<EdgeInfo> taskEdgeList;

    @Data
    @NoArgsConstructor
    public static class EdgeInfo{
        /** 来源 id */
        private Long inputId;
        private String inputName;
        /** 输出 id */
        private Long outputId;
        private String outputName;
        /** 状态 */
        private TaskStatus status;
        private Long statusLogId;
        /** 信息 */
        private String msg;
        /** 日志记录 */
        private List<TaskLog> taskLogList;

        public EdgeInfo(Long inputId, String inputName,Long outputId, String outputName)  {
            this.inputId = inputId;
            this.inputName = inputName;
            this.outputId = outputId;
            this.outputName = outputName;
        }
    }

    @NoArgsConstructor
    @Data
    public static class TaskLog{
        private Long taskId;
        private String name;
        private String namespace;
        private TaskStatus status;
        private Long statusLogId;
        private String msg;
        private List<Long> logIdList;

        public TaskLog(Long taskId, String name, String namespace) {
            this.taskId = taskId;
            this.name = name;
            this.namespace = namespace;
        }
    }
}
