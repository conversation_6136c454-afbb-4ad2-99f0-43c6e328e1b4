package cloud.demand.app.modules.report_proxy.entity.meta;

import cloud.demand.app.common.BaseDO;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 表字段组
 */
@Data
@ToString
@Table("table_column_group")
public class TableColumnGroupDO extends BaseDO {
    /** 组名称<br/>Column: [name] */
    @Column(value = "name")
    private String name;

    /** 组描述<br/>Column: [desc] */
    @Column(value = "desc")
    private String desc;

    /** 创建用户<br/>Column: [create_user] */
    @Column(value = "create_user")
    private String createUser;

    /** 更新用户<br/>Column: [update_user] */
    @Column(value = "update_user")
    private String updateUser;

    /** 乐观锁版本<br/>Column: [cas_version] */
    @Column(value = "cas_version")
    private Integer casVersion;
}
