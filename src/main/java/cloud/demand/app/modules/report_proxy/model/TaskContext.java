package cloud.demand.app.modules.report_proxy.model;

import cloud.demand.app.modules.report_proxy.anno.TaskRunSql;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportRegisterInfoDO;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportTableGraphInfoDO;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunLogDO;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunSqlDO;
import cloud.demand.app.modules.report_proxy.entity.monitor.TaskMonitorInfoDO;
import cloud.demand.app.modules.report_proxy.entity.table.ReportTableInfoDO;
import cloud.demand.app.modules.report_proxy.entity.task.ReportTableGraphTaskPointDO;
import cloud.demand.app.modules.report_proxy.entity.task.ReportTableGraphTaskVO;
import cloud.demand.app.modules.report_proxy.enums.TaskPointTypeEnum;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class TaskContext {

    /**
     * 环境
     */
    private Env env;

    /**
     * 日志信息
     */
    private CommonTaskRunLogDO logInfo;

    /**
     * sql 信息 (key：#序号)
     */
    private Map<String, CommonTaskRunSqlDO> sqlMap;

    private MonitorConfigInfo monitorInfoEnv;

    /**
     * 配置监控信息
     * @param monitorInfo 监控信息
     */
    public void buildMonitorConfigWithInfo(MonitorInfo monitorInfo) {
        if (monitorInfo != null) {
            // 1. 解析监控点信息
            ReportTableGraphTaskVO taskInfo = monitorInfo.getTaskListVO(); // 任务信息
            Map<Long, ReportTableInfoDO> tableIdMap = monitorInfo.getTableIdMap(); // 表信息
            List<TaskMonitorInfoDO> taskMonitorInfoList = monitorInfo.getTaskMonitorInfoList(); // 任务监控信息
            List<ReportTableGraphInfoDO> graphInfoList = monitorInfo.getGraphList(); // 图信息
            List<ReportRegisterInfoDO> registerList = monitorInfo.getRegisterList(); // 注册信息
            List<TaskMonitorInfoDO> preTaskList = new ArrayList<>();
            List<TaskMonitorInfoDO> postTaskList = new ArrayList<>();
            Map<String, List<Tuple2<TaskMonitorInfoDO, ReportTableGraphTaskPointDO>>> postTableMap = new HashMap<>();

            // 2. 转为监控运行环境信息
            MonitorConfigInfo monitorConfigInfo = new MonitorConfigInfo();
            monitorConfigInfo.setTableMap(tableIdMap);
            monitorConfigInfo.setTaskInfo(taskInfo);
            monitorConfigInfo.setPreTaskList(preTaskList);
            monitorConfigInfo.setPostTaskList(postTaskList);
            monitorConfigInfo.setPostTableMap(postTableMap);
            monitorConfigInfo.setGraphList(graphInfoList);
            monitorConfigInfo.setRegisterList(registerList);
            // 3. 填充环境信息，按照监控点类型分组
            for (TaskMonitorInfoDO mon : taskMonitorInfoList) {
                String taskPointType = mon.getTaskPointType();
                TaskPointTypeEnum pointType = TaskPointTypeEnum.getByName(taskPointType);
                if (pointType == null){
                    throw new IllegalArgumentException("不支持的监控点类型：" + taskPointType);
                }
                switch (pointType) {
                    case PRE_TASK:
                        // 任务前置
                        preTaskList.add(mon);
                        break;
                    case POST_TASK:
                        // 任务后置
                        postTaskList.add(mon);
                        break;
                    case POST_TABLE:
                        // 表后置
                        ReportTableGraphTaskPointDO point = taskInfo.getTaskPointList().stream()
                                .filter(item -> Objects.equal(item.getId(), mon.getTaskPointId())).findFirst().orElse(null);
                        if (point == null){
                            throw new IllegalArgumentException(String.format("任务监控点不存在，监控名称：【%s】,id：【%s】", mon.getName(), mon.getId()));
                        }
                        ReportTableInfoDO tableInfoDO = tableIdMap.get(point.getTableId());
                        if (tableInfoDO == null){
                            throw new IllegalArgumentException(String.format("表不存在，监控名称：【%s】,id：【%s】", mon.getName(), mon.getId()));
                        }
                        postTableMap.computeIfAbsent(tableInfoDO.getFullTable(), key -> new ArrayList<>()).add(new Tuple2<>(mon, point));
                        break;
                    default:
                        throw new IllegalArgumentException("不支持的监控点类型：" + taskPointType);
                }
            }
            this.monitorInfoEnv = monitorConfigInfo;
        }
    }


    @Data
    public static class MonitorConfigInfo {

        /**
         * 任务前置监控
         */
        private List<TaskMonitorInfoDO> preTaskList;
        /**
         * 任务后置监控
         */
        private List<TaskMonitorInfoDO> postTaskList;
        /**
         * 表后置监控
         */
        private Map<String, List<Tuple2<TaskMonitorInfoDO, ReportTableGraphTaskPointDO>>> postTableMap;
        /**
         * 任务信息
         */
        private ReportTableGraphTaskVO taskInfo;
        /**
         * 表信息 table_id --> table
         */
        private Map<Long, ReportTableInfoDO> tableMap;

        /** 图信息 */
        private List<ReportTableGraphInfoDO> graphList;

        /**
         * 注册信息
         */
        private List<ReportRegisterInfoDO> registerList;
    }

    @AllArgsConstructor
    @Data
    public static class Env {

        private boolean logCompleteSql; // 是否记录完整 sql
        private boolean logArgs; // 是否记录 sql入参
        private int fixedSeconds; // 心跳频率（秒）
        private List<String> ignoreClass; // 记录方法时跳过的指定类名
        private boolean logSql; // 是否记录 sql
        private List<String> ignoreTable; // 记录 sql 时跳过的指定表名

        public Env(TaskRunSql taskRunSql) {
            this.logArgs = taskRunSql.logArgs();
            this.logCompleteSql = taskRunSql.logCompleteSql();
            this.fixedSeconds = taskRunSql.fixedSeconds();
            this.ignoreClass = ListUtils.newList("TaskInterceptorAspect", "CrudDBHelperInterceptor");
            if (taskRunSql.ignoreClass().length > 0) {
                this.ignoreClass.addAll(ListUtils.newList(taskRunSql.ignoreClass()));
            }
            this.ignoreTable = ListUtils.newList("common_task_run_log","common_task_run_sql","simple_common_task","task_monitor_log","global_sql_args");
            if (taskRunSql.ignoreTable().length > 0) {
                this.ignoreTable.addAll(ListUtils.newList(taskRunSql.ignoreTable()));
            }
            this.logSql = taskRunSql.logSql();
        }
    }
}
