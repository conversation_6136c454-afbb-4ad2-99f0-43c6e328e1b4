package cloud.demand.app.modules.report_proxy.entity.table;

import cloud.demand.app.common.utils.EntityCompareUtils.CompareField;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 报表字段信息
 */
@Data
@ToString
@Table("report_table_column_info")
public class ReportTableColumnInfoDO {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 报表表id<br/>Column: [table_id] */
    @Column(value = "table_id")
    @CompareField
    private Long tableId;

    /** 字段顺序<br/>Column: [position] */
    @Column(value = "position")
    @CompareField
    private Long position;

    /** 字段名称<br/>Column: [name] */
    @Column(value = "name")
    @CompareField
    private String name;

    /** 字段类型<br/>Column: [type] */
    @Column(value = "type")
    @CompareField
    private String type;

    /** 字段默认值<br/>Column: [default_value] */
    @Column(value = "default_value")
    @CompareField
    private String defaultValue;

    /** 字段描述<br/>Column: [comment] */
    @Column(value = "comment")
    @CompareField
    private String comment;

    /** 是否是分区键 0-否 1-是<br/>Column: [is_in_partition_key] */
    @Column(value = "is_in_partition_key")
    @CompareField
    private Integer isInPartitionKey;

    /** 是否是排序键 0-否 1-是<br/>Column: [is_in_sorting_key] */
    @Column(value = "is_in_sorting_key")
    @CompareField
    private Integer isInSortingKey;

    /** 是否是主键  0-否 1-是<br/>Column: [is_in_primary_key] */
    @Column(value = "is_in_primary_key")
    @CompareField
    private Integer isInPrimaryKey;

    /** 是否是索引键（非主键） 0-否 1-是<br/>Column: [is_in_sampling_key] */
    @Column(value = "is_in_sampling_key")
    @CompareField
    private Integer isInSamplingKey;


    /**
     * 创建的时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private LocalDateTime createTime;

    /**
     * 创建用户<br/>Column: [create_user]
     */
    @Column(value = "create_user", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String createUser;

    /**
     * 更新的时间<br/>Column: [update_time]
     */
    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private LocalDateTime updateTime;

    /**
     * 更新用户<br/>Column: [update_user]
     */
    @Column(value = "update_user", updateValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()", insertValueScript = "cloud.demand.app.common.utils.LoginUtils.getUserName()")
    private String updateUser;

    /**
     * 乐观锁版本<br/>Column: [cas_version]
     */
    @Column(value = "cas_version")
    private Integer casVersion;

}