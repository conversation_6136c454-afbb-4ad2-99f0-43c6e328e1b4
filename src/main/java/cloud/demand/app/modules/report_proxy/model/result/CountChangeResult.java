package cloud.demand.app.modules.report_proxy.model.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CountChangeResult implements ISqlResult{
    private String column;
    private Double changeRate;
    private int argIndex;
    public static CountChangeResult getDefault(int argIndex){
        // 0.1 表示 10% 的变化
        return new CountChangeResult("_count", 0.1,argIndex);
    }

    @Override
    public String getResultScript() {
        String script = "(args[0].${column} - args[1].${column})/args[0].${column}";
        return script.replace("${column}",column);
    }

    @Override
    public String getExpectResultScript() {
        return "java.lang.Math.abs(args["+argIndex+"]) <= " + changeRate;
    }
}
