package cloud.demand.app.modules.report_proxy.anno;

import java.lang.annotation.Documented;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

/** 任务拦截 */
@Target({java.lang.annotation.ElementType.METHOD})
@Documented
@Inherited
@Retention(java.lang.annotation.RetentionPolicy.RUNTIME)
public @interface TaskRunSql {

    /** 命名空间 */
    String namespace() default "#";

    /** 任务名脚本 args标识入参，target 代表被代理的类 */
    String nameScript() default "";

    /** 任务关键字脚本  args标识入参，target 代表被代理的类 */
    String keyScript() default "";

    /** 扩展脚本  args标识入参，target 代表被代理的类 */
    String extScript() default "";

    /** 心跳间隔时间 */
    int fixedSeconds() default 30;

    /** 忽略的方法 */
    String[] ignoreClass() default {};

    /** 记录 完整sql，默认不记录完整 sql，只对 sql 进行分析来源和去向表 */
    boolean logCompleteSql() default true;

    /** 是否记录 sql 参数，默认不记录 */
    boolean logArgs() default false;

    /** 是否记录 sql */
    boolean logSql() default true;

    /** 忽略的表名，logSql = true时，该参数才会生效，忽略的时候按照contains 处理，不是 equals，即模糊匹配 */
    String[] ignoreTable() default {};

}
