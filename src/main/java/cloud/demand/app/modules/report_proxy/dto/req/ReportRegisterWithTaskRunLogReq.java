package cloud.demand.app.modules.report_proxy.dto.req;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024/12/18 10:46
 */
@Data
public class ReportRegisterWithTaskRunLogReq {

    public static final String prefix = "report_info@";

    private TaskRunLogReq logReq;

    private ReportRegisterReq reportRegisterReq;

    public GraphWithTaskRunLogReq transform() {
        GraphWithTaskRunLogReq graphWithTaskRunLogReq = new GraphWithTaskRunLogReq();
        graphWithTaskRunLogReq.setLogReq(this.getLogReq());
        graphWithTaskRunLogReq.setName(getGraphName());
        graphWithTaskRunLogReq.setDesc(this.getReportRegisterReq().getDesc());
        return graphWithTaskRunLogReq;
    }

    public String getGraphName() {
        return prefix + this.getReportRegisterReq().getName();
    }
}
