package cloud.demand.app.modules.report_proxy.entity.config;

import cloud.demand.app.modules.report_proxy.enums.Constant;
import cloud.demand.app.modules.report_proxy.utils.ReportUtils;
import com.google.common.base.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupNameParam implements IGroupName{
    private String group;
    private String name;

    public boolean isAll(){
        return Objects.equal(group, Constant.ALL) && Objects.equal(name,Constant.ALL);
    }

    public boolean isMatch(String group,String name){
        return isMatchGroup(group) && isMatchName(name);
    }

    public boolean isMatchGroup(String group){
        return ReportUtils.matchWithParam(this.group, group);
    }


    public boolean isMatchName(String name){
        return ReportUtils.matchWithParam(this.name, name);
    }
}
