package cloud.demand.app.modules.report_proxy.web;

import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyAndBatchEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyAndEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportProxyReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportScriptInfoReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportScriptProxyAndBatchEvalReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportSqlAnalysisSqlReq;
import cloud.demand.app.modules.report_proxy.dto.req.ReportSqlReq;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportAnalysisSqlResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportBatchEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportProxyReqScheduleResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportScriptBatchEVALResp;
import cloud.demand.app.modules.report_proxy.dto.resp.ReportSqlResp;
import cloud.demand.app.modules.report_proxy.service.ReportProxyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.mvel2.MVEL;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@Tag(name = "报表代理", description = "报表请求代理")
@JsonrpcController("/report/proxy")
public class ReportProxyController {

    @Resource
    private ReportProxyService reportProxyService;


    @Operation(summary = "报表代理")
    @RequestMapping
    public Object proxyReq(@JsonrpcParam @Valid ReportProxyReq req){
        return reportProxyService.proxyReq(req);
    }

    @Operation(summary = "报表 sql 分析")
    @RequestMapping
    public ReportAnalysisSqlResp sqlInfo(@JsonrpcParam @Valid ReportSqlAnalysisSqlReq req){
        return reportProxyService.sqlInfoWithProxy(req);
    }

    @Operation(summary = "代理 sql 查询")
    @RequestMapping
    public ReportSqlResp proxySql(@JsonrpcParam @Valid ReportSqlReq req){
        return reportProxyService.proxySql(req);
    }

    @Operation(summary = "DBHelper列表")
    @RequestMapping
    public List<String> listDBHelper(){
        return reportProxyService.listDBHelper();
    }

    @Operation(summary = "报表代理并解析")
    @RequestMapping
    public ReportEVALResp proxyReqAndEval(@JsonrpcParam @Valid ReportProxyAndEvalReq req){
        return reportProxyService.proxyReqAndEval(req);
    }

    @Operation(summary = "批量报表代理并解析")
    @RequestMapping
    public ReportBatchEVALResp proxyReqAndBatchEval(@JsonrpcParam @Valid ReportProxyAndBatchEvalReq req){
        return reportProxyService.proxyReqAndBatchEval(req);
    }

    @Operation(summary = "批量脚本报表代理并解析")
    @RequestMapping
    public TreeMap<String,List<String>> proxyScript(@JsonrpcParam @Valid ReportScriptInfoReq req){
        return reportProxyService.proxyScript(req);
    }

    @Operation(summary = "批量脚本报表代理并解析")
    @RequestMapping
    public ReportScriptBatchEVALResp proxyScriptReqAndBatchEval(@JsonrpcParam @Valid ReportScriptProxyAndBatchEvalReq req){
        return reportProxyService.proxyScriptReqAndBatchEval(req);
    }

    @Operation(summary = "报表代理定时任务信息")
    @RequestMapping
    public ReportProxyReqScheduleResp proxyReqScheduleInfo(){
        return reportProxyService.proxyReqScheduleInfo();
    }
}
