package cloud.demand.app.modules.report_proxy.utils;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.utils.FiberUtils;
import cloud.demand.app.modules.order.dto.resp.ConsensusDemandDetailDTO;
import cloud.demand.app.modules.report_proxy.entity.config.GroupNameParam;
import cloud.demand.app.modules.report_proxy.entity.config.IGroupName;
import cloud.demand.app.modules.report_proxy.enums.Constant;
import cloud.demand.app.modules.report_proxy.interceptor.CrudDBHelperInterceptor;
import cloud.demand.app.modules.report_proxy.interceptor.SelectDBHelperInterceptor;
import cloud.demand.app.modules.report_proxy.interceptor.SelectDBHelperInterceptor.MethodInfo;
import com.google.common.base.Objects;
import com.google.gson.Gson;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.dbhelper.utils.ScriptUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.mvel2.MVEL;
import org.springframework.aop.support.AopUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import yunti.boot.web.jsonrpc.JsonrpcController;

/** 报表工具类 */
public class ReportUtils {

    // 定义禁止的关键字（不区分大小写）
    private static final Pattern FORBIDDEN_KEYWORDS = Pattern.compile(
            "\\b(ALTER|DROP|DELETE|CREATE|INSERT|UPDATE|TRUNCATE|RENAME)\\b",
            Pattern.CASE_INSENSITIVE | Pattern.UNICODE_CASE
    );

    public static boolean isSafeSelectQuery(String sql) {
        // 检查是否包含禁止的关键字
        boolean hasForbidden = FORBIDDEN_KEYWORDS.matcher(sql).find();
        // 检查是否以 SELECT 开头（简单逻辑，按需调整）
        boolean isSelect = sql.trim().toUpperCase().startsWith("SELECT");
        return !hasForbidden && isSelect;
    }

    public static Object eval(String script, Object t){
        if (script.matches(".*(Runtime|exec|System\\.).*")) {
            throw new IllegalArgumentException("非法表达式");
        }
        return ScriptUtils.getValueFromScript(t, false, script);
    }

    public static Boolean evalToBoolean(String script, Object t){
        if (script.matches(".*(Runtime|exec|System\\.).*")) {
            throw new IllegalArgumentException("非法表达式");
        }
        Object ret = ScriptUtils.getValueFromScript(t, false, script);
        return (ret instanceof Boolean && BooleanUtils.isTrue((Boolean) ret)) || Objects.equal(ret, "true");
    }

    /**
     * 通过 params 过滤 args
     * @param args sql 变量集合
     * @param params 参数集合
     * @param <T> 泛型
     * @return
     */
    public static <T extends IGroupName>  List<T> filterWithParams(List<T> args, List<GroupNameParam> params){
        List<T> ret = new ArrayList<>();
        if (ListUtils.isEmpty(args) || ListUtils.isEmpty(params)){
            return ret;
        }
        // 是否为全部
        boolean hasAll = params.stream().anyMatch(GroupNameParam::isAll);
        if (hasAll){
            return args;
        }
        for (T arg : args) {
            boolean match = false;
            // 任意匹配即可
            for (GroupNameParam param : params) {
                if (param.isMatch(arg.getGroup(),arg.getName())){
                    match = true;
                    break;
                }
            }
            if (match){
                ret.add(arg);
            }
        }
        return ret;
    }

    /**
     * 参数匹配
     * @param param * 或 t.*等参数
     * @param value 值
     * @return
     */
    public static boolean matchWithParam(String param, String value){
        if (Objects.equal(param, Constant.ALL)){
            return true;
        }
        if (Objects.equal(param, value)){
            return true;
        }
        if (param.contains(Constant.ALL)){
            // 替换 * 为正则
            String regex = "^" + param.replace(".", "\\.").replace(Constant.ALL,"[A-Za-z1-9_\\-.]*") + "$";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(value);
            return matcher.find();
        }
        return false;
    }

    /** 比较获取最小 */
    public static <T> T compareMin(T t1, T t2, Comparator<T> comparable) {
        if (t1 == null){
            return t2;
        }
        if (t2 == null){
            return t1;
        }
        return comparable.compare(t1, t2) > 0 ? t2 : t1;
    }

    /** 比较获取最大 */
    public static <T> T compareMax(T t1, T t2, Comparator<T> comparable) {
        if (t1 == null){
            return t2;
        }
        if (t2 == null){
            return t1;
        }
        return comparable.compare(t1, t2) > 0 ? t1 : t2;
    }

    /**
     * 替换 sql 中的 参数
     * @param sql sql
     * @param regex 正则
     * @param value 值
     * @return
     */
    public static String replaceArgs(String sql,String regex,String value){
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(sql);
        StringBuilder sbr = new StringBuilder();
        int start = 0;
        while (matcher.find()){
            sbr.append(sql, start, matcher.start());
            sbr.append(value);
            start = matcher.end();
        }
        sbr.append(sql, start, sql.length());
        return sbr.toString();
    }

    public static boolean match(String sql,String regex){
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(sql);
        return matcher.find();
    }

    /**
     * 这个是给 proxy sql 专用的
     * @return 执行方法信息
     */
    public static List<MethodInfo> genMethodInfo(){
        return genMethodInfo(null, ListUtils.newList("CrudDBHelperInterceptor","SelectDBHelperInterceptor"), "cloud.demand.app","ReportProxyServiceImpl",false);
    }


    /**
     * 拼接方法，格式：类名#方法名(参数)，如果参数和类名有共同前缀则：前缀:类名#方法名(参数)
     * @param className 类名
     * @param methodName 方法名
     * @param params 参数名集合
     * @return
     */
    public static String appendMethod(String className,String methodName,List<String> params){
        StringBuilder sbr = new StringBuilder();
        // 有参数，提取参数和类名的公共前缀
        if (ListUtils.isNotEmpty(params)){
            String[] split = className.split("\\.");
            int start = 0,end = split.length - 1;
            // 二分找前缀
            while(start <= end){
                int mid = (start + end) / 2;
                StringBuilder startWith = new StringBuilder();
                for (int i = 0; i <= mid; i++) {
                    startWith.append(split[i]);
                    if (i != mid){
                        startWith.append(".");
                    }
                }
                String prefix = startWith.toString();
                boolean isStartWith = true;
                for (String param : params) {
                    if (!param.startsWith(prefix)){
                        isStartWith = false;
                        break;
                    }
                }
                if (isStartWith){
                    start ++;
                }else {
                    end --;
                }
            }
            StringBuilder prefix = new StringBuilder();
            String prefixStr = null;
            if (end >= 0){
                // 前缀拼接
                for (int i = 0; i <= end; i++) {
                    prefix.append(split[i]);
                    if (i != end){
                        prefix.append(".");
                    }
                }
                prefixStr = prefix.toString();
                sbr.append(prefixStr);
                sbr.append(":");
            }
            // className 非公共前缀
            if (end < split.length - 1){
                for (int i = end + 1; i < split.length; i++) {
                    sbr.append(split[i]);
                    if (i != split.length - 1){
                        sbr.append(".");
                    }
                }
            }
            // 方法名拼接
            sbr.append("#").append(methodName);
            sbr.append("(");
            // 参数拼接，去除前缀
            for (int i = 0; i < params.size(); i++) {
                String param = params.get(i);
                if (prefixStr != null){
                    param = param.replace(prefixStr, "");
                }
                if (param.startsWith(".")){
                    param = param.substring(1);
                }
                sbr.append(param);
                if (i != params.size() - 1){
                    sbr.append(",");
                }
            }
            sbr.append(")");
        }else {
            // 无参数 className#methodName 即可
            sbr.append(className).append("#").append(methodName);
        }
        return sbr.toString();
    }

    /**
     * 解析方法
     * @param method
     * @return
     */
    public static MethodInfoForProxy parseMethod(String method){
        if (StringUtils.isBlank(method)){
            return null;
        }
        String prefix = "";
        {
            // 类名和参数类名是否有统一前缀
            int i = method.indexOf(":");
            if (i >= 0){
                prefix = method.substring(0, i);
                method = method.substring(i + 1);
            }
        }

        int i = method.indexOf("#");
        if (i < 0){
            return null;
        }

        if (StringUtils.isNotBlank(prefix)){
            prefix = prefix + ".";
        }

        String className = prefix + method.substring(0, i); // 类名
        String methodName = method.substring(i + 1); // 方法名，包括参数
        List<String> params = new ArrayList<>();

        {
            // 解析参数
            int paramStart = methodName.indexOf("(");
            if (paramStart >= 0){
                String paramStr = methodName.substring(paramStart + 1, methodName.length() - 1);
                String[] split = paramStr.split(",");
                for (String param : split) {
                    params.add(prefix + param.trim());
                }
                methodName = methodName.substring(0, paramStart);
            }
        }

        MethodInfoForProxy methodInfo = new MethodInfoForProxy();
        methodInfo.setClassName(className);
        methodInfo.setParams(params);
        methodInfo.setMethodName(methodName);
        return methodInfo;
    }

    @Data
    public static class MethodInfoForProxy{
        private String className;
        private String methodName;
        private List<String> params;
    }

    /**
     * 获取执行方法信息
     * @return 执行方法信息
     */
    public static List<MethodInfo> genMethodInfo(Thread curThread, List<String> ignoreClass, String startClass, String endClass, boolean onlyFirst){
        if (curThread == null){
            curThread = Thread.currentThread();
        }
        StackTraceElement[] stackTrace = curThread.getStackTrace();
        List<MethodInfo> ret = new ArrayList<>();
        for (int i = 3; i < stackTrace.length; i++) {
            StackTraceElement stackTraceElement = stackTrace[i];
            // 只记录业务方法
            String className = stackTraceElement.getClassName();
            String methodName = stackTraceElement.getMethodName();
            // 如果到了这个类，说明已经结束了（代理类即代理起点）
            if (StringUtils.isNotBlank(endClass) && className.endsWith(endClass)){
                break;
            }
            // 忽略的 class
            boolean ignore = false;
            if (ListUtils.isNotEmpty(ignoreClass)){
                for (String aClass : ignoreClass) {
                    if (className.endsWith(aClass)){
                        ignore = true;
                        break;
                    }
                }
            }
            if (ignore){
                continue;
            }
            if ((StringUtils.isBlank(startClass) || className.startsWith(startClass)) && !className.contains("$") && !methodName.contains("$")){
                MethodInfo methodInfo = new MethodInfo();
                methodInfo.setClassName(stackTraceElement.getClassName());
                methodInfo.setMethodName(stackTraceElement.getMethodName());
                methodInfo.setLineNumber(stackTraceElement.getLineNumber());
                ret.add(methodInfo);
                if (onlyFirst){
                    break;
                }
            }
        }
        return ret;
    }

    /**
     * 获取执行方法信息
     * @param script 脚本
     * @param t 参数
     * @return
     */
    public static String getValueFromScript(String script,Object t){
        Object valueFromScript = ScriptUtils.getValueFromScript(t, true, script);
        return valueFromScript == null ? "" : String.valueOf(valueFromScript);
    }

    /**
     * 生产 key
     * @param script 脚本
     * @param pjp 任务切片
     * @return
     */
    public static String generateKey(String script, ProceedingJoinPoint pjp) {
        String key = "";
        if (StringUtils.isBlank(script)){
            return key;
        }
        Map<String, Object> context = new HashMap<>();
        context.put("args", pjp.getArgs()); // 类型是Object[]
        context.put("target", pjp.getTarget()); // 代理类

        try {
            Object result = MVEL.eval(script, context);
            if (result != null) { // 返回结果为null等价于keyScript为空字符串
                key = result.toString();
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        return key;
    }

    /**
     * 默认心跳 30s 执行一次，最大时间为 1 小时
     * 执行 beat 方法，beat 方法返回 false 时，自动结束
     * @param proceedFunc 执行方法
     * @param beatFunc beat 方法
     * @param <T> 返回值
     * @return 返回值
     */
    public static <T> T beat(Supplier<T> proceedFunc,Supplier<Boolean> beatFunc){
        return beat(proceedFunc, 30, beatFunc, 1);
    }

    /**
     * 执行 beat 方法，beat 方法返回 false 时，自动结束
     * @param proceedFunc 执行方法
     * @param fixedSeconds 执行间隔
     * @param beatFunc beat 方法
     * @param maxHours 最大执行时间
     * @param <T> 返回值
     * @return 返回值
     */
    public static <T> T beat(Supplier<T> proceedFunc, int fixedSeconds, Supplier<Boolean> beatFunc, int maxHours){
        Thread thread = Thread.currentThread();
        AtomicBoolean run = new AtomicBoolean(true);
        // 1 小时后自动结束
        maxHours = Math.min(maxHours,3); // 最多 3 小时
        long end = System.currentTimeMillis() + maxHours * 60 * 60 * 1000L;
        int wait = Math.max(5,fixedSeconds); // 至少等 5 秒
        FiberUtils.getExecutor().submit(() -> {
            // 任务结束或者 1 个小时时间到了，自动结束
            while (run.get() && System.currentTimeMillis() <= end && thread.isAlive()){
                // 等待
                try {
                    TimeUnit.SECONDS.sleep(wait);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                Boolean beatResult = beatFunc.get();
                if (BooleanUtils.isFalse(beatResult)){
                    // 如果 beat 方法返回 false，则退出
                    break;
                }
            }
        });
        try {
            return proceedFunc.get();
        }finally {
            run.set(false);
        }
    }

    /**
     * 获取拦截器，统一创建入口，方便调整
     * @return
     */
    public static SelectDBHelperInterceptor getInterceptor(SpringJdbcDBHelper helper){
        return new CrudDBHelperInterceptor(helper);
    }

    /**
     * ?占位符替换
     * @param sql sql
     * @param args sql 变量
     * @return 替换变量后的 sql
     */
    public static String replaceArgs(String sql,List<Object> args){
        if (ListUtils.isEmpty(args) || StringUtils.isBlank(sql)){
            return sql;
        }
        // 按 ? 对 sql 进行分割
        String[] split = sql.split("\\?");
        StringBuilder sbr = new StringBuilder();
        sbr.append(split[0]);
        int index = 1;
        for (Object arg : args) {
            // 如果参数是集合，则需要特殊处理
            if (arg instanceof Collection){
                Collection<?> argV = (Collection<?>) arg;
                if (argV.size() > 0){
                    Object next = argV.iterator().next();
                    // 字符串默认带上单引号
                    if (next instanceof String){
                        sbr.append("'");
                        sbr.append(String.join("','",(Collection<String>)argV));
                        sbr.append("'");
                    }else {
                        // 非字符串直接用 append 拼接，底层逻辑是 String.valueOf
                        Iterator<?> iterator = argV.iterator();
                        if (iterator.hasNext()){
                            Object nx = iterator.next();
                            sbr.append(nx);
                            if (iterator.hasNext()){
                                sbr.append(",");
                            }
                        }
                    }
                }else {
                    sbr.append("?");
                }
            }else {
                if (arg instanceof String){
                    sbr.append("'").append(arg).append("'");
                }else {
                    sbr.append(arg);
                }
            }
            // 兜底处理，避免报错
            if (index < split.length){
                sbr.append(split[index++]);
            }
        }
        // 如果还有剩余的，则?占位符不做处理
        while (index < split.length){
            sbr.append("?");
            sbr.append(split[index++]);
        }
        return sbr.toString();
    }

    /**
     * 获取报表的controller层方法，key：controller的path，controller 的 method
     * @return Map
     */
    public static Map<String, Map<String,ReportMethod>> getReportMethods() {
        // 拉取所有 bean 下面是 controller 的
        Map<String, Object> webMap = SpringUtil.getBeansWithAnnotation(RestController.class);
        List<ReportController> reportControllers = new ArrayList<>();
        for (Object value : webMap.values()) {
            Class<?> targetClass = AopUtils.getTargetClass(value);
            // 目前只支持按 JsonrpcController 注解的来判断，后续可以保底用RestController的
            JsonrpcController annotation = targetClass.getAnnotation(JsonrpcController.class);
            if (annotation != null){
                String[] paths = annotation.value();
                if (paths != null && paths.length > 0){
                    reportControllers.add(new ReportController(paths, value, targetClass));
                }
            }
        }
        // 获取所有 controller 的 path 和 method 信息
        Map<String, Map<String,ReportMethod>> ret = new HashMap<>();
        if (ListUtils.isNotEmpty(reportControllers)){
            for (ReportController reportController : reportControllers) {
                String[] paths = reportController.getPaths();
                Object bean = reportController.getBean();
                Class<?> targetClass = reportController.getTargetClass();
                Method[] methods = targetClass.getDeclaredMethods();
                for (Method method : methods) {
                    // 只看有RequestMapping注解的方法，如果有指定定义 Postxx或者 Getxx 的后续可在这里扩展
                    if (method.isAnnotationPresent(RequestMapping.class)){
                        // 默认取method 的名称做 name
                        String name = method.getName();
                        for (String path : paths) {
                            ReportMethod reportMethod = new ReportMethod(path,name,bean,targetClass,method);
                            Map<String, ReportMethod> pathMap = ret.computeIfAbsent(path, k -> new HashMap<>());
                            pathMap.put(name, reportMethod);
                        }
                    }
                }
            }
        }
        return ret;
    }

    @AllArgsConstructor
    @Data
    public static class ReportController {
        /** controller 层路径 */
        private String[] paths;

        /** spring 的 bean */
        private Object bean;

        /** bean 的目标 class */
        private Class<?> targetClass;
    }

    @AllArgsConstructor
    @Data
    public static class ReportMethod{
        /** controller 层路径 */
        private String path;

        /** 路径下的方法名 */
        private String methodName;

        /** spring 的 bean  */
        private Object bean;

        /** bean 的 class */
        private Class<?> targetClass;

        /** 方法 */
        private Method method;
    }
}
