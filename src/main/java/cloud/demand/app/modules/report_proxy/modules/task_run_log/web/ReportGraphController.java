package cloud.demand.app.modules.report_proxy.modules.task_run_log.web;

import cloud.demand.app.modules.report_proxy.dto.req.GraphNameReq;
import cloud.demand.app.modules.report_proxy.dto.req.GraphTaskReq;
import cloud.demand.app.modules.report_proxy.dto.req.GraphWithTaskRunLogFilterReq;
import cloud.demand.app.modules.report_proxy.dto.req.GraphWithTaskRunLogReq;
import cloud.demand.app.modules.report_proxy.dto.resp.TaskTableGraphResp;
import cloud.demand.app.modules.report_proxy.entity.graph.ReportTableGraphTaskInfoVO;
import cloud.demand.app.modules.report_proxy.modules.task_run_log.service.ReportGraphService;
import cloud.demand.app.modules.sop.domain.ReturnT;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@Slf4j
@Tag(name = "报表graph", description = "报表graph")
@JsonrpcController("/report/graph")
public class ReportGraphController {
    @Resource
    private ReportGraphService reportGraphService;

    @Operation(summary = "根据任务运行日志生成表级别血缘")
    @RequestMapping
    public ReturnT<String> genGraphWithTaskRunLog(@JsonrpcParam GraphWithTaskRunLogReq req){
        reportGraphService.genGraphWithTaskRunLog(req);
        return ReturnT.ok();
    }

    @Operation(summary = "根据任务运行日志更新表级别血缘")
    @RequestMapping
    public ReturnT<String> updateGraphWithTaskRunLog(@JsonrpcParam GraphWithTaskRunLogReq req){
        reportGraphService.updateGraphWithTaskRunLog(req);
        return ReturnT.ok();
    }

    @Operation(summary = "获取任务运行视图")
    @RequestMapping
    public TaskTableGraphResp getTaskTableGraph(@JsonrpcParam GraphWithTaskRunLogFilterReq req){
        return reportGraphService.getTaskTableGraph(req);
    }

    @Operation(summary = "删除graph")
    @RequestMapping
    public ReturnT<String> deleteGraph(@JsonrpcParam GraphNameReq req){
        reportGraphService.deleteGraph(req);
        return ReturnT.ok();
    }
}
