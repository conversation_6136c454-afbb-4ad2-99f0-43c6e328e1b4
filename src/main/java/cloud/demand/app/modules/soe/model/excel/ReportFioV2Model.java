package cloud.demand.app.modules.soe.model.excel;

import cloud.demand.app.modules.soe.dto.fio.ReportFioResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioV2Resp;
import com.alibaba.excel.annotation.ExcelProperty;
import java.math.BigDecimal;
import lombok.Data;

/** 预测内外导出数据 */
@Data
public class ReportFioV2Model {

    @ExcelProperty(value = "产品类型",index = 0)
    private String productType;

    @ExcelProperty(value = "产品",index = 1)
    private String product;

    @ExcelProperty(value = "年月",index = 2)
    private String yearMonth;

    @ExcelProperty(value = "干预类型",index = 3)
    private String modType; // 干预前 or 干预后

    @ExcelProperty(value = "行业部门",index = 4)
    private String industryDept;

    @ExcelProperty(value = "战区",index = 5)
    private String warZone;

    @ExcelProperty(value = "客户简称",index = 6)
    private String customerShortName;

    @ExcelProperty(value = "境内外",index = 7)
    private String customhouseTitle;

    @ExcelProperty(value = "国家",index = 8)
    private String countryName;

    @ExcelProperty(value = "地域",index = 9)
    private String regionName;

    @ExcelProperty(value = "实例类型",index = 10)
    private String instanceType;

    @ExcelProperty(value = "订单量",index = 11)
    private BigDecimal totalCore;

    @ExcelProperty(value = "提前13周",index = 12)
    private BigDecimal w13;

    @ExcelProperty(value = "提前12周",index = 13)
    private BigDecimal w12;

    @ExcelProperty(value = "提前11周",index = 14)
    private BigDecimal w11;

    @ExcelProperty(value = "提前10周",index = 15)
    private BigDecimal w10;

    @ExcelProperty(value = "提前9周",index = 16)
    private BigDecimal w9;

    @ExcelProperty(value = "提前8周",index = 17)
    private BigDecimal w8;

    @ExcelProperty(value = "提前7周",index = 18)
    private BigDecimal w7;

    @ExcelProperty(value = "提前6周",index = 19)
    private BigDecimal w6;

    @ExcelProperty(value = "提前5周",index = 20)
    private BigDecimal w5;

    @ExcelProperty(value = "提前4周",index = 21)
    private BigDecimal w4;

    @ExcelProperty(value = "提前3周",index = 22)
    private BigDecimal w3;

    @ExcelProperty(value = "提前2周",index = 23)
    private BigDecimal w2;

    @ExcelProperty(value = "提前1周",index = 24)
    private BigDecimal w1;

    @ExcelProperty(value = "提前0周",index = 25)
    private BigDecimal w0;

    public static ReportFioV2Model transform(ReportFioV2Resp.Item item){
        ReportFioV2Model ret = new ReportFioV2Model();
        ret.setProductType(item.getProductType());
        ret.setProduct(item.getProduct());
        ret.setYearMonth(item.getYearMonth());
        ret.setModType(item.getModType());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setWarZone(item.getWarZone());
        ret.setCustomerShortName(item.getCustomerShortName());
        ret.setCustomhouseTitle(item.getCustomhouseTitle());
        ret.setCountryName(item.getCountryName());
        ret.setRegionName(item.getRegionName());
        ret.setInstanceType(item.getInstanceType());
        ret.setTotalCore(item.getTotalCore());
        ret.setW13(item.getW13());
        ret.setW12(item.getW12());
        ret.setW11(item.getW11());
        ret.setW10(item.getW10());
        ret.setW9(item.getW9());
        ret.setW8(item.getW8());
        ret.setW7(item.getW7());
        ret.setW6(item.getW6());
        ret.setW5(item.getW5());
        ret.setW4(item.getW4());
        ret.setW3(item.getW3());
        ret.setW2(item.getW2());
        ret.setW1(item.getW1());
        ret.setW0(item.getW0());
        return ret;

    }
}
