package cloud.demand.app.modules.soe.dto.cloud_device_delivery;

import cloud.demand.app.modules.soe.dto.req.AbstractDimFieldsReq;
import cloud.demand.app.modules.soe.dto.req.IIndexReq;
import cloud.demand.app.modules.soe.enums.field.CloudDeviceDeliveryFieldEnum;
import cloud.demand.app.modules.soe.model.item.CloudDeviceDeliveryItem;
import cloud.demand.app.modules.soe.process.service.impl.CloudDeviceDemandServiceImpl;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.*;
import java.util.function.Predicate;

/** 公共请求 */
@Data
public class DeliveryCommonReq extends AbstractDimFieldsReq implements IIndexReq {

    @NotEmpty(message = "切片时间不能为空")
    private String statTime; // 切片时间

    @NotEmpty(message = "开始年月不能为空")
    private String startYearMonth; // 开始年月

    @NotEmpty(message = "结束年月不能为空")
    private String endYearMonth; // 结束年月
    private String startYearWeek; // 起始年周：2024-W01

    private String endYearWeek; // 结束年周：2024-W01

    private List<String> deviceType; // 设备类型

    private List<String> customhouseTitle;// 境内外

    private List<String> regionName; // 地域

    private List<String> zoneName; // 需求 zone

    private List<String> campus;  // 需求 campus

    private List<String> industryDept; // 行业

    private List<String> customerShortName; // 客户简称

    private List<String> instanceType; // 实例类型


    private List<String> skipIndex; // 需要跳过的指标

    private List<String> quotaId; // q 单号

    private List<String> produceStatus; // 生产状态

    private List<String> isFakePosition; // 是否为假机位

    private List<String> planProductName = ListUtils.newList("腾讯云CVM"); // 产品名称

    private Boolean isDetail; // 是否为详细

    @Pattern(regexp = "^(年月|年周)$", message = "dateType 只能为年月或者年周")
    private String dateType;

    /** 只看无货期(true - 只看无货期，false - 只看有货期， null - 都看) */
    private Boolean isNotExpect;


    /** 是否为年月 */
    public boolean isYearMonth(){
        List<String> groupBy = getGroupBy();
        return Objects.equals(dateType,"年月")|| (ListUtils.isNotEmpty(groupBy) && groupBy.contains(CloudDeviceDeliveryFieldEnum.yearMonth.name()));
    }

    /** 是否为年周 */
    public boolean isYearWeek(){
        List<String> groupBy = getGroupBy();
        return Objects.equals(dateType,"年周")|| (ListUtils.isNotEmpty(groupBy) && groupBy.contains(CloudDeviceDeliveryFieldEnum.yearWeek.name()));
    }

    /**
     * 交付请求
     * @param isQuoteUseTime 是否为期望交付时间
     * @param isExcel 是否为导出 excel
     * @return req
     */
    public CloudDeviceDemandServiceImpl.DeliveryReq transform(boolean isQuoteUseTime,boolean isExcel) {
        isExcel = isExcel || BooleanUtils.isTrue(getIsDetail());
        CloudDeviceDemandServiceImpl.DeliveryReq ret = new CloudDeviceDemandServiceImpl.DeliveryReq();
        ret.setGroupBy(getGroupBy());
        ret.setIsQuoteUseTime(isQuoteUseTime);
        ret.setIsExcel(isExcel);
        ret.setQuotaId(getQuotaId());
        ret.setProduceStatus(getProduceStatus());
        ret.setIsFakePosition(getIsFakePosition());
        ret.setPlanProductName(getPlanProductName());
        ret.setStatTime(getStatTime());
        ret.setRegionName(getRegionName());
        ret.setCampus(getCampus());
        ret.setStatTimeAdd1(DateUtils.formatDate(DateUtils.parseLocalDate(getStatTime()).plusDays(1)));
        ret.setStartYearMonth(getStartYearMonth());
        ret.setEndYearMonth(getEndYearMonth());
        if (getStartYearWeek() != null){
            ret.setIntStartYearWeek(getStartYearWeek().replace("-W",""));
        }
        if (getEndYearWeek() != null){
            ret.setIntEndYearWeek(getEndYearWeek().replace("-W",""));
        }
        ret.setIsNotExpect(BooleanUtils.isTrue(getIsNotExpect()));
        ret.setIsExpect(BooleanUtils.isFalse(getIsNotExpect()));
        ret.setDeviceType(getDeviceType());
        ret.setIndustryDept(getIndustryDept());
        ret.setCustomerShortName(getCustomerShortName());

        return ret;
    }

    /**
     * 需求请求
     * @param isExcel 是否为 excel
     * @return
     */
    public CloudDeviceDemandServiceImpl.DemandReq transform(boolean isExcel) {
        CloudDeviceDemandServiceImpl.DemandReq ret = new CloudDeviceDemandServiceImpl.DemandReq();
        ret.setDemandType(ListUtils.newArrayList("NEW","ELASTIC")); // 只看新增弹性
        ret.setStatTime(getStatTime());
        ret.setStartYearMonth(getStartYearMonth());
        ret.setEndYearMonth(getEndYearMonth());
        ret.setCustomhouseTitle(getCustomhouseTitle());
        ret.setZoneName(getZoneName());
        ret.setIndustryDept(getIndustryDept());
        ret.setCustomerShortName(getCustomerShortName());
        ret.setInstanceType(getInstanceType());
        ret.setGroupBy(getGroupBy());
        ret.setIsExcel(isExcel);

        return ret;
    }


    public List<Predicate<CloudDeviceDeliveryItem>> buildFilter() {
        List<Predicate<CloudDeviceDeliveryItem>> ret = new ArrayList<>();
        // 下面维度都是底表没法直接过滤的，这里过滤一下
        // 境内外
        if (ListUtils.isNotEmpty(customhouseTitle)){
            Set<String> set = new HashSet<>(customhouseTitle);
            ret.add(item -> set.contains(item.getCustomhouseTitle()));
        }
        // CVM 城市
        if (ListUtils.isNotEmpty(regionName)){
            Set<String> set = new HashSet<>(regionName);
            ret.add(item -> set.contains(item.getRegionName()));
        }
        // 需求 zone
        if (ListUtils.isNotEmpty(zoneName)){
            Set<String> set = new HashSet<>(zoneName);
            ret.add(item -> set.contains(item.getZoneName()));
        }
        // 实例类型
        if (ListUtils.isNotEmpty(instanceType)){
            Set<String> set = new HashSet<>(instanceType);
            ret.add(item -> set.contains(item.getInstanceType()));
        }
        return ret;
    }
}
