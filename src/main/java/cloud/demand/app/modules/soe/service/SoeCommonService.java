package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.operation_view.operation_view2.entity.DwsBufferSafeInventoryDfDO;
import cloud.demand.app.modules.operation_view.operation_view2.entity.InventoryHealthManualConfigSnapshotDO;
import cloud.demand.app.modules.operation_view.operation_view2.model.OperationViewResp2;
import cloud.demand.app.modules.soe.dto.other.YearWeekReq;
import cloud.demand.app.modules.soe.dto.req.SoeOverviewReq;
import cloud.demand.app.modules.soe.entitiy.dict.DeviceApplyDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeHistoricalFulfillmentDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeProfitAndLossDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.modules.soe.model.dict.CvmType;
import cloud.demand.app.modules.soe.model.dict.GpuType;
import cloud.demand.app.modules.soe.model.dict.YearMonthWeek;
import cloud.demand.app.modules.soe.model.dict.YearWeek;
import cloud.demand.app.modules.soe.model.excel.SoeCustomerInfoModel;
import cloud.demand.app.modules.soe.model.req.OperationViewReq;
import cloud.demand.app.modules.soe.model.resp.OperationViewResp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/** 公共服务 */
public interface SoeCommonService {

    /** 获取最新版本的策略 by 产品 */
    public Set<String> getLatestStrategyByProduct(String product);

    /** 获取所以CVM类型 */
    public List<CvmType> getAllCvmType();

    /** 获取所有GPU类型 */
    public List<GpuType> getAllGpuType();

    /** gpu卡型 --> gpu信息，非最细维度map的key，可能一对多，这里配置固定策略限制一对多的数据 */
    public Map<String,GpuType> getGpuCardTypeMap();

    /** 获取全部年月 */
    List<YearWeek> getAllYearWeek(YearWeekReq req);

    List<YearMonthWeek> getAllYearMonthWeek(YearWeekReq req);

    /** 获取采购单信息 */
    List<DeviceApplyDO> getDeviceApplyInfo();

    /** 地域到国家 */
    List<SoeRegionNameCountryDO> getRegion2Country();

    /** 获取安全库存（调用CVM运营视图接口） */
    List<OperationViewResp.Item> getActualAndSafeInv(OperationViewReq req,String operationViewAlgorithm);

    /** 获取备货配额 */
    List<DwsBufferSafeInventoryDfDO> getBufferSafeInventoryDfDO(String statTime);

    /** 人工调整 */
    List<InventoryHealthManualConfigSnapshotDO> querySnapshotManual(String statTime);

    /** 获取客户历史履约，只有CVM的 */
    List<SoeHistoricalFulfillmentDO> getAllHistoricalFulfillment(String productType);

    /** 获取客户盈利率，只有CVM的 */
    List<SoeProfitAndLossDO> getAllProfitAndLoss(String productType);

    /** 获取客户信息，从ads的分货表那 */
    List<SoeCustomerInfoModel> getCustomerInfo();

    /** 实例类型 --> 合并实例类型 */
    Map<String,String> getInstanceType2Un();

    /** 或者最大版本号 */
    String getMaxVersionCode(LocalDate demandStatTime);

    /** 获取起始年月，通过ppl 版本号 */
    String getBeginYearMonthByVersionCode(String maxVersionCode);

    /** 客户类型 --> uin （仅订单范围） */
    Map<Integer, List<String>> getOrderUinType2Uin();

    /** 获取所有内领客户 uin */
    Set<String> getAllInnerUinType();
}
