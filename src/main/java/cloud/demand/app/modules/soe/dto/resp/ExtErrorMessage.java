package cloud.demand.app.modules.soe.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/** 导入异常信息 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class ExtErrorMessage extends ErrorMessage{
    /** 是否抛出异常 */

    private boolean throwable;

    public ExtErrorMessage(String message) {
        super(-1, -1, null, message);
    }
}
