package cloud.demand.app.modules.soe.enums.score;

import cloud.demand.app.modules.soe.entitiy.distribute.IScoreGetter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/** 损益得分 */
@Getter
@AllArgsConstructor
public enum ProfitAndLossScoreEnum implements IScoreGetter {
    HIGH("高毛利（超过30%）",6),
    MEDIUM("中毛利（10%-30%）",4),
    LOW("低毛利（0%-10%）",2),
    NEGATIVE("负毛利（<=0%）",0),
    ;

    private final String name;
    private final Integer score;

    public static ProfitAndLossScoreEnum getDef(){
        return MEDIUM;
    }

    /** 获取得分 */
    public static ProfitAndLossScoreEnum getScoreByName(String name){
        if (StringUtils.isBlank(name)){
            return null;
        }
        for (ProfitAndLossScoreEnum value : values()) {
            if (Objects.equals(value.getName(),name)){
                return value;
            }
        }
        return null;
    }
}
