package cloud.demand.app.modules.soe.dto.fiolongtail;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.Data;

import java.util.List;


/**
 * 预测内外请求（中长尾执行）
 */
@Data
public class ReportFioLongTailReq {


    /**
     * 起始年月：格式：yyyy-MM
     */
    //@SopReportWhere(sql = "year_month >= ?")
    private String startYearMonth;

    /**
     * 结束年月：格式：yyyy-MM
     */
    //@SopReportWhere(sql = "year_month <= ?")
    private String endYearMonth;

    /**
     * 境内外
     */
    @SopReportWhere(columnValue = "customhouse_title")
    private List<String> customhouseTitle;

    /**
     * 国家名称
     */
    @SopReportWhere(columnValue = "country_name")
    private List<String> countryName;

    /**
     * 新旧机型
     * */
    private List<String> generationInstanceType;

    @SopReportWhere(sql = "gins_family in (?)")
    private List<String> inGenerationInstanceType;

    @SopReportWhere(sql = "gins_family not in (?)")
    private List<String> notInGenerationInstanceType;

    /**
     * 采购机型
     * */
    private List<String> purchaseInstanceType;

    @SopReportWhere(sql = "gins_family in (?)")
    private List<String> inPurchaseInstanceType;

    @SopReportWhere(sql = "gins_family not in (?)")
    private List<String> notInPurchaseInstanceType;

    /**
     * 机型族
     */
    @SopReportWhere(columnValue = "gins_family")
    private List<String> ginsFamily;


    /**
     * 分组key集合
     */
    private List<String> matchKey;

    /**
     * 维度集合
     */
    private List<String> dims;
}
