package cloud.demand.app.modules.soe.enums;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplProjectTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/** 常量 */
public class Constant {

    /** 标签分隔符 */
    public static final String split = "@";

    public static final String other = "其他";

    public static final String all = "整体";

    /** 空日期 */
    public static final String null_date = "1997-01-01";

    /** 分货看板需求的产品 */
    public static final List<String> distributeProductClass = Collections.unmodifiableList(ListUtils.newList(
            Ppl13weekProductTypeEnum.CVM.getName(),
            Ppl13weekProductTypeEnum.EMR.getName(),
            Ppl13weekProductTypeEnum.EKS.getName())
    );

    public static final List<String> gpuProduct = Collections.unmodifiableList(ListUtils.newList(
            Ppl13weekProductTypeEnum.GPU.getName(),
            Ppl13weekProductTypeEnum.BM.getName())
    );

    public static String getProductTypeByProduct(String product){
        return Constant.gpuProduct.contains(product)?"GPU":"CVM";
    }
}
