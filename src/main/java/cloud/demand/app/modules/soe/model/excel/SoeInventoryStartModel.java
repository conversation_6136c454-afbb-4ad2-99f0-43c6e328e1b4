package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/** 期初库存 */
@Data
public class SoeInventoryStartModel extends SoeCommonModel{

    @ExcelProperty(value = "设备类型",index = 11)
    private String deviceType;

    @ExcelProperty(value = "库存类型",index = 12)
    private String lineType;

    /**
     * 物料类型：好料、差料、呆料
     */
    @ExcelProperty(value = "物料类型",index = 13)
    private String materialType;

    /**
     * 库存细类：用户预扣、小核库存、大核库存、大核预留等等
     */
    @ExcelProperty(value = "库存细类",index = 14)
    private String invDetailType;
}
