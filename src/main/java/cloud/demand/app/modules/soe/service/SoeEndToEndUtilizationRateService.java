package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.soe.entitiy.dict.SoeEndToEndUtilizationRateDO;
import cloud.demand.app.modules.soe.model.item.SoeEndToEndUtilizationRateItem;
import cloud.demand.app.modules.soe.model.req.SoeEndToEndUtilizationRateReq;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2024/9/13 15:41
 */
public interface SoeEndToEndUtilizationRateService extends BasVersionCommonService<SoeEndToEndUtilizationRateDO>{
    List<SoeEndToEndUtilizationRateItem> getAvgData(SoeEndToEndUtilizationRateReq req);

    List<SoeEndToEndUtilizationRateItem> getYearEndData(SoeEndToEndUtilizationRateReq req);

    Map<String, BigDecimal> getTotalData(String year, String productType,List<String> customhouseTitle);
}
