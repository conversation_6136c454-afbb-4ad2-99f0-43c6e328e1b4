package cloud.demand.app.modules.soe.model.req;

import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthInstanceFamilyType2;
import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.soe.dto.req.SoeOverviewReq;
import cloud.demand.app.modules.soe.enums.InstanceTypeStrategyEnum;
import cloud.demand.app.modules.soe.enums.ZoneStrategyEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/** CVM运营视图请求 */
@Data
@ToString
public class OperationViewReq {

    /** 切片时间 */
    private LocalDate statTime;

    /** 区域 */
    private List<String> areaName;

    /** 地域 */
    private List<String> regionName;

    /** 可用区 */
    private List<String> zoneName;

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 机型 */
    private List<String> instanceType;

    /** 物料类型：好差呆料 */
    private List<String> materialType;

    /** 可用区策略，主力可用区等 */
    private List<String> zoneCategory;

    /** 机型策略，主力机型等 */
    private List<String> instanceTypeCategory;

    /** 库存细分 */
    private List<String> invDetailType;

    /** 库存类型 */
    private List<String> lineType;


    /** soe请求转cvm运营视图请求 */
    public static OperationViewReq transform(SoeOverviewReq req){
        OperationViewReq ret = new OperationViewReq();
        ret.setStatTime(req.getStatTime());
        ret.setMaterialType(req.getMaterialType());
        ret.setInvDetailType(req.getInvDetailType());
        ret.setLineType(req.getLineType());
        List<String> instanceTypeCategory = req.getInstanceTypeCategory();
        if (ListUtils.isNotEmpty(instanceTypeCategory)){
            ret.setInstanceTypeCategory(instanceTypeCategory.stream()
                    .map(InstanceTypeStrategyEnum::getCodeFromName).collect(Collectors.toList()));
        }
        List<String> zoneCategory = req.getZoneCategory();
        if (ListUtils.isNotEmpty(zoneCategory)){
            ret.setZoneCategory(zoneCategory.stream()
                    .map(ZoneStrategyEnum::getCodeFromName).collect(Collectors.toList()));
        }
        // 注意：以下过滤条件不做过滤入参设置，这里入参会作为缓存的key，为了避免多次缓存
//        ret.setAreaName(req.getAreaName());
//        ret.setRegionName(req.getRegionName());
//        ret.setZoneName(req.getZoneName());
//        ret.setCustomhouseTitle(req.getCustomhouseTitle());
//        ret.setInstanceType(req.getInstanceType());
        return ret;

    }
}
