package cloud.demand.app.modules.soe.model.excel;


import cloud.demand.app.modules.soe.dto.fiolongtail.ReportFioLongTailResp;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/** 预测内外导出数据 */

@Data
public class ReportFioLongTailModel {

    @ExcelProperty(value = "年月",index = 0)
    private String yearMonth;
    @ExcelProperty(value = "境内外",index = 1)
    private String customhouseTitle;
    @ExcelProperty(value = "国家名称",index = 2)
    private String countryName;
    @ExcelProperty(value = "实例类型",index = 3)
    private String ginsFamily;
    @ExcelProperty(value = "购买量",index = 4)
    private BigDecimal totalCore;
    @ExcelProperty(value = "提前13周",index = 5)
    private BigDecimal w13;
    @ExcelProperty(value = "提前9周",index = 6)
    private BigDecimal w9;
    @ExcelProperty(value = "提前5周",index = 7)
    private BigDecimal w5;
    @ExcelProperty(value = "提前5内",index = 8)
    private BigDecimal w0;

    public static ReportFioLongTailModel transform(ReportFioLongTailResp.Item item){
        ReportFioLongTailModel model = new ReportFioLongTailModel();
        model.setYearMonth(item.getYearMonth());
        model.setCustomhouseTitle(item.getCustomhouseTitle());
        model.setCountryName(item.getCountryName());
        model.setGinsFamily(item.getGinsFamily());
        model.setTotalCore(item.getTotalCore());
        model.setW13(item.getW13());
        model.setW9(item.getW9());
        model.setW5(item.getW5());
        model.setW0(item.getW0());
        return model;
    }
}
