package cloud.demand.app.modules.soe.entitiy;

import cloud.demand.app.modules.soe.model.item.SoeInventoryStartItem;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SoeInventoryStartExportDO extends SoeInventoryStartDO{

    /**
     * 物料类型：好料、差料、呆料
     */
    @Column("material_type")
    private String materialType;

    /**
     * 库存细类：用户预扣、小核库存、大核库存、大核预留等等
     */
    @Column("inv_detail_type")
    private String invDetailType;

    /** 库存分类：线上库存，线下库存 */
    @Column("line_type")
    private String lineType;

    public static SoeInventoryStartItem transform(SoeInventoryStartExportDO startDO) {
        SoeInventoryStartItem ret = new SoeInventoryStartItem();
        ret.setProductType(startDO.getProductType());
        ret.setYearMonth(startDO.getYearMonth());
        ret.setYearWeek(startDO.getYearWeek());
        ret.setInstanceType(startDO.getInstanceType());
        ret.setDeviceType(startDO.getDeviceType());
//        ret.setGinsFamily();
//        ret.setGpuType();
        ret.setCustomhouseTitle(startDO.getCustomhouseTitle());
        ret.setAreaName(startDO.getAreaName());
        ret.setRegionName(startDO.getRegionName());
        ret.setZoneName(startDO.getZoneName());
        ret.setCoreNum(startDO.getCoreNum());
        ret.setInvDetailType(startDO.getInvDetailType());
        ret.setMaterialType(startDO.getMaterialType());
        ret.setLineType(startDO.getLineType());
        ret.setDeviceType(startDO.getDeviceType());
        return ret;
    }
}
