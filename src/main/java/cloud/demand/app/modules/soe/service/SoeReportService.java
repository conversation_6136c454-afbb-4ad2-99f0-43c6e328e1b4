package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.soe.dto.req.SoeOverviewReq;
import cloud.demand.app.modules.soe.dto.resp.SoeDetailResp;
import cloud.demand.app.modules.soe.dto.resp.SoeOverviewResp;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;

/** soe报表 */
public interface SoeReportService {
    /** soe概览，soe趋势 */
    SoeOverviewResp queryReport(SoeOverviewReq req);

    /** soe明细 */
    SoeDetailResp queryReportDetail(SoeOverviewReq req);

    /** 导出 */
    ResponseEntity<InputStreamResource> excel(SoeOverviewReq req);

    /** 履约分析 */
    SoeDetailResp queryScaleForecastReport(SoeOverviewReq req);

    /** 导出履约分析明细 */
    ResponseEntity<InputStreamResource> excelScaleForecast(SoeOverviewReq req);

    /** 海外视图 */
    SoeDetailResp queryViewTotalReport(SoeOverviewReq req);
}
