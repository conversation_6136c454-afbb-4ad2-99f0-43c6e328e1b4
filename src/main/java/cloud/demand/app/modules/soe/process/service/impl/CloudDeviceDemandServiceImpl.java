package cloud.demand.app.modules.soe.process.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.soe.enums.field.CloudDeviceDeliveryFieldEnum;
import cloud.demand.app.modules.soe.model.item.CloudDeviceDeliveryItem;
import cloud.demand.app.modules.soe.model.item.CloudDeviceDemandItem;
import cloud.demand.app.modules.soe.model.sql.ColumnParam;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.soe.process.service.CloudDeviceDemandService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class CloudDeviceDemandServiceImpl implements CloudDeviceDemandService {

    /**
     * new ck
     */
    @Resource
    private DBHelper ckcldDBHelper;

    /**
     * cubes
     */
    @Resource
    private DBHelper ckcrpreadDBHelper;


    @Override
    public List<CloudDeviceDeliveryItem> getDelivery(DeliveryReq req) {
        boolean isExcel = BooleanUtils.isTrue(req.getIsExcel());
        String sql = ORMUtils.getSql("/sql/soe/cloud_device_delivery/cvm_delivery.sql");
        // 维度
        List<String> groupBy = ObjectUtils.defaultIfNull(req.getGroupBy(), new ArrayList<>());
        // 底表没有的维度，这里要加上（避免有过滤但是没有维度）
        // 实例类型
        if (ListUtils.isNotEmpty(req.getInstanceType()) && !groupBy.contains(CloudDeviceDeliveryFieldEnum.instanceType.name())) {
            groupBy.add(CloudDeviceDeliveryFieldEnum.instanceType.name());
        }
        // 境内外
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle()) && !groupBy.contains(CloudDeviceDeliveryFieldEnum.customhouseTitle.name())) {
            groupBy.add(CloudDeviceDeliveryFieldEnum.customhouseTitle.name());
        }
        // 可用区
        if (ListUtils.isNotEmpty(req.getZoneName()) && !groupBy.contains(CloudDeviceDeliveryFieldEnum.zoneName.name())) {
            groupBy.add(CloudDeviceDeliveryFieldEnum.zoneName.name());
        }
        // 可用区
        if (ListUtils.isNotEmpty(req.getRegionName()) && !groupBy.contains(CloudDeviceDeliveryFieldEnum.regionName.name())) {
            groupBy.add(CloudDeviceDeliveryFieldEnum.regionName.name());
        }
        sql = SimpleSqlBuilder.buildDims(sql, CloudDeviceDeliveryFieldEnum.getFieldNames(), groupBy);
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, CloudDeviceDeliveryItem.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        // 替换where 条件
        sql = SimpleSqlBuilder.doReplace(sql, "where_condition", whereSQL.getSQL());
        // 构建 sql
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(req);
        sql = sqlBuilder.build(sql);
        List<CloudDeviceDeliveryItem> ret = ckcrpreadDBHelper.getRaw(CloudDeviceDeliveryItem.class, sql, whereSQL.getParams());

        // 处理年周
        if (ListUtils.isNotEmpty(ret)) {
            ret.forEach(item -> {
                item.setYearWeek(SoeCommonUtils.yearWeekFormat(item.getYearWeek()));
                // 导出有多个年周，所以这里要额外判断一下
                if (isExcel){
                    item.setSlaDateExpectYearWeek(SoeCommonUtils.yearWeekFormat(item.getSlaDateExpectYearWeek()));
                    item.setErpActualDateYearWeek(SoeCommonUtils.yearWeekFormat(item.getErpActualDateYearWeek()));
                    item.setQuotaUseTimeYearWeek(SoeCommonUtils.yearWeekFormat(item.getQuotaUseTimeYearWeek()));
                }
            });
        }
        return ret;
    }

    @Override
    public List<CloudDeviceDeliveryItem> getSupply(DeliveryReq req) {
        String sql = ORMUtils.getSql("/sql/soe/cloud_device_delivery/cvm_delivery.sql");
        // 维度
        List<String> groupBy = ObjectUtils.defaultIfNull(req.getGroupBy(), new ArrayList<>());
        sql = SimpleSqlBuilder.buildDims(sql, CloudDeviceDeliveryFieldEnum.getFieldNames(), groupBy);
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, CloudDeviceDeliveryItem.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        // 替换where 条件
        sql = SimpleSqlBuilder.doReplace(sql, "where_condition", whereSQL.getSQL());
        // 构建 sql
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(req);
        sql = sqlBuilder.build(sql);
        List<CloudDeviceDeliveryItem> raw = ckcrpreadDBHelper.getRaw(CloudDeviceDeliveryItem.class, sql, whereSQL.getParams());
        // 处理年周
        if (ListUtils.isNotEmpty(raw)) {
            raw.forEach(item -> {
                String yearWeek = item.getYearWeek();
                if (yearWeek != null) {
                    item.setYearWeek(yearWeek.substring(0, 4) + "-W" + yearWeek.substring(4));
                }
            });
        }
        return raw;
    }

    @Override
    public List<CloudDeviceDeliveryItem> getDemand(DemandReq req) {
        String sql = ORMUtils.getSql("/sql/soe/cloud_device_delivery/forecast_add_or_return.sql");
        // 维度
        List<String> groupBy = ObjectUtils.defaultIfNull(req.getGroupBy(), new ArrayList<>());
        sql = SimpleSqlBuilder.buildDims(sql, CloudDeviceDeliveryFieldEnum.getFieldNames(), groupBy);
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, CloudDeviceDeliveryItem.class);
        WhereSQL whereSQL = whereBuilder.whereSQL();
        // 替换where 条件
        sql = SimpleSqlBuilder.doReplace(sql, "where_condition", whereSQL.getSQL());
        // 构建 sql
        ObjectSqlBuilder sqlBuilder = new ObjectSqlBuilder();
        sqlBuilder.addSqlParams(req);
        sql = sqlBuilder.build(sql);
        List<CloudDeviceDemandItem> raw = ckcldDBHelper.getRaw(CloudDeviceDemandItem.class, sql, whereSQL.getParams());
        return ListUtils.transform(raw, CloudDeviceDemandItem::transform);
    }

    @Data
    public static class DeliveryReq extends CommonReq {
        @ColumnParam("is_quote_use_time")
        private Boolean isQuoteUseTime; // 是否使用期望交付时间
        @SopReportWhere(columnValue = "quota_id")
        private List<String> quotaId; // q 单号

        @SopReportWhere(columnValue = "produce_status")
        private List<String> produceStatus; // 生产状态

        @SopReportWhere(columnValue = "is_fake_position")
        private List<String> isFakePosition; // 是否为假机位

        @SopReportWhere(columnValue = "quota_plan_product_name")
        private List<String> planProductName; // 规划产品

        @ColumnParam("stat_time")
        private String statTime; // 切片时间

        @ColumnParam("stat_time_add_1")
        private String statTimeAdd1; // 切片时间 + 1天

        @SopReportWhere(sql = "((substr(quota_use_time, 1, 7) >= ?) and (year_month is null or year_month >= ?))")
        private String startYearMonth; // 开始年月

        @SopReportWhere(sql = "((substr(quota_use_time, 1, 7) <= ?) and (year_month is null or year_month <= ?))")
        private String endYearMonth; // 结束年月

        @SopReportWhere(sql = "((toYearWeek(toDate(substr(quota_use_time, 1, 10)), 7) >= ?) and (year_week is null or year_week >= ?))")
        private String intStartYearWeek; // 开始年周

        @SopReportWhere(sql = "((toYearWeek(toDate(substr(quota_use_time, 1, 10)), 7) <= ?) and (year_week is null or year_week <= ?))")
        private String intEndYearWeek; // 结束年周

        @SopReportWhere(useSqlWhenNullOrFalse = false,sql = "year_month is null and year_week is null")
        private Boolean isNotExpect; // 只看无货期

        @SopReportWhere(useSqlWhenNullOrFalse = false,sql = "year_month is not null or year_week is not null")
        private Boolean isExpect; // 只看有货期

        @SopReportWhere(columnValue = "device_type")
        private List<String> deviceType; // 设备类型

        @SopReportWhere(columnValue = "quota_campus_name")
        private List<String> campus;  // 需求 campus

        @SopReportWhere(columnValue = "xy_industry")
        private List<String> industryDept; // 行业

        @SopReportWhere(columnValue = "xy_customer_name")
        private List<String> customerShortName; // 客户简称

        /**
         * 实例类型 - 底数没法过滤，得加到维度里面，zoneName，customhouseTitle同理
         */
        private List<String> instanceType;

        private List<String> regionName;

        private List<String> zoneName;

        private List<String> customhouseTitle;

        private List<String> groupBy; // 维度
    }

    @Data
    public static class DemandReq extends CommonReq {

        @ColumnParam("stat_time")
        private String statTime; // 切片时间

        @ColumnParam("demand_type")
        private List<String> demandType; // 需求类型

        @ColumnParam("product_class")
        private List<String> productClass; // 产品类别

        @SopReportWhere(sql = "(year_month is null or year_month >= ?)")
        private String startYearMonth; // 开始年月

        @SopReportWhere(sql = "(year_month is null or year_month <= ?)")
        private String endYearMonth; // 结束年月

        @SopReportWhere
        private List<String> customhouseTitle;// 境内外

        @SopReportWhere
        private List<String> zoneName; // 需求 zone

        @SopReportWhere
        private List<String> industryDept; // 行业

        @SopReportWhere
        private List<String> customerShortName; // 客户简称

        @SopReportWhere
        private List<String> instanceType; // 实例类型

        private List<String> groupBy; // 维度
    }

    @Data
    public static class CommonReq {

        @SopReportWhere(sql = "1=1")
        private Boolean preWhere = true;

        @ColumnParam("is_excel")
        private Boolean isExcel; // 是否为导出
    }
}
