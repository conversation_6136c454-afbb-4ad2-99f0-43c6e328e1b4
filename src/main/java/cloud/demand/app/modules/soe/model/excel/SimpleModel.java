package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
public class SimpleModel {
    @ExcelProperty("字典值")
    private String name;

    public static List<SimpleModel> build(List<String> list){
        if (ListUtils.isEmpty(list)){
            return ListUtils.newList();
        }
        return list.stream().map(SimpleModel::new).collect(Collectors.toList());
    }
}
