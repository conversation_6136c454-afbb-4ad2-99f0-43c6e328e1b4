package cloud.demand.app.modules.soe.service.impl;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.model.dto.TxyRegionInfoDTO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv3.model.clean.ICleanInstanceFamily;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.modules.soe.model.CommonItem;
import cloud.demand.app.modules.soe.model.clean.ICleanCrpWarZone;
import cloud.demand.app.modules.soe.model.clean.IInstanceTypeClean;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.soe.model.clean.IUnCustomerShortName;
import cloud.demand.app.modules.soe.model.clean.IUnInstanceType;
import cloud.demand.app.modules.soe.model.clean.IYearWeekClean;
import cloud.demand.app.modules.soe.model.clean.MappingList;
import cloud.demand.app.modules.soe.model.dict.CvmType;
import cloud.demand.app.modules.soe.model.dict.GpuType;
import cloud.demand.app.modules.soe.model.fields.template.IDistributeClean;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.utils.CleanHashMap;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.Constant;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 清洗
 */
@Service
public class SoeCleanServiceImpl implements SoeCleanService {

    @Resource
    private DictService dictService;

    @Resource
    private SoeCommonService commonService;

    @Resource
    private DBHelper obsDBHelper;


    // =============== 本地缓存 ================
    /**
     * 境内外，区域，地域，可用区
     */
    private final ThreadLocal<MappingList> regionThread = new ThreadLocal<>();

    /**
     * cap
     */
    private final ThreadLocal<Map<String, StaticZoneDO>> campus2ZoneInfoMapThread = new ThreadLocal<>();

    /**
     * key:实例类型,value:cvm机型族以及设备类型信息
     */
    private final ThreadLocal<Map<String, CvmType>> cvmTypeMap = new ThreadLocal<>();

    /**
     * key：卡型，value：gpu机型族以及设备类型信息
     */
    private final ThreadLocal<Map<String, GpuType>> gpuTypeMap = new ThreadLocal<>();

    /**
     * key:设备类型,value:cvm机型族以及设备类型信息
     */
    private final ThreadLocal<Map<String, CvmType>> cvmTypeByDeviceTypeMap = new ThreadLocal<>();

    /**
     * key：设备类型，value：gpu机型族以及设备类型信息
     */
    private final ThreadLocal<Map<String, GpuType>> gpuTypeByDeviceTypeMap = new ThreadLocal<>();

    /**
     * key：实例类型，value：gpu机型族以及设备类型信息
     * */
    private final ThreadLocal<Map<String, GpuType>> gpuTypeByInstanceTypeMap = new ThreadLocal<>();

    /**
     * 展示机型集合
     */
    private final ThreadLocal<Map<String, Set<String>>> showInstanceThread = new ThreadLocal<>();

    /**
     * 地域到国家
     */
    private final ThreadLocal<Map<String, String>> regionName2CountryMap = new ThreadLocal<>();

    /**
     * 客户简称到通用客户简称
     * */
    private final ThreadLocal<Map<String, String>> customerShortNameToUnMap = new ThreadLocal<>();

    /**
     * 实例类型到通用实例类型
     * */
    private final ThreadLocal<Map<String, String>> instanceTypeToUnMap = new ThreadLocal<>();

    private final ThreadLocal<Map<String,String>> instanceFamilyMapLocal = new ThreadLocal<>();

    private Map<String,String> getInstanceFamilyMap(){
        Map<String, String> map = instanceFamilyMapLocal.get();
        if (map == null){
            map = new HashMap<>();
            List<Map> raw = obsDBHelper.getRaw(Map.class,
                    "select distinct CvmInstanceTypeCode,CvmInstanceGroup from bas_obs_cloud_cvm_type");
            for (Map map1 : raw) {
                map.put((String) map1.get("CvmInstanceTypeCode"), (String) map1.get("CvmInstanceGroup"));
            }
            instanceFamilyMapLocal.set(map);
        }
        return map;
    }

    private final ThreadLocal<Map<String,String>> customerShortName2CrpWarZoneMapLocal = new ThreadLocal<>();

    private Map<String,String> getCustomerShortName2CrpWarZoneMap(){
        Map<String, String> map = customerShortName2CrpWarZoneMapLocal.get();
        if (map == null){
            List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
            customerConfig = customerConfig.stream().filter(item ->
                    StringUtils.isNotBlank(item.getCustomerName())
                            && StringUtils.isNotBlank(item.getWarZoneName())
                            && BooleanUtils.isTrue(item.getIsEnable())).collect(Collectors.toList());
            map = ListUtils.toMap(customerConfig,IndustryDemandIndustryWarZoneDictDO::getCustomerName,IndustryDemandIndustryWarZoneDictDO::getWarZoneName);
            customerShortName2CrpWarZoneMapLocal.set(map);
        }
        return map;
    }


    /**
     * 获取实例类型-->cvm机型族信息
     */
    public Map<String, CvmType> getCvmTypeMap() {
        Map<String, CvmType> ret = cvmTypeMap.get();
        if (ret != null) {
            return ret;
        }
        ret = new CleanHashMap<>("获取实例类型-->cvm机型族信息");
        List<CvmType> allCvmType = commonService.getAllCvmType();
        for (CvmType cvmType : allCvmType) {
            ret.put(cvmType.getInstanceType(), cvmType);
            ret.put(cvmType.getInstanceTypeName(), cvmType);
        }
        cvmTypeMap.set(ret);
        return ret;
    }

    /**
     * 获取设备类型-->cvm机型族信息
     */
    public Map<String, CvmType> getCvmTypeByDeviceTypeMap() {
        Map<String, CvmType> ret = cvmTypeByDeviceTypeMap.get();
        if (ret != null) {
            return ret;
        }
        ret = new CleanHashMap<>("获取设备类型-->cvm机型族信息");
        List<CvmType> allCvmType = commonService.getAllCvmType();
        for (CvmType cvmType : allCvmType) {
            if (SoeCommonUtils.isNotBlank(cvmType.getDeviceType())) {
                ret.put(cvmType.getDeviceType(), cvmType);
            }
        }
        cvmTypeByDeviceTypeMap.set(ret);
        return ret;
    }

    /**
     * 卡型->gpu机型族信息
     */
    @Override
    public Map<String, GpuType> getGpuTypeMap() {
        Map<String, GpuType> ret = gpuTypeMap.get();
        if (ret != null) {
            return ret;
        }
        ret = new CleanHashMap<>("卡型->gpu机型族信息");

        Map<String, GpuType> gpuCardTypeMap = commonService.getGpuCardTypeMap();
        for (GpuType gpuType : gpuCardTypeMap.values()) {
            if (StringUtils.isNotBlank(gpuType.getGpuType())) {
                // 忽略大小写
                ret.put(gpuType.getGpuType().toLowerCase(), gpuType);
            }
            if (StringUtils.isNotBlank(gpuType.getCubesGpuType())) {
                // 忽略大小写
                ret.put(gpuType.getCubesGpuType().toLowerCase(), gpuType);
            }
            if (ListUtils.isNotEmpty(gpuType.getCubesGpuTypes())){
                for (String cubesGpuType : gpuType.getCubesGpuTypes()) {
                    // 忽略大小写
                    ret.put(cubesGpuType.toLowerCase(), gpuType);
                }
            }
        }
        gpuTypeMap.set(ret);

        return ret;
    }

    /**
     * 设备类型 ---> gpu机型族信息
     */
    public Map<String, GpuType> getGpuTypeByDeviceTypeMap() {
        Map<String, GpuType> ret = gpuTypeByDeviceTypeMap.get();
        if (ret != null) {
            return ret;
        }
        ret = new CleanHashMap<>("设备类型 ---> gpu机型族信息");

        List<GpuType> allGpuType = commonService.getAllGpuType();
        for (GpuType gpuType : allGpuType) {
            if (StringUtils.isNotBlank(gpuType.getDeviceType())) {
                ret.put(gpuType.getDeviceType(), gpuType);
            }
        }
        gpuTypeByDeviceTypeMap.set(ret);
        return ret;
    }

    @Override
    public void cleanCloudDeviceDelivery(Object obj) {
        if (obj instanceof IRegionClean){
            cleanRegion((IRegionClean) obj);
        }
        if (obj instanceof IInstanceTypeClean){
            cleanCvm((IInstanceTypeClean) obj);
        }
    }

    @Override
    public void cleanRegion(IRegionClean region) {
        if (region.getCampus() == null || StringUtils.equals(region.getCampus(), Constant.EMPTY_VALUE_STR)) {
            cleanRegionWithZone(region);
        } else if (!EnvUtils.isLocalEnv()) {
            cleanRegionWithCampus(region);
        }
        cleanCountryName(region);
    }

    @Override
    public void cleanUnCustomerShortName(IUnCustomerShortName name) {
        if (name == null){
            return;
        }
        Map<String, String> map = customerShortNameToUnMap.get();
        if (map == null){
            List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
            customerConfig = customerConfig.stream().filter(item ->
                    StringUtils.isNotBlank(item.getCustomerName())
                            && StringUtils.isNotBlank(item.getCommonCustomerName())
                            && BooleanUtils.isTrue(item.getIsEnable())).collect(Collectors.toList());
            map = ListUtils.toMap(customerConfig,IndustryDemandIndustryWarZoneDictDO::getCustomerName,IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName);
            customerShortNameToUnMap.set(map);
        }
        // 没有通用客户简称默认给客户简称
        name.setUnCustomerShortName(map.getOrDefault(name.getCustomerShortName(),name.getCustomerShortName()));
    }

    @Override
    public void cleanUnInstanceType(IUnInstanceType type) {
        if (type == null){
            return;
        }
        Map<String, String> map = instanceTypeToUnMap.get();
        if (map == null){
            map = commonService.getInstanceType2Un();
            instanceTypeToUnMap.set(map);
        }
        type.setUnInstanceType(map.getOrDefault(type.getInstanceType(),type.getInstanceType()));
    }

    @Override
    public void cleanInstanceFamily(ICleanInstanceFamily instanceFamily) {
        Map<String, String> instanceFamilyMap = getInstanceFamilyMap();
        String instanceType = instanceFamily.getInstanceType();
        if (SoeCommonUtils.isNotBlank(instanceType)){
            instanceFamily.setInstanceFamily(instanceFamilyMap.get(instanceType));
        }
    }

    /**
     * 清洗国家
     */
    private void cleanCountryName(IRegionClean region) {
        Map<String, String> map = getRegionName2CountryMap();
        String regionName = region.getRegionName();
        String countryName = region.getCountryName();
        if (!SoeCommonUtils.isNotBlank(countryName) && SoeCommonUtils.isNotBlank(regionName)) {
            countryName = map.getOrDefault(regionName, Constant.EMPTY_VALUE_STR);
        }
        region.setCountryName(ObjectUtils.defaultIfNull(countryName, Constant.EMPTY_VALUE_STR));
    }

    /**
     * 使用campus清洗
     */
    private void cleanRegionWithCampus(IRegionClean region) {
        Map<String, StaticZoneDO> map = campus2ZoneInfoMapThread.get();
        if (map == null) {
            map = new CleanHashMap<>(dictService.getCampus2ZoneInfoMap(), "使用campus清洗");
            campus2ZoneInfoMapThread.set(map);
        }
        StaticZoneDO staticZoneDO = map.get(region.getCampus());
        if (staticZoneDO != null) {
            region.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
            region.setAreaName(staticZoneDO.getAreaName());
            region.setRegionName(staticZoneDO.getRegionName());
            region.setZoneName(staticZoneDO.getZoneName());
        } else {
            if (region.getCountryName() == null) {
                region.setCountryName(Constant.EMPTY_VALUE_STR);
            }
            if (region.getRegionName() == null) {
                region.setRegionName(Constant.EMPTY_VALUE_STR);
            }
            if (region.getZoneName() == null) {
                region.setZoneName(Constant.EMPTY_VALUE_STR);
            }
            if (region.getAreaName() == null) {
                region.setAreaName(Constant.EMPTY_VALUE_STR);
            }
            if (region.getCustomhouseTitle() == null) {
                region.setCustomhouseTitle(Constant.EMPTY_VALUE_STR);
            }
        }
    }


    /**
     * 使用可用区清洗
     */
    private void cleanRegionWithZone(IRegionClean region) {
        MappingList mappingList = regionThread.get();
        if (mappingList == null) {
            mappingList = initRegion();
            regionThread.set(mappingList);
        }
        List<Map<String, String>> children = mappingList.getChildren();
        String customhouseTitle = region.getCustomhouseTitle();
        String areaName = region.getAreaName();
        String regionName = region.getRegionName();
        String zoneName = region.getZoneName();
        if (SoeCommonUtils.isNotBlank(zoneName)) {
            regionName = SoeCommonUtils.getDefault(children.get(0).get(zoneName),regionName);
        }
        if (SoeCommonUtils.isNotBlank(regionName)) {
            areaName = SoeCommonUtils.getDefault(children.get(1).get(regionName),areaName);
        }
        if (SoeCommonUtils.isNotBlank(areaName)) {
            customhouseTitle = SoeCommonUtils.getDefault(children.get(2).get(areaName),customhouseTitle);
        }

        if (SoeCommonUtils.isNotBlank(customhouseTitle) || region.getCustomhouseTitle() == null) {
            region.setCustomhouseTitle(customhouseTitle);
        }

        if (SoeCommonUtils.isNotBlank(areaName) || region.getAreaName() == null) {
            region.setAreaName(areaName);
        }

        if (SoeCommonUtils.isNotBlank(regionName) || region.getRegionName() == null) {
            region.setRegionName(regionName);
        }

    }

    /**
     * 获取展示的产品
     */
    private Set<String> getLatestStrategyByProduct(String productType) {
        Map<String, Set<String>> setMap = showInstanceThread.get();
        if (setMap == null) {
            setMap = new HashMap<>();
            Set<String> setInfo = commonService.getLatestStrategyByProduct(productType);
            setMap.put(productType, setInfo);
            showInstanceThread.set(setMap);
            return setInfo;
        } else {
            Set<String> setInfo = setMap.get(productType);
            if (setInfo == null) {
                setInfo = commonService.getLatestStrategyByProduct(productType);
                setMap.put(productType, setInfo);
            }
            return setInfo;
        }

    }


    /**
     * 初始化，境内外-区域-地域-可用区
     */
    private MappingList initRegion() {
        MappingList root = new MappingList(new ArrayList<>());
        List<TxyRegionInfoDTO> allTxyRegionInfo = dictService.getAllTxyRegionInfo();
        fillExtRegion(allTxyRegionInfo);
        for (TxyRegionInfoDTO info : allTxyRegionInfo) {
            String customhouseTitle = info.getCustomhouseTitle();
            String areaName = info.getAreaName();
            String regionName = info.getRegionName();
            String zoneName = info.getZoneName();
            MappingList.build(root, ListUtils.newList(zoneName, regionName, areaName, customhouseTitle));
        }
        return root;
    }

    /** 脏数据填充 */
    private void fillExtRegion(List<TxyRegionInfoDTO> allTxyRegionInfo) {
        List<TxyRegionInfoDTO> extRegionList = new ArrayList<>();
        // 补充脏数据映射
        TxyRegionInfoDTO e = new TxyRegionInfoDTO();
        e.setZoneName("福州EC一区");
        e.setRegionName("福州ec");
        e.setAreaName("华东地区");
        e.setCustomhouseTitle("境内");
        extRegionList.add(e);

        e = new TxyRegionInfoDTO();
        e.setZoneName("杭州EC一区");
        e.setRegionName("杭州ec");
        e.setAreaName("华东地区");
        e.setCustomhouseTitle("境内");
        extRegionList.add(e);

        Set<String> zoneNameSet = allTxyRegionInfo.stream().map(TxyRegionInfoDTO::getZoneName).collect(Collectors.toSet());
        for (TxyRegionInfoDTO txyRegionInfoDTO : extRegionList) {
            if (!zoneNameSet.contains(txyRegionInfoDTO.getZoneName())){
                allTxyRegionInfo.add(txyRegionInfoDTO);
            }
        }
    }

    /** 地域到国家 */
    private Map<String, String> getRegionName2CountryMap() {
        Map<String, String> map = regionName2CountryMap.get();
        if (map == null) {
            List<SoeRegionNameCountryDO> region2Country = commonService.getRegion2Country();
            map = ListUtils.toMap(region2Country, SoeRegionNameCountryDO::getRegionName, SoeRegionNameCountryDO::getCountryName);
            regionName2CountryMap.set(new CleanHashMap<>(map, "地域到国家"));
        }
        return map;
    }

    @Override
    public void clean(List<? extends CommonItem> commonItem, Boolean useShowStrategy) {
        boolean useShow = BooleanUtils.isTrue(useShowStrategy);
        if (ListUtils.isNotEmpty(commonItem)) {
            for (CommonItem item : commonItem) {
                doClean(item, useShow);
            }
        }
    }

    @Override
    public void simpleClean(List<? extends IDistributeClean> distributeCleans) {
        if (ListUtils.isNotEmpty(distributeCleans)) {
            for (IDistributeClean item : distributeCleans) {
                // 清洗境内外，区域，地域，可用区
                cleanRegion(item);
                // 清洗cvm
                cleanCvm(item);
                // 清洗通用客户简称cvm
                if (item instanceof IUnCustomerShortName){
                    cleanUnCustomerShortName((IUnCustomerShortName) item);
                }
                // 清洗合并机型
                if (item instanceof IUnInstanceType){
                    cleanUnInstanceType((IUnInstanceType) item);
                }
                if (item instanceof ICleanCrpWarZone){
                    cleanCrpWarZone((ICleanCrpWarZone) item);
                }
            }
        }
    }


    /** 清洗 crp 战区 */
    private void cleanCrpWarZone(ICleanCrpWarZone obj){
        Map<String, String> customerShortName2CrpWarZoneMap = getCustomerShortName2CrpWarZoneMap();
        String customerShortName = obj.getCustomerShortName();
        if (SoeCommonUtils.isNotBlank(customerShortName)) {
            String crpWarZone = customerShortName2CrpWarZoneMap.get(customerShortName);
            if (SoeCommonUtils.isNotBlank(crpWarZone)){
                obj.setWarZone(crpWarZone);
                if (!SoeCommonUtils.isNotBlank(obj.getPanShiWarZone())){
                    obj.setPanShiWarZone(crpWarZone);
                }
            }
        }
    }

    /**
     * 执行清洗
     */
    private void doClean(CommonItem item, boolean useShow) {
        // 清洗境内外，区域，地域，可用区
        cleanRegion(item);
        // 清洗cvm
        cleanCvm(item);
        // 清洗gpu
        cleanGpu(item);
        // 清理原始机型卡型和机型族
        cleanOrigin(item);
        // 是否启用展示策略
        if (useShow) {
            cleanUseShow(item);
        }
        // 清洗年周
        cleanYearWeek(item);
    }

    /** 清理原始机型卡型和机型族 */
    private void cleanOrigin(CommonItem item) {
        item.setOriginGinFamily(item.getGinsFamily());
        item.setOriginInstanceType(item.getInstanceType());
        item.setOriginGpuType(item.getGpuType());
    }

    /**
     * 清洗CVM（设备类型转实例类型，实例类型查机型族）
     */
    private void cleanCvm(IInstanceTypeClean item) {
        // 非CVM不清洗
        if (!Objects.equals(item.getProductType(), ProductTypeEnum.CVM.getCode())) {
            return;
        }
        String deviceType = item.getDeviceType();
        String instanceType = item.getInstanceType();
        String ginsFamily = item.getGinsFamily();
        // step1：设备类型清洗实例类型
        if (StringUtils.isNotBlank(deviceType) && StringUtils.isBlank(instanceType)) {
            Map<String, CvmType> map = getCvmTypeByDeviceTypeMap();
            CvmType cvmType = map.get(deviceType);
            if (cvmType != null) {
                instanceType = cvmType.getInstanceType();
                ginsFamily = cvmType.getGinsFamily();
            }
        }

        if (StringUtils.isNotBlank(instanceType) && StringUtils.isBlank(ginsFamily)) {
            // step2：实例类型清洗机型族
            Map<String, CvmType> map = getCvmTypeMap();
            CvmType cvmType = map.get(instanceType);
            if (cvmType != null) {
                instanceType = cvmType.getInstanceType();
                ginsFamily = cvmType.getGinsFamily();
            }
        }

        item.setInstanceType(SoeCommonUtils.defaultEmptyIfBlank(instanceType));
        item.setGinsFamily(SoeCommonUtils.defaultEmptyIfBlank(ginsFamily));
    }

    /**
     * 清洗Gpu（设备类型转卡类型，卡类型转卡型）
     */
    private void cleanGpu(CommonItem item) {
        // 非GPU不清洗
        if (!Objects.equals(item.getProductType(), ProductTypeEnum.GPU.getCode())) {
            return;
        }


        String deviceType = item.getDeviceType();
        String gpuType = item.getGpuType();
        String ginsFamily = item.getGinsFamily();
        String instanceType = item.getInstanceType();
        BigDecimal coreNum = item.getCoreNum();
        BigDecimal num = item.getNum();

        // step1：设备类型清洗 卡类型 和 卡数
        if (SoeCommonUtils.isNotBlank(deviceType) && !SoeCommonUtils.isNotBlank(gpuType)) {
            Map<String, GpuType> map = getGpuTypeByDeviceTypeMap();
            GpuType type = map.get(deviceType);
            if (type != null) {
                gpuType = type.getGpuType();
                ginsFamily = type.getGinsFamily();
                if (coreNum == null && num != null) {
                    coreNum = num.multiply(type.getGpuNum());
                }
            }
        }

        // step2：实例类型清洗 核对卡型（如果匹配不是使用自己清洗的，而不是数据源的）
        if (SoeCommonUtils.isNotBlank(instanceType)){
            Map<String, GpuType> map = getGpuTypeByInstanceType();
            GpuType type = map.get(instanceType);
            if (type!=null && !Objects.equals(type.getGpuType(),gpuType)){
                gpuType = type.getGpuType();
                ginsFamily = type.getGinsFamily();
            }
        }


        // step3：卡类型清洗 卡型（推理等）
        if (SoeCommonUtils.isNotBlank(gpuType) && !SoeCommonUtils.isNotBlank(ginsFamily)) {
            Map<String, GpuType> map = getGpuTypeMap();
            GpuType type = map.get(gpuType.toLowerCase());
            if (type != null) {
                ginsFamily = type.getGinsFamily();
                // 有些gpuType名称不一致（cubesGpuType），这里统一改动策略表的gpuType
                gpuType = type.getGpuType();
                if (coreNum == null && num != null) {
                    coreNum = num.multiply(type.getGpuNum());
                }
            }
        }

        item.setGpuType(SoeCommonUtils.defaultEmptyIfBlank(gpuType));
        item.setGinsFamily(SoeCommonUtils.defaultEmptyIfBlank(ginsFamily));
        item.setCoreNum(coreNum);
    }

    private Map<String, GpuType> getGpuTypeByInstanceType() {
        Map<String, GpuType> ret = gpuTypeByInstanceTypeMap.get();
        if (ret != null) {
            return ret;
        }
        ret = new CleanHashMap<>("实例类型 ---> gpu机型族信息");

        List<GpuType> allGpuType = commonService.getAllGpuType();
        for (GpuType gpuType : allGpuType) {
            if (StringUtils.isNotBlank(gpuType.getInstanceType())) {
                ret.put(gpuType.getInstanceType(), gpuType);
            }
        }

        return ret;
    }

    /**
     * 是否使用展示策略清洗
     */
    private void cleanUseShow(CommonItem item) {
        cleanCvmUseShow(item);
        cleanGpuUseShow(item);
    }

    /**
     * cvm展示清洗
     */
    private void cleanCvmUseShow(CommonItem item) {
        if (!Objects.equals(item.getProductType(), ProductTypeEnum.CVM.getCode())) {
            return;
        }
        Set<String> instanceTypeSet = getLatestStrategyByProduct(ProductTypeEnum.CVM.getCode());
        // 不在统计范围内设置为其他
        String instanceType = item.getInstanceType();
        String ginsFamily = item.getGinsFamily();
        if (!instanceTypeSet.contains(instanceType)) {
            instanceType = cloud.demand.app.modules.soe.enums.Constant.other;
            ginsFamily = cloud.demand.app.modules.soe.enums.Constant.other;
        }
        item.setInstanceType(instanceType);
        item.setGinsFamily(ginsFamily);
    }

    /**
     * gpu展示清洗
     */
    private void cleanGpuUseShow(CommonItem item) {
        // 非GPU不清洗
        if (!Objects.equals(item.getProductType(), ProductTypeEnum.GPU.getCode())) {
            return;
        }
        Set<String> gpuTypeSet = getLatestStrategyByProduct(ProductTypeEnum.GPU.getCode());
        // 不在统计范围内设置为其他
        String gpuType = item.getGpuType();
        String ginsFamily = item.getGinsFamily();
        if (!gpuTypeSet.contains(gpuType)) {
            gpuType = cloud.demand.app.modules.soe.enums.Constant.other;
            ginsFamily = cloud.demand.app.modules.soe.enums.Constant.other;
        }
        item.setGpuType(gpuType);
        item.setGinsFamily(ginsFamily);
    }


    private void cleanYearWeek(IYearWeekClean clean) {
        String yearWeek = clean.getYearWeek();
        if (StringUtils.isNotBlank(yearWeek)) {
            clean.setYearWeek(SoeCommonUtils.yearWeekFormat(yearWeek));
        }
    }
}
