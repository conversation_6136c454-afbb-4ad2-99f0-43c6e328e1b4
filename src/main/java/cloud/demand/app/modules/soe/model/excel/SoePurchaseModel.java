package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/** 采购数据 */
@Data
public class SoePurchaseModel extends SoeCommonCustomerDeviceModel{

    @ExcelProperty(value = "预计交付日期",index = 15)
    private String slaDateExpect;

    @ExcelProperty(value = "期望交付日期",index = 16)
    private String quotaUseTime;


    @ExcelProperty(value = "是否无货期",index = 17)
    private String isNoExpect;

}
