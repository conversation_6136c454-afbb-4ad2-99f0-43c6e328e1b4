package cloud.demand.app.modules.soe.enums.index;

import cloud.demand.app.modules.soe.enums.StoreNameEnum;
import cloud.demand.app.modules.soe.model.index.IndexTree;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * soe概览指标枚举
 */
@Getter
public enum SoeOverviewIndexEnum implements ISoeIndexEnum {

    // =============== 进-采购 ================
    soe_purchase("供应-采购@采购到货", StoreNameEnum.soe_purchase, true),
    soe_no_expect("供应-采购@采购（无货期）", StoreNameEnum.soe_no_expect, true),
    soe_not_executed("供应-采购@待执行", StoreNameEnum.soe_not_executed, true),

    soe_return("供应-采购@退回", StoreNameEnum.soe_return, true),

    soe_purchase_total("供应-采购", null),

    // =============== 销-预测净增 ================
    soe_forecast_add("需求-预测净增@新增", StoreNameEnum.soe_forecast_add, true),

    soe_forecast_return("需求-预测净增@退回", StoreNameEnum.soe_forecast_return, true),

    soe_scale_net("需求-预测净增@实际净增", StoreNameEnum.soe_scale_net, false),

    soe_forecast_total("需求-预测净增", null),

    // =============== 存-期初库存（好料） ================

    soe_inventory_change("库存-库存变化（采购-预测净增）", null, false),

    soe_inventory_start_actual("库存-期初库存(实际)", StoreNameEnum.soe_inventory_start, false),

    soe_inventory_start("库存-期初库存", StoreNameEnum.soe_inventory_start, true),

    soe_inventory_start_for_end("库存-期初库存+库存变化（好料）", null, false),

    // =============== 存-期末库存（好料） ================
    soe_inventory_end_security("库存-期末库存@安全库存", StoreNameEnum.soe_inventory_end_security, true),
    soe_inventory_end_turnover("库存-期末库存@周转库存", StoreNameEnum.soe_inventory_end_turnover, true),

    soe_inventory_end_total("库存-期末库存", null),

    // 这里要先计算总的，冗余库存是通过总的减去（安全+周转）
    soe_inventory_end_redundancy("库存-期末库存@冗余库存", null, false),

    // =============== 冗余系数 ================
    soe_redundancy_coefficient("冗余系数", null, false),

    // =============== 模拟数据 ================
    soe_mock("模拟数据", StoreNameEnum.soe_mock, false, false, true),

    // =============== 端到端利用率 ================

    soe_purchase_change("端到端利用率-采购变化量", null, false),

    soe_forecast_change("端到端利用率-需求变化量", null, false),

    // 期初
    soe_end_to_end_scale_start("端到端利用率-期初@售卖规模", StoreNameEnum.soe_end_to_end_scale_start, true),

    soe_end_to_end_scale_start_for_end("端到端利用率-期初@售卖规模 + 需求-预测净增", null, false),

    soe_end_to_end_total_start("端到端利用率-期初@物理总规模", StoreNameEnum.soe_end_to_end_total_start, true),

    soe_end_to_end_total_start_for_end("端到端利用率-期初@物理总规模 + 供应-采购", null, false),

    soe_end_to_end_start("端到端利用率-期初", null),

    // 期末
    // 期末的售卖规模 = 期初的售卖规模 + 需求-预测净增
    soe_end_to_end_scale_end("端到端利用率-期末@售卖规模", null, false),

    // 期末的物理总规模 = 期初的物理总规模 + 供应-采购
    soe_end_to_end_total_end("端到端利用率-期末@物理总规模", null, false),

    soe_end_to_end_avg_utilization_rate_end("端到端利用率-期末@均值目标", StoreNameEnum.soe_end_to_end_avg_utilization_rate_end, false),

    // 期末的物理总规模 = 期初的物理总规模 + 供应-采购
    soe_end_to_end_year_utilization_rate_end("端到端利用率-期末@年底目标", StoreNameEnum.soe_end_to_end_year_utilization_rate_end, false),

    soe_end_to_end_end("端到端利用率-期末", null, false),
    ;
    private final String name;

    private final StoreNameEnum storeName;

    private final Boolean hasMock;

    private final Boolean hasExcel;

    private final Boolean hide;

    SoeOverviewIndexEnum(String name, StoreNameEnum storeName) {
        this(name, storeName, false);
    }

    SoeOverviewIndexEnum(String name, StoreNameEnum storeName, Boolean hasMock) {
        this(name, storeName, hasMock, storeName != null);
    }

    SoeOverviewIndexEnum(String name, StoreNameEnum storeName, Boolean hasMock, Boolean hasExcel) {
        this(name, storeName, hasMock, hasExcel, false);
    }

    SoeOverviewIndexEnum(String name, StoreNameEnum storeName, Boolean hasMock, Boolean hasExcel, Boolean hide) {
        this.name = name;
        this.storeName = storeName;
        this.hasMock = hasMock;
        this.hasExcel = hasExcel;
        this.hide = hide;
    }

    public static List<IndexTree> getTree() {
        return getTree(false, false);
    }

    /**
     * 获取mock的名称
     */
    public static List<String> getMockName() {
        List<String> ret = new ArrayList<>();
        for (SoeOverviewIndexEnum value : values()) {
            if (BooleanUtils.isTrue(value.hide)) {
                continue;
            }
            if (BooleanUtils.isTrue(value.hasMock)) {
                ret.add(value.name);
            }
        }
        return ret;
    }

    public static List<IndexTree> getTree(boolean onlyMock, boolean onExcel) {
        List<IndexTree> indexTrees = new ArrayList<>();
        // 构建index树
        for (SoeOverviewIndexEnum value : SoeOverviewIndexEnum.values()) {
            if (BooleanUtils.isTrue(value.hide)) {
                continue;
            }
            if (onlyMock && !BooleanUtils.isTrue(value.hasMock)) {
                continue;
            }
            if (onExcel && !BooleanUtils.isTrue(value.hasExcel)) {
                continue;
            }
            String name = value.getName();
            String[] split = name.split("@");
            List<IndexTree> temp = indexTrees;
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                IndexTree indexTree = temp.stream().filter(item -> item.getName().equals(s)).findFirst().orElse(null);
                if (indexTree == null) {
                    indexTree = new IndexTree();
                    indexTree.setName(s);
                    temp.add(indexTree);
                }
                if (i == split.length - 1) {
                    indexTree.setFullName(name);
                } else {
                    temp = indexTree.getChildren();
                    if (temp == null) {
                        temp = new ArrayList<>();
                        indexTree.setChildren(temp);
                    }
                }
            }
        }
        return indexTrees;
    }

    /**
     * 是否为无年月指标
     */
    public static boolean isNoYearMonth(String fullLabel) {
        // 无年月的指标集合
        List<String> noYearMonthIndex = ListUtils.newList(
                soe_inventory_start.getName(),
                soe_end_to_end_total_start.getName(),
                soe_end_to_end_scale_start.getName()
        );
        return noYearMonthIndex.contains(fullLabel);
    }
}
