package cloud.demand.app.modules.soe.entitiy;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** 预测 */
@Data
public class SoeForecastDO {

    /** 产品类型 */
    @Column("product_type")
    private String productType;

    /** 实例类型 */
    @Column("instance_type")
    private String instanceType;

    /** 实例规格 */
    @Column("instance_model")
    private String instanceModel;

    /** 地域 */
    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    /** 卡型 */
    @Column("gpu_type")
    private String gpuType;

    /** 行业部门 */
    @Column("industry_dept")
    private String industryDept;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    /** 核数 */
    @Column("core_num")
    private BigDecimal coreNum;

    /** 赢率 */
    @Column("win_rate")
    private BigDecimal winRate;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 年周 */
    @Column("year_week")
    private String yearWeek;


}
