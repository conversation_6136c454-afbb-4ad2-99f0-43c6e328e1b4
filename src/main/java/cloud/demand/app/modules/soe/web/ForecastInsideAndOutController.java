package cloud.demand.app.modules.soe.web;


import cloud.demand.app.modules.mrpv3.utils.CommonUtils;
import cloud.demand.app.modules.soe.dto.fio.ReportFioAdvanceWeekResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioMetaDataResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioReq;
import cloud.demand.app.modules.soe.dto.fio.ReportFioResp;
import cloud.demand.app.modules.soe.dto.fio.ReportFioV2Resp;
import cloud.demand.app.modules.soe.dto.req.YearMonthReq;
import cloud.demand.app.modules.soe.model.excel.ReportFioModel;
import cloud.demand.app.modules.soe.model.excel.ReportFioV2Model;
import cloud.demand.app.modules.soe.service.FioReportService;
import cloud.demand.app.web.model.common.StreamDownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/** 预测内外（不属于SOE，这里不应该放到soe，有时间在移出去） */
@Slf4j
@JsonrpcController("/fio")
public class ForecastInsideAndOutController {

    @Resource
    private FioReportService fioReportService;

    /** 预测内外报表接口 */
    @RequestMapping
    public ReportFioResp queryReport(@JsonrpcParam @Valid ReportFioReq req){
        if (req.getStatTime() == null){
            req.setStatTime(fioReportService.getMaxStatTime());
        }
        req.setGenerationInstanceType(CommonUtils.fillGenerationInstanceType(req.getGenerationInstanceType()));
        return fioReportService.queryReport(req);
    }

    /** 预测内外报表接口v2 */
    @RequestMapping
    public ReportFioV2Resp queryReportV2(@JsonrpcParam @Valid ReportFioReq req){
        if (req.getStatTime() == null){
            req.setStatTime(fioReportService.getMaxStatTime());
        }
        req.setGenerationInstanceType(CommonUtils.fillGenerationInstanceType(req.getGenerationInstanceType()));
        return fioReportService.queryReportV2(req);
    }

    /** 预测内外报表接口 */
    @RequestMapping
    public ReportFioResp queryReportV3(@JsonrpcParam @Valid ReportFioReq req){
        if (req.getStatTime() == null){
            req.setStatTime(fioReportService.getMaxStatTime());
        }
        req.setGenerationInstanceType(CommonUtils.fillGenerationInstanceType(req.getGenerationInstanceType()));
        return fioReportService.queryReportV3(req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> excel(@JsonrpcParam @Valid ReportFioReq req){
        if (req.getStatTime() == null){
            req.setStatTime(fioReportService.getMaxStatTime());
        }
        req.setGenerationInstanceType(CommonUtils.fillGenerationInstanceType(req.getGenerationInstanceType()));
        req.setAllowZeroOrder(true);
        ReportFioResp resp = fioReportService.queryReport(req);

        List<ReportFioModel> excelData = ListUtils.transform(resp.getData(), ReportFioModel::transform);

        ByteArrayInputStream in;

        try(ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out,ReportFioModel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("预测内外")
                    .doWrite(excelData);
            in = new ByteArrayInputStream(out.toByteArray());
        }catch (Exception e){
            log.error(e.getMessage());
            throw new ITException("导出预测内外数据失败");
        }

        String filename = "预测内外-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> excelV2(@JsonrpcParam @Valid ReportFioReq req){
        if (req.getStatTime() == null){
            req.setStatTime(fioReportService.getMaxStatTime());
        }
        req.setGenerationInstanceType(CommonUtils.fillGenerationInstanceType(req.getGenerationInstanceType()));
        ReportFioV2Resp resp = fioReportService.queryReportV2(req);

        List<ReportFioV2Model> excelData = ListUtils.transform(resp.getData(), ReportFioV2Model::transform);

        ByteArrayInputStream in;

        try(ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            EasyExcel.write(out,ReportFioV2Model.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("预测内外")
                    .doWrite(excelData);
            in = new ByteArrayInputStream(out.toByteArray());
        }catch (Exception e){
            log.error(e.getMessage());
            throw new ITException("导出预测内外数据失败");
        }

        String filename = "预测内外-" + DateUtils.format(new Date()) + UUID.randomUUID().toString().substring(0, 8) + ".xlsx";
        return new StreamDownloadBean(filename, in);
    }

    /** 元数据 */
    @RequestMapping
    public ReportFioMetaDataResp getMetaData(){
        return ReportFioMetaDataResp.build();
    }

    /** 提前周查询 */
    @RequestMapping
    public ReportFioAdvanceWeekResp getAdvanceWeek(@JsonrpcParam @Valid YearMonthReq req){
        return fioReportService.getAdvanceWeek(req);
    }
}
