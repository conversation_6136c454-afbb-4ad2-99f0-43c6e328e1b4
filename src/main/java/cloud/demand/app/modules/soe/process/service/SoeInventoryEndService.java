package cloud.demand.app.modules.soe.process.service;

import cloud.demand.app.modules.soe.model.item.SoeInventoryEndItem;
import cloud.demand.app.modules.soe.model.req.SoeInventoryEndReq;

import java.util.List;

/** 存-期末库存（好料）（安全库存，周转库存） */
public interface SoeInventoryEndService {

    /** 安全库存 */
    public List<SoeInventoryEndItem> getSafetyData(SoeInventoryEndReq req);

    /** 预测安全库存 */
    public List<SoeInventoryEndItem> getForecastSafetyData(SoeInventoryEndReq req);

    /** 周转库存 */
    public List<SoeInventoryEndItem> getTurnover(SoeInventoryEndReq req);

    /** 预测周转库存 */
    public List<SoeInventoryEndItem> getForecastTurnover(SoeInventoryEndReq req);

}
