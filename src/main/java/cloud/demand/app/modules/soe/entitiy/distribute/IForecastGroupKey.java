package cloud.demand.app.modules.soe.entitiy.distribute;

public interface IForecastGroupKey {
    public String getProductType();
    public String getProduct();
    public String getYearMonth();
    public String getModType();
    public String getIndustryDept();
    public String getWarZone();
    public String getCustomerShortName();
    public String getUnCustomerShortName();
    public String getCustomhouseTitle();
    public String getCountryName();
    public String getRegionName();

    public String getUnInstanceType();
    public String getInstanceType();
    public void setProductType(String v);
    public void setProduct(String v);
    public void setYearMonth(String v);
    public void setIndustryDept(String v);
    public void setModType(String v);
    public void setWarZone(String v);
    public void setCustomerShortName(String v);
    public void setUnCustomerShortName(String v);
    public void setCustomhouseTitle(String v);
    public void setCountryName(String v);
    public void setRegionName(String v);
    public void setUnInstanceType(String v);
    public void setInstanceType(String v);
}
