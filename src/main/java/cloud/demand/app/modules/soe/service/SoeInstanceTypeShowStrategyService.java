package cloud.demand.app.modules.soe.service;

import cloud.demand.app.modules.soe.dto.other.SoeInstanceTypeShowStrategyReq;
import cloud.demand.app.modules.soe.dto.resp.ErrorMessage;
import cloud.demand.app.modules.soe.entitiy.SoeInstanceTypeShowStrategyDO;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/** 实例类型展示策略 */
public interface SoeInstanceTypeShowStrategyService {

    /** 导入 */
    List<ErrorMessage> uploadExcel(MultipartFile file);

    /** 查询数据 */

    List<SoeInstanceTypeShowStrategyDO> getAll(SoeInstanceTypeShowStrategyReq req);

    /** 导出 */
    ResponseEntity<InputStreamResource> templateExcel();

}
