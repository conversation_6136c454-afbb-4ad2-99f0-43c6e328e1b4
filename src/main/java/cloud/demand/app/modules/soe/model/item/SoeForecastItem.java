package cloud.demand.app.modules.soe.model.item;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.soe.entitiy.SoeForecastDO;
import cloud.demand.app.modules.soe.model.CommonItem;
import cloud.demand.app.modules.soe.model.fields.ICustomerShortName;
import cloud.demand.app.modules.soe.model.fields.IIndustryDept;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/** 预测净增 */
@Data
public class SoeForecastItem extends CommonItem implements IIndustryDept, ICustomerShortName {

    /** 客户简称 */
    private String customerShortName;

    /** 行业部门 */
    private String industryDept;

    /** 赢率 */
    private BigDecimal winRate;

    /** 当月比率（按照切片日期处理比率） */
    private BigDecimal curYearMonthRate;

    /** 需求来源 */
    private String demandSource;

    /** 预约单号 */
    private String yunxiaoOrderId;

    /** 起始购买时间 */
    private String beginBuyDate;

    /** 结束购买时间 */
    private String endBuyDate;

    private String demandType;

    public static SoeForecastItem transformMock(SoeMockItem mockItem,String demandType){
        SoeForecastItem soeForecastItem = new SoeForecastItem();
        soeForecastItem.setCustomerShortName(mockItem.getCustomerShortName());
        soeForecastItem.setIndustryDept(mockItem.getIndustryDept());
        soeForecastItem.setProductType(mockItem.getProductType());
        soeForecastItem.setYearMonth(mockItem.getYearMonth());
        soeForecastItem.setYearWeek(mockItem.getYearWeek());
        soeForecastItem.setOriginInstanceType(mockItem.getOriginInstanceType());
        soeForecastItem.setInstanceType(mockItem.getInstanceType());
        soeForecastItem.setDeviceType(mockItem.getDeviceType());
        soeForecastItem.setOriginGinFamily(mockItem.getOriginGinFamily());
        soeForecastItem.setGinsFamily(mockItem.getGinsFamily());
        soeForecastItem.setOriginGpuType(mockItem.getOriginGpuType());
        soeForecastItem.setGpuType(mockItem.getGpuType());
        soeForecastItem.setCustomhouseTitle(mockItem.getCustomhouseTitle());
        soeForecastItem.setCountryName(mockItem.getCountryName());
        soeForecastItem.setAreaName(mockItem.getAreaName());
        soeForecastItem.setRegionName(mockItem.getRegionName());
        soeForecastItem.setZoneName(mockItem.getZoneName());
        soeForecastItem.setCoreNum(mockItem.getCoreNum());
        soeForecastItem.setNum(mockItem.getNum());
        soeForecastItem.setIndustryDept(mockItem.getIndustryDept());
        soeForecastItem.setCustomerShortName(mockItem.getCustomerShortName());
        // 模拟数据赢率默认为1
        if (Objects.equals(soeForecastItem.getProductType(), ProductTypeEnum.GPU.getCode())){
            soeForecastItem.setWinRate(BigDecimal.ONE);
        }
        soeForecastItem.setDemandType(demandType);
        return soeForecastItem;
    }

    public static SoeForecastItem transform(SoeForecastDO entity) {
        SoeForecastItem ret = new SoeForecastItem();
        ret.setCoreNum(entity.getCoreNum());
        ret.setYearMonth(entity.getYearMonth());
        ret.setYearWeek(entity.getYearWeek());
        ret.setInstanceType(entity.getInstanceType());
        ret.setRegionName(entity.getRegionName());
        ret.setZoneName(entity.getZoneName());
        ret.setGpuType(entity.getGpuType());
        ret.setProductType(entity.getProductType());
        ret.setIndustryDept(entity.getIndustryDept());
        ret.setCustomerShortName(entity.getCustomerShortName());
        if (entity.getWinRate() != null && entity.getWinRate().compareTo(BigDecimal.ZERO) > 0){
            ret.setWinRate(entity.getWinRate());
        }
        return ret;
    }
}
