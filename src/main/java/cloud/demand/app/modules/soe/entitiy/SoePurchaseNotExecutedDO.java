package cloud.demand.app.modules.soe.entitiy;

import cloud.demand.app.modules.soe.model.item.SoePurchaseItem;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SoePurchaseNotExecutedDO {

    /** 产品类型，CVM，GPU */
    @Column("product_type")
    private String productType;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 年周 */
    @Column("year_week")
    private String yearWeek;

    @Column("dim_device_type")
    private String deviceType;

    /** 实例类型 */
    @Column("dim_instance_type")
    private String instanceType;

    /** 国内外 */
    @Column("dim_region_class")
    private String regionClass;

    /** 地域 */
    @Column("dim_region")
    private String regionName;

    /** 国家 */
    @Column("dim_country")
    private String countryName;

    /** 核数 */
    @Column("core_num")
    private BigDecimal coreNum;

    @Column("device_num")
    private BigDecimal num;

    public static SoePurchaseItem transform(SoePurchaseNotExecutedDO executedDO){
        SoePurchaseItem soePurchaseItem = new SoePurchaseItem();
        soePurchaseItem.setProductType(executedDO.getProductType());
        soePurchaseItem.setYearMonth(executedDO.getYearMonth());
        soePurchaseItem.setYearWeek(executedDO.getYearWeek());
        soePurchaseItem.setInstanceType(executedDO.getInstanceType());
        soePurchaseItem.setDeviceType(executedDO.getDeviceType());
//        soePurchaseItem.setGinsFamily();
//        soePurchaseItem.setGpuType();
        soePurchaseItem.setCustomhouseTitle(executedDO.getRegionClass());
        soePurchaseItem.setCountryName(executedDO.getCountryName());
//        soePurchaseItem.setAreaName();
        soePurchaseItem.setRegionName(executedDO.getRegionName());
//        soePurchaseItem.setZoneName();
        soePurchaseItem.setCoreNum(executedDO.getCoreNum());
        soePurchaseItem.setNum(executedDO.getNum());
        return soePurchaseItem;


    }
}
