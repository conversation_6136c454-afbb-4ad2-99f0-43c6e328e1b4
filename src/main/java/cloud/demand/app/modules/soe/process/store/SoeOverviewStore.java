package cloud.demand.app.modules.soe.process.store;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.mrpv2.entity.BasInnerIndustryCustomerDO;
import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv2.service.BasInnerIndustryCustomerService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.soe.dto.other.SoeMockReq;
import cloud.demand.app.modules.soe.dto.req.SoeCommonReq;
import cloud.demand.app.modules.soe.dto.req.SoeOverviewReq;
import cloud.demand.app.modules.soe.enums.DemandStrategyEnum;
import cloud.demand.app.modules.soe.enums.InventoryStrategyEnum;
import cloud.demand.app.modules.soe.enums.MockStatusEnum;
import cloud.demand.app.modules.soe.enums.index.SoeOverviewIndexEnum;
import cloud.demand.app.modules.soe.model.CommonItem;
import cloud.demand.app.modules.soe.model.CommonReq;
import cloud.demand.app.modules.soe.model.item.*;
import cloud.demand.app.modules.soe.model.req.*;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.soe.model.sql.SoeSqlParams;
import cloud.demand.app.modules.soe.process.service.*;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.service.SoeEndToEndUtilizationRateService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import cloud.demand.app.modules.sop_util.process.store.IDBData;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import java.math.BigDecimal;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * soe概览仓库注册
 */
@Component
@StoreRegisterClient
public class SoeOverviewStore {
    @Resource
    private SoeCommonService commonService;

    /**
     * 模拟数据
     */
    @Resource
    private SoeMockService mockService;

    /**
     * 采购
     */
    @Resource
    private SoePurchaseService purchaseService;

    /**
     * 售卖
     */
    @Resource
    private SoeScaleService scaleService;

    /**
     * 预测
     */
    @Resource
    private SoeForecastService forecastService;


    /**
     * 期初库存
     */
    @Resource
    private SoeInventoryStartService inventoryStartService;

    /**
     * 期末库存
     */
    @Resource
    private SoeInventoryEndService inventoryEndService;

    /**
     * 端到端
     */
    @Resource
    private SoeEndToEndService endToEndService;

    /**
     * 端到端目标利用率
     */
    @Resource
    private SoeEndToEndUtilizationRateService endToEndUtilizationRateService;

    /**
     * 产品字典表
     */
    @Resource
    private MrpV2DictService mrpV2DictService;

    /**
     * 内领行业客户（通常指 TCI 行业）
     */
    @Resource
    private BasInnerIndustryCustomerService innerIndustryCustomerService;

    /**
     * 清洗
     */
    @Resource
    private SoeCleanService cleanService;

    @Resource
    private RedisHelper redisHelper;


    @StoreRegister(name = "soe_init", desc = "初始化参数")
    public List<IDBData> initVersion(Map<String, Object> params) {
        List<String> showStrategy = (List<String>) params.get("show_strategy");
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        ObjectSqlBuilder sqlBuilder = (ObjectSqlBuilder) params.get("sqlBuilder");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        // Soe 端对端目标利用率使用的过滤器
        List<Predicate<CommonItem>> soeEndToEndUtilizationRateFilter = (List<Predicate<CommonItem>>) params.get("soeEndToEndUtilizationRateFilter");
        // 需要展示的机型集合
        showStrategy.addAll(commonService.getLatestStrategyByProduct(req.getProduct()));
        // req 初始化
        SoeCommonReq.initAndCheck(req);
        // 参数初始化
        SoeSqlParams.transform(sqlParams, req);
        // 如果是 Max 需求口径特殊处理
        minVersionYearMonth(sqlParams, req);

        // 产品集合
        List<PplConfigProductEnumDO> enumDOS = mrpV2DictService.queryAllProductEnum(null, true);
        Map<String, List<PplConfigProductEnumDO>> flagGroup = ListUtils.groupBy(enumDOS, PplConfigProductEnumDO::getFlag);

        // 设置productClass
        List<String> productClass = new ArrayList<>();
        // CVM产品查询CVM+PAAS，否则为GPU
        if (Objects.equals(sqlParams.getProductType(), ProductTypeEnum.CVM.getCode())) {
            List<PplConfigProductEnumDO> cvmProduct = flagGroup.get("CVM");
            if (ListUtils.isNotEmpty(enumDOS)) {
                productClass.addAll(cvmProduct.stream().map(PplConfigProductEnumDO::getProductName).collect(Collectors.toList()));
            }
            List<PplConfigProductEnumDO> paasProduct = flagGroup.get("PAAS");
            if (ListUtils.isNotEmpty(paasProduct)) {
                productClass.addAll(paasProduct.stream().map(PplConfigProductEnumDO::getProductName).collect(Collectors.toList()));
            }
        } else {
            List<PplConfigProductEnumDO> gpuProduct = flagGroup.get("GPU");
            if (ListUtils.isNotEmpty(gpuProduct)) {
                productClass.addAll(gpuProduct.stream().map(PplConfigProductEnumDO::getProductName).collect(Collectors.toList()));
            }
        }
        sqlParams.setProductClass(productClass);
        // 过去内领客户
        List<BasInnerIndustryCustomerDO> list = innerIndustryCustomerService.getListWithCache();
        List<BasInnerIndustryCustomerDO.IndustryCustomer> transform = BasInnerIndustryCustomerDO.transform(list);
        StringBuilder str = new StringBuilder();
        String sql = "(industry_dept = '${industry_dept}' and customer_short_name in ('${customer_short_name}'))";
        for (int i = 0; i < transform.size(); i++) {
            BasInnerIndustryCustomerDO.IndustryCustomer industryCustomer = transform.get(i);
            String tempSql = SimpleSqlBuilder.doReplace(sql, "industry_dept", industryCustomer.getIndustryDept());
            tempSql = SimpleSqlBuilder.doReplace(tempSql, "customer_short_name", StringUtils.join(industryCustomer.getCustomerShortName(), "','"));
            str.append(tempSql);
            if (i != transform.size() - 1) {
                str.append(" or ");
            }
        }
        // 如果需要缓存的话，这里先查一遍，由于所以其他步骤都需要等 soe_init 执行完，所以这里缓存之后，后续步骤查一定是用缓存数据
        if (BooleanUtils.isTrue((Boolean) params.get("need_cache_safe_inv"))) {
            // 安全库存，周转库存（如果查询当前量则初始化的时候缓存）
            if (InventoryStrategyEnum.getByName(req.getInventoryStrategy()) == InventoryStrategyEnum.Current) {
                // 提前查一次，做为本地缓存
                commonService.getActualAndSafeInv(OperationViewReq.transform(req), redisHelper.getString("operationViewAlgorithm"));
            }
        }

        sqlParams.setInnerIndustryCustomer(str.toString());

        // 设置sqlBuilder
        sqlBuilder.addSqlParams(sqlParams);

        // 构建过滤器
        filter.addAll(SoeOverviewReq.buildFilter(req));
        // Soe 端对端目标利用率使用的过滤器
        soeEndToEndUtilizationRateFilter.addAll(SoeOverviewReq.buildInstanceFilter(req));
        return null;
    }

    /**
     * 设置最新版本年月（只对 Max 口径生效）
     * -- 1. 查询对应切片需求汇总表【最大 version_code】
     * -- 2. 通过ppl_version获取 【1. 最大version_code】的起始需求年月
     * -- 3. 在【2. 起始需求年月】之前的只看预约单
     * @param sqlParams sql参数
     * @param req 请求
     */
    private void minVersionYearMonth(SoeSqlParams sqlParams, SoeOverviewReq req) {
        String demandStrategy = req.getDemandStrategy();
        DemandStrategyEnum byName = DemandStrategyEnum.getByName(demandStrategy);
        // 保底有值
        String beginYearMonthByVersionCode = "1970-01";
        if (byName == DemandStrategyEnum.Forecast_Order){
            String maxVersionCode = commonService.getMaxVersionCode(req.getDemandStatTime());
            if (StringUtils.isNotBlank(maxVersionCode)){
                beginYearMonthByVersionCode = commonService.getBeginYearMonthByVersionCode(maxVersionCode);
            }
        }
        sqlParams.setMinVersionYearMonth(beginYearMonthByVersionCode);
    }

    @StoreRegister(name = "soe_mock", desc = "模拟数据")
    public List<SoeMockItem> soeMock(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        // 如果不使用模拟数据，这个返回null
        if (BooleanUtils.isNotTrue(req.getUseMock())) {
            return null;
        }
        SoeMockReq transform = SoeMockReq.transform(req);
        List<SoeMockItem> data = mockService.getData(transform);
        // 清洗（这里先不清洗展示机型，要先过滤原始机型在清洗）
        cleanService.clean(data, BooleanUtils.isTrue(req.getUseShowStrategy()));
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "soe_purchase", desc = "进-采购@采购到货")
    public List<SoePurchaseItem> soePurchase(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoePurchaseReq transform = SoePurchaseReq.transform(req);
        setCommonField(transform, params);

        // 采购到货
        List<SoePurchaseItem> ret = purchaseService.getPurchaseData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoePurchaseItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_purchase.getName()))
                    .map(SoePurchaseItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }


    @StoreRegister(name = "soe_no_expect", desc = "进-采购@无货期")
    public List<SoePurchaseItem> soeRelocation(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        // 不使用无货期则返回空集合
        if (BooleanUtils.isNotTrue(req.getUseShowNoExpect())) {
            return new ArrayList<>();
        }
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoePurchaseReq transform = SoePurchaseReq.transform(req);
        setCommonField(transform, params);
        List<SoePurchaseItem> ret = purchaseService.getNoExpectData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoePurchaseItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_no_expect.getName()))
                    .map(SoePurchaseItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_not_executed", desc = "进-采购@待执行")
    public List<SoePurchaseItem> soeNotExecuted(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoePurchaseReq transform = SoePurchaseReq.transform(req);
        setCommonField(transform, params);

        // 采购待执行
        List<SoePurchaseItem> ret = purchaseService.getNoExecutionData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoePurchaseItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_not_executed.getName()))
                    .map(SoePurchaseItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_return", desc = "进-采购@退回")
    public List<SoePurchaseItem> soeReturn(Map<String, Object> params) {
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        //暂无数据源
        List<SoePurchaseItem> ret = ListUtils.newArrayList();

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoePurchaseItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_return.getName()))
                    .map(SoePurchaseItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }
        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);
        return ret;
    }

    @StoreRegister(name = "soe_scale_external", desc = "规模净增@外部计费")
    public List<SoeScaleItem> soeScaleExternal(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeScaleReq transform = SoeScaleReq.transform(req);
        setCommonField(transform, params);
        List<SoeScaleItem> ret = scaleService.getExternalData(transform);
        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_scale_inner", desc = "规模净增@内部服务")
    public List<SoeScaleItem> soeScaleInner(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeScaleReq transform = SoeScaleReq.transform(req);
        setCommonField(transform, params);
        List<SoeScaleItem> ret = scaleService.getInnerData(transform);
        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_forecast_add", desc = "销-预测净增@新增")
    public List<SoeForecastItem> soeForecastAdd(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeForecastReq transform = SoeForecastReq.transform(req);
        setCommonField(transform, params);

        // 预测净增
        List<SoeForecastItem> ret = forecastService.getAddData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeForecastItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_forecast_add.getName()))
                    .map(item -> SoeForecastItem.transformMock(item, DemandTypeEnum.NEW.getName()))
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_forecast_return", desc = "销-预测净增@退回")
    public List<SoeForecastItem> soeForecastReturn(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeForecastReq transform = SoeForecastReq.transform(req);
        setCommonField(transform, params);

        // 预测净增
        List<SoeForecastItem> ret = forecastService.getRetData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeForecastItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_forecast_return.getName()))
                    .map(item -> SoeForecastItem.transformMock(item, DemandTypeEnum.RETURN.getName()))
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_scale_net", desc = "销-预测净增@实际净增")
    public List<SoeScaleItem> soeScaleNet(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeScaleReq transform = SoeScaleReq.transform(req);
        setCommonField(transform, params);

        // 如果是老的需求当月处理策略，这里返回null
        if (SoeCommonUtils.isOldDemandCurrentType(req.getStatTime())) {
            return null;
        }

        // 实际净增
        List<SoeScaleItem> ret = scaleService.getNetData(transform);

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_inventory_start", desc = "存-期初库存（好料）")
    public List<SoeInventoryStartItem> soeInventoryStart(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        SoeInventoryStartReq transform = SoeInventoryStartReq.transform(req);
        setCommonField(transform, params);

        // 这里只能查到实际库存
        List<SoeInventoryStartItem> data = inventoryStartService.getData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeInventoryStartItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_inventory_start.getName()))
                    .map(SoeInventoryStartItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                data.addAll(mock);
            }
        }

        // 如果有趋势，这里需要给每个月都填充月份数据
        if (BooleanUtils.isTrue(sqlParams.getIsTrend())) {
            // 期初库存（实际库存），每个月先给固定查询的库存
            boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
            List<String> range = isYearMonth ? sqlParams.getYearMonthRange() : sqlParams.getYearWeekRange();

            List<SoeInventoryStartItem> ret = new ArrayList<>();
            for (String yearMonthOrYearWeek : range) {
                for (SoeInventoryStartItem datum : data) {
                    SoeInventoryStartItem copy = SoeInventoryStartItem.copy(datum);
                    if (isYearMonth) {
                        copy.setYearMonth(yearMonthOrYearWeek);
                    } else {
                        copy.setYearWeek(yearMonthOrYearWeek);
                    }
                    ret.add(copy);
                }
            }
            data = ret;
        }

        // 过滤
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "soe_inventory_start_actual", desc = "存-期初库存（实际）")
    public List<SoeInventoryStartItem> soeInventoryStartActual(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        SoeInventoryStartReq transform = SoeInventoryStartReq.transform(req);
        setCommonField(transform, params);

        // 这里只能查到实际库存
        List<SoeInventoryStartItem> data = inventoryStartService.getData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeInventoryStartItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_inventory_start.getName()) && Objects.equals(item.getStatTime(), sqlParams.getStatTime()))
                    .map(SoeInventoryStartItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                data.addAll(mock);
            }
        }

        String startDate = sqlParams.getStartDate();
        String startYearMonth = startDate.substring(0, 7);

        // 设置年月为起始年月
        data.forEach(item -> item.setYearMonth(startYearMonth));

        // 过滤
        data = SoeOverviewReq.filter(filter, data);

        return data;
    }

    /**
     * 库存数据填充未来查询范围内年月的数据
     */
    private List<SoeInventoryStartItem> fillYearMonth(SoeSqlParams sqlParams, List<SoeInventoryStartItem> data) {
        // 如果有趋势，这里需要给每个月都填充月份数据
        if (BooleanUtils.isTrue(sqlParams.getIsTrend())) {
            // 期初库存（实际库存），每个月先给固定查询的库存
            boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
            List<String> range = isYearMonth ? sqlParams.getYearMonthRange() : sqlParams.getYearWeekRange();

            List<SoeInventoryStartItem> ret = new ArrayList<>();
            for (String yearMonthOrYearWeek : range) {
                for (SoeInventoryStartItem datum : data) {
                    SoeInventoryStartItem copy = SoeInventoryStartItem.copy(datum);
                    if (isYearMonth) {
                        copy.setYearMonth(yearMonthOrYearWeek);
                    } else {
                        copy.setYearWeek(yearMonthOrYearWeek);
                    }
                    ret.add(copy);
                }
            }
            return ret;
        } else {
            return data;
        }
    }


    @StoreRegister(name = "soe_inventory_end_security", desc = "存-期末库存（好料）@安全库存")
    public List<SoeInventoryEndItem> soeInventoryEndSecurity(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeInventoryEndReq transform = SoeInventoryEndReq.transform(req);
        setCommonField(transform, params);

        // 包月安全库存
        List<SoeInventoryEndItem> ret;
        // 库存口径
        InventoryStrategyEnum inventoryStrategyEnum = InventoryStrategyEnum.getByName(req.getInventoryStrategy());
        switch (inventoryStrategyEnum) {
            case Current:
                ret = inventoryEndService.getSafetyData(transform);
                break;
            case Forecast:
                ret = inventoryEndService.getForecastSafetyData(transform);
                break;
            default:
                ret = new ArrayList<>();
        }

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeInventoryEndItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_inventory_end_security.getName()))
                    .map(SoeInventoryEndItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }

        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_inventory_end_turnover", desc = "存-期末库存（好料）@周转库存")
    public List<SoeInventoryEndItem> soeInventoryEndTurnover(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeInventoryEndReq transform = SoeInventoryEndReq.transform(req);
        setCommonField(transform, params);

        // 周转库存
        List<SoeInventoryEndItem> ret;
        // 库存口径
        InventoryStrategyEnum inventoryStrategyEnum = InventoryStrategyEnum.getByName(req.getInventoryStrategy());

        switch (inventoryStrategyEnum) {
            case Current:
                ret = inventoryEndService.getTurnover(transform);
                break;
            case Forecast:
                ret = inventoryEndService.getForecastTurnover(transform);
                break;
            default:
                ret = new ArrayList<>();
        }

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeInventoryEndItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_inventory_end_turnover.getName()))
                    .map(SoeInventoryEndItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                ret.addAll(mock);
            }
        }
        // 过滤
        ret = SoeOverviewReq.filter(filter, ret);

        return ret;
    }

    @StoreRegister(name = "soe_end_to_end_scale_start", desc = "端到端利用率@售卖规模")
    public List<SoeEndToEndItem> soeEndToEndScaleStart(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        SoeEndToEndReq transform = SoeEndToEndReq.transform(req);
        setCommonField(transform, params);

        // 这里只能查到实际库存
        List<SoeEndToEndItem> data = endToEndService.getScaleData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeEndToEndItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_end_to_end_scale_start.getName()))
                    .map(SoeEndToEndItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                data.addAll(mock);
            }
        }

        // 如果有趋势，这里需要给每个月都填充月份数据
        if (BooleanUtils.isTrue(sqlParams.getIsTrend())) {
            // 期初库存（实际库存），每个月先给固定查询的库存
            boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
            List<String> range = isYearMonth ? sqlParams.getYearMonthRange() : sqlParams.getYearWeekRange();

            List<SoeEndToEndItem> ret = new ArrayList<>();
            for (String yearMonthOrYearWeek : range) {
                for (SoeEndToEndItem datum : data) {
                    SoeEndToEndItem copy = SoeEndToEndItem.copy(datum);
                    if (isYearMonth) {
                        copy.setYearMonth(yearMonthOrYearWeek);
                    } else {
                        copy.setYearWeek(yearMonthOrYearWeek);
                    }
                    ret.add(copy);
                }
            }
            data = ret;
        }

        // 过滤
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "soe_end_to_end_total_start", desc = "端到端利用率@物理总规模")
    public List<SoeEndToEndItem> soeEndToEndTotalStart(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("filter");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        SoeEndToEndReq transform = SoeEndToEndReq.transform(req);
        setCommonField(transform, params);

        // 这里只能查到实际库存
        List<SoeEndToEndItem> data = endToEndService.getTotalData(transform);

        // 获取mock数据
        List<SoeMockItem> mockItems = (List<SoeMockItem>) params.get(SoeOverviewIndexEnum.soe_mock.getName());
        if (ListUtils.isNotEmpty(mockItems)) {
            List<SoeEndToEndItem> mock = mockItems.stream()
                    .filter(item -> MockStatusEnum.isOpen(item.getStatus()) && Objects.equals(item.getFullLabel(), SoeOverviewIndexEnum.soe_end_to_end_total_start.getName()))
                    .map(SoeEndToEndItem::transformMock)
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(mock)) {
                data.addAll(mock);
            }
        }

        // 如果有趋势，这里需要给每个月都填充月份数据
        if (BooleanUtils.isTrue(sqlParams.getIsTrend())) {
            // 期初库存（实际库存），每个月先给固定查询的库存
            boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
            List<String> range = isYearMonth ? sqlParams.getYearMonthRange() : sqlParams.getYearWeekRange();

            List<SoeEndToEndItem> ret = new ArrayList<>();
            for (String yearMonthOrYearWeek : range) {
                for (SoeEndToEndItem datum : data) {
                    SoeEndToEndItem copy = SoeEndToEndItem.copy(datum);
                    if (isYearMonth) {
                        copy.setYearMonth(yearMonthOrYearWeek);
                    } else {
                        copy.setYearWeek(yearMonthOrYearWeek);
                    }
                    ret.add(copy);
                }
            }
            data = ret;
        }

        // 过滤
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "soe_end_to_end_avg_utilization_rate_end", desc = "端到端利用率@端到端利用率均值")
    public List<SoeEndToEndUtilizationRateItem> soeEndToEndAvgUtilizationRateEnd(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("soeEndToEndUtilizationRateFilter");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        SoeEndToEndUtilizationRateReq transform = SoeEndToEndUtilizationRateReq.transform(req);
        setCommonField(transform, params);

        //
        List<SoeEndToEndUtilizationRateItem> data = endToEndUtilizationRateService.getAvgData(transform);

        //构建趋势数据
        data = builderSoeEndToEndUtilizationRateItemTrend(data,sqlParams);
        // 过滤
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "soe_end_to_end_year_utilization_rate_end", desc = "端到端利用率@端到端利用率年底目标")
    public List<SoeEndToEndUtilizationRateItem> soeEndToEndYearUtilizationRateEnd(Map<String, Object> params) {
        SoeOverviewReq req = (SoeOverviewReq) params.get("req");
        SoeSqlParams sqlParams = (SoeSqlParams) params.get("sqlParams");
        List<Predicate<CommonItem>> filter = (List<Predicate<CommonItem>>) params.get("soeEndToEndUtilizationRateFilter");

        SoeEndToEndUtilizationRateReq transform = SoeEndToEndUtilizationRateReq.transform(req);
        setCommonField(transform, params);

        //
        List<SoeEndToEndUtilizationRateItem> data = endToEndUtilizationRateService.getYearEndData(transform);

        //构建趋势数据
        data = builderSoeEndToEndUtilizationRateItemTrend(data,sqlParams);

        // 过滤
        data = SoeOverviewReq.filter(filter, data);
        return data;
    }

    @StoreRegister(name = "dy_index_demo", desc = "动态指标 demo")
    public List<DyIndexItem> dyIndexDemo(Map<String, Object> params){
        return ListUtils.newList(
                new DyIndexItem("SA5","ON15","2024-12", BigDecimal.TEN),
                new DyIndexItem("SA5","ON15","2025-01", BigDecimal.TEN),
                new DyIndexItem("SA4","ON16","2025-01", BigDecimal.TEN),
                new DyIndexItem("SA4","ON17","2025-01", BigDecimal.TEN)
        );
    }

    /**
     * 设置公共字段
     */
    private void setCommonField(CommonReq req, Map<String, Object> params) {
        req.setSqlParams((SoeSqlParams) params.get("sqlParams"));
        req.setSqlBuilder((ObjectSqlBuilder) params.get("sqlBuilder"));
    }

    private List<SoeEndToEndUtilizationRateItem> builderSoeEndToEndUtilizationRateItemTrend(List<SoeEndToEndUtilizationRateItem> sourceItem, SoeSqlParams sqlParams) {
        List<SoeEndToEndUtilizationRateItem> ret = ListUtils.newArrayList();
        if (BooleanUtils.isFalse(sqlParams.getIsTrend())) {
            //如果没有趋势
            ret = sourceItem;
            return ret;
        }
        // 如果有趋势，这里需要给每个月都填充月份数据
        boolean isYearMonth = BooleanUtils.isTrue(sqlParams.getIsYearMonth());
        List<String> range = isYearMonth ? sqlParams.getYearMonthRange() : sqlParams.getYearWeekRange();

        for (String yearMonthOrYearWeek : range) {
            for (SoeEndToEndUtilizationRateItem datum : sourceItem) {
                if(!yearMonthOrYearWeek.startsWith(datum.getYear())){
                    continue;
                }
                SoeEndToEndUtilizationRateItem copy = new SoeEndToEndUtilizationRateItem();
                BeanUtils.copyProperties(datum,copy);
                if (isYearMonth) {
                    copy.setYearMonth(yearMonthOrYearWeek);
                } else {
                    copy.setYearWeek(yearMonthOrYearWeek);
                }
                ret.add(copy);
            }
        }
        return ret;
    }
}
