package cloud.demand.app.modules.soe.process.store;

import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.soe.dto.cloud_device_delivery.CloudDeviceDeliveryReq;
import cloud.demand.app.modules.soe.dto.cloud_device_delivery.DeliveryCommonReq;
import cloud.demand.app.modules.soe.dto.cloud_device_delivery.SupplyAndDemandReq;
import cloud.demand.app.modules.soe.dto.other.YearWeekReq;
import cloud.demand.app.modules.soe.enums.field.CloudDeviceDeliveryFieldEnum;
import cloud.demand.app.modules.soe.model.dict.YearMonthWeek;
import cloud.demand.app.modules.soe.model.item.CloudDeviceDeliveryItem;
import cloud.demand.app.modules.soe.process.service.CloudDeviceDemandService;
import cloud.demand.app.modules.soe.process.service.impl.CloudDeviceDemandServiceImpl;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.service.SoeDictService;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import cloud.demand.app.modules.sop_util.process.store.IDBData;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 腾讯云物理机交付仓库
 */
@Component
@StoreRegisterClient
public class CloudDeviceDeliveryStore {

    /**
     * 物理机需求采购
     */
    @Resource
    private CloudDeviceDemandService service;

    @Resource
    private SoeCleanService cleanService;

    @Resource
    private MrpV2DictService mrpV2DictService;

    @Resource
    private SoeCommonService commonService;


    @StoreRegister(name = "cloud_device_delivery_init", desc = "初始化")
    public List<IDBData> init(Map<String, Object> params) {
        DeliveryCommonReq req = (DeliveryCommonReq) params.get("req");
        boolean isDetail = BooleanUtils.isTrue(req.getIsDetail());
        if (req.isYearMonth()) {
            req.setStartYearWeek(null);
            req.setEndYearWeek(null);
        } else if (req.isYearWeek()) {
            // 是年周
            if (StringUtils.isBlank(req.getStartYearWeek()) && StringUtils.isBlank(req.getEndYearWeek())){
                String startYearMonth = req.getStartYearMonth();
                String endYearMonth = req.getEndYearMonth();
                int startYear = Integer.parseInt(startYearMonth.substring(0, 4)) - 1;
                int endYear = Integer.parseInt(endYearMonth.substring(0, 4)) + 1;
                List<YearMonthWeek> allYearMonthWeek = commonService.getAllYearMonthWeek(new YearWeekReq(startYear, endYear));
                Map<String, YearMonthWeek> yearMonthWeekMap = ListUtils.toMap(allYearMonthWeek, YearMonthWeek::getYearMonth, item -> item);
                req.setStartYearWeek(yearMonthWeekMap.get(startYearMonth).getStartYearWeek());
                req.setEndYearWeek(yearMonthWeekMap.get(endYearMonth).getEndYearWeek());
            }
            // 屏蔽年月
            req.setStartYearMonth(null);
            req.setEndYearMonth(null);
        }
        // 是否为明细
        if (isDetail) {
            req.setDims(new ArrayList<>(CloudDeviceDeliveryFieldEnum.getFieldNames()));
        }


        List<Predicate<CloudDeviceDeliveryItem>> filter = (List<Predicate<CloudDeviceDeliveryItem>>) params.get("filter");
        filter.addAll(req.buildFilter());
        return null;
    }

    @StoreRegister(name = "cloud_device_delivery", desc = "采购到货交付")
    public List<CloudDeviceDeliveryItem> delivery(Map<String, Object> params) {
        CloudDeviceDeliveryReq req = (CloudDeviceDeliveryReq) params.get("req");
        boolean isQuoteUseTime = BooleanUtils.isTrue((Boolean) params.get("is_quote_use_time")); // 是否为期望交付时间
        boolean isExcel = BooleanUtils.isTrue((Boolean) params.get("is_excel"));
        List<Predicate<CloudDeviceDeliveryItem>> filter = (List<Predicate<CloudDeviceDeliveryItem>>) params.get("filter");
        List<CloudDeviceDeliveryItem> ret = service.getDelivery(req.transform(isQuoteUseTime, isExcel));
        ret = cleanAndFilter(ret, filter);
        return ret;
    }

    @StoreRegister(name = "cloud_device_supply", desc = "供应（库存+采购到货交付）")
    public List<CloudDeviceDeliveryItem> supply(Map<String, Object> params) {
        SupplyAndDemandReq req = (SupplyAndDemandReq) params.get("req");
        boolean isQuoteUseTime = BooleanUtils.isTrue((Boolean) params.get("is_quote_use_time")); // 是否为期望交付时间
        boolean isExcel = BooleanUtils.isTrue((Boolean) params.get("is_excel"));
        List<Predicate<CloudDeviceDeliveryItem>> filter = (List<Predicate<CloudDeviceDeliveryItem>>) params.get("filter");
        List<CloudDeviceDeliveryItem> ret = service.getSupply(req.transform(isQuoteUseTime, isExcel));
        ret = cleanAndFilter(ret, filter);
        return ret;
    }

    @StoreRegister(name = "cloud_device_demand", desc = "云需求")
    public List<CloudDeviceDeliveryItem> demand(Map<String, Object> params) {
        SupplyAndDemandReq req = (SupplyAndDemandReq) params.get("req");
        boolean isExcel = BooleanUtils.isTrue((Boolean) params.get("is_excel"));
        List<Predicate<CloudDeviceDeliveryItem>> filter = (List<Predicate<CloudDeviceDeliveryItem>>) params.get("filter");
        CloudDeviceDemandServiceImpl.DemandReq storeReq = req.transform(isExcel);

        // 填充 cvm 产品类
        List<PplConfigProductEnumDO> enumDOS = mrpV2DictService.queryAllProductEnum(null, true);
        List<String> cvmProductClass = enumDOS.stream()
                .filter(item -> Objects.equals(item.getFlag(), "CVM") || Objects.equals(item.getFlag(), "PAAS"))
                .map(PplConfigProductEnumDO::getProductName).collect(Collectors.toList());
        storeReq.setProductClass(cvmProductClass);

        List<CloudDeviceDeliveryItem> ret = service.getDemand(storeReq);

        ret = cleanAndFilter(ret, filter);
        return ret;
    }

    /**
     * 清洗 + 过滤
     *
     * @param data   数据
     * @param filter 过滤器
     * @return 清洗和过滤后的数据
     */
    private List<CloudDeviceDeliveryItem> cleanAndFilter(List<CloudDeviceDeliveryItem> data, List<Predicate<CloudDeviceDeliveryItem>> filter) {
        List<CloudDeviceDeliveryItem> ret = new ArrayList<>();
        if (ListUtils.isEmpty(data)) {
            return ret;
        }
        // 清洗 + 过滤
        for (CloudDeviceDeliveryItem datum : data) {
            cleanService.cleanCloudDeviceDelivery(datum);
            boolean ok = true;
            for (Predicate<CloudDeviceDeliveryItem> predicate : filter) {
                if (!predicate.test(datum)) {
                    ok = false;
                    break;
                }
            }
            if (ok) {
                ret.add(datum);
            }
        }
        return ret;
    }
}
