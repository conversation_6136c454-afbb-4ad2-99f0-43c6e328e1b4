package cloud.demand.app.modules.soe.model.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** 预测数据 */
@Data
public class SoeForecastModel extends SoeCommonModel{

    @ExcelProperty(value = "购买开始时间",index = 11)
    private String beginBuyDate;

    @ExcelProperty(value = "购买结束时间",index = 12)
    private String endBuyDate;

    @ExcelProperty(value = "行业部门",index = 13)
    private String industryDept;

    @ExcelProperty(value = "客户简称",index = 14)
    private String customerShortName;

    /** 需求来源 */
    @ExcelProperty(value = "需求来源",index = 15)
    private String demandSource;

    /** 预约单号 */
    @ExcelProperty(value = "预约单号",index = 16)
    private String yunxiaoOrderId;

    @ExcelProperty(value = "赢率（%）",index = 17)
    private BigDecimal winRate;

    @ExcelProperty(value = "当月需求占比（%）",index = 18)
    private BigDecimal curYearMonthRate;

    /** 需求类型：新增，退回 */
    @ExcelProperty(value = "需求类型", index = 19)
    private String demandType;

}
