package cloud.demand.app.modules.soe.entitiy.distribute.ads;

import cloud.demand.app.modules.soe.entitiy.distribute.IWeekNData;
import cloud.demand.app.modules.soe.entitiy.distribute.dws.DwsSoeYunxiaoApplyItemDfDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/** soe分货明细 */
@Data
@ToString
@Table("ads_soe_distribute_item_df")
public class AdsSoeDistributeItemWithDwsDO extends AdsSoeDistributeItemDfDO {

    /** 预约单 */
    private List<DwsSoeYunxiaoApplyItemDfDO> orderList;
}
