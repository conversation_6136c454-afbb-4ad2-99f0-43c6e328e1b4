package cloud.demand.app.modules.soe.process;


import cloud.demand.app.modules.soe.dto.item.ReportIndexItem;
import cloud.demand.app.modules.soe.dto.item.ReportMapIndexItem;
import cloud.demand.app.modules.soe.dto.req.SoeOverviewReq;
import cloud.demand.app.modules.soe.enums.QueryProcessEnum;
import cloud.demand.app.modules.soe.enums.StoreNameEnum;
import cloud.demand.app.modules.soe.enums.index.*;
import cloud.demand.app.modules.soe.enums.report.CloudDeviceDeliveryProcessEnum;
import cloud.demand.app.modules.soe.enums.store.CloudDeviceDeliveryStoreEnum;
import cloud.demand.app.modules.soe.model.fields.FieldEnum;
import cloud.demand.app.modules.soe.model.sql.ObjectSqlBuilder;
import cloud.demand.app.modules.soe.model.sql.SoeSqlParams;
import cloud.demand.app.modules.soe.process.utils.SoeStepUtils;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop_util.process.QueryProcess;
import cloud.demand.app.modules.sop_util.process.QueryProcessBuilder;
import cloud.demand.app.modules.sop_util.process.StoreFactory;
import cloud.demand.app.modules.sop_util.process.model.IIndexData;
import cloud.demand.app.modules.sop_util.process.step.IQueryData;
import cloud.demand.app.modules.sop_util.process.step.QueryStep;
import cloud.demand.app.modules.sop_util.process.utils.StepUtils;
import cloud.demand.app.modules.sop_util_v2.process.utils.SopUtilV2StepUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

import static cloud.demand.app.modules.sop_util.utils.CommonUtils.toChildren;

/**
 * 查询进程构建工厂
 */
@Component
@Slf4j
public class QueryProcessFactory {

    /**
     * 进程集合
     */
    private Map<String, QueryProcess> processMap;

    /**
     * 仓库
     */
    @Resource
    private StoreFactory storeFactory;

    /**
     * 工具集合
     */
    private final SoeStepUtils utils = new SoeStepUtils();

    private final SopUtilV2StepUtils sopUtilV2StepUtils = new SopUtilV2StepUtils();

    @PostConstruct
    public void init() {
        processMap = new HashMap<>();
        register();
    }

    /**
     * 通过注册的进程名获取查询进程
     */
    public QueryProcess getProcess(String name) {
        return processMap.get(name);
    }

    /**
     * 注册查询流程
     */
    private void register() {
        // =============== 腾讯云物理机 ================
        processMap.put(CloudDeviceDeliveryProcessEnum.CLOUD_DEVICE_DELIVERY.getName(), registerCloudDeviceDelivery());
        processMap.put(CloudDeviceDeliveryProcessEnum.CLOUD_DEVICE_DELIVERY_EXCEL.getName(), registerCloudDeviceDeliveryExcel());

        processMap.put(CloudDeviceDeliveryProcessEnum.SUPPLY_AND_DEMAND.getName(), registerSupplyAndDemand());

        // =============== SOE ================

        // S&OE进销存滚动看板-概览
        processMap.put(QueryProcessEnum.SOE_OVERVIEW.getName(), registerSoe(false));

        // S&OE进销存滚动看板-明细
        processMap.put(QueryProcessEnum.SOE_DETAIL.getName(), registerSoe(true));

        // S&OE履约分析明细
        processMap.put(QueryProcessEnum.SOE_SCALE_FORECAST.getName(), registerSoeDemandScale(true));

        // S&OE履约分析明细导出
        processMap.put(QueryProcessEnum.SOE_SCALE_FORECAST_EXCEL.getName(), registerSoeDemandScaleExcel());

        // S&OE进销存滚动看板-导出
        processMap.put(QueryProcessEnum.SOE_EXCEL.getName(), registerExcel());

        // S&OE海外
        processMap.put(QueryProcessEnum.SOE_VIEW_TOTAL.getName(), registerSoeViewTotal(true));

        // 动态指标
        processMap.put(QueryProcessEnum.DY_INDEX.getName(), registerDyIndex());
    }

    private QueryProcess registerDyIndex() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        sopUtilV2StepUtils.buildStep(builder, DyIndexEnum.values());
        return builder.build();
    }

    private QueryProcess registerCloudDeviceDeliveryExcel() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        builder.addParam("filter",ArrayList::new);
        builder.addStep(StepUtils.buildInit("STEP-INIT", CloudDeviceDeliveryStoreEnum.INIT.getName()));
        QueryStep queryStep = sopUtilV2StepUtils.getSoeStepUtils().buildSimpleStep(CloudDeviceDeliveryIndexEnum.QUOTA_USE_TIME, null); // 期望交付时间
        queryStep.putStepParam("is_excel",true); // 只有导出需要声明【is_excel = true】
        builder.addStep(queryStep);
        queryStep = sopUtilV2StepUtils.getSoeStepUtils().buildSimpleStep(CloudDeviceDeliveryIndexEnum.EXPECTED_DELIVERY_TIME, null);
        queryStep.putStepParam("is_excel",true); // 只有导出需要声明【is_excel = true】
        builder.addStep(queryStep);
        sopUtilV2StepUtils.getSoeStepUtils().buildDep(builder); // 依赖补充
        return builder.build();
    }

    private QueryProcess registerCloudDeviceDelivery() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        builder.addParam("filter",ArrayList::new);
        builder.addStep(StepUtils.buildInit("STEP-INIT", CloudDeviceDeliveryStoreEnum.INIT.getName()));
        sopUtilV2StepUtils.buildStep(builder, CloudDeviceDeliveryIndexEnum.values()); // 构建查询步骤
        sopUtilV2StepUtils.getSoeStepUtils().buildDep(builder); // 依赖补充
        sopUtilV2StepUtils.getSoeStepUtils().buildSkip(builder); // 处理需要跳过的指标
        return builder.build();
    }

    private QueryProcess registerSupplyAndDemand() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        builder.addParam("filter",ArrayList::new);
        builder.addStep(StepUtils.buildInit("STEP-INIT", CloudDeviceDeliveryStoreEnum.INIT.getName()));
        sopUtilV2StepUtils.buildStep(builder, SupplyAndDemandIndexEnum.values()); // 构建查询步骤
        sopUtilV2StepUtils.getSoeStepUtils().buildDep(builder); // 依赖补充
        sopUtilV2StepUtils.getSoeStepUtils().buildSkip(builder); // 处理需要跳过的指标
        return builder.build();
    }

    private QueryProcess registerSoeViewTotal(boolean isMapIndex) {
        QueryProcessBuilder builder = initBuilder();

        // =============== 模拟数据 ================
        // step 0.1：模拟数据
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_mock, isMapIndex, true));

        // =============== 进-采购 ================
        // step 1.1：采购到货
        builder.addStep(utils.buildQueryStep(SoeViewTotalEnum.soe_purchase, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 1.2：采购（无货期）
        builder.addStep(utils.buildQueryStep(SoeViewTotalEnum.soe_no_expect, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 1.3：待执行
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_not_executed, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex,true));
        // step 1.4：退回
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_return, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex,true));
        // step 1.5：采购
        builder.addStep(utils.buildQueryStep(SoeViewTotalEnum.soe_purchase_total, ListUtils.newList(
                SoeViewTotalEnum.soe_purchase.getName(),
                SoeViewTotalEnum.soe_no_expect.getName(),
                SoeOverviewIndexEnum.soe_not_executed.getName(),
                SoeOverviewIndexEnum.soe_return.getName()
        ), utils.getAdd3AndSub(), isMapIndex,true));

        // =============== 销-预测净增 ================
        // step 3.1：新增
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_forecast_add, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex,true));
        // step 3.2：退回
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_forecast_return, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex,true));
        // step 3.3：实际净增
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_scale_net, isMapIndex,true));
        // step 3.3：销-预测净增（新增-退回-实际净增）
        builder.addStep(utils.buildQueryStep(SoeViewTotalEnum.soe_forecast_total, ListUtils.newList(
                SoeOverviewIndexEnum.soe_forecast_add.getName(),
                SoeOverviewIndexEnum.soe_forecast_return.getName(),
                SoeOverviewIndexEnum.soe_scale_net.getName()
        ), utils.getSub(), isMapIndex));
        // =============== 存-期初库存（好料） ================
        // step 4.1: 存-库存变化（采购-预测净增）
        builder.addStep(utils.buildChangeStep(SoeOverviewIndexEnum.soe_inventory_change, ListUtils.newList(
                        SoeViewTotalEnum.soe_purchase_total.getName(),
                        SoeViewTotalEnum.soe_forecast_total.getName()),
                utils.getSub(), isMapIndex));
        // step 4.2 和 4.3 都是同一份数据，但是由于4.2引用了数据源仓库，
        // 所以其他步骤在引用的时候只能拿到数据源仓库提供的数据，无法拿到计算后的数据（除了步骤自己），所以4.3单独再算了一次
        // step 4.2：存-期初库存（好料）-- 滚动计算步骤（期初m1 = 期初m0 + 采购m1 - 预测m1）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_start, isMapIndex,true));
        builder.addStep(utils.buildQueryStep(SoeViewTotalEnum.soe_inventory_start_actual, isMapIndex,false));
        // step 4.3：存-期初库存+库存变化（好料）-- 滚动计算步骤（期初m1 = 期初m0 + 采购m1 - 预测m1）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_start_for_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_inventory_start.getName(),
                        SoeOverviewIndexEnum.soe_inventory_change.getName()),
                utils.getAdd(), isMapIndex, true));

        // =============== 存-末初库存（好料） ================
        // step 5.3：存-期末库存（好料）(期初+采购-预测净增)
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeViewTotalEnum.soe_inventory_end, ListUtils.newList(
                SoeOverviewIndexEnum.soe_inventory_start_for_end.getName(),
                SoeViewTotalEnum.soe_purchase_total.getName(),
                SoeViewTotalEnum.soe_forecast_total.getName()
        ), utils.getAdd2AndSub(), isMapIndex),false,isMapIndex));
        // 构建依赖
        buildDep(builder);
        // 批量更新是否跳过步骤
        buildSkip(builder);
        return builder.build();
    }

    private QueryProcess registerSoeDemandScaleExcel() {
        QueryProcessBuilder builder = initBuilder();

        // step 2.1：实际新增
        builder.addStep(utils.buildSimpleStep(SoeScaleForecastIndexEnum.SCALE_ADD,null));
        // step 2.2：实际退回
        builder.addStep(utils.buildSimpleStep(SoeScaleForecastIndexEnum.SCALE_RETURN,null));
        // step 3.1：预测新增
        builder.addStep(utils.buildSimpleStep(SoeScaleForecastIndexEnum.FORECAST_ADD,null));
        // step 3.2：预测退回
        builder.addStep(utils.buildSimpleStep(SoeScaleForecastIndexEnum.FORECAST_RETURN,null));

        return builder.build();
    }

    private QueryProcess registerSoeDemandScale(boolean isMapIndex) {
        QueryProcessBuilder builder = initBuilder();

        // step 2.1：实际新增
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.SCALE_ADD, isMapIndex));
        // step 2.2：实际退回
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.SCALE_RETURN, isMapIndex));
        // step 2.3：实际净增(新增+退回)，退回是负数
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.SCALE_ADD_RETURN,
                ListUtils.newList(
                        SoeScaleForecastIndexEnum.SCALE_ADD.getName(),
                        SoeScaleForecastIndexEnum.SCALE_RETURN.getName()
                ), utils.getAdd(), isMapIndex));
        // step 3.1：预测新增
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.FORECAST_ADD, isMapIndex));
        // step 3.2：预测退回
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.FORECAST_RETURN, isMapIndex));
        // step 3.3：预测净增(新增+退回)，退回是负数
        builder.addStep(utils.buildQueryStep(SoeScaleForecastIndexEnum.FORECAST_ADD_RETURN,
                ListUtils.newList(
                        SoeScaleForecastIndexEnum.FORECAST_ADD.getName(),
                        SoeScaleForecastIndexEnum.FORECAST_RETURN.getName()
                ), utils.getAdd(), isMapIndex));

        // 构建依赖
        buildDep(builder);
        return builder.build();
    }

    /**
     * 导出excel
     */
    private QueryProcess registerExcel() {
        QueryProcessBuilder builder = initBuilder();
        // step 0.1：模拟数据
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_mock, null));

        // =============== 进-采购 ================
        // step 2.1：采购到货
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_purchase, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 2.2：采购无货期
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_no_expect, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 2.3：待执行
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_not_executed, SoeOverviewIndexEnum.soe_mock.getName()));
        // =============== 规模净增 ================
//        // step 2.1：外部计费
//        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_scale_external, null));
//        // step 2.2：内部服务
//        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_scale_inner, null));
        // =============== 销-预测净增 ================
        // step 3.1：新增
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_forecast_add, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 3.2：退回
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_forecast_return, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 3.3：实际净增
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_scale_net, null));
        // =============== 存-期初库存（好料） ================
        // step 4.1：存-期初库存（好料）-- 滚动计算步骤（期初m1 = 期初m0 + 采购m1 - 预测m1）
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_inventory_start, null));
        // =============== 存-末初库存（好料） ================
        // step 5.1：安全库存
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_inventory_end_security, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 5.2：周转库存
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_inventory_end_turnover, SoeOverviewIndexEnum.soe_mock.getName()));
        // =============== 端到端利用率 ================
        // step 6.1：售卖规模
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_end_to_end_scale_start, SoeOverviewIndexEnum.soe_mock.getName()));
        // step 6.2：物理总规模
        builder.addStep(utils.buildSimpleStep(SoeOverviewIndexEnum.soe_end_to_end_total_start, SoeOverviewIndexEnum.soe_mock.getName()));

        // 构建依赖
        buildDep(builder);
        buildSkip(builder);
        return builder.build();
    }

    /**
     * SOE趋势、SOE概览、SOE明细
     */
    private QueryProcess registerSoe(boolean isMapIndex) {
        QueryProcessBuilder builder = initBuilder();
        // 是否需要缓存安全库存
        builder.addParam("need_cache_safe_inv", ()-> Boolean.TRUE);

        // =============== 模拟数据 ================
        // step 0.1：模拟数据
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_mock, isMapIndex, true));

        // =============== 进-采购 ================
        // step 1.1：采购到货
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_purchase, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 1.2：采购（无货期）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_no_expect, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 2.3：待执行
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_not_executed, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 2.4：模拟数据详细 采购（退回）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_return,SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // 模拟数据详细（采购）
        builder.addStep(utils.buildMockDetail("FILL_MOCK_DETAIL_PURCHASE",SoeOverviewIndexEnum.soe_mock.getName(),ListUtils.newList(
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_purchase.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_no_expect.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_not_executed.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_return.getName())
        ),isMapIndex));

        // step 2.4：进-采购（采购到货+无货期+待执行 + 退回）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_purchase_total,
                ListUtils.newList(
                        SoeOverviewIndexEnum.soe_purchase.getName(),
                        SoeOverviewIndexEnum.soe_no_expect.getName(),
                        SoeOverviewIndexEnum.soe_not_executed.getName(),
                        SoeOverviewIndexEnum.soe_return.getName(),
                        "FILL_MOCK_DETAIL_PURCHASE"
                ), utils.getAdd3AndSub(), isMapIndex));

        // =============== 销-预测净增 ================
        // step 3.1：新增
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_forecast_add, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 3.2：退回
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_forecast_return, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex));
        // step 3.3：实际净增
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_scale_net, isMapIndex));

        // 模拟数据详细（预测净增）
        builder.addStep(utils.buildMockDetail("FILL_MOCK_DETAIL_FORECAST",SoeOverviewIndexEnum.soe_mock.getName(),ListUtils.newList(
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_forecast_add.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_forecast_return.getName())
        ),isMapIndex));

        // step 3.3：销-预测净增（新增-退回-实际净增）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_forecast_total, ListUtils.newList(
                SoeOverviewIndexEnum.soe_forecast_add.getName(),
                SoeOverviewIndexEnum.soe_forecast_return.getName(),
                SoeOverviewIndexEnum.soe_scale_net.getName(),
                "FILL_MOCK_DETAIL_FORECAST"
        ), utils.getSubWithMinZero(), isMapIndex));

        // =============== 存-期初库存（好料） ================
        // step 4.1: 存-库存变化（采购-预测净增）
        builder.addStep(utils.buildChangeStep(SoeOverviewIndexEnum.soe_inventory_change, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_purchase_total.getName(),
                        SoeOverviewIndexEnum.soe_forecast_total.getName()),
                utils.getSub(), isMapIndex));

        // step 4.2 和 4.3 都是同一份数据，但是由于4.2引用了数据源仓库，
        // 所以其他步骤在引用的时候只能拿到数据源仓库提供的数据，无法拿到计算后的数据（除了步骤自己），所以4.3单独再算了一次
        // step 4.2：存-期初库存（好料）-- 滚动计算步骤（期初m1 = 期初m0 + 采购m1 - 预测m1）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_start, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_inventory_start.getName(),
                        SoeOverviewIndexEnum.soe_inventory_change.getName()),
                utils.getAdd(), isMapIndex),true,isMapIndex));

        builder.addStep(utils.buildMockDetail("FILL_MOCK_DETAIL_START_INV_STORE",SoeOverviewIndexEnum.soe_mock.getName(),ListUtils.newList(
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_inventory_start.getName())
        ),isMapIndex,ListUtils.newList(FieldEnum.yearMonth.name()),true)); // 期初没有年月，所以这里忽略年月字段

        // step 4.3：存-期初库存+库存变化（好料）-- 滚动计算步骤（期初m1 = 期初m0 + 采购m1 - 预测m1）
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_start_for_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_inventory_start.getName(),
                        SoeOverviewIndexEnum.soe_inventory_change.getName()),
                utils.getAdd(), isMapIndex, true));

        // =============== 存-末初库存（好料） ================
        // step 5.1：安全库存
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_end_security, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex),false,isMapIndex));
        // step 5.2：周转库存
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_end_turnover, SoeOverviewIndexEnum.soe_mock.getName(), isMapIndex),false,isMapIndex));

        // 模拟数据详细（安全 + 周转）
        builder.addStep(utils.buildMockDetail("FILL_MOCK_DETAIL_END_INV",SoeOverviewIndexEnum.soe_mock.getName(),ListUtils.newList(
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_inventory_end_security.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_inventory_end_turnover.getName())
        ),isMapIndex));

        // step 5.3：存-末初库存（好料）(期初+采购-预测净增)
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_end_total, ListUtils.newList(
                SoeOverviewIndexEnum.soe_inventory_start_for_end.getName(),
                SoeOverviewIndexEnum.soe_purchase_total.getName(),
                SoeOverviewIndexEnum.soe_forecast_total.getName(),
                "FILL_MOCK_DETAIL_END_INV"
        ), utils.getAdd2AndSub(), isMapIndex),false,isMapIndex));

        // step 5.4：冗余库存（期末库存-安全库存-周转库存）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_inventory_end_redundancy, ListUtils.newList(
                SoeOverviewIndexEnum.soe_inventory_end_total.getName(),
                SoeOverviewIndexEnum.soe_inventory_end_security.getName(),
                SoeOverviewIndexEnum.soe_inventory_end_turnover.getName(),
                "FILL_MOCK_DETAIL_END_INV"
        ), utils.getSub(), isMapIndex),false,isMapIndex));

        // =============== 冗余系数 ================
        // step 6：冗余系数（期末库存/安全库存）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_redundancy_coefficient, ListUtils.newList(
                SoeOverviewIndexEnum.soe_inventory_end_total.getName(),
                SoeOverviewIndexEnum.soe_inventory_end_security.getName()
        ), utils.getAddAndDivide(), isMapIndex),false,isMapIndex));

        // =============== 端到端 ================

        // step 7.0 采购变化量，需求变化量
        builder.addStep(utils.buildChangeStep(SoeOverviewIndexEnum.soe_purchase_change, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_purchase_total.getName()),
                utils.getAdd(), isMapIndex));

        builder.addStep(utils.buildChangeStep(SoeOverviewIndexEnum.soe_forecast_change, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_forecast_total.getName()),
                utils.getAdd(), isMapIndex));


        // step 7.1：端到端利用率-期初@售卖规模（这里加上需求预测是应为下个月的期初 = 当月期末）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_scale_start, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_scale_start.getName(),
                        SoeOverviewIndexEnum.soe_forecast_change.getName()),
                utils.getAdd(), isMapIndex),true,isMapIndex));

        // step 7.2：端到端利用率-期初@物理总规模（同上，但是加的是采购）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_total_start, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_total_start.getName(),
                        SoeOverviewIndexEnum.soe_purchase_change.getName()),
                utils.getAdd(), isMapIndex),true,isMapIndex));

        builder.addStep(utils.buildMockDetail("FILL_MOCK_DETAIL_END_TO_END_SCALE_STORE",SoeOverviewIndexEnum.soe_mock.getName(),ListUtils.newList(
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_end_to_end_scale_start.getName()),
                new SoeStepUtils.MockDepIndex(SoeOverviewIndexEnum.soe_end_to_end_total_start.getName())
        ),isMapIndex,ListUtils.newList(FieldEnum.yearMonth.name()),true)); // 期初没有年月，所以这里忽略年月字段

        //（再算一般期初）
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_scale_start_for_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_scale_start.getName(),
                        SoeOverviewIndexEnum.soe_forecast_change.getName()),
                utils.getAdd(), isMapIndex,true),true,isMapIndex));

        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_total_start_for_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_total_start.getName(),
                        SoeOverviewIndexEnum.soe_purchase_change.getName()),
                utils.getAdd(), isMapIndex,true),true,isMapIndex));

        // step 7.3：端到端利用率
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_start, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_scale_start_for_end.getName(),
                        SoeOverviewIndexEnum.soe_end_to_end_total_start_for_end.getName()),
                utils.getAddAndDivide(), isMapIndex),true,isMapIndex));

        // 期末
        // step 8.1：期末端到端-售卖规模
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_scale_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_scale_start_for_end.getName(),
                        SoeOverviewIndexEnum.soe_forecast_total.getName()),
                utils.getAdd(), isMapIndex),false,isMapIndex));

        // step 8.2：期末端到端-物理机总数
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_total_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_total_start_for_end.getName(),
                        SoeOverviewIndexEnum.soe_purchase_total.getName()),
                utils.getAdd(), isMapIndex),false,isMapIndex));
        // step 8.3：期末端到端-利用率均值目标
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_avg_utilization_rate_end, isMapIndex));

        // step 8.4：期末端到端-利用率年底目标
        builder.addStep(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_year_utilization_rate_end, isMapIndex));

        // step 8.5：期末端到端-利用率
        builder.addStep(dealInvTotal(utils.buildQueryStep(SoeOverviewIndexEnum.soe_end_to_end_end, ListUtils.newList(
                        SoeOverviewIndexEnum.soe_end_to_end_scale_end.getName(),
                        SoeOverviewIndexEnum.soe_end_to_end_total_end.getName()),
                utils.getAddAndDivide(), isMapIndex),false,isMapIndex));

        // 构建依赖
        buildDep(builder);

        // 批量更新是否跳过步骤
        if (isMapIndex) {
            buildSkip(builder);
        }
        return builder.build();
    }


    /**
     * 处理期初和期末库存
     * @param startOrEnd 期初或期末
     * @param step 步骤
     * @return
     */
    private QueryStep dealInvTotal(QueryStep step,boolean startOrEnd, boolean isMapIndex){
        BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform = step.getTransform();
        step.setTransform((map, listMap) -> {
            SoeOverviewReq req = (SoeOverviewReq)map.get("req");

            List<IQueryData> items = transform.apply(map, listMap);

            // 不是趋势（字段有年月）直接返回
            String key = FieldEnum.yearMonth.name();
            boolean hasYearMonthFields = SoeCommonUtils.contains(key,req.getFields(),null);
            if (!hasYearMonthFields){
                return items;
            }

            if (isMapIndex){
                computeTotalMapIndex(toChildren(items),key,startOrEnd);
            }else {
                computeTotalIndex(toChildren(items),key,startOrEnd);
            }

            return items;

        });
        return step;
    }

    /** 处理总计 */
    private void computeTotalIndex(List<ReportIndexItem> items,String key, boolean startOrEnd) {
        for (ReportIndexItem item : items) {
            item.flushTotal(startOrEnd,key);
        }
    }

    /** 处理总计 */
    private void computeTotalMapIndex(List<ReportMapIndexItem> items, String key, boolean startOrEnd) {
        items.forEach(item-> item.flushTotal(startOrEnd,key));
    }

    /**
     * 所以步骤都需要等初始化完成才执行（异步太多了，这里统一做限制，如果是同步操作这里无需加限制）
     */
    private void buildDep(QueryProcessBuilder builder) {
        utils.buildDep(builder);
    }

    /**
     * 构建是否跳过该步骤
     */
    private void buildSkip(QueryProcessBuilder builder) {
        builder.batchUpdateStep(queryStep -> {
            if (queryStep.getStepName().equals("STEP-INIT")) {
                return;
            }
            Function<Map<String, Object>, Boolean> oriSkipFunc = queryStep.getSkipFunc();
            queryStep.setSkipFunc(map -> {
                // 该指标是否在查询范围内
                SoeOverviewReq req = (SoeOverviewReq) map.get("req");
                List<String> index = req.getIndex();
                // 原来的跳过方法（两者一个跳过则都跳过）
                boolean oriSkip = false;
                if (oriSkipFunc != null) {
                    oriSkip = oriSkipFunc.apply(map);
                }
                // 如果index为空，默认查所有（即不跳过）
                if (ListUtils.isEmpty(index)) {
                    return oriSkip;
                }
                return oriSkip || index.contains(queryStep.getIndexModel().getIndexName());
            });
        });
    }

    /**
     * 初始化
     */
    private QueryProcessBuilder initBuilder() {
        QueryProcessBuilder builder = new QueryProcessBuilder(storeFactory);
        // 需要展示的机型集合（不在其中的用其他表示）
        builder.addParam("show_strategy", ArrayList::new);
        // sql参数
        builder.addParam("sqlParams", SoeSqlParams::new);
        // sql构建器
        builder.addParam("sqlBuilder", ObjectSqlBuilder::new);
        // 过滤器
        builder.addParam("filter", ArrayList::new);

        // Soe 端对端目标利用率使用的过滤器
        builder.addParam("soeEndToEndUtilizationRateFilter", ArrayList::new);

        // 填充初始化参数
        builder.addStep(StepUtils.buildInit("STEP-INIT", StoreNameEnum.soe_init.getName()));
        return builder;
    }
}
