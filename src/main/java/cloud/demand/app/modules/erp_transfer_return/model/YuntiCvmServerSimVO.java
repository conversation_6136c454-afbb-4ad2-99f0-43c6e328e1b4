package cloud.demand.app.modules.erp_transfer_return.model;

import com.pugwoo.dbhelper.annotation.Column;
import java.util.Date;
import lombok.Data;

@Data
public class YuntiCvmServerSimVO {

    /** 运营产品ID<br/>Column: [product_id] */
    @Column(value = "product_id")
    private Integer productId;

    /** 实例机型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例所属项目名称<br/>Column: [project_name] */
    @Column(value = "project_name")
    private String projectName;

    /** 需求可用区<br/>Column: [cloud_campus] */
    @Column(value = "cloud_campus")
    private String cloudCampus;

    /** 实例固资号<br/>Column: [instance_asset_id] */
    @Column(value = "instance_asset_id")
    private String instanceAssetId;

    /** 实例CPU核数<br/>Column: [cpu] */
    @Column(value = "cpu")
    private Integer cpu;

    /** 实际生产落入的资源池：0代表自研云;1代表公有云<br/>Column: [pool] */
    @Column(value = "pool")
    private Integer pool;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 单号<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    public String getApplyOrderNo() {
        return orderId == null ? "" : orderId;
    }

}
