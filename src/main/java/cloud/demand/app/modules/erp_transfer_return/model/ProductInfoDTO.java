package cloud.demand.app.modules.erp_transfer_return.model;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/**
 * 运营产品信息
 */
@Data
public class ProductInfoDTO {

    @Column("bgId")
    private Integer bgId;
    @Column("bgName")
    private String bgName;
    @Column("bgShortName")
    private String bgShortName;
    @Column("deptId")
    private Integer deptId;
    @Column("deptName")
    private String deptName;
    @Column("planProductId")
    private Integer planProductId;
    @Column("planProductName")
    private String planProductName;
    @Column("productId")
    private Integer productId;
    @Column("productName")
    private String productName;

}
