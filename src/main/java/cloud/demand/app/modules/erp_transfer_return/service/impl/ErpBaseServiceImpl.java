package cloud.demand.app.modules.erp_transfer_return.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.resource.BasStrategeDeviceTypeDO;
import cloud.demand.app.entity.rrp.ReportConfigTransferDO;
import cloud.demand.app.entity.yunti.BasObsCloudCvmTypeDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.erp_transfer_return.model.PlanProductDeptInfoDTO;
import cloud.demand.app.modules.erp_transfer_return.model.ProductInfoDTO;
import cloud.demand.app.modules.erp_transfer_return.service.ErpBaseService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class ErpBaseServiceImpl implements ErpBaseService {

    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DBHelper resourcedbDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private DBHelper obsDBHelper;

    /**
     * @param bizGroup 为空表示查全部
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, keyScript = "args[0]", cloneReturn = false)
    public List<ReportConfigTransferDO> getBizGroupConfig(String bizGroup) {
        if (StringTools.isBlank(bizGroup)) {
            return rrpDBHelper.getAll(ReportConfigTransferDO.class);
        } else {
            return rrpDBHelper.getAll(ReportConfigTransferDO.class,
                    "where biz_group=?", bizGroup);
        }
    }

    /**
     * 获得按规划产品,业务模块按层次对应的key所对应的，
     * 返回结果可能因为computeType有值而有多条
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, cloneReturn = false)
    public Map<String, List<ReportConfigTransferDO>> getBizGroupMap() {
        List<ReportConfigTransferDO> all = rrpDBHelper.getAll(ReportConfigTransferDO.class);
        return ListUtils.toMapList(all, o ->
                join(o.getPlanProduct(), o.getBusiness1(), o.getBusiness2(), o.getBusiness3()), o -> o);
    }

    /**
     * 获得资源视图的配置
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, cloneReturn = false)
    public Map<String, List<CloudDemandCsigResourceViewCategoryDO>> getResourceViewCategoryMap() {
        List<CloudDemandCsigResourceViewCategoryDO> all =
                yuntiDBHelper.getAll(CloudDemandCsigResourceViewCategoryDO.class);
        // 处理一下名称中的trim，如果有的话
        ListUtils.forEach(all, o -> {
            if (o.getCategory1() != null) {
                o.setCategory1(o.getCategory1().trim());
            }
            if (o.getCategory2() != null) {
                o.setCategory2(o.getCategory2().trim());
            }
            if (o.getCategory3() != null) {
                o.setCategory3(o.getCategory3().trim());
            }
            if (o.getCategory4() != null) {
                o.setCategory4(o.getCategory4().trim());
            }
            if (o.getCategory5() != null) {
                o.setCategory5(o.getCategory5().trim());
            }
        });
        return ListUtils.toMapList(all, o -> join(o.getPlanProductName()), o -> o);
    }

    /**
     * 获得机型信息（主要是机型代次）的新
     */
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, cloneReturn = false)
    @Override
    public Map<String, BasStrategeDeviceTypeDO> getBasStrategeDeviceTypeMap() {
        List<BasStrategeDeviceTypeDO> all =
                resourcedbDBHelper.getAll(BasStrategeDeviceTypeDO.class, "where EnableFlag=1");
        return ListUtils.toMap(all, o -> join(o.getNAME()), o -> o);
    }

    @Override
    public List<String> getAllBizGroup() {
        List<ReportConfigTransferDO> config = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getBizGroupConfig("");
        return new ArrayList<>(ListUtils.toSet(config, ReportConfigTransferDO::getBizGroup));
    }

    @Override
    public List<String> getTransferPlanProductNames(String bizGroup) {
        List<ReportConfigTransferDO> config = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getBizGroupConfig(bizGroup);
        return new ArrayList<>(ListUtils.toSet(config, ReportConfigTransferDO::getPlanProduct));
    }

    @Override
    public List<String> getTransferBusiness1(String bizGroup) {
        List<ReportConfigTransferDO> config = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getBizGroupConfig(bizGroup);
        return new ArrayList<>(ListUtils.toSet(config, ReportConfigTransferDO::getBusiness1));
    }

    @Override
    public String getBizGroup(String productName, String business1,
                              String business2, String business3, String deviceType) {

        if (Objects.equals(productName, ProductTypeEnum.CRS.getCode()) && Objects.equals(business1, "[N][腾讯云CRS]")
                && Objects.equals(business2, "[CRS][现网运营_CRS]") && !business3.contains("CH")){
            return "";
        }


        Map<String, List<ReportConfigTransferDO>> bizGroupMap =
                SpringUtil.getBean(ErpBaseServiceImpl.class).getBizGroupMap();
        List<ReportConfigTransferDO> configs = bizGroupMap.get(join(productName, business1, business2, business3));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetBizGroup(configs, deviceType);
        }

        configs = bizGroupMap.get(join(productName, business1, business2));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetBizGroup(configs, deviceType);
        }

        configs = bizGroupMap.get(join(productName, business1));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetBizGroup(configs, deviceType);
        }

        configs = bizGroupMap.get(join(productName));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetBizGroup(configs, deviceType);
        }
        return "";
    }

    @Override
    public CloudDemandCsigResourceViewCategoryDO getResourceViewCategory(String productName, String deviceType) {
        Map<String, List<CloudDemandCsigResourceViewCategoryDO>> resourceViewCategoryMap =
                SpringUtil.getBean(ErpBaseServiceImpl.class).getResourceViewCategoryMap();

        List<CloudDemandCsigResourceViewCategoryDO> configs = resourceViewCategoryMap.get(join(productName));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetResourceViewConfig(configs, deviceType);
        }

        return null;
    }

    @Override
    public CloudDemandCsigResourceViewCategoryDO getResourceViewCategoryByCvmType(String productName, String instanceType) {
        Map<String, List<CloudDemandCsigResourceViewCategoryDO>> resourceViewCategoryMap =
                SpringUtil.getBean(ErpBaseServiceImpl.class).getResourceViewCategoryMap();

        List<CloudDemandCsigResourceViewCategoryDO> configs = resourceViewCategoryMap.get(join(productName));
        if (ListUtils.isNotEmpty(configs)) {
            return doGetResourceViewConfigByCvmType(configs, instanceType);
        }

        return null;
    }

    private CloudDemandCsigResourceViewCategoryDO doGetResourceViewConfigByCvmType(
            List<CloudDemandCsigResourceViewCategoryDO> configs, String instanceType) {
        // 先匹配有computeType的
        for (CloudDemandCsigResourceViewCategoryDO config : configs) {
            if (StringTools.isNotBlank(config.getComputeType())) {
                String computeType = dictService.getCvmComputeType(instanceType);
                if (Objects.equals(computeType, config.getComputeType())) {
                    return config;
                }
            }
        }

        // 再匹配没有computeType的
        for (CloudDemandCsigResourceViewCategoryDO config : configs) {
            if (StringTools.isBlank(config.getComputeType())) {
                return config;
            }
        }

        return null;
    }

    private CloudDemandCsigResourceViewCategoryDO doGetResourceViewConfig(
            List<CloudDemandCsigResourceViewCategoryDO> configs, String deviceType) {
        // 先匹配有computeType的
        for (CloudDemandCsigResourceViewCategoryDO config : configs) {
            if (StringTools.isNotBlank(config.getComputeType())) {
                String computeType = dictService.getComputeType(deviceType);
                if (Objects.equals(computeType, config.getComputeType())) {
                    return config;
                }
            }
        }

        // 再匹配没有computeType的
        for (CloudDemandCsigResourceViewCategoryDO config : configs) {
            if (StringTools.isBlank(config.getComputeType())) {
                return config;
            }
        }

        return null;
    }

    private String doGetBizGroup(List<ReportConfigTransferDO> configs, String deviceType) {
        // 先匹配有computeType的
        for (ReportConfigTransferDO config : configs) {
            if (StringTools.isNotBlank(config.getComputeType())) {
                String computeType = dictService.getComputeType(deviceType);
                if (Objects.equals(computeType, config.getComputeType())) {
                    return config.getBizGroup();
                }
            }
        }

        // 再匹配没有computeType的
        for (ReportConfigTransferDO config : configs) {
            if (StringTools.isBlank(config.getComputeType())) {
                return config.getBizGroup();
            }
        }

        return "";
    }

    @Override
    public boolean isInTransferBizType(String bizGroup, String productName,
                                       String business1, String business2, String business3,
                                       String deviceType) {
        if (StringTools.isBlank(productName) || StringTools.isBlank(business1)) {
            return false;
        }

        List<ReportConfigTransferDO> config = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getBizGroupConfig(bizGroup);
        Map<String, List<ReportConfigTransferDO>> map = ListUtils.groupBy(config,
                ReportConfigTransferDO::getPlanProduct);

        List<ReportConfigTransferDO> biz = map.get(productName);
        if (biz == null) {
            return false;
        }
        boolean isMatched = false;
        for (ReportConfigTransferDO b : biz) {
            if (StringTools.isNotBlank(business2) && business2.trim().startsWith("[自研上云]")) {
                continue; // 二级模块是[自研上云]的都不要
            }

            // 如果规则business1有值，则要求相等
            if (StringTools.isNotBlank(b.getBusiness1()) && !Objects.equals(business1, b.getBusiness1())) {
                continue;
            }
            // 如果规则business2有值，则要求相等
            if (StringTools.isNotBlank(b.getBusiness2()) && !Objects.equals(business2, b.getBusiness2())) {
                continue;
            }

            // 20220927：腾讯云CRS - [N][腾讯云CRS] - [CRS][现网运营_CRS]下所有CH的业务模块要统计进来
            if (Objects.equals(bizGroup, "CRS") && Objects.equals(business2, "[CRS][现网运营_CRS]") && !business3.contains("CH")){
                continue;
            }

            // 如果规则business3有值，则要求相等
            if (StringTools.isNotBlank(b.getBusiness3()) && !Objects.equals(business3, b.getBusiness3())) {
                continue;
            }
            // 如果规则computeType有值，则匹配得上
            if (StringTools.isNotBlank(b.getComputeType())) {
                String computeType = dictService.getComputeType(deviceType);
                if (!Objects.equals(computeType, b.getComputeType())) {
                    continue;
                }
            }

            isMatched = true;
        }

        return isMatched;
    }


    @Override
    public String getGenerationType(String deviceType) {
        if (StringTools.isBlank(deviceType)) {
            return "";
        }
        Map<String, BasStrategeDeviceTypeDO> map = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getBasStrategeDeviceTypeMap();
        BasStrategeDeviceTypeDO basStrategeDeviceTypeDO = map.get(deviceType);
        if (basStrategeDeviceTypeDO == null || basStrategeDeviceTypeDO.getGenerationType() == null) {
            return "专用";
        }

        Integer generationType = basStrategeDeviceTypeDO.getGenerationType();
        if (generationType == 1) {
            return "采购";
        } else if (generationType == 2) {
            return "存量";
        } else {
            return "专用";
        }
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    public Map<Integer, PlanProductDeptInfoDTO> getProductInfoMap() {
        String sql = ORMUtils.getSql("/sql/obs/get_all_plan_product_info.sql");
        List<PlanProductDeptInfoDTO> all = obsDBHelper.getRaw(PlanProductDeptInfoDTO.class, sql);
        return ListUtils.toMap(all, PlanProductDeptInfoDTO::getPlanProductId, o -> o);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120)
    public Map<String, PlanProductDeptInfoDTO> getProductInfoMapByName() {
        String sql = ORMUtils.getSql("/sql/obs/get_all_plan_product_info.sql");
        List<PlanProductDeptInfoDTO> all = obsDBHelper.getRaw(PlanProductDeptInfoDTO.class, sql);
        return ListUtils.toMap(all, PlanProductDeptInfoDTO::getPlanProductName, o -> o);
    }

    @Override
    public PlanProductDeptInfoDTO getProductInfoByProductId(Integer planProductId) {
        if (planProductId == null) {
            return null;
        }
        Map<Integer, PlanProductDeptInfoDTO> map = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getProductInfoMap();
        return map.get(planProductId);
    }

    @Override
    public PlanProductDeptInfoDTO getProductInfoByProductName(String planProductName) {
        if (StringTools.isBlank(planProductName)) {
            return null;
        }
        Map<String, PlanProductDeptInfoDTO> map = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getProductInfoMapByName();
        return map.get(planProductName);
    }

    @Override
    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, cloneReturn = false)
    public Map<Integer, ProductInfoDTO> getProductInfoByProductId() {
        String sql = ORMUtils.getSql("/sql/obs/get_all_product_info.sql");
        List<ProductInfoDTO> all = obsDBHelper.getRaw(ProductInfoDTO.class, sql);
        return ListUtils.toMap(all, ProductInfoDTO::getProductId, o -> o);
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 120, cloneReturn = false)
    public Map<String, BasObsCloudCvmTypeDO> getInstanceTypeNameMap() {
        List<BasObsCloudCvmTypeDO> list = obsDBHelper.getAll(BasObsCloudCvmTypeDO.class,
                "GROUP BY CvmInstanceType");
        return ListUtils.toMap(list, o -> o.getCvmInstanceType(), o -> o);
    }

    @Override
    public String getInstanceTypeCodeByName(String instanceTypeName) {
        Map<String, BasObsCloudCvmTypeDO> map = SpringUtil.getBean(ErpBaseServiceImpl.class)
                .getInstanceTypeNameMap();
        BasObsCloudCvmTypeDO type = map.get(instanceTypeName);
        return type == null ? "" : type.getCvmInstanceTypeCode();
    }

    private String join(String productName, String business1, String business2, String business3) {
        return join(productName, business1, business2) + (StringTools.isNotBlank(business3) ? business3 : "");
    }

    private String join(String productName, String business1, String business2) {
        return join(productName, business1) + (StringTools.isNotBlank(business2) ? business2 : "");
    }

    private String join(String productName, String business1) {
        return join(productName) + (StringTools.isNotBlank(business1) ? business1 : "");
    }

    private String join(String productName) {
        return (StringTools.isNotBlank(productName) ? productName : "");
    }

}
