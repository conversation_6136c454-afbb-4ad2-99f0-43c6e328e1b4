package cloud.demand.app.modules.sop_util.process.step;

import cloud.demand.app.modules.sop_util.process.model.IIndexData;
import cloud.demand.app.modules.sop_util.process.model.IndexModel;
import cloud.demand.app.modules.sop_util.process.store.IDBData;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryStep {

    /**
     * 同步异步，true：异步，false：同步
     */
    private boolean async;

    /** 步骤参数 */
    private Map<String,Object> stepParams;

    /**
     * 步骤模型
     */
    private String stepName;

    /**
     * 指标模型
     */
    private IndexModel indexModel;

    /**
     * 依赖
     */
    private List<String> dependence;

    /**
     * index数据转换为queryData
     */
    private BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform;

    /** 需要对数据做最后的处理（这里依赖的是最终的成品，页面展示的） */
    private BiFunction<Map<String, Object>, Map<String, List<IQueryData>>, List<IQueryData>> endTransform;

    /**
     * 跳过该步骤的方法
     */
    private Function<Map<String, Object>, Boolean> skipFunc;

    /**
     * 合并的方法 （total,merge）
     */
    private BiConsumer<List<IQueryData>, List<IQueryData>> merge;

    public QueryStep(String stepName,
                     String storeName,
                     BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform,
                     Function<List<IDBData>, List<IIndexData>> indexTransform,
                     Map<String,Object> stepParams
                     ) {
        this(stepName,stepName,storeName,transform,indexTransform,stepParams);
    }

    public QueryStep(String stepName,
                     String indexName,
                     String storeName,
                     BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform,
                     Function<List<IDBData>, List<IIndexData>> indexTransform,
                     Map<String,Object> stepParams
    ){
        this(stepName,indexName,storeName,transform,(map,idbData)-> indexTransform.apply(idbData),stepParams);
    }

    public QueryStep(String stepName,
                     String indexName,
                     String storeName,
                     BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform,
                     BiFunction<Map<String,Object>,List<IDBData>, List<IIndexData>> indexTransform,
                     Map<String,Object> stepParams
    ) {
        this(stepName,indexName,storeName,null,transform,indexTransform,stepParams);
    }

    public QueryStep(String stepName,
                     String indexName,
                     String storeName,
                     BiFunction<Map<String, Object>, Map<String, List<IQueryData>>, List<IQueryData>> endTransform,
                     BiFunction<Map<String, Object>, Map<String, List<IIndexData>>, List<IQueryData>> transform,
                     BiFunction<Map<String,Object>,List<IDBData>, List<IIndexData>> indexTransform,
                     Map<String,Object> stepParams
    ) {
        this.async = false;
        this.stepName = stepName;
        this.indexModel = new IndexModel();
        this.merge = (iQueryData, iQueryData2) -> {
            if (ListUtils.isNotEmpty(iQueryData2)){
                iQueryData.addAll(iQueryData2);
            }
        };
        this.endTransform = endTransform;
        this.transform = transform;
        this.indexModel.setIndexName(indexName);
        this.indexModel.setUseStoreCache(false);
        this.indexModel.setTransform(indexTransform);
        this.indexModel.setStoreName(storeName);
        this.stepParams = stepParams;
    }

    /**
     * 增加步骤参数，当需要补充参数时，续避免直接用 set，使用该方法以防止覆盖
     * @param key key
     * @param value value
     */
    public void putStepParam(String key, Object value){
        if (stepParams == null){
            stepParams = new HashMap<>();
        }
        stepParams.put(key,value);
    }
}
