package cloud.demand.app.modules.sop_util.domain.baffle;

import lombok.Data;

import java.math.BigDecimal;
import java.time.YearMonth;

/** 挡板-包括年月，设备类型，台数，核数信息 */
@Data
public class BaseRespItem {
    /** 年月 */
    private YearMonth yearMonth;
    /** 物理机机型族 */
    private String deviceFamily;
    /** 设备类型 */
    private String deviceType;
    /** 台数 */
    private BigDecimal num;
    /** 核数 */
    private BigDecimal coreNum;
}
