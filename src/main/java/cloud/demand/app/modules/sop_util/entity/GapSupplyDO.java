package cloud.demand.app.modules.sop_util.entity;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class GapSupplyDO {
    /** sop工具业务类型 */
    @Column("any_util_business_type")
    private String utilBusinessType;
    /** 大类 */
    @Column("any_sop_big_family")
    private String sopBigFamily;

    @Column("index_year_month")
    private String yearMonth;

    @Column("sum_core_num")
    private BigDecimal coreNum;
}
