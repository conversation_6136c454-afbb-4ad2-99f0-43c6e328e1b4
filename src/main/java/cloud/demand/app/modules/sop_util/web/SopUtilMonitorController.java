package cloud.demand.app.modules.sop_util.web;

import cloud.demand.app.modules.sop_util.domain.supply.ReportLoseDataResp;
import cloud.demand.app.modules.sop_util.domain.supply.ReportSupplyReq;
import cloud.demand.app.modules.sop_util.service.SopUtilMonitorService;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;

@JsonrpcController("/sop/util/monitor")
public class SopUtilMonitorController {

    /** 监控 */
    @Resource
    private SopUtilMonitorService monitorService;


    /** 监控-分摊，cvm折算丢失的数据 */
    @RequestMapping
    public ReportLoseDataResp queryLoseData(@JsonrpcParam @Valid ReportSupplyReq req){
        ReportSupplyReq.init(req);
        return new ReportLoseDataResp(monitorService.loseData(req));
    }
}
