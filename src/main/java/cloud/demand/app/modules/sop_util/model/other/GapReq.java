package cloud.demand.app.modules.sop_util.model.other;

import cloud.demand.app.modules.sop_util.model.CommonReq;
import lombok.Data;

/** 缺口请求 */
@Data
public class GapReq extends CommonReq {

    /** 采购对冲版本，实际传的应该是物理机的，因为查的是物理机的数据 */
    private String version;

    /** 是否只看CVM产品数据(默认false) */
    private Boolean onlyCvmProduct = true;

    /** 是否只看有映射的物理机机型【映射策略表：sop_util_device_mapping】 */
    private Boolean onlyMappingDeviceType = false;

    private Boolean onlyCloud = true;

    /** 是否排除GPU机型 */
    private Boolean excludeGpu = true;

}
