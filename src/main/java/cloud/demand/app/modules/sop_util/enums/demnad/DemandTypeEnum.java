package cloud.demand.app.modules.sop_util.enums.demnad;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Objects;


/** 需求类型 */
@AllArgsConstructor
@Getter
public enum DemandTypeEnum {

    SCALE("总规模"),
    ADD("新增"),
    RET("退回"),
    ADD_RET("净增"),

    ;
    private final String name;

    public static boolean isScale(String name){
        return Objects.equals(name,SCALE.getName());
    }
}
