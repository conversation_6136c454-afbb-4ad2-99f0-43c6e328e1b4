package cloud.demand.app.modules.sop_util.domain.baffle.req;

import cloud.demand.app.modules.sop_util.domain.baffle.BaseReq;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/** 供应请求 */
@Data
public class SupplyBaffleReq extends BaseReq{
    /** 版本日期 */
    @NotNull(message = "传入的版本不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING,pattern = "yyyyMMdd")
    @JsonProperty("version")
    private LocalDate versionDate;
}
