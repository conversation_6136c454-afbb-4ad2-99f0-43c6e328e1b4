package cloud.demand.app.modules.sop_util.enums.demnad;

import cloud.demand.app.modules.sop_util.enums.IndexStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/** 客户需求计划复盘总览-指标/步骤 */
@AllArgsConstructor
@Getter
public enum DemandOverviewIndexEnum {
    INDEX_01("Y24 总规模",IndexStatusEnum.INIT),
    INDEX_02("Y24 预测净增"),
    INDEX_03("Y24 总规模（上一轮）",IndexStatusEnum.INIT),
    INDEX_04("Y24 预测净增（上一轮）"),
    INDEX_05("Y24 预算",IndexStatusEnum.INIT),
    INDEX_06("Y24 预算净增",IndexStatusEnum.INIT),
    INDEX_07("Y23 实际大盘规模",IndexStatusEnum.INIT),

    INDEX_08("Y23 实际净增"),
    INDEX_09("Y24 预测净增Δ VS Y24 预测净增（上一轮）",DemandLabelTypeEnum.PROCESS_DATA),
    INDEX_10("Y24 总规模Δ VS Y24 预算",DemandLabelTypeEnum.PROCESS_DATA,IndexStatusEnum.INIT),
    INDEX_11("Y24 总规模Δ VS Y24 总规模（上一轮）",DemandLabelTypeEnum.PROCESS_DATA,IndexStatusEnum.INIT),
    INDEX_12("总规模增长%（Y24/Y23）",DemandLabelTypeEnum.PROCESS_DATA),
    INDEX_13("Y24 预测净增Δ VS Y23 实际净增",DemandLabelTypeEnum.PROCESS_DATA,IndexStatusEnum.INIT),


    ;

    DemandOverviewIndexEnum(String name){
        this(name,DemandLabelTypeEnum.def());
    }

    DemandOverviewIndexEnum(String name,IndexStatusEnum statusEnum){
        this(name,DemandLabelTypeEnum.def(),statusEnum);
    }

    DemandOverviewIndexEnum(String name,DemandLabelTypeEnum typeEnum){
        this.name = name;
        this.typeEnum = typeEnum;
        this.statusEnum = IndexStatusEnum.SUCCESS;
    }

    /** 指标名称 */
    private final String name;

    /** 指标类型 */
    private final DemandLabelTypeEnum typeEnum;

    /** 指标状态 */
    private final IndexStatusEnum statusEnum;
}
