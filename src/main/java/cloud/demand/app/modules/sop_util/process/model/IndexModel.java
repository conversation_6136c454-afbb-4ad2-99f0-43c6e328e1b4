package cloud.demand.app.modules.sop_util.process.model;

import cloud.demand.app.modules.sop_util.process.store.IDBData;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

@Data
public class IndexModel {

    /** 仓库名称 */
    private String storeName;

    /** 是否使用仓库缓存 */
    private boolean useStoreCache;

    private String indexName;


    private BiFunction<Map<String,Object>,List<IDBData>,List<IIndexData>> transform;
}
