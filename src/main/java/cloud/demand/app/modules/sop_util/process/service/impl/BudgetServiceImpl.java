package cloud.demand.app.modules.sop_util.process.service.impl;

import cloud.demand.app.modules.sop_util.domain.DimReq;
import cloud.demand.app.modules.sop_util.process.service.BudgetService;
import cloud.demand.app.modules.sop_util.process.store.item.BudgetItem;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/** 预算 */
@Service
public class BudgetServiceImpl implements BudgetService {
    @Override
    public List<BudgetItem> getData(DimReq req) {
        List<BudgetItem> ret = new ArrayList<>();
        if (ListUtils.isEmpty(req.getDim())){
            ret.add(new BudgetItem());
        }
        return ret;
    }
}
