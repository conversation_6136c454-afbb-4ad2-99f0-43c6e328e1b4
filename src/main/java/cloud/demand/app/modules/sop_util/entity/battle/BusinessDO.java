package cloud.demand.app.modules.sop_util.entity.battle;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

/** 基础实体类+事业群，部门和规划产品 */
@Data
public class BusinessDO extends BaseDO{

    /** 事业群 */
    @Column("bg_name")
    private String bgName;

    /** 部门名称 */
    @Column("dept_name")
    private String deptName;

    /** 规划产品名称 */
    @Column("plan_product_name")
    private String planProductName;
}
