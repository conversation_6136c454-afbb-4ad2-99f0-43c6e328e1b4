package cloud.demand.app.modules.sop_util.domain.demand;

import cloud.demand.app.modules.sop_util.model.version.SopUtilVersionItem;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 需求版本
 */
@Data
public class ReportDemandVersionResp {

    List<Item> data;

    public static Item transform(SopUtilVersionItem versionItem) {
        Item item = new Item();
        item.setLabel(versionItem.getLabel());
        item.setCvmVersion(versionItem.getCvmVersion());
        item.setCvmVersionDateTime(versionItem.getCvmVersionDateTime());
        item.setDeviceVersion(versionItem.getDeviceVersion());
        item.setDeviceVersionDateTime(versionItem.getDeviceVersionDateTime());
        return item;
    }

    @Data
    public static class Item {
        /**
         * 标签
         */
        private String label;

        /**
         * cvm版本
         */
        private String cvmVersion;

        /**
         * cvm的版本时间
         */
        private LocalDateTime cvmVersionDateTime;

        /**
         * 物理机版本
         */
        private String deviceVersion;

        /**
         * 物理机版本时间
         */
        private LocalDateTime deviceVersionDateTime;
        
        /** 是否有禁用 */
        private Boolean isForbidden = false;
    }

}
