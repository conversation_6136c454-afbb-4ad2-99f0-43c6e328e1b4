package cloud.demand.app.modules.sop_util.domain;

import lombok.Data;

import java.util.List;

@Data
public class DimReq implements IDimReq{

    private List<String> dim;

    @Override
    public List<String> getDim() {
        return dim;
    }

    public static DimReq copy(IDimReq req){
        if (req == null){
            return null;
        }
        DimReq dimReq = new DimReq();
        dimReq.setDim(req.getDim());
        return dimReq;
    }
}
