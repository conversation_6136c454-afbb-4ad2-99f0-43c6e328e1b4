package cloud.demand.app.modules.sop_util.model;

import cloud.demand.app.modules.sop_util.domain.IDimReq;
import cloud.demand.app.modules.sop_util.model.demand.SopDemandReturnReq;
import lombok.Data;

import java.util.List;

/** 公共请求 */
@Data
public class CommonReq implements IDimReq {

    /** sop工具业务类型 */
    private List<String> utilBusinessType;

    /** sop大类 */
    private List<String> sopBigFamily;

    /** 设备类型 */
    private List<String> deviceType;

    /** 取值时间范围 */
    private SopDemandReturnReq.TimePeriod time;

    /** 维度 */
    private List<String> dim;
}
