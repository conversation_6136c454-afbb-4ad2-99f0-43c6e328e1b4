package cloud.demand.app.modules.sop_util.domain.baffle.resp;

import cloud.demand.app.modules.sop_util.domain.baffle.BaseRespItem;
import cloud.demand.app.modules.sop_util.entity.battle.item.SupplyDO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/** 供应响应数据 */
@Data
public class SupplyRespItem extends BaseRespItem {

    public static List<SupplyRespItem> transform(SupplyDO data, YearMonth startYearMonth){
        List<SupplyRespItem> ret = new ArrayList<>();
        if (data == null){
            return ret;
        }
        BigDecimal[] coreNumM12 = data.getCoreNumM12();
        BigDecimal baseValue = BigDecimal.ZERO;
        for (int i = 0; i < coreNumM12.length; i++) {
            SupplyRespItem item = new SupplyRespItem();
            item.setDeviceFamily(data.getDeviceFamily());
            item.setDeviceType(data.getDeviceType());
            item.setYearMonth(startYearMonth.plusMonths(i)); // 基于起始时间 +n 月（n：0-11）
            item.setCoreNum(baseValue.add(coreNumM12[i])); // 累计计算，Mn = Sum(Mn-1) + Mn
            baseValue = item.getCoreNum();  // 修改base为Sum(Mn)
            ret.add(item);
        }
        return ret;
    }
}
