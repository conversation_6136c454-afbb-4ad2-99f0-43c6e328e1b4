package cloud.demand.app.modules.sop_util.process.service.impl;

import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop.http.service.SopUtilBaffleHttpService;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop_util.domain.DimReq;
import cloud.demand.app.modules.sop_util.domain.baffle.req.InventoryBaffleReq;
import cloud.demand.app.modules.sop_util.domain.baffle.req.SupplyBaffleReq;
import cloud.demand.app.modules.sop_util.domain.baffle.resp.InventoryRespItem;
import cloud.demand.app.modules.sop_util.domain.baffle.resp.SupplyRespItem;
import cloud.demand.app.modules.sop_util.model.other.SupplyReq;
import cloud.demand.app.modules.sop_util.process.service.SupplyService;
import cloud.demand.app.modules.sop_util.process.store.item.InventoryItem;
import cloud.demand.app.modules.sop_util.process.store.item.SupplyItem;
import cloud.demand.app.modules.sop_util.service.SopUtilCommonService;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/** 供应 */
@Service
public class SupplyServiceImpl implements SupplyService {

    /** 库存，供应和缺口-上游接口 */
    @Resource
    private SopUtilBaffleHttpService httpService;

    /** 公共清洗 */
    @Resource
    CommonDbHelper commonDbHelper;

    @Resource
    SopUtilCommonService commonService;

    @Override
    public List<SupplyItem> getData(SupplyReq req) {
        SupplyBaffleReq httpReq = new SupplyBaffleReq();

        httpReq.setVersionDate(commonService.getVersionDate(req.getVersion()));
        httpReq.setOnyCvmProduct(BooleanUtils.isTrue(req.getOnlyCvmProduct()));
        httpReq.setExcludeGpu(BooleanUtils.isTrue(req.getExcludeGpu()));

        List<SupplyRespItem> data = httpService.getSupplyData(httpReq);

        // 物理机机型族 -> 大类
        Map<String, String> bigFamilyMap = commonDbHelper.getBigFamilyMap(ResourceType.DEVICE.getName());

        List<SupplyItem> ret = new ArrayList<>();

        boolean excludeGpu = BooleanUtils.isTrue(req.getExcludeGpu());

        for (SupplyRespItem datum : data) {
            if (excludeGpu && datum.getDeviceFamily().contains("GPU")){
                continue;
            }
            SupplyItem item = new SupplyItem();
            item.setYearMonth(CommonUtils.yearMonth(datum.getYearMonth()));
            item.setCoreNum(datum.getCoreNum());
            item.setDeviceType(datum.getDeviceType());
            // 保底设置为【其他】
            item.setSopBigFamily(bigFamilyMap.getOrDefault(datum.getDeviceFamily(), Constant.OTHER_VALUE_STR));
            ret.add(item);
        }
        return ret;
    }
}
