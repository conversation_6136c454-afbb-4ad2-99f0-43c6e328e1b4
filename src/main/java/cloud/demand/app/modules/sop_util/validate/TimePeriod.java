package cloud.demand.app.modules.sop_util.validate;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Documented
@Target({ElementType.PARAMETER,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TimePeriodValidate.class)
public @interface TimePeriod {

    /** 是否运行null  */
    boolean allowNull() default true;

    /** 是否运行结束日期为null */
    boolean allowEndNull() default true;

    /** 时间正则，默认年月 */
    String pattern() default "yyy-MM";

    String message() default "日期范围格式不合法";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
