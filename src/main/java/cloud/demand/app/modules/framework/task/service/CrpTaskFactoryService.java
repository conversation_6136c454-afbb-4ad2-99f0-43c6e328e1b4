package cloud.demand.app.modules.framework.task.service;

import cloud.demand.app.modules.framework.task.model.CrpTaskMethodInfo;
import cloud.demand.app.modules.framework.task.domain.TaskReq;
import cloud.demand.app.modules.framework.task.domain.TaskReturnT;

/** 任务工厂 */
public interface CrpTaskFactoryService {

    /** 获取任务信息 */
    public CrpTaskMethodInfo getTaskInfo(String namespace, String taskName);

    /** 执行任务（调用设置的任务执行类并设置任务环境） */
    public TaskReturnT<Object> doTask(TaskReq req);
}
