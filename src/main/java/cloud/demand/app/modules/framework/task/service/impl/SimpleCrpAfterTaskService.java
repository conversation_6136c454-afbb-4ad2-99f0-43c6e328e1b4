package cloud.demand.app.modules.framework.task.service.impl;

import cloud.demand.app.modules.framework.task.anno.CrpTaskAspectMethod;
import cloud.demand.app.modules.framework.task.service.CrpAfterTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
/** 后置处理的简单实现 */
@Component
@Slf4j
public class SimpleCrpAfterTaskService implements CrpAfterTaskService {
    @CrpTaskAspectMethod(isAll = true,isBefore = false,order = -1)
    public void log(){
        log.info("后置操作开始...");
    }
}
