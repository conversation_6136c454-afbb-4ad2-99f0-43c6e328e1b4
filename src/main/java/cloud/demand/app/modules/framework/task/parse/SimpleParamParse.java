package cloud.demand.app.modules.framework.task.parse;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pugwoo.wooutils.json.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/** 简单解析类 */
public class SimpleParamParse implements IParamParse{
    @Override
    public Object[] parse(String param) {
        if (StringUtils.isBlank(param)){
            return null;
        }
        List<Object> parse = JSON.parse(param, new TypeReference<List<Object>>() {});
        Object[] ret = new Object[parse.size()];
        for (int i = 0; i < parse.size(); i++) {
            ret[i] = parse.get(i);
        }
        return ret;
    }

    /** 没有状态的解析这里可以返回自身 */
    @Override
    public IParamParse copy() {
        return this;
    }
}
