package cloud.demand.app.modules.framework.task.utils;


import cloud.demand.app.modules.framework.task.model.CrpTaskLocalEnv;

/** 任务执行环境 */
public class CrpTaskEnv {
    private static final InheritableThreadLocal<CrpTaskLocalEnv> local = new InheritableThreadLocal<>();

    /** 获取上下文 */
    public static CrpTaskLocalEnv get(){
        return local.get();
    }

    /** 设置上下文 */
    public static void set(CrpTaskLocalEnv info){
        local.set(info);
    }

    /** 移除上下文 */
    public static void remove(){
        local.remove();
    }
}
