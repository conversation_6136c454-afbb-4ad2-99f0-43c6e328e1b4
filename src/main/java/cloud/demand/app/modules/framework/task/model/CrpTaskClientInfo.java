package cloud.demand.app.modules.framework.task.model;

import cloud.demand.app.modules.framework.task.anno.CrpTaskAspectMethod;
import cloud.demand.app.modules.framework.task.anno.CrpTaskClient;
import cloud.demand.app.modules.framework.task.parse.IParamParse;
import cloud.demand.app.modules.framework.task.parse.SimpleParamParse;
import cloud.demand.app.modules.framework.task.exception.CrpTaskException;
import cloud.demand.app.modules.framework.task.service.CrpTaskService;
import cloud.demand.app.modules.framework.task.utils.CrpTaskUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.support.AopUtils;
import org.springframework.context.ApplicationContext;
import yunti.boot.exception.ITException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CrpTaskClientInfo {

    /** 命名空间 */
    private String namespace;

    /** 命名空间描述 */
    private String namespaceDesc;

    /** 任务执行类 */
    private CrpTaskService taskService;

    /** 待执行的任务bean */
    private Object bean;

    /** 参数解析的类 */
    private IParamParse parse;

    /** key：taskName */
    private Map<String,CrpTaskMethodInfo> taskMap;

    /** 公共前置处理，按order降序 */
    private List<Consumer<Object[]>> beforeTaskMethod;

    /** 公共后置处理，按order降序 */
    private List<Consumer<Object[]>> afterTaskMethod;

    public static CrpTaskClientInfo transForm(ApplicationContext context,
                                              Map<Class<? extends IParamParse>,IParamParse> parseMap,
                                              String beanName,
                                              Object bean){
        CrpTaskClientInfo ret = new CrpTaskClientInfo();

        CrpTaskClient client = AopUtils.getTargetClass(bean).getAnnotation(CrpTaskClient.class);

        // step1：初始化信息
        ret.setBean(bean); // 执行任务的bean
        ret.setNamespace(client.namespace());   // 命名空间
        ret.setNamespaceDesc(client.desc()); // 命名空间描述
        ret.setTaskService(context.getBean(client.taskBean())); // 代理执行任务的bean
        Class<?>[] aspect = client.aspectTaskBean();   // 任务执行切面

        Map<String, Consumer<Object[]>> beforeMethodMap = new HashMap<>();
        Map<String, Consumer<Object[]>> afterMethodMap = new HashMap<>();

        List<Tuple2<Integer,Consumer<Object[]>>> beforeAll = new ArrayList<>();
        List<Tuple2<Integer,Consumer<Object[]>>> afterAll = new ArrayList<>();

        if (aspect != null){
            for (Class<?> aClass : aspect) {
                Object aspectBean = context.getBean(aClass);
                for (Method declaredMethod : aspectBean.getClass().getDeclaredMethods()) {
                    CrpTaskAspectMethod anno = declaredMethod.getAnnotation(CrpTaskAspectMethod.class);
                    if (anno == null){
                        continue;
                    }
                    // 是否为全局的
                    if (anno.isAll()){
                        // 是全局的存到list中
                        List<Tuple2<Integer,Consumer<Object[]>>> ls = anno.isBefore()? beforeAll: afterAll;
                        ls.add(new Tuple2<>(anno.order(),(objs)-> {
                            try {
                                declaredMethod.setAccessible(true);
                                if (declaredMethod.getParameterCount() == 0){
                                    declaredMethod.invoke(aspectBean);
                                }else {
                                    declaredMethod.invoke(aspectBean,objs);
                                }
                            } catch (Exception e) {
                                // 如果是方法内部抛出的异常，这里捕获并注入是否失败的参数
                                if (e instanceof InvocationTargetException){
                                    throw new CrpTaskException(((InvocationTargetException) e).getTargetException(), anno.failIfException());
                                }
                                throw new ITException(e);
                            }
                        }));
                    }else {
                        // 不是全局的存到map中
                        if (StringUtils.isBlank(anno.taskName())){
                            throw new ITException(String.format("任务切面绑定的任务名称不能为空，method:【%s】", declaredMethod.getName()));
                        }
                        Map<String, Consumer<Object[]>> mp = anno.isBefore()? beforeMethodMap: afterMethodMap;
                        mp.put(anno.taskName(),(objs)-> {
                            try {
                                declaredMethod.setAccessible(true);
                                if (declaredMethod.getParameterCount() == 0){
                                    declaredMethod.invoke(aspectBean);
                                }else {
                                    declaredMethod.invoke(aspectBean,objs);
                                }
                            } catch (Exception e) {
                                // 如果是方法内部抛出的异常，这里捕获并注入是否失败的参数
                                if (e instanceof InvocationTargetException){
                                    throw new CrpTaskException(((InvocationTargetException) e).getTargetException(), anno.failIfException());
                                }
                                throw new ITException(e);
                            }
                        });
                    }
                }
            }
            // step2：填充切片信息
            if (ListUtils.isNotEmpty(beforeAll)){
                ret.setBeforeTaskMethod(beforeAll.stream().sorted(Comparator.comparingInt(Tuple2::_1)).map(Tuple2::_2).collect(Collectors.toList()));
            }
            if (ListUtils.isNotEmpty(afterAll)){
                ret.setAfterTaskMethod(afterAll.stream().sorted(Comparator.comparingInt(Tuple2::_1)).map(Tuple2::_2).collect(Collectors.toList()));
            }
        }
        // step3：参数解析类实例化
        Class<? extends IParamParse> parseClass = client.parse();
        IParamParse parseObj = ObjectUtils.defaultIfNull(CrpTaskUtils.getParse(context, parseMap, parseClass),new SimpleParamParse());
        ret.setParse(parseObj);
        // step4：注册任务方法map
        ret.setTaskMap(CrpTaskMethodInfo.transForm(ret,beforeMethodMap,afterMethodMap,context,parseMap));
        return ret;
    }
}
