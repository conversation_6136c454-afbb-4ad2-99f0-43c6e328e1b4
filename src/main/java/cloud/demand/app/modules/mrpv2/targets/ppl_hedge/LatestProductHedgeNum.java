package cloud.demand.app.modules.mrpv2.targets.ppl_hedge;

import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataMap;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.nutz.lang.Lang;

public class LatestProductHedgeNum extends ProductHedgeTemplate{

    @Override
    public String versionEn() {
        return "latest";
    }

    @Override
    public String targetName() {
        return "latest_product_hedge_num";
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public String version() {
        return "最新版";
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(new LatestProductHostForecastNum(),
                new LatestProductStockCannotSupplyNum(),
                new LatestProductStockSupplyNum());
    }

    @Override
    public Map<String, DailyMrpV2DataMap> loadComplexData(Map<String, Object> shares) {
        MyMapUtils.putTargetGenTime(shares, "最新版PPL对冲数据");
        return productHedgeNum(loadSql(), shares, null);
    }
}
