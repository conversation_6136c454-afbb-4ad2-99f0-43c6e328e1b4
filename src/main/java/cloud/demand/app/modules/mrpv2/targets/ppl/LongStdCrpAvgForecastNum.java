package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

public class LongStdCrpAvgForecastNum extends LongStdCrpForecastNum {

    @Override
    public String targetName() {
        return "avg_long_forecast_num";
    }

    @Override
    public String targetDisplay() {
        return "532版";
    }

    @Override
    public List<TailPplForecastDO> loadTailPplForecastDO(Map<String, Object> shares, String yearMonthStart) {
        // 这里要加载3个中长尾版本的532值
        Map<String, TailPplForecastDO> m3Map = tailMnTargetVOMap(shares, -3);
        Map<String, TailPplForecastDO> m2Map = tailMnTargetVOMap(shares, -2);
        Map<String, TailPplForecastDO> m1Map = tailMnTargetVOMap(shares, -1);
        return avgResult(m3Map, m2Map, m1Map);
    }

    public static Map<String, TailPplForecastDO> tailMnTargetVOMap(Map<String, Object> shares, int addMonth) {
        List<TailPplForecastDO> mn = (List<TailPplForecastDO>) shares.get("tail-m" + addMonth);
        if (!CollectionUtils.isEmpty(mn)) {
            return ListUtils.toMap(mn, TailPplForecastDO::withTimeKey, Function.identity());
        } else {
            return new HashMap<>();
        }
    }

    public static List<TailPplForecastDO> avgResult(Map<String, TailPplForecastDO> m3TargetVOMap,
            Map<String, TailPplForecastDO> m2TargetVOMap,
            Map<String, TailPplForecastDO> m1TargetVOMap) {
        Set<String> keys = new HashSet<>(m3TargetVOMap.keySet());
        keys.addAll(m2TargetVOMap.keySet());
        keys.addAll(m1TargetVOMap.keySet());
        List<TailPplForecastDO> result = new ArrayList<>();
        for (String key : keys) {
            TailPplForecastDO r;
            TailPplForecastDO m3 = m3TargetVOMap.get(key);
            r = avg(null, m3, Constant.b05);
            TailPplForecastDO m2 = m2TargetVOMap.get(key);
            r = avg(r, m2, Constant.b03);
            TailPplForecastDO m1 = m1TargetVOMap.get(key);
            r = avg(r, m1, Constant.b02);
            result.add(r);
        }
        return result;
    }

    public static TailPplForecastDO avg(TailPplForecastDO r, TailPplForecastDO mn, BigDecimal percentage) {
        if (mn != null) {
            if (r == null) {
                r = TailPplForecastDO.copyMyself(mn);
                r.setNum(r.getNum().multiply(percentage));
            } else {
                BigDecimal num = mn.getNum().multiply(percentage);
                r.setNum(r.getNum().add(num));
            }
        }
        return r;
    }
}
