package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.NotShowIndex;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;

import java.util.List;
import java.util.Map;

public class LongCurBillNum extends CurBillNum implements NotShowIndex {
    public static String staticTargetName() {
        return "long_cur_bill_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }


    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public TargetTemplate dailyNotDistinct() {
        return new LongDailyNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate monthAvgNotDistinct() {
        return new LongMonthAvgNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate dailyDistinct() {
        return new LongDailyNotDistinctCurBillNum();
    }

    @Override
    public TargetTemplate monthAvgDistinct() {
        return new LongMonthAvgNotDistinctCurBillNum();
    }
}
