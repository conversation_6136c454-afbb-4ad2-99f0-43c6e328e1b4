package cloud.demand.app.modules.mrpv2.entity;

import cloud.demand.app.modules.mrpv2.enums.BizTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.IsNewCategoryEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("virtual_table")
public class TailPplForecastDO {
    @Column(value = "predict_index")
    private Integer predictIndex;
    @Column(value = "year_month")
    private String yearMonth;
    @Column(value = "gins_family")
    private String ginsFamily;
    @Column(value = "region_name")
    private String regionName;
    @Column(value = "demand_type")
    private String demandType;
    @Column(value = "biz_type")
    private String bizType;
    @Column(value = "num")
    private BigDecimal num;
    @Column(value = "data_product")
    private String dataProduct;

    /** 是否为新版中长尾 */
    @Column(value = "is_new_category")
    private Integer isNewCategory;

    /** {@link cloud.demand.app.modules.mrpv2.enums.IsAddOrElaEnum} */
    private Integer isAddOrEla;

    public static TailPplForecastDO copyMyself(TailPplForecastDO o) {
        TailPplForecastDO tailPplForecastDO = new TailPplForecastDO();
        tailPplForecastDO.setPredictIndex(o.getPredictIndex());
        tailPplForecastDO.setYearMonth(o.getYearMonth());
        tailPplForecastDO.setGinsFamily(o.getGinsFamily());
        tailPplForecastDO.setRegionName(o.getRegionName());
        tailPplForecastDO.setDemandType(o.getDemandType());
        tailPplForecastDO.setBizType(o.getBizType());
        tailPplForecastDO.setNum(o.getNum());
        tailPplForecastDO.setDataProduct(o.getDataProduct());
        tailPplForecastDO.setIsNewCategory(o.getIsNewCategory());
        tailPplForecastDO.setIsAddOrEla(o.getIsAddOrEla());
        return tailPplForecastDO;
    }

    public static MrpV2DataDO copy(TailPplForecastDO o) {
        MrpV2DataDO mrpV2DataDO = new MrpV2DataDO();
        mrpV2DataDO.setOrderType("中长尾");
        mrpV2DataDO.setBizType(o.getBizType());
        mrpV2DataDO.setRegionName(o.getRegionName());
        mrpV2DataDO.setGinsFamily(o.getGinsFamily());
        mrpV2DataDO.setCustomerType("中长尾客户");
        mrpV2DataDO.setDemandType(o.getDemandType());
        mrpV2DataDO.setDataProduct(o.getDataProduct());
        mrpV2DataDO.setYearMonth(o.getYearMonth());
        mrpV2DataDO.setNum(o.getNum());
        mrpV2DataDO.setIsNewCategory(o.getIsNewCategory());
        mrpV2DataDO.setIsAddOrEla(o.getIsAddOrEla());
        return mrpV2DataDO;
    }

    public static List<MrpV2DataDO> copy(List<TailPplForecastDO> os) {
        List<MrpV2DataDO> r = new ArrayList<>(os.size());
        for (TailPplForecastDO o : os) {
            r.add(copy(o));
        }
        return r;
    }

    public String key() {
        return String.join("@",bizType,
                regionName, ginsFamily, demandType, dataProduct);
    }

    public String withTimeKey() {
        return String.join("@", key(), yearMonth);
    }
}
