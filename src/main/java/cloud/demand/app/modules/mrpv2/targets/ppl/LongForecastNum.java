package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.modules.mrpv2.entity.CurrentIndex;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.NotShowIndex;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import org.nutz.lang.Lang;

import java.util.List;
import java.util.Map;

public class LongForecastNum extends TargetTemplate implements CurrentIndex, NotShowIndex {

    public static String staticTargetName()  {
        return "long_forecast_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "预测量(中长尾)";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "需求计划", null);
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        return Lang.list(// 新版中长尾指标数据
                new LongStdCrpLatestForecastNum(), new LongStdCrpBenchForecastNum(), new LongStdCrpAvgForecastNum(), new LongStdCrpV2AvgForecastNum());
    }
}
