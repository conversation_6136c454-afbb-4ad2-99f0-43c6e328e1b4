package cloud.demand.app.modules.mrpv2.alert.parse.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.alert.entity.AbstractAlter;
import cloud.demand.app.modules.mrpv2.alert.entity.IndustryDemandPlanAlterDO;
import cloud.demand.app.modules.mrpv2.alert.parse.AbstractAlterParse;
import com.pugwoo.dbhelper.DBHelper;
import lombok.AllArgsConstructor;

import java.util.List;

@AllArgsConstructor
public class IndustryDemandPlanByCustomerParse extends AbstractAlterParse<List<IndustryDemandPlanAlterDO>> {
    private DBHelper ckcldDBHelper;

    private String latestTableName;

    @Override
    protected DBHelper getDbHelper() {
        return ckcldDBHelper;
    }

    @Override
    protected String getSql() {
        //【daily_mrp_v2_data】表改为动态表，根据【daily_mrp_v2_data_describe】的配置【is_latest】为【1】的为最新版本表
        return ORMUtils.getSql("/sql/mrp/alert/industry_demand_plan_by_customer.sql").replace("${tableName}",latestTableName);
    }

    @Override
    protected Class<? extends AbstractAlter> getAlterClass() {
        return IndustryDemandPlanAlterDO.class;
    }
}
