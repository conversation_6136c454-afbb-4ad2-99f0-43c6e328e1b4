package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataMap;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.model.clean.AbstractCleanDemand;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.LatestForecastCoreByDayOriginNumMax;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.LatestForecastCoreByDayOriginNumMin;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 532新版预测量 - 核天
 */
@Slf4j
public class LatestForecastCoreByDayNum extends TargetTemplate {
    protected final DBHelper ckcldStdCrpDBHelper = DBList.ckcldStdCrpDBHelper;

    @Override
    public String targetName() {
        return "latest_forecast_core_by_day_num";
    }

    @Override
    public String targetDisplay() {
        return "预测核天量";
    }

    @Override
    public String loadSql() {
        return PaasUtils.sqlReplacePAAS(ORMUtils.getSql("/sql/mrp/latest_forecast_num_std_crp_core_by_day.sql"));
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测量-核天",
                new RspFieldHeader("", "需求计划", null));
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public List<TargetTemplate> complexNode() {
        // TODO 支持干预前后，共识前后核天预测准确率
        return ListUtils.newList(new LatestForecastCoreByDayNumMin(), new LatestForecastCoreByDayNumMax());
    }

    @Override
    public Map<String, DailyMrpV2DataMap> loadComplexData(Map<String, Object> shares) {
        // 从 532新版-核天预测量中取值
        String yearMonthStart = (String) shares.get("year_month_start");
        String sql = loadSql();
        MyMapUtils.putTargetGenTime(shares, "预测量-核天");
        List<DDO> all = ckcldStdCrpDBHelper.getRaw(DDO.class, sql, yearMonthStart);

        // 遍历拿到的数据，补充缺少的指标
        Map<String, DailyMrpV2DataMap> result = new HashMap<>();
        CleanService bean = SpringUtil.getBean(CleanService.class);

        for (DDO ddo : all) {

            bean.clean(ddo); // 清洗数据

            String customerType = MyMapUtils.getDefCustomerType(ddo.getCustomerType(),ddo.getIndustryDept());
            ddo.setCustomerType(MyMapUtils.customerType(shares, ddo.getUin(),
                    ddo.getYearMonth(), customerType));

            String key = ddo.withTimeKey();
            DailyMrpV2DataMap dataMap = result.get(key);

            if (dataMap == null) {
                dataMap = DDO.copy(ddo);
                result.put(key, dataMap);
            } else {
                // 更新数据
                DDO.merge(dataMap, ddo);
                log.error("核天预测量数据重复，key=" + key);
            }
        }

        // 暂时忽略中长尾数据

        return result;
    }

    @Data
    @Table("virtual_table")
    public static class DDO extends AbstractCleanDemand {
        // 匹配大宽表的key字段 开始
        /** 订单类型<br/>Column: [order_type] */
        @Column(value = "order_type")
        private String orderType;

        /** 业务分类<br/>Column: [biz_type] */
        @Column(value = "biz_type")
        private String bizType;

        @Column(value = "is_spike")
        private Integer isSpike;

        /** 行业/产品(也叫行业/部门)<br/>Column: [industry_or_product] */
        @Column(value = "industry_or_product")
        private String industryOrProduct;

        /** 行业部门<br/>Column: [industry_dept] */
        @Column(value = "industry_dept")
        private String industryDept;

        /** 地域类型<br/>Column: [region_type] */
        @Column(value = "region_type")
        private String regionType;

        /** 地域<br/>Column: [region_name] */
        @Column(value = "region_name")
        private String regionName;

        /** 可用区<br/>Column: [zone_name] */
        @Column(value = "zone_name")
        private String zoneName;

        /** 实例类型<br/>Column: [gins_family] */
        @Column(value = "gins_family")
        private String ginsFamily;

        /** uin<br/>Column: [uin] */
        @Column(value = "uin")
        private String uin;

        /** 客户内外部属性<br/>Column: [customer_inside_or_outside] */
        @Column(value = "customer_inside_or_outside")
        private String customerInsideOrOutside;

        /** 客户类型<br/>Column: [customer_type] */
        @Column(value = "customer_type")
        private String customerType;

        /** 战区名<br/>Column: [war_zone_name] */
        @Column(value = "war_zone_name")
        private String warZoneName;

        /** 客户集团<br/>Column: [customer_group] */
        @Column(value = "customer_group")
        private String customerGroup;

        /** 客户简称<br/>Column: [customer_short_name] */
        @Column(value = "customer_short_name")
        private String customerShortName;

        /** 客户名称<br/>Column: [customer_name] */
        @Column(value = "customer_name")
        private String customerName;

        /** 客户个人类型, 个人/企业<br/>Column: [customer_name] */
        @Column(value = "customer_person_type")
        private String customerPersonType;

        /** 需求类型<br/>Column: [demand_type] */
        @Column(value = "demand_type")
        private String demandType;

        @Column(value = "data_product")
        private String dataProduct;

        @Column(value = "product_class")
        private String productClass;

        // 匹配大宽表的key字段 结束

        // 时间及数值字段 开始

        @Column(value = "year_month")
        private String yearMonth;

        /**
         * 最小核天
         */
        @Column("min_core_day_num")
        private BigDecimal minCoreDayNum;

        /**
         * 最大核天
         */
        @Column("max_core_day_num")
        private BigDecimal maxCoreDayNum;

        public String key() {
            // 适当去掉一些关联出来的字段，如境内外，uin关联的客户信息
            return String.join("@", orderType, bizType, industryOrProduct, industryDept,getProductClass(),getNewCustomerType(),getProjectType(),
                    regionName, zoneName, ginsFamily, uin, demandType, dataProduct, customerType,
                    customerName, customerShortName, warZoneName);
        }

        public String withTimeKey() {
            return String.join("@", key(), yearMonth);
        }

        public static DailyMrpV2DataMap copy(DDO other) {
            DailyMrpV2DataMap mrpV2DataDO = new DailyMrpV2DataMap();
            mrpV2DataDO.setOrderType(other.getOrderType());
            mrpV2DataDO.setBizType(other.getBizType());
            mrpV2DataDO.setIndustryOrProduct(other.getIndustryOrProduct());
            mrpV2DataDO.setIndustryDept(other.getIndustryDept());
            mrpV2DataDO.setRegionType(other.getRegionType());
            mrpV2DataDO.setRegionName(other.getRegionName());
            mrpV2DataDO.setZoneName(other.getZoneName());
            mrpV2DataDO.setGinsFamily(other.getGinsFamily());
            mrpV2DataDO.setUin(other.getUin());
            mrpV2DataDO.setCustomerInsideOrOutside(other.getCustomerInsideOrOutside());
            mrpV2DataDO.setCustomerType(other.getCustomerType());
            mrpV2DataDO.setWarZoneName(other.getWarZoneName());
            mrpV2DataDO.setCustomerGroup(other.getCustomerGroup());
            mrpV2DataDO.setCustomerShortName(other.getCustomerShortName());
            mrpV2DataDO.setCustomerName(other.getCustomerName());
            mrpV2DataDO.setCustomerPersonType(other.getCustomerPersonType());
            mrpV2DataDO.setDemandType(other.getDemandType());
            mrpV2DataDO.setDataProduct(other.getDataProduct());

            mrpV2DataDO.setProjectType(other.getProjectType()); // 项目类型
            mrpV2DataDO.setProductClass(other.getProductClass()); // 产品
            mrpV2DataDO.setNewCustomerType(other.getNewCustomerType()); // 新客户分类

            // 目前只支持干预后的核天
            mrpV2DataDO.putTarget(LatestForecastCoreByDayNumMin.staticTargetName(), other.getYearMonth(), other.getMinCoreDayNum());
            mrpV2DataDO.putTarget(LatestForecastCoreByDayNumMax.staticTargetName(), other.getYearMonth(), other.getMaxCoreDayNum());
            return mrpV2DataDO;
        }

        public static void merge(DailyMrpV2DataMap mrpV2DataDO,DDO other){
            mrpV2DataDO.putTarget(LatestForecastCoreByDayNumMin.staticTargetName(), other.getYearMonth(), other.getMinCoreDayNum());
            mrpV2DataDO.putTarget(LatestForecastCoreByDayNumMax.staticTargetName(), other.getYearMonth(), other.getMaxCoreDayNum());
        }

        public static DailyMrpV2DataMap originCopy(DDO other) {
            DailyMrpV2DataMap mrpV2DataDO = new DailyMrpV2DataMap();
            mrpV2DataDO.setOrderType(other.getOrderType());
            mrpV2DataDO.setBizType(other.getBizType());
            mrpV2DataDO.setIndustryOrProduct(other.getIndustryOrProduct());
            mrpV2DataDO.setIndustryDept(other.getIndustryDept());
            mrpV2DataDO.setRegionType(other.getRegionType());
            mrpV2DataDO.setRegionName(other.getRegionName());
            mrpV2DataDO.setZoneName(other.getZoneName());
            mrpV2DataDO.setGinsFamily(other.getGinsFamily());
            mrpV2DataDO.setUin(other.getUin());
            mrpV2DataDO.setCustomerInsideOrOutside(other.getCustomerInsideOrOutside());
            mrpV2DataDO.setCustomerType(other.getCustomerType());
            mrpV2DataDO.setWarZoneName(other.getWarZoneName());
            mrpV2DataDO.setCustomerGroup(other.getCustomerGroup());
            mrpV2DataDO.setCustomerShortName(other.getCustomerShortName());
            mrpV2DataDO.setCustomerName(other.getCustomerName());
            mrpV2DataDO.setCustomerPersonType(other.getCustomerPersonType());
            mrpV2DataDO.setDemandType(other.getDemandType());
            mrpV2DataDO.setDataProduct(other.getDataProduct());

            mrpV2DataDO.setProjectType(other.getProjectType()); // 项目类型
            mrpV2DataDO.setProductClass(other.getProductClass()); // 产品
            mrpV2DataDO.setNewCustomerType(other.getNewCustomerType()); // 新客户分类

            // 目前只支持干预后的核天
            mrpV2DataDO.putTarget(LatestForecastCoreByDayOriginNumMin.staticTargetName(), other.getYearMonth(), other.getMinCoreDayNum());
            mrpV2DataDO.putTarget(LatestForecastCoreByDayOriginNumMax.staticTargetName(), other.getYearMonth(), other.getMaxCoreDayNum());
            return mrpV2DataDO;
        }

        public static void originMerge(DailyMrpV2DataMap mrpV2DataDO,DDO other){
            mrpV2DataDO.putTarget(LatestForecastCoreByDayOriginNumMin.staticTargetName(), other.getYearMonth(), other.getMinCoreDayNum());
            mrpV2DataDO.putTarget(LatestForecastCoreByDayOriginNumMax.staticTargetName(), other.getYearMonth(), other.getMaxCoreDayNum());
        }
    }


}
