package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_report.service.util.SQLS;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.entity.OdsTxyAppidInfoCfShortDO;
import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

@Slf4j
public class V2AvgForecastNum extends AvgForecastNum {
    @Override
    public String targetName() {
        return "v2_avg_forecast_num";
    }

    @Override
    public String targetDisplay() {
        return "532新版";
    }

    @Override
    public String loadSql() {
        return ORMUtils.getSql("/sql/mrp/avg_forecast_num.sql");
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        String yearMonthStart = (String) shares.get("year_month_start");
        String sql = loadSql();
        initShares(shares);
        MyMapUtils.putTargetGenTime(shares, "532新版预测量");
        Map<String, MrpV2DataDO> m2Map = mnTargetVOMap(shares, sql, -2, yearMonthStart);
        Map<String, MrpV2DataDO> m1Map = mnTargetVOMap(shares, sql, -1, yearMonthStart);
        Map<String, MrpV2DataDO> m0Map = mnTargetVOMap(shares, sql, 0, yearMonthStart);
        return AvgForecastNum.avgResult(m2Map, m1Map, m0Map);

    }

    private List<PplVersionDO> loadAllPplVersionDOs() {
        return demandDBHelper.getAll(PplVersionDO.class,
                "where status = ?", PplVersionStatusEnum.DONE.getCode());
    }

    public void initShares(Map<String, Object> shares) {
        List<PplVersionDO> pplVersionDOS = loadAllPplVersionDOs();
        shares.put("pplVersionDOS", pplVersionDOS);
        List<YearMonth> allYearMonths = BenchForecastNum.loadAllYearMonth(pplVersionDOS);
        shares.put("allYearMonths", allYearMonths);
        for (int i = -3; i < 0; i++) {
            Map<String, List<String>> mn = BenchForecastNum.monthAddIncreaseMonthVersion(i, pplVersionDOS, allYearMonths);
            shares.put("m" + i + "BenchVersionMap", mn);
        }
    }

    @Override
    public Map mnVersionMap(Map<String, Object> shares, int addMonth, List<YearMonth> allYearMonths) {
        log.info("mrpv2 load v2 avg mnVersionMap");
        Map mnVersionMap;
        if (addMonth == 0) {
            // 提取M-1的最后一个版本
            Map<String, List<String>> m1 = (Map<String, List<String>>) shares.get("m-1BenchVersionMap");
            mnVersionMap = new HashMap();
            for (Entry<String, List<String>> entry : m1.entrySet()) {
                List<String> versionCodes = entry.getValue();
                mnVersionMap.put(entry.getKey(), ListUtils.newArrayList(versionCodes.get(versionCodes.size() - 1)));
            }
            shares.put("m-0BenchVersionMap", mnVersionMap);
        } else {
            mnVersionMap = (Map) shares.get("m" + addMonth + "BenchVersionMap");
        }
        return mnVersionMap;
    }
}
