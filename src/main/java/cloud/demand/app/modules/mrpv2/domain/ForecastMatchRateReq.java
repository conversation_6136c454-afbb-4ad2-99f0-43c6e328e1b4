package cloud.demand.app.modules.mrpv2.domain;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

/** 准确率查询（提供给行业驾驶舱） */
@ToString(callSuper = true)
@Data
public class ForecastMatchRateReq extends SimpleForecastMatchRateReq{


    /** 客户类型，枚举：1-内部客户、0-外部客户 */
    private Integer customerRange;// 客户类型，枚举：1-内部客户、0-外部客户

    /** 通用客户简称集合 */
    private List<String> customerShortName; // 注意：这里是通用客户简称，一期是客户简称，二期前端不动这里在请求行业数据看板时转换

    /** 行业部门集合 */
    private List<String> industryDept;

    /** CRP战区集合 */
    private List<String> warZoneName; // 注意：这里是通用CRP战区，一期是战区，二期前端不动这里在请求行业数据看板时转换

    /** 境内外 */
    private List<String> customhouseTitle;

    /** 地域集合 */
    private List<String> regionName;

    /** 可用区集合 */
    private List<String> zoneName;

    /** 实例类型集合 */
    private List<String> instanceType;


    public static SimpleForecastMatchRateReq toSimple(ForecastMatchRateReq req) {
        SimpleForecastMatchRateReq simpleForecastMatchRateReq = new SimpleForecastMatchRateReq();
        simpleForecastMatchRateReq.setStatTime(req.getStatTime());
        simpleForecastMatchRateReq.setProduct(req.getProduct());
        simpleForecastMatchRateReq.setDemandType(req.getDemandType());
        simpleForecastMatchRateReq.setStartYearMonth(req.getStartYearMonth());
        simpleForecastMatchRateReq.setEndYearMonth(req.getEndYearMonth());
        simpleForecastMatchRateReq.setIsIntervention(req.getIsIntervention());
        simpleForecastMatchRateReq.setDims(req.getDims());
        return simpleForecastMatchRateReq;
    }

    /** 是否运行使用缓存 */
    public boolean enableUseCache(){
        // 是否有过滤条件（非行业的过滤条件）
        boolean hasFilter = ListUtils.isNotEmpty(customerShortName) ||
                ListUtils.isNotEmpty(customhouseTitle) ||
                ListUtils.isNotEmpty(regionName) ||
                ListUtils.isNotEmpty(zoneName) ||
                ListUtils.isNotEmpty(warZoneName) ||
                ListUtils.isNotEmpty(instanceType);
        List<String> enableDims = ListUtils.newArrayList("industryDept"); // 只允许有行业部门的维度
        List<String> dims = getDims();
        boolean hasDim = ListUtils.isNotEmpty(dims) && dims.stream().anyMatch(item-> !enableDims.contains(item));
        // 没过非行业部门滤条件 & 没有不在允许范围内的维度 & 查询计费用量
        return !hasFilter && !hasDim && BooleanUtils.isTrue(getIsQueryBill());
    }
}
