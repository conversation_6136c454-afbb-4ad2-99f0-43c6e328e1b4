package cloud.demand.app.modules.mrpv2.alert.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlterRes<T> {

    public static String SUCCESS = "成功";
    private Boolean isOk;
    private String msg;

    private T data;

    public static <T> AlterRes<T> success(){
        return new AlterRes<T>(true,SUCCESS,null);
    }

    public static <T> AlterRes<T> fail(String msg){
        return new AlterRes<T>(false,msg,null);
    }
}
