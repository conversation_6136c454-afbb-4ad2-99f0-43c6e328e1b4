package cloud.demand.app.modules.mrpv2.alert.web;

import cloud.demand.app.modules.mrpv2.alert.domain.AlterRes;
import cloud.demand.app.modules.mrpv2.alert.entity.AlertHistoryChange;
import cloud.demand.app.modules.mrpv2.alert.entity.RegionAndZoneName;
import cloud.demand.app.modules.mrpv2.alert.service.AlterService;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;

import javax.annotation.Resource;
import java.util.List;

/** 行业报表告警 */
@JsonrpcController("/industry-report-alter")
public class AlertController {

    @Resource
    AlterService alterService;

    @RequestMapping
    public AlterRes<?> checkIndustryDemandPlan(){
        return alterService.checkIndustryDemandPlan();
    }

    /** 监控行业数据看板最近两个版本的历史数据（上个月-上三个月）
     * 返回isOk为true，data没有数据即正常
     * */
    @RequestMapping
    public AlterRes<List<AlertHistoryChange>> checkIndustryHistory(){
        return alterService.checkIndustryHistory();
    }


    /** 监控地域-可用区映射关系是否正常 */
    @RequestMapping
    public AlterRes<List<RegionAndZoneName>> checkReginNameWithZoneName(){
        return alterService.checkReginNameWithZoneName();
    }
}
