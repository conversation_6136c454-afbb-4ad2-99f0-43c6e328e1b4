package cloud.demand.app.modules.mrpv2.enums;

import com.pugwoo.wooutils.collect.ListUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.function.Supplier;

/** 准确率存表需求类型 */
@Getter
@AllArgsConstructor
public enum MrpV2RateTotalDemandTypeEnum {
    ADD_ELASTIC("新增&弹性", () -> ListUtils.newList("新增", "弹性")),

    RETURN("退回", () -> ListUtils.newList("退回")),
    NET("净增", ListUtils::newList),

    ;
    private final String name;
    private final Supplier<List<String>> demandType;
}
