package cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.help.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.plan.StaticModuleDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.entity.yunti.YuntiStategyZoneDO;
import cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.help.PurchaseDictService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PurchaseDictServiceImpl implements PurchaseDictService {

    @Resource
    private DBHelper yuntiDBHelper;

    @Resource
    private DBHelper cdCommonDbHelper;

    @Resource
    private DBHelper ckcldDBHelper;

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    @Override
    public List<YuntiStategyZoneDO> getZoneInfo() {
        return yuntiDBHelper.getAll(YuntiStategyZoneDO.class);
    }

    @HiSpeedCache(expireSecond = 300, cloneReturn = false)
    @Override
    public List<StaticModuleDO> getModuleInfo() {
        return cdCommonDbHelper.getAll(StaticModuleDO.class);
    }

    @Override
    public List<DwdOrderApplyDO> getDwdOrderApply(String versionDate) {
        String sql = ORMUtils.getSql("/sql/mrp/purchase/product_order_num_dwd.sql");
        return ckcldDBHelper.getRaw(DwdOrderApplyDO.class, sql,versionDate);
    }

    @Override
    public List<CloudDemandCsigResourceViewCategoryDO> getProductCategory() {
        return yuntiDBHelper.getAll(CloudDemandCsigResourceViewCategoryDO.class);
    }
}
