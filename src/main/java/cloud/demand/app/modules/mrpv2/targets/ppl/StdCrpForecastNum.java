package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.entity.TailPplForecastDO;
import cloud.demand.app.modules.mrpv2.enums.IsNewCategoryEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

public abstract class StdCrpForecastNum extends TargetTemplate {

    protected final DBHelper ckcldStdCrpDBHelper = DBList.ckcldStdCrpDBHelper;

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测量",
                new RspFieldHeader("", "需求计划", null));
    }

    /** 新版中长尾预测量 */
    public abstract String loadLongTailSql();

    @HiSpeedCache
    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        String yearMonthStart = (String) shares.get("year_month_start");
        String sql = loadSql();
        MyMapUtils.putTargetGenTime(shares, targetDisplay() + "预测量");
        // 头部客户 + 中长尾（新分类无法从底表直接分辨是头部还是中长尾，这里一起查，内存区分）
        List<MrpV2DataDO> result =  ckcldStdCrpDBHelper.getRaw(MrpV2DataDO.class, sql, yearMonthStart);
        CleanService bean = SpringUtil.getBean(CleanService.class);

        for (MrpV2DataDO mrpV2DataDO : result) {
            bean.clean(mrpV2DataDO);
            // 中长尾两种分类都是中长尾的屏蔽客户细节信息
            if (Objects.equals(mrpV2DataDO.getOrderType(),"中长尾")
                    && Objects.equals(mrpV2DataDO.getProjectType(), ProjectTypeEnum.NORMAL_PROJECT.getName())
                    && Objects.equals(mrpV2DataDO.getCustomerType(),Constant.TAIL_CUSTOMER)){
                mrpV2DataDO.setIsNewCategory(IsNewCategoryEnum.YEAS.getCode());
                mrpV2DataDO.setTempZoneName(Constant.EMPTY_VALUE);
                mrpV2DataDO.setUin("0");
                mrpV2DataDO.setCustomerShortName(Constant.EMPTY_VALUE);
                mrpV2DataDO.setCustomerPersonType(Constant.EMPTY_VALUE);
                mrpV2DataDO.setCustomerType(Constant.TAIL_CUSTOMER);
                mrpV2DataDO.setCustomerGroup(Constant.EMPTY_VALUE);
            }
        }

//        // 新版中长尾
//        List<MrpV2DataDO> longTailForecast = ckcldStdCrpDBHelper.getRaw(MrpV2DataDO.class, loadLongTailSql(), yearMonthStart);
//        if (ListUtils.isNotEmpty(longTailForecast)){
//            longTailForecast.forEach(item->{
//                item.setIsNewCategory(IsNewCategoryEnum.YEAS.getCode());
//                item.setTempZoneName(Constant.EMPTY_VALUE);
//            });
//            result.addAll(longTailForecast);
//        }
        // 顺手加载完老版中长尾预测(用来计算准确率的，现在不用了)
        {
//            List<TailPplForecastDO> m = loadTailPplForecastDO(shares, yearMonthStart);
//            if (!CollectionUtils.isEmpty(m)) {
//                result.addAll(TailPplForecastDO.copy(m));
//            }
        }
        return result;
    }

    public abstract List<TailPplForecastDO> loadTailPplForecastDO(Map<String, Object> shares, String yearMonthStart);
    public Map<Integer, List<TailPplForecastDO>> loadTailForecastAndPutShares(Map<String, Object> shares,
            String yearMonthStart) {

        // 2024年10月30日11:16:15 这个sql里的方案已经不存在了，查不到任何数据了，所以这里直接不查了
        // step1：查预中长尾测量
        // String sql = ORMUtils.getSql("/sql/mrp/tail_ppl_forecast.sql");
        List<TailPplForecastDO> result = new ArrayList<>(); // demandDBHelper.getRaw(TailPplForecastDO.class, sql, yearMonthStart);

        Map<Integer, List<TailPplForecastDO>> map =
                ListUtils.toMapList(result, TailPplForecastDO::getPredictIndex, Function.identity());
        shares.put("tail-m0", map.get(1));
        shares.put("tail-m-1", map.get(1));
        shares.put("tail-m-2", map.get(2));
        shares.put("tail-m-3", map.get(3));
        return map;
    }
}
