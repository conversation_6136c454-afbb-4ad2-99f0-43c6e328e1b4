package cloud.demand.app.modules.mrpv2.targets.ppl;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 预测准确率核天 - 最大预测核天
 */
@Slf4j
public class V2AvgForecastCoreByDayNumMax extends TargetTemplate {
    public static String staticTargetName() {
        return "v2_avg_forecast_core_by_day_num_max";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public String targetDisplay() {
        return "532新版-干预后-最大预测核天";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测量-核天",
                new RspFieldHeader("", "需求计划", null));
    }

    @Override
    public String loadSql() {
        return null;
    }

    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }
}
