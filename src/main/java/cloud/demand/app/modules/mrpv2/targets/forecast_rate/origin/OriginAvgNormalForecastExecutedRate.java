package cloud.demand.app.modules.mrpv2.targets.forecast_rate.origin;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.AvgNormalForecastExecutedRate;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

import java.util.List;
import java.util.Map;

public class OriginAvgNormalForecastExecutedRate extends AvgNormalForecastExecutedRate {

    public static String staticTargetName() {
        return "origin_avg_normal_forecast_executed_rate";
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "预测内执行占比(原始)",
                new RspFieldHeader("", "预测指标", null));
    }
    @Override
    public String targetName() {
        return staticTargetName();
    }

}
