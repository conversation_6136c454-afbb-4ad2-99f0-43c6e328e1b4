package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.targets.NotShowIndex;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;

public class LongChangeServeNum extends ChangeServeNum implements NotShowIndex {

    public static String staticTargetName() {
        return "long_change_serve_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }


    @Override
    public TargetTemplate dailyNotDistinct() {
        return new LongDailyNotDistinctChangeServeNum();
    }

    @Override
    public TargetTemplate monthAvgNotDistinct() {
        return new LongMonthAvgNotDistinctChangeServeNum();
    }

    @Override
    public TargetTemplate dailyDistinct() {
        return new LongDailyDistinctChangeServeNum();
    }

    @Override
    public TargetTemplate monthAvgDistinct() {
        return new LongMonthAvgDistinctChangeServeNum();
    }

}
