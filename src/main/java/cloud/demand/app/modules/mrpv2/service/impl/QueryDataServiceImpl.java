package cloud.demand.app.modules.mrpv2.service.impl;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.rrp.ReportConfigGpuTypeDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_report.web.IndustryReportController;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.DynamicProperties;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataDescribeDO;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.mrpv2.entity.OriginIndex;
import cloud.demand.app.modules.mrpv2.enums.*;
import cloud.demand.app.modules.mrpv2.model.*;
import cloud.demand.app.modules.mrpv2.service.DailyMrpV2DataDescribeService;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.service.QueryDataService;
import cloud.demand.app.modules.mrpv2.service.QueryForecastMateRate;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.buy.*;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.ForecastMatchRate;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.ForecastMatchRateCoreByDay;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.MAPE;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.NormalForecastExecutedRate;
import cloud.demand.app.modules.mrpv2.targets.ppl.*;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.StdCrpAvgForecastOriginNum;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.StdCrpBenchForecastOriginNum;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.StdCrpLatestForecastOriginNum;
import cloud.demand.app.modules.mrpv2.targets.ppl.origin.StdCrpV2AvgForecastOriginNum;
import cloud.demand.app.modules.mrpv2.targets.ppl_hedge.*;
import cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.HostDeliveredNum;
import cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.HostDeliveredRealNum;
import cloud.demand.app.modules.mrpv2.targets.purchase_order_detail.ProductOrderNum;
import cloud.demand.app.modules.mrpv2.targets.scaling.*;
import cloud.demand.app.modules.mrpv2.task.process.DailyMrpV2AdsProcess;
import cloud.demand.app.modules.mrpv2.utils.FiberUtils;
import cloud.demand.app.modules.mrpv2.utils.MyTimeUtils;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.mrpv2.utils.RangeUtils;
import cloud.demand.app.modules.mrpv2.web.DailyMrpV2Version;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq.Choose;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq.Page;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq.TimePeriod;
import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;
import cloud.demand.app.modules.mrpv3.execption.MrpV2ToV3Exception;
import cloud.demand.app.modules.mrpv3.service.MrpV3ReportService;
import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthConfigService;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.google.common.base.CaseFormat;
import com.google.common.base.Supplier;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.*;
import com.pugwoo.wooutils.json.JSON;
import erp.base.fiber.support.dispatcher.FiberTaskExecutor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.keyvalue.MultiKey;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.jdbc.UncategorizedSQLException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class QueryDataServiceImpl implements QueryDataService {

    /** 行业数据看板 v3 */
    @Resource
    private MrpV3ReportService mrpV3ReportService;

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldDBHelper;

    /** read only 生产 */
    @Resource
    private DBHelper prodReadOnlyCkStdCrpDBHelper;

    @Resource
    private DailyMrpV2AdsProcess process;

    @Resource
    DictService dictService;

    @Resource
    private PplDictService pplDictService;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private IndustryReportController industryReportController;

    @Resource
    private QueryForecastMateRate queryForecastMateRate;

    /**
     * 获取主力机型主流园区
     */
    @Resource
    private InventoryHealthConfigService configService;

    /**
     * 行业数据看板版本表信息
     */
    @Resource
    private DailyMrpV2DataDescribeService describeService;

    private static final Map<String, RspFieldHeader> selectFields2HeaderMap;
    private static final Map<String, String> selectFields2GroupSqlMap;
    private static Map<String, String> dataOrderByDictMap = null;

    public static Map<String, String> getSelectFields2GroupSqlMap() {
        return new HashMap<>(selectFields2GroupSqlMap);
    }

    /**
     * 异步线程池
     */
    private final FiberTaskExecutor fiberTaskExecutor = FiberTaskExecutor.newDefaultExecutor();

    static {
        List<RspFieldHeader> preHeaders = ListUtils.newArrayList(
                new RspFieldHeader("orderType", "客户分类", null),
                new RspFieldHeader("bizType", "业务分类", null),
                new RspFieldHeader("industryOrProduct", "行业/部门", null),
                new RspFieldHeader("regionType", "境内外", null),
                new RspFieldHeader("regionName", "地域", null),
                new RspFieldHeader("zoneName", "可用区", null),
                new RspFieldHeader("ginsFamily", "实例类型", null),
                new RspFieldHeader("gpuType", "GPU类型", null),
                new RspFieldHeader("gpuCardType", "GPU卡型", null),
                new RspFieldHeader("ginsFamily", "实例类型", null),
                new RspFieldHeader("customerInsideOrOutside", "内外部", null),
                new RspFieldHeader("customerType", "客户类型", null),
                new RspFieldHeader("warZoneName", "战区", null),
                new RspFieldHeader("customerGroup", "客户集团", null),
                new RspFieldHeader("customerShortName", "客户简称", null),
                new RspFieldHeader("demandType", "需求类型", null),
                new RspFieldHeader("isNewCategory", "是否为新中长尾算法", null),
                new RspFieldHeader("crpWarZoneName", "CRP战区", null),
                new RspFieldHeader("unCustomerShortName", "通用客户简称", null),
                new RspFieldHeader("newCustomerType", "新客户分类", null),
                new RspFieldHeader("projectType", "项目类型", null),
                new RspFieldHeader("productClass", "子产品", null),
                new RspFieldHeader("instanceTypeGeneration", "新旧机型", null),
                new RspFieldHeader("incomeSalesDesc", "销售通路", null)
        );
        Map<String, RspFieldHeader> rawSelectFields2HeaderMap = new HashMap<>();
        Map<String, String> rawSelectFields2GroupSqlMap = new HashMap<>();
        for (RspFieldHeader h : preHeaders) {
            rawSelectFields2HeaderMap.put(h.getField(), h);
            rawSelectFields2GroupSqlMap.put(h.getField(),
                    CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, h.getField()));
        }
        selectFields2HeaderMap = Collections.unmodifiableMap(rawSelectFields2HeaderMap);
        selectFields2GroupSqlMap = Collections.unmodifiableMap(rawSelectFields2GroupSqlMap);
    }

    /**
     * 根据环境，返回不同的 DB，测试环境用老的 DB 库，生产用新的
     *
     * @return
     */
    private DBHelper getCKDBHelper() {
//        return ckcldDBHelper;
        return prodReadOnlyCkStdCrpDBHelper;
    }

    private static synchronized Map<String, String> getDataOrderByDictMap() {
        if (dataOrderByDictMap == null) {
            List<TargetTemplate> directTranslate = ListUtils.newArrayList(
                    // 干预后
                    new LatestForecastNum(), new BenchForecastNum(), new AvgForecastNum(),
                    new V2AvgForecastNum(),
                    // 干预前
                    new StdCrpLatestForecastOriginNum(), new StdCrpBenchForecastOriginNum(),
                    new StdCrpAvgForecastOriginNum(),
                    new StdCrpV2AvgForecastOriginNum(),

                    new YunxiaoOrderNum(),

                    new LatestProductStockSupplyNum(), new BenchProductStockSupplyNum(),
                    new AvgProductStockSupplyNum(), new V2AvgProductStockSupplyNum(),

                    new LatestProductStockCannotSupplyNum(), new BenchProductStockCannotSupplyNum(),
                    new AvgProductStockCannotSupplyNum(), new V2AvgProductStockCannotSupplyNum(),

                    new LatestProductHostForecastNum(), new BenchProductHostForecastNum(),
                    new AvgProductHostForecastNum(), new V2AvgProductHostForecastNum(),

                    new ProductOrderNum(), new HostDeliveredNum(), new HostDeliveredRealNum(),

                    new MonthAvgNotDistinctCurBillNum(), new MonthAvgNotDistinctCurServeNum(),
                    new MonthAvgNotDistinctChangeBillNum(), new MonthAvgNotDistinctChangeServeNum(),
                    new MonthAvgDistinctChangeBillNum(), new MonthAvgDistinctChangeServeNum(),

                    new DailyNotDistinctCurBillNum(), new DailyNotDistinctCurServeNum(),
                    new DailyNotDistinctChangeBillNum(), new DailyNotDistinctChangeServeNum(),
                    new DailyDistinctChangeBillNum(), new DailyDistinctChangeServeNum(),

                    // 中长尾数据
                    new LongMonthAvgNotDistinctCurBillNum(), new LongMonthAvgNotDistinctCurServeNum(),
                    new LongMonthAvgNotDistinctChangeBillNum(), new LongMonthAvgNotDistinctChangeServeNum(),
                    new LongMonthAvgNotDistinctCurBillNum(), new LongMonthAvgDistinctChangeServeNum(),

                    new LongDailyNotDistinctCurBillNum(), new LongDailyNotDistinctCurServeNum(),
                    new LongMonthAvgNotDistinctCurBillNum(), new LongDailyNotDistinctChangeServeNum(),
                    new LongDailyDistinctChangeBillNum(), new LongDailyDistinctChangeServeNum(),

                    new SuccessBuyNum(), new FailBuyNum(), new SuccessBuyResourceNum(), new FailBuyResourceNum(),
                    new SuccessBuyResourceRate(), new SuccessBuyRate()
            );
            Map<String, String> rawDataOrderByDictMap = ListUtils.toMap(directTranslate,
                    TargetTemplate::targetName, TargetTemplate::realOrderField);
            dataOrderByDictMap = Collections.unmodifiableMap(rawDataOrderByDictMap);
        }
        return dataOrderByDictMap;
    }

    @Override
    public List<Map<String, Object>> getQueryTargetList() {
        return currentTargetShowList.stream()
                .map(t -> MapUtils.of("field", t.targetName(), "display", t.targetDisplay(),
                        "type", t.header().getDisplay(), "status", ForecastStatus.getName(t instanceof OriginIndex)))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> rspSetSelectFieldsHeader(List<String> selectFields, QueryTableRsp rsp) {
        List<String> newSelectFields = new ArrayList<>();
        for (String inputSelectField : selectFields) {
            if (selectFields2HeaderMap.containsKey(inputSelectField)) {
                rsp.getSelectFieldsHeader().add(selectFields2HeaderMap.get(inputSelectField));
                newSelectFields.add(inputSelectField);
            }
        }
        return newSelectFields;
    }

    private RspFieldHeader toHeader(TargetTemplate t, String time) {
        RspFieldHeader h = new RspFieldHeader();
        h.setDisplay(t.targetDisplay());
        h.setField(String.join("@", t.targetName(), time));
        RspFieldHeader baseH = t.header();
        RspFieldHeader rootH = baseH;
        while (rootH.getParentHeader() != null) {
            rootH = rootH.getParentHeader();
        }
        rootH.setParentHeader(new RspFieldHeader("", time));
        h.setParentHeader(baseH);
        return h;
    }

    @Override
    public Map<String, Object> rspSetTargetsHeader(QueryTableRsp rsp, QueryTableReq req, boolean export) {
        boolean reGroupToOrigin = false;
        List<String> queryTargets = req.getOriginQueryTargets();
        List<TargetTemplate> targetTemplates = new ArrayList<>();
        for (String input : queryTargets) {
            for (TargetTemplate t : QueryDataService.targetShowList) {
                if (t.targetName().equals(input)) {
                    targetTemplates.add(t);
                }
            }
        }
        List<RspFieldHeader> result = rsp.getTargetsHeader();
        Set<String> normalField = new HashSet<>();
        Map<String, DynamicTargetTemplate> specialField = new HashMap<>();
        Map<String, TargetTemplate> distinctScaleNumMap = new HashMap<>();
        List<String> searchTimeList = req.getSearchTimeList();
        for (String time : searchTimeList) {
            for (TargetTemplate t : targetTemplates) {
                if (t instanceof ScaleNum) {
                    ScaleNum s = (ScaleNum) t;
                    if (!req.getBillingScaleDistinct()) {
                        extracted(result, normalField, time,
                                req.getBillingScaleMode().equals("日切片") ? s.dailyNotDistinct()
                                        : s.monthAvgNotDistinct());
                    } else {
                        if (s instanceof CurBillNum || s instanceof CurServeNum) {
                            extracted(result, normalField, time,
                                    req.getBillingScaleMode().equals("日切片") ? s.dailyDistinct()
                                            : s.monthAvgDistinct());
                        } else {
                            TargetTemplate tt = req.getBillingScaleMode().equals("日切片") ? s.dailyDistinct()
                                    : s.monthAvgDistinct();
                            RspFieldHeader h = toHeader(tt, time);
                            result.add(h);

                            distinctScaleNumMap.putIfAbsent(tt.targetName(), tt);
                        }
                    }
                } else if (t instanceof DynamicTargetTemplate) {
                    specialField.putIfAbsent(t.targetName(), (DynamicTargetTemplate) t);
                    if (t instanceof SuccessBuyRate) {
                        RspFieldHeader h = toHeader(t, time);
                        result.add(h);
                        extracted(result, normalField, time,
                                new SuccessBuyNum(), false);
                        extracted(result, normalField, time,
                                new FailBuyNum(), false);
                    } else if (t instanceof SuccessBuyResourceRate) {
                        RspFieldHeader h = toHeader(t, time);
                        result.add(h);
                        extracted(result, normalField, time,
                                new SuccessBuyResourceNum(), false);
                        extracted(result, normalField, time,
                                new FailBuyResourceNum(), false);
                    } else if (t instanceof NormalForecastExecutedRate) {
                        grabForecastNumAndScaleNum(req, result, normalField, distinctScaleNumMap, time, t, export);
                    } else if (t instanceof ForecastMatchRate || t instanceof MAPE) {
                        grabForecastNumAndScaleNum(req, result, normalField, distinctScaleNumMap, time, t, export);
                        // 强制向下group by
                        if (!req.getSelectFields().contains("ginsFamily")) {
                            req.getSelectFields().add("ginsFamily");
                            reGroupToOrigin = true;
                        }
                        if (!req.getSelectFields().contains("regionName")) {
                            req.getSelectFields().add("regionName");
                            reGroupToOrigin = true;
                        }
                        if (!req.getSelectFields().contains("bizType")) {
                            req.getSelectFields().add("bizType");
                            reGroupToOrigin = true;
                        }
                        // 只有中长尾这里才进行处理（不影响头部客户）
                        if (req.needDemandTypeGroup()) {
                            if (!req.getSelectFields().contains("isNewCategory")) {
                                req.getSelectFields().add("isNewCategory");
                                reGroupToOrigin = true;
                            }
                        }
                    } else if (t instanceof ForecastMatchRateCoreByDay) {
                        // 核天准确率的预测数据要取预测核天和执行核天
                        grabForecastCoreByDayNumAndScaleCoreByDayNum(req, result, normalField, distinctScaleNumMap,
                                time, t, export);
                        // 强制向下group by
                        if (!req.getSelectFields().contains("ginsFamily")) {
                            req.getSelectFields().add("ginsFamily");
                            reGroupToOrigin = true;
                        }
                        if (!req.getSelectFields().contains("regionName")) {
                            req.getSelectFields().add("regionName");
                            reGroupToOrigin = true;
                        }
                        if (!req.getSelectFields().contains("bizType")) {
                            req.getSelectFields().add("bizType");
                            reGroupToOrigin = true;
                        }
                    }
                } else if (t.complexNode() != null) {
                    // 导出改造，这里只展示勾选的版本
                    if (ListUtils.isNotEmpty(req.getVersion())) {
                        List<String> version = req.getVersion();
                        List<String> collect = Arrays.stream(DailyMrpVersion.values()).map(DailyMrpVersion::getDesc)
                                .collect(Collectors.toList());
                        for (TargetTemplate c : t.complexNode()) {
                            if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                                extracted(result, normalField, time, c);
                            }
                        }
                    } else {
                        for (TargetTemplate c : t.complexNode()) {
                            extracted(result, normalField, time, c);
                        }
                    }
                } else {
                    extracted(result, normalField, time, t);
                }
            }
        }
        // 如果需要新增弹性预测存量计费（这里在targetsHeader中剔除掉）
        if (req.needForecastStockNum()) {
            String longDisplay = new LongForecastNum().targetDisplay();
            String longBill = LongChangeBillNum.staticTargetName();
            // 重新设置头部
            result = result.stream().filter(item -> {
                if (item.getParentHeader() != null) {
                    return !(Objects.equals(item.getParentHeader().getDisplay(), longDisplay));
                }
                return !(Objects.equals(item.getDisplay(), longDisplay));
            }).collect(Collectors.toList());

            result = result.stream().filter(item -> !item.getField().contains(longBill)).collect(Collectors.toList());

            rsp.setTargetsHeader(result);
        }

        Map<String, Object> m = new HashMap<>();
        m.put("normalField", normalField);
        m.put("specialField", specialField);
        m.put("reGroupToOrigin", reGroupToOrigin);
        m.put("distinctScaleNumMap", distinctScaleNumMap);
        return m;
    }

    private void grabForecastNumAndScaleNum(QueryTableReq req, List<RspFieldHeader> result, Set<String> normalField,
            Map<String, TargetTemplate> distinctScaleNumMap, String time, TargetTemplate t, boolean export) {
        // // 导出改造，这里只展示勾选的版本
        if (ListUtils.isNotEmpty(req.getVersion())) {
            List<String> version = req.getVersion();
            List<String> collect = Arrays.stream(DailyMrpVersion.values()).map(DailyMrpVersion::getDesc)
                    .collect(Collectors.toList());
            for (TargetTemplate c : t.complexNode()) {
                if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                    RspFieldHeader h = toHeader(c, time);
                    result.add(h);
                }
            }
            // 区分原始和干预后
            for (TargetTemplate c : OriginIndexEnum.getForecastNum(req.getForecastStatus()).complexNode()) {
                if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                    extracted(result, normalField, time, c, false);
                }
            }
        } else {
            for (TargetTemplate c : t.complexNode()) {
                RspFieldHeader h = toHeader(c, time);
                result.add(h);
            }
            for (TargetTemplate c : OriginIndexEnum.getForecastNum(req.getForecastStatus()).complexNode()) {
                extracted(result, normalField, time, c, false);
            }
        }

        TargetTemplate billNum;
        TargetTemplate serveNum;
        if (!req.getBillingScaleDistinct()) {
            if (req.getBillingScaleMode().equals("日切片")) {
                billNum = new ChangeBillNum().dailyNotDistinct();
                serveNum = new ChangeServeNum().dailyNotDistinct();
            } else {
                billNum = new ChangeBillNum().monthAvgNotDistinct();
                serveNum = new ChangeServeNum().monthAvgNotDistinct();
            }

            extracted(result, normalField, time, billNum, false);
            extracted(result, normalField, time, serveNum, false);
        } else {
            if (req.getBillingScaleMode().equals("日切片")) {
                billNum = new ChangeBillNum().dailyDistinct();
                serveNum = new ChangeServeNum().dailyDistinct();
            } else {
                billNum = new ChangeBillNum().monthAvgDistinct();
                serveNum = new ChangeServeNum().monthAvgDistinct();
            }

            distinctScaleNumMap.putIfAbsent(billNum.targetName(), billNum);
            distinctScaleNumMap.putIfAbsent(serveNum.targetName(), serveNum);
        }

        // 需要补充
        if (req.needLongScaleNum()) {
            TargetTemplate longBillNum;
            TargetTemplate longServeNum;
            if (!req.getBillingScaleDistinct()) {
                if (req.getBillingScaleMode().equals("日切片")) {
                    longBillNum = new LongChangeBillNum().dailyNotDistinct();
                    longServeNum = new LongChangeServeNum().dailyNotDistinct();
                } else {
                    longBillNum = new LongChangeBillNum().monthAvgNotDistinct();
                    longServeNum = new LongChangeServeNum().monthAvgNotDistinct();
                }

                extracted(result, normalField, time, longBillNum, false);
                extracted(result, normalField, time, longServeNum, false);
            } else {
                if (req.getBillingScaleMode().equals("日切片")) {
                    longBillNum = new LongChangeBillNum().dailyDistinct();
                    longServeNum = new LongChangeServeNum().dailyDistinct();
                } else {
                    longBillNum = new LongChangeBillNum().monthAvgDistinct();
                    longServeNum = new LongChangeServeNum().monthAvgDistinct();
                }

                distinctScaleNumMap.putIfAbsent(longBillNum.targetName(), billNum);
                distinctScaleNumMap.putIfAbsent(longServeNum.targetName(), serveNum);
            }
        }
    }

    /**
     * 设置预测核天和执行核天的 Template
     *
     * @param req
     * @param result
     * @param normalField
     * @param distinctScaleNumMap
     * @param time
     * @param t
     */
    private void grabForecastCoreByDayNumAndScaleCoreByDayNum(QueryTableReq req, List<RspFieldHeader> result,
            Set<String> normalField,
            Map<String, TargetTemplate> distinctScaleNumMap, String time, TargetTemplate t, boolean export) {
        if (ListUtils.isNotEmpty(req.getVersion())) {
            List<String> version = req.getVersion();
            List<String> collect = Arrays.stream(DailyMrpVersion.values()).map(DailyMrpVersion::getDesc)
                    .collect(Collectors.toList());
            for (TargetTemplate c : t.complexNode()) {
                if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                    RspFieldHeader h = toHeader(c, time);
                    result.add(h);
                }
            }

            for (TargetTemplate c : OriginIndexEnum.getV2ForecastMatchRateCoreByDay(req.getForecastStatus())
                    .complexNode()) {
                if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                    extracted(result, normalField, time, c, false);
                }
            }
            for (TargetTemplate c : OriginIndexEnum.getLatestForecastMatchRateCoreByDay(req.getForecastStatus())
                    .complexNode()) {
                if (!collect.contains(c.targetDisplay()) || version.contains(c.targetDisplay())) {
                    extracted(result, normalField, time, c, false);
                }
            }
        } else {
            for (TargetTemplate c : t.complexNode()) {
                RspFieldHeader h = toHeader(c, time);
                result.add(h);
            }
            for (TargetTemplate c : OriginIndexEnum.getV2ForecastMatchRateCoreByDay(req.getForecastStatus())
                    .complexNode()) {
                extracted(result, normalField, time, c, false);
            }
            for (TargetTemplate c : OriginIndexEnum.getLatestForecastMatchRateCoreByDay(req.getForecastStatus())
                    .complexNode()) {
                extracted(result, normalField, time, c, false);
            }
        }

        TargetTemplate billNum;
        TargetTemplate serveNum;
        if (!req.getBillingScaleDistinct()) {
            billNum = new BillCoreByDayNum().monthNotDistinct();
            serveNum = new ServeCoreByDayNum().monthNotDistinct();

            extracted(result, normalField, time, billNum, false);
            extracted(result, normalField, time, serveNum, false);
        } else {
            billNum = new BillCoreByDayNum().monthDistinct();
            serveNum = new ServeCoreByDayNum().monthDistinct();

            distinctScaleNumMap.putIfAbsent(billNum.targetName(), billNum);
            distinctScaleNumMap.putIfAbsent(serveNum.targetName(), serveNum);
        }
    }

    private void extracted(List<RspFieldHeader> result, Set<String> normalField, String time, TargetTemplate t,
            boolean addToResult) {
        RspFieldHeader h = toHeader(t, time);
        if (addToResult) {
            result.add(h);
        }
        if (!time.equals("总计")) {
            normalField.add(h.getField());
        }
    }

    private void extracted(List<RspFieldHeader> result, Set<String> normalField, String time, TargetTemplate t) {
        extracted(result, normalField, time, t, true);
    }

    @Override
    public List<String> tidyTimeRange(TimePeriod timePeriod) {
        if (timePeriod == null) {
            return null;
        }
        String start = timePeriod.getStart();
        String end = timePeriod.getEnd();
        if (StringUtils.isBlank(start) || StringUtils.isBlank(end)) {
            return null;
        }
        DailyMrpV2DataDescribeDO describeDO = SpringUtil.getBean(QueryDataService.class).getDescribeDO();
        List<String> searchTimeList;
        if (start.contains("Q")) {
            searchTimeList = MyTimeUtils.listQ(start, end);
            searchTimeList = searchTimeList.stream()
                    .filter(t -> describeDO.getYearQList().contains(t))
                    .collect(Collectors.toList());
        } else {
            searchTimeList = DateUtils.listYearMonth(start, end).stream()
                    .map(YearMonth::toDateStr).collect(Collectors.toList());
            searchTimeList = searchTimeList.stream()
                    .filter(t -> describeDO.getYearMonthList().contains(t))
                    .collect(Collectors.toList());
        }
        return searchTimeList;
    }

    private LocalDate dataDate(QueryTableReq req) {
        LocalDate d = req.getStatTime();
        if (d == null) {
            d = SpringUtil.getBean(QueryDataService.class).getLatestLocalDate();
        }
        req.setStatTime(d);
        return d;
    }

    /**
     * 采购机型
     */
    public void purchaseInstanceType(QueryTableReq content) {
        // 可采购机型限制
        if (BooleanUtils.isTrue(content.getIsPurchaseInstanceType()) || BooleanUtils.isTrue(content.isPurchase())) {
            // 获取采购机型
            List<String> purchaseInstanceType = SpringUtil.getBean(MrpV2DictService.class).getPurchaseInstanceType();
            // 如果有才限制，避免没有配置采购机型，页面没有数据
            if (ListUtils.isNotEmpty(purchaseInstanceType)) {
                List<String> newInstanceType;
                if (ListUtils.isNotEmpty(content.getInstanceTypes())) {
                    newInstanceType = (List<String>) org.apache.commons.collections4.CollectionUtils.intersection(
                            content.getInstanceTypes(), purchaseInstanceType);
                    if (ListUtils.isEmpty(newInstanceType)){
                        newInstanceType = ListUtils.newArrayList("没数据");
                    }
                } else {
                    newInstanceType = new ArrayList<>(purchaseInstanceType);
                }
                content.setInstanceTypes(newInstanceType);
            }
        }else if (BooleanUtils.isFalse(content.isPurchase())){
            // 非采购机型，用全量机型减去采购机型
            MrpV2DictService bean = SpringUtil.getBean(MrpV2DictService.class);
            List<String> purchaseInstanceType = bean.getPurchaseInstanceType();
            List<String> allInstanceType = bean.getAllInstanceType();
            purchaseInstanceType.forEach(allInstanceType::remove);
            if (ListUtils.isNotEmpty(allInstanceType)) {
                List<String> newInstanceType;
                if (ListUtils.isNotEmpty(content.getInstanceTypes())) {
                    newInstanceType = (List<String>) org.apache.commons.collections4.CollectionUtils.intersection(
                            content.getInstanceTypes(), allInstanceType);
                    if (ListUtils.isEmpty(newInstanceType)){
                        newInstanceType = ListUtils.newArrayList("没数据");
                    }
                } else {
                    newInstanceType = new ArrayList<>(allInstanceType);
                }
                content.setInstanceTypes(newInstanceType);
            }
        }
    }

    /**
     * 机型代次（新旧机型）
     */
    public void generationInstanceType(QueryTableReq content) {
        // 新机型限制
        if (BooleanUtils.isTrue(content.getIsNewInstanceType()) || BooleanUtils.isTrue(content.isNew())) {
            // 获取新旧机型
            List<String> generationInstanceType = SpringUtil.getBean(MrpV2DictService.class)
                    .getNewGenerationInstanceType();
            // 如果有才限制，避免没有配置采购机型，页面没有数据
            if (ListUtils.isNotEmpty(generationInstanceType)) {
                List<String> newInstanceType;
                if (ListUtils.isNotEmpty(content.getInstanceTypes())) {
                    newInstanceType = (List<String>) org.apache.commons.collections4.CollectionUtils.intersection(
                            content.getInstanceTypes(), generationInstanceType);
                    if (ListUtils.isEmpty(newInstanceType)){
                        newInstanceType = ListUtils.newArrayList("没数据");
                    }
                } else {
                    newInstanceType = new ArrayList<>(generationInstanceType);
                }
                content.setInstanceTypes(newInstanceType);
            }
        }else if (BooleanUtils.isFalse(content.isNew())){
            // 非新机型，用全量机型减去新机型
            MrpV2DictService bean = SpringUtil.getBean(MrpV2DictService.class);
            List<String> newGenerationInstanceType = bean.getNewGenerationInstanceType();
            List<String> allInstanceType = bean.getAllInstanceType();
            newGenerationInstanceType.forEach(allInstanceType::remove);
            // 如果有才限制，避免没有配置采购机型，页面没有数据
            if (ListUtils.isNotEmpty(allInstanceType)) {
                List<String> newInstanceType;
                if (ListUtils.isNotEmpty(content.getInstanceTypes())) {
                    newInstanceType = (List<String>) org.apache.commons.collections4.CollectionUtils.intersection(
                            content.getInstanceTypes(), allInstanceType);
                    if (ListUtils.isEmpty(newInstanceType)){
                        newInstanceType = ListUtils.newArrayList("没数据");
                    }
                } else {
                    newInstanceType = new ArrayList<>(allInstanceType);
                }
                content.setInstanceTypes(newInstanceType);
            }
        }
    }

    public void mainInstanceFamilyAndZone(QueryTableReq content) {
        boolean mainInstanceFamilyAndZone = BooleanUtils.isTrue(content.getMainInstanceFamilyAndZone());
        boolean mainZone = BooleanUtils.isTrue(content.getMainZone()) || BooleanUtils.isTrue(content.isMainZone());
        boolean mainInstanceFamily = BooleanUtils.isTrue(content.getMainInstanceFamily()) || BooleanUtils.isTrue(content.isMainInstance());
        if (mainInstanceFamilyAndZone || mainZone || mainInstanceFamily) {
            // 获取切片时间前一天
            String date = ObjectUtils.defaultIfNull(content.getStatTime(), LocalDate.now()).plusDays(-1)
                    .format(DateTimeFormatter.ISO_LOCAL_DATE);
            // 准备主力园区
            Map<String, List<String>> map = ObjectUtils.defaultIfNull(configService.getZoneConfigMap(date),
                    new HashMap<>());
            Set<String> zoneNames = new HashSet<>(
                    map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));

            // 准备主力机型
            map = ObjectUtils.defaultIfNull(configService.getInstanceTypeConfigMap(date), new HashMap<>());
            Set<String> instanceFamilies = new HashSet<>(
                    map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));

            if (mainInstanceFamilyAndZone || mainZone){
                List<String> inputZoneNames = content.getZoneNames();
                if (!CollectionUtils.isEmpty(inputZoneNames)) {
                    // 取交集
                    inputZoneNames.removeIf(d -> !zoneNames.contains(d));
                    if (CollectionUtils.isEmpty(inputZoneNames)) {
                        inputZoneNames.add("无数据");
                    }
                } else {
                    content.setZoneNames(new ArrayList<>(zoneNames));
                }
            }
            if (mainInstanceFamilyAndZone || mainInstanceFamily){
                List<String> inputInstanceTypes = content.getInstanceTypes();
                if (!CollectionUtils.isEmpty(inputInstanceTypes)) {
                    // 取交集
                    inputInstanceTypes.removeIf(d -> !instanceFamilies.contains(d));
                    if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                        inputInstanceTypes.add("无数据");
                    }
                } else {
                    content.setInstanceTypes(new ArrayList<>(instanceFamilies));
                }
            }
        }
        // 非主力机型，非主力园区
        if (BooleanUtils.isFalse(content.isMainZone()) || BooleanUtils.isFalse(content.isMainInstance())){
            // 获取切片时间前一天
            String date = ObjectUtils.defaultIfNull(content.getStatTime(), LocalDate.now()).plusDays(-1)
                    .format(DateTimeFormatter.ISO_LOCAL_DATE);

            if (BooleanUtils.isFalse(content.isMainZone())){
                // 准备主力园区
                Map<String, List<String>> map = ObjectUtils.defaultIfNull(configService.getZoneConfigMap(date),
                        new HashMap<>());
                Set<String> zoneNames = new HashSet<>(
                        map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));
                content.setIgnoreZoneNames(new ArrayList<>(zoneNames));
            }

            if (BooleanUtils.isFalse(content.isMainInstance())){
                // 准备主力机型
                Map<String, List<String>> map = ObjectUtils.defaultIfNull(configService.getInstanceTypeConfigMap(date), new HashMap<>());
                Set<String> instanceFamilies = new HashSet<>(
                        map.getOrDefault(InventoryHealthZoneType.PRINCIPAL.getCode(), ListUtils.newList()));

                content.setIgnoreInstanceTypes(new ArrayList<>(instanceFamilies));
            }
        }
    }

    private void commonInstanceType(QueryTableReq content) {
        Boolean commonInstanceType = content.getCommonInstanceType();
        List<String> instanceTypes = content.getInstanceTypes();
        List<String> newInstanceTypes;
        if (!CollectionUtils.isEmpty(content.getInstanceTypes())) {
            newInstanceTypes = new ArrayList<>(content.getInstanceTypes());
        } else {
            newInstanceTypes = new ArrayList<>();
        }
        // 选了通用机型
        if (commonInstanceType) {
            // 加载配置表
            Map<String, String> revertMap = (Map<String, String>) buildCommonInstanceConfigMap().get("revert");
            if (!CollectionUtils.isEmpty(instanceTypes)) {
                // 筛选了实例类型
                Iterator<String> iter = instanceTypes.iterator();
                while (iter.hasNext()) {
                    String originInstanceType = iter.next();
                    // 如果筛选了通用后的机型，那筛选列表要加回通用前的机型
                    String beforeCommon = revertMap.get(originInstanceType);
                    if (StringUtils.isNotBlank(beforeCommon)) {
                        String[] ins = beforeCommon.split("[,|，]");
                        for (String i : ins) {
                            i = org.springframework.util.StringUtils.trimWhitespace(i);
                            if (StringUtils.isNotBlank(i)) {
                                newInstanceTypes.add(i);
                            }
                        }
                    }
                }
            }
            String chooseGinsFamily = content.getChoose().getGinsFamily();
            if (StringUtils.isNotBlank(chooseGinsFamily)) {
                // choose 机型非空
                String beforeCommon = revertMap.get(chooseGinsFamily);
                if (StringUtils.isNotBlank(beforeCommon)) {
                    content.getChoose().setGinsFamilies(new ArrayList<>());
                    String[] ins = beforeCommon.split("[,|，]");
                    for (String i : ins) {
                        i = org.springframework.util.StringUtils.trimWhitespace(i);
                        if (StringUtils.isNotBlank(i)) {
                            content.getChoose().getGinsFamilies().add(i);
                        }
                    }
                }
            }
        }
        content.setInstanceTypes(newInstanceTypes);
    }

    private void renderInstanceType(QueryTableReq content) {
        Boolean isRendering = content.getIsRendering();
        if (isRendering != null) {
            // 只要渲染机型或非渲染机型
            List<String> inputInstanceTypes = content.getInstanceTypes();
            List<String> allGinsFamily = industryReportController.queryAllGinsFamily();
            Set<String> targetGinsFamily;
            if (isRendering) {
                targetGinsFamily = allGinsFamily.stream()
                        .filter(e -> e.startsWith("RS") || e.startsWith("RM"))
                        .collect(Collectors.toSet());
            } else {
                targetGinsFamily = allGinsFamily.stream()
                        .filter(e -> !e.startsWith("RS") && !e.startsWith("RM"))
                        .collect(Collectors.toSet());
            }
            if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                // 如果原来没有筛选实例机型，那直接把（非）渲染机型列表写入到实例机型
                inputInstanceTypes = new ArrayList<>(targetGinsFamily);
                content.setInstanceTypes(inputInstanceTypes);
            } else {
                // 如果原来有筛选实例机型，那就取交集
                inputInstanceTypes.removeIf(e -> !targetGinsFamily.contains(e));
                if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                    // 如果取完交集后，筛选的实例机型是空的，那就放入无数据
                    inputInstanceTypes.add("无数据");
                }
            }
        }
    }

    private void fillFilter(WhereSQL whereSQL, QueryTableReq req) {
        fillFilter(whereSQL, req, true);
    }

    private void fillFilter(WhereSQL whereSQL, QueryTableReq req, boolean demandTypeFilter) {
        // 筛选项
        req.fillWhereSQL(whereSQL, demandTypeFilter);
        // 日期
        whereSQL.and("stat_time = ?", req.getStatTime());
        // 产品
        whereSQL.and("data_product = ?", req.getProduct());
        // 展开
        req.getChoose().fillWhereSQL(req ,whereSQL, demandTypeFilter);
        // 权限
        authFilter(whereSQL);
    }

    private List<String> getStaticFieldFromReq(QueryTableReq req) {
        List<String> staticField = new ArrayList<>();
        boolean isOldOrderType = BooleanUtils.isNotTrue(req.getIsNewOrderType());
        for (String field : req.getSelectFields()) {
            String column = selectFields2GroupSqlMap.get(field);
            // 客户分类有新老两种，为了支持业务查询，这里特殊处理下
            if (isOldOrderType && column.equals("order_type")) {
                column = "old_order_type";
            }
            staticField.add(column + " " + field);
        }
        return staticField;
    }

    private List<String> getOriginStaticFieldFromReq(QueryTableReq req) {
        List<String> staticField = new ArrayList<>();
        boolean isOldOrderType = BooleanUtils.isNotTrue(req.getIsNewOrderType());
        for (String field : req.getOriginSelectFields()) {
            String column = selectFields2GroupSqlMap.get(field);
            // 客户分类有新老两种，为了支持业务查询，这里特殊处理下
            if (isOldOrderType && column.equals("order_type")) {
                column = "old_order_type";
            }
            staticField.add(column + " " + field);
        }
        return staticField;
    }


    private Map<String, Object> loadResultDistinct(QueryTableReq req, Map<String, Object> fieldMap,
            Boolean reGroupToOrigin,
            boolean export) {
//        StopWatch stopWatch = new StopWatch("去重查询");
        String[] groupBySql = new String[req.getSelectFields().size()];
        int i = 0;
        for (String groupBys : req.getSelectFields()) {
            groupBySql[i++] = groupBys;
        }
        Map<String, Object> commonInstanceConfigMapWrap =
                req.getCommonInstanceType() ? buildCommonInstanceConfigMap() : null;
        Map<String, String> commonInstanceConfigMap = commonInstanceConfigMapWrap != null
                ? (Map<String, String>) commonInstanceConfigMapWrap.get("result") : null;

        List<String> staticField = getStaticFieldFromReq(req);
        List<String> groupStaticField = getOriginStaticFieldFromReq(req);
        // 先构建静态字段的查询
        List<String> newStaticField = new ArrayList<>(staticField);
        List<String> groupNewStaticField = new ArrayList<>(groupStaticField);
        List<String> finalNewStaticField = new ArrayList<>(req.getSelectFields());
        List<String> finalGroupNewStaticField = new ArrayList<>(req.getOriginSelectFields());

        List<String> commonNewStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(newStaticField, commonInstanceConfigMap) : null;
        List<String> commonGroupNewStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(groupNewStaticField, commonInstanceConfigMap) : null;
        List<String> commonFinalNewStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(finalNewStaticField, commonInstanceConfigMap) : null;
        List<String> commonFinalGroupNewStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(finalGroupNewStaticField, commonInstanceConfigMap) : null;

        Set<String> normalField = (Set<String>) fieldMap.get("normalField");
        Map<String, String> totalSqlMap = new HashMap<>();
        Map<String, TargetTemplate> distinctScaleNumMap = (Map<String, TargetTemplate>) fieldMap.get(
                "distinctScaleNumMap");
        StringBuilder havingSqlSB = new StringBuilder(" ");
        List<String> searchTimeList = req.getSearchTimeList();
        Object[] params = null;
        String finalSql = "";
        String finalGroupSQL = "";
        if (!CollectionUtils.isEmpty(normalField)) {
            for (String normal : normalField) {
                // 添加静态字段的sum
                newStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                groupNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                finalNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                finalGroupNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");

                if (req.getCommonInstanceType()) {
                    commonNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                    commonGroupNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                    commonFinalNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                    commonFinalGroupNewStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                }

                // 添加having
                havingSqlSB.append("`");
                havingSqlSB.append(normal);
                havingSqlSB.append("` is not null or ");
                // 添加最终字段的总计
                String[] s = normal.split("@");
                String origin = totalSqlMap.get(s[0]);
                if (origin == null) {
                    totalSqlMap.put(s[0], "coalesce(`" + normal + "`, 0)");
                } else {
                    origin = origin + " + coalesce(`" + normal + "`, 0)";
                    totalSqlMap.put(s[0], origin);
                }
            }
            String havingSQL = havingSqlSB.substring(0, havingSqlSB.lastIndexOf("or"));

            for (String distinct : distinctScaleNumMap.keySet()) {
                // 添加动态字段的null
                for (int ii = 1; ii < searchTimeList.size(); ii++) {
                    newStaticField.add("null `" + distinct + "@" + searchTimeList.get(ii) + "`");
                    groupNewStaticField.add("null `" + distinct + "@" + searchTimeList.get(ii) + "`");

                    if (req.getCommonInstanceType()) {
                        commonNewStaticField.add("null `" + distinct + "@" + searchTimeList.get(ii) + "`");
                        commonGroupNewStaticField.add("null `" + distinct + "@" + searchTimeList.get(ii) + "`");
                    }
                }
            }

            String staticSqlField = String.join(",", commonInstanceConfigMap != null ?
                    commonNewStaticField : newStaticField);
            String groupStaticSqlField = String.join(",", commonInstanceConfigMap != null ?
                    commonGroupNewStaticField : groupNewStaticField);
            WhereSQL staticWhereSQL = new WhereSQL();
            fillFilter(staticWhereSQL, req);

            staticWhereSQL.having(havingSQL);

            params = staticWhereSQL.getParams();

            // 【daily_mrp_v2_data】表改为动态表，根据【daily_mrp_v2_data_describe】的配置【is_latest】为【1】的为最新版本表
            String tableName = describeService.getTableNameWithStatTime(req.getStatTime());

            staticWhereSQL.addGroupBy(String.join(",", req.getOriginSelectFields()));
            finalGroupSQL =
                    "select " + groupStaticSqlField + " from cloud_demand." + tableName + " " + staticWhereSQL.getSQL();
            staticWhereSQL.resetGroupBy();
            staticWhereSQL.addGroupBy(groupBySql);
            finalSql = "select " + staticSqlField + " from cloud_demand." + tableName + " " + staticWhereSQL.getSQL();
        }
        // 判断需求类型
        Set<String> tmpDemandTypes;
        if (CollectionUtils.isEmpty(req.getDemandTypes())) {
            tmpDemandTypes = new HashSet<>();
        } else {
            tmpDemandTypes = new HashSet<>(req.getDemandTypes());
        }
        // 如果choose里有需求类型的筛选，取交集
        String chooseDemandType = req.getChoose().getDemandType();
        if (!StringUtils.isBlank(chooseDemandType)) {
            if (CollectionUtils.isEmpty(tmpDemandTypes)) {
                tmpDemandTypes.add(chooseDemandType);
            } else {
                tmpDemandTypes.removeIf(e -> !chooseDemandType.equals(e));
                if (CollectionUtils.isEmpty(tmpDemandTypes)) {
                    tmpDemandTypes.add("无数据");
                }
            }
        }

        boolean hasNew;
        boolean hasRet;
        if (CollectionUtils.isEmpty(tmpDemandTypes)) {
            // 需求类型筛选为空,认为都要有
            hasNew = true;
            hasRet = true;
        } else {
            hasNew = tmpDemandTypes.contains("新增") || tmpDemandTypes.contains("弹性");
            hasRet = tmpDemandTypes.contains("退回");
        }
        if (hasNew || hasRet) {
            // 只有有新增或退回才去union all，否则不union all
            // 构建动态字段的查询
            List<String> dyStaticField = new ArrayList<>(staticField);
            dyStaticField = commonInstanceConfigMap != null
                    ? getCommonStaticFieldFromList(dyStaticField, commonInstanceConfigMap) : dyStaticField;
            List<String> outerDyStaticField = new ArrayList<>(req.getSelectFields());
            List<String> outerGroupDyStaticField = new ArrayList<>(req.getOriginSelectFields());
            for (String normal : normalField) {
                // 添加静态字段的null
                outerDyStaticField.add("null `" + normal + "`");
                outerGroupDyStaticField.add("null `" + normal + "`");
            }
            StringBuilder dyHavingSqlSB = new StringBuilder(" ");
            for (String distinct : distinctScaleNumMap.keySet()) {
                // 添加动态字段的sum
                for (int ii = 1; ii < searchTimeList.size(); ii++) {
                    String f = "`" + distinct + "@" + searchTimeList.get(ii) + "`";
                    String df;
                    if (distinct.startsWith("month_avg")) {
                        df = f.replace("month_avg", "month_avg_not");
                    } else {
                        df = f.replace("daily", "daily_not");
                    }
                    if (distinct.startsWith("month_distinct")) {
                        df = f.replace("month_distinct", "month_not_distinct");
                    }
                    String sumF = "sum(" + df + ")";
                    if (hasNew && hasRet) {
                        dyStaticField.add(sumF + " " + f);
                    } else if (hasNew) {
                        dyStaticField.add("if(" + sumF + " > 0, " + sumF + ", null) " + f);
                    } else {
                        dyStaticField.add("if(" + sumF + " < 0, " + sumF + ", null) " + f);
                    }
                    outerDyStaticField.add("sum(" + f + ") " + f);
                    outerGroupDyStaticField.add("sum(" + f + ") " + f);
                    finalNewStaticField.add("sum(" + f + ") " + f);
                    finalGroupNewStaticField.add("sum(" + f + ") " + f);

                    if (req.getCommonInstanceType()) {
                        commonFinalNewStaticField.add("sum(" + f + ") " + f);
                        commonFinalGroupNewStaticField.add("sum(" + f + ") " + f);
                    }
                    // 添加having
                    dyHavingSqlSB.append(f);
                    dyHavingSqlSB.append(" is not null or ");

                    // 添加最终字段的总计
                    String origin = totalSqlMap.get(distinct);
                    if (origin == null) {
                        totalSqlMap.put(distinct, "coalesce(" + f + ", 0)");
                    } else {
                        origin = origin + " + coalesce(" + f + ", 0)";
                        totalSqlMap.put(distinct, origin);
                    }
                }
            }
            String dyHavingSQL = dyHavingSqlSB.substring(0, dyHavingSqlSB.lastIndexOf("or"));

            String dySqlField = String.join(",", dyStaticField);
            WhereSQL dyWhereSQL = new WhereSQL();
            fillFilter(dyWhereSQL, req, false);
            String[] extendGroupBySql = new String[groupBySql.length + 2];
            if (groupBySql.length > 0) {
                System.arraycopy(groupBySql, 0, extendGroupBySql, 0, groupBySql.length);
            }
            String ginsFamilyText = "gins_family";
//            if (req.getCommonInstanceType()) {
//                ginsFamilyText = selectCommonInstanceType(ginsFamilyText, commonInstanceConfigMap).replace("ginsFamily", "");
//            }
            extendGroupBySql[extendGroupBySql.length - 1] = ginsFamilyText;
            //extendGroupBySql[extendGroupBySql.length - 1] = "gins_family";
            extendGroupBySql[extendGroupBySql.length - 2] = "temp_zone_name";
            dyWhereSQL.addGroupBy(extendGroupBySql);
            dyWhereSQL.having(dyHavingSQL);

            // 【daily_mrp_v2_data】表改为动态表，根据【daily_mrp_v2_data_describe】的配置【is_latest】为【1】的为最新版本表
            String tableName = describeService.getTableNameWithStatTime(req.getStatTime());

            String dySql = "select " + dySqlField + " from cloud_demand." + tableName + " " + dyWhereSQL.getSQL();
            String outerDySqlField = String.join(",", outerDyStaticField);
            String outerGroupDySqlField = String.join(",", outerGroupDyStaticField);
            dySql = "select " + outerDySqlField + " from ( " + dySql + " ) tt";
            if (groupBySql.length > 0) {
                dySql = dySql + " group by " + String.join(",", groupBySql) + " ";
            }
            String dyGroupSql = "select " + outerGroupDySqlField + " from ( " + dySql + " ) tt";
            if (!CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                dyGroupSql += " group by " + String.join(",", req.getOriginSelectFields()) + " ";
            }
            if (StringUtils.isBlank(finalSql)) {
                finalSql = dySql;
            } else {
                finalSql += " union all " + dySql;
            }
            if (StringUtils.isBlank(finalGroupSQL)) {
                finalGroupSQL = dyGroupSql;
            } else {
                finalGroupSQL += " union all " + dyGroupSql;
            }

            Object[] p2 = dyWhereSQL.getParams();
            Object[] np;
            if (params == null) {
                np = new Object[p2.length];
                System.arraycopy(p2, 0, np, 0, p2.length);
            } else {
                np = new Object[params.length + p2.length];
                System.arraycopy(params, 0, np, 0, params.length);
                System.arraycopy(p2, 0, np, params.length, p2.length);
            }
            params = np;
        }
        if (StringUtils.isBlank(finalSql)) {
            return MapUtils.of("result", ListUtils.newArrayList(), "count", 0L);
        }

        // 外层查询
        // 总计计算

        StringBuilder fieldSqlSB = new StringBuilder(String.join(",", commonInstanceConfigMap != null ?
                commonFinalNewStaticField : finalNewStaticField));
        StringBuilder fieldGroupSqlSB = new StringBuilder(String.join(",", commonInstanceConfigMap != null ?
                commonFinalGroupNewStaticField : finalGroupNewStaticField));

        for (Entry<String, String> entry : totalSqlMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            fieldSqlSB.append(", ");
            fieldSqlSB.append(value);
            fieldSqlSB.append(" `");
            fieldSqlSB.append(key);
            fieldSqlSB.append("@总计`");

            fieldGroupSqlSB.append(", ");
            fieldGroupSqlSB.append(value);
            fieldGroupSqlSB.append(" `");
            fieldGroupSqlSB.append(key);
            fieldGroupSqlSB.append("@总计`");
        }
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.addGroupBy(String.join(",", req.getOriginSelectFields()));
        List<String> sqlHavingFields = commonInstanceConfigMap != null ?
                commonFinalGroupNewStaticField : finalGroupNewStaticField;
        String countHavingSql = "";

        if (sqlHavingFields.size() > 0) {
            // 解决当查到的 data 长度是 0 时，count 是 1 的 bug。
            // 因为 select null, sum from (空的子查询) 会返回一行空数据，通过加上 having 去掉这一行所有字段都是 null 的数据
            countHavingSql += " having " + String.join(" or ", sqlHavingFields.stream().map(o -> {

                String[] items = o.split(" ");

                if (items.length > 1) {
                    return items[1].trim() + " is not null";
                }

                return o + " is not null";
            }).collect(Collectors.toList()));
        }
//        stopWatch.start("查询总数");
        // count
        Long count = 0L;
        String countSql = "select count(1) from (select " + fieldGroupSqlSB + " from ( " + finalGroupSQL + " ) t "
                + whereSQL.getSQL() + countHavingSql + ") ttt";
        countSql = countSql.replaceAll("where|WHERE", "prewhere");

        String finalCountSql = countSql;
        Object[] finalParams = params;
        Supplier<Object> countFun = ()-> getCKDBHelper().getRawOne(Long.class, finalCountSql, finalParams);
//        stopWatch.stop();

        String finalGroupSQL1 = finalGroupSQL;
        String finalSql1 = finalSql;
        Supplier<Object> resultFun = ()-> {
            Object[] finalParams1 = finalParams;
            String finalSql2 = finalSql1;
            if (reGroupToOrigin && !CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                WhereSQL copyWhereSql = whereSQL.copy();
                // 先按原字段查出分页的信息字段有哪些
                Page page = req.getPage();
                if (page != null) {
                    copyWhereSql.limit(page.toOffset(), page.getPageSize());
                }

                String orderBySql;
                if (CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                    orderBySql = "";
                } else {
                    orderBySql = String.join(" desc,", req.getOriginSelectFields()) + " desc";
                }
                if (StringUtils.isNotBlank(req.getDataSortColName()) && !export) {
                    String tmp = req.getDataSortColName() + " " + req.getDataSortRule();
                    orderBySql = orderBySql.equals("") ? tmp : tmp + ", " + orderBySql;
                }
                copyWhereSql.addOrderBy(orderBySql);
                String sql = "select " + fieldGroupSqlSB + " from ( " + finalGroupSQL1 + " ) t " + copyWhereSql.getSQL();
                sql = sql.replaceAll("where|WHERE", "prewhere");
//                stopWatch.start("强制group by查原主key集合");
                // 加载原数据
                Collection<Map> result = getCKDBHelper().getRaw(Map.class, sql, finalParams1);
//                stopWatch.stop();
                List<Object[]> tParams = new ArrayList<>();
                for (Map m : result) {
                    Object[] oa = new Object[req.getOriginSelectFields().size()];
                    int t = 0;
                    for (String s : req.getOriginSelectFields()) {
                        oa[t++] = m.get(s);
                    }
                    tParams.add(oa);
                }
                if (!CollectionUtils.isEmpty(tParams)) {
                    whereSQL.and("(" + String.join(",", req.getOriginSelectFields())
                            .replace("gins_family", "ginsFamily") + ") in (?)");
                    Object[] newParams = new Object[finalParams1.length + 1];
                    System.arraycopy(finalParams1, 0, newParams, 0, finalParams1.length);
                    newParams[newParams.length - 1] = tParams;
                    finalParams1 = newParams;
                } else {
                    // 原信息都查不到数据，直接失效
                    whereSQL.and("1 = 0");
                }
            }

            whereSQL.resetGroupBy();
            whereSQL.addGroupBy(groupBySql);
            if (!reGroupToOrigin) {
                Page page = req.getPage();
                if (page != null) {
                    // 分页
                    whereSQL.limit(page.toOffset(), page.getPageSize());
                }

                String orderBySql;
                if (CollectionUtils.isEmpty(req.getSelectFields())) {
                    orderBySql = "";
                } else {
                    orderBySql = String.join(" desc,", req.getSelectFields()) + " desc";
                }
                if (StringUtils.isNotBlank(req.getDataSortColName()) && !export) {
                    String tmp = req.getDataSortColName() + " " + req.getDataSortRule();
                    orderBySql = orderBySql.equals("") ? tmp : tmp + ", " + orderBySql;
                }
                whereSQL.addOrderBy(orderBySql);
            }
            finalSql2 = finalSql2.replaceAll("where|WHERE", "prewhere");
            finalSql2 = "select " + fieldSqlSB + " from ( " + finalSql2 + " ) t " + whereSQL.getSQL();
            // 加载数据
//            stopWatch.start("最后结果查询");
            Collection<Map> result = getCKDBHelper().getRaw(Map.class, finalSql2, finalParams1);
//            stopWatch.stop();
            if (commonInstanceConfigMap != null) {
                bindCommonInstanceType(result, (Map<String, String>) commonInstanceConfigMapWrap.get("revert"));
            }
            return result;
        };
//        log.info(stopWatch.prettyPrint());
        List<Object> objects = PageQueryUtils.waitAll(120, resultFun, countFun);

        return MapUtils.of("result", objects.get(0), "count", objects.get(1));
    }

    private String ckIfNullPlus(String value1, String value2) {
        return " if(isNull(" + value1 + ") AND isNull(" + value2 + "), NULL, "
                + "coalesce(" + value1 + ", 0) + coalesce(" + value2 + ", 0)) ";
    }

    private String selectCommonInstanceType(String col, Map<String, String> commonInstanceConfig) {
        // 这个入参 map，key 和 value 不会相等，已经过滤了
        // key 是要通用化的，value 是通用化结果后的
        if (col.contains("gins_family") && !CollectionUtils.isEmpty(commonInstanceConfig)) {
            StringBuilder sb = new StringBuilder(" case gins_family ");
            for (Entry<String, String> entry : commonInstanceConfig.entrySet()) {
                sb.append("when '");
                sb.append(entry.getKey());
                sb.append("' then '");
                sb.append(entry.getValue());
                sb.append("' ");
            }
            sb.append("else gins_family end ginsFamily ");
            return sb.toString();
        } else {
            return col;
        }
    }

    private Map<String, Object> buildCommonInstanceConfigMap() {
        List<Mrpv2CommonInstanceTypeConfigDO> configDOS = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast = 1");
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> result = new HashMap<>();
        for (Mrpv2CommonInstanceTypeConfigDO configDO : configDOS) {
            try {
                String commonInstanceType = org.springframework.util.StringUtils.trimWhitespace(
                        configDO.getCommonInstanceType());
                String[] instanceTypes = configDO.getInstanceTypes().split("[,|，]");
                for (String ins : instanceTypes) {
                    String newIns = org.springframework.util.StringUtils.trimWhitespace(ins);
                    if (!newIns.equals(commonInstanceType)) {
                        result.put(newIns, commonInstanceType);
                    }
                }
            } catch (Exception e) {
                log.warn("getCommonStaticFieldFromList ex:", e);
            }
        }
        resultMap.put("result", result);
        resultMap.put("revert", ListUtils.toMap(configDOS,
                Mrpv2CommonInstanceTypeConfigDO::getCommonInstanceType,
                Mrpv2CommonInstanceTypeConfigDO::getInstanceTypes));
        return resultMap;
    }

    private List<String> getCommonStaticFieldFromList(List<String> staticField,
            Map<String, String> commonInstanceConfigMap) {
        return staticField.stream()
                .map(s -> selectCommonInstanceType(s, commonInstanceConfigMap))
                .collect(Collectors.toList());
    }


    private void bindCommonInstanceType(Collection<Map> data, Map<String, String> revertMap) {
        for (Map m : data) {
            // 这个 ginsFamily 已经是通用化后的
            Object ginsFamilyObj = m.get("ginsFamily");
            if (ginsFamilyObj instanceof String) {
                String ginsFamily = (String) ginsFamilyObj;
                String raw = revertMap.getOrDefault(ginsFamily, ginsFamily);
                m.put("commonInstanceType", raw);
            }
        }
    }

    private Map<String, Object> loadResultNotDistinct(QueryTableReq req, Map<String, Object> fieldMap,
            Boolean reGroupToOrigin,
            boolean export) {
        List<String> staticField = getStaticFieldFromReq(req);
        Map<String, Object> commonInstanceConfigMapWrap =
                req.getCommonInstanceType() ? buildCommonInstanceConfigMap() : null;
        Map<String, String> commonInstanceConfigMap = commonInstanceConfigMapWrap != null
                ? (Map<String, String>) commonInstanceConfigMapWrap.get("result") : null;
        List<String> commonStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(staticField, commonInstanceConfigMap) : null;
        List<String> groupStaticField = getOriginStaticFieldFromReq(req);
        List<String> commonGroupStaticField = commonInstanceConfigMap != null
                ? getCommonStaticFieldFromList(groupStaticField, commonInstanceConfigMap) : null;
        Map<String, String> totalSqlMap = new HashMap<>();
        StringBuilder havingSqlSB = new StringBuilder(" ");
        Set<String> normalField = (Set<String>) fieldMap.get("normalField");
        for (String normal : normalField) {
            String[] s = normal.split("@");
            String origin = totalSqlMap.get(s[0]);
            if (origin == null) {
                totalSqlMap.put(s[0], "coalesce(`" + normal + "`, 0)");
            } else {
                origin = origin + " + coalesce(`" + normal + "`, 0)";
                totalSqlMap.put(s[0], origin);
            }

            staticField.add("sum(`" + normal + "`) `" + normal + "`");
            groupStaticField.add("sum(`" + normal + "`) `" + normal + "`");
            if (req.getCommonInstanceType()) {
                commonStaticField.add("sum(`" + normal + "`) `" + normal + "`");
                commonGroupStaticField.add("sum(`" + normal + "`) `" + normal + "`");
            }
            havingSqlSB.append("`");
            havingSqlSB.append(normal);
            havingSqlSB.append("` is not null or ");
        }

        String havingSQL = havingSqlSB.substring(0, havingSqlSB.lastIndexOf("or"));
        // 先看哪些静态字段需要直接在宽表里加载出来
        StringBuilder fieldSqlSB = new StringBuilder(String.join(",",
                commonInstanceConfigMap != null ? commonStaticField : staticField));
        StringBuilder groupFieldSqlSB = new StringBuilder(String.join(",",
                commonInstanceConfigMap != null ? commonGroupStaticField : groupStaticField));

        // 总计查询
        for (Entry<String, String> entry : totalSqlMap.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            fieldSqlSB.append(", ");
            fieldSqlSB.append(value);
            fieldSqlSB.append(" `");
            fieldSqlSB.append(key);
            fieldSqlSB.append("@总计`");

            groupFieldSqlSB.append(", ");
            groupFieldSqlSB.append(value);
            groupFieldSqlSB.append(" `");
            groupFieldSqlSB.append(key);
            groupFieldSqlSB.append("@总计`");
        }

        WhereSQL whereSQL = new WhereSQL();
        // 填充筛选项
        fillFilter(whereSQL, req);
        // group by 哪些字段
        String[] groupBySql = new String[req.getSelectFields().size()];
        int i = 0;
        for (String groupBys : req.getSelectFields()) {
            //groupBySql[i++] = selectFields2GroupSqlMap.get(groupBys);
            groupBySql[i++] = groupBys;
        }
        whereSQL.addGroupBy(String.join(",", req.getOriginSelectFields()));
        whereSQL.having(havingSQL);
        // count
        Long count = 0L;

        // 【daily_mrp_v2_data】表改为动态表，根据【daily_mrp_v2_data_describe】的配置【is_latest】为【1】的为最新版本表
        String tableName = describeService.getTableNameWithStatTime(req.getStatTime());

        String countSql = "select count(1) from (select " + groupFieldSqlSB + " from cloud_demand." + tableName + " "
                + whereSQL.getSQL() + ")";
        countSql = countSql.replaceAll("where|WHERE", "prewhere");
        String finalCountSql = countSql;
        Supplier<Object> countFun = ()-> getCKDBHelper().getRawOne(Long.class, finalCountSql, whereSQL.getParams());

        Supplier<Object> resultFun = ()->{
            if (reGroupToOrigin && !CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                WhereSQL copyWhereSql = whereSQL.copy();
                // 先按原字段查出分页的信息字段有哪些
                Page page = req.getPage();
                if (page != null) {
                    copyWhereSql.limit(page.toOffset(), page.getPageSize());
                }

                String orderBySql;
                if (CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                    orderBySql = "";
                } else {
                    orderBySql = String.join(" desc,", req.getOriginSelectFields()) + " desc";
                }
                if (StringUtils.isNotBlank(req.getDataSortColName())) {
                    String tmp = req.getDataSortColName() + " " + req.getDataSortRule();
                    orderBySql = orderBySql.equals("") ? tmp : tmp + ", " + orderBySql;
                }
                copyWhereSql.addOrderBy(orderBySql);
                String sql = "select " + groupFieldSqlSB + " from cloud_demand." + tableName + " "
                        + copyWhereSql.getSQL();
                sql = sql.replaceAll("where|WHERE", "prewhere");
                // 加载原数据
                Collection<Map> result = getCKDBHelper().getRaw(Map.class, sql, copyWhereSql.getParams());
                boolean isOldOrderType = BooleanUtils.isNotTrue(req.getIsNewOrderType());
                List<String> underlineOrigin = req.getOriginSelectFields().stream()
                        .map(o -> {
                            if ("ginsFamily".equals(o)) {
                                return o;
                            } else {
                                String column = selectFields2GroupSqlMap.get(o);
                                // 兼容老客户分类
                                if (isOldOrderType && Objects.equals("order_type",column)){
                                    column = "old_order_type";
                                }
                                return column;
                            }
                        })
                        .collect(Collectors.toList());
                List<Object[]> params = new ArrayList<>();
                for (Map m : result) {
                    Object[] oa = new Object[req.getOriginSelectFields().size()];
                    int t = 0;
                    for (String s : req.getOriginSelectFields()) {
                        oa[t++] = m.get(s);
                    }
                    params.add(oa);
                }
                if (CollectionUtils.isEmpty(params)) {
                    // 失效，下层查不到东西，所以重聚也查不到东西
                    whereSQL.and("1 = 0");
                } else {
                    whereSQL.and("(" + String.join(",", underlineOrigin) + ") in (?)", params);
                }
            }
            whereSQL.resetGroupBy();

            // 分页
            whereSQL.addGroupBy(groupBySql);
            if (!reGroupToOrigin) {
                Page page = req.getPage();
                if (page != null) {
                    whereSQL.limit(page.toOffset(), page.getPageSize());
                }

                String orderBySql;
                if (CollectionUtils.isEmpty(req.getSelectFields())) {
                    orderBySql = "";
                } else {
                    orderBySql = String.join(" desc,", req.getSelectFields()) + " desc";
                }
                if (StringUtils.isNotBlank(req.getDataSortColName()) && !export) {
                    String tmp = req.getDataSortColName() + " " + req.getDataSortRule();
                    orderBySql = orderBySql.equals("") ? tmp : tmp + ", " + orderBySql;
                }
                whereSQL.addOrderBy(orderBySql);
            }
            String sql = "select " + fieldSqlSB + " from cloud_demand." + tableName + " "
                    + whereSQL.getSQL();
            sql = sql.replaceAll("where|WHERE", "prewhere");
            // 加载数据
            Collection<Map> result = getCKDBHelper().getRaw(Map.class, sql, whereSQL.getParams());
            if (commonInstanceConfigMap != null) {
                bindCommonInstanceType(result, (Map<String, String>) commonInstanceConfigMapWrap.get("revert"));
            }
            return result;
        };
        List<Object> objects = PageQueryUtils.waitAll(120, resultFun, countFun);
        return MapUtils.of("result", objects.get(0), "count", objects.get(1));
    }

    private Map<String, Object> loadData(QueryTableReq req,
            Map<String, Object> fieldMap, boolean export) {
        Map<String, DynamicTargetTemplate> specialField = (Map<String, DynamicTargetTemplate>) fieldMap.get(
                "specialField");
        Map<String, TargetTemplate> distinctScaleNumMap = (Map<String, TargetTemplate>) fieldMap.get(
                "distinctScaleNumMap");
        Boolean reGroupToOrigin = (Boolean) fieldMap.get("reGroupToOrigin");
        Map<String, Object> resultMap;
        if (CollectionUtils.isEmpty(distinctScaleNumMap)) {
            resultMap = loadResultNotDistinct(req, fieldMap, reGroupToOrigin, export);
        } else {
            resultMap = loadResultDistinct(req, fieldMap, reGroupToOrigin, export);
        }
        Collection<Map> result = (Collection<Map>) resultMap.get("result");
        Long count = (Long) resultMap.get("count");

        if (!CollectionUtils.isEmpty(specialField) && !CollectionUtils.isEmpty(result)) {
            for (DynamicTargetTemplate dt : specialField.values()) {
                // 如果有向下强制聚合的情况的，那部分指标不参与计算，后面才计算
                if (reGroupToOrigin && dt instanceof NormalForecastExecutedRate) {
                    continue;
                }
                dt.dynamicFieldToData(result, req, reGroupToOrigin);
            }
        }

//        List<Map> swapList = new ArrayList<>(result);
//
//        // 指标交换（个别指标数据需要替换处理）
//        if (!swapList.isEmpty() && req.needLongScaleNum()){
//            swapIndex(swapList);
//            result = swapList;
//        }

        // 是否还原成原来的透视维度
        if (reGroupToOrigin && !CollectionUtils.isEmpty(result)) {
            Map<String, Map> groups = new HashMap<>();
            List<String> originSelectFields = req.getOriginSelectFields();

            // 需要忽略的数字字段
            List<String> ignoreFields = Arrays.asList("isNewCategory");

            for (Map m : result) {
                StringBuilder keySB = new StringBuilder();
                for (String os : originSelectFields) {
                    keySB.append(m.get(os));
                    keySB.append("@");
                }
                String key;
                if (CollectionUtils.isEmpty(originSelectFields)) {
                    key = "";
                } else {
                    key = keySB.substring(0, keySB.length() - 1);
                }
                Map origin = groups.get(key);
                if (origin == null) {
                    origin = new HashMap(m);
                    groups.put(key, origin);
                } else {

                    for (Object objKey : m.keySet()) {
                        Object other = m.get(objKey);
                        if (other == null || ignoreFields.contains(objKey)) {
                            continue;
                        }

                        Object value = origin.get(objKey);
                        if (other instanceof BigDecimal) {
                            BigDecimal v = value == null ? BigDecimal.ZERO : (BigDecimal) value;

                            origin.put(objKey, v.add((BigDecimal) other));
                        } else if (other instanceof Number) {
                            double v = value == null ? 0 : ((Number) value).doubleValue();
                            origin.put(objKey, v + (Double) other);
                        }
                    }
                }
            }
            for (Entry<String, Map> e : groups.entrySet()) {
                Map origin = e.getValue();
                for (Object objKey : origin.keySet()) {
                    if (((String) objKey).contains(ForecastMatchRate.staticTargetName())) {
                        // 向上聚合的率转字符串
                        origin.put(objKey,
                                String.format("%.2f%%", Constant.b100.multiply((BigDecimal) origin.get(objKey))));
                    } else if (((String) objKey).contains(MAPE.staticTargetName())) {
                        BigDecimal value = (BigDecimal) origin.get(objKey);
                        // 如果超过值域，要调整
                        if (value.compareTo(BigDecimal.ZERO) < 0) {
                            value = BigDecimal.ZERO;
                        } else if (value.compareTo(Constant.b3) > 0) {
                            value = Constant.b3;
                        }
                        // 向上聚合的率转字符串
                        origin.put(objKey,
                                String.format("%.2f%%", Constant.b100.multiply(value)));
                    }
                }
            }
            result = groups.values();
            if (!CollectionUtils.isEmpty(specialField)
                    && specialField.containsKey(NormalForecastExecutedRate.staticTargetName())) {
                specialField.get(NormalForecastExecutedRate.staticTargetName())
                        .dynamicFieldToData(result, req, reGroupToOrigin);
            }
            List<Map> resultList = new ArrayList<>(result);

            if (StringUtils.isBlank(req.getDataSortColName())) {
                if (!CollectionUtils.isEmpty(req.getOriginSelectFields())) {
                    SortingField<Map, String> sf = new SortingField<Map, String>(SortingOrderEnum.DESC) {
                        @Override
                        public String apply(Map input) {
                            Object obj = input.get(req.getOriginSelectFields().get(0));
                            return obj == null ? null : obj.toString();
                        }
                    };
                    if (req.getOriginSelectFields().size() > 1) {
                        SortingField<Map, String>[] sfs = new SortingField[req.getOriginSelectFields().size() - 1];
                        for (int i = 1; i < req.getOriginSelectFields().size(); i++) {
                            int finalI = i;
                            sfs[i - 1] = new SortingField<Map, String>(SortingOrderEnum.DESC) {
                                @Override
                                public String apply(Map input) {
                                    Object obj = input.get(req.getOriginSelectFields().get(finalI));
                                    return obj == null ? null : obj.toString();
                                }
                            };
                        }
                        SortingUtils.sort(resultList, sf, sfs);
                    } else {
                        SortingUtils.sort(resultList, sf);
                    }
                }
            } else {

                SortingField<Map, StringOrBigDecimal> sf = new SortingField<Map, StringOrBigDecimal>
                        (req.getDataSortRule().equals("asc") ? SortingOrderEnum.ASC : SortingOrderEnum.DESC) {
                    @Override
                    public StringOrBigDecimal apply(Map input) {
                        Object obj = input.get(req.getDataSortColName().replace("`", ""));
                        return obj == null ? null : new StringOrBigDecimal(obj);
                    }
                };

                SortingField<Map, String>[] sfs = new SortingField[req.getOriginSelectFields().size()];
                for (int i = 0; i < req.getOriginSelectFields().size(); i++) {
                    int finalI = i;
                    sfs[i] = new SortingField<Map, String>(SortingOrderEnum.DESC) {
                        @Override
                        public String apply(Map input) {
                            Object obj = input.get(req.getOriginSelectFields().get(finalI));
                            return obj == null ? null : obj.toString();
                        }
                    };
                }
                SortingUtils.sort(resultList, sf, sfs);
            }

            result = resultList;
        }

        // 脏数据清理0为null(已经由前端处理，这里不再清理)
//        ErrorDataUtils.transData2Null(result);
        return MapUtils.of("result", result, "count", count);
    }

    /**
     * 指标数据交换
     */
    private void swapIndex(List<Map> resultList) {
        if (resultList.isEmpty()) {
            return;
        }
        // 需要替换的指标（中长尾的规模数据）
        List<String> headerSaleIndex = ListUtils.of(CurBillNum.staticTargetName(),
                CurServeNum.staticTargetName(),
                ChangeBillNum.staticTargetName(),
                ChangeServeNum.staticTargetName());
        // 中长尾的规模指标
        List<String> longSaleIndex = ListUtils.of(LongCurBillNum.staticTargetName(),
                LongCurServeNum.staticTargetName(),
                LongChangeBillNum.staticTargetName(),
                LongChangeServeNum.staticTargetName());

        Map<String, String> replaceMap = new HashMap<>();
        // 抽取一个初始化待替换的指标
        for (Object o : resultList.get(0).entrySet()) {
            Entry<String, Object> entry = (Entry<String, Object>) o;
            if (entry.getValue() == null || entry.getValue() instanceof Double
                    || entry.getValue() instanceof BigDecimal) {
                String key = entry.getKey();
                if (replaceMap.containsKey(key)) {
                    replaceMap.put(replaceMap.get(key), key);
                    continue;
                }
                for (int i = 0; i < longSaleIndex.size(); i++) {
                    String longIndex = longSaleIndex.get(i);
                    String headerIndex = headerSaleIndex.get(i);
                    // 先处理中长尾，中长尾的指标包含头部指标（多了个long_前缀）
                    if (key.contains(longIndex)) {
                        String replace = key.replace(longIndex, headerIndex);
                        replaceMap.put(replace, key);
                        break;
                    }
                    if (key.contains(headerIndex)) {
                        String replace = key.replace(headerIndex, longIndex);
                        replaceMap.put(replace, key);
                        break;
                    }
                }
            }
        }
        if (replaceMap.isEmpty()) {
            return;
        }
        for (Map<String, Object> objectMap : resultList) {
            if (!Objects.equals(objectMap.get("orderType"), "中长尾")) {
                continue;
            }
            Map<String, Object> swapValueMap = new HashMap<>();
            // 待替换数据存map
            replaceMap.forEach((key, value) -> swapValueMap.put(value, objectMap.get(key)));
            // 最终替换指标数据
            objectMap.putAll(swapValueMap);
        }

    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 300)
    @Override
    public DailyMrpV2DataDescribeDO getDescribeDO() {
        return describeService.getLatest();
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 300)
    @Override
    public LocalDate getLatestLocalDate() {
        return describeService.getLatest().getLatestSuccessStatTime();
    }

    @Override
    public List<String> queryGpuCardType() {
        Map<String, ReportConfigGpuTypeDO> gpuTypeDOMap = dictService.loadGpuCardConfig();
        return gpuTypeDOMap.values().stream().map(ReportConfigGpuTypeDO::getGpuCardType).distinct()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    @Override
    public List<String> queryGpuType() {
        Map<String, ReportConfigGpuTypeDO> gpuTypeDOMap = dictService.loadGpuCardConfig();
        return gpuTypeDOMap.values().stream().map(ReportConfigGpuTypeDO::getGpuType).distinct()
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
    }

    /**
     * 版本列表缓存，本地5分钟，10分钟拉一次存redis，总有效时间为1小时
     */
    @HiSpeedCache(expireSecond = 600, continueFetchSecond = 3600, useRedis = true, cacheRedisDataMillisecond = 300
            * 1000)
    @Override
    public List<DailyMrpV2Version> listVersion() {
        String startVersion = DynamicProperties.startVersion();

        List<String> data = prodReadOnlyCkStdCrpDBHelper.getRaw(String.class,
                "SELECT DISTINCT stat_time FROM std_crp.dws_daily_mrp_v3_data_df\n"
                        + "ORDER BY stat_time");
        data.sort((o1, o2) -> StringUtils.compare(o2, o1));

        // step1：限制起始版本号
        if (ListUtils.isNotEmpty(data)) {
            data = data.stream().filter(item -> item.compareTo(startVersion) > 0).collect(Collectors.toList());
        }

        // 转换为对象
        List<DailyMrpV2Version> ret = new ArrayList<>();
        if (ListUtils.isNotEmpty(data)) {
            for (String item : data) {
                ret.add(new DailyMrpV2Version(item));
            }
        }

        // step3：版本号黑名单限制
        if (ListUtils.isNotEmpty(ret)) {
            List<String> blackVersions = DynamicProperties.blackVersions();
            if (ListUtils.isNotEmpty(blackVersions)) {
                ret = ret.stream().filter(item -> !blackVersions.contains(item.getStatTime()))
                        .collect(Collectors.toList());
            }
        }

        return ret;

    }

    @Override
    public QueryTableRsp pageQueryTable(QueryTableReq req, boolean export) {
        Page page = req.getPage();
        if (page == null) {
            page = new Page(1, 1000);
            req.setPage(page);
        }

        QueryTableReq tempReq = JSON.clone(req);

        QueryTableRsp rsp = queryTable(req, export);

        // 结果为空或者已经全部查询完毕
        if (rsp == null || rsp.getCount() <= page.getPageSize()) {
            return rsp;
        }

        // 异步查询
        Collection<Map> extData = PageQueryUtils.page(
                (p) -> {
                    QueryTableReq clone = JSON.clone(tempReq);
                    clone.setPage(p);
                    QueryTableRsp temp = queryTable(clone, export);
                    return temp == null ? null : temp.getData();
                },
                page.getPageNo() + 1,
                page.getPageSize(),
                rsp.getCount(),
                10,
                TimeUnit.MINUTES
        );
        rsp.getData().addAll(extData);
        return rsp;
    }

    private String translateOrderByField(String withTimeCol) {
        if (withTimeCol.contains("@")) {
            String[] colArr = withTimeCol.split("@");
            String v = getDataOrderByDictMap().get(colArr[0]);
            return v == null ? null : v + "@" + colArr[1];
        }
        return null;
    }

    private void defaultSortDataColName(QueryTableReq req, QueryTableRsp rsp) {
        if (StringUtils.isNotBlank(req.getDataSortColName())) {
            String translate = translateOrderByField(req.getDataSortColName());
            if (translate == null) {
                req.setDataSortColName(null);
            } else {
                // 检查排序列在不在头部
                boolean has = false;
                for (RspFieldHeader h : rsp.getTargetsHeader()) {
                    if (h.getField().contains(translate)) {
                        has = true;
                        break;
                    }
                }
                if (has) {
                    req.setDataSortColName("`" + translate + "`");
                } else {
                    req.setDataSortColName(null);
                }
            }
            return;
        }
        // 先看有没有计费存量列
        for (RspFieldHeader h : rsp.getTargetsHeader()) {
            if (h.getField().contains(CurBillNum.staticTargetName())) {
                String translate = translateOrderByField(h.getField());
                if (translate != null) {
                    req.setDataSortColName("`" + translate + "`");
                    return;
                }
            }
        }
        // 再看第一个列
        for (RspFieldHeader h : rsp.getTargetsHeader()) {
            String translate = translateOrderByField(h.getField());
            if (translate != null) {
                req.setDataSortColName("`" + translate + "`");
                return;
            }
        }
    }

    @Override
    public QueryTableRsp queryTable(QueryTableReq req, boolean export) {
        // 有新行业数据看板接口，这里根据 abTest 配置以及是否能够完成转换（v2 转 v3）来决定是否用 v3 看板的接口
        {
            boolean allowABTest = cloud.demand.app.modules.mrpv3.DynamicProperties.allowABTest();
            // 使用 v3 看板的兼容接口，暂不未适配导出
            if (allowABTest && !export) {
                try {
                    return mrpV3ReportService.queryIndustryMRPReportV2(req);
                }catch (Exception e){
                    // 直接抛出异常，老行业数据看板已弃用，这里不忽略异常
                    throw e;
                }
            }
        }


        // 总计加权处理
        QueryTableReq.Choose choose = req.getChoose();
        List<String> selectFields = req.getSelectFields();
        List<String> nextSelectFields = req.getNextSelectFields();
        // 当前层级为总计,如果有下一级指标，并且下一级指标只有一个，并且是projectType
        if (BooleanUtils.isTrue(req.getIsNewOrderType()) &&
                ListUtils.isEmpty(selectFields) &&
                ListUtils.isNotEmpty(nextSelectFields) && nextSelectFields.size() == 1 && nextSelectFields.contains("projectType")){
            // 查两次，然后 merge
            QueryTableReq clone = JSON.clone(req);
            clone.setSelectFields(ListUtils.newArrayList("projectType","bizType")); // 项目类型 + 业务类型（内部计费，外部服务）
            clone.setNextSelectFields(null);
            List<QueryTableRsp> rspList = PageQueryUtils.waitAll(30,
                    ()-> doQueryTable(req, export),
                    ()-> doQueryTable(clone, export));
            QueryTableRsp rsp = rspList.get(0);
            QueryTableRsp mergeRsp = rspList.get(1);
            return mergeProjectType(rsp,mergeRsp,req);
        } else if (BooleanUtils.isTrue(req.getIsNewOrderType()) &&
                ListUtils.isNotEmpty(selectFields) && selectFields.size() == 1 && selectFields.contains("orderType") &&
                choose != null && Objects.equals(choose.getOrderType(),"中长尾")){
            // 新客户分类查询中长尾
            // 查两次，然后 merge
            QueryTableReq clone = JSON.clone(req);
            clone.setSelectFields(ListUtils.newArrayList("orderType","projectType","bizType")); // 业务类型 + 项目类型
            List<QueryTableRsp> rspList = PageQueryUtils.waitAll(30,
                    () -> doQueryTable(req, export),
                    () -> doQueryTable(clone, export));
            QueryTableRsp rsp = rspList.get(0);
            QueryTableRsp mergeRsp = rspList.get(1);
            return mergeProjectType(rsp,mergeRsp,req);
        }else {
            return doQueryTable(req, export);
        }

    }

    /**
     * 加权计算准确率
     * @param rsp 原始结果
     * @param mergeRsp 加了项目类型和业务类型的结果
     * @param req 原始请求
     * @return
     */
    private QueryTableRsp mergeProjectType(QueryTableRsp rsp,QueryTableRsp mergeRsp,QueryTableReq req) {
        // 没有数据，不用处理
        if (rsp.getCount() == 0){
            return rsp;
        }
        Collection<Map> mergeRspData = mergeRsp.getData();

        Map<String,Double> scaleMap = new HashMap<>(); // key：年月，value：总规模（外部计费+内部服务）
        Map<String,Map<String,Double>> rateMap = new HashMap<>(); // key：年月，key：准确率指标，value：加权的准确率

        // 指标前缀
        Boolean billingScaleDistinct = req.getBillingScaleDistinct(); // 是否去重
        String billingScaleMode = req.getBillingScaleMode(); // 月均还是月切
        String pre = Objects.equals(billingScaleMode,"月均切片")? "month_avg": "daily";
        pre += BooleanUtils.isTrue(billingScaleDistinct)?"_distinct":"_not_distinct";
        pre += "_change";
        String finPre = pre;

        // step1：汇总计算
        mergeRspData.forEach(item->{
            if (item == null){
                return;
            }
            String projectType = (String)item.get("projectType");
            // 如果项目类型是不参与，则不处理
            if (Objects.equals(projectType,ProjectTypeEnum.EMPTY_PROJECT.getName())){
                return;
            }
            String bizType = (String)item.get("bizType");
            String tempPre = finPre + (Objects.equals(bizType,"外部行业")? "_bill":"_serve");
            Map<String,Double> itemScaleMap = new HashMap<>(); // 规模 map
            Map<String,Map<String,Double>> itemRateMap = new HashMap<>(); // 准确率 map
            for (Object o : item.entrySet()) {
                Map.Entry entry = (Map.Entry) o;
                String key = (String) entry.getKey();
                Object value = entry.getValue();
                // 如果key是 projectType 或者 bizType，则不处理
                if (key.equals("projectType") || key.equals("bizType")){
                    continue;
                }
                // 执行量统计
                if (key.contains("@") && value instanceof Double){
                    if (key.contains(tempPre)){
                        String ym = key.split("@")[1];
                        Double aDouble = (Double) value;
                        itemScaleMap.put(ym,Math.abs(aDouble));
                    }
                }else if (key.contains("@") && value instanceof String){
                    // 准确率统计
                    String obj = (String) value;
                    String ym = key.split("@")[1];
                    Double aDouble = Double.parseDouble(obj.replace("%",""));
                    itemRateMap.computeIfAbsent(ym,k->new HashMap<>()).put(key,aDouble);
                }
            }
            // 准确率 * 执行量 = 加权准确率
            itemRateMap.forEach((ym, mp) -> {
                Double aDouble = itemScaleMap.getOrDefault(ym,0.0);
                mp.forEach((k,v)-> {
                    Double weightRate = v * aDouble;
                    Map<String, Double> doubleMap = rateMap.computeIfAbsent(ym, m -> new HashMap<>());
                    Double rate = doubleMap.get(k);
                    if (rate == null){
                        rate = weightRate;
                    }else {
                        rate += weightRate;
                    }
                    doubleMap.put(k,rate);
                }); // 准确率加权执行量
            });
            // 总执行量（用来做分母）：准确率 = 加权准确率/总执行量
            itemScaleMap.forEach((ym,v)->{
                Double aDouble = scaleMap.get(ym);
                if (aDouble == null){
                    aDouble = v;
                }else {
                    aDouble += v;
                }
                scaleMap.put(ym,aDouble);
            });
        });
        Collection<Map> data = rsp.getData();
        Map next = data.iterator().next();
        rateMap.forEach((ym,m)->{
            Double totalScale = scaleMap.getOrDefault(ym,0.0);
            m.forEach((k,v)->{
                if (totalScale == 0){
                    next.put(k,null);
                }else {
                    Double rate = v / totalScale;
                    next.put(k,String.format("%.2f%%", rate));
                }
            });
        });
        return rsp;
    }

    @SneakyThrows
    private void await(CountDownLatch countDownLatch) {
        try {
            boolean await = countDownLatch.await(30, TimeUnit.SECONDS);
            if (!await) {
                throw new Exception("请求超时【30s】");
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public void doAsyncQueryTable(FiberUtils.Executor executor,ConcurrentHashMap<ReplaceIndexEnum, QueryTableRsp> mergeMap,
            ReplaceIndexEnum indexEnum,
            QueryTableReq req,
            boolean export,
            CountDownLatch downLatch) {
        executor.submit(() -> {
            try {
                mergeMap.put(indexEnum, doQueryTable(req, export));
            } finally {
                downLatch.countDown();
            }
        });
    }

    /**
     * @param req 请求体
     * @param res 返回体
     * @param mergeRes 需要合并进返回的的返回体
     * @param replaceIndex 需要替换的指标
     */
    @SneakyThrows
    private void merge(QueryTableReq req, QueryTableRsp res, QueryTableRsp mergeRes, List<String> replaceIndex) {
        if (res == null || mergeRes == null) {
            throw new ITException("查询结果为空，无法合并");
        }
        if (ListUtils.isEmpty(res.getData()) || ListUtils.isEmpty(mergeRes.getData())) {
            return;
        }
        List<String> originSelectFields = req.getOriginSelectFields();
        Map<String, Map> keyMap = new HashMap<>();
        for (Map m : res.getData()) {
            StringBuilder keySB = new StringBuilder();
            for (String os : originSelectFields) {
                keySB.append(m.get(os));
                keySB.append("@");
            }
            String key;
            if (CollectionUtils.isEmpty(originSelectFields)) {
                key = "";
            } else {
                key = keySB.substring(0, keySB.length() - 1);
            }
            keyMap.put(key, m);
        }
        for (Map m : mergeRes.getData()) {
            StringBuilder keySB = new StringBuilder();
            for (String os : originSelectFields) {
                keySB.append(m.get(os));
                keySB.append("@");
            }
            String key;
            if (CollectionUtils.isEmpty(originSelectFields)) {
                key = "";
            } else {
                key = keySB.substring(0, keySB.length() - 1);
            }
            Map map = keyMap.get(key);
            if (map != null) {
                // 指定key数据替换
                for (Object o : m.entrySet()) {
                    Entry<String, Object> entry = (Entry<String, Object>) o;
                    String rk = entry.getKey();
                    for (String index : replaceIndex) {
                        if (rk.contains(index)) {
                            map.put(rk, entry.getValue());
                            break;
                        }
                    }
                }
            }
        }
    }


    private QueryTableRsp doQueryTable(QueryTableReq req, boolean export) {
        // --- 参数检查和响应预先设置 ---
        req.fillFields();
        QueryTableRsp rsp = new QueryTableRsp();
        List<String> selectFields = req.getSelectFields();
        List<String> searchTimeList = tidyTimeRange(req.getTimePeriod());
        if (CollectionUtils.isEmpty(searchTimeList)) {
            return rsp;
        }
        searchTimeList.add(0, "总计");
        req.setSearchTimeList(searchTimeList);
        List<String> newSelectFields = rspSetSelectFieldsHeader(selectFields, rsp);
        req.setSelectFields(newSelectFields);
        req.setOriginSelectFields(new ArrayList<>(newSelectFields));
        List<String> queryTargets = req.getQueryTargets();
        if (CollectionUtils.isEmpty(queryTargets)) {
            return rsp;
        }
        Map<String, Object> fieldMap = rspSetTargetsHeader(rsp, req, export);
        defaultSortDataColName(req, rsp);
        Set<String> normalField = (Set<String>) fieldMap.get("normalField");
        Map<String, DynamicTargetTemplate> specialField = (Map<String, DynamicTargetTemplate>) fieldMap.get(
                "specialField");
        Map<String, TargetTemplate> distinctScaleNumMap = (Map<String, TargetTemplate>) fieldMap.get(
                "distinctScaleNumMap");
        if (CollectionUtils.isEmpty(normalField) && CollectionUtils.isEmpty(specialField)
                && CollectionUtils.isEmpty(distinctScaleNumMap)) {
            return rsp;
        }
        LocalDate date = dataDate(req);
        if (date == null) {
            return rsp;
        }
        // 采购机型（是否为采购机型）
        purchaseInstanceType(req);
        // 机型代次（新旧机型）
        generationInstanceType(req);
        // --- 部分筛选项的处理(有用到statTime，需要提前处理好statTime)
        mainInstanceFamilyAndZone(req);
        // 因用的机型库不全，渲染机型直接通过是否 RS 或者 RM 开头判断，所以下面被注释了
//        renderInstanceType(req);
        commonInstanceType(req);
        // --- 开始拉取数据 ---
        Map<String, Object> result;
        try {
            result = loadData(req, fieldMap, export);
        } catch (UncategorizedSQLException e) {
            log.error(e.getMessage());
            throw new ITException("指标查询不到，可能是指标数据无对应年月数据");
        }
        rsp.setData((Collection<Map>) result.get("result"));
        rsp.setCount((Long) result.get("count"));

        // 处理新版中长尾带行业维度（业务类型/行业）
        cleanData(rsp.getData(), req);
        // 中长尾预测数据替换（取firefly提供的接口数据）
        cleanForecastMatchRate(rsp.getData(), req);
        // 核天准确率532新版人工矫正
        cleanForecastMatchRateCoreDay(rsp.getData(), req);
        // 屏蔽中长尾的净增
        cleanForecastMatchRateWithDemandType(rsp.getData(), req);
        // 中长尾客户简称设置备注（不直接改数据，客户简称有过滤条件，不能查询和展示不一致）
        cleanRemark(rsp.getData(), req);
        return rsp;
    }

    /**
     * 清洗备注
     */
    private void cleanRemark(Collection<Map> data, QueryTableReq req) {
        if (ListUtils.isEmpty(data)) {
            return;
        }
        data.forEach(item -> {
            if (item == null) {
                return;
            }
            // 客户简称 ---> 客户简称备注
            // uin:0  ----> 中长尾客户
            String customerShortName = (String) item.get("customerShortName");
            String unCustomerShortName = (String) item.get("unCustomerShortName");
            if (Objects.equals(customerShortName, Constant.TAIL_CUSTOMER_SHORT_NAME) ||
                    Objects.equals(unCustomerShortName, Constant.TAIL_CUSTOMER_SHORT_NAME)) {
                item.put("customerShortNameRemark", Constant.TAIL_CUSTOMER);
            }
        });
    }

    private void cleanForecastMatchRateWithDemandType(Collection<Map> data, QueryTableReq req) {
        if (req.needCleanLongRate()) {
            for (Map datum : data) {
                for (Object o : datum.entrySet()) {
                    Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    // 这里有可能是核天的，加@防止清洗到其他指标
                    if (key.contains(ForecastMatchRate.staticTargetName() + "@") && value instanceof String) {
                        datum.put(key, null);
                    }
                }
            }
        }
    }

    /**
     * 核天532新版准确率（客户+行业维度）
     */
    private void cleanForecastMatchRateCoreDay(Collection<Map> data, QueryTableReq req) {
        // 需要客户维度的清洗
        doCleanForecastMatchRateCoreDay(data, req, true);
        // 不需要客户维度的清洗
        doCleanForecastMatchRateCoreDay(data, req, false);
    }

    private void doCleanForecastMatchRateCoreDay(Collection<Map> data, QueryTableReq req,
            boolean needCustomerShortName) {
        // 是否需要干预
        if (!req.needForecastMatchRateCoreDay()) {
            return;
        }
        if (!checkNeedForecastMatchRateCoreDay(req, needCustomerShortName)) {
            return;
        }
        if (ListUtils.isEmpty(data)) {
            return;
        }

        int yearMonthSize = 1;
        try {
            // 获取起始结束范围
            TimePeriod timePeriod = req.getTimePeriod();
            String start = timePeriod.getStart();
            String end = timePeriod.getEnd();
            List<String> yearMonthRange = RangeUtils.rangeDate(start, end);
            yearMonthSize = yearMonthRange.size();
        } catch (Exception ignore) {
        }

        // 转换查询条件
        ForecastMatchRateCoreDayReq coreDayReq = ForecastMatchRateCoreDayReq.transForm(req, needCustomerShortName);

        // 查询核天信息
        ForecastMatchRateCoreDayRes coreDayRes = queryForecastMateRate.queryCoreDay(coreDayReq);

        // 转换为主key+指标信息map
        MultiKeyMap<String, Map<String, List<ForecastMatchRateCoreDayInfoList>>> map = ForecastMatchRateCoreDayRes.transForm(
                coreDayRes, coreDayReq);

        if (ListUtils.isEmpty(map)) {
            return;
        }

        List<String> demandTypes = req.getDemandTypes();
        demandTypes = demandTypes == null ? null : demandTypes.stream()
                .filter(item -> !Objects.equals(Constant.EMPTY_VALUE, item)).collect(Collectors.toList());

        if (ListUtils.isEmpty(demandTypes)) {
            demandTypes = Arrays.stream(DemandTypeEnum.values()).map(DemandTypeEnum::getName)
                    .collect(Collectors.toList());
        }

        String defDemandType = DemandTypeEnum.getDemandType(demandTypes);

        boolean hasBizType = req.getOriginSelectFields().contains("bizType");

        for (Map<String, Object> datum : data) {
            // 如果按业务类型分组了但是不是外部行业的不进行矫正
            if (hasBizType && !Objects.equals(datum.get("bizType"), "外部行业")) {
                continue;
            }
            String demandType = (String) datum.get("demandType");
            if (demandType == null) {
                demandType = defDemandType;
            }
            String industryOrProduct = (String) datum.get("industryOrProduct");
            String customerShortName = (String) datum.get("customerShortName");
            MultiKey<String> key =
                    needCustomerShortName ? new MultiKey<>(demandType, industryOrProduct, customerShortName) :
                            new MultiKey<>(demandType, industryOrProduct);
            Map<String, List<ForecastMatchRateCoreDayInfoList>> infoMap = map.get(key);
            if (infoMap != null) {
                doChangeMatchRateCoreDay(datum, infoMap, BigDecimal.valueOf(yearMonthSize));
            }
        }
    }


    private void doChangeMatchRateCoreDay(Map<String, Object> datum,
            Map<String, List<ForecastMatchRateCoreDayInfoList>> infoMap, BigDecimal yearMonthSize) {
        infoMap.forEach((k, vl) -> vl.forEach(v -> {
            String index = (String) datum.get(k);
            // step1：获取原始准确率
            BigDecimal oldV = BigDecimal.ZERO;
            if (index != null && index.contains("%")) {
                oldV = new BigDecimal(index.substring(0, index.length() - 1));
            }
            v.setBeforeMatchRate(oldV);
            BigDecimal addV = BooleanUtils.isTrue(v.getIsOrigin()) ? v.getOriginUp() : v.getCurrentUp();
            if (addV == null) {
                addV = BigDecimal.ZERO;
            }

            // step2：处理新准确
            BigDecimal newV = oldV.add(addV);
            v.setAfterMatchRate(newV);
            datum.put(k, String.format("%.2f%%", newV));

            // step3：备注信息方法指标+"_info"里面
            String infoKey = k + "_info";
            ForecastMatchRateCoreDayInfo info = (ForecastMatchRateCoreDayInfo) datum.computeIfAbsent(infoKey,
                    (item) -> new ForecastMatchRateCoreDayInfo());
            if (info.getBeforeMatchRate() == null) {
                info.setBeforeMatchRate(oldV);
                info.setInfoList(new ArrayList<>());
            }
            info.setUpMatchRate(ObjectUtils.defaultIfNull(info.getUpMatchRate(), BigDecimal.ZERO).add(addV));
            info.setAfterMatchRate(newV);
            info.getInfoList().add(v);

            // step4：处理总计
            String totalK = k.split("@")[0] + "@总计";
            String totalIndex = (String) datum.get(totalK);
            BigDecimal oldTotal = BigDecimal.ZERO;
            if (totalIndex != null && totalIndex.contains("%")) {
                oldTotal = new BigDecimal(totalIndex.substring(0, totalIndex.length() - 1));
            }
            BigDecimal newTotal = oldTotal.add(addV.divide(yearMonthSize, 6, RoundingMode.HALF_UP));
            datum.put(totalK, String.format("%.2f%%", newTotal));
        }));
    }

    private boolean checkNeedForecastMatchRateCoreDay(QueryTableReq req, boolean needCustomerShortName) {
        // 新客户分类：项目类型。。。
        // 老客户分类：订单。。。
        Set<String> allowDim = BooleanUtils.isTrue(req.getIsNewOrderType())?
                new HashSet<>(
                        Arrays.asList("projectType", "productClass", "industryOrProduct", "customerShortName", "demandType")):
                new HashSet<>(
                        Arrays.asList("orderType", "bizType", "industryOrProduct", "customerShortName", "demandType"));

        List<String> originSelectFields = req.getOriginSelectFields();

        QueryTableReq.Choose choose = req.getChoose();

        List<String> ext = originSelectFields.stream().filter(item -> !allowDim.contains(item))
                .collect(Collectors.toList());

        // 包含除了上面四个之外额外的分组则不进行532新版核天干预
        if (ListUtils.isNotEmpty(ext)) {
            return false;
        }

        // 行业部门，客户简称，需求类型(条件)
        boolean hasIndustryDept = false;
        boolean hasCustomerShortName = false;
        boolean hasBizType = BooleanUtils.isTrue(req.getIsNewOrderType());
        boolean hasDemandType = false;
        boolean hasProductClass = !BooleanUtils.isTrue(req.getIsNewOrderType()); // 老客户分类不看这个
        boolean hasOther;

        List<String> demandTypes = req.getDemandTypes();
        demandTypes = demandTypes == null ? null : demandTypes.stream()
                .filter(item -> !Objects.equals(Constant.EMPTY_VALUE, item)).collect(Collectors.toList());

        // 不需要现在需求类型了，矫正可以设置多个需求类型
        if (true || originSelectFields.contains("demandType") || (ListUtils.isNotEmpty(demandTypes)
                && demandTypes.size() == 1)) {
            hasDemandType = true;
        }
        // 按业务类型分组或者指定了外部业务且不指定内领业务
        if (originSelectFields.contains("bizType") ||
                (ListUtils.isNotEmpty(req.getBizTypes()) && req.getBizTypes().contains("外部行业") && !req.getBizTypes()
                        .contains("内领业务"))) {
            hasBizType = true;
        }

        if (!hasProductClass && (originSelectFields.contains("productClass") ||
                (ListUtils.isNotEmpty(req.getProductClass()) && req.getProductClass().size() == 1
                        && req.getProductClass().contains(Ppl13weekProductTypeEnum.CVM.getName())))){
            hasProductClass = true;
        }

        if (originSelectFields.contains("industryOrProduct")) {
            hasIndustryDept = true;
        }
        if (originSelectFields.contains("customerShortName")) {
            hasCustomerShortName = true;
        }

        // 有其他过滤条件
        hasOther = ListUtils.isNotEmpty(req.getRegionNames()) || ListUtils.isNotEmpty(req.getInstanceTypes());

        // 如果不需要客户简称，这里过滤条件也不能有客户简称
        if (!needCustomerShortName) {
            hasOther = hasOther || ListUtils.isNotEmpty(req.getCustomerShortNames());
        }

        // 如果不需要customerShortName，这里有和没有是相反的，有则不能做行业矫正，反正可以
        if (!needCustomerShortName) {
            hasCustomerShortName = !hasCustomerShortName;
        }

        return hasProductClass && hasDemandType && hasBizType && hasIndustryDept && hasCustomerShortName && !hasOther;
    }

    private void cleanForecastMatchRate(Collection<Map> data, QueryTableReq req) {
        // 配置限制执行
        if (!DynamicProperties.enableForecastMateFromInterface()) {
            return;
        }

        // 季度不处理准确率
        TimePeriod timePeriod = req.getTimePeriod();
        if (timePeriod != null && StringUtils.isNotBlank(timePeriod.getStart()) && timePeriod.getStart().contains("Q")){
            return;
        }

        boolean hasDemandTypeDim = req.getOriginSelectFields().contains("demandType");
        List<String> demandTypes = ObjectUtils.defaultIfNull(req.getDemandTypes(),new ArrayList<>());
        demandTypes = demandTypes.stream().filter(SoeCommonUtils::isNotBlank).collect(Collectors.toList());

        boolean isNewOrderType = BooleanUtils.isTrue(req.getIsNewOrderType());

        // 非中长尾 并且 非新客户分类则跳过，新客户分类不是按客户分类看，而是项目类型看，存在非中长尾查询场景
        if (!req.isLongRate() && !isNewOrderType) {
            return;
        }

        // 展示不屏蔽，这里要等驾驶舱的同步后再改
//        if (!isNewOrderType){
//            return; // 非新客户分类不展示中长尾准确率
//        }

        // 如果没有需求维度，且有新增和退回需求类型，则不进行清洗
        if (!hasDemandTypeDim && (ListUtils.isEmpty(demandTypes) || (demandTypes.contains("退回") && demandTypes.size() > 1))){
            return;
        }
        boolean hasProductClass = req.getOriginSelectFields().contains("productClass");
        boolean hasProjectType = req.getOriginSelectFields().contains("projectType");
        boolean hasBizType = req.getOriginSelectFields().contains("bizType");

        if (isNewOrderType){
            // 新版客户分类还需要额外匹配两个维度
            // 产品：CVM&CBS
            // 项目：常规项目
            List<String> productClass = req.getProductClass();
            List<String> projectType = req.getProjectType();
            // 不只有 CVM&CBS 且没有产品维度
            if (!SoeCommonUtils.onlyContains(productClass,Ppl13weekProductTypeEnum.CVM.getName()) &&
                    !hasProductClass){
                return;
            }
            // 不只有常规项目且没有项目维度
            if (!SoeCommonUtils.onlyContains(projectType, ProjectTypeEnum.NORMAL_PROJECT.getName()) &&
                    !hasProjectType){
                return;
            }
        }

        Set<String> allowFields;

        if (isNewOrderType){
            allowFields = new HashSet<>(Arrays.asList(
                    "orderType",
                    "productClass", // 子产品
                    "projectType", // 项目类型
                    "bizType", // 业务类型
                    "instanceTypeGeneration", // 新旧机型
                    "regionType", // 境内外
                    "regionName",
                    "ginsFamily",
                    "demandType"
            ));
        }else {
            allowFields = new HashSet<>(Arrays.asList(
                    "orderType",
                    "regionName",
                    "ginsFamily",
                    "demandType"
            ));
        }
        // 待清洗的集合(新版中长尾准确率的业务类型是空值，只有地域和实例类型两个维度信息)
        List<String> ext = req.getOriginSelectFields().stream().filter(item -> !allowFields.contains(item))
                .collect(Collectors.toList());

        // 过滤掉有值的记录
        boolean isExt = ListUtils.isNotEmpty(ext);

        List<Map> cleanList = (List<Map>)data;
        if (isExt || isNewOrderType){
            String cvmProductClass = Ppl13weekProductTypeEnum.CVM.getName();
            String normalProjectType = ProjectTypeEnum.NORMAL_PROJECT.getName();
            String outBizType = "外部行业";

            cleanList = data.stream().filter(item -> {
                // 新客户分类需要额外匹配 CVM&CBS且常规项目
                if (isNewOrderType){
                    if (hasProductClass){
                        String productClass = (String) item.get("productClass");
                        if (!Objects.equals(cvmProductClass, productClass)){
                            return false;
                        }
                    }
                    if (hasProjectType){
                        String projectType = (String) item.get("projectType");
                        if (!Objects.equals(normalProjectType, projectType)){
                            return false;
                        }
                    }
                    if (hasBizType){
                        String bizType = (String) item.get("bizType");
                        if (StringUtils.isNotBlank(bizType) && !StringUtils.equals(bizType, outBizType)) {
                            return false;
                        }
                    }
                }
                if (isExt){
                    for (String dim : ext) {
                        String dimValue = (String) item.get(dim);
                        if (StringUtils.isNotBlank(dimValue) && !StringUtils.equals(dimValue, Constant.EMPTY_VALUE)) {
                            return false;
                        }
                    }
                }
                return true;
            }).collect(Collectors.toList());
        }

        // 清理准确率
        if (ListUtils.isNotEmpty(cleanList)) {
            List<String> selectFields = req.getSelectFields();
            // 境内外，新旧机型按维度多查几次
            boolean hasRegionType = selectFields.contains("regionType");
            boolean hasInstanceTypeGeneration = selectFields.contains("instanceTypeGeneration");
            Choose choose = req.getChoose();
            List<String> country = req.getCountry();
            List<String> generationInstanceType = req.getGenerationInstanceType();
            if (choose != null){
                String regionType = choose.getRegionType();
                if (regionType != null){
                    country = ListUtils.newArrayList(regionType);
                }
                String instanceTypeGeneration = choose.getInstanceTypeGeneration();
                if (instanceTypeGeneration != null){
                    generationInstanceType = ListUtils.newArrayList(instanceTypeGeneration);
                }
            }
            if (hasRegionType){
                if (ListUtils.isEmpty(country)) {
                    country = ListUtils.newArrayList("境内", "境外");
                }else if (country.size() == 1){
                    country = ListUtils.newArrayList(Constant.EMPTY_VALUE);
                }
            }else {
                country = ListUtils.newArrayList(Constant.EMPTY_VALUE);
            }
            if (hasInstanceTypeGeneration){
                if (ListUtils.isEmpty(generationInstanceType)) {
                    generationInstanceType = ListUtils.newArrayList(GenerationEnum.NEW.getName(),GenerationEnum.OLD.getName());
                }else if (generationInstanceType.size() == 1){
                    generationInstanceType = ListUtils.newArrayList(Constant.EMPTY_VALUE);
                }
            }else {
                generationInstanceType = ListUtils.newArrayList(Constant.EMPTY_VALUE);
            }
            for (String c : country) {
                for (String g : generationInstanceType) {
                    doCleanForecastMatchRateWithInterface(cleanList, req, c, g);
                }
            }
        }
    }


    /**
     * 通过接口清洗
     */
    private void doCleanForecastMatchRateWithInterface(List<Map> cleanList, QueryTableReq req,String customhouseTitle,String generationInstanceType) {

        customhouseTitle = SoeCommonUtils.isNotBlank(customhouseTitle) ? customhouseTitle: null;
        generationInstanceType = SoeCommonUtils.isNotBlank(generationInstanceType) ? generationInstanceType: null;

        boolean hasDemandTypeDim = req.getOriginSelectFields().contains("demandType");
        List<String> demandTypes = req.getDemandTypes();

        // 接口查询数据 - 用陈文提供的接口 Ppl13WeekRateViewController
        ForecastMateRateReq rateReq = ForecastMateRateReq.transForm(req,customhouseTitle,generationInstanceType);
        ForecastMateRateRes res = queryForecastMateRate.query(rateReq);
        if (res == null || res.getData() == null) {
            return;
        }

        // 整体流程是将cleanList进行准确率替换
        // step1：ppl13接口返回数据转换，主key - 需要替换指标的map
        // step2：遍历cleanList找map中符合的进行指标替换，如果原因值为null则不进行处理
        List<ForecastMateRateResList> list = res.getData();

        boolean hasInstanceTypeDim = BooleanUtils.isTrue(rateReq.getHasInstanceTypeDim());
        boolean hasRegionNameDim = BooleanUtils.isTrue(rateReq.getHasRegionNameDim());
        boolean hasRegionTypeDim = customhouseTitle != null;
        boolean hasGenerationInstanceType = generationInstanceType != null;
        String defDemandType;
        if (ListUtils.isNotEmpty(demandTypes)) {
            defDemandType = demandTypes.contains("退回") ? "退回" : "新增";
        } else {
            defDemandType = Constant.EMPTY_VALUE;
        }

        LocalDate statTime = req.getStatTime();
        if (statTime == null) {
            statTime = LocalDate.now();
        }

        MultiKeyMap<String, Map<String, BigDecimal>> map = ForecastMateRateRes.transFormRateResList(hasRegionNameDim,
                hasInstanceTypeDim, statTime, list);

        for (Map<String, Object> objectMap : cleanList) {
            String demandType = (String) objectMap.get("demandType");
            String ginsFamily = (String) objectMap.get("ginsFamily");
            String regionName = (String) objectMap.get("regionName");
            String regionType = (String) objectMap.get("regionType");
            String instanceTypeGeneration = (String) objectMap.get("instanceTypeGeneration");

            // 有境内外但是不匹配
            if (hasRegionTypeDim && Objects.equals(regionType, customhouseTitle)){
                continue;
            }

            // 有境内外但是不匹配
            if (hasGenerationInstanceType && Objects.equals(instanceTypeGeneration, generationInstanceType)){
                continue;
            }

            // 与维度绑定，空值归到默认值避免any(xx)以及不查该字段时为null的影响
            if (!hasInstanceTypeDim || ginsFamily == null) {
                ginsFamily = Constant.EMPTY_VALUE;
            }
            if (!hasRegionNameDim || regionName == null) {
                regionName = Constant.EMPTY_VALUE;
            }
            if (!hasDemandTypeDim && demandType == null) {
                demandType = defDemandType;
            }

            boolean isOrigin = Objects.equals(ForecastStatus.origin.getName(), req.getForecastStatus());
            Map<String, BigDecimal> decimalMap = map.get(demandType, ginsFamily, regionName);
            if (decimalMap != null) {
                for (Entry<String, BigDecimal> entry : decimalMap.entrySet()) {
                    String key = entry.getKey();
                    if (isOrigin) {
                        key = "origin_" + key;
                    }
                    BigDecimal value = entry.getValue();
                    if (value != null) {
                        objectMap.put(key, String.format("%.2f%%",
                                Constant.b100.multiply(ObjectUtils.defaultIfNull(entry.getValue(), BigDecimal.ZERO))));
                    } else {
                        objectMap.put(key, null);
                    }
                }
            }
        }

    }


    /**
     * 清洗为null
     */
    private void doCleanForecastMatchRateWithNull(List<Map> cleanList, QueryTableReq req) {
        TimePeriod timePeriod = req.getTimePeriod();
        List<String> yearMonthList = RangeUtils.rangeDate(timePeriod.getStart(), timePeriod.getEnd());
        yearMonthList.add("总计");
        List<String> version = req.getVersion();
        List<String> versionName = VersionEnum.desc2Name(version);

        if (CollectionUtils.isEmpty(versionName)) {
            versionName = VersionEnum.allName();
        }

        List<StringJoin> stringJoins = Arrays.asList(
                new StringJoin(Arrays.asList("", "origin_")),
                new StringJoin(versionName),
                new StringJoin(ForecastMatchRate.staticTargetName()),
                new StringJoin("@"),
                new StringJoin(yearMonthList)
        );

        List<String> join = StringJoin.join(stringJoins);

        if (ListUtils.isEmpty(join)) {
            return;
        }

        for (Map<String, Object> datum : cleanList) {
            for (String index : join) {
                datum.put(index, null);
            }
        }
    }


    private void cleanData(Collection<Map> data, QueryTableReq req) {
        if (ListUtils.isEmpty(data)) {
            return;
        }
        // 中长尾屏蔽准确率
        for (Map datum : data) {
            String orderType = (String)datum.get("orderType");
            String projectType = (String)datum.get("projectType");
            if (Objects.equals(orderType,"中长尾") || Objects.equals(projectType,ProjectTypeEnum.NORMAL_PROJECT.getName()) || Objects.equals(projectType,ProjectTypeEnum.EMPTY_PROJECT.getName()) ){
                for (Object o : datum.entrySet()) {
                    Map.Entry<String, Object> entry = (Map.Entry<String, Object>) o;
                    if (entry.getValue() instanceof String && Objects.equals("0.00%", entry.getValue())) {
                        datum.put(entry.getKey(), null);
                    }
                }
            }
        }
    }

    private void authFilter(WhereSQL whereSQL) {
        String username = LoginUtils.getUserName();
        if ("no".equalsIgnoreCase(username) || "UNKNOWN".equalsIgnoreCase(username)) {
            // 不用过滤了，能看到全部数据
            return;
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(username);
        Set<String> authIndustryDepts = new HashSet<>();
        Set<String> authProducts = new HashSet<>();
        for (IndustryDemandAuthDO authDO : auths) {
            if (IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(authDO.getRole())) {
                // 不用过滤了，能看到全部数据
                return;
            }
            // 只看行业数据关注人
            if (!IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode().equals(authDO.getRole())){
                continue;
            }
            if (authDO.getProduct() != null) {
                if (authDO.getProduct().contains("CVM")
                        || authDO.getProduct().contains("CVM&CBS")) {
                    // 看有哪些行业的权限
                    String industryDepts = authDO.getIndustry();
                    if (!StringUtils.isBlank(industryDepts)) {
                        String[] industryDeptArray = industryDepts.split(";");
                        for (String s : industryDeptArray) {
                            if (!StringUtils.isBlank(s)) {
                                authIndustryDepts.add(s);
                            }
                        }
                    }
                }
                // 改成包含【内部业务部】，避免配置了行业权限但是无法看到 PAAS 产品的问题
                if (authDO.getIndustry().contains("内部业务部")) {
                    // 看有哪些产品的权限
                    String products = authDO.getProduct();
                    if (!StringUtils.isBlank(products)) {
                        String[] productArray = products.split(";");
                        for (String s : productArray) {
                            if (!StringUtils.isBlank(s)) {
                                authProducts.add(s);
                            }
                        }
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(authIndustryDepts) && CollectionUtils.isEmpty(authProducts)) {
            whereSQL.and("1 = 0");
            return;
        }
        WhereSQL orSql = new WhereSQL();
        if (!CollectionUtils.isEmpty(authIndustryDepts)) {
            WhereSQL outer = new WhereSQL();
            outer.and("industry_or_product in (?)", authIndustryDepts);
            orSql.or(outer);
        }
        if (!CollectionUtils.isEmpty(authProducts)) {
            WhereSQL inner = new WhereSQL();
            inner.and("industry_or_product in (?)", authProducts);
            orSql.or(inner);
        }
        whereSQL.and(orSql);
    }

    @Data
    @NoArgsConstructor
    static class StringJoin {

        private boolean isList;
        private String v1;
        private List<String> v2;

        public StringJoin(String v1) {
            this.isList = false;
            this.v1 = v1;
        }

        public StringJoin(List<String> v2) {
            this.isList = true;
            this.v2 = v2;
        }

        public static List<String> join(List<StringJoin> stringJoins) {
            if (ListUtils.isEmpty(stringJoins)) {
                return new ArrayList<>();
            }
            List<String> ret = new ArrayList<>();
            for (StringJoin stringJoin : stringJoins) {
                List<String> temp = new ArrayList<>();
                if (stringJoin.isList) {
                    for (String s : stringJoin.v2) {
                        if (temp.isEmpty()) {
                            temp.add(s);
                        } else {
                            for (String str : ret) {
                                temp.add(str + s);
                            }
                        }
                    }
                }
                ret = temp;
            }
            return ret;
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class StringOrBigDecimal implements Comparable<StringOrBigDecimal> {

        private Object value;

        @Override
        public int compareTo(@NotNull StringOrBigDecimal o) {
            if (value == null && o.value == null) {
                return 0;
            } else if (value == null && o.value != null) {
                return -1;
            } else if (value != null && o.value == null) {
                return 1;
            }
            if (value instanceof BigDecimal) {
                return ((BigDecimal) value).compareTo((BigDecimal) o.value);
            } else if (value instanceof Double) {
                return ((Double) value).compareTo((Double) o.value);
            } else if (value instanceof String) {
                return ((String) value).compareTo((String) o.value);
            } else {
                return 0;
            }
        }
    }
}
