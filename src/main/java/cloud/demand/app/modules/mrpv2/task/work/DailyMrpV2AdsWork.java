package cloud.demand.app.modules.mrpv2.task.work;

import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2AdsTask;
import cloud.demand.app.modules.mrpv2.enums.DailyMrpV2Enum;
import cloud.demand.app.modules.mrpv2.service.ComputeAndSaveService;
import cloud.demand.app.modules.mrpv2.task.process.DailyMrpV2AdsProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class DailyMrpV2AdsWork extends AbstractSopWork<DailyMrpV2AdsTask> {
    @Resource
    private DailyMrpV2AdsProcess process;

    @Resource
    private ComputeAndSaveService computeAndSaveService;


    @Override
    public ISopProcess<DailyMrpV2AdsTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return DailyMrpV2Enum.ADS;
    }

    // 两分钟一次
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(DailyMrpV2AdsTask task) {
        computeAndSaveService.computeAndSave(task.getMrpDate());
    }
}
