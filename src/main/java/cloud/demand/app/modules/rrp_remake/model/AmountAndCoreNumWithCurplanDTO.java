package cloud.demand.app.modules.rrp_remake.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AmountAndCoreNumWithCurplanDTO {

    private Integer amount = 0;
    private Integer curplanAmount = 0;

    private Integer coreNum = 0;
    private Integer curplanCoreNum = 0;


    public void add(Integer amount, Integer curplanAdjust, Integer cpuLogicCore, Integer curplanAdjustCpuLogicCore) {
        this.amount += amount;
        this.curplanAmount += curplanAdjust;
        this.coreNum += cpuLogicCore;
        this.curplanCoreNum += curplanAdjustCpuLogicCore;
    }
}
