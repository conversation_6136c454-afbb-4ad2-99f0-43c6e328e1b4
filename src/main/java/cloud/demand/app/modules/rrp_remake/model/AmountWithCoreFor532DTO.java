package cloud.demand.app.modules.rrp_remake.model;

import cloud.demand.app.modules.mrpv2.Constant;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AmountWithCoreFor532DTO {

    // 预测量
    private Integer demandAmount = 0;
    // 532预测量
    private BigDecimal demand532Amount = BigDecimal.valueOf(0.0);
    // 执行量
    private Integer applyAmount = 0;
    // 预测量 （核心）
    private Integer demandCoreAmount = 0;
    // 532预测量（核心）
    private BigDecimal demand532CoreAmount = BigDecimal.valueOf(0.0);
    // 执行量（核心）
    private Integer applyCoreAmount = 0;


    // m-2月的量 0.5
    private Integer m2Amount = 0;
    // m-1月的量 0.3
    private Integer m1Amount = 0;
    // m月的量 0.2
    private Integer mAmount = 0;

    // m-2月的量 (核心) 0.5
    private Integer m2CoreAmount = 0;
    // m-1月的量 （核心） 0.3
    private Integer m1CoreAmount = 0;
    // m月的量  （核心） 0.2
    private Integer mCoreAmount = 0;

    public void addM2(Integer amount, Integer coreAmount) {
        this.m2Amount = this.m2Amount + amount;
        this.m2CoreAmount = this.m2CoreAmount + coreAmount;
    }

    public void addM1(Integer amount, Integer coreAmount) {
        this.m1Amount = this.m1Amount + amount;
        this.m1CoreAmount = this.m1CoreAmount + coreAmount;
    }

    public void addM(Integer amount, Integer coreAmount) {
        this.mAmount = this.mAmount + amount;
        this.mCoreAmount = this.mCoreAmount + coreAmount;
    }

    public void calculate532() {
        this.demand532Amount = this.demand532Amount.add(new BigDecimal(m2Amount).multiply(Constant.b05))
                .add(new BigDecimal(m1Amount).multiply(Constant.b03))
                .add(new BigDecimal(mAmount).multiply(Constant.b02));
        this.demand532CoreAmount = this.demand532CoreAmount.add(new BigDecimal(m2CoreAmount).multiply(Constant.b05))
                .add(new BigDecimal(m1CoreAmount).multiply(Constant.b03))
                .add(new BigDecimal(mCoreAmount).multiply(Constant.b02));
    }


}
