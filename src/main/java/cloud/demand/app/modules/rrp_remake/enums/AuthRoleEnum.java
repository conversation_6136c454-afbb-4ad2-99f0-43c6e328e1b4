package cloud.demand.app.modules.rrp_remake.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限角色枚举
 */
@Getter
public enum AuthRoleEnum {
    UNKNOWN("unknown", "未知"),
    // 以下角色 by 规划产品设置
    PRODUCT_SUBMITER("com_r", "产品录单人"),
    PRODUCT_VIEWER("view_r", "需求查看人"), // 不包含审批权限，只能看
    PRODUCT_APPROVER("prd_r", "产品审核人"),
    // 以下角色全局生效
    COMD_APPROVER("res_r", "资源组"), // 包含审批权限
    COMD_GM_APPROVER("res_gm_r", "资源总监"), // 包含审批权限
    COMD_VIEWER("res_viewer", "资源组查看人"), // 只能看，不能审批
    ADMIN("mng_r", "管理员"), // 只能看，不能审批，跟资源组查看人类似，但是不接收资源组查看人的通知（比如邮件）
            ;

    final private String en;
    final private String ch;

    AuthRoleEnum(String en, String ch) {
        this.en = en;
        this.ch = ch;
    }

    public static AuthRoleEnum getByEn(String en) {
        for (AuthRoleEnum e : AuthRoleEnum.values()) {
            if (Objects.equals(en, e.en)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static AuthRoleEnum getByCh(String ch) {
        for (AuthRoleEnum e : AuthRoleEnum.values()) {
            if (Objects.equals(ch, e.ch)) {
                return e;
            }
        }
        return UNKNOWN;
    }

    public static String getChByEn(String en) {
        AuthRoleEnum e = getByEn(en);
        return e == null ? UNKNOWN.ch : e.ch;
    }

    public static String getEnByCh(String ch) {
        AuthRoleEnum e = getByCh(ch);
        return e == null ? UNKNOWN.en : e.en;
    }

    public static Set<AuthRoleEnum> listAll() {
        return Arrays.stream(values()).collect(Collectors.toSet());
    }

    final public static Set<AuthRoleEnum> GLOBAL_VIEW_SET;
    final public static Set<AuthRoleEnum> SINGLE_VIEW_SET;

    static {
        GLOBAL_VIEW_SET = new HashSet<>();
        GLOBAL_VIEW_SET.add(COMD_APPROVER);
        GLOBAL_VIEW_SET.add(COMD_VIEWER);
        GLOBAL_VIEW_SET.add(COMD_GM_APPROVER);
        GLOBAL_VIEW_SET.add(ADMIN);

        SINGLE_VIEW_SET = new HashSet<>();
        SINGLE_VIEW_SET.add(PRODUCT_SUBMITER);
        SINGLE_VIEW_SET.add(PRODUCT_VIEWER);
        SINGLE_VIEW_SET.add(PRODUCT_APPROVER);

    }

}
