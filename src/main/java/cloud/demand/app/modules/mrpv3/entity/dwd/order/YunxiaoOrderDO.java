package cloud.demand.app.modules.mrpv3.entity.dwd.order;

import cloud.demand.app.modules.mrpv3.entity.getter.common.ISimpleGetter;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

/** 预约单 */
@Data
public class YunxiaoOrderDO implements ISimpleGetter {
    /** 业务类型 */
    @Column("biz_type")
    private String bizType;

    /** 行业部门 */
    @Column("industry_dept")
    private String industryDept;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 地域 */
    @Column("region_name")
    private String regionName;

    /** 可用区 */
    @Column("zone_name")
    private String zoneName;

    /** 实例类型 */
    @Column("instance_type")
    private String instanceType;

    /** 客户 uin */
    @Column("uin")
    private String uin;

    /** 需求类型 */
    @Column("demand_type")
    private String demandType;


    /** 产品 */
    @Column("data_product")
    private String dataProduct;

    /** 产品子类 */
    @Column("product_class")
    private String productClass;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    /** 项目类型 */
    @Column("is_spike")
    private Integer isSpike;

    /** 核数 */
    @Column("num")
    private BigDecimal num;



    @Override
    public String getProduct() {
        return dataProduct;
    }


    @Override
    public BigDecimal getNum1() {
        return num;
    }

    @Override
    public String getWarZone() {
        return null;
    }

    @Override
    public String getCountryName() {
        return null;
    }

    @Override
    public String getAreaName() {
        return null;
    }
}
