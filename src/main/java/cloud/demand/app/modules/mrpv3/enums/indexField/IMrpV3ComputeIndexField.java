package cloud.demand.app.modules.mrpv3.enums.indexField;

import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexFieldEnum;
import cloud.demand.app.modules.mrpv3.enums.indexField.rate.*;
import cloud.demand.app.modules.mrpv3.enums.indexField.scale.*;
import cloud.demand.app.modules.mrpv3.utils.WeightBigDecimal;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 指标字段
 */
public interface IMrpV3ComputeIndexField extends
        IMrpV3IndexField, // 基础指标字段
        ICoreDayScaleChangeAbsCoreNum, // 过程指标字段
        ICoreDayScaleChangeAbsDistinctCoreNum,
        IDailyScaleChangeAbsCoreNum,
        IDailyScaleChangeAbsDistinctCoreNum,
        IMonthlyScaleChangeAbsCoreNum,
        IMonthlyScaleChangeAbsDistinctCoreNum, // 过程指标字段
        IMonthly532NewRate, // 计算指标字段
        IMonthlyNewestRate,
        IDaily532NewRate,
        IDailyNewestRate,
        ICoreDay532NewRate,
        IMonthly532NewDistinctRate,
        IMonthlyNewestDistinctRate,
        IDaily532NewDistinctRate,
        IDailyNewestDistinctRate,
        ICoreDay532NewDistinctRate,
        IOriginMonthly532NewRate,
        IOriginMonthlyNewestRate,
        IOriginDaily532NewRate,
        IOriginDailyNewestRate,
        IOriginCoreDay532NewRate,
        IOriginMonthly532NewDistinctRate,
        IOriginMonthlyNewestDistinctRate,
        IOriginDaily532NewDistinctRate,
        IOriginDailyNewestDistinctRate,
        IOriginCoreDay532NewDistinctRate  // 计算指标字段
{


    /**
     * 拷贝，包括计算指标，比如准确率
     */
    static <T extends IMrpV3ComputeIndexField> void copyComputeIndexField(T target, T source) {
        IMrpV3IndexField.copyIndexField(target, source);
        target.setMonthly532NewRate(source.getMonthly532NewRate());
        target.setMonthlyNewestRate(source.getMonthlyNewestRate());
        target.setMonthly532NewDistinctRate(source.getMonthly532NewDistinctRate());
        target.setMonthlyNewestDistinctRate(source.getMonthlyNewestDistinctRate());
        target.setOriginMonthly532NewRate(source.getOriginMonthly532NewRate());
        target.setOriginMonthlyNewestRate(source.getOriginMonthlyNewestRate());
        target.setOriginMonthly532NewDistinctRate(source.getOriginMonthly532NewDistinctRate());
        target.setOriginMonthlyNewestDistinctRate(source.getOriginMonthlyNewestDistinctRate());
        target.setDaily532NewRate(source.getDaily532NewRate());
        target.setDailyNewestRate(source.getDailyNewestRate());
        target.setDaily532NewDistinctRate(source.getDaily532NewDistinctRate());
        target.setDailyNewestDistinctRate(source.getDailyNewestDistinctRate());
        target.setOriginDaily532NewRate(source.getOriginDaily532NewRate());
        target.setOriginDailyNewestRate(source.getOriginDailyNewestRate());
        target.setOriginDaily532NewDistinctRate(source.getOriginDaily532NewDistinctRate());
        target.setOriginDailyNewestDistinctRate(source.getOriginDailyNewestDistinctRate());
        target.setCoreDay532NewRate(source.getCoreDay532NewRate());
        target.setCoreDay532NewDistinctRate(source.getCoreDay532NewDistinctRate());
        target.setOriginCoreDay532NewRate(source.getOriginCoreDay532NewRate());
        target.setOriginCoreDay532NewDistinctRate(source.getOriginCoreDay532NewDistinctRate());
    }

    static <T extends IMrpV3ComputeIndexField> void copyComputeIndexField(T target, T source, MrpV3IndexFieldEnum[] indexes) {
        if (indexes != null) {
            for (MrpV3IndexFieldEnum index : indexes) {
                Function<Object, BigDecimal> fieldGetter = index.getFieldGetter();
                index.getFieldSetter().accept(target, fieldGetter.apply(source));
            }
        }
    }

    /**
     * 累加字段
     */
    static <T extends IMrpV3ComputeIndexField> void sumIndexField(T target, T source, MrpV3IndexFieldEnum[] indexes) {
        if (indexes != null) {
            for (MrpV3IndexFieldEnum index : indexes) {
                Function<Object, BigDecimal> fieldGetter = index.getFieldGetter();
                BigDecimal v1 = fieldGetter.apply(target);
                BigDecimal v2 = fieldGetter.apply(source);
                // 权重
                if (v1 instanceof WeightBigDecimal) {
                    v1 = ((WeightBigDecimal) v1).getWeight();
                }
                ;
                if (v2 instanceof WeightBigDecimal) {
                    v2 = ((WeightBigDecimal) v2).getWeight();
                }
                index.getFieldSetter().accept(target, SoeCommonUtils.addWithNull(v1, v2));
            }
        }
    }
}
