package cloud.demand.app.modules.mrpv3.service;


import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportParamReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;

import java.util.List;

public interface MrpV3ReportService {

    /** 报表查询 */
    public MrpV3ReportResp queryReport(MrpV3ReportReq req);

    /** 查询参数 */
    List<String> getParams(MrpV3ReportParamReq req);

    /** 兼容行业数据看板 v3 */
    QueryTableRsp queryIndustryMRPReportV2(QueryTableReq req);

    /** 半兼容行业数据看板 v3 （实际返回值还是 v3 的） */
    MrpV3ReportResp queryIndustryMRPReportV3(QueryTableReq req);
}
