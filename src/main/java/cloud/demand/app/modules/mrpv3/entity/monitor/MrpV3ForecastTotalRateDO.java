package cloud.demand.app.modules.mrpv3.entity.monitor;


import cloud.demand.app.modules.mrpv3.enums.MrpReportVersionEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TotalRateCategoryEnum;
import cloud.demand.app.modules.mrpv3.enums.indexField.rate.*;
import cloud.demand.app.modules.mrpv3.model.item.MrpV3DataItem;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 总准确率（拉取行业数据看板 v3 接口）
 */
@Data
@Table("daily_mrp_v3_forecast_total_rate")
public class MrpV3ForecastTotalRateDO implements
        IMonthlyNewestRate,
        IMonthly532NewRate,
        IDailyNewestRate,
        IDaily532NewRate,
        ICoreDay532NewRate,

        IMonthlyNewestDistinctRate,
        IMonthly532NewDistinctRate,
        IDailyNewestDistinctRate,
        IDaily532NewDistinctRate,
        ICoreDay532NewDistinctRate {

    /**
     * 切片日期
     */
    @Column("stat_time")
    private LocalDate statTime;

    /**
     * 版本，v2 or v3
     */
    @Column("version")
    private String version;

    /**
     * 策略
     * */
    @Column("category")
    private String category;

    /**
     * 年月,格式：yyyy-MM
     */
    @Column("year_month")
    private String yearMonth;

    /**
     * 需求类型 {@link cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalDemandTypeEnum}
     */
    @Column("demand_type")
    private String demandType;

    /**
     * 行业部门
     * */
    @Column("industry_dept")
    private String industryDept;

    /**
     * 532新版准确率
     */
    @Column("monthly_532_new_rate")
    private BigDecimal monthly532NewRate;

    /**
     * 最新版准确率
     */
    @Column("monthly_newest_rate")
    private BigDecimal monthlyNewestRate;

    @Column("daily_532_new_rate")
    private BigDecimal daily532NewRate;

    @Column("daily_newest_rate")
    private BigDecimal dailyNewestRate;

    @Column("core_day_532_new_rate")
    private BigDecimal coreDay532NewRate;

    // =============== 去重 ================
    @Column("monthly_532_new_distinct_rate")
    private BigDecimal monthly532NewDistinctRate;

    /**
     * 最新版准确率
     */
    @Column("monthly_newest_distinct_rate")
    private BigDecimal monthlyNewestDistinctRate;

    @Column("daily_532_new_distinct_rate")
    private BigDecimal daily532NewDistinctRate;

    @Column("daily_newest_distinct_rate")
    private BigDecimal dailyNewestDistinctRate;

    @Column("core_day_532_new_distinct_rate")
    private BigDecimal coreDay532NewDistinctRate;

    public static MrpV3ForecastTotalRateDO transform(MrpV3DataItem item, String demandType, MrpV3TotalRateCategoryEnum category) {
        MrpV3ForecastTotalRateDO ret = new MrpV3ForecastTotalRateDO();
        ret.setStatTime(item.getStatTime());
        ret.setCategory(category.getName());
        ret.setIndustryDept(item.getIndustryDept());
        ret.setVersion(MrpReportVersionEnum.V3.name());
        ret.setYearMonth(item.getYearMonth());
        ret.setDemandType(demandType);
        ret.setMonthly532NewRate(item.getMonthly532NewRate());
        ret.setMonthlyNewestRate(item.getMonthlyNewestRate());
        ret.setDaily532NewRate(item.getDaily532NewRate());
        ret.setDailyNewestRate(item.getDailyNewestRate());
        ret.setCoreDay532NewRate(item.getCoreDay532NewRate());
        ret.setMonthly532NewDistinctRate(item.getMonthly532NewDistinctRate());
        ret.setMonthlyNewestDistinctRate(item.getMonthlyNewestDistinctRate());
        ret.setDaily532NewDistinctRate(item.getDaily532NewDistinctRate());
        ret.setDailyNewestDistinctRate(item.getDailyNewestDistinctRate());
        ret.setCoreDay532NewDistinctRate(item.getCoreDay532NewDistinctRate());
        return ret;
    }
}
