package cloud.demand.app.modules.mrpv3.entity.dwd.purchase;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

/** 全年需求报表来源 */
@Data
public class ReportPurchaseOrderDetailDO {

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 地域 */
    @Column("region_name")
    private String regionName;

    @Column("zone_name")
    private String zoneName;

    /** 设备类型 */
    @Column("r_device_type")
    private String deviceType;

    /** 需求类型 */
    @Column("r_demand_type")
    private String demandType;

    /** 行业部门 */
    @Column("industry_dept")
    private String industryDept;

    /** 客户简称 */
    @Column("r_customer_name")
    private String customerShortName;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 核数 */
    @Column("core_num")
    private BigDecimal coreNum;

    /** 台数 */
    @Column("gpu_num")
    private BigDecimal gpuNum;

    /** 产品 */
    @Column("data_product")
    private String dataProduct;
}
