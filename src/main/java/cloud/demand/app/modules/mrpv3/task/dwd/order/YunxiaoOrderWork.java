package cloud.demand.app.modules.mrpv3.task.dwd.order;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import cloud.demand.app.modules.mrpv3.DynamicProperties;
import cloud.demand.app.modules.mrpv3.entity.dwd.order.YunxiaoOrderDO;
import cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.mrpv3.task.DwdMrpCommonWork;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import java.time.YearMonth;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/** 预约量 */
@Service
@Slf4j
public class YunxiaoOrderWork extends DwdMrpCommonWork {

    /** 新 ck */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Override
    public List<?> getData(String statTime) {
        YearMonth startYearMonth = DynamicProperties.getStartYearMonth();
        String yearMonth = SopDateUtils.getYearMonth(startYearMonth);  // 起始年月

        // 取上一天的数据
        String yesterday = SopDateUtils.beforeDate(statTime);

        String sql = ORMUtils.getSql("/sql/mrp_v3/dwd/order/yunxiao_order.sql");
        sql = SimpleSqlBuilder.doReplace(sql,"stat_time",yesterday);
        sql = SimpleSqlBuilder.doReplace(sql,"start_year_month",yearMonth);
        sql = PaasUtils.sqlReplacePAAS(sql);
        return ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDO.class, sql);
    }

    @Override
    public String getIndex() {
        return MrpV3IndexEnum.yunxiaoOrderNum.getName();
    }

    @Override
    public ITaskEnum getEnum() {
        return MrpV3TaskEnum.MRP_V3_YUNXIAO_ORDER;
    }
}
