package cloud.demand.app.modules.mrpv3.entity.dwd.forecast;

import cloud.demand.app.modules.mrpv3.entity.getter.common.ISimpleGetter;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/** ppl预测 */
@Data
public class PplForecastAdvanceWeekDO implements ISimpleGetter {
    /** 业务类型 */
    @Column("biz_type")
    private String bizType;

    /** 行业部门 */
    @Column("industry_dept")
    private String industryDept;

    /** 境内外 */
    @Column("customhouse_title")
    private String customhouseTitle;

    /** 地域名称 */
    @Column("region_name")
    private String regionName;

    /** 可用区名称 */
    @Column("zone_name")
    private String zoneName;

    /** 实例类型 */
    @Column("instance_type")
    private String instanceType;

    /** 客户 UIN */
    @Column("uin")
    private String uin;

    /** 需求类型 */
    @Column("demand_type")
    private String demandType;

    /** 数据产品 */
    @Column("data_product")
    private String dataProduct;

    /** 产品大类 */
    @Column("product_class")
    private String productClass;

    /** 年月 */
    @Column("year_month")
    private String yearMonth;

    /** 客户简称 */
    @Column("customer_short_name")
    private String customerShortName;

    /** crp战区 */
    @Column("war_zone_name")
    private String warZoneName;

    @Column("sum_w13")
    private BigDecimal w13;

    @Column("sum_w6")
    private BigDecimal w6;

    @Override
    public String getWarZone() {
        return getWarZoneName();
    }

    @Override
    public String getProduct() {
        return getDataProduct();
    }

    /** @see cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum#pplForecastAdvanceWeek */
    @Override
    public BigDecimal getNum1() {
        return getW6();
    }

    @Override
    public BigDecimal getNum2() {
        return getW13();
    }

    @Override
    public String getCountryName() {
        return null;
    }

    @Override
    public String getAreaName() {
        return null;
    }
}
