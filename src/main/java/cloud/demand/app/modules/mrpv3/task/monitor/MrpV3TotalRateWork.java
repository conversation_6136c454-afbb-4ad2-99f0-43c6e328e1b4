package cloud.demand.app.modules.mrpv3.task.monitor;

import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.MrpV2RateTotalDemandTypeEnum;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.service.DailyMrpV2DataDescribeService;
import cloud.demand.app.modules.mrpv2.service.QueryDataService;
import cloud.demand.app.modules.mrpv2.utils.PageQueryUtils;
import cloud.demand.app.modules.mrpv2.utils.SecurityFiberSupplier;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReqBuilder;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.mrpv3.entity.monitor.MrpV3ForecastTotalRateDO;
import cloud.demand.app.modules.mrpv3.enums.*;
import cloud.demand.app.modules.mrpv3.service.MrpV3ReportService;
import cloud.demand.app.modules.mrpv3.task.process.MrpV3TotalRateProcess;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import erp.base.fiber.support.dispatcher.FiberTaskExecutor;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import yunti.boot.exception.ITException;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.BiConsumer;

/**
 * 监控任务-总准确率
 */
@Service
public class MrpV3TotalRateWork extends MonitorCommonWork {

    @Resource
    private MrpV3ReportService mrpV3ReportService;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private DailyMrpV2DataDescribeService describeService;

    @Resource
    private MrpV3TotalRateProcess process;

    /**
     * mrp v2 查询
     */
    @Resource
    private QueryDataService queryDataService;

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return MrpV3TaskEnum.MRP_V3_MONITOR_TOTAL_RATE;
    }

    @Override
    public void doWork(SimpleCommonTask task) {
        genData(task.getVersion());
    }

    public void genData(String statTime) {
        List<Runnable> runnableList = new ArrayList<>();

        Arrays.stream(MrpV3TotalRateCategoryEnum.values()).filter(MrpV3TotalRateCategoryEnum::isEnabled).forEach(
                (item) -> runnableList.add(() -> genCategoryData(statTime,item))
        );

        // 一个批次最多执行 3 个任务，每个批次最多等待 3 分钟
        PageQueryUtils.submit(3, 180, runnableList);
    }

    private void genCategoryData(String statTime, MrpV3TotalRateCategoryEnum categoryEnum) {
        List<MrpV3ForecastTotalRateDO> data = new ArrayList<>();
        // step1：查询新行业数据看板接口
        List<MrpV3ForecastTotalRateDO> v3Data = getV3Data(statTime, categoryEnum);
        // step2：查询老行业数据看板接口
        List<MrpV3ForecastTotalRateDO> v2Data = getV2Data(statTime, categoryEnum);
        data.addAll(v2Data);
        data.addAll(v3Data);
        // 设置为切片时间
        LocalDate date = DateUtils.parseLocalDate(statTime);
        data.forEach(item -> item.setStatTime(date));
        // step3：删除分区
        CkDBUtils.deletePartitionMultiField(ckcldStdCrpDBHelper, MrpV3ForecastTotalRateDO.class, statTime, categoryEnum.getName());
        // step4：写入数据
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, data);
    }

    /**
     * 获取数据
     */
    private List<MrpV3ForecastTotalRateDO> getV3Data(String statTime, MrpV3TotalRateCategoryEnum categoryEnum) {
        List<MrpV3ForecastTotalRateDO> ret = new ArrayList<>();
        for (MrpV2RateTotalDemandTypeEnum value : MrpV2RateTotalDemandTypeEnum.values()) {
            MrpV3ReportReq req = buildReq(statTime, value.getDemandType().get(), categoryEnum);
            MrpV3ReportResp resp = mrpV3ReportService.queryReport(req);
            ret.addAll(ListUtils.transform(resp.getData(), item -> MrpV3ForecastTotalRateDO.transform(item, value.getName(), categoryEnum)));
        }
        return ret;
    }

    private List<MrpV3ForecastTotalRateDO> getV2Data(String statTime, MrpV3TotalRateCategoryEnum categoryEnum) {
        LocalDate date = DateUtils.parseLocalDate(statTime);
        List<Boolean> flagList = ListUtils.newList(true, false);
        // key：需求类型@年月
        Map<String, MrpV3ForecastTotalRateDO> data = new HashMap<>();
        for (MrpV2RateTotalDemandTypeEnum value : MrpV2RateTotalDemandTypeEnum.values()) {
            for (Boolean distinct : flagList) {
                for (Boolean monthly : flagList) {
                    QueryTableReq req = buildV2Req(date, value, distinct, monthly, categoryEnum);
                    QueryTableRsp rsp = queryDataService.queryTable(req, false);
                    parseRsp(rsp.getData(), data, value, distinct, monthly, categoryEnum);
                }
            }
        }
        return new ArrayList<>(data.values());
    }

    /**
     * 解析 v2 的返回值
     *
     * @param data     v2数据集
     * @param retData  返回给上层的 map
     * @param value    需求枚举
     * @param distinct 是否去重
     * @param monthly  是否为月均
     */
    private void parseRsp(Collection<Map> data,
                          Map<String, MrpV3ForecastTotalRateDO> retData,
                          MrpV2RateTotalDemandTypeEnum value,
                          Boolean distinct,
                          Boolean monthly,
                          MrpV3TotalRateCategoryEnum categoryEnum) {
        Iterator<Map> iterator = data.iterator();
        Map<String, BiConsumer<MrpV3ForecastTotalRateDO, BigDecimal>> indexSetMap = new HashMap<>();
        MultiKeyMap<String, MrpV3IndexFieldEnum> map = MrpV3IndexFieldEnum.getMap();
        String demandType = value.getName();
        String monthlyStr = BooleanUtils.toStringTrueFalse(monthly);
        String distinctStr = BooleanUtils.toStringTrueFalse(distinct);
        indexSetMap.put("latest_forecast_match_rate", (item, v) -> map.get("最新版", monthlyStr, distinctStr).getFieldSetter().accept(item, v));
        indexSetMap.put("v2_avg_forecast_match_rate", (item, v) -> map.get("532新版", monthlyStr, distinctStr).getFieldSetter().accept(item, v));
        indexSetMap.put("v2_avg_forecast_match_rate_core_by_day", (item, v) -> map.get("核天", BooleanUtils.toStringTrueFalse(true), distinctStr).getFieldSetter().accept(item, v));
        List<String> yearMonthRange = CommonUtils.getYearMonthList(YearMonth.of(2024, 1), YearMonth.now());
        while (iterator.hasNext()) {
            Map next = iterator.next();
            String industryDept = (String) next.get("industryOrProduct");
            yearMonthRange.forEach(ym -> indexSetMap.forEach((k, set) -> {
                String key = k + "@" + ym;
                String str = (String) next.get(key);
                if (str != null) {
                    BigDecimal v = new BigDecimal(str.replace("%", "")).divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
                    String join = String.join("@", demandType, ym, industryDept);
                    MrpV3ForecastTotalRateDO item = retData.get(join);
                    if (item == null) {
                        item = new MrpV3ForecastTotalRateDO();
                        item.setVersion(MrpReportVersionEnum.V2.name());
                        item.setDemandType(demandType);
                        item.setYearMonth(ym);
                        item.setCategory(categoryEnum.getName());
                        item.setIndustryDept(industryDept);
                        retData.put(join, item);
                    }
                    set.accept(item, v);
                }
            }));
        }
    }


    private QueryTableReq buildV2Req(LocalDate statTime,
                                     MrpV2RateTotalDemandTypeEnum demandTypeEnum,
                                     boolean distinct,
                                     boolean isMonthly,
                                     MrpV3TotalRateCategoryEnum categoryEnum) {
        if (demandTypeEnum == null) {
            throw new ITException("需求类型不能为空");
        }
        QueryTableReq req = new QueryTableReq();
        req.setProductClass(ListUtils.newArrayList(Ppl13weekProductTypeEnum.CVM.getName()));
        req.setIsNewOrderType(true);
        req.setProduct("CVM");
        req.setStatTime(statTime);
        if (!EnvUtils.isProduction()) {
            req.setProjectType(ListUtils.newArrayList(ProjectTypeEnum.KEY_PROJECT.getName()));
        }
        req.setSelectFields(ListUtils.newArrayList());
        req.setNextSelectFields(ListUtils.newArrayList("projectType"));
        req.setQueryTargets(ListUtils.newArrayList("forecast_match_rate", "forecast_match_rate_core_by_day"));
        List<String> demandType;
        switch (demandTypeEnum) {
            case ADD_ELASTIC:
                demandType = ListUtils.newArrayList("新增", "弹性");
                break;
            case RETURN:
                demandType = ListUtils.newArrayList("退回");
                break;
            case NET:
                demandType = ListUtils.newArrayList("新增", "弹性", "退回");
                break;
            default:
                throw new ITException("未知需求类型");
        }
        req.setDemandTypes(demandType);
        req.setIsNewCategory(ListUtils.newArrayList(1, -1));
        req.setIsBlack(ListUtils.newArrayList(0, -1));
        req.setBizTypes(ListUtils.newArrayList("外部行业", "(空值)"));
        req.setCountry(ListUtils.newArrayList("境内", "境外"));
        req.setForecastStatus("云运管干预");
        req.setBillingScaleMode(isMonthly ? "月均切片" : "日切片");
        req.setBillingScaleDistinct(distinct);
        QueryTableReq.TimePeriod timePeriod = new QueryTableReq.TimePeriod();
        // 时间范围从：23年到现在
        timePeriod.setStart("2024-01");
        YearMonth yearMonth = statTime != null ? YearMonth.of(statTime.getYear(), statTime.getMonthValue()) : YearMonth.now();
        timePeriod.setEnd(yearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        req.setTimePeriod(timePeriod);
        req.setIsRendering(false);
        req.setRateSumFunction("执行量");
        req.setCommonInstanceType(true);
        MrpV3ReportReqSetEnum[] setEnums = categoryEnum.getSetEnums();
        for (MrpV3ReportReqSetEnum setEnum : setEnums) {
            setEnum.getV2Func().accept(req);
        }
        return req;
    }

    /**
     * 构建请求
     */
    private MrpV3ReportReq buildReq(String statTime, List<String> demandType, MrpV3TotalRateCategoryEnum categoryEnum) {
        String endYearMonth = SopDateUtils.getYearMonth(LocalDate.now());
        MrpV3ReportReq req = new MrpV3ReportReqBuilder()
                .statTime(statTime)
                .demandType(demandType)
                .startYearMonth("2024-01")
                .endYearMonth(endYearMonth)
                .category(MrpV3ReqCategoryEnum.TOTAL_RATE)
                .isDefault(true)
                .indexFieldEnums(ListUtils.newArrayList(
                        MrpV3IndexFieldEnum.monthly_532_new_rate,
                        MrpV3IndexFieldEnum.monthly_newest_rate,
                        MrpV3IndexFieldEnum.daily_532_new_rate,
                        MrpV3IndexFieldEnum.daily_newest_rate,
                        MrpV3IndexFieldEnum.core_day_532_new_rate,
                        // 去重
                        MrpV3IndexFieldEnum.monthly_532_new_distinct_rate,
                        MrpV3IndexFieldEnum.monthly_newest_distinct_rate,
                        MrpV3IndexFieldEnum.daily_532_new_distinct_rate,
                        MrpV3IndexFieldEnum.daily_newest_distinct_rate,
                        MrpV3IndexFieldEnum.core_day_532_new_distinct_rate
                ))
                .builder();
        MrpV3ReportReqSetEnum[] setEnums = categoryEnum.getSetEnums();
        for (MrpV3ReportReqSetEnum setEnum : setEnums) {
            setEnum.getV3Func().accept(req);
        }
        return req;
    }
}
