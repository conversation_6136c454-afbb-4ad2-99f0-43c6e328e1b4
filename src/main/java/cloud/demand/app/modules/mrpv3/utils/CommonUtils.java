package cloud.demand.app.modules.mrpv3.utils;

import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class CommonUtils {

    /**
     * 通过产品获取默认的产品子类
     * @param product 产品：CVM or GPU
     * @return 产品子类
     */
    public static String getProductClassByProduct(String product){
        if (Objects.equals(product,"CVM")){
            return Ppl13weekProductTypeEnum.CVM.getName();
        }else if (Objects.equals(product,"GPU")){
            return Ppl13weekProductTypeEnum.GPU.getName();
        }
        return Constant.EMPTY_VALUE;
    }

    /**
     * 纠正老机型为旧机型
     * @param generationInstanceType 机型代次集合
     * @return
     */
    public static List<String> fillGenerationInstanceType(List<String> generationInstanceType){
        if (ListUtils.isEmpty(generationInstanceType)){
            return generationInstanceType;
        }
        return generationInstanceType.stream().map(item-> Objects.equals(item,"老机型")?"旧机型":item).collect(Collectors.toList());
    }
}
