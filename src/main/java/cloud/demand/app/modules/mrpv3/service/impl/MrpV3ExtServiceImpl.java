package cloud.demand.app.modules.mrpv3.service.impl;

import cloud.demand.app.modules.mrpv3.dto.req.MrpV3ReportReq;
import cloud.demand.app.modules.mrpv3.dto.req.ext.MrpV3ReqForIndustry;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3MetaData;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.mrpv3.service.MrpV3ExtService;
import cloud.demand.app.modules.mrpv3.service.MrpV3ReportService;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.LinkedHashMap;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class MrpV3ExtServiceImpl implements MrpV3ExtService {

    @Resource
    private MrpV3ReportService mrpV3ReportService;

    @Override
    public MrpV3ReportResp getReportByIndustry(MrpV3ReqForIndustry req) {
        Assert.notNull(req, "req is null");
        req.check();
        MrpV3ReportReq mrpV3ReportReq = MrpV3ReqForIndustry.transform(req);
        return mrpV3ReportService.queryReport(mrpV3ReportReq);
    }

    @Override
    public MrpV3MetaData getMetaData() {
        MrpV3MetaData metaData = MrpV3MetaData.build();
        final LinkedHashMap<String, String> dim = metaData.getDim();
        final LinkedHashMap<String, String> index = metaData.getIndex();

        List<String> enableIndexes = MrpV3ReqForIndustry.getEnableIndexes();
        List<String> enableDims = MrpV3ReqForIndustry.getEnableDims();
        if (ListUtils.isNotEmpty(dim)){
            LinkedHashMap<String, String> newDim = dim.keySet().stream().filter(enableDims::contains)
                    .collect(LinkedHashMap::new, (map, key) -> map.put(key, dim.get(key)), LinkedHashMap::putAll);
            metaData.setDim(newDim);
        }
        if (ListUtils.isNotEmpty(index)) {
            LinkedHashMap<String, String> newIndex = index.keySet().stream().filter(enableIndexes::contains)
                    .collect(LinkedHashMap::new, (map, key) -> map.put(key, index.get(key)), LinkedHashMap::putAll);
            metaData.setIndex(newIndex);
        }
        return metaData;
    }
}
