package cloud.demand.app.modules.mrpv3.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/** 执行口径 */
@Getter
@AllArgsConstructor
public enum RateSumFunctionEnum {
    EX("执行量",true,false),
    FE("需求预测量",false,true),
    EX_FE("执行+需求预测量",true,true),
    ;
    private final String name;
    private final boolean hasScaleRateSum; // 是否有规模权重
    private final boolean hasForecastRateSum; // 是否有预测权重

    /** 默认执行量口径 */
    public static RateSumFunctionEnum getByName(String name) {
        if (StringUtils.isBlank(name)){
            return EX;
        }
        for (RateSumFunctionEnum rateSumFunctionEnum : RateSumFunctionEnum.values()) {
            if (rateSumFunctionEnum.getName().equals(name)) {
                return rateSumFunctionEnum;
            }
        }
        return EX;
    }
}
