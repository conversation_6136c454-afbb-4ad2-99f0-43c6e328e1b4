package cloud.demand.app.modules.mrpv3.task.process;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.task.process.SimpleAbstractCommonTaskProcess;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.sop.entity.task.SopDwdTaskDO;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/** dws数据生成 */
@Service
public class DwsMrpV3TaskProcess extends SimpleAbstractCommonTaskProcess<SimpleCommonTask> {
    @Override
    public SimpleCommonTask getReadyTask(ITaskEnum taskEnum) {

        List<String> dwdTask = Arrays.stream(MrpV3TaskEnum.values()).filter(MrpV3TaskEnum::isDwd).map(MrpV3TaskEnum::getName).collect(Collectors.toList());

        // 就绪条件：
        // 1.状态为NEW或者ERROR
        // 2.不存在同版本的 正在运行
        // 3.dwd全部完成
        return DBList.demandDBHelper.getRawOne(SimpleCommonTask.class,
                "select * from simple_common_task dws " +
                        "where dws.name = ? and dws.status in (?)" +
                        "and not exists (select 1 from simple_common_task temp where temp.id != dws.id and temp.version = dws.version and temp.name = dws.name and temp.status in (?))" +
                        "and exists (select 1 from simple_common_task temp where temp.version = dws.version and temp.batch_id = dws.batch_id and temp.name in (?) and temp.status = ? group by temp.batch_id having count(0) = ?)" +
                        "order by id asc",
                taskEnum.getName(),
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                TaskStatus.RUNNING.getName(),
                dwdTask,
                TaskStatus.FINISH.getName(),
                dwdTask.size());
    }
}
