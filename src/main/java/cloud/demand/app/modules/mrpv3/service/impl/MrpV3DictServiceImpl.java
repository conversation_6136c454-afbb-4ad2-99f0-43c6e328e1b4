package cloud.demand.app.modules.mrpv3.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv3.enums.MrpV3TaskEnum;
import cloud.demand.app.modules.mrpv3.service.MrpV3DictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;

@Slf4j
@Service
public class MrpV3DictServiceImpl implements MrpV3DictService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 600)
    @Override
    public String getMaxStaTime() {
        return demandDBHelper.getRawOne(String.class,
                "select MAX(version) from simple_common_task dws \n" +
                        "where name = ?\n" +
                        "and status = 'FINISH'\n" +
                        "and not exists (select 1 from simple_common_task temp where temp.name = ? and temp.version = dws.version\n" +
                        "and temp.id > dws.id and status != 'FINISH')",
                MrpV3TaskEnum.MRP_V3_DWS_TOTAL.getName(),
                MrpV3TaskEnum.MRP_V3_DWS_TOTAL.getName());
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 180)
    @Override
    public Map<String, List<String>> getCommonInstanceType() {
        List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold > 0");
        Map<String, String> instanceTypeToGroup = new HashMap<>(); // 机型 -> 机型收敛
        for (PplForecastConfigSpikeThresholdDO threshold : thresholds) {
            List<String> splitInstanceTypes = threshold.getSplitInstanceTypes();
            for (String type : splitInstanceTypes) {
                instanceTypeToGroup.put(type, threshold.getCommonInstanceType());
            }
        }
        Map<String, List<String>> ret = new HashMap<>();
        for (Map.Entry<String, String> entry : instanceTypeToGroup.entrySet()) {
            ret.computeIfAbsent(entry.getValue(), k -> new ArrayList<>()).add(entry.getKey());
        }
        return ret;
    }

    @HiSpeedCache(expireSecond = 360)
    @Override
    public Map<Long, String> getUin2PSWarZone() {
        List<Map> raw = ckcldStdCrpDBHelper.getRaw(Map.class,
                "SELECT DISTINCT uin ,war_zone  FROM std_crp.dwd_txy_appid_info_cf where war_zone != '(空值)'");
        return ListUtils.toMap(raw, (mp)-> (Long)mp.get("uin"), (mp)-> (String)mp.get("war_zone"));
    }

    @HiSpeedCache(expireSecond = 360)
    @Override
    public Map<String, String> getUin2IncomeSalesDesc() {
        List<Map> raw = ckcldStdCrpDBHelper.getRaw(Map.class,
                "SELECT DISTINCT uin ,income_sales_desc  FROM std_crp.dwd_txy_appid_info_cf where income_sales_desc not in ('(空值)','空')");
        return ListUtils.toMap(raw, (mp)-> String.valueOf(mp.get("uin")), (mp)-> (String)mp.get("income_sales_desc"));
    }
}
