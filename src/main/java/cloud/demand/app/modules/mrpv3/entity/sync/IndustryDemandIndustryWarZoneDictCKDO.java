package cloud.demand.app.modules.mrpv3.entity.sync;

import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import com.pugwoo.dbhelper.annotation.Table;

/** 行业战区配置-切片 */
@Data
@ToString
@Table("industry_demand_industry_war_zone_dict")
public class IndustryDemandIndustryWarZoneDictCKDO {
    /** 切片时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 数据生成时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private LocalDateTime createTime;

    /** 行业部门<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 战区名<br/>Column: [war_zone_name] */
    @Column(value = "war_zone_name")
    private String warZoneName;

    /** 战区id<br/>Column: [war_zone_id] */
    @Column(value = "war_zone_id")
    private Integer warZoneId;

    /** 通用客户简称<br/>Column: [common_customer_name] */
    @Column(value = "common_customer_name")
    private String commonCustomerName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 是否大客户<br/>Column: [is_big_customer] */
    @Column(value = "is_big_customer")
    private Boolean isBigCustomer;

    /** 来源： input;import<br/>Column: [data_source] */
    @Column(value = "data_source")
    private String dataSource;

    /** 大客户所属产品<br/>Column: [big_customer_product] */
    @Column(value = "big_customer_product")
    private String bigCustomerProduct;

    /** 是否大客户<br/>Column: [is_top_customer] */
    @Column(value = "is_top_customer")
    private Boolean isTopCustomer;

    public static IndustryDemandIndustryWarZoneDictCKDO transform(IndustryDemandIndustryWarZoneDictDO mysqlDO, LocalDate statTime){
        IndustryDemandIndustryWarZoneDictCKDO ret = new IndustryDemandIndustryWarZoneDictCKDO();
        ret.setStatTime(statTime);
        ret.setCreateTime(LocalDateTime.now());
        ret.setIndustry(mysqlDO.getIndustry());
        ret.setWarZoneName(mysqlDO.getWarZoneName());
        ret.setWarZoneId(mysqlDO.getWarZoneId());
        ret.setCommonCustomerName(mysqlDO.getCommonCustomerName());
        ret.setCustomerName(mysqlDO.getCustomerName());
        ret.setIsBigCustomer(mysqlDO.getIsBigCustomer());
        ret.setDataSource(mysqlDO.getDataSource());
        ret.setBigCustomerProduct(mysqlDO.getBigCustomerProduct());
        ret.setIsTopCustomer(mysqlDO.getIsTopCustomer());
        return ret;

    }

}
