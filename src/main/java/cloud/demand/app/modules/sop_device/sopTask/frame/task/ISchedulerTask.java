package cloud.demand.app.modules.sop_device.sopTask.frame.task;

import cloud.demand.app.modules.sop_device.sopTask.frame.register.interfaces.IScheduledDO;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.scheduling.support.CronTrigger;
import yunti.boot.YuntiProperties;

/**
 * 定时任务
 */
@Slf4j()
public abstract class ISchedulerTask<T extends IScheduledDO> {

    @Resource
    private ThreadPoolTaskScheduler taskScheduler;

    @Resource
    YuntiProperties yuntiProperties;

    /** 是否允许运行 */
    private volatile Boolean enableRun;

    /** 任务注册：key：任务 id，value：任务运行，任务信息 */
    protected final Map<Long, Tuple2<ScheduledFuture<?>, T>> taskRegistry = new ConcurrentHashMap<>();

    /** 获取任务集合 */
    public abstract List<T> getTaskList();

    /**
     * 四分钟刷新一次
     */
    @Scheduled(fixedRate = 240 * 1000)
    public void flush() {

        if (!preCheck()){
            return;
        }

        log.info("flush task start ....");
        List<T> taskList = getTaskList();

        taskList = taskList == null ? ListUtils.newArrayList() : taskList.stream()
                .filter(item -> BooleanUtils.isTrue(item.getIsEnable()))
                .collect(Collectors.toList()); // 过滤掉未启用的任务

        Set<Long> enableTaskIdSet = taskList.stream()
                .map(IScheduledDO::getId)
                .collect(Collectors.toSet());  // 启用的任务ID集合

        if (ListUtils.isNotEmpty(taskList)) {
            for (T t : taskList) {
                flushOne(t);
            }
        }
        if (ListUtils.isNotEmpty(taskRegistry)){
            Iterator<Entry<Long, Tuple2<ScheduledFuture<?>, T>>> iterator = taskRegistry.entrySet().iterator();
            while (iterator.hasNext()) {
                Entry<Long, Tuple2<ScheduledFuture<?>, T>> entry = iterator.next();
                if (!enableTaskIdSet.contains(entry.getKey())) {
                    if (cancelOne(entry.getKey(),false)){
                        iterator.remove();
                    }
                }
            }
        }
        log.info("flush task finish 。");
    }

    /**
     * 前置简称
     */
    private synchronized boolean preCheck(){

        if (enableRun != null){
            return enableRun;
        }

        //  测试环境不生效
//        if (yuntiProperties.getStage().equals("DEV")
//                || yuntiProperties.getStage().equals("TEST")
//                || yuntiProperties.getStage().equals("TKEx-TEG-TEST")) {
//            log.info("测试环境不生效");
//
//            enableRun = false;
//        }

        // 非 pod0 不运行
        if (enableRun == null){
            String podName = System.getenv("POD_NAME");
            if (podName == null || !podName.matches("^[A-Za-z_\\-0-9]*-0$")) {
                log.info("非 pod0 不运行");
                enableRun = false;
            }else {
                enableRun = true;
            }
        }

        return enableRun;
    }

    /**
     * 取消一个任务
     * @param id 任务 id
     * @param remove 是否要移除
     * @return
     */
    private boolean cancelOne(Long id, boolean remove)  {
        Tuple2<ScheduledFuture<?>, T> tuple2 = taskRegistry.get(id);
        if (tuple2 == null) {
            return false;
        }
        if(!tuple2._1.cancel(true)){
            log.info("cancel task fail, id:{}", id);
            return false;
        }
        // 是否需要移除
        if (remove){
            taskRegistry.remove(id);
        }
        return true;
    }

    /**
     * 刷洗一个任务
     * @param t 任务实体
     */
    private void flushOne(T t) {
        Long id = t.getId();
        Tuple2<ScheduledFuture<?>, T> tuple2 = taskRegistry.get(id);
        if (tuple2 == null){
            createOne(t);
            postCreateOne(t,false);
        }else {
            T t1 = tuple2._2();
            if (!Objects.equal(t.getCron(),t1.getCron())){
                // 修改了 cron 表达式，取消旧任务，创建新任务
                if (cancelOne(t1.getId(),true)){
                    createOne(t);
                    postCreateOne(t,true);
                }
            }
        }
    }

    protected void postCreateOne(T t,boolean isUpdate){}

    /**
     * 创建一个任务
     */
    private void createOne(T t){
        // 创建新任务
        if (!checkT(t)) {
            return;
        }
        CronTrigger cronTrigger = new CronTrigger(t.getCron());
        ScheduledFuture<?> future = taskScheduler.schedule(
                () -> executeTask(t.getId()), cronTrigger);
        Tuple2<ScheduledFuture<?>, T> tuple2 = new Tuple2<>(future, t);
        taskRegistry.put(t.getId(), tuple2);
    }

    /** 校验任务 */
    private boolean checkT(T t) {
        String cron = t.getCron();
        if (!CronSequenceGenerator.isValidExpression(cron)) {
            log.error("cron表达式错误, cron:{}", cron);
            cronError(t);
            return false;
        }
        return postCheckT(t);
    }


    /**
     * 后置校验
     */
    protected boolean postCheckT(T t){
        return true;
    }

    /** cron 表达式异常 */
    protected void cronError(T t){};

    /** 执行任务 */
    protected abstract void executeTask(Long id);

}
