package cloud.demand.app.modules.sop_device.entity;

import cloud.demand.app.modules.sop_device.domain.IndexVal;
import cloud.demand.app.modules.sop_device.domain.IndexVal.ValItem;
import java.util.function.Function;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public enum SupplyIndexEnums {
    Dy("不参与对冲的需求", SupplyIndexGetter::getNotHedgeDemand),
    Dc("参与对冲的需求", SupplyIndexGetter::getHedgeDemand),
    Dc1("已执行未匹配服务器", SupplyIndexGetter::getExeNotMatchDemand),
    Dc2("参与对冲的需求预测", SupplyIndexGetter::getNotExeDemand),
    Dc3("不参与对冲的需求预测", SupplyIndexGetter::getNotExeNotHedgeDemand),
    Do("预算增量的申领量", SupplyIndexGetter::getBudgetIncrement),
    Dt("改造计划需求", SupplyIndexGetter::getTransformPlanDemand),

    R("未执行退回计划", SupplyIndexGetter::getReturnPlan),
    Ro("预算增量退回和存量非退役退回", SupplyIndexGetter::getBudgetIncrementReturn),
    Sc("自研上云折算整机库存", SupplyIndexGetter::getConvertedOverallInventory),

    Si1("内部库存-退回", SupplyIndexGetter::getInnerStorageByReturn),
    Si2("内部库存-采购", SupplyIndexGetter::getInnerStorageByPurchase),
    Si("内部库存", SupplyIndexGetter::getInnerStorage),
    So("采购在途", SupplyIndexGetter::getOuterStorage),
    St("改造计划交付", SupplyIndexGetter::getTransformPlanStorage),
    OR1("精准对冲-退回", SupplyIndexGetter::getExactHedgeByReturn),
    OR2("模糊对冲消耗库存-退回", SupplyIndexGetter::getFuzzyHedgeInvByReturn),
    ODr2("模糊对冲满足需求-退回", SupplyIndexGetter::getFuzzyHedgeDemandByReturn),
    OS1("精准对冲(供=需)OS1", SupplyIndexGetter::getExactHedgeByStorage),
    OS2("模糊对冲消耗库存OS2", SupplyIndexGetter::getFuzzyHedgeInvByStorage),
    ODs2("模糊对冲满足需求ODs2", SupplyIndexGetter::getFuzzyHedgeDemandByStorage),
    Ost1("精准对冲-改造", SupplyIndexGetter::getExactHedgeByTransform),
    Ost2("模糊对冲消耗库存OSt2", SupplyIndexGetter::getFuzzyHedgeInvByTransform),
    ODt2("模糊对冲满足需求ODt2", SupplyIndexGetter::getFuzzyHedgeDemandByTransform),
    A("有效对冲供应汇总", SupplyIndexGetter::getTotalHedgeInv),
    B("有效对冲需求汇总", SupplyIndexGetter::getTotalHedgeDemand),
    SpLr("退回剩余", SupplyIndexGetter::getReturnLeft),
    SpLv("可用库存剩余", SupplyIndexGetter::getInvLeft),
    SpLg("改造剩余", SupplyIndexGetter::getTransformLeft),
    SpL("供应剩余", SupplyIndexGetter::getTotalLeft),
    PGb("原始采购净预测", SupplyIndexGetter::getPurchaseForecast),
    Gap("手改Gap", SupplyIndexGetter::getPurchaseForecastGap),
    PGa("手工修改后采购净预测", SupplyIndexGetter::getPurchaseForecastFinal),
    ;
    String name;
    Function<SupplyIndexGetter, ? extends ValItem> function;

    public String getName() {
        return name;
    }

    public ValItem getVal(SupplyIndexGetter t) {
        if (t == null) {
            return null;
        }
        return function.apply(t);
    }

    /**
     * 计算型指标 ，如果是由其他原子指标计算而来，可以直接在这里实现即可
     */
    public interface SupplyIndexGetter {

        /**
         * 不参与对冲需求Dy
         * 物理机需求预测里已执行已匹配服务器需求Dy=物理机需求预测量-Dc1-(Dw)
         */
        default ValItem getNotHedgeDemand() {
            return null;
        }

        /**
         * 参与对冲的需求Dc
         * @return
         */
        default ValItem getHedgeDemand() {
            return null;
        }

        /**
         * 已执行未匹配服务器Dc1
         * 物理机采购对冲已提单需求D1+D2+D4+D5
         */
        default ValItem getExeNotMatchDemand() {
            return null;
        }

        /**
         * 参与对冲的需求预测Dc2
         * 物理机采购对冲未执行预测D9参与对冲的部分(即剔除业务类型=云业务&项目类型=采购buff后的)+D8
         *
         * @return
         */
        default ValItem getNotExeDemand() {
            return null;
        }

        /**
         * 不参与对冲的需求预测Dc3
         * 物理机采购对冲未执行预测D9不参与对冲的部分(即业务类型=云业务&项目类型=采购buff)
         *
         * @return
         */
        default ValItem getNotExeNotHedgeDemand() {
            return null;
        }

        /**
         * 改造计划需求Dt
         * 物理机采购对冲的改造计划预计消耗的原始机型D10
         *
         * @return
         */
        default ValItem getTransformPlanDemand() {
            return null;
        }

        /**
         * 未执行退回计划R
         * 物理机采购对冲退回S9
         */
        default ValItem getReturnPlan() {
            return null;
        }

        /**
         * 内部库Si
         * 物理机采购对冲S1+S3+S10
         */
        default ValItem getInnerStorage() {
            return null;
        }

        /**
         * 内部库Si1 - 采购
         * 物理机采购对冲S1+S3+S10 采购PURCHASE
         */
        default ValItem getInnerStorageByPurchase() {
            return null;
        }

        /**
         * 内部库Si2
         * 物理机采购对冲S1+S3+S10 退回
         */
        default ValItem getInnerStorageByReturn() {
            return null;
        }


        /**
         * 采购在途So
         * 物理机采购对冲S4+S5
         */
        default ValItem getOuterStorage() {
            return null;
        }

        /**
         * 改造计划交付St
         * 物理机采购对冲的改造预计交付目标机型S13
         */
        default ValItem getTransformPlanStorage() {
            return null;
        }

        /**
         * 精准对冲(供=需)OR1
         * 物理机采购对冲S9里精准(设备类型供需一致)匹配成功的数量
         */
        default ValItem getExactHedgeByReturn() {
            return null;
        }

        /**
         * 模糊对冲消耗库存OR2
         * 物理机采购对冲S9里模糊(设备类型供需不一致)匹配成功的消耗库存数量
         */
        default ValItem getFuzzyHedgeInvByReturn() {
            return null;
        }

        /**
         * 模糊对冲满足需求ODr2
         * 物理机采购对冲S9里模糊(设备类型供需不一致)匹配成功的满足需求数量
         */
        default ValItem getFuzzyHedgeDemandByReturn() {
            return null;
        }

        /**
         * 精准对冲(供=需)OS1
         * 物理机采购对冲(S1+S3+S10+S4+S5)里精准(设备类型供需一致)匹配成功的数量
         */
        default ValItem getExactHedgeByStorage() {
            return null;
        }

        /**
         * 模糊对冲消耗库存OS2
         * 物理机采购对冲(S1+S3+S10+S4+S5)里模糊(设备类型供需不一致)匹配成功的消耗库存数量
         */
        default ValItem getFuzzyHedgeInvByStorage() {
            return null;
        }

        /**
         * 模糊对冲满足需求ODs2
         * 物理机采购对冲(S1+S3+S10+S4+S5)里模糊(设备类型供需不一致)匹配成功的满足需求数量
         */
        default ValItem getFuzzyHedgeDemandByStorage() {
            return null;
        }


        /**
         * 精准对冲(供=需)OSt1
         * 物理机采购对冲S13里精准(设备类型供需一致)匹配成功的数量
         */
        default ValItem getExactHedgeByTransform() {
            return null;
        }

        /**
         * 模糊对冲消耗库存OSt2
         * 物理机采购对冲S13里模糊(设备类型供需不一致)匹配成功的消耗库存数量
         */
        default ValItem getFuzzyHedgeInvByTransform() {
            return null;
        }

        /**
         * 糊对冲满足需求ODt2
         * 物理机采购对冲S13里模糊(设备类型供需不一致)匹配成功的满足需求数量
         */
        default ValItem getFuzzyHedgeDemandByTransform() {
            return null;
        }

        /**
         * 有效对冲供应汇总
         * (退回+可用库存+改造)对冲消耗的库存=(OR1+OR2)+(OS1+OS2)+(OSt1+OSt2)
         */
        default ValItem getTotalHedgeInv() {
            return null;
        }

        /**
         * 有效对冲需求汇总
         * 被(退回+可用库存+改造)满足的需求=(OR1+ODr2)+(OS1+ODs2)+(OSt1+ODt2)
         */
        default ValItem getTotalHedgeDemand() {
            return null;
        }

        /**
         * 退回剩余R'=R-(OR1+OR2)
         */
        default ValItem getReturnLeft() {
            return null;
        }

        /**
         * 可用库存剩余S'=(Si+So)-(OS1+OS2)
         */
        default ValItem getInvLeft() {
            return null;
        }


        /**
         * 改造剩余St'=St-(OSt1+OSt2)
         */
        default ValItem getTransformLeft() {
            return null;
        }

        /**
         * 供应剩余Supply'汇总=R'+S'+St'
         */
        default ValItem getTotalLeft() {
            return IndexVal.add(getReturnLeft(), getInvLeft(), getTransformLeft());

        }


        /**
         * 原始采购净预测PG.b
         */
        default ValItem getPurchaseForecast() {
            return null;
        }

        /**
         * 手工修改后采购净预测PG.a
         */
        default ValItem getPurchaseForecastFinal() {
            return null;
        }

        /**
         * 手改Gap=PG.b-PG.a
         */
        default ValItem getPurchaseForecastGap() {
            return IndexVal.subtract(getPurchaseForecast(), getPurchaseForecastFinal());
        }

        /**
         * 预算增量的申领量Do
         * @return
         */
        default ValItem getBudgetIncrement() {
            return null;
        }

        /**
         * 预算增量退回和存量非退役退回Ro
         * @return
         */
        default ValItem getBudgetIncrementReturn() {
            return null;
        }

        /**
         * 自研上云折算整机库存Sc
         * @return
         */
        default ValItem getConvertedOverallInventory() {
            return null;
        }
        /**
         * 未清楚的指标先用这个
         *
         * @return
         */
        default ValItem getUnKnown() {
            return null;
        }

    }
}