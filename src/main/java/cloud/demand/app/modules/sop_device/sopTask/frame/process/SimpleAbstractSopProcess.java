package cloud.demand.app.modules.sop_device.sopTask.frame.process;

import cloud.demand.app.modules.sop.entity.task.SopAttachment;
import cloud.demand.app.modules.sop.enums.TaskActionTypeEnum;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.MDCUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import cloud.demand.app.modules.sop.util.TaskUtil;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.IDepTaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.exception.SopTaskException;
import cloud.demand.app.modules.sop_device.sopTask.frame.task.SimpleSopTask;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Objects;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.PostConstruct;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

public abstract class SimpleAbstractSopProcess<T extends SimpleSopTask> implements ISopProcess<T>{

    @Override
    public T getById(Long id) {
        return getDbHelper().getByKey(getT(), id);
    }

    @Override
    public void initTask(T t, ITaskEnum taskEnum) {
        t.setStatus(TaskStatus.NEW.getName());
        t.setName(taskEnum.getName());
        t.setCreateTime(new Date());
        getDbHelper().insert(t);
    }

    @Override
    public T getReadyTask(ITaskEnum taskEnum) {
        if (taskEnum instanceof IDepTaskEnum){
            IDepTaskEnum[] dept = ((IDepTaskEnum) taskEnum).getDept();
            if (dept != null && dept.length > 0){
                return getDepReadTask((IDepTaskEnum) taskEnum);
            }else {
                return getSimpleReadTask(taskEnum);
            }
        }else {
            return getSimpleReadTask(taskEnum);
        }
    }

    protected T getSimpleReadTask(ITaskEnum taskEnum){
        return getDbHelper().getOne(getT(),
                "where status in (?) and name = ? order by id",
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                taskEnum.getName());
    }

    protected T getDepReadTask(IDepTaskEnum taskEnum){
        IDepTaskEnum[] dept = taskEnum.getDept();
        List<String> depName = Arrays.stream(dept).map(IDepTaskEnum::getName).collect(Collectors.toList());
        String table = CkDBUtils.getTableName(getT());
        String sql = "select t.* from ${table} t where status in (?) and name = ? " +
                " and exists (select 1 from ${table} dep where dep.version = t.version and dep.batch_id = t.batch_id and dep.name in (?) and dep.status = ? group by dep.name having count(0) = ?) " +
                " order by id";
        sql = sql.replace("${table}",table);
        return getDbHelper().getRawOne(getT(),
                sql,
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                taskEnum.getName(),
                depName,
                TaskStatus.FINISH.getName(),
                depName.size());

    }

    @Override
    public void startTask(T t) throws SopTaskException {
        t.setStartTime(new Date());
        t.setStatus(TaskStatus.RUNNING.getName());
        t.setTraceId(MDCUtil.getTraceId());
        int update = getDbHelper().update(t,"where status in (?)",Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()));
        if (update == 0){
            throw new SopTaskException("启动任务失败，状态非初始化或者执行失败");
        }
    }

    @Override
    public void finishTask(T t) {
        t.setStatus(TaskStatus.FINISH.getName());
        setEndTime(t);
        getDbHelper().update(t);
    }

    @Override
    public void failTask(T t, Exception e) {
        t.setErrorMsg(e.getMessage());
        setEndTime(t);
        boolean needAlert = TaskUtil.alertSimpleError(t, getAlertKey(t), e);
        if (!needAlert){
            t.setStatus(TaskStatus.ERROR.getName());
        }else{
            t.setStatus(TaskStatus.MANUAL_STOP.getName());
        }
        // 留个状态口子，可以通过该db来防止一直失败
        getDbHelper().update(t,"where status <> ?", TaskStatus.MANUAL_STOP.getName());
    }

    @Override
    public void batchFailRunningTask(Long[] ids, String msg) {
        getDbHelper().executeRaw(String.format("update %s set status = ?, error_msg = ? where id in (?) and status = ?", CkDBUtils.getTableName(t)),
                TaskStatus.ERROR.getName(),msg,ids,TaskStatus.RUNNING.getName());
    }

    protected String getAlertKey(T t){
        String ret = "${name}-${lock_value}-${trace_id}";
        ret = ret.replace("${name}", ObjectUtils.defaultIfNull(t.getName(),""));
        ret = ret.replace("${lock_value}",ObjectUtils.defaultIfNull(t.getLockValue(),""));
        ret = ret.replace("${trace_id}",ObjectUtils.defaultIfNull(t.getTraceId(),""));
        return ret;
    }

    @Override
    public List<T> getRunningTask(ITaskEnum taskEnum) {
        return getDbHelper().getAll(getT(),
                "where status in (?) and name = ? order by id",
                Collections.singletonList(TaskStatus.RUNNING.getName()),
                taskEnum.getName());
    }

    @Override
    public T getReadyOrRunningTask(ITaskEnum taskEnum) {
        return getDbHelper().getOne(getT(),
                "where status in (?) and name = ? order by id",
                ListUtils.newList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName(),TaskStatus.RUNNING.getName()),
                taskEnum.getName());
    }

    @Override
    public void updateStatus(List<Long> ids, String status, String preStatus) {
        if (ListUtils.isEmpty(ids)){
            throw new BizException("ids is empty");
        }
        if (!TaskStatus.isContains(status)){
            throw new BizException("status is not contains");
        }
        String table = CkDBUtils.getTableName(getT());
        if (StringUtils.isNotBlank(preStatus)){
            if (!TaskStatus.isContains(preStatus)){
                throw new BizException("preStatus is not contains");
            }
            getDbHelper().executeRaw("update " + table + " set status = ? where id in (?) and status = ?",status,ids,preStatus);
        }else {
            getDbHelper().executeRaw("update " + table + " set status = ? where id in (?)",status,ids);
        }
    }

    @Override
    public void alert(T t, String msg) {
        TaskUtil.alertTimeOut(SopAttachment.transform(t,ListUtils.newList(TaskActionTypeEnum.MANUAL_STOP)), getAlertKey(t),msg);
    }

    protected void setEndTime(T t){
        t.setEndTime(new Date());
        t.setCostMs((int)((t.getEndTime().getTime() - t.getStartTime().getTime())));
    }

    private Class<T> t;

    protected abstract DBHelper getDbHelper();
    protected Class<T> getT(){
        return t;
    }

    @PostConstruct
    public void init(){
        Type genericSuperclass = this.getClass().getGenericSuperclass();
        ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        Type genericType = actualTypeArguments[0];
        t = (Class<T>) genericType;
    }

}
