package cloud.demand.app.modules.sop_device.entity;

import cloud.demand.app.modules.sop_device.domain.IndexVal;
import cloud.demand.app.modules.sop_device.utils.LocalDateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

import static cloud.demand.app.modules.sop_device.domain.SupplyDemandHedgingResultDTO.createValItem;

/**
 * Dy 不参与对冲的需求
 *
 * <AUTHOR>
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NotExeNotHedgeDemandDO implements SupplyWrapper {
    // 日期
    private Date day;

    @Override
    public String getObsBizType() {
        return businessType;
    }

    @Override
    public String getErpBizType() {
        return businessType;
    }


    @Override
    public String getPlanProduct() {
        return planProductName;
    }

    @Override
    public String getProjectType() {
        return projectName;
    }

    // 端面统计周期维度，对应这个时间
    @Override
    public LocalDate getDemandDate() {
        // return LocalDateUtil.parse(requireDate);
        return LocalDateUtil.parse2LocaLDate(planYear, planMonth);
    }

    @Override
    public LocalDate getSupplyDate() {
        return LocalDateUtil.parse2LocaLDate(planYear, planMonth);
    }

    @Override
    public Integer getDemandYear() {
        return Integer.parseInt(planYear);
    }

    @Override
    public Integer getDemandMonth() {
        return Integer.parseInt(planMonth);
    }

    @Override
    public Integer getDemandWeek() {
        return Integer.parseInt(planWeek);
    }

    @Override
    public String getDemandCampus() {
        return requireCampus;
    }

    @Override
    public String getDemandDeviceType() {
        return deviceType;
    }

    @Override
    public String getDemandDeviceFamily() {
        return deviceFamilyName;
    }

    @Override
    public String getSupplyDeviceFamily() {
        return deviceFamilyName;
    }

    // 版本号
    private String version;

    // 订单编号
    private String orderNum;

    // 业务类型
    private String businessType;

    // 国家
    private String country;

    // 项目名称
    private String projectName;

    // 设备类型
    private String deviceType;

    // 需求数量
    private BigDecimal requireNum;

    // 需求日期
    private Date requireDate;

    // 计划年份
    private String planYear;

    // 计划月份
    private String planMonth;

    // 计划周数
    private String planWeek;

    // 部门名称
    private String deptName;

    // 计划产品名称
    private String planProductName;

    // 逻辑 PC 编码
    private String logicPcCode;

    // 供应校区
    private String supplyCampus;

    // 需求校区
    private String requireCampus;


    /**
     * 场景细分 or 机型族<br/>Column: [DeviceFamilyName]
     */
    private String deviceFamilyName;


    // CPU 逻辑核心数
    private Integer cpuLogicCore;

    private BigDecimal deviceCore;

    private String unitName;

    private BigDecimal deviceCapacity;

    @Override
    public String getSupplyAsset() {
        return logicPcCode;
    }

    @Override
    public IndexVal.ValItem getNotHedgeDemand() {
        return createValItem(requireNum, deviceCore, deviceCapacity, unitName);
    }

    @Override
    public IndexVal.ValItem getHedgeDemand() {
        return createValItem();
    }

    /**
     * 需求类型
     */
    @Override
    public String getDemandType() {
        return orderNum;
    }

    /**
     * 满足方式
     */
    @Override
    public String getDemandMatch() {
        return "-";
    }

    /**
     * 满足类型
     */
    @Override
    public String getDemandMatchType() {
        return orderNum;
    }

    /**
     * 业务部门
     */
    @Override
    public String getDeptName() {
        return deptName;
    }

    /**
     * 供应Campus
     */
    @Override
    public String getSupplyCampus() {
        return supplyCampus;
    }

    /**
     * 供应机型
     */
    @Override
    public String getSupplyDeviceType() {
        return deviceType;
    }

    @Override
    public String getCapacityUnit() {
        return unitName;
    }

}