package cloud.demand.app.modules.sop_device.service.impl;

import cloud.demand.app.modules.sop_device.domain.DataVersion;
import cloud.demand.app.modules.sop_device.domain.HedgingWrapperReq;
import cloud.demand.app.modules.sop_device.domain.IndexVal.ValItem;
import cloud.demand.app.modules.sop_device.domain.Result;
import cloud.demand.app.modules.sop_device.entity.AdsPhysicalHedgeReportLocalDO;
import cloud.demand.app.modules.sop_device.entity.SupplyIndexEnums;
import cloud.demand.app.modules.sop_device.entity.SupplyWrapper;
import cloud.demand.app.modules.sop_device.service.CalculateStockSupplyService;
import cloud.demand.app.modules.sop_device.service.DataCreateJob;
import cloud.demand.app.modules.sop_device.wrapper.BaseData;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.data.Database;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class  DataCreateJobImpl implements DataCreateJob {


    @Resource
    private BaseData baseData;

    @Resource
    private DBHelper ckcldDBHelper;

    @Resource
    private DBHelper ckcldStdCrpSwapDBHelper;

    @Resource
    CalculateStockSupplyService calculateStockSupplyService;


    @Override
    public <T extends SupplyWrapper> void saveToCk(List<T> dto, String version) {
        log.info("原子性写入saveToCk start "+ version.toString()+" "+dto.size());
        saveProcess(dto, version);
    }

    /**
     * 原子性写入避免数据真空期
     */
    private <T extends SupplyWrapper> void saveProcess(List<T> dto,
                                                       String version) {
        log.info("原子性写入saveProcess获取配置信息"+ version.toString()+" "+dto.size());
        try {
            String ckcldStdCrpSwapDBHelperName = AnnotationUtils.findAnnotation(ckcldStdCrpSwapDBHelper.getClass(), Database.class).value();
            log.info("ckcldStdCrpSwapDBHelper "+ ckcldStdCrpSwapDBHelperName);

            String ckcldDBHelperName = AnnotationUtils.findAnnotation(ckcldDBHelper.getClass(), Database.class).value();
            log.info("ckcldDBHelper "+ ckcldDBHelperName);

        }catch (Exception e){
            log.info("saveProcessException "+ e.getMessage());
        }

        log.info("原子性写入saveProcess_executeRaw start"+ version.toString()+" "+dto.size());

        int rawNum = ckcldStdCrpSwapDBHelper.executeRaw(
                "ALTER TABLE std_crp_swap.ads_physical_hedge_report_local ON CLUSTER default_cluster DROP PARTITION ?",
                version);

        log.info("原子性写入saveProcess"+ version.toString()+" "+rawNum);
        try {
            TimeUnit.SECONDS.sleep(5);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        List<T> all =new ArrayList<>();
        all.addAll(dto);



        List<DataVersion> dataVersionList = calculateStockSupplyService.querySnapshotNumberList();

        HedgingWrapperReq wrapperReq = new HedgingWrapperReq(Long.parseLong(version));
        for (DataVersion dataVersion : dataVersionList) {
            if (dataVersion.getVersion().toString().equals(version)) {
                wrapperReq.setDateVersion(dataVersion.getVersionDate());
                break;
            }
        }
        wrapperReq.setPageNum(0);
        wrapperReq.setPageSize(1);

        // 聚合预测数据
        List<T> forecaseList = getAndFilterForecaseWithReq(wrapperReq);
        // 聚合不参与对冲数据
        List<T> notExeNotHedgeDemandDOList = getAndFilterNotExeNotHedgeDemandWithReq(wrapperReq);


        all.addAll(forecaseList);
        all.addAll(notExeNotHedgeDemandDOList);

        log.info("原子性写入saveProcess数据聚合完成"+ all.size());

        List<AdsPhysicalHedgeReportLocalDO> hedgers = all.stream()
                .flatMap(item -> Arrays.stream(SupplyIndexEnums.values()).map(idx -> {
                    ValItem valItem = idx.getVal(item);
                    if (valItem != null) {
                        AdsPhysicalHedgeReportLocalDO rs = AdsPhysicalHedgeReportLocalDO.form(item);
                        rs.setPIndex(idx.name());
                        rs.setNum(valItem.getNum());
                        rs.setCore(valItem.getCore());
                        rs.setCapacity(valItem.getCapacity());
                        rs.setVersion(version);
                        return rs;
                    }
                    return null;
                })).filter(Objects::nonNull).collect(Collectors.toList());

        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(hedgers);
        log.info("插入swapCK完成 version："+ version + " size：" +hedgers.size() );
        hedgers.clear();

        try {
            Thread.sleep(3000);
        } catch (InterruptedException ignored) {
        }
        // 清除正式库避免双倍数据
        ckcldStdCrpSwapDBHelper.executeRaw(
                "ALTER TABLE cloud_demand.ads_physical_hedge_report_local ON CLUSTER default_cluster DROP PARTITION ?",
                version);
        ckcldDBHelper.executeRaw("ALTER TABLE cloud_demand.ads_physical_hedge_report_local ON CLUSTER default_cluster REPLACE PARTITION ? FROM std_crp_swap.ads_physical_hedge_report_local", version);

        log.info("替换并插入CK完成 version："+ version + " size：" +hedgers.size() );
    }


    public <T extends SupplyWrapper> List<T> getAndFilterForecaseWithReq(HedgingWrapperReq req) {
        return baseData.queryNetPurchaseForecastBySnapshot(req);
    }

    public <T extends SupplyWrapper> List<T> getAndFilterNotExeNotHedgeDemandWithReq(HedgingWrapperReq req) {
        Result<List<T>> totalResult = baseData.queryNotExeNotHedgeDemand(req);
        int total = totalResult.getTotal();
        req.setPageSize(total);

        List<T> resultList = new ArrayList<>();
        Result<List<T>> listResult = baseData.queryNotExeNotHedgeDemand(req);
        if (listResult.getTotal() > 0 && !CollectionUtils.isEmpty(listResult.getData())) {
            resultList.addAll(listResult.getData());
        }

        List<T> totalPurchaseForecasResult = ListUtils.newArrayList();
        totalPurchaseForecasResult.addAll(resultList);

        return resultList;
    }


}
