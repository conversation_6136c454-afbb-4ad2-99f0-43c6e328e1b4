package cloud.demand.app.modules.sop_yunti.task.process;

import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.task.process.SimpleAbstractCommonTaskProcess;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.enums.TaskStatus;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_yunti.enums.YuntiTaskEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

@Service
public class AdsYuntiTaskProcess extends SimpleAbstractCommonTaskProcess<SimpleCommonTask> {
    @Override
    public SimpleCommonTask getReadyTask(ITaskEnum taskEnum) {
        // ads就绪条件：
        // 1.状态为NEW或者ERROR
        // 2.同期dws就绪
        return getDbHelper().getRawOne(SimpleCommonTask.class,
                "select ads.* from simple_common_task ads " +
                        "where ads.status in (?) and ads.name = ? " +
                        "and exists (select 1 from simple_common_task dws where dws.batch_id = ads.batch_id and dws.version = ads.version and dws.status = ? and dws.name = ?) " +
                        "order by ads.id asc",
                Arrays.asList(TaskStatus.NEW.getName(),TaskStatus.ERROR.getName()),
                taskEnum.getName(),
                TaskStatus.FINISH.getName(),
                YuntiTaskEnum.DWS_SOP_YUNTI_CVM.getName());
    }

    /** 查询所有已完成的版本号 */
    public List<String> getAllVersion(ITaskEnum taskEnum){
        return getDbHelper().getRaw(String.class,
                "select distinct version from simple_common_task where status in (?) and name = ? order by version desc",
                ListUtils.newList(TaskStatus.NEW.getName(),TaskStatus.FINISH.getName(),TaskStatus.ERROR.getName()),
                taskEnum.getName());
    }

    public void initTask(String version){
        String batchId = SoeCommonUtils.getBatchId();
        // 初始化ads
        initTask(new SimpleCommonTask(version,batchId),YuntiTaskEnum.ADS_SOP_YUNTI_CVM);
        // 初始化dws
        initTask(new SimpleCommonTask(version,batchId),YuntiTaskEnum.DWS_SOP_YUNTI_CVM);
        // 初始化dwd
        initTask(new SimpleCommonTask(version,batchId),YuntiTaskEnum.DWD_SOP_YUNTI_CVM_RETURN);
        initTask(new SimpleCommonTask(version,batchId),YuntiTaskEnum.DWD_SOP_YUNTI_CVM_DEMAND);
    }
}
