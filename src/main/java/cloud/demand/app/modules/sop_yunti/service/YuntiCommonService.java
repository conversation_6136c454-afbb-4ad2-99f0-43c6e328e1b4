package cloud.demand.app.modules.sop_yunti.service;

import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandHedgingDetailResList;
import cloud.demand.app.modules.sop.domain.http.YuntiCvmDemandVersionCodeRes;
import cloud.demand.app.modules.sop.entity.other.DwdYuntiCvmDemandForecastItemDfDO;
import cloud.demand.app.modules.sop.entity.other.DwdYuntiCvmReturnPlanItemDfDO;
import cloud.demand.app.modules.sop.entity.other.IYuntiCvmDemandDO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/** 云梯公共 */
public interface YuntiCommonService {
    /**
     * 获取日期之前最近关闭的版本
     * @param date 日期(包括日期当天)
     * @param needFinished 是否只看已完成的
     * @param limitCurWeek 是否限制在日期所在当周
     * @return 云梯自研干预版本号
     */
    public YuntiCvmDemandVersionCodeRes.Item getLatestVersionByDate(LocalDate date, boolean needFinished, boolean limitCurWeek);


    /**
     * 查询云梯自研干预版本集合（查不到内部会报错）
     * @return 版本集合
     */
    public YuntiCvmDemandVersionCodeRes queryVersionList();

    public YuntiCvmDemandVersionCodeRes queryVersionListV2();

    /**
     * 查询所有已经完成的版本
     * @return 版本集合
     */
    public YuntiCvmDemandVersionCodeRes queryFinishedVersionList();

    /**
     * 查询版本信息
     * @param version 版本号
     * @return 版本信息
     */
    public YuntiCvmDemandVersionCodeRes.Item queryVersionInfo(String version);

    /**
     * 根据干预版本号查询云梯自研干预数据
     * @param versionCode 版本号
     * @return 自研干预对冲数据
     */
    public List<YuntiCvmDemandHedgingDetailResList> getHedgingData(String versionCode);


    /**
     * 获取云梯cvm需求切片数据
     * @param date 切片日期（最新的为昨天）
     * @return 云梯切片数据
     */
    public List<DwdYuntiCvmDemandForecastItemDfDO> getCvmDemandDfData(LocalDate date);

    /**
     * 获取指定版本号的全量cvm需求数据（自研干预+云梯切片）
     * @param versionCode 版本号
     * @return 指定版本的cvm需求全量数据
     */
    public List<IYuntiCvmDemandDO> getCvmDemandDataWithHedging(String versionCode);


    /**
     * 获取指定版本号的全量cvm退回数据（云梯切片）
     * @param version 版本号
     * @return 指定版本的cvm退回切片数据
     */
    public List<DwdYuntiCvmReturnPlanItemDfDO> getReturnDataByVersion(String version);


    /**
     * 需求id转订单id
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return 需求id转订单id集合
     */
    public Map<String, Set<String>> getDemandItemId2OrderId(LocalDate startDate,LocalDate endDate);

    /**
     * 退回id转订单id
     * @param startDate 起始时间
     * @param endDate 结束时间
     * @return 退回id转订单id集合
     */
    public Map<String, Set<String>> getReturnItemId2OrderId(LocalDate startDate,LocalDate endDate);
}
