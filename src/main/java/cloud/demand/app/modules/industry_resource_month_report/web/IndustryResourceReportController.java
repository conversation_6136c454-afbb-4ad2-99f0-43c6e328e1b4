package cloud.demand.app.modules.industry_resource_month_report.web;

import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryRankMailReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryRankReqWithCardType;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceWithCardAndTopReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceWithCardReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.req.IndustryResourceWithExportReq;
import cloud.demand.app.modules.industry_resource_month_report.dto.resp.IndustryRankMailResp;
import cloud.demand.app.modules.industry_resource_month_report.dto.resp.IndustryRankResp;
import cloud.demand.app.modules.industry_resource_month_report.dto.resp.IndustryResourceResp;
import cloud.demand.app.modules.industry_resource_month_report.service.IndustryResourceReportService;
import cloud.demand.app.modules.mrpv2.utils.RangeUtils;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandProductEnumDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.sop.domain.ReturnT;
import cloud.demand.app.modules.sop.service.alert.SopRtxRobotHttpService;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/** 分行业资源运作月报 */
@JsonrpcController("/industry-resource/report")
@Slf4j(topic = "分行业资源运作月报-controller")
public class IndustryResourceReportController {

    @Resource
    private IndustryResourceReportService industryResourceReportService;

    @Resource
    private IndustryDemandDictService industryDemandDictService;

    @Resource
    private SopRtxRobotHttpService rtxRobotHttpService;

    /** 每天 9 点 40 预热 */
    @Synchronized(waitLockMillisecond = 0)
    @Scheduled(cron = "0 40 9 * * ?")
    public void scheduledPreheating(){
        try{
            industryResourceReportService.preheating();
        }catch (Exception e){
            e.printStackTrace();
//            rtxRobotHttpService.sendMarkdown(TpClientUtil.appendUsers(
//                    "分行业资源运作月报预热失败：\n" + e.getMessage(),
//                    ListUtils.newList("smithyyi")));
        }
    }

    /** 查询排行 */
    @RequestMapping
    public IndustryRankResp queryRank(@JsonrpcParam @Valid IndustryRankReqWithCardType req){
        return industryResourceReportService.queryRank(req);
    }

    /** 查询排行邮件 */
    @RequestMapping
    public IndustryRankMailResp queryRankMail(@JsonrpcParam @Valid IndustryRankMailReq req){
        return industryResourceReportService.queryRankMail(req);
    }

    /** 查询报表（只看 top，非 top 用其他归到一起） */
    @RequestMapping
    public IndustryResourceResp queryReportWithTop(@JsonrpcParam @Valid IndustryResourceWithCardAndTopReq req) {
        req.checkTopN();
        return industryResourceReportService.queryReportWithTop(req);
    }

    /** 查询报表 */
    @RequestMapping
    public IndustryResourceResp queryReport(@JsonrpcParam @Valid IndustryResourceWithCardReq req) {
        return industryResourceReportService.queryReport(req);
    }

    /** 导出报表 */
    @RequestMapping
    public ResponseEntity<InputStreamResource> exportReport(@JsonrpcParam @Valid IndustryResourceWithExportReq req){
        return industryResourceReportService.exportReport(req);
    }

    /** 筛选缓存 */
    @RequestMapping
    public ReturnT<String> deleteCache(@JsonrpcParam @Valid IndustryResourceWithCardReq req){
        industryResourceReportService.deleteCache(req);
        return ReturnT.ok();
    }

    /** 删除全部缓存 */
    @RequestMapping
    public ReturnT<String> deleteAllCache(@JsonrpcParam DataCard dataCard){
        YearMonth startYearMonth = YearMonth.of(2024, 7);
        YearMonth endYearMonth = YearMonth.now();
        List<IndustryDemandProductEnumDO> allIndustryProductEnum = industryDemandDictService.getAllIndustryProductEnum();
        List<String> industryDeptList = allIndustryProductEnum.stream().map(IndustryDemandProductEnumDO::getIndustry).distinct()
                .collect(Collectors.toList());
        List<String> yearMonthRange = RangeUtils.rangeDate(startYearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")),
                endYearMonth.format(DateTimeFormatter.ofPattern("yyyy-MM")));
        for (String yearMonth : yearMonthRange) {
            for (String industryDept : industryDeptList) {
                IndustryResourceWithCardReq req = new IndustryResourceWithCardReq();
                req.setYearMonth(yearMonth);
                req.setIndustryDept(industryDept);
                req.setDataCard(dataCard.getDataCard());
                industryResourceReportService.deleteCache(req);
            }
        }
        return ReturnT.ok();
    }

    @Data
    static class DataCard{
        private List<String> dataCard;
    }

    /** 预热 */
    @RequestMapping
    public ReturnT<String> preheating(){
        industryResourceReportService.preheating();
        return ReturnT.ok();
    }


}
