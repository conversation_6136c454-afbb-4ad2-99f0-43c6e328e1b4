package cloud.demand.app.modules.industry_resource_month_report.model;

import cloud.demand.app.modules.soe.anno.ExcelSheet;
import java.util.List;
import lombok.Data;

@Data
public class SummaryModel {

    @ExcelSheet(value = "预测-执行趋势数据(新增)",modelClass = ForecastTrendAddModel.class)
    private List<ForecastTrendAddModel> forecastTrendAddModel;

    @ExcelSheet(value = "预测-执行分机型数据(新增)",modelClass = ForecastScaleAddModel.class)
    private List<ForecastScaleAddModel> forecastScaleAddModel;

    @ExcelSheet(value = "预测-执行趋势数据(退回)",modelClass = ForecastTrendReturnModel.class)
    private List<ForecastTrendReturnModel> forecastTrendReturnModel;

    @ExcelSheet(value = "预测-执行分机型数据(退回)",modelClass = ForecastScaleReturnModel.class)
    private List<ForecastScaleReturnModel> forecastScaleReturnModel;

    @ExcelSheet(value = "分客户订单详情",modelClass = OrderSeverLevelModel.class)
    private List<OrderSeverLevelModel> orderSeverLevelModel;

    @ExcelSheet(value = "预扣统计",modelClass = WithholdingModel.class)
    private List<WithholdingModel> withholdingModel;

    @ExcelSheet(value = "GPU计费卡（单位卡）",modelClass = GpuBillModel.class)
    private List<GpuBillModel> gpuBillModel;

    @ExcelSheet(value = "GPU新增预测(最新版)（单位卡）",modelClass = GpuForecastAddModel.class, dynamicFields = {"numberTreeMap"})
    private List<GpuForecastAddModel> gpuForecastAddModel;

    @ExcelSheet(value = "GPU存量预扣（单位卡）",modelClass = GpuWithholdingModel.class)
    private List<GpuWithholdingModel> gpuWithholdingModel;
}
