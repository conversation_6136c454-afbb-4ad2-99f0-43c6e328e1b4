package cloud.demand.app.modules.industry_resource_month_report.dto.resp;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Objects;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/** 行业排行数据 */
@Data
public class IndustryRankData implements Comparator<IndustryRankData> {
    /** 年月 */
    private String yearMonth;
    /** 行业部门 */
    private String industryDept;
    /** 排行 */
    private Integer rank;
    /** 排行提升（相比上个月） */
    private Integer rankUp;
    /** 排行值 */
    private BigDecimal rankValue;

    /** html 的 freemarker 用 */
    @JsonProperty("rankValueP")
    public String getRankValueP(){
        BigDecimal data = ObjectUtils.defaultIfNull(rankValue, BigDecimal.ZERO).multiply(new BigDecimal(100));
        return String.format("%.2f%%", data);
    }

    @Override
    public int compare(IndustryRankData o1, IndustryRankData o2) {
        // 默认降序
        return SoeCommonUtils.compare2(o2.getRankValue(), o1.getRankValue());
    }

    /**
     * 排序并设置排行
     * @param rankData 排名数据
     */
    public static void sortAndSetRank(List<IndustryRankData> rankData,boolean up){
        if (ListUtils.isNotEmpty(rankData)){
            rankData.sort((o1, o2) -> {
                int i = SoeCommonUtils.compare2(o2.getRankValue(), o1.getRankValue());
                return up ? i : -i;
            });
            int rank = 0;
            int curRankSize = 1;
            BigDecimal rankValue = null;
            for (IndustryRankData next : rankData) {
                BigDecimal nextRankValue = next.getRankValue();
                if (rankValue != null && Objects.equal(rankValue, nextRankValue)) {
                    next.setRank(rank);
                    curRankSize++;
                } else {
                    rank += curRankSize;
                    curRankSize = 1;
                    next.setRank(rank);
                }
                rankValue = nextRankValue;
            }
        }
    }
}
