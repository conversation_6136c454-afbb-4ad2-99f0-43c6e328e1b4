package cloud.demand.app.modules.physical_device.service;

import cloud.demand.app.modules.physical_device.entity.SimplePurchaseForecastDO;
import cloud.demand.app.modules.physical_device.model.JiaPingCommonReq;
import com.pugwoo.dbhelper.model.PageData;
import java.util.List;

public interface OfferInfo2ThirdPartyService {

    /**
     * 提供物理机需求预测数据给架构平台部
     * 简单用 DO 返回，如果后面有变化再上其他类
     *
     * @param req 筛选项
     * @return 结果
     */
    PageData physicalDemandDataForJiaPing(JiaPingCommonReq req);

    List<SimplePurchaseForecastDO> purchaseForecastDataForJiaPing(JiaPingCommonReq req);
}
