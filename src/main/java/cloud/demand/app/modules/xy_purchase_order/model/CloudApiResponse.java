package cloud.demand.app.modules.xy_purchase_order.model;

import lombok.Data;

import java.util.List;

@Data
public class CloudApiResponse<T> {

    private DataSet dataSet;

    @Data
    public static class DataSet<T> {
        private Header header;
        private DataSetData<T> data;
    }

    @Data
    public static class Header {
        private String version;
        private String errorInfo;
        private Integer returnCode;
    }

    @Data
    public static class DataSetData<T> {
        private List<T> deviceList;
    }

}
