package cloud.demand.app.modules.xy_purchase_order.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class DeliveryDeviceDTO {

    private String serverId;
    private String assetId;
    private String ip;
    private String idcName;
    private String statusId;
    private String deviceClass;
    private String deliveryTime;
    private String initStartTime;
    private String initEndTime;
    private String processResult;
    private String intoBufferTime;
    private String deliveryEndTime;
    private String deptId;
    private String deptName;
    private String initErrType;
    private String busiSetId;
    private String busiSetName;
    private String quotaId;
    private String module;
    private String lastOperator;
    private String checkStatus;
    private String onlineId;
    @JsonProperty("oCreator")
    private String oCreator;
    @JsonProperty("qCreator")
    private String qCreator;
    @JsonProperty("oBusiModuleName")
    private String oBusiModuleName;
    @JsonProperty("qBusiModuleName")
    private String qBusiModuleName;
    @JsonProperty("oBusiName")
    private String oBusiName;
    @JsonProperty("qBusiName")
    private String qBusiName;
    private String assetSN;
    private String tdtName;
    private String version;
    private String hardMemo;
    @JsonProperty("SLAEndTime")
    private String SLAEndTime;

}
