package cloud.demand.app.modules.repo_show.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.demand.DemandCsigCompanyInventoryDetailDO;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.entity.resource.ApplyCancelRecordDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigResourceViewCategoryDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.repo_show.enums.CompanyInvMarkTypeEnum;
import cloud.demand.app.modules.repo_show.enums.InvTypeEnum;
import cloud.demand.app.modules.repo_show.model.*;
import cloud.demand.app.modules.repo_show.service.DecisionViewService;
import cloud.demand.app.modules.xy_purchase_order.model.DeliveryDeviceDTO;
import cloud.demand.app.modules.xy_purchase_order.service.XyThirdPartyService;
import com.alibaba.excel.EasyExcel;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DecisionViewServiceImpl implements DecisionViewService {

    @Resource
    private DBHelper resourcedbDBHelper;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private XyThirdPartyService xyThirdPartyService;

    /**
     * 采购单订单状态映射
     */
    private Map<Integer, String> codeMap = new HashMap<Integer, String>(){{
        put(0, "打回重填");
        put(1, "产品审核");
        put(2, "TEG审核中");
        put(3, "星云拒绝");
        put(4, "资源分配");
        put(5, "挂起");
        put(6, "资源总监审核");
        put(7, "云GM审核");
        put(7000001, "资源匹配下发");
        put(7000002, "搬迁审核");
        put(7000003, "搬迁中");
        put(7000004, "搬迁交付");
        put(8000001, "ERP审核中");
        put(8000002, "等待到货");
        put(8000005, "部分到货");
        put(8000006, "完全到货");
        put(8000007, "设备交付");
        put(8000127, "ERP拒绝");
        put(9000001, "资源审核");
        put(9000005, "运管审核");
        put(9000000, "ERP部门审核");
        put(9000007, "ERP总监审核");
    }};

    @Override
    @Synchronized(waitLockMillisecond = 1000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "genCompanyInventory")
    @Transactional("demandTransactionManager")
    public void genCompanyInventory(){
        ArrayList<DemandCsigCompanyInventoryDetailDO> result = Lang.list();
        //  已预约-生产过程料部分
        result.addAll(genProductProcessMaterialData());
        //  采购-空闲设备-BG部分
        result.addAll(genPurchaseButFreeData());
        fillAllExpandFields(result);
        //  替换已经生成的数据
        replaceData(result, DateUtils.toLocalDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
    }



    /**
     * 统一后置处理所有业务新增展示的字段
     */
    private void fillAllExpandFields(List<DemandCsigCompanyInventoryDetailDO> details) {
        Set<String> ids = ListUtils.toSet(details, o -> o.getQOrderId());
        List<DeviceApplyRealTimeVO> all
                = shuttleDBHelper.getAll(DeviceApplyRealTimeVO.class, "where sub_id in (?)", ids);
        Map<String, DeviceApplyRealTimeVO> map = ListUtils.toMap(all, o -> o.getSubId(), o -> o);


        for (DemandCsigCompanyInventoryDetailDO detail : details) {
            detail.setCpuLogicCore(dictService.getDeviceLogicCpuCore(detail.getDeviceType(), true));
            String qOrderId = detail.getQOrderId();
            String qOrderSourceId = null;

            if (StringUtils.isBlank(qOrderId)) {
                continue;
            }
            DeviceApplyRealTimeVO deviceApplyVO = map.get(qOrderId);
            if (deviceApplyVO == null) {
                //  Q单号中存在'S'或后缀，说明erp进行了拆单，可能不完整
                //  因此需要按照原单号去查询相关信息
                if (qOrderId.contains("S")) {
                     qOrderSourceId = qOrderId.split("S")[0];
                    if (StringUtils.isNotBlank(qOrderSourceId)) {
                        //  再查一次
                        List<DeviceApplyRealTimeVO> source
                                = shuttleDBHelper.getAll(DeviceApplyRealTimeVO.class, "where sub_id in (?)", qOrderSourceId);
                        if (ListUtils.isNotEmpty(source)) {
                            deviceApplyVO = source.get(0);
                        } else {
                            continue;
                        }
                    }
                }else {
                    continue;
                }
            }

            detail.setQOrderId(qOrderId);
            detail.setLadingOrderTime(deviceApplyVO.getCreateTime());
            detail.setDeptAuthTime(deviceApplyVO.getDeptAuthTime());
            detail.setPlanMonth(deviceApplyVO.getPlanMonth());
            detail.setExpectDeliveryDate(DateUtils.toLocalDate(deviceApplyVO.getExpectDeliveryDate()));
            detail.setIndustry(deviceApplyVO.getIndustry());
            detail.setCustomerName(deviceApplyVO.getCustomerName());
            detail.setCustomerType(deviceApplyVO.getCustomerType());
            detail.setPurpose(deviceApplyVO.getPurpose());

            Integer status = deviceApplyVO.getStatus();
            if (status != null) {
                String statusName = codeMap.get(status);
                if (StringUtils.isNotBlank(statusName)) {
                    detail.setStatusName(statusName);
                }
            }

            //  获取实际交付时间时根据原来的单号去找
            if (qOrderSourceId != null){
                continue;
            }

            try {
                List<DeliveryDeviceDTO> deliveryDevice = xyThirdPartyService.queryDeliveryDevice(
                        deviceApplyVO.getSubId(), deviceApplyVO.getCreateTime());
                if (deliveryDevice == null || deliveryDevice.isEmpty()) {
                    continue;
                }
                List<DeliveryDeviceDTO> filter = ListUtils.filter(deliveryDevice, o ->
                        Objects.equals(o.getAssetId(), detail.getAssetId()));
                DeliveryDeviceDTO dto = filter.isEmpty() ? null : filter.get(0);
                if (dto != null) {
                    detail.setSlaEndTime(DateUtils.parse(dto.getSLAEndTime(), "yyyy-MM-dd HH:mm:ss"));
                }
            } catch (Exception e) {
                String msg = "call cloud-api to query qorder={" + deviceApplyVO.getSubId() + "} fail:" + e.getMessage();
                log.error(msg, e);
                taskLogService.genRunLog("genCompanyInventory", "fillAllExpandFields", msg);
            }
        }
    }


    /**
     * 导出接口服务
     */
    @Override
    public FileNameAndBytesDTO exportCompanyInventoryData(String statTime){
        //  文件名：后缀加毫秒数
        String templateName = "company_inventory_detail.xlsx";
        InputStream is = IOUtils.readClasspathResourceInputStream("excel/decision_view/" + templateName);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        EasyExcel.write(out).withTemplate(is).sheet(0).doWrite(queryCompanyInventoryDetailData(statTime));
        FileNameAndBytesDTO dto = new FileNameAndBytesDTO();
        dto.setBytes(out.toByteArray());
        dto.setFileName("公司库存明细" + DateUtils.format(DateUtils.parse(statTime), "yyyy_MM_dd"));
        return dto;
    }

    private List<CompanyInventoryExportDTO> queryCompanyInventoryDetailData(String statTime){
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.addAnd("stat_time = ?", statTime);
        whereContent.addAnd("product_type != ''");
        List<DemandCsigCompanyInventoryDetailDO> details =
                demandDBHelper.getAll(DemandCsigCompanyInventoryDetailDO.class, whereContent.getSql(), whereContent.getParams());
        return CompanyInventoryExportDTO.tranform(details);
    }



    /**
     * 已预约-生产过程料部分
     */
    private List<DemandCsigCompanyInventoryDetailDO> genProductProcessMaterialData(){
        //  公司资源池属于云的全量库存设备明细
        String sql = ORMUtils.getSql("/sql/repo_show/decision_view/production_process_materials.sql");
        List<PpmDTO> raw = resourcedbDBHelper.getRaw(PpmDTO.class, sql);
        Map<String, PpmDTO> sourceMap = ListUtils.toMap(raw, o -> o.getAssetId(), o -> o);

        //  查出固资对应最新的初次期望交付日期，并覆盖上面查出的初次期望交付日期结果
        String finalPromiseTimeSql = ORMUtils.getSql("/sql/repo_show/decision_view/final_promise_time_cover.sql");
        Map<String, FinalCoverDTO> coverMap =
                ListUtils.toMap(resourcedbDBHelper.getRaw(FinalCoverDTO.class, finalPromiseTimeSql), o -> o.getAssetId(), o -> o);

        for (Map.Entry<String, FinalCoverDTO> entry : coverMap.entrySet()) {
            String key = entry.getKey();
            FinalCoverDTO value = entry.getValue();
            if (value.getPromisTime() == null){
                continue;
            }
            PpmDTO ppmDTO = sourceMap.get(key);
            if (ppmDTO != null){
                String finalDate = DateUtils.formatDate(new Date(value.getPromisTime().getTime()));
                ppmDTO.setPromisTime(finalDate);
            }
        }


        List<CloudDemandCsigResourceViewCategoryDO> allConfig = loadAllConfig();
        BiFunction<String, String, String> keyFuc = (o1, o2) -> Strings.join("@", o1, o2);
        //  【规划产品 + 计算类型】作为K
        Map<String, CloudDemandCsigResourceViewCategoryDO> blankConfigMap = getBlankConfigMap(allConfig);
        Map<String, CloudDemandCsigResourceViewCategoryDO> notBlankConfigMap = getNotBlankConfigMap(allConfig, keyFuc);
        List<String> allNetPlanProductName = rrpDBHelper.getRaw(String.class,
                "select plan_product from report_config_transfer where biz_group = '网络' and deleted = 0");

        List<DemandCsigCompanyInventoryDetailDO> result = Lang.list();

        for (PpmDTO dto : raw) {
            DemandCsigCompanyInventoryDetailDO one = DemandCsigCompanyInventoryDetailDO.transform(dto);
            one.setStatTime(DateUtils.toLocalDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
            //  填充公司库存的产品类型
            String planProductName = one.getPlanProductName();
            //  获取该机型的计算类型
            String computeType = dictService.getComputeType(one.getDeviceType());
            //   通过规划产品 + 计算类型查找， 如果没有就只通过规划产品找
            CloudDemandCsigResourceViewCategoryDO config = notBlankConfigMap.get(keyFuc.apply(planProductName, computeType));
            if (config == null) {
                config = blankConfigMap.get(planProductName);
            }
            one.setProductType(config == null ? "" : config.getCategory3());
            if (allNetPlanProductName.contains(planProductName)){
                one.setProductType("网络");
            }
            StaticZoneDO staticZoneInfoById = dictService.getStaticZoneInfoByModule(one.getModuleName());
            if (staticZoneInfoById != null) {
                one.setCustomhouseTitle(staticZoneInfoById.getCustomhouseTitle());
            }
            fillUnitAndLogicNum(one);
            result.add(one);
        }
        log.info("genCompanyInventory Part1:{}", result.size());
        ListUtils.forEach(result, o -> addMarking(o));
        return result;
    }

    /**
     * 采购-空闲设备-BG部分
     */
    private List<DemandCsigCompanyInventoryDetailDO> genPurchaseButFreeData(){
        //  1、获取取消记录表的全部固资
        String sql = ORMUtils.getSql("/sql/repo_show/decision_view/apply_cancel_record.sql");
        List<ApplyCancelRecordDO> raw = resourcedbDBHelper.getRaw(ApplyCancelRecordDO.class, sql);
        Map<String, ApplyCancelRecordDO> map = ListUtils.toMap(raw, o -> o.getAsset(), o -> o);

        //  2、到服务器表查询当前状态，如果部门为资源中心且为空闲，即表示被云取消后，未被其他bg使用
        //  拿到符合2条件的全部固资号
        String freeAssetIdsSql = "select distinct LogicPcCode from sto_rmdb_servers" +
                        " where LogicPcCode in (?) and BelongDpId = 32 and OID <= 0 and OID != -101";
        List<String> freeAssetIds = resourcedbDBHelper.getRaw(String.class, freeAssetIdsSql, map.keySet());

        //  3、获取2中结果固资号的全部基础信息
        String assetBaseInfoSql = ORMUtils.getSql("/sql/repo_show/decision_view/asset_base_info.sql");
        Map<String, AssetBaseInfoDTO> baseInfoMap =
                ListUtils.toMap(resourcedbDBHelper.getRaw(AssetBaseInfoDTO.class,
                        assetBaseInfoSql, freeAssetIds), o -> o.getAssetId(), o -> o);

        //  4、获取预约单相关信息
        List<String> filteredOpCodes =
                raw.stream().filter((o) -> freeAssetIds.contains(o.getAsset())).
                        map((o) -> o.getOpCode()).collect(Collectors.toList());

        String yuyueOrderInfoSql = ORMUtils.getSql("/sql/repo_show/decision_view/yuyue_order_info.sql");
        Map<String, YuyueOrderInfoDTO> yuyueOrderMap = ListUtils.toMap(
                        resourcedbDBHelper.getRaw(YuyueOrderInfoDTO.class, yuyueOrderInfoSql, filteredOpCodes),
                        o -> o.getDetailCode(), o -> o);

        List<CloudDemandCsigResourceViewCategoryDO> allConfig = loadAllConfig();
        BiFunction<String, String, String> keyFuc = (o1, o2) -> Strings.join("@", o1, o2);

        //  【规划产品 + 计算类型】作为K
        Map<String, CloudDemandCsigResourceViewCategoryDO> blankConfigMap = getBlankConfigMap(allConfig);
        Map<String, CloudDemandCsigResourceViewCategoryDO> notBlankConfigMap = getNotBlankConfigMap(allConfig, keyFuc);
        List<String> allNetPlanProductName = rrpDBHelper.getRaw(String.class,
                "select plan_product from report_config_transfer where biz_group = '网络' and deleted = 0");

        List<DemandCsigCompanyInventoryDetailDO> result = Lang.list();

        // 填充DO
        for (String assetId : freeAssetIds) {
            ApplyCancelRecordDO applyCancelRecordDO = map.get(assetId);

            AssetBaseInfoDTO assetBaseInfoDTO = baseInfoMap.get(assetId);
            if(assetBaseInfoDTO == null){
                //  找不到固资信息就跳过这个固资了
                String msg = "服务器表中没有找到{"+ assetId + "}的信息";
                taskLogService.genRunLog("genCompanyInventory", "genPurchaseButFreeData", msg);
                    continue;
            }

            YuyueOrderInfoDTO yuyueOrderInfoDTO = yuyueOrderMap.get(applyCancelRecordDO.getOpCode());
            if (yuyueOrderInfoDTO == null){
                //  可能没有预约单
                String msg = "预约单信息表中没有找到{"+ assetId + "}的信息";
                taskLogService.genRunLog("genCompanyInventory", "genPurchaseButFreeData", msg);
            }

            DemandCsigCompanyInventoryDetailDO one = DemandCsigCompanyInventoryDetailDO.transform(assetBaseInfoDTO);

            one.setInvType(InvTypeEnum.PURCHASE_FREE_BG.getName());
            one.setInvTypeCode(InvTypeEnum.PURCHASE_FREE_BG.getCode());
            one.setInvMark("需求取消");
            one.setStatTime(DateUtils.toLocalDate(DateUtils.addTime(new Date(), Calendar.DATE, -1)));
            one.setQOrderId(applyCancelRecordDO.getQuotaCode());
            one.setAssetId(assetId);

            String planProduct = applyCancelRecordDO.getPlanProduct();
            one.setPlanProductName(planProduct);
            String computeType = dictService.getComputeType(one.getDeviceType());
            //   通过规划产品 + 计算类型查找， 如果没有就只通过规划产品找
            CloudDemandCsigResourceViewCategoryDO config = notBlankConfigMap.get(keyFuc.apply(planProduct, computeType));
            if (config == null) {
                config = blankConfigMap.get(planProduct);
            }
            one.setProductType(config == null ? "" : config.getCategory3());
            if (allNetPlanProductName.contains(planProduct)){
                one.setProductType("网络");
            }
            StaticZoneDO staticZoneInfoById = dictService.getStaticZoneInfoByModule(one.getModuleName());
            if (staticZoneInfoById != null) {
                one.setCustomhouseTitle(staticZoneInfoById.getCustomhouseTitle());
            }

            if (yuyueOrderInfoDTO != null) {
                one.setPromisTime(DemandCsigCompanyInventoryDetailDO.toLocalDate(yuyueOrderInfoDTO.getPromisTime()));
                one.setActualDate(yuyueOrderInfoDTO.getActualDate());
                one.setExpectDate(DemandCsigCompanyInventoryDetailDO.toLocalDate(yuyueOrderInfoDTO.getExpectDate()));
            }

            fillUnitAndLogicNum(one);
            result.add(one);
        }
        log.info("genCompanyInventory Part2:{}", result.size());
        return result;
    }

    /**
     * 替换，保证幂等性
     */
    private void replaceData(List<DemandCsigCompanyInventoryDetailDO> result, LocalDate statTime){
        demandDBHelper.delete(DemandCsigCompanyInventoryDetailDO.class, "where stat_time = ?", statTime);
        demandDBHelper.insertBatchWithoutReturnId(result);
    }

    /**
     * 获取资源视图的配置信息
     * @return
     */
    private List<CloudDemandCsigResourceViewCategoryDO> loadAllConfig(){
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        List<String> products = Lang.list("CVM", "裸金属", "GPU", "CDB", "CBS", "COS");
        whereContent.andIn(CloudDemandCsigResourceViewCategoryDO::getCategory3, products);
        return yuntiDBHelper.getAll(CloudDemandCsigResourceViewCategoryDO.class, whereContent.getSql(), whereContent.getParams());
    }

    /**
     * 获取不带ComputeType的产品映射Map
     */
    private Map<String, CloudDemandCsigResourceViewCategoryDO> getBlankConfigMap(List<CloudDemandCsigResourceViewCategoryDO> allConfig){
        List<CloudDemandCsigResourceViewCategoryDO> blankConfig =
                ListUtils.filter(allConfig, (o) -> Strings.isBlank(o.getComputeType()));
        return ListUtils.toMap(blankConfig, CloudDemandCsigResourceViewCategoryDO::getPlanProductName, (o) -> o);
    }

    /**
     * 获取带ComputeType的产品映射Map
     */
    private Map<String, CloudDemandCsigResourceViewCategoryDO> getNotBlankConfigMap(List<CloudDemandCsigResourceViewCategoryDO> allConfig,
                                                                                    BiFunction<String, String, String> keyFuc){
        List<CloudDemandCsigResourceViewCategoryDO> notBlankConfig =
                ListUtils.filter(allConfig, (o) -> Strings.isNotBlank(o.getComputeType()));
        return ListUtils.toMap(notBlankConfig, (o) -> keyFuc.apply(o.getPlanProductName(), o.getComputeType()), (o) -> o);
    }


    /**
     * 库存打标
     */
    private void addMarking(DemandCsigCompanyInventoryDetailDO source){
        source.setInvType(InvTypeEnum.PPM.getName());
        source.setInvTypeCode(InvTypeEnum.PPM.getCode());
        //  1、生产过程异常料：库存分类2 = 生产异常（到货未交货，指的是异常料这块）
        if(Objects.equals(source.getInventoryClass2(), "生产异常")){
            source.setInvMark(CompanyInvMarkTypeEnum.ABNORMAL_MATERIAL.getName());
            return;
        }
        //  2、未知交付状态：无可交付时间
        if (source.getActualDate() == null){
            source.setInvMark(CompanyInvMarkTypeEnum.UNKNOWN_DELIVERY_STATUS.getName());
            return;
        }

        if (source.getPromisTime() == null){
            source.setInvMark(CompanyInvMarkTypeEnum.UNKNOWN_PROMISE_TIME.getName());
            return;
        }

        //  初次需求时间-精确到天
        LocalDate promisTime = source.getPromisTime();
        String promisDate = DateUtils.formatDate(promisTime);
        //  可交付时间-精确到天
        String actualDate = DateUtils.formatDate(source.getActualDate());
        if (promisDate.compareTo(actualDate) < 0){  //  3、 延期交付未领：可交付时间 > 初次需求时间 （以日期2022-09-01为单位，不细化小时分钟）
            source.setInvMark(CompanyInvMarkTypeEnum.DELAY_DELIVERY_NOT_APPLY.getName());
        } else if (promisDate.compareTo(actualDate) == 0){  //  4、按期交付未领：可交付时间 = 初次需求时间
            source.setInvMark(CompanyInvMarkTypeEnum.ON_SCHEDULE_NOT_APPLY.getName());
        } else if (promisDate.compareTo(actualDate) > 0){  //  5、提前交付未领：可交付时间 < 初次需求时间
            source.setInvMark(CompanyInvMarkTypeEnum.IN_ADVANCE_NOT_APPLY.getName());
        }
    }


    /**
     * 添加单位和逻辑数
     */
    private void fillUnitAndLogicNum(DemandCsigCompanyInventoryDetailDO source){
        String productType = source.getProductType();
        BigDecimal logicNum = BigDecimal.ZERO;
        String unit = "";
        if (StringUtils.isBlank(productType)) {
            source.setTransUnit(unit);
            source.setTransAmount(logicNum);
            return;
        }

        if (ProductTypeEnum.CVM.getCode().equals(productType) || ProductTypeEnum.METAL.getCode().equals(productType)){
            logicNum = BigDecimal.valueOf(dictService.getDeviceLogicCpuCore(source.getDeviceType(), true));
            unit = "核";
        } else if (ProductTypeEnum.GPU.getCode().equals(productType)){
            logicNum = BigDecimal.valueOf(dictService.getDeviceGpuCard(source.getDeviceType()));
            unit = "卡";
        } else if (ProductTypeEnum.CBS.getCode().equals(productType)) {
            logicNum = dictService.getDeviceCbsStore(source.getDeviceType());
            unit = "PB";
        } else if (ProductTypeEnum.COS.getCode().equals(productType)) {
            logicNum = dictService.getDeviceCosStore(source.getPlanProductName(), source.getDeviceType());
            unit = "PB";
        } else if (ProductTypeEnum.CDB.getCode().equals(productType)) {
            logicNum = dictService.getDeviceCdbMemCap(source.getDeviceType());
            unit = "TB";
        }else if (ProductTypeEnum.NETWORK.getCode().equals(productType)){
            logicNum = BigDecimal.ONE;
            unit = "台";
        }
        source.setTransUnit(unit);
        source.setTransAmount(logicNum);

    }


    @Data
    public static class FinalCoverDTO{
        @Column("asset_id")
        private String assetId;

        @Column("PromisTime")
        private Timestamp promisTime;
    }


}
