package cloud.demand.app.modules.repo_show.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Table("demand_csig_yg_inventory")
public class DemandCsigYgInventoryDO extends BaseDO {

    /** 固资id<br/>Column: [assert_id] */
    @Column(value = "assert_id")
    private String assertId;

    /** 统计日期<br/>Column: [day] */
    @Column(value = "day")
    private Date day;

    /** 产品类型 CVM 裸金属<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** region名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 台数<br/>Column: [device_amount] */
    @Column(value = "compute_type")
    private String computeType;

    /** campus<br/>Column: [campus] */
    @Column(value = "campus")
    private String campus;

    /** module_name<br/>Column: [module_name] */
    @Column(value = "module_name")
    private String moduleName;

    /** 1 该领未领 2 取消单据导致<br/>Column: [type] */
    @Column(value = "type")
    private Integer type;

    /** 转换后的数<br/>Column: [trans_amount] */
    @Column(value = "trans_amount")
    private BigDecimal transAmount;

    /** 转换后的单位<br/>Column: [trans_unit] */
    @Column(value = "trans_unit")
    private String transUnit;

}
