package cloud.demand.app.modules.repo_show;


import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.entity.demand.DemandCsigCompanyInventoryDetailDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.repo_show.enums.ProductUnitEnum;
import cloud.demand.app.modules.repo_show.req.*;
import cloud.demand.app.modules.repo_show.rsp.*;
import cloud.demand.app.modules.repo_show.service.DecisionViewService;
import cloud.demand.app.modules.repo_show.service.QueryRepoShowService;
import cloud.demand.app.web.model.common.DownloadBean;
import cloud.demand.app.web.model.common.Result;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 资源视图前端接口
 */
@JsonrpcController("/repo-show")
@Slf4j
public class RepoShowController {

    @Resource
    QueryRepoShowService queryRepoShowService;
    @Resource
    DictService dictService;
    @Resource
    DecisionViewService decisionViewService;
    @Resource
    DBHelper demandDBHelper;

    @RequestMapping
    public RepoShowRsp queryAllProductSummary(@JsonrpcParam RepShowReq req) {
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        return queryRepoShowService.queryAllProductRepoShow(DateUtils.formatDate(req.getDate()));
    }

    @RequestMapping
    public RepoShowRsp queryNetworkSummary(@JsonrpcParam RepShowReq req) {
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        return queryRepoShowService.queryNetworkRepoShow(DateUtils.formatDate(req.getDate()));
    }


    @RequestMapping
    public RepoShowTargetRsp queryAllTargets(@JsonrpcParam RepoShowTargetReq req) {
        Date start = req.getStart();
        System.out.println(DateUtils.getYear(start));
        Date end = req.getEnd();
        if (start != null && end != null){
            if (end.before(start)){
                throw new WrongWebParameterException("日期参数格式有误");
            }
        }
        return queryRepoShowService.queryTarget(req);
    }

    @RequestMapping
    public Result batchDeleteTarget(@CurrentUser TofUser user, @JsonrpcParam UpdateTargetReq req){
        if (ListUtils.isEmpty(req.getIds())){
            throw new WrongWebParameterException("未传入待删除的id集合");
        }
        Integer result = queryRepoShowService.batchDeleteTarget(user.getUsername(), req.getIds());
        return result == 0 ? Result.success("删除成功") : Result.fail(-1, "删除失败");
    }

    @RequestMapping
    public List<String> queryAllProducts(){
        return ProductUnitEnum.getAllProductTypes();
    }

    @RequestMapping
    public List<Map<String, String>> queryAllTargetIndicators(){
        return queryRepoShowService.queryAllTargetIndicators();
    }

    @RequestMapping
    public QueryTargetIndicatorRsp queryTargetIndicators(@JsonrpcParam ProductTargetReq req){
        if (StringUtils.isBlank(req.getProductType())){
            throw new WrongWebParameterException("产品类型为空");
        }
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        return queryRepoShowService.queryTargetIndicators(req.getProductType(), req.getDate());
    }

    @RequestMapping
    public Result updateTarget(@CurrentUser TofUser tofUser, @JsonrpcParam UpdateTargetReq req){
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        if (StringUtils.isBlank(req.getProductType())){
            throw new WrongWebParameterException("产品类型为空");
        }
        if (ListUtils.isEmpty(req.getMapList())){
            return new Result();
        }
        String username = tofUser.getUsername();
        Integer result = queryRepoShowService.updateTarget(req, username);
        return result == 0 ? Result.success("更新成功") : Result.fail(-1, "更新失败");
    }

    @RequestMapping
    public Object isContainSupplyChainDate(@JsonrpcParam QueryRepoShowReq req) {
        return queryRepoShowService.isContainSupplyChainDate(req);
    }

    @RequestMapping
    public SupplyChainUploadExcelRsp uploadSuppleChainExcel(@RequestParam("file") MultipartFile file,
            @CurrentUser TofUser user) {
        String realUsername = user.getRealUsername();
        return queryRepoShowService.uploadSuppleChainExcel(file, realUsername);
    }

    @RequestMapping
    public String test() {
        dictService.getZoneIdByCampusName();
        return "success";
    }

    @RequestMapping
    public String syncYgInventory() {
        Date date = new Date();
        return queryRepoShowService.syncYgInventory(date);
    }

    @RequestMapping
    public DownloadBean exportCompanyInventoryDetails(@JsonrpcParam RepShowReq req){
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        FileNameAndBytesDTO dto = decisionViewService.exportCompanyInventoryData(DateUtils.formatDate(req.getDate()));
        return new DownloadBean(dto.getFileName(), dto.getBytes());
    }

    /**
     * 这里只需要简单的查询全部数据，因此直接返回，不用Service了
     * @param req   查询日期
     * @return
     */
    @RequestMapping
    public DecisionViewDetailRsp queryAllCompanyInvData(@JsonrpcParam RepShowReq req){
        if (req.getDate() == null){
            throw new WrongWebParameterException("日期参数格式有误");
        }
        DecisionViewDetailRsp rsp = new DecisionViewDetailRsp();
        rsp.setStatTime(DateUtils.formatDate(req.getDate()));
        List<DemandCsigCompanyInventoryDetailDO> all =
                demandDBHelper.getAll(DemandCsigCompanyInventoryDetailDO.class, "where stat_time = ?", req.getDate());
        rsp.setData(all);
        rsp.setSize(all.size());
        return rsp;
    }

}
