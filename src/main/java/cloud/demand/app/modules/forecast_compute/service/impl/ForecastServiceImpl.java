package cloud.demand.app.modules.forecast_compute.service.impl;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.forecast_compute.entity.ForecastComputeTaskDO;
import cloud.demand.app.modules.forecast_compute.entity.ForecastComputeTaskInputDO;
import cloud.demand.app.modules.forecast_compute.entity.ForecastComputeTaskRunDO;
import cloud.demand.app.modules.forecast_compute.enums.FillDataTypeEnum;
import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import cloud.demand.app.modules.forecast_compute.enums.TaskStatusEnum;
import cloud.demand.app.modules.forecast_compute.model.CreateTaskDTO;
import cloud.demand.app.modules.forecast_compute.model.CreateTaskInputDataDTO;
import cloud.demand.app.modules.forecast_compute.model.ForecastAlgorithms.Algorithm;
import cloud.demand.app.modules.forecast_compute.model.TaskOutputDTO;
import cloud.demand.app.modules.forecast_compute.model.TaskOutputDataDTO;
import cloud.demand.app.modules.forecast_compute.service.ForecastService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ForecastServiceImpl implements ForecastService {

    @Resource
    private DBHelper cdCommonDbHelper;

    @Override
    public void setRelativeTask(String taskUuid, String relativeTaskUuid) {
        if (StringTools.isBlank(taskUuid) || StringTools.isBlank(relativeTaskUuid)) {
            return;
        }
        ForecastComputeTaskDO task = cdCommonDbHelper.getOne(ForecastComputeTaskDO.class,
                "where task_uuid=?", taskUuid);
        if (task != null) {
            task.setRelativeTaskUuid(relativeTaskUuid);
            cdCommonDbHelper.update(task);
        }
    }

    @Override
    @Transactional("cdcommonTransactionManager")
    public List<String> createTasks(List<CreateTaskDTO> reqs) {
        List<String> taskUuid = new ArrayList<>();
        if (reqs == null) {
            return taskUuid;
        }
        for (CreateTaskDTO req : reqs) {
            if (req != null) {
                taskUuid.add(createTask(req));
            } else {
                taskUuid.add("");
            }
        }
        return taskUuid;
    }

    @Override
    @Transactional("cdcommonTransactionManager")
    public String createTask(CreateTaskDTO req) {
        // 1. 创建任务DO
        ForecastComputeTaskDO taskDO = checkAndBuildTaskDO(req);
        cdCommonDbHelper.insert(taskDO);

        // 2. 插入input值，并获取到所有日期用于后面补数
        // 说明：这里隐含了一个假设，就是input包含了全部日期，也即不存在一个日期，全部维度都缺该天的数据
        // 这个隐含假设是成立的，即不可能有一天，所有的维度下都没有数据，这种情况几乎不可能发生
        List<ForecastComputeTaskInputDO> inputDOs = ListUtils.transform(req.getInputData(),
                o -> CreateTaskInputDataDTO.trans(o));
        ListUtils.forEach(inputDOs, o -> o.setTaskId(taskDO.getId()));
        cdCommonDbHelper.insertBatchWithoutReturnId(inputDOs);

        List<LocalDate> allDate = ListUtils.transform(inputDOs, o -> o.getDate());
        ListUtils.sortAscNullLast(allDate, (o) -> o);

        LocalDate startDate = allDate.get(0);
        LocalDate endDate = allDate.get(allDate.size() - 1);

        // 3. 插入run分组
        List<ForecastComputeTaskInputDO> distinctDims = getDistinctDims(inputDOs, taskDO.getInputDimsCount());
        List<ForecastComputeTaskRunDO> taskRuns = new ArrayList<>();
        for (ForecastComputeTaskInputDO dim : distinctDims) {
            for (Algorithm algorithm : req.getAlgorithms()) {
                ForecastComputeTaskRunDO run = new ForecastComputeTaskRunDO();
                taskRuns.add(run);
                run.setTaskId(taskDO.getId());
                run.setStatus(TaskStatusEnum.NEW.getCode());
                run.setPredictAlgorithm(algorithm.getName().getName());
                run.setPredictAlgorithmArgs(Strings.join("@", algorithm.getParams()));
                run.setInputDateBegin(startDate);
                run.setInputDateEnd(endDate);
                run.setPredictIndexStart(taskDO.getPredictIndexStart());
                run.setPredictIndexEnd(taskDO.getPredictIndexEnd());

                // 根据预测周期来确定预测时间范围
                SerialIntervalEnum serialIntervalEnum = SerialIntervalEnum.getByCode(taskDO.getSerialInterval());
                if (serialIntervalEnum == SerialIntervalEnum.DAY) {
                    run.setPredictDateStart(endDate.plusDays(taskDO.getPredictIndexStart()));
                    run.setPredictDateEnd(endDate.plusDays(taskDO.getPredictIndexEnd()));
                } else if (serialIntervalEnum == SerialIntervalEnum.WEEK) {
                    run.setPredictDateStart(endDate.plusWeeks(taskDO.getPredictIndexStart()));
                    run.setPredictDateEnd(endDate.plusWeeks(taskDO.getPredictIndexEnd()));
                } else if (serialIntervalEnum == SerialIntervalEnum.MONTH) {
                    run.setPredictDateStart(endDate.plusMonths(taskDO.getPredictIndexStart()));
                    run.setPredictDateEnd(endDate.plusMonths(taskDO.getPredictIndexEnd()));
                }

                if (taskDO.getInputDimsCount() >= 1) {
                    run.setDim1(dim.getDim1());
                }
                if (taskDO.getInputDimsCount() >= 2) {
                    run.setDim2(dim.getDim2());
                }
                if (taskDO.getInputDimsCount() >= 3) {
                    run.setDim3(dim.getDim3());
                }
                if (taskDO.getInputDimsCount() >= 4) {
                    run.setDim4(dim.getDim4());
                }
                if (taskDO.getInputDimsCount() >= 5) {
                    run.setDim5(dim.getDim5());
                }
            }
        }
        cdCommonDbHelper.insertBatchWithoutReturnId(taskRuns);
        return taskDO.getTaskUuid();
    }

    @Override
    public TaskOutputDTO queryTaskOutput(String taskUuid) {
        ForecastComputeTaskDO taskDO = cdCommonDbHelper.getOne(
                ForecastComputeTaskDO.class, "where task_uuid=?", taskUuid);
        TaskOutputDTO result = new TaskOutputDTO();
        if (taskDO == null) {
            result.setIsDone(false);
            result.setMessage("taskUuid:" + taskUuid + "不存在");
            return result;
        }

        if (Objects.equals(taskDO.getStatus(), TaskStatusEnum.NEW.getCode())) {
            result.setIsDone(false);
            result.setMessage("taskUuid:" + taskUuid + "任务等待调度");
            return result;
        } else if (Objects.equals(taskDO.getStatus(), TaskStatusEnum.RUNNING.getCode())) {
            result.setIsDone(false);
            result.setMessage("taskUuid:" + taskUuid + "任务正在运行中");
            return result;
        } else if (Objects.equals(taskDO.getStatus(), TaskStatusEnum.FAIL.getCode())) {
            result.setIsDone(false);
            result.setMessage("taskUuid:" + taskUuid + "任务失败了");
            return result;
        } else if (Objects.equals(taskDO.getStatus(), TaskStatusEnum.DONE.getCode())) {
            result.setIsDone(true);
            result.setMessage("成功完成");

            List<OutputDTO> list = cdCommonDbHelper.getRaw(OutputDTO.class,
                    "SELECT a.dim1,a.dim2,a.dim3,a.dim4,a.dim5,b.date,b.date_str,b.`index`,b.value\n" +
                            "FROM `forecast_compute_task_run` a LEFT JOIN `forecast_compute_task_run_output` b\n" +
                            "ON a.id=b.`task_run_id`\n" +
                            "WHERE a.`task_id`=?\n" +
                            "ORDER BY a.dim1,a.dim2,a.dim3,a.dim4,a.dim5,b.date_str", taskDO.getId());
            result.setOutputData(ListUtils.transform(list, o -> OutputDTO.trans(o)));
        } else {
            result.setIsDone(false);
            result.setMessage("未知状态:" + taskDO.getStatus());
        }

        return result;
    }

    @Data
    public static class OutputDTO {

        @Column("date")
        private Date date;
        @Column("date_str")
        private String dateStr;
        @Column("index")
        private Integer index;
        @Column("dim1")
        private String dim1;
        @Column("dim2")
        private String dim2;
        @Column("dim3")
        private String dim3;
        @Column("dim4")
        private String dim4;
        @Column("dim5")
        private String dim5;
        @Column("value")
        private BigDecimal value;

        public static TaskOutputDataDTO trans(OutputDTO d) {
            TaskOutputDataDTO taskOutputDataDTO = new TaskOutputDataDTO();
            taskOutputDataDTO.setDate(d.getDate());
            taskOutputDataDTO.setDateStr(d.getDateStr());
            taskOutputDataDTO.setIndex(d.getIndex());
            taskOutputDataDTO.setValue(d.getValue());
            taskOutputDataDTO.setDim1(d.getDim1());
            taskOutputDataDTO.setDim2(d.getDim2());
            taskOutputDataDTO.setDim3(d.getDim3());
            taskOutputDataDTO.setDim4(d.getDim4());
            taskOutputDataDTO.setDim5(d.getDim5());
            return taskOutputDataDTO;
        }
    }

    private ForecastComputeTaskDO checkAndBuildTaskDO(CreateTaskDTO req) {
        ForecastComputeTaskDO taskDO = new ForecastComputeTaskDO();
        taskDO.setCreateUser(req.getCreateUser());
        taskDO.setTaskUuid(UUID.randomUUID().toString().replace("-", ""));
        taskDO.setTaskName(req.getTaskName());

        if (StringTools.isBlank(req.getTaskType())) {
            throw new WrongWebParameterException("taskType必须提供");
        } else {
            taskDO.setTaskType(req.getTaskType());
        }

        taskDO.setSeqType(req.getSeqType());
        taskDO.setStatus(TaskStatusEnum.NEW.getCode());
        taskDO.setSerialInterval(req.getSerialInterval());

        taskDO.setInputDimsCount(req.getInputDims());
        taskDO.setInputDimsName(req.getInputDimsName());
        taskDO.setOutputDimsCount(req.getInputDims());
        taskDO.setOutputDimsName(req.getInputDimsName());

        taskDO.setIsAutoFillData(req.getIsAutoFillData() == null ? 0 : (req.getIsAutoFillData() ? 1 : 0));
        if (taskDO.getIsAutoFillData() == 1) {
            taskDO.setFillDataType(FillDataTypeEnum.ZERO.getCode());
        }

        taskDO.setPredictIndexStart(req.getPredictIndexStart());
        taskDO.setPredictIndexEnd(req.getPredictIndexEnd());

        if (ListUtils.isEmpty(req.getInputData())) {
            throw new WrongWebParameterException("输入数据为空");
        }
        if (ListUtils.isEmpty(req.getAlgorithms())) {
            throw new WrongWebParameterException("预测算法必须提供至少一个");
        }

        return taskDO;
    }

    /**
     * 获得不同维度的值，每个维度只返回一种就好了，之类不去它的date和value，只取维度
     */
    private List<ForecastComputeTaskInputDO> getDistinctDims(List<ForecastComputeTaskInputDO> inputDOs, int inputDims) {
        Map<String, List<ForecastComputeTaskInputDO>> map = ListUtils.groupBy(inputDOs, o -> {
            StringBuilder sb = new StringBuilder();
            if (inputDims >= 1) {
                sb.append("@").append(o.getDim1());
            }
            if (inputDims >= 2) {
                sb.append("@").append(o.getDim2());
            }
            if (inputDims >= 3) {
                sb.append("@").append(o.getDim3());
            }
            if (inputDims >= 4) {
                sb.append("@").append(o.getDim4());
            }
            if (inputDims >= 5) {
                sb.append("@").append(o.getDim5());
            }
            return sb.toString();
        });

        List<ForecastComputeTaskInputDO> result = new ArrayList<>();
        for (List<ForecastComputeTaskInputDO> list : map.values()) {
            ForecastComputeTaskInputDO d = list.get(0);
            d.setDate(null); // 清除有歧义的数据，避免误用数据
            d.setValue(null); // 清除有歧义的数据，避免误用数据
            result.add(d);
        }
        return result;
    }

}
