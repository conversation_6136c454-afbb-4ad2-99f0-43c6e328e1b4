package cloud.demand.app.modules.forecast_compute.model;

import cloud.demand.app.modules.forecast_compute.entity.ForecastComputeTaskInputDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CreateTaskInputDataDTO {

    /**输入日期*/
    private Date date;
    /**数值*/
    private BigDecimal value;

    /**维度1*/
    private String dim1;
    /**维度2*/
    private String dim2;
    /**维度3*/
    private String dim3;
    /**维度4*/
    private String dim4;
    /**维度5*/
    private String dim5;

    public static ForecastComputeTaskInputDO trans(CreateTaskInputDataDTO d) {
        ForecastComputeTaskInputDO inputDO = new ForecastComputeTaskInputDO();
        inputDO.setDate(DateUtils.toLocalDate(d.getDate()));
        inputDO.setValue(d.getValue());
        inputDO.setDim1(d.getDim1());
        inputDO.setDim2(d.getDim2());
        inputDO.setDim3(d.getDim3());
        inputDO.setDim4(d.getDim4());
        inputDO.setDim5(d.getDim5());
        return inputDO;
    }

}
