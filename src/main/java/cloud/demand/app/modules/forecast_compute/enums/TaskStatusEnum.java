package cloud.demand.app.modules.forecast_compute.enums;

import lombok.Getter;


/**
 * <AUTHOR>
 */
@Getter
public enum TaskStatusEnum {

    /**
     * new
     */
    NEW("NEW", "初始"),

    /**
     * running
     */
    RUNNING("RUNNING", "运行中"),

    DONE("DONE","成功结束"),

    FAIL("FAIL", "失败");


    TaskStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private final String name;
    private final String code;

}
