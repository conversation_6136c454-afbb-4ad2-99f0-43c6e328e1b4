package cloud.demand.app.modules.forecast_compute.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("forecast_compute_task_run")
public class ForecastComputeTaskRunDO {

    /** 主键<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    @Column(value = "task_id")
    private Integer taskId;

    /** 开始运行时间<br/>Column: [run_time_start] */
    @Column(value = "run_time_start")
    private Date runTimeStart;

    /** 结束运行时间<br/>Column: [run_time_end] */
    @Column(value = "run_time_end")
    private Date runTimeEnd;

    /** 运行状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 预测算法<br/>Column: [predict_algorithm] */
    @Column(value = "predict_algorithm")
    private String predictAlgorithm;

    /** 预测算法参数,逗号分隔<br/>Column: [predict_algorithm_args] */
    @Column(value = "predict_algorithm_args")
    private String predictAlgorithmArgs;

    /** 输入开始日期<br/>Column: [input_date_begin] */
    @Column(value = "input_date_begin")
    private LocalDate inputDateBegin;

    /** 输入结束日期<br/>Column: [input_date_end] */
    @Column(value = "input_date_end")
    private LocalDate inputDateEnd;

    /** 预测起始位置,index>=1<br/>Column: [predict_index_start] */
    @Column(value = "predict_index_start")
    private Integer predictIndexStart;

    /** 预测结束位置,index>=1<br/>Column: [predic_index_end] */
    @Column(value = "predict_index_end")
    private Integer predictIndexEnd;

    /** 实际预测起始日期<br/>Column: [predict_date_start] */
    @Column(value = "predict_date_start")
    private LocalDate predictDateStart;

    /** 实际预测结束日期<br/>Column: [predict_date_end] */
    @Column(value = "predict_date_end")
    private LocalDate predictDateEnd;

    /** 维度1<br/>Column: [dim1] */
    @Column(value = "dim1", insertValueScript = "''")
    private String dim1;

    /** 维度2<br/>Column: [dim2] */
    @Column(value = "dim2", insertValueScript = "''")
    private String dim2;

    /** 维度3<br/>Column: [dim3] */
    @Column(value = "dim3", insertValueScript = "''")
    private String dim3;

    /** 维度4<br/>Column: [dim4] */
    @Column(value = "dim4", insertValueScript = "''")
    private String dim4;

    /** 维度5<br/>Column: [dim5] */
    @Column(value = "dim5", insertValueScript = "''")
    private String dim5;

}
