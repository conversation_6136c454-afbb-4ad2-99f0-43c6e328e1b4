package cloud.demand.app.modules.sop_return.domain.report;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroupBy;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryReportDimRes{
    private List<QueryReportDimResList> data;
    private List<String> dimValue;
}
