package cloud.demand.app.modules.sop_return.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_return.entity.report.DwsSopReturnReportMifDO;
import cloud.demand.app.modules.sop_return.entity.task.SopReturnAdsTask;
import cloud.demand.app.modules.sop_return.entity.task.SopReturnDwsTask;
import cloud.demand.app.modules.sop_return.enums.SopReturnTaskEnum;
import cloud.demand.app.modules.sop_return.service.SopReturnCleanService;
import cloud.demand.app.modules.sop_return.task.process.SopReturnAdsProcess;
import cloud.demand.app.modules.sop_return.task.process.SopReturnDwsProcess;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;

@Service
@Slf4j
public class SopReturnDwsWork extends AbstractSopWork<SopReturnDwsTask> {
    @Resource
    private SopReturnDwsProcess process;

    @Resource
    private SopReturnAdsProcess nextProcess;

    @Resource
    protected DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SopReturnCleanService cleanService;

    @Override
    public ISopProcess<SopReturnDwsTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SopReturnTaskEnum.SOP_RETURN_DWS;
    }

    @TaskLog(taskName = "SopReturnDwsWork")
    @Scheduled(fixedRate = 30 * 1000)
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(SopReturnDwsTask task) {
        // 删除旧数据
        doDelete(task.getVersion());
        // 拷贝dwd的数据(物理机退回+cvm退回)到dws
        doCopy(task);
        // 开启ads的任务
        doNext(task);
    }

    private void doDelete(String version) {
        CkDBUtils.delete(ckcldStdCrpDBHelper,version, DwsSopReturnReportMifDO.class);
    }

    private void doCopy(SopReturnDwsTask task){
        String sql = ORMUtils.getSql("/sql/sop_return/dwd2dws.sql");
        sql = sql.replace("${version}", task.getVersion());
        // 清洗字段：is_num_effective，phy_device_type，phy_device_family，generation_type，core_type，big_class，product_big_class
        // 其中 big_class和product_big_class cvm和物理机处理不同，有cvm_big_class 和 device_big_class, product_big_class同理
        Map<String, String> clean = cleanService.clean(task.getVersion());
        for (Map.Entry<String, String> entry : clean.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            sql = sql.replace("${"+key+"}",value);
        }
        ckcldStdCrpDBHelper.executeRaw(sql);
    }


    private void doNext(SopReturnDwsTask task){
        SopReturnAdsTask t = new SopReturnAdsTask();
        t.setDwsId(task.getId());
        t.setVersion(task.getVersion());
        nextProcess.initTask(t,SopReturnTaskEnum.SOP_RETURN_ADS);
    }
}
