package cloud.demand.app.modules.sop_return.service;

import cloud.demand.app.modules.sop_return.service.impl.SopReturnCommonServiceImpl;
import org.apache.commons.collections4.map.MultiKeyMap;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SopReturnCommonService {

    /**
     * 根据cvm规格集合查询cvm信息，底表：bas_obs_cloud_cvm_type
     * @param models cvm规格集合
     * @return cvm信息
     */
    public List<SopReturnCommonServiceImpl.CvmInstanceInfo> byCvmInstanceModel(Set<String> models);


    /**
     * 根据物理机规格集合查物理机信息，底表：bas_stratege_device_type
     * @param devices 物理机规格集合
     * @return 物理机信息
     */
    public  List<SopReturnCommonServiceImpl.DeviceInfo> byDeviceType(Set<String> devices);


    /**
     * 根据规划产品名称集合查询大类，产品大类信息，底表：cloud_demand_csig_resource_view_category
     * @param planProducts 规划产品名称集合
     * @return  大类，产品大类集合
     */
    public List<SopReturnCommonServiceImpl.PlanProductInfo> byPlanProductName(Set<String> planProducts);

}
