package cloud.demand.app.modules.sop_return.domain.report.resource;

import cloud.demand.app.modules.sop.domain.report.SopResourceReq;
import cloud.demand.app.modules.sop_return.domain.report.QueryReportReturnReq;
import lombok.Data;
import lombok.EqualsAndHashCode;


/** v2-退回报表查询 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryReportReturnResourceReq extends SopResourceReq {

    /** 通用查询请求 */
    private QueryReportReturnReq commonReq;
}
