# Sop退回报表

> 需求文档：
>
> https://doc.weixin.qq.com/sheet/e3_ABUAkAbzACo2rfqZz49TS0IPRrN0P?scode=AJEAIQdfAAob1EjZy0AekAsAaVAPU&tab=9mejtw

> 功能链接：（测试、生产）
>
> https://exp-crp.woa.com/report/sop/return
>
> https://crp.woa.com/report/sop/return

> 目录结构：
> 
> ```
> ├── domain                                  dto类
> │   ├── report                            报表dto类
> │   └── transFromData                            版本变化原因dto类
> ├── entity                                  实体类
> │   ├── other                             其他(清洗、版本变化原因)
> │   ├── report                            报表
> │   └── task                              任务
> ├── enums                                   枚举(退回指标、任务类型。。。)
> ├── frame                                   框架(分组、where条件)
> │   ├── group                             分组(报表查询list转indexItem分组)
> │   │   └── interfaces                分组实体类需要继承的接口
> │   └── where                             where条件(注解形式)
> │       └── anno                            where需要的注解(包括where和group以及groupBy)
> ├── group                                   分组业务类(继承了分组框架)
> ├── service                                 业务处理类
> │   └── impl                              业务处理实现类
> ├── task                                    定时任务
> │   ├── process                           处理db流程
> │   └── work                              实际工作
> ├── utils                                   工具类(包括处理反射获取类及其父类的所有字段和dim拼接)
> └── web                                     controller层  
> ```

> 新增一个过滤条件：
> 1. QueryReportCommon 新增字段
> 2. SopReturnGroupProcessor 
>    2.1 transFormTR 设置属性
> 3. QueryReturnReportItemResp 
>    3.1 dimData 新增维度

> 基本流程:
> 
> https://iwiki.woa.com/pages/viewpage.action?pageId=4008736264

> 发布清单：
>
> https://iwiki.woa.com/pages/viewpage.action?pageId=4008667959

> API接口：
>
> https://iwiki.woa.com/pages/viewpage.action?pageId=4008545602