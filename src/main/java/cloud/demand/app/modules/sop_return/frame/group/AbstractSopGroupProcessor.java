package cloud.demand.app.modules.sop_return.frame.group;

import cloud.demand.app.modules.sop_return.frame.group.interfaces.*;
import cloud.demand.app.modules.sop_return.utils.FunctionUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

public abstract class AbstractSopGroupProcessor<T extends ISopEntity,R extends ISopData<RL>,RL extends ISopIndexItem> implements ISopGroupProcessor<T,R,RL> {

    // ===================== init field =====================

    protected final String startVersion;
    protected final String endVersion;


    // ===================== process field =====================
    private final Class<R> rClass;
    private final Class<RL> rlClass;
    private Map<String, Field> dimFieldMap;
    private RL rl = null;
    private R r = null;

    public AbstractSopGroupProcessor(String startVersion, String endVersion) {
        this.startVersion = startVersion;
        this.endVersion = endVersion;
        Type[] actualTypeArguments = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();
        rClass = (Class<R>) actualTypeArguments[1];
        rlClass = (Class<RL>) actualTypeArguments[2];
    }

    @SneakyThrows
    public List<RL> initRLT(String dimKey) {
        List<RL> ret = new ArrayList<>();
        if (rl == null){
            rl = rlClass.getDeclaredConstructor().newInstance();
            rl.setIsNull(true);
        }
        for (ISopIndex index : getISopIndex()) {
            RL clone = (RL)rl.clone();
            clone.setDim(dimKey + "#" + index.getDesc());
            clone.setIndexName(index.getName());
            clone.setName(index.getDesc());
            ret.add(clone);
        }
        return ret;
    }

    @SneakyThrows
    @Override
    public R transFormTR(T t, String dimKey) {
        if (r == null){
            r = rClass.getDeclaredConstructor().newInstance();
        }
        R clone = (R)r.clone();
        clone.setDimKey(dimKey);
        clone.setResType(t.getResType());
        clone.setResPoolType(t.getResPoolType());
        clone.setObsProjectType(t.getObsProjectType());
        clone.setBgName(t.getBgName());
        clone.setCustomBgName(t.getCustomBgName());
        clone.setDeptName(t.getDeptName());
        clone.setOldDeptName(t.getOldDeptName());
        clone.setPlanProductName(t.getPlanProductName());
        clone.setCustomhouseTitle(t.getCustomhouseTitle());
        clone.setCountryName(t.getCountryName());
        clone.setCityName(t.getCityName());
        clone.setCmdbCampusName(t.getCmdbCampusName());
        clone.setCmdbModuleName(t.getCmdbModuleName());
        clone.setTxyZoneName(t.getTxyZoneName());
        clone.setObsProjectType(t.getObsProjectType());
        clone.setCvmGinsFamily(t.getCvmGinsFamily());
        clone.setCvmGinsType(t.getCvmGinsType());
        clone.setPhyDeviceFamily(t.getPhyDeviceFamily());
        clone.setPhyDeviceType(t.getPhyDeviceType());
        clone.setIsHedge(t.getIsHedge());
        clone.setHasHedged(t.getHasHedged());
        clone.setIsCa(t.getIsCa());
        clone.setCapacityUnit(t.getCapacityUnit());
        clone.setIndexItems(initRLT(dimKey));
        transFormTRL(t,clone);
        return clone;
    }

    @SneakyThrows
    @Override
    public String getDimKey(T t, List<String> dims) {
        StringBuilder sbr = new StringBuilder();
        Map<String, Function<T, Object>> fieldFunMap = this.getTFieldFunMap();
        if (!CollectionUtils.isEmpty(fieldFunMap)){
            if (!CollectionUtils.isEmpty(dims)){
                for (String dim : dims) {
                    Function<T, Object> tfs = fieldFunMap.get(dim);
                    sbr.append(tfs == null? null : tfs.apply(t));
                    sbr.append("@");
                }
            }
        }else {
            if (dimFieldMap == null){
                Type[] actualTypeArguments = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();
                Field[] declaredFields = ((Class<T>) actualTypeArguments[0]).getDeclaredFields();
                dimFieldMap = new HashMap<>();
                for (Field declaredField : declaredFields) {
                    declaredField.setAccessible(true);
                    dimFieldMap.put(declaredField.getName(),declaredField);
                }
            }
            if (!CollectionUtils.isEmpty(dims)){
                for (String dim : dims) {
                    Field field = dimFieldMap.get(dim);
                    sbr.append(field == null? null : field.get(t));
                    sbr.append("@");
                }
            }
        }
        if (sbr.length()>0){
            sbr.setLength(sbr.length()-1);
        }
        return sbr.toString();
    }

    @Override
    public void transFormTRL(T t, R r) {
        for (RL indexItem : r.getIndexItems()) {
            if (Objects.equals(t.getIndex(),indexItem.getIndexName())){
                if (Objects.equals(t.getVersion(),startVersion)){
                    indexItem.setStartNum(FunctionUtil.sum(t.getSumNum(),indexItem.getStartNum()));
                    indexItem.setStartCoreNum(FunctionUtil.sum(t.getSumCoreNum(),indexItem.getStartCoreNum()));
                    indexItem.setStartCapacity(FunctionUtil.sum(t.getSumCapacity(),indexItem.getStartCapacity()));
                }
                if (Objects.equals(t.getVersion(),endVersion)){
                    indexItem.setEndNum(FunctionUtil.sum(t.getSumNum(),indexItem.getEndNum()));
                    indexItem.setEndCoreNum(FunctionUtil.sum(t.getSumCoreNum(),indexItem.getEndCoreNum()));
                    indexItem.setEndCapacity(FunctionUtil.sum(t.getSumCapacity(),indexItem.getEndCapacity()));
                }
                indexItem.setIsNull(false);
                break;
            }
        }
    }

    public abstract ISopIndex[] getISopIndex();
    public abstract Map<String,Function<T,Object>> getTFieldFunMap();

    public abstract Map<String,Function<R,Object>> getRFieldFunMap();
}
