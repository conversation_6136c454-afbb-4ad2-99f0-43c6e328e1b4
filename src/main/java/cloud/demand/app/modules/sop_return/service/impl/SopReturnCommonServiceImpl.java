package cloud.demand.app.modules.sop_return.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop_return.service.SopReturnCommonService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import lombok.Data;
import org.apache.commons.collections4.map.MultiKeyMap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SopReturnCommonServiceImpl implements SopReturnCommonService {

    @Resource
    private DBHelper obsDBHelper;

    @Resource
    private DBHelper resourcedbDBHelper;

    @Resource
    private DBHelper yuntiDBHelper;

//    @PostConstruct
    public void init(){
    }

    @Override
    public List<CvmInstanceInfo> byCvmInstanceModel(Set<String> models){
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.andInIfValueNotEmpty("CvmInstanceModel",models == null ? null : new ArrayList<>(models));
        return obsDBHelper.getRaw(CvmInstanceInfo.class,
                "select HostDeviceClass,CvmInstanceGroup ,CvmInstanceType ,CvmInstanceModel ,CoreType" +
                        " from bas_obs_cloud_cvm_type " + whereContent.getSql(), whereContent.getParams());
    }

    @Override
    public  List<DeviceInfo> byDeviceType(Set<String> devices){
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.andInIfValueNotEmpty("NAME",devices == null ? null : new ArrayList<>(devices));
        return resourcedbDBHelper.getRaw(DeviceInfo.class,
                "select NAME,DeviceFamilyName,DeviceCategory,generation_type,device_class2 " +
                        " from bas_stratege_device_type " + whereContent.getSql(), whereContent.getParams());
    }

    @HiSpeedCache(expireSecond = 600,continueFetchSecond = 1800,keyScript = "args[0]")
    @Override
    public List<PlanProductInfo> byPlanProductName(Set<String> planProducts){
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.andEqual("deleted",0);
        whereContent.andInIfValueNotEmpty("plan_product_name",planProducts == null ? null : new ArrayList<>(planProducts));
        return yuntiDBHelper.getRaw(PlanProductInfo.class,
                "select plan_product_name,compute_type,category_1,category_2,category_3,category_4,category_5 from cloud_demand_csig_resource_view_category " +
                        whereContent.getSql(),whereContent.getParams());

    }


    // =========== 实体类 ===========

    @Data
    public static class PlanProductInfo{
        @Column("plan_product_name")
        private String planProductName;

        /** cpu、gpu，部分类型需要通过该字段区分 */
        @Column("compute_type")
        private String computeType;

        @Column("category_1")
        private String category_1;

        /** 大类 */
        @Column("category_2")
        private String category_2;

        /** 产品大类 */
        @Column("category_3")
        private String category_3;

        @Column("category_4")
        private String category_4;

        @Column("category_5")
        private String category_5;

    }

    @Data
    public static class DeviceInfo{

        @Column("NAME")
        private String deviceType;

        /** 设备策略: 专用、通用 */
        @Column("NAME")
        private String DeviceCategory;

        /** 判断是是否为Gpu */
        @Column("device_class2")
        private String deviceClass2;

        @Column("DeviceFamilyName")
        private String deviceFamilyName;

        @Column("generation_type")
        private Integer generationType;
    }

    @Data
    public static class CvmInstanceInfo{

        @Column("HostDeviceClass")
        private String hostDeviceClass;
        @Column("CvmInstanceGroup")
        private String CvmInstanceGroup;
        @Column("CvmInstanceType")
        private String CvmInstanceType;
        @Column("CvmInstanceModel")
        private String CvmInstanceModel;
        @Column("CoreType")
        private Integer CoreType;
    }
}
