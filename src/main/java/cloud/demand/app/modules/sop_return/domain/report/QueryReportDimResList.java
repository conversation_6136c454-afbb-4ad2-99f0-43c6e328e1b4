package cloud.demand.app.modules.sop_return.domain.report;

import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroupBy;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryReportDimResList{
    private String dimValue;
    private BigDecimal num;
    private BigDecimal coreNum;
    private BigDecimal capacity;
}
