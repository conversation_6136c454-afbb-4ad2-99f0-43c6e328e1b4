package cloud.demand.app.modules.sop_demand.model.req.resource;

import cloud.demand.app.modules.sop.domain.report.SopResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandReportReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

/** v2-需求报表查询 */
@Data
@EqualsAndHashCode(callSuper = true)

public class QueryDemandReportResourceReq extends SopResourceReq {

    /** 通用查询请求 */
    private QueryDemandReportReq commonReq;

}
