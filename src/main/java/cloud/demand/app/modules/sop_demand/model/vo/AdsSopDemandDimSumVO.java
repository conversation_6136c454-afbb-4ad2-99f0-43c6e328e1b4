package cloud.demand.app.modules.sop_demand.model.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AdsSopDemandDimSumVO {

    @Column("dim")
    private String dim;

    @Column("sumNum")
    private BigDecimal sumNum;

    @Column("sumCoreNum")
    private BigDecimal sumCoreNum;

    @Column("sumCapacity")
    private BigDecimal sumCapacity;

}
