package cloud.demand.app.modules.sop_demand.model.req;


import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.sop.domain.report.IHasYuntiVersion;
import cloud.demand.app.modules.sop.domain.report.IVersionReq;
import cloud.demand.app.modules.sop.enums.HolidayFieldEnum;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportGroupBy;
import cloud.demand.app.modules.sop_return.frame.where.anno.SopReportWhere;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class QueryDemandReportReq extends QueryDemandCommonReq implements IVersionReq, IHasYuntiVersion {

    @NotBlank(message = "起始版本不能为空")
    @SopReportWhere("version")
    @SopReportGroupBy
    private String startVersion;

    @NotBlank(message = "结束版本不能为空")
    @SopReportWhere(columnValue = "version")
    @SopReportGroupBy
    private String endVersion;

    @NotBlank(message = "起始指标年月不能为空")
    @SopReportWhere(sql = "holiday_year_month >= ?")
    private String startYearMonth;

    @NotBlank(message = "结束指标年月不能为空")
    @SopReportWhere(sql = "holiday_year_month <= ?")
    private String endYearMonth;

    @SopReportGroupBy
    private List<String> dim;

    /** 加锁的维度 */
    private List<String> lockDim;

    @JsonIgnore
    @SopReportGroupBy(value = "index")
    private String index;

    private String paramType;

    private QueryTableReq.Page page;

    private String dataSortColName;
    private Boolean isDim = false;
    private String numType = "startNum";
    private String dataSortRule = "desc";


    public QueryDemandReportReq copy(){
        QueryDemandReportReq ret = new QueryDemandReportReq();
        ret.setStartVersion(this.getStartVersion());
        ret.setEndVersion(this.getEndVersion());
        ret.setStartYearMonth(this.getStartYearMonth());
        ret.setEndYearMonth(this.getEndYearMonth());
        ret.setDim(this.getDim());
        ret.setLockDim(this.getLockDim());
        ret.setIndex(this.getIndex());
        ret.setParamType(this.getParamType());
        ret.setPage(this.getPage());
        ret.setDataSortColName(this.getDataSortColName());
        ret.setIsDim(this.getIsDim());
        ret.setNumType(this.getNumType());
        ret.setDataSortRule(this.getDataSortRule());
        ret.setCodeVersion(this.getCodeVersion());
        ret.setNonDeptName(this.getNonDeptName());
        ret.setIgnoreComputingPlatform(this.getIgnoreComputingPlatform());
        ret.setBusinessType(this.getBusinessType());
        ret.setObsBusinessType(this.getObsBusinessType());
        ret.setResType(this.getResType());
        ret.setObsProjectType(this.getObsProjectType());
        ret.setResPoolType(this.getResPoolType());
        ret.setBgName(this.getBgName());
        ret.setCustomBgName(this.getCustomBgName());
        ret.setDeptName(this.getDeptName());
        ret.setOldDeptName(this.getOldDeptName());
        ret.setPlanProductName(this.getPlanProductName());
        ret.setCustomhouseTitle(this.getCustomhouseTitle());
        ret.setCountryName(this.getCountryName());
        ret.setCityName(this.getCityName());
        ret.setCmdbCampusName(this.getCmdbCampusName());
        ret.setCmdbModuleName(this.getCmdbModuleName());
        ret.setTxyZoneName(this.getTxyZoneName());
        ret.setCvmGinsKingdom(this.getCvmGinsKingdom());
        ret.setCvmGinsFamily(this.getCvmGinsFamily());
        ret.setCvmGinsType(this.getCvmGinsType());
        ret.setPhyDeviceBigFamily(this.getPhyDeviceBigFamily());
        ret.setPhyDeviceFamily(this.getPhyDeviceFamily());
        ret.setPhyDeviceType(this.getPhyDeviceType());
        ret.setCoreType(this.getCoreType());
        ret.setCpuPlatform(this.getCpuPlatform());
        ret.setCpuType(this.getCpuType());
        ret.setCpuModel(this.getCpuModel());
        ret.setNetworkCardType(this.getNetworkCardType());
        ret.setGpuType(this.getGpuType());
        ret.setGpuCardType(this.getGpuCardType());
        ret.setGpuModel(this.getGpuModel());
        ret.setIsHedge(this.getIsHedge());
        ret.setHasHedged(this.getHasHedged());
        ret.setIsCa(this.getIsCa());
        ret.setCapacityUnit(this.getCapacityUnit());
        ret.setIndexYear(this.getIndexYear());
        ret.setIndexMonth(this.getIndexMonth());
        ret.setIndexYearMonth(this.getIndexYearMonth());
        ret.setIndexWeek(this.getIndexWeek());
        ret.setIsNumEffective(this.getIsNumEffective());
        ret.setDimKey(this.getDimKey());
        ret.setDimValue(this.getDimValue());
        return ret;

    }
}
