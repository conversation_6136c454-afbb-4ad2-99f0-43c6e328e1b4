package cloud.demand.app.modules.sop_demand.model.resp;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class QueryDemandTrendItemResp {

    private String indexYearMonth;

    private Boolean isStartNull;

    private Boolean isEndNull;

    private BigDecimal startNum;
    private BigDecimal endNum;
    private BigDecimal startCoreNum;
    private BigDecimal endCoreNum;
    private BigDecimal startCapacity;
    private BigDecimal endCapacity;

}
