package cloud.demand.app.modules.sop_demand.web;

import cloud.demand.app.modules.sop.domain.report.IHasYuntiVersion;
import cloud.demand.app.modules.sop_demand.entity.other.SopDemandVersionChangeResultDO;
import cloud.demand.app.modules.sop_demand.model.req.CreateDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandDimReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandReportReq;
import cloud.demand.app.modules.sop_demand.model.req.QueryDemandTrendReq;
import cloud.demand.app.modules.sop_demand.model.req.UpdateDemandChangeResultReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandDimResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandParamsResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandReportResourceReq;
import cloud.demand.app.modules.sop_demand.model.req.resource.QueryDemandTrendResourceReq;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandDimResp;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandReportItemResp;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandTrendResp;
import cloud.demand.app.modules.sop_demand.service.SopDemandJxcService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.util.YuntiUtils;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@JsonrpcController("/sop-demand/jxc")
public class SopDemandJxcController {

    @Resource
    SopDemandJxcService sopDemandJxcService;

    // 以下为sop需求报表接口

    @Operation(description = "查询需求报表下拉框")
    @RequestMapping
    public List<String> queryParams(@JsonrpcParam @Valid QueryDemandColumnReq req,
                                    BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return sopDemandJxcService.queryParams(req);
    }

    @RequestMapping
    public List<String> queryParamsByResource(@JsonrpcParam @Valid QueryDemandParamsResourceReq req) {
        Assert.notNull(req.getParamType(), "getParamType is null");
        return sopDemandJxcService.queryParamsByResource(req);
    }

    @Deprecated
    @Operation(description = "查询需求报表信息")
    @RequestMapping
    public QueryDemandReportItemResp queryReportItem(@JsonrpcParam @Valid QueryDemandReportReq req,
                                                     BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return sopDemandJxcService.queryReportItem(req);
    }

    /**
     * v2-查询退回报表信息，按cvm和物理机分开查（分组和排序一起处理）
     * @param req 请求体
     * @return
     */
    @RequestMapping
    public QueryDemandReportItemResp queryReportItemByResource(@JsonrpcParam @Valid QueryDemandReportResourceReq req) {
        Assert.notNull(req.getCommonReq(), "getCommonReq is null");
        Assert.notNull(req.getCommonReq().getStartYearMonth(), "getStartYearMonth is null");
        Assert.notNull(req.getCommonReq().getEndYearMonth(), "getEndYearMonth is null");
        return sopDemandJxcService.queryReportItemByResource(req);
    }

    @Operation(description = "查询需求变化趋势")
    @Deprecated
    @RequestMapping
    public QueryDemandTrendResp queryTrend(@JsonrpcParam @Valid QueryDemandTrendReq req,
                                           BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return sopDemandJxcService.queryTrend(req);
    }

    /**
     * v2-查询变化趋势(按资源类型分开查)
     * @param req
     * @return
     */
    @RequestMapping
    public QueryDemandTrendResp queryTrendByResource(@JsonrpcParam @Valid QueryDemandTrendResourceReq req){
        Assert.notNull(req.getCommonReq(), "getCommonReq is null");
        Assert.notNull(req.getCommonReq().getStartYearMonth(), "getStartYearMonth is null");
        Assert.notNull(req.getCommonReq().getEndYearMonth(), "getEndYearMonth is null");
        Assert.notNull(req.getCommonReq().getIndex(), "getIndex is null");
        return sopDemandJxcService.queryTrendByResource(req);
    }

    @Operation(description = "查询需求维度分组统计数据")
    @Deprecated
    @RequestMapping
    public QueryDemandDimResp queryDim(@JsonrpcParam @Valid QueryDemandDimReq req,
                                       BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return sopDemandJxcService.queryDim(req);
    }

    /**
     * v2-查询维度分组统计数据
     * @param req
     * @return
     */
    @RequestMapping
    public QueryDemandDimResp queryDimByResource(@JsonrpcParam @Valid QueryDemandDimResourceReq req){
        Assert.notNull(req.getCommonReq(), "getCommonReq is null");
        Assert.notNull(req.getCommonReq().getStartYearMonth(), "getStartYearMonth is null");
        Assert.notNull(req.getCommonReq().getEndYearMonth(), "getEndYearMonth is null");
        Assert.notNull(req.getCommonReq().getIndex(), "getIndex is null");
        Assert.notNull(req.getCommonReq().getDim(), "getDim is null");
        return sopDemandJxcService.queryDimByResource(req);
    }

    // 以下为sop需求版本变化原因接口

    @Operation(description = "查询sop需求版本变化原因")
    @RequestMapping
    public List<SopDemandVersionChangeResultDO> queryChangeResult(@JsonrpcParam @Valid QueryDemandChangeResultReq req,
                                                                  BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        return sopDemandJxcService.queryChangeResult(req);
    }

    @Operation(description = "创建sop需求版本变化原因")
    @RequestMapping
    public void createChangeResult(@JsonrpcParam @Valid CreateDemandChangeResultReq req,
                                   BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        sopDemandJxcService.createSopDemandResult(req);
    }

    @Operation(description = "更新sop需求版本变化原因")
    @RequestMapping
    public void updateChangeResult(@JsonrpcParam @Valid UpdateDemandChangeResultReq req,
                                   BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        sopDemandJxcService.updateSopDemandResult(req);
    }

    @Operation(description = "通过id主键组删除sop需求版本变化原因")
    @RequestMapping
    public void deleteChangeResult(@JsonrpcParam @Valid IdsDTO req,
                                   BindingResult bindingResult) {
        YuntiUtils.throwErrorsIfAny(bindingResult);
        sopDemandJxcService.deleteSopDemandResult(req.getIds());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class IdsDTO {
        @NotEmpty(message = "待删除的id不能为空")
        private List<Long> ids;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class QueryDemandColumnReq implements IHasYuntiVersion {
        @NotBlank(message = "起始版本不能为空")
        private String startVersion;

        @NotBlank(message = "结束版本不能为空")
        private String endVersion;

        @NotBlank(message = "查询字段不能为空")
        private String paramType;

    }

}
