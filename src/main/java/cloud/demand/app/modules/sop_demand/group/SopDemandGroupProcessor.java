package cloud.demand.app.modules.sop_demand.group;

import cloud.demand.app.modules.sop_demand.entity.report.AdsSopDemandReportMifAnyDO;
import cloud.demand.app.modules.sop_demand.entity.report.AdsSopDemandReportMifSumDO;
import cloud.demand.app.modules.sop_demand.enums.SopDemandIndex;
import cloud.demand.app.modules.sop_demand.model.resp.QueryDemandReportItemResp;
import cloud.demand.app.modules.sop_return.frame.group.AbstractSopGroupProcessor;
import cloud.demand.app.modules.sop_return.frame.group.interfaces.ISopIndex;
import cloud.demand.app.modules.sop_return.utils.FunctionUtil;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;

public class SopDemandGroupProcessor extends AbstractSopGroupProcessor<AdsSopDemandReportMifSumDO, QueryDemandReportItemResp.Data, QueryDemandReportItemResp.IndexItem> {

    private Map<String, Function<AdsSopDemandReportMifSumDO, Object>> tfunctionMap;

    private Map<String, Function<QueryDemandReportItemResp.Data, Object>> rfunctionMap;

    @Override
    public void transFormTRL(AdsSopDemandReportMifSumDO t, QueryDemandReportItemResp.Data r) {
        for (QueryDemandReportItemResp.IndexItem indexItem : r.getIndexItems()) {
            if (Objects.equals(t.getIndex(), indexItem.getIndexName())) {
                if (Objects.equals(t.getVersion(), startVersion)) {
                    indexItem.setStartNum(FunctionUtil.sum(t.getSumNum(), indexItem.getStartNum()));
                    indexItem.setStartCoreNum(FunctionUtil.sum(t.getSumCoreNum(), indexItem.getStartCoreNum()));
                    indexItem.setStartCapacity(FunctionUtil.sum(t.getSumCapacity(), indexItem.getStartCapacity()));
                }
                if (Objects.equals(t.getVersion(), endVersion)) {
                    indexItem.setEndNum(FunctionUtil.sum(t.getSumNum(), indexItem.getEndNum()));
                    indexItem.setEndCoreNum(FunctionUtil.sum(t.getSumCoreNum(), indexItem.getEndCoreNum()));
                    indexItem.setEndCapacity(FunctionUtil.sum(t.getSumCapacity(), indexItem.getEndCapacity()));
                }
                setDiff(indexItem);
                indexItem.setIsNull(false);
                break;
            }
        }
    }

    protected void setDiff(QueryDemandReportItemResp.IndexItem indexItem) {
        indexItem.setDiffNum(FunctionUtil.sub(indexItem.getEndNum(), indexItem.getStartNum()));
        indexItem.setDiffCoreNum(FunctionUtil.sub(indexItem.getEndCoreNum(), indexItem.getStartCoreNum()));
        indexItem.setDiffCapacity(FunctionUtil.sub(indexItem.getEndCapacity(), indexItem.getStartCapacity()));
    }


    private <T, R extends T> Map<String, Function<R, Object>> initMap(Class<T> tClass) {
        Map<String, Function<R, Object>> ret = new HashMap<>();
        for (Field field : tClass.getDeclaredFields()) {
            field.setAccessible(true);
            ret.put(field.getName(), (item) -> {
                try {
                    return field.get(item);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            });
        }
        return ret;
    }

    private void init() {
        tfunctionMap = initMap(AdsSopDemandReportMifAnyDO.class);
        rfunctionMap = initMap(AdsSopDemandReportMifAnyDO.class);
    }

    public SopDemandGroupProcessor(String startVersion, String endVersion) {
        super(startVersion, endVersion);
        init();
    }

    @Override
    public ISopIndex[] getISopIndex() {
        return SopDemandIndex.getISopIndex();
    }

    @Override
    public Map<String, Function<AdsSopDemandReportMifSumDO, Object>> getTFieldFunMap() {
        return tfunctionMap;
    }

    @Override
    public Map<String, Function<QueryDemandReportItemResp.Data, Object>> getRFieldFunMap() {
        return rfunctionMap;
    }


    @Override
    public QueryDemandReportItemResp.Data transFormTR(AdsSopDemandReportMifSumDO vo, String dimKey) {
        QueryDemandReportItemResp.Data data = super.transFormTR(vo, dimKey);
        data.setIndexDate(vo.getIndexDate());
        data.setIndexYear(vo.getIndexYear());
        data.setIndexMonth(vo.getIndexMonth());
        data.setIndexYearMonth(vo.getIndexYearMonth());
        data.setIndexWeek(vo.getIndexWeek());
        data.setBusinessType(vo.getBusinessType());
        data.setObsBusinessType(vo.getObsBusinessType());
        data.setPhyDeviceBigFamily(vo.getPhyDeviceBigFamily());
        data.setCoreType(vo.getCoreType());
        data.setCpuPlatform(vo.getCpuPlatform());
        data.setCpuType(vo.getCpuType());
        data.setCpuModel(vo.getCpuModel());
        data.setNetworkCardType(vo.getNetworkCardType());
        data.setGpuType(vo.getGpuType());
        data.setGpuCardType(vo.getGpuCardType());
        data.setGpuModel(vo.getGpuModel());
        data.setModBizType(vo.getModBizType());
        data.setBusinessRange(vo.getBusinessRange());

        // 需求明细报表的，退回报表没有
        data.setCvmGinsKingdom(vo.getCvmGinsKingdom());

        return data;
    }

}
