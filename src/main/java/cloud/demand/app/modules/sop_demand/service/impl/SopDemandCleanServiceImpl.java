package cloud.demand.app.modules.sop_demand.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.sop.enums.ResourceType;
import cloud.demand.app.modules.sop_demand.entity.other.SopDemandDwsClean;
import cloud.demand.app.modules.sop_demand.entity.report.DwsSopDemandReportMifDO;
import cloud.demand.app.modules.sop_demand.service.SopDemandCleanService;
import cloud.demand.app.modules.sop_demand.service.SopDemandCommonService;
import cloud.demand.app.modules.sop_return.enums.CoreType;
import cloud.demand.app.modules.sop_return.service.impl.SopReturnCommonServiceImpl;
import cloud.demand.app.modules.sop_return.utils.SopCaseBuilder;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SopDemandCleanServiceImpl implements SopDemandCleanService {

    @Resource
    DBHelper ckcldStdCrpDBHelper;

    @Resource
    SopDemandCommonService commonService;

    private List<SopDemandDwsClean> getRaw(String version) {
        String sql = ORMUtils.getSql("/sql/sop_demand/dwd2dws_select.sql");
        sql = sql.replace("${version}",version);
        return ckcldStdCrpDBHelper.getRaw(SopDemandDwsClean.class, sql);
    }

    @Override
    public Map<String, String> clean(String version) {

        Map<String, String> cleanFieldMap = new HashMap<>();

        // 通过cvm规格清洗物理机规格和物理机类型
        List<SopDemandDwsClean> raw = getRaw(version);
        execUpdateSql(raw, cleanFieldMap);

        // 通用字段清洗
        doClean(raw,version, cleanFieldMap);

        return cleanFieldMap;

    }

    private Map<List<String>, String> filter(Map<List<String>, String> map,Set<String> values){
        Map<List<String>, String> ret = new HashMap<>();
        for (Map.Entry<List<String>, String> entry : map.entrySet()) {
            List<String> key = entry.getKey();
            String value = entry.getValue();
            key.removeIf(item->!values.contains(item));
            if (!key.isEmpty()){
                ret.put(key,value);
            }
        }
        return ret;
    }



    private void doClean(List<SopDemandDwsClean> raw,String version, Map<String, String> cleanFieldMap) {
        Set<String> values = raw.stream().map(SopDemandDwsClean::getPhyDeviceType).collect(Collectors.toSet());

        // 加载辅助数据
        Map<List<String>, String> deviceFamilyMap = filter(commonService.getDeviceFamily(),values);
        Map<List<String>, String> cpuPlatformMap = filter(commonService.getCpuPlatform(),values);
        Map<List<String>, String> cpuTypeMap = filter(commonService.getCpuType(),values);
        Map<List<String>, String> cpuModelMap = filter(commonService.getCpuModel(),values);
        Map<List<String>, String> gpuTypeMap = filter(commonService.getGpuType(),values);
        Map<List<String>, String> gpuCardTypeMap = filter(commonService.getGpuCardType(),values);
        Map<List<String>, String> gpuModelMap = filter(commonService.getGpuModel(),values);

        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.andEqual("version", version);

        String deviceFamilySql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceBigFamily),
                deviceFamilyMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceBigFamily), deviceFamilySql);

        String cpuPlatformSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuPlatform),
                cpuPlatformMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuPlatform), cpuPlatformSql);

        String cpuTypeSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuType),
                cpuTypeMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuType), cpuTypeSql);

        String cpuModelSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuModel),
                cpuModelMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCpuModel), cpuModelSql);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getNetworkCardType),
                "substr(phy_device_type, length(phy_device_type) - position(reverse(phy_device_type), '-') + 2)");

        String gpuTypeSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuType),
                gpuTypeMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuType), gpuTypeSql);

        String gpuCardTypeSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuCardType),
                gpuCardTypeMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuCardType), gpuCardTypeSql);

        String gpuModelSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuModel),
                gpuModelMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getGpuModel), gpuModelSql);

    }

    /**
     * 通过cvm规格清洗物理机规格和物理机类型
     */
    private void execUpdateSql(List<SopDemandDwsClean> raw, Map<String, String> cleanFieldMap) {

        // 物理机设备规格集合
        Set<String> deviceSet = new HashSet<>();
        // cvm设备规格集合
        Set<String> cvmInstanceSet = new HashSet<>();
        // 规格产品 -> cvm规格
        Map<String, Set<String>> planProductCvmMap = new HashMap<>();

        for (SopDemandDwsClean clean : raw) {
            String resType = clean.getResType();
            if (Objects.equals(resType, ResourceType.CVM.getName())) {
                cvmInstanceSet.add(clean.getCvmGinsType());
                planProductCvmMap.computeIfAbsent(clean.getPlanProductName(), k -> new HashSet<>()).add(clean.getCvmGinsType());
            } else if (Objects.equals(resType, ResourceType.DEVICE.getName())) {
                deviceSet.add(clean.getPhyDeviceType());
            }
        }

        // cvmModel -> deviceInfo（物理机类型+物理机规格）
        Map<Object, SopReturnCommonServiceImpl.DeviceInfo> cvmDeviceMap = new HashMap<>();

        // cvmModel -> coreType
        Map<String, List<String>> cvmModel2coreTypeMap = new HashMap<>();

        // cvmModel -> HostDeviceClass
        Map<String, String> hostDeviceClass2CvmInstance = new HashMap<>();

        if (!CollectionUtils.isEmpty(cvmInstanceSet)) {
            List<SopReturnCommonServiceImpl.CvmInstanceInfo> cvmInstanceInfos = commonService.byCvmInstanceModel(cvmInstanceSet);
            for (SopReturnCommonServiceImpl.CvmInstanceInfo value : cvmInstanceInfos) {
                deviceSet.add(value.getHostDeviceClass());
                hostDeviceClass2CvmInstance.put(value.getHostDeviceClass(), value.getCvmInstanceModel());
                cvmModel2coreTypeMap.computeIfAbsent(CoreType.getNameByCode(value.getCoreType()),
                        k -> new ArrayList<>()).add(value.getCvmInstanceModel());
            }
        }
        if (!CollectionUtils.isEmpty(deviceSet)) {
            List<SopReturnCommonServiceImpl.DeviceInfo> deviceInfos = commonService.byDeviceType(deviceSet);
            for (SopReturnCommonServiceImpl.DeviceInfo deviceInfo : deviceInfos) {
                if (hostDeviceClass2CvmInstance.containsKey(deviceInfo.getDeviceType())) {
                    cvmDeviceMap.put(hostDeviceClass2CvmInstance.get(deviceInfo.getDeviceType()), deviceInfo);
                }
            }
        }

        Map<Object, String> coreTypeMap = new HashMap<>();
        cvmModel2coreTypeMap.forEach((key, value) -> {
            if (key != null) {
                coreTypeMap.put(value.toArray(), key);
            }
        });

        // 处理cvm的物理机类型和物理机规格
        updateDeviceInfoByCvmInstanceModel(cvmDeviceMap, cleanFieldMap);

        // 更新核类型
        updateCoreType(coreTypeMap, cleanFieldMap);
    }

    /**
     * 通过cvm规格清洗物理机规格和物理机类型
     *
     * @param cvmDeviceMap cvm规格和物理机信息映射
     */
    private void updateDeviceInfoByCvmInstanceModel(Map<Object, SopReturnCommonServiceImpl.DeviceInfo> cvmDeviceMap, Map<String, String> cleanFieldMap) {
        Function<SopReturnCommonServiceImpl.DeviceInfo, String>[] functions = new Function[2];
        functions[0] = SopReturnCommonServiceImpl.DeviceInfo::getDeviceType;
        functions[1] = SopReturnCommonServiceImpl.DeviceInfo::getDeviceFamilyName;
        String[] sources = {
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCvmGinsType)
        };
        String[] targets = {
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getPhyDeviceFamily)
        };
        cleanFieldMap.putAll(SopSelectCaseBuilder.build(sources, targets, cvmDeviceMap, functions));
    }

    /**
     * 通过物理机规格和cvm规格清洗核类型和代次(需要跑前缀方法--updateDeviceInfoByCvmInstanceModel)
     *
     * @param coreTypeMap 核类型和cvm规格映射
     */
    private void updateCoreType(Map<Object, String> coreTypeMap, Map<String, String> cleanFieldMap) {

        String coreTypeSetSql = SopCaseBuilder.build2(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCvmGinsType),
                ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCoreType),
                coreTypeMap);

        cleanFieldMap.put(ORMUtils.getColumnByMethod(DwsSopDemandReportMifDO::getCoreType), coreTypeSetSql);
    }

}
