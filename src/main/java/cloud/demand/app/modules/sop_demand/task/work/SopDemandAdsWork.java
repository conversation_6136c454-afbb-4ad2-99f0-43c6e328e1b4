package cloud.demand.app.modules.sop_demand.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import cloud.demand.app.modules.sop.service.CommonDbHelper.ProductDeptBgInfo;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.TwoMap;
import cloud.demand.app.modules.sop_demand.entity.report.AdsSopDemandReportMifDO;
import cloud.demand.app.modules.sop_demand.entity.report.DwsSopDemandReportMifDO;
import cloud.demand.app.modules.sop_demand.entity.task.SopDemandAdsTask;
import cloud.demand.app.modules.sop_demand.enums.SopDemandTaskEnum;
import cloud.demand.app.modules.sop_demand.task.process.SopDemandAdsProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import com.pugwoo.dbhelper.DBHelper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SopDemandAdsWork extends AbstractSopWork<SopDemandAdsTask> {

    @Resource
    private SopDemandAdsProcess process;

    @Resource
    protected DBHelper ckcldStdCrpDBHelper;

    @Resource
    private CommonDbHelper commonDbHelper;

    @Override
    public ISopProcess<SopDemandAdsTask> getTask() {
        return process;
    }

    @Override
    public ITaskEnum getEnum() {
        return SopDemandTaskEnum.SOP_DEMAND_ADS;
    }

    @TaskLog(taskName = "SopDemandAdsWork")
    @Scheduled(fixedRate = 30 * 1000)
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(SopDemandAdsTask task) {
        // 删除原数据
        doDelete(task.getVersion());
        // 拷贝dws数据到ads
        doCopy(task);
        // 删除dws的数据
        doDeleteDws(task);
    }

    private void doDelete(String version) {
        CkDBUtils.delete(ckcldStdCrpDBHelper, version, AdsSopDemandReportMifDO.class);
    }

    private void doCopy(SopDemandAdsTask task) {
        String sql = ORMUtils.getSql("/sql/sop_demand/dws2ads.sql");
        sql = sql.replace("${version}", task.getVersion());
        // 清洗老部门
        TwoMap<Integer, String, ProductDeptBgInfo> oldProductInfoMap = commonDbHelper.getOldProductInfoMap();
        Map<String, ProductDeptBgInfo> map2 = oldProductInfoMap.getMap2();
        Map<String, List<String>> map = new HashMap<>();
        map2.forEach((k,v)-> {
            if (SoeCommonUtils.isNotBlank(v.getDeptName())){
                map.computeIfAbsent(v.getDeptName(), ls -> new ArrayList<>()).add(k);
            }
        });
        Map<List<String>, String> flipMap = new HashMap<>();
        map.forEach((k,v)-> flipMap.put(v,k));
        // 清洗老部门
        String build = SopSelectCaseBuilder.build("plan_product_name", CommonUtils.withSqlString(Constant.EMPTY_VALUE), flipMap);
        sql = SimpleSqlBuilder.doReplace(sql, "old_dept_name", build);
        ckcldStdCrpDBHelper.executeRaw(sql);
    }

    private void doDeleteDws(SopDemandAdsTask task) {
        CkDBUtils.delete(ckcldStdCrpDBHelper, task.getVersion(), DwsSopDemandReportMifDO.class);
    }

}
