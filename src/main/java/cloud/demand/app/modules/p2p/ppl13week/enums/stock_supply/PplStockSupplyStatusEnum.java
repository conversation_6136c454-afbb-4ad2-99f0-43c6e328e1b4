package cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply;

import lombok.Getter;

import java.util.Objects;

/**
 * 对冲状态
 */
@Getter
public enum PplStockSupplyStatusEnum {

    INIT("INIT", "待下发"),

    /**对应于数据下方节点2*/
    SENDING("SENDING", "下发中"),

    /**已下发之后就看各自cvm/cbs的子状态了，对应于对冲数据回传节点3*/
    SENT("SENT", "已下发"),

    /**已完成，即已接收完所有的对冲结果*/
    DONE("DONE", "已完成"),

    /** 失败的时候，会一直重试 */
    FAIL("FAIL", "失败"),

    /** fail 是可以重试的 ERROR 不重试 */
    ERROR("ERROR", "错误"),

    DISCARD("DISCARD", "废弃")

    ;

    final private String code;
    final private String name;

    PplStockSupplyStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplStockSupplyStatusEnum getByCode(String code) {
        for (PplStockSupplyStatusEnum e : PplStockSupplyStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplStockSupplyStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}