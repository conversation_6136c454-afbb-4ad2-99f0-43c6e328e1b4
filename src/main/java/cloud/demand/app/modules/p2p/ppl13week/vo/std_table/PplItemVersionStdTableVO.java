package cloud.demand.app.modules.p2p.ppl13week.vo.std_table;

import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosAZEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplCosStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator.ChineseName;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.RegexUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.Date;
import java.util.Map;
import lombok.Data;

@Data
public class PplItemVersionStdTableVO {

    @Column(value = "version_code")
    private String versionCode;

    @Column(value = "version_name")
    private String versionName;

    @Column(value = "demand_begin_year")
    private Integer demandBeginYear;

    @Column(value = "demand_begin_month")
    private Integer demandBeginMonth;

    @Column(value = "demand_end_year")
    private Integer demandEndYear;

    @Column(value = "demand_end_month")
    private Integer demandEndMonth;

    @Column(value = "version_type")
    private String versionType;

    @Column(value = "version_status")
    private String versionStatus;

    @Column(value = "version_note")
    private String versionNote;

    @Column(value = "version_creator")
    private String versionCreator;

    @Column(value = "start_audit_time")
    private Date startAuditTime;

    @Column(value = "end_audit_time")
    private Date endAuditTime;

    @Column(value = "version_create_time")
    private Date versionCreateTime;

    @Column(value = "version_group_industry_dept")
    private String versionGroupIndustryDept;

    @Column(value = "version_group_product")
    private String versionGroupProduct;

    @Column(value = "version_group_status")
    private String versionGroupStatus;

    @Column(value = "ppl_id")
    private String pplId;

    @Column(value = "ppl_order")
    private String pplOrder;

    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "status")
    private String status;

    @Column(value = "instance_num")
    private Integer instanceNum;

    @Column(value = "total_core")
    private Integer totalCore;

    @Column(value = "source")
    private String source;

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "industry")
    private String industry;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "customer_type")
    private String customerType;

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "war_zone")
    private String warZone;

    @Column(value = "customer_source")
    private String customerSource;

    @Column(value = "creator")
    private String creator;

    @Column(value = "submit_time")
    private Date submitTime;

    @Column(value = "product")
    private String product;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "demand_scene")
    private String demandScene;

    @Column(value = "project_name")
    private String projectName;

    @Column(value = "bill_type")
    private String billType;

    @Column(value = "win_rate")
    private BigDecimal winRate;

    @Column(value = "begin_buy_date")
    private LocalDate beginBuyDate;

    @Column(value = "end_buy_date")
    private LocalDate endBuyDate;

    @Column(value = "begin_elastic_date")
    private String beginElasticDate;

    @Column(value = "end_elastic_date")
    private String endElasticDate;

    @Column(value = "note")
    private String note;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "total_disk")
    private Integer totalDisk;

    @Column(value = "alternative_instance_type")
    private String alternativeInstanceType;

    @Column(value = "affinity_type")
    private String affinityType;

    @Column(value = "affinity_value")
    private BigDecimal affinityValue;

    @Column(value = "system_disk_type")
    private String systemDiskType;

    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    @Column(value = "data_disk_type")
    private String dataDiskType;

    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    @Column(value = "gpu_type")
    private String gpuType;

    @Column(value = "gpu_num")
    private BigDecimal gpuNum;

    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    @Column(value = "is_accept_adjust")
    private Integer isAcceptAdjust;

    @Column(value = "accept_gpu")
    private String acceptGpu;

    @Column(value = "biz_scene")
    private String bizScene;

    @Column(value = "biz_detail")
    private String bizDetail;

    @Column(value = "service_time")
    private String serviceTime;

    @Column(value = "gpu_product_type")
    private String gpuProductType;

    @Column(value = "is_comd")
    private Integer isComd;

    @Column(value = "source_ppl_id")
    private String sourcePplId;

    @Column(value = "is_lock")
    private Integer isLock;

    @Column(value = "is_from_forecast")
    private Integer isFromForecast;

    @Column(value = "is_forecast_inner")
    private Integer isForecastInner;

    @Column(value = "is_spike")
    private Integer isSpike;

    @Column(value = "database_name")
    @ChineseName("数据库名称")
    private String databaseName;

    /**
     *  是否多AZ，数据库产品
     */
    @Column(value = "more_than_one_az")
    private Boolean moreThanOneAZ;

    /**
     * 数据库存储类型，数据库产品
     * @see PplDatabaseStorageEnum
     */
    @Column(value = "database_storage_type")
    @ChineseName("数据库存储类型")
    private String databaseStorageType;

    /**
     * 实例部署类型，数据库产品
     * @see PplDeloyTypeEnum
     */
    @Column(value = "deploy_type")
    @ChineseName("实例部署类型")
    private String deployType;

    /**
     * 实例架构类型，数据库产品
     * @see PplDataBaseFrameworkEnum
     */
    @Column(value = "framework_type")
    @ChineseName("实例架构类型")
    private String frameworkType;

    /**
     *  分片数量，数据库产品
     */
    @Column(value = "slice_num")
    @ChineseName("分片数量")
    private Integer sliceNum;

    /**
     *  副本数量，数据库产品
     */
    @Column(value = "replica_num")
    @ChineseName("副本数量")
    private Integer replicaNum;

    /**
     *  只读数量，数据库产品
     */
    @Column(value = "read_only_num")
    @ChineseName("只读数量")
    private Integer readOnlyNum;

    /**
     *  数据库实例规格（2C16G 这种），数据库产品
     */
    @Column(value = "database_specs")
    @ChineseName("数据库实例规格")
    private String databaseSpecs;

    /**
     *  数据库存储量，单位GB，数据库产品
     */
    @Column(value = "database_storage")
    private BigDecimal databaseStorage;

    /**
     *  总数据库存储量，单位GB，数据库产品
     */
    @Column(value = "total_database_storage")
    @ChineseName("总数据库存储量")
    private BigDecimal totalDatabaseStorage;

    /**
     *  COS存储类型，COS产品
     * @see PplCosStorageEnum
     */
    @Column(value = "cos_storage_type")
    @ChineseName("COS存储类型")
    private String cosStorageType;

    /**
     *  单AZ、多AZ，COS产品
     * @see PplCosAZEnum
     */
    @Column(value = "cos_az")
    private String cosAZ;

    /**
     *  COS存储量，单位PB，COS产品
     */
    @Column(value = "cos_storage")
    private BigDecimal cosStorage;

    /**
     *  带宽，单位 Gbit/s，COS产品
     */
    @Column(value = "bandwidth")
    private Integer bandwidth;

    /**
     * qps，COS产品
     */
    @Column(value = "qps")
    private Integer qps;

    /**
     *  总COS存储量，单位PB，COS产品
     */
    @Column(value = "total_cos_storage")
    @ChineseName("总COS存储量")
    private BigDecimal totalCosStorage;

    /**
     * 单台核心数<br/>
     */
    @Column(value = "instance_model_core_num")
    private Integer instanceModelCoreNum;

    /**
     * 单台内存数，单位GB<br/>
     */
    @Column(value = "instance_model_ram_num")
    private Integer instanceModelRamNum;

    /**
     * 总内存数，单位GB<br/>Column: [total_memory]
     */
    @Column(value = "total_memory")
    @ChineseName("总内存数")
    private Integer totalMemory;



    public static YearMonth parseYearMonth(String versionCode, Date startAuditTime, Date versionCreateTime) {
        // versionCode的前缀一定是V_yyyyMMdd，取里面的yyyy和MM
        if (versionCode != null && RegexUtils.isMatch(versionCode, "V_\\d{8}.*")) {
            String year = versionCode.substring(2, 6);
            String month = versionCode.substring(6, 8);
            Integer yearI = NumberUtils.parseInt(year);
            Integer monthI = NumberUtils.parseInt(month);
            if (yearI != null && yearI >= 2000 && yearI < 2100 && monthI != null && monthI >= 1 && monthI <= 12) {
                return YearMonth.of(yearI, monthI);
            }
        }

        Date time = startAuditTime;
        if (time == null) {
            time = versionCreateTime;
        }
        if (time != null) {
            return YearMonth.of(DateUtils.getYear(time), DateUtils.getMonth(time));
        }

        return null;
    }

    /**
     *   按规则解析versionCode，返回日期，解析不了时返回 null <br/>
     *   versionCode 格式 : V_yyyyMMdd <br/>
     *   例: 输入 V_20240101, 返回 {@code LocalDate.of(2024, 1, 1)}
     */
    public static LocalDate parseLocalDate(String versionCode) {
        try {
            // versionCode的前缀一定是V_yyyyMMdd，取里面的yyyy和MM
            if (versionCode != null && RegexUtils.isMatch(versionCode, "V_\\d{8}.*")) {
                String year = versionCode.substring(2, 6);
                String month = versionCode.substring(6, 8);
                String date = versionCode.substring(8, 10);
                Integer yearI = NumberUtils.parseInt(year);
                Integer monthI = NumberUtils.parseInt(month);
                Integer dateI = NumberUtils.parseInt(date);
                if (yearI != null && yearI >= 2000 && yearI < 2100 && monthI != null && monthI >= 1 && monthI <= 12) {
                    return LocalDate.of(yearI, monthI, dateI);
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public static DwdCrpPplItemVersionCfDO transTo(PplItemVersionStdTableVO vo,
                                                   Map<String, String> regionNameToCustomhouseTitleMap) {
        DwdCrpPplItemVersionCfDO odsCrpPplItemVersionCfDO = new DwdCrpPplItemVersionCfDO();
        odsCrpPplItemVersionCfDO.setVersionCode(StdUtils.handleStr(vo.getVersionCode()));
        YearMonth yearMonth = parseYearMonth(vo.getVersionCode(), vo.getStartAuditTime(), vo.getVersionCreateTime());
        if (yearMonth != null) {
            odsCrpPplItemVersionCfDO.setVersionYear(yearMonth.getYear());
            odsCrpPplItemVersionCfDO.setVersionMonth(yearMonth.getMonthValue());
        }
        odsCrpPplItemVersionCfDO.setVersionCodeDate(parseLocalDate(vo.getVersionCode()));
        odsCrpPplItemVersionCfDO.setVersionName(StdUtils.handleStr(vo.getVersionName()));
        odsCrpPplItemVersionCfDO.setVersionBeginYear(vo.getDemandBeginYear());
        odsCrpPplItemVersionCfDO.setVersionBeginMonth(vo.getDemandBeginMonth());
        odsCrpPplItemVersionCfDO.setVersionEndYear(vo.getDemandEndYear());
        odsCrpPplItemVersionCfDO.setVersionEndMonth(vo.getDemandEndMonth());
        odsCrpPplItemVersionCfDO.setVersionType(StdUtils.handleStr(vo.getVersionType()));
        odsCrpPplItemVersionCfDO.setVersionTypeName(StdUtils.handleStr(PplVersionTypeEnum.getNameByCode(vo.getVersionType())));
        odsCrpPplItemVersionCfDO.setVersionStatus(StdUtils.handleStr(vo.getVersionStatus()));
        odsCrpPplItemVersionCfDO.setVersionStatusName(StdUtils.handleStr(PplVersionStatusEnum.getNameByCode(vo.getVersionStatus())));
        odsCrpPplItemVersionCfDO.setVersionNote(StdUtils.handleStr(vo.getVersionNote()));
        odsCrpPplItemVersionCfDO.setVersionCreator(StdUtils.handleStr(vo.getVersionCreator()));
        odsCrpPplItemVersionCfDO.setVersionStartAuditTime(vo.getStartAuditTime());
        odsCrpPplItemVersionCfDO.setVersionEndAuditTime(vo.getEndAuditTime());
        odsCrpPplItemVersionCfDO.setVersionGroupIndustryDept(StdUtils.handleStr(vo.getVersionGroupIndustryDept()));
        odsCrpPplItemVersionCfDO.setVersionGroupProduct(StdUtils.handleStr(vo.getVersionGroupProduct()));
        odsCrpPplItemVersionCfDO.setVersionGroupStatus(StdUtils.handleStr(vo.getVersionGroupStatus()));
        odsCrpPplItemVersionCfDO.setVersionGroupStatusName(StdUtils.handleStr(PplVersionGroupStatusEnum.getNameByCode(vo.getVersionGroupStatus())));
        odsCrpPplItemVersionCfDO.setPplId(StdUtils.handleStr(vo.getPplId()));
        odsCrpPplItemVersionCfDO.setPplOrder(StdUtils.handleStr(vo.getPplOrder()));
        odsCrpPplItemVersionCfDO.setUpdateTime(vo.getUpdateTime());
        odsCrpPplItemVersionCfDO.setStatus(StdUtils.handleStr(vo.getStatus()));
        odsCrpPplItemVersionCfDO.setStatusName(StdUtils.handleStr(PplItemStatusEnum.getNameByCode(vo.getStatus())));
        odsCrpPplItemVersionCfDO.setInstanceNum(vo.getInstanceNum());
        odsCrpPplItemVersionCfDO.setTotalCore(vo.getTotalCore());
        odsCrpPplItemVersionCfDO.setSource(StdUtils.handleStr(vo.getSource()));
        odsCrpPplItemVersionCfDO.setSourceName(StdUtils.handleStr(PplOrderSourceTypeEnum.getNameByCode(vo.getSource())));
        odsCrpPplItemVersionCfDO.setYear(vo.getYear());
        odsCrpPplItemVersionCfDO.setMonth(vo.getMonth());
        odsCrpPplItemVersionCfDO.setCustomerUin(StdUtils.handleStr(vo.getCustomerUin()));
        odsCrpPplItemVersionCfDO.setIndustry(StdUtils.handleStr(vo.getIndustry()));
        odsCrpPplItemVersionCfDO.setIndustryDept(StdUtils.handleStr(vo.getIndustryDept()));
        odsCrpPplItemVersionCfDO.setCustomerType(StdUtils.handleStr(vo.getCustomerType()));
        odsCrpPplItemVersionCfDO.setCustomerTypeName(StdUtils.handleStr(CustomerTypeEnum.getNameByCode(vo.getCustomerType())));
        odsCrpPplItemVersionCfDO.setCustomerName(StdUtils.handleStr(vo.getCustomerName()));
        odsCrpPplItemVersionCfDO.setCustomerShortName(StdUtils.handleStr(vo.getCustomerShortName()));
        odsCrpPplItemVersionCfDO.setWarZone(StdUtils.handleStr(vo.getWarZone()));
        odsCrpPplItemVersionCfDO.setCustomerSource(StdUtils.handleStr(vo.getCustomerSource()));
        odsCrpPplItemVersionCfDO.setSubmitUser(StdUtils.handleStr(vo.getCreator()));
        odsCrpPplItemVersionCfDO.setSubmitTime(vo.getSubmitTime());
        odsCrpPplItemVersionCfDO.setProduct(StdUtils.handleStr(vo.getProduct()));
        odsCrpPplItemVersionCfDO.setDemandType(StdUtils.handleStr(vo.getDemandType()));
        odsCrpPplItemVersionCfDO.setDemandTypeName(StdUtils.handleStr(PplDemandTypeEnum.getNameByCode(vo.getDemandType())));
        odsCrpPplItemVersionCfDO.setDemandScene(StdUtils.handleStr(vo.getDemandScene()));
        odsCrpPplItemVersionCfDO.setProjectName(StdUtils.handleStr(vo.getProjectName()));
        odsCrpPplItemVersionCfDO.setBillType(StdUtils.handleStr(vo.getBillType()));
        odsCrpPplItemVersionCfDO.setWinRate(vo.getWinRate());
        odsCrpPplItemVersionCfDO.setBeginBuyDate(vo.getBeginBuyDate());
        odsCrpPplItemVersionCfDO.setEndBuyDate(vo.getEndBuyDate());
        odsCrpPplItemVersionCfDO.setBeginElasticTime(StdUtils.handleStr(vo.getBeginElasticDate()));
        odsCrpPplItemVersionCfDO.setEndElasticTime(StdUtils.handleStr(vo.getEndElasticDate()));
        odsCrpPplItemVersionCfDO.setNote(StdUtils.handleStr(vo.getNote()));
        odsCrpPplItemVersionCfDO.setCustomhouseTitle(StdUtils.handleStr(regionNameToCustomhouseTitleMap.get(vo.getRegionName())));
        odsCrpPplItemVersionCfDO.setRegionName(StdUtils.handleStr(vo.getRegionName()));
        odsCrpPplItemVersionCfDO.setZoneName(StdUtils.handleStr(vo.getZoneName()));
        odsCrpPplItemVersionCfDO.setInstanceType(StdUtils.handleStr(vo.getInstanceType()));
        odsCrpPplItemVersionCfDO.setInstanceModel(StdUtils.handleStr(vo.getInstanceModel()));
        odsCrpPplItemVersionCfDO.setTotalDisk(vo.getTotalDisk());
        odsCrpPplItemVersionCfDO.setAlternativeInstanceType(StdUtils.handleStr(vo.getAlternativeInstanceType()));
        odsCrpPplItemVersionCfDO.setAffinityType(StdUtils.handleStr(vo.getAffinityType()));
        odsCrpPplItemVersionCfDO.setAffinityValue(vo.getAffinityValue());
        odsCrpPplItemVersionCfDO.setSystemDiskType(StdUtils.handleStr(vo.getSystemDiskType()));
        odsCrpPplItemVersionCfDO.setSystemDiskStorage(vo.getSystemDiskStorage());
        odsCrpPplItemVersionCfDO.setSystemDiskNum(vo.getSystemDiskNum());
        odsCrpPplItemVersionCfDO.setDataDiskType(StdUtils.handleStr(vo.getDataDiskType()));
        odsCrpPplItemVersionCfDO.setDataDiskStorage(vo.getDataDiskStorage());
        odsCrpPplItemVersionCfDO.setDataDiskNum(vo.getDataDiskNum());
        odsCrpPplItemVersionCfDO.setGpuType(StdUtils.handleStr(vo.getGpuType()));
        odsCrpPplItemVersionCfDO.setGpuNum(vo.getGpuNum());
        odsCrpPplItemVersionCfDO.setTotalGpuNum(vo.getTotalGpuNum());
        odsCrpPplItemVersionCfDO.setIsAcceptAdjustGpu(vo.getIsAcceptAdjust());
        odsCrpPplItemVersionCfDO.setAcceptGpu(StdUtils.handleStr(vo.getAcceptGpu()));
        odsCrpPplItemVersionCfDO.setBizScene(StdUtils.handleStr(vo.getBizScene()));
        odsCrpPplItemVersionCfDO.setBizDetail(StdUtils.handleStr(vo.getBizDetail()));
        odsCrpPplItemVersionCfDO.setServiceTime(StdUtils.handleStr(vo.getServiceTime()));
        odsCrpPplItemVersionCfDO.setGpuProductType(StdUtils.handleStr(vo.getGpuProductType()));
        odsCrpPplItemVersionCfDO.setIsComd(vo.getIsComd());
        odsCrpPplItemVersionCfDO.setSourcePplId(StdUtils.handleStr(vo.getSourcePplId()));
        odsCrpPplItemVersionCfDO.setIsLock(vo.getIsLock());
        odsCrpPplItemVersionCfDO.setIsFromForecast(vo.getIsFromForecast());
        odsCrpPplItemVersionCfDO.setIsForecastInner(vo.getIsForecastInner());
        odsCrpPplItemVersionCfDO.setIsSpike(vo.getIsSpike());
        odsCrpPplItemVersionCfDO.setDatabaseName(vo.getDatabaseName());
        odsCrpPplItemVersionCfDO.setMoreThanOneAZ(vo.getMoreThanOneAZ());
        odsCrpPplItemVersionCfDO.setDatabaseStorageType(vo.getDatabaseStorageType());
        odsCrpPplItemVersionCfDO.setDeployType(vo.getDeployType());
        odsCrpPplItemVersionCfDO.setFrameworkType(vo.getFrameworkType());
        odsCrpPplItemVersionCfDO.setSliceNum(vo.getSliceNum());
        odsCrpPplItemVersionCfDO.setReplicaNum(vo.getReplicaNum());
        odsCrpPplItemVersionCfDO.setReadOnlyNum(vo.getReadOnlyNum());
        odsCrpPplItemVersionCfDO.setDatabaseSpecs(vo.getDatabaseSpecs());
        odsCrpPplItemVersionCfDO.setDatabaseStorage(vo.getDatabaseStorage());
        odsCrpPplItemVersionCfDO.setTotalDatabaseStorage(vo.getTotalDatabaseStorage());
        odsCrpPplItemVersionCfDO.setCosStorageType(vo.getCosStorageType());
        odsCrpPplItemVersionCfDO.setCosAZ(vo.getCosAZ());
        odsCrpPplItemVersionCfDO.setCosStorage(vo.getCosStorage());
        odsCrpPplItemVersionCfDO.setBandwidth(vo.getBandwidth());
        odsCrpPplItemVersionCfDO.setQps(vo.getQps());
        odsCrpPplItemVersionCfDO.setTotalCosStorage(vo.getTotalCosStorage());
        odsCrpPplItemVersionCfDO.setInstanceModelCoreNum(vo.getInstanceModelCoreNum());
        odsCrpPplItemVersionCfDO.setInstanceModelRamNum(vo.getInstanceModelRamNum());
        odsCrpPplItemVersionCfDO.setTotalMemory(vo.getTotalMemory());
        return odsCrpPplItemVersionCfDO;
    }

}
