package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionTypeEnum;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;

/**
 * 用户查询分组概览信息
 */
@Data
public class PplVersionGroupInfoVO extends PplVersionGroupDO {

    @RelatedColumn(localColumn = "version_code", remoteColumn = "version_code")
    private PplVersionDO pplVersionDO;

    /**
     * 按审批顺序来，record_version严格递增
     */
    @RelatedColumn(localColumn = "id", remoteColumn = "version_group_id", extraWhere = "where biz_deleted = 0 order by id ")
    private List<PplVersionGroupRecordDO> records;

    public String getVersionTypeName() {
        if (pplVersionDO == null) {
            return "[未知]";
        }
        String versionType = pplVersionDO.getVersionType();
        PplVersionTypeEnum type = PplVersionTypeEnum.getByCode(versionType);
        return type == null ? "[未知]" : type.getName();
    }

    /**
     * 正常是有的，返回null是异常的，由上游log记录和处理
     */
    public PplVersionGroupRecordDO getLastRecord() {
        return ListUtils.isEmpty(records) ? null : records.get(records.size() - 1);
    }

    /**
     * 正常是有的，查询倒数第1个record id
     */
    public Long getLastRecordId() {
        if (ListUtils.isEmpty(records)) {
            return 0L;
        }
        return records.get(records.size() - 1).getId();
    }

    /**
     * 正常是有的，返回null是异常的，由上游log记录和处理
     *
     * @return
     */
    public PplVersionGroupRecordDO getFirstRecord() {
        return ListUtils.isEmpty(records) ? null : records.get(0);
    }

}
