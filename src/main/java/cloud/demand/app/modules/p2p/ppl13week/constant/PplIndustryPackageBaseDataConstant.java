package cloud.demand.app.modules.p2p.ppl13week.constant;

import java.util.Arrays;
import java.util.List;

public interface PplIndustryPackageBaseDataConstant {

    /** 包管理中特殊的客户： 行业专项包预测 */
    String SUPPLEMENT_CUSTOMER = "行业专项包预测";

    /** 包管理中特殊的战区： 行业专项包预测 */
    String SUPPLEMENT_WAR_ZONE = "行业专项包预测";

    /** 随机可用区 */
    String SUPPLEMENT_ZONE_NAME = "随机可用区";

    String MODEL_FORECAST_EMR_PROJECT = "模型预测-EMR";

    String MODEL_FORECAST_EKS_PROJECT = "模型预测-EKS";

    String MODEL_FORECAST_CDB_PROJECT = "模型预测-CDB";


    String CDB_SMALL_INSTANCE = "16C64G";
    String CDB_BIG_INSTANCE = "32C128G";


    List<String> MODEL_FORECAST_PROJECT_LIST = Arrays.asList(MODEL_FORECAST_EMR_PROJECT, MODEL_FORECAST_EKS_PROJECT,MODEL_FORECAST_CDB_PROJECT);

}
