package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.NEW_SPLIT_FOR_PPL;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_CDB;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EKS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EMR;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_FOR_MIDDLE;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_FOR_ZIYAN;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTaskInputDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTransformRecord;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastSerialIntervalTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.CreateSplitVersionReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryForecastCategoryReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryForecastCommonReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryForecastListReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryForecastListRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryForecastListRsp.InputInfo;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryForecastListRsp.TaskInfo;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryForecastSchemaInfoRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitVersionRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitVersionRsp.Item;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCustomerScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastProductEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastResourcePoolEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceFromEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekViewService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import io.swagger.v3.oas.annotations.Operation;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class Ppl13weekViewServiceImpl implements Ppl13weekViewService {

    @Resource
    DBHelper demandDBHelper;
    @Autowired
    @Lazy
    Ppl13weekViewServiceImpl ppl13weekViewService;
    @Resource
    Ppl13weekPredictServiceImpl ppl13weekPredictService;
    @Resource
    private Ppl13weekSplitService ppl13weekSplitService;

    private LocalDate forecastRangeDate(LocalDate date, Integer shiftMonth) {
        return date.plusMonths(shiftMonth);
    }

    // 获取算法名
    private String getAlgorithmName(PplForecastTaskInputDO taskDO) {
        String algorithm = taskDO.getPredictAlgorithm();
        String algorithmName;
        algorithmName = algorithm + "(" + taskDO.getPredictAlgorithmArgs() + ")";
        return algorithmName;
    }

    DynamicProperty<String> forecastStartDate = DynamicProperty.create("forecastStartDate", "2023-05-01");

    @Override
    public List<QueryForecastListRsp> queryForecastList(QueryForecastListReq req) {

        WhereContent whereContent = new WhereContent();
        whereContent.addAnd("predict_month >= ?", forecastStartDate.get());
        whereContent.order("predict_month desc");
        // task 开启了的才使用
        whereContent.addAnd(" is_enable=1 and category=? ", req.getCategory());

        List<PplForecastTaskInputDO> inputDOS = demandDBHelper.getRaw(PplForecastTaskInputDO.class,
                "select * from ppl_forecast_task_input " + whereContent.getSql(),
                whereContent.getParams());

        Map<LocalDate, List<PplForecastTaskInputDO>> inputGroupBy = ListUtils.groupBy(inputDOS,
                PplForecastTaskInputDO::getPredictMonth);

        List<QueryForecastListRsp> ret = Lang.list();
        for (LocalDate localDate : inputGroupBy.keySet()) {

            QueryForecastListRsp queryForecastListRsp = new QueryForecastListRsp();
            ret.add(queryForecastListRsp);

            PplForecastTaskInputDO first = inputGroupBy.get(localDate).get(0);
            boolean isWeekMax = Strings.equals(first.getSourceFrom(), Ppl13weekForecastSourceFromEnum.WEEK.getCode()) ||
                    Strings.equals(first.getSourceFrom(), Ppl13weekForecastSourceFromEnum.WEEK_MAX.getCode()) ||
                    Strings.equals(first.getSourceFrom(), Ppl13weekForecastSourceFromEnum.WEEK_AVG.getCode());

            if (isWeekMax) {
                queryForecastListRsp.setPredictDate(DateUtils.format(localDate, "yyyy-MM-dd"));
            } else {
                queryForecastListRsp.setPredictDate(DateUtils.format(localDate, "yyyy-MM"));
            }

            List<InputInfo> transform = ListUtils.transform(inputGroupBy.get(localDate), (o) -> {
                InputInfo inputInfo = new InputInfo();
                inputInfo.setInputId(o.getInputId());
                inputInfo.setSourceType(o.getSourceType());
                inputInfo.setSourceTypeName(Ppl13weekForecastSourceTypeEnum.getNameByCode(o.getSourceType()));
                inputInfo.setSourceFrom(o.getSourceFrom());
                inputInfo.setSourceFromName(Ppl13weekForecastSourceFromEnum.getNameByCode(o.getSourceFrom()));

                List<TaskInfo> taskRsp = Lang.list();
                String name = Strings.join("@", o.getPredictAlgorithm(), o.getPredictAlgorithmArgs());
                taskRsp.add(new TaskInfo(o.getTaskId(), name));
                inputInfo.setTaskInfo(taskRsp);
                return inputInfo;
            });
            queryForecastListRsp.setData(transform);
        }

        ListUtils.sortDescNullLast(ret, QueryForecastListRsp::getPredictDate);
        return ret;
    }

    @Override
    public QueryForecastSchemaInfoRsp queryForecastSchemaInfo(List<Long> taskIds) {

        List<PplForecastTaskInputDO> tasks = demandDBHelper
                .getRaw(PplForecastTaskInputDO.class,
                        "select * from ppl_forecast_task_input where task_id in (?)",
                        taskIds);

        if (Lang.isEmpty(tasks)) {
            return new QueryForecastSchemaInfoRsp();
        }
        PplForecastTaskInputDO task = tasks.get(0);

        // 整合数据
        QueryForecastSchemaInfoRsp rsp = new QueryForecastSchemaInfoRsp();
        ListUtils.sortAscNullLast(taskIds, (o) -> o);
        List<PplForecastTransformRecord> resultDO = demandDBHelper.getAll(PplForecastTransformRecord.class,
                "where status = 'FINISH' and task_id in ( ?) order by id desc", taskIds);

        rsp.setTransferToCRPTime(Lang.isEmpty(resultDO) ? null : resultDO.get(0).getCreateTime());
        rsp.setGenerateTime(task.getTaskCreateTime());
        rsp.setAlgorithmName(getAlgorithmName(task));

        if (task.getSerialInterval().equals(PplForecastSerialIntervalTypeEnum.WEEK.getType())) {
            rsp.setForecastRangeFromDate(task.getInputDateEnd().plusWeeks(task.getPredictIndexStart()));
            rsp.setForecastRangeToDate(task.getInputDateEnd().plusWeeks(task.getPredictIndexEnd()));
        } else {
            rsp.setForecastRangeFromDate(
                    forecastRangeDate(task.getInputDateEnd(), task.getPredictIndexStart()));
            rsp.setForecastRangeToDate(
                    forecastRangeDate(task.getInputDateEnd(), task.getPredictIndexEnd()));
        }
        rsp.setSerialIntervalName(PplForecastSerialIntervalTypeEnum.getNameByType(task.getSerialInterval()));
        rsp.setInputDimsName(task.getInputDimsName());
        rsp.setBillType(task.getBillType());
        rsp.setBillTypeName(Ppl13weekForecastBillTypeEnum.getNameByCode(task.getBillType()));
        rsp.setTaskId(task.getTaskId());

        rsp.setResourcePoolName(Ppl13weekForecastResourcePoolEnum.getNameByCode(task.getResourcePool()));
        rsp.setCustomerScopeName(Ppl13weekForecastCustomerScopeEnum.getNameByCode(task.getCustomerScope()));

        // 获取原始任务信息，因为PplForecastTaskInputDO是视图，可能没有product字段
        PplForecastPredictTaskDO originalTask = demandDBHelper.getOne(PplForecastPredictTaskDO.class, "where id = ?", task.getTaskId());
        if (originalTask != null && originalTask.getProduct() != null) {
            String productCode = originalTask.getProduct();
            rsp.setProductCode(productCode);

            // 使用Ppl13weekForecastProductEnum获取产品名称和单位名称
            Ppl13weekForecastProductEnum productEnum = Ppl13weekForecastProductEnum.getByCode(productCode);
            if (productEnum != null) {
                rsp.setProductName(productEnum.getName());
                rsp.setUnitName(productEnum.getUnitName());
            } else {
                // 如果枚举中没有找到，直接使用code作为名称
                rsp.setProductName(productCode);
                rsp.setUnitName("");
            }
        }

        return rsp;
    }

    static final Supplier<String> pplForecastCategorySort =
            DynamicProperty.create("ppl.forecast.category.sort", "");

    static final Supplier<String> pplForecastWeekCategorySort =
            DynamicProperty.create("ppl.forecast.week_category.sort", "");


    @Override
    @Operation(summary = "查询中长尾所有的方案名称", description = "返回list")
    public Object queryForecastCategory(QueryForecastCategoryReq req) {

        String sql = "select distinct category from ppl_forecast_task_input where is_enable=1 and serial_interval=? and resource_pool=?";
        List<String> raw = demandDBHelper.getRaw(String.class, sql, req.getSerialInterval(), req.getResourcePool());

        if (PplForecastSerialIntervalTypeEnum.WEEK.getType().equals(req.getSerialInterval())) {
            sortByConfig(raw, pplForecastWeekCategorySort.get());
            return raw;
        }

        sortByConfig(raw, pplForecastCategorySort.get());
        return raw;
    }

    private void sortByConfig(List<String> raw, String s) {
        // 2023-10-07 增加自定义排序
        List<String> sortKey = Arrays.stream(s.split("[#;@]"))
                .map(StringUtils::trimWhitespace)
                .filter((o) -> !o.isEmpty())
                .collect(Collectors.toList());

        if (sortKey.isEmpty()) {
            ListUtils.sortAscNullLast(raw, (o) -> o);
        } else {
            raw.sort((o1, o2) -> {
                int index1 = sortKey.indexOf(o1);
                int index2 = sortKey.indexOf(o2);

                if (index1 != -1 && index2 != -1) {
                    // 相同使用配置的索引排序
                    return Integer.compare(index1, index2);
                } else if (index1 != -1) {
                    return -1;
                } else if (index2 != -1) {
                    return 1;
                } else {
                    // 都没有找到使用string 排序
                    return o1.compareTo(o2);
                }

            });
        }
    }


    @Override
    @Operation(summary = "查询现在已经拆分的版本", description = "返回list")
    public QuerySplitVersionRsp querySplitVersion(QueryForecastCommonReq req) {

        WhereContent whereContent = new WhereContent();
        whereContent.andIn(PplForecastPredictTaskOutputVersionDO::getTaskId, req.getTaskIds());

        List<PplForecastPredictTaskOutputVersionDO> all = ORMUtils.db(demandDBHelper)
                .getAll(PplForecastPredictTaskOutputVersionDO.class, whereContent);

        Map<String, List<PplForecastPredictTaskOutputVersionDO>> groupByData = ListUtils.groupBy(all,
                (o) -> Strings.join("@", o.getOutputVersionType(), o.getOutputVersionName()));

        List<QuerySplitVersionRsp.Item> ret = Lang.list();

        for (String key : groupByData.keySet()) {
            List<PplForecastPredictTaskOutputVersionDO> one = groupByData.get(key);
            Item item = new Item();
            item.setOutputVersionIds(
                    one.stream().map(PplForecastPredictTaskOutputVersionDO::getId).collect(Collectors.toList()));
            item.setTaskIds(
                    one.stream().map(PplForecastPredictTaskOutputVersionDO::getTaskId).collect(Collectors.toList()));
            item.setOutputVersionType(one.get(0).getOutputVersionType());
            item.setOutputVersionTypeName(
                    Ppl13weekForecastOutputVersionTypeEnum.getNameByCode(one.get(0).getOutputVersionType()));
            item.setOutputVersionName(one.get(0).getOutputVersionName());
            ret.add(item);
        }
        return new QuerySplitVersionRsp(ret);
    }

    @Override
    public List<ImmutableMap<String, String>> queryOutputVersionType() {
        List<ImmutableMap<String, String>> ret = Lang.list();
        for (Ppl13weekForecastOutputVersionTypeEnum typeEnum : Ppl13weekForecastOutputVersionTypeEnum.values()) {
            ImmutableMap<String, String> d = ImmutableMap.of("code", typeEnum.getCode(), "name", typeEnum.getName());
            ret.add(d);
        }
        return ret;
    }

    @Synchronized(keyScript = "args[0].outputVersionName", throwExceptionIfNotGetLock = false)
    @Transactional(transactionManager = "demandTransactionManager")
    @Override
    @SneakyThrows
    public ImmutableMap<String, String> createSplitVersion(CreateSplitVersionReq req) {
        if (Lang.isEmpty(req.getTaskIds())) {
            throw BizException.makeThrow("taskIds 为空");
        }

        StringBuilder retLog = new StringBuilder();
        for (Long taskId : req.getTaskIds()) {
            createSplitVersionByTaskId(req, taskId, retLog);
        }
        return ImmutableMap.of("data", "success", "retLog", retLog.toString());
    }

    private void createSplitVersionByTaskId(CreateSplitVersionReq req, Long taskId, StringBuilder retLog) {

        // 检查同一个taskId下版本名称是否已经存在，不允许用相同的版本名称
        PplForecastPredictTaskOutputVersionDO exist = demandDBHelper.getOne(
                PplForecastPredictTaskOutputVersionDO.class,
                "where task_id=? and output_version_name=?", taskId, req.getOutputVersionName());
        if (exist != null) {
            throw BizException.makeThrow("版本名称已经存在，请使用另外一个版本名称来创建");
        }

        PplForecastPredictTaskOutputVersionDO one = new PplForecastPredictTaskOutputVersionDO();
        one.setCreateUser(LoginUtils.getUserNameWithSystem());
        one.setTaskId(taskId);
        one.setOutputVersionName(req.getOutputVersionName());
        one.setOutputVersionType(req.getOutputVersionType());
        one.setStatus("NEW");
        demandDBHelper.insert(one);

        // 在ppl一键替换的底数来源
        if (SPLIT_FOR_MIDDLE.getCode().equals(req.getOutputVersionType())) {
            one.setDesc("拆分成行业部门,可用区，大小核心，周");
            demandDBHelper.update(one);
            retLog.append(ppl13weekSplitService.splitCloudMiddle(taskId, one.getId()));
        }
        // 下发自研的数据
        else if (SPLIT_FOR_ZIYAN.getCode().equals(req.getOutputVersionType())) {
            one.setDesc("页面点击拆分， 自研拆分");
            ppl13weekSplitService.splitProjectCustomBgDeviceGroup(one.getId());
        }
        // 下发中长尾CVM的数据
        else if (Objects.equals(NEW_SPLIT_FOR_PPL.getCode(), req.getOutputVersionType())) {
            one.setDesc("页面点击拆分, 新中长尾的拆分，按照阈值来管理的中长尾范围, 内部+外部");
            retLog.append(ppl13weekSplitService.splitNewLongTail(taskId, one.getId()));
        }
        // 拆分 EMR 的数据， 不拆分可用区，相比 NEW_SPLIT_FOR_PPL 少了可用区的拆分，值填为随机可用区
        else if (Objects.equals(SPLIT_EMR.getCode(), req.getOutputVersionType())) {
            one.setDesc("页面点击拆分, EMR的拆分，不拆分可用区,值填为随机可用区");
            retLog.append(ppl13weekSplitService.splitEmrLongTail(taskId, one.getId()));
        } else if (Objects.equals(SPLIT_EKS.getCode(), req.getOutputVersionType())) {
            one.setDesc("页面点击拆分, EKS的拆分，不拆分可用区,值填为随机可用区, 机型为大核心");
            retLog.append(ppl13weekSplitService.splitEksLongTail(taskId, one.getId()));
        } else if (Objects.equals(SPLIT_CDB.getCode(), req.getOutputVersionType())) {
            one.setDesc("页面点击拆分, CDB的拆分");
            retLog.append(ppl13weekSplitService.splitCdbLongTail(taskId, one.getId()));
        }
        else {
            throw new WrongWebParameterException("拆分方式:" + req.getOutputVersionType() + "暂不支持");
        }

        one.setStatus("SPLIT");
        demandDBHelper.update(one);
    }

}
