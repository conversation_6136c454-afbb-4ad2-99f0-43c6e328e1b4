package cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

@Data
public class InnerVersionParams {

    private String industryDept;

    private String product;

    // 行业审批版本id
    private Long versionId;

    public String toRemoveCustomerConfigBizIdWithVersionId() {
        return StrUtil.format("remove_customer_{}_{}_{}", industryDept, product, versionId);
    }

    public String toRemoveCustomerConfigBizIdWithoutVersionId() {
        return StrUtil.format("remove_customer_{}_{}", industryDept, product);
    }

    public String toCustomerLevelConfigBizIdWithVersionId() {
        return StrUtil.format("customer_level_{}_{}_{}", industryDept, product, versionId);
    }

    public String toCustomerLevelConfigBizIdWithoutVersionId() {
        return StrUtil.format("customer_level_{}_{}", industryDept, product);
    }

}
