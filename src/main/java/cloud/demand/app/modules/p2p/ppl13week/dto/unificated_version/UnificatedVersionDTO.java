package cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version;

import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import com.tencent.rainbow.util.JsonUtil;
import lombok.Data;
import lombok.ToString;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;

@Data
@ToString
public class UnificatedVersionDTO extends UnificatedVersionDO {


    /**
     * 未设置deadline的数据为不可调整时间的事件，不查出来展示
     */
    @RelatedColumn(localColumn = "id", remoteColumn = "version_id")
    private List<UnificatedVersionEventDO> eventList;

    private DemandYearMonth pplDemand;
    private DemandYearMonth productDemand;
    private DemandYearMonth yearDemand;


    public void parseJsonDemand() {
        this.setPplDemand(JsonUtil.toObject(this.getPplDemandYearMonth(),
                DemandYearMonth.class));
        if (this.getPplDemand().getOverseasBeginYear() == null){
            this.getPplDemand().setOverseasBeginYear(this.getPplDemand().getBeginYear());
            this.getPplDemand().setOverseasBeginMonth(this.getPplDemand().getBeginMonth());
        }
        this.setProductDemand(JsonUtil.toObject(this.getProductDemandYearMonth(),
                DemandYearMonth.class));
        this.setYearDemand(JsonUtil.toObject(this.getYearDemandYearMonth(),
                DemandYearMonth.class));
    }


    public Date getDeadlineByCode(String eventCode) {
        Date deadline = null;
        for (UnificatedVersionEventDO unificatedVersionEventDO : eventList) {
            if (unificatedVersionEventDO.getEventCode().equals(eventCode)) {
                deadline = unificatedVersionEventDO.getDeadline();
                break;
            }
        }
        return deadline;

    }

    public LocalDate getLocalDateForDeadlineByCode(String eventCode) {
        Date deadline = null;
        for (UnificatedVersionEventDO unificatedVersionEventDO : eventList) {
            if (unificatedVersionEventDO.getEventCode().equals(eventCode)) {
                deadline = unificatedVersionEventDO.getDeadline();
                break;
            }
        }
        if (deadline == null) {
            return null;
        } else {
            int year = Integer.parseInt(new SimpleDateFormat("yyyy").format(deadline));
            int month = Integer.parseInt(new SimpleDateFormat("MM").format(deadline));
            int dayOfMonth = Integer.parseInt(new SimpleDateFormat("dd").format(deadline));
            return LocalDate.of(year, month, dayOfMonth);
        }
    }

    public UnificatedVersionEventDO getEventByCode(String eventCode) {
        UnificatedVersionEventDO result = null;
        for (UnificatedVersionEventDO unificatedVersionEventDO : eventList) {
            if (unificatedVersionEventDO.getEventCode().equals(eventCode)) {
                result = unificatedVersionEventDO;
            }
        }
        return result;
    }


}
