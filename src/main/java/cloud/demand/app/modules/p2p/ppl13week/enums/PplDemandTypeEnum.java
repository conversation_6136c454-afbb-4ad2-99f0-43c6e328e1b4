package cloud.demand.app.modules.p2p.ppl13week.enums;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.nutz.lang.Lang;


@Getter
public enum PplDemandTypeEnum {

    NEW("NEW", "新增需求", "新增","常规"),

    ELASTIC("ELASTIC", "弹性需求", "弹性","短租"),

    RETURN("RETURN", "退回需求", "退回", "");

    final private String code;
    final private String name;
    final private String name2;
    final private String remark;

    PplDemandTypeEnum(String code, String name,String name2, String remark) {
        this.code = code;
        this.name = name;
        this.name2 = name2;
        this.remark = remark;
    }

    public static List<String> names() {
        return Arrays.stream(PplDemandTypeEnum.values())
                .map(PplDemandTypeEnum::getName)
                .collect(Collectors.toList());
    }

    public static List<String> names2() {
        return Arrays.stream(PplDemandTypeEnum.values())
                .map(PplDemandTypeEnum::getName2)
                .collect(Collectors.toList());
    }

    public static String getForecastType(String code){
        List<String> newCode = Lang.list(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode());
        if (newCode.contains(code)) {
            return PplForecastTypeEnum.NEW.getType();
        } else {
            return PplForecastTypeEnum.RET.getType();
        }
    }

    public static PplDemandTypeEnum getByCode(String code) {
        for (PplDemandTypeEnum e : PplDemandTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static PplDemandTypeEnum getByName(String name) {
        for (PplDemandTypeEnum e : PplDemandTypeEnum.values()) {
            if (Objects.equals(name, e.getName()) || Objects.equals(name, e.getName2())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplDemandTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    public static String getName2ByCode(String code) {
        PplDemandTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName2();
    }


    public static String getCodeByName(String name) {
        PplDemandTypeEnum e = getByName(name);
        return e == null ? "" : e.getCode();
    }

    public static String getRemarkByCode(String code) {
        PplDemandTypeEnum e = getByCode(code);
        return e == null ? "" : e.getRemark();
    }
}
