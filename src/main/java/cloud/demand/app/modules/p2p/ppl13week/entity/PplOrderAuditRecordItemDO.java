package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_order_audit_record_item")
public class PplOrderAuditRecordItemDO extends PplItemBaseDO {

    @Column(value = "audit_record_id")
    private Long auditRecordId;

    /**
     * 延期需求原来的开始购买日期
     */
    @Column(value = "expired_begin_buy_date")
    private LocalDate expiredBeginBuyDate;


    /**
     * 订单明细id<br/>Column: [order_number_id]
     */
    @Column(value = "order_number_id")
    private String orderNumberId;

    /**
     *  修改下期生效，审批时架构师在录入页面对此PPL进行修改并提交为下期生效，对审批流中的PPL无影响，仅做标识
     */
    @Column(value = "modify_next_version_valid")
    private Boolean modifyNextVersionValid;

}