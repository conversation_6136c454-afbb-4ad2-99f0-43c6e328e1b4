package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum PplRecordChangeEventEnum {

    // 数据初始化
    PPL_INIT("PPL_INIT", "数据初始化"),

    // 待提交需求确认：
    PPL_EXPIRED_COPY_ADD("PPL_EXPIRED_COPY_ADD", "需求过期复制"),

    PPL_IMPORT_ADD("PPL_IMPORT_ADD", "新增PPL"),

    PPL_IMPORT_EDIT("PPL_IMPORT_EDIT", "调整PPL"),

    PPL_NEXT_VERSION_VALID_ADD("PPL_NEXT_VERSION_VALID_ADD", "新增PPL下期生效"),

    PPL_NEXT_VERSION_VALID_EDIT("PPL_NEXT_VERSION_VALID_EDIT", "调整PPL下期生效"),

    PPL_NEXT_VERSION_VALID_WITHDRAW("PPL_NEXT_VERSION_VALID_WITHDRAW", "撤销PPL下期生效"),

    PPL_IMPORT_SUBMITTED("PPL_IMPORT_SUBMITTED", "PPL需求确认"),

    PPL_NEXT_VERSION_VALID_SUBMITTED("PPL_NEXT_VERSION_VALID_SUBMITTED", "PPL下期生效确认"),

    PPL_IMPORT_CANCEL("PPL_IMPORT_CANCEL", "取消PPL"),

    PPL_IMPORT_REVERT("PPL_IMPORT_REVERT", "撤销PPL变更"),

    // 需求沟通：
    PRE_SUBMIT_REJECT("PRE_SUBMIT_REJECT", "需求沟通打回"),

    PRE_SUBMIT_WITHDRAW("PRE_SUBMIT_WITHDRAW", "需求沟通撤回"),

    // 审批：
    PPL_AUDIT_ADD("PPL_AUDIT_ADD", "审批干预新增"),

    PPL_AUDIT_EDIT("PPL_AUDIT_EDIT", "审批干预修改"),

    PPL_AUDIT_REJECT("PPL_AUDIT_REJECT", "审批拒绝"),


    // 预约单事件：
    YUN_SUBMITTED("YUN_SUBMITTED", "预约单提交"),

    YUN_SUBMITTED_ADD("YUN_SUBMITTED_ADD", "预约单提交（拆单新增）"),

    YUN_CANCELED("YUN_CANCELED", "预约单取消"),

    // 用户接受引导、拒绝引导
    /**
     * 行业接受引导
     */
    CONSENSUS_ACCEPT("CONSENSUS_ACCEPT", "行业接受引导"),

    /**
     * 行业拒绝引导
     */
    CONSENSUS_REFUSE("CONSENSUS_REFUSE", "行业拒绝引导"),

    /**
     * 预测转订单
     */
    PPL_TRANSFORM_ORDER("PPL_TRANSFORM_ORDER", "预测转订单"),

    /**
     * 取消预测转订单
     */
    CANCEL_PPL_TRANSFORM_ORDER("CANCEL_PPL_TRANSFORM_ORDER", "取消预测转订单"),

    ;


    final private String code;
    final private String name;

    PplRecordChangeEventEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplRecordChangeEventEnum getByCode(String code) {
        for (PplRecordChangeEventEnum e : PplRecordChangeEventEnum.values()) {
            if (Objects.equals(e.getCode(), code)) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplRecordChangeEventEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
