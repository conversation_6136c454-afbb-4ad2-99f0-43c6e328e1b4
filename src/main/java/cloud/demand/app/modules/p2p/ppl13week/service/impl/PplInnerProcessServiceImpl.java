package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.config.QueueDefinition;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv2.enums.CacheDimEnum;
import cloud.demand.app.modules.mrpv2.enums.ForecastStatus;
import cloud.demand.app.modules.mrpv2.enums.ProjectTypeEnum;
import cloud.demand.app.modules.mrpv2.model.MrpV2Cache;
import cloud.demand.app.modules.mrpv2.service.CacheService;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq.Choose;
import cloud.demand.app.modules.mrpv2.web.QueryTableReq.TimePeriod;
import cloud.demand.app.modules.mrpv2.web.QueryTableRsp;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalData;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService.ApprovalMessageBody;
import cloud.demand.app.modules.p2p.ppl13week.constant.PplIndustryPackageBaseDataConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.DayScaleDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.DayScaleDTO.DayScaleResult;
import cloud.demand.app.modules.p2p.ppl13week.dto.DayScaleDTO.ExecItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.DayScaleDTO.ForecastItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.DayScaleDTO.YearMonthDemand;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoResp.DemandNumByCustomerAndInstanceType;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoResp.StatusNameConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ApprovalProductInfoResp.VersionAndNode;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ChangePplOrderInnerStatusReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastConstituteResp.ForecastConstituteDetail;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastInstanceTypeConstituteResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ForecastInstanceTypeConstituteResp.ForecastInstanceTypeDetail;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq.CustomerItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq.GroupQueryCondition;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendReq.HistoricalTrendGroup;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendsResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendsResp.CountDownLatchWithTrendItems;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendsResp.CustomerDemandInfo;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.HistoricalTrendsResp.TrendItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.IndustryAuthDataReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.IndustryAuthDataResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.IndustryVersionRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.LatestItemVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.NewAuditOverviewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO.PermissionVersionItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PermissionDTO.PermissionVersionRoleItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplApproveNoteVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveNodeDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerProcessVersionSlaDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplOrderJoinAuditRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryApproveNoteResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryApproveNoteResp.ApproveNoteVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditOverviewReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditOverviewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditOverviewVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditProcessLineReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryAuditProcessLineResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryInnerVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryProjectMonitorDetailReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryProjectMonitorDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryProjectMonitorVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryProjectMonitorVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryRateWithAuth;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryRateWithAuthRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryRateWithAuthRsp.Result;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeAllFieldDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeRecordNewDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplOrderAuditRecordItemChangeDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplOrderAuditRecordItemChangeDTO.ChangeItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.BatchAdjustZeroReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.BatchDealInnerPplOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryApproveNoteReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryIndustryAuditPplReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryInnerPplOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryInnerVersionListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.UpdatePplOrderItemListReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.IndustryPplTodoRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplAuditItemQueueDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessNodeDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionSlaDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderExpiredRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.DraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplInnerProcessNodeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplInnerVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderAuditStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderExpiredOperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderInputStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderQueryTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionRelateGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.SystemUserConstant;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplDeadlineAutoPassEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessAttributeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.event.PplChangeEvent;
import cloud.demand.app.modules.p2p.ppl13week.service.MailTableService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplModelForecastForPackageService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAuditRecordService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import cloud.demand.app.modules.p2p.ppl13week.vo.IndustryPplItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.IndustryPplOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerDeptVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerProcessVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemAppliedVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditListVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderDraftVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderVo;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderWithAuditRecordVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionRelateGroupVO;
import cloud.demand.app.web.OpsController.InitDataVo;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.rabbitmq.client.Channel;
import com.tencent.rainbow.util.JsonUtil;
import erp.base.fiber.support.dispatcher.FiberTaskExecutor;
import io.vavr.Tuple2;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.nutz.json.Json;
import org.nutz.lang.Lang;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import yunti.boot.apiusage.WithReport;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplInnerProcessServiceImpl implements PplInnerProcessService {

    @Resource
    DBHelper demandDBHelper;
    @Resource
    private PermissionService permissionService;
    @Resource
    PplDictService pplDictService;
    @Resource
    PplOrderAuditRecordService auditRecordService;
    @Resource
    PplCommonService pplCommonService;
    @Resource
    private RabbitTemplate rabbitTemplate;
    @Resource
    Alert alert;
    @Resource
    private MailTableService mailTableService;
    @Resource
    private PplDraftService pplDraftService;
    @Resource
    private PplInnerVersionService innerVersionService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private TodoService todoService;

    @Resource
    private FillerService fillerService;

    @Resource
    private PplIndustryProcessService industryProcessService;

    @Resource
    private PplModelForecastForPackageService modelForecastForPackageService;

    FiberTaskExecutor fiberTaskExecutor = FiberTaskExecutor.newDefaultExecutor();

    Supplier<String> domainSupplier = DynamicProperty.create("app.industry.domain", "");

    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    static ExecutorService executor = Executors.newFixedThreadPool(8);

    @Resource
    private CacheService queryDataService;

    @Resource
    private DictService dictService;

    public static final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(8);

    @Override
    public QueryProjectMonitorVersionResp queryProjectMonitorVersion(QueryProjectMonitorVersionReq req) {
        List<PplInnerProcessVersionDO> versions =
                demandDBHelper.getAll(PplInnerProcessVersionDO.class, "where industry_dept = ? " +
                                "and status != ? order by id desc", req.getIndustryDept(),
                        PplInnerProcessVersionStatusEnum.NEW.getCode());
        QueryProjectMonitorVersionResp resp = new QueryProjectMonitorVersionResp();
        resp.setVersions(ListUtils.transform(versions, o -> {
            QueryProjectMonitorVersionResp.Item item = new QueryProjectMonitorVersionResp.Item();
            item.setVersionId(o.getId());
            item.setIndustryDept(o.getIndustryDept());
            item.setProjectBeginDate(DateUtils.formatDate(o.getBeginDate()));
            item.setProjectEndDate(DateUtils.formatDate(o.getEndDate()));
            item.setProduct(o.getProduct());
            item.setStatus(o.getStatus());
            return item;
        }));
        return resp;
    }

    @Override
    public List<PplInnerProcessVersionSlaDO> getProcessByVersionId(Long versionId) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplInnerProcessVersionSlaDO::getVersionId, versionId);
        List<PplInnerProcessVersionSlaDO> processNodeList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                whereContent.getSql(),
                whereContent.getParams());
        if (ListUtils.isEmpty(processNodeList)) {
            return null;
        }
        Map<Long, PplInnerProcessVersionSlaDO> idTomap = ListUtils.toMap(processNodeList,
                PplInnerProcessVersionSlaDO::getId, v -> v);
        Map<Long, PplInnerProcessVersionSlaDO> nextIdMap = ListUtils.toMap(processNodeList,
                PplInnerProcessVersionSlaDO::getNextSlaId, v -> v);

        LinkedList<PplInnerProcessVersionSlaDO> linkedList = new LinkedList<>();
        Long normalFirstId = processNodeList.get(0).getId();
        Long normalNextId = processNodeList.get(0).getNextSlaId();
        Long firstId = normalFirstId;
        Long nextId = normalNextId;
        List<Long> idList = new ArrayList<>();
        // 从index为0的开始找下一个node进行填充，直至最后一个node
        while (nextId != null) {
            idList.add(nextId);
            PplInnerProcessVersionSlaDO item = idTomap.get(nextId);
            if (item == null) {
                break;
            }
            nextId = item.getNextSlaId();
            if (Objects.equals(nextId, normalNextId) || Objects.equals(nextId, normalFirstId)
                    || idList.contains(nextId)) {
                throw new BizException("审批流程配置参数错误，出现环形审批流，审批流版本id【" + versionId + "】");
            }
            linkedList.addLast(item);
        }
        // 从index为0的开始找前一个node进行填充，直至第一个node
        while (firstId != null) {
            idList.add(firstId);
            PplInnerProcessVersionSlaDO item = idTomap.get(firstId);
            if (item == null) {
                break;
            }
            PplInnerProcessVersionSlaDO preNode = nextIdMap.get(firstId);
            firstId = preNode == null ? null : preNode.getId();
            if (Objects.equals(firstId, normalFirstId) || Objects.equals(firstId, normalNextId)
                    || idList.contains(firstId)) {
                throw new BizException("审批流程配置参数错误，出现环形审批流，审批流版本id【" + versionId + "】");
            }
            linkedList.addFirst(item);
        }
        return linkedList;
    }

    @Override
    public QueryAuditProcessLineResp queryAuditProcessLine(QueryAuditProcessLineReq req) {
        // 1. 根据tab+当前登录用户找到待审批列表或驳回待处理列表
        String userName = LoginUtils.getUserNameWithSystem();
        Map<String, Object> params = new HashMap<>();
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("industry_dept = :industryDept");
        params.put("industryDept", req.getIndustryDept());
        switch (req.getTabType()) {
            case "WAIT_AUDIT":
                whereSQL.and("c.audit_status = :auditStatus");
                whereSQL.and("c.current_processor like :currentProcessor");
                params.put("auditStatus", PplOrderAuditStatusEnum.WAIT.getCode());
                params.put("currentProcessor", "%" + userName + "%");
                break;
            case "REJECT_HANDLE":
                whereSQL.and("c.audit_status=?", PplOrderAuditStatusEnum.REJECT_MODIFY.getCode());
                whereSQL.and("c.reject_processor like ?", "%" + userName + "%");
                break;
            default:
                throw new WrongWebParameterException("未知的tab类型");
        }

        whereSQL.and("b.version_id = :versionId");
        params.put("versionId", req.getVersionId());
        String whereSql = whereToAnd(whereSQL.getSQL());

        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/ppl_order_join_audit_record_item.sql");
        sql = sql.replace("${FILTER}", whereSql);

        List<PplOrderJoinAuditRecordItemVO> raw = demandDBHelper.getRaw(PplOrderJoinAuditRecordItemVO.class, sql,
                params);

//        List<PplOrderDO> pplOrder = demandDBHelper.getAll(PplOrderDO.class, whereSQL.getSQL(), whereSQL.getParams());
        QueryAuditProcessLineResp resp = new QueryAuditProcessLineResp();

        PplInnerProcessVersionSlaDO slaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = 'ENTER'", req.getVersionId());
        if (slaDO != null || slaDO.getDeadlineTime() != null) {
            // 设置需求录入截止时间
            resp.setAuditStartTime(DateUtils.format(slaDO.getDeadlineTime()));
        }

        if (ListUtils.isEmpty(raw)) {
            return resp;
        }

        // 2. 从数据库查找节点列表，目前只考虑了一个部门+一个产品的情况
        List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaList(req.getVersionId());
        if (ListUtils.isEmpty(versionSlaList)) {
            throw new BizException("部门:" + req.getIndustryDept() + "的流程未配置");
        }

        // 3. 确定当前最早的节点是哪个节点，这个节点之前的是审批完
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        PplInnerProcessDO processDO = demandDBHelper.getByKey(PplInnerProcessDO.class, currentVersion.getProcessId());
        Map<String, List<PplOrderJoinAuditRecordItemVO>> byNodeCode = ListUtils.groupBy(raw,
                PplOrderJoinAuditRecordItemVO::getNodeCode);

        AtomicReference<String> status = new AtomicReference<>("DONE");
        AtomicReference<String> statusName = new AtomicReference<>("已完成");
        if (slaDO.getDeadlineTime() == null || slaDO.getDeadlineTime().after(new Date())) {
            status.set("FUTURE");
            statusName.set("审批未开始");
        }
        List<QueryAuditProcessLineResp.Node> nodes = ListUtils.transform(versionSlaList, o -> {
            QueryAuditProcessLineResp.Node node = new QueryAuditProcessLineResp.Node();
            node.setNodeName(o.getNodeName());
            node.setNodeCode(o.getNodeCode());
            boolean isChangeToProcess = false;
            if (byNodeCode.get(o.getNodeCode()) != null) {
                if ("DONE".equals(status.get())) {
                    status.set("PROCESSING");
                    statusName.set("审批中");
                    isChangeToProcess = true;
                }
            }
            if ("PROCESSING".equals(status.get())) {
                if (!isChangeToProcess) {
                    status.set("FUTURE");
                    statusName.set("审批未开始");
                }
            }
            node.setStatus(status.get());
            node.setStatusName(statusName.get());
            node.setDeadlineAutoPass(o.getDeadlineAutoPass());
            node.setIsHighLight(o.getIsHighlight());
            node.setSameApproveAutoPass(processDO.getSameApproveAutoPass());
            node.setRoleAttribute(o.getRoleAttribute());
            return node;
        });
        resp.setNodes(nodes);

        // 4. 查询出deadline
        List<QueryAuditProcessLineResp.Node> processNode =
                ListUtils.filter(resp.getNodes(), o -> "PROCESSING".equals(o.getStatus()));
        if (!processNode.isEmpty()) {
            // 说明：当前只有一个部门和产品，先这样处理
            PplInnerProcessVersionSlaDO sla = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                    "where version_id=? and deadline_type='EXIT' and node_code=?",
                    currentVersion.getId(), processNode.get(0).getNodeCode());
            if (sla != null || sla.getDeadlineTime() != null) {
                processNode.get(0).setDeadline(DateUtils.format(sla.getDeadlineTime()));
            }
        }

        return resp;
    }


    @Override
    public QueryProjectMonitorDetailResp queryProjectMonitorDetail(QueryProjectMonitorDetailReq req) {
        QueryProjectMonitorDetailResp resp = new QueryProjectMonitorDetailResp();// 没有任何权限

        // 1. 获得checkPermission的权限范围
        PplInnerProcessVersionDO version = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where id=?", req.getVersionId());
        Boolean isNotCurrentVersion = !version.getStatus()
                .equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        if (version == null) {
            throw new BizException("版本id=" + req.getVersionId() + "不存在");
        }
        List<PplInnerProcessVersionSlaDO> versionSlas =
                demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                        "where version_id=? and deadline_type='EXIT'", req.getVersionId());
        Map<String, PplInnerProcessVersionSlaDO> versionSlaMap = ListUtils.toMap(versionSlas, o -> o.getNodeCode(),
                o -> o);

        String userName = LoginUtils.getUserNameWithSystem();
        WhereContent where = checkPermission(userName, version.getIndustryDept(), "t1.", version.getId());
        if (where == null) {
            resp.setMessage("您还没有任何权限，查看不了数据");
            return resp;
        }

        // 说明：能出现在record中且使用了该versionId的，就已经是需要的数据，所以这里不需要加industry_dept的条件
        WhereContent where1 = new WhereContent();
        where1.addAnd("t2.version_id=?", version.getId());
        where1.addAnd("t2.audit_status in (?)", ListUtils.newList(PplOrderAuditStatusEnum.AUDITED.getCode(),
                PplOrderAuditStatusEnum.REJECT_MODIFY.getCode(),
                PplOrderAuditStatusEnum.REFUSE.getCode(),
                PplOrderAuditStatusEnum.WAIT.getCode(),
                PplOrderAuditStatusEnum.PASS.getCode()));

        if (ListUtils.isNotEmpty(req.getWarZone())) {
            where1.addAnd("t1.war_zone in (?)", req.getWarZone());
        }

        if (ListUtils.isNotEmpty(req.getCustomerShortName())) {
            where1.addAnd("t1.customer_short_name in (?)", req.getCustomerShortName());
        }

        if (StringUtils.isNotBlank(req.getProduct())) {
            List<String> productList = new ArrayList<>();
            if (req.getProduct().equals(Ppl13weekProductTypeEnum.PAAS.getName())) {
                productList = Ppl13weekProductTypeEnum.getPaasProductNameList();
            } else {
                productList = Arrays.asList(req.getProduct());
            }
            where1.addAnd("t1.ppl_order in (select ppl_order from ppl_order_audit_record_item where deleted = 0 "
                            + "and product in (?) and audit_record_id in (select id from ppl_order_audit_record where version_id = ?) group by ppl_order)",
                    productList, req.getVersionId());
        }

        where.addAnd(where1);

        // 2. 查询出所有的pplOrder
        List<PplOrderWithAuditRecordVO> pplOrdersWithRecord =
                demandDBHelper.getAll(PplOrderWithAuditRecordVO.class, where.getSql(), where.getParams());
        if (ListUtils.isEmpty(pplOrdersWithRecord)) {
            // 如果都没有，那么再查一下没有权限限制的，看看真的是否没有数据
            List<PplOrderWithAuditRecordVO> list = demandDBHelper.getAll(
                    PplOrderWithAuditRecordVO.class, where1.getSql(), where1.getParams());
            if (list.isEmpty()) {
                // 真的没有任何数据
                resp.setMessage("当前版本还没有提交任何ppl单审批");
                return resp;
            } else {
                // 因为权限没有查到数据
                resp.setMessage("您当前的权限，没有查询到ppl单审批数据");
                return resp;
            }
        }

        List<PplOrderDO> pplOrders = new ArrayList<>();
        Set<String> existPplOrder = new HashSet<>();
        for (PplOrderWithAuditRecordVO pplOrderWithRecord : pplOrdersWithRecord) {
            if (!existPplOrder.contains(pplOrderWithRecord.getPplOrderDO().getPplOrder())) {
                PplOrderDO pplOrderDO = pplOrderWithRecord.getPplOrderDO();
                if (isNotCurrentVersion) {
                    pplOrderDO.setNodeCode("");
                    pplOrderDO.setAuditStatus("");
                    pplOrderDO.setCurrentProcessor("");
                    pplOrderDO.setCurrentRole("");
                }
                pplOrders.add(pplOrderDO);
                existPplOrder.add(pplOrderWithRecord.getPplOrderDO().getPplOrder());
            }
        }
        Map<String, List<PplOrderAuditRecordDO>> pplOrderRecords = ListUtils.toMapList(
                pplOrdersWithRecord, o -> o.getPplOrderDO().getPplOrder(), o -> o.getPplOrderRecordDO());

        Map<String, List<PplOrderWithAuditRecordVO>> nodeToRefuseMap = pplOrdersWithRecord.stream()
                .filter(v -> v.getPplOrderRecordDO().getAuditStatus().equals(PplOrderAuditStatusEnum.REFUSE.getCode()))
                .collect(Collectors.groupingBy(v -> v.getPplOrderRecordDO().getNodeCode()));
        // 3. 构造出每一层的pplOrder数据，从顶点开始（倒置的树开始）
        List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaList(version.getId());
        if (ListUtils.isEmpty(versionSlaList)) {
            throw new BizException("当前版本的部门:" + version.getIndustryDept() + "的流程未配置，无法展示详情");
        }

        Map<String, Map<String, Set<String>>> nodeProcessor = getNodeProcessor(version.getIndustryDept());
        List<QueryProjectMonitorDetailResp.Node> roots = build(versionSlaList, pplOrders, nodeProcessor,
                pplOrderRecords, nodeToRefuseMap, req.getVersionId());
        resp = new QueryProjectMonitorDetailResp();
        resp.setRootNodes(roots);

        // 4. 补全数据，DONE和FUTURE状态
        // 如果所有的pplOrder的nodeCode都是空，那么意味着全部已经完成
        boolean isAllDone = ListUtils.filter(pplOrders, o -> StringTools.isNotBlank(o.getNodeCode())).isEmpty();
        fillData(roots, isAllDone, versionSlaMap, pplOrderRecords);

        return resp;
    }

    @Override
    public String syncIndustryItem() {
        PplVersionDO versionDO = demandDBHelper.getOne(PplVersionDO.class,
                "where status = ?", PplVersionStatusEnum.PROCESS.getCode());
        if (versionDO == null) {
            return "未开启版本无法同步";
        }
        if (versionDO.getDeadline().before(new Date())) {
            return "当前13周版本已截止录入";
        }
        // 查出未消费的最新版本的item
        List<PplAuditItemQueueDO> AllItemQueueDOList = demandDBHelper.getAll(PplAuditItemQueueDO.class,
                "where status = 0 and version_id in "
                        + "(SELECT max(version_id) from ppl_audit_item_queue where deleted = 0 GROUP BY industry_dept,product)");
        if (CollectionUtils.isEmpty(AllItemQueueDOList)) {
            return "当前不存在未消费的item";
        }
        // 查出版本所有的组
        List<PplVersionGroupDO> groupDOList = demandDBHelper.getAll(PplVersionGroupDO.class,
                "where version_code = ?", versionDO.getVersionCode());
        if (CollectionUtils.isEmpty(groupDOList)) {
            return "当前不存在版本组";
        }
        Map<String, PplVersionGroupDO> groupDOMap = groupDOList.stream()
                .collect(Collectors.toMap(v -> v.getIndustryDept() + "-" + v.getProduct(), v -> v));

        Map<String, List<PplAuditItemQueueDO>> group = AllItemQueueDOList.stream()
                .collect(Collectors.groupingBy(v -> v.getIndustryDept() + "-" + v.getProduct()));

        // 根据分组仅需处理
        group.forEach((k, v) -> {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            PplVersionGroupDO versionGroupDO = groupDOMap.get(k);
            if (versionGroupDO != null) {
                fiberTaskExecutor.getExecutorService().execute(() -> {
                    try {
                        //手动开启事务
                        // 以组为单位进行事物提交，避免因为某个组的数据异常导致了其他组没法正常消费
                        TransactionTemplate transactionTemplate =
                                SpringUtil.getBean("demandTransactionTemplate", TransactionTemplate.class);
                        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
                            @Override
                            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                                syncIndustryItemForGroup(versionGroupDO, v);
                            }
                        });
                    } catch (Exception e) {
                        AlarmRobotUtil.doAlarm("syncIndustryItemForGroup", k + "同步行业数据失败,请检查 e:" + e, null,
                                false);
                        log.error("syncIndustryItemForGroup" + e.getStackTrace());
                        log.error("syncIndustryItemForGroup" + e.getMessage());
                    }
                });
            }
        });

        return "同步成功";
    }

    @Override
    public String syncIndustryItemForIndustry(String industryDept, String product) {
        PplVersionDO versionDO = demandDBHelper.getOne(PplVersionDO.class,
                "where status = ?", PplVersionStatusEnum.PROCESS.getCode());
        PplVersionGroupDO versionGroupDO = demandDBHelper.getOne(PplVersionGroupDO.class,
                "where industry_dept = ? and product = ? and version_code = ?",
                industryDept, product, versionDO.getVersionCode());

        List<PplAuditItemQueueDO> AllItemQueueDOList = demandDBHelper.getAll(PplAuditItemQueueDO.class,
                "where status = 0 and product = ? and version_id in "
                        + "(SELECT max(version_id) from ppl_audit_item_queue where deleted = 0 and industry_dept = ? and product = ?)",
                product, industryDept, product);

        TransactionTemplate transactionTemplate =
                SpringUtil.getBean("demandTransactionTemplate", TransactionTemplate.class);
        transactionTemplate.execute(new TransactionCallbackWithoutResult() {
            @Override
            protected void doInTransactionWithoutResult(@NotNull TransactionStatus status) {
                syncIndustryItemForGroup(versionGroupDO, AllItemQueueDOList);
            }
        });

        return "同步成功";
    }

    @Transactional("demandTransactionManager")
    public void syncIndustryItemForGroup(PplVersionGroupDO versionGroupDO, List<PplAuditItemQueueDO> syncItem) {

        List<String> overseas = pplDictService.queryAllRegionName(true);

        List<PplVersionGroupRecordDO> recordDOList = demandDBHelper.getAll(PplVersionGroupRecordDO.class,
                "where version_group_id = ?", versionGroupDO.getId());
        List<Long> needDeleteRecordId = recordDOList.stream()
                .map(PplVersionGroupRecordDO::getId).collect(Collectors.toList());

        // 第一个节点的record
        PplVersionGroupRecordDO recordDO = recordDOList.stream()
                .filter(o -> (o.getStatus().equals(PplVersionGroupStatusEnum.CREATE.getCode())
                        || o.getStatus().equals(PplVersionGroupStatusEnum.PRE_SUBMIT.getCode())))
                .collect(Collectors.toList()).get(0);

        // 软删除所有旧record 和 item
        if (!CollectionUtils.isEmpty(needDeleteRecordId)) {
            // 批量删除item
            demandDBHelper.executeRaw("update ppl_version_group_record_item set deleted = 1 "
                    + "where version_group_record_id in (?)", needDeleteRecordId);

            //保留的第一个节点的record 不删除
            needDeleteRecordId.remove(recordDO.getId());

            // 批量删除record
            demandDBHelper.executeRaw("update ppl_version_group_record set deleted = 1 "
                    + "where id in (?)", needDeleteRecordId);

            // 更新group状态
            demandDBHelper.executeRaw("update ppl_version_group set status = ? "
                    + "where deleted = 0 and id = ?", recordDO.getStatus(), recordDO.getVersionGroupId());
        }

        // 插入新Item
        Integer newRecordVersion = pplCommonService.getVersionGroupRecordVersion();
        List<PplVersionGroupRecordItemDO> recordItemDOList = new ArrayList<>();
        boolean isCVM = Ppl13weekProductTypeEnum.CVM.getName().equals(versionGroupDO.getProduct());
        PplVersionDO version = demandDBHelper.getOne(PplVersionDO.class,
                "where version_code = ?",versionGroupDO.getVersionCode());

        for (PplAuditItemQueueDO pplAuditItemQueueDO : syncItem) {
            PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO = JsonUtil.toObject(
                    pplAuditItemQueueDO.getItemJson(), PplVersionGroupRecordItemDO.class);
            pplVersionGroupRecordItemDO.setAlternativeInstanceType(
                    pplVersionGroupRecordItemDO.getAlternativeInstanceType() == null ? ""
                            : pplVersionGroupRecordItemDO.getAlternativeInstanceType());
            pplVersionGroupRecordItemDO.setVersionGroupId(versionGroupDO.getId());
            pplVersionGroupRecordItemDO.setVersionGroupRecordId(recordDO.getId());
            pplVersionGroupRecordItemDO.setRecordVersion(newRecordVersion);
            pplVersionGroupRecordItemDO.setImportType("sync");
            if (isCVM && pplIsBeforeVersionBeginMonth(version, pplVersionGroupRecordItemDO)) {
                // 【6756_2617】https://zhiyan.woa.com/requirement/6756/story/#/2617?story_tab=info
                // 针对 CVM，ppl 的开始购买日期在 版本配置的录入范围之前，则不同步到 版本数据之中
                continue;
            }
            // 非退回 且 海外 且不 满足海外年月范围
            if (!pplVersionGroupRecordItemDO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                && overseas.contains(pplVersionGroupRecordItemDO.getRegionName())
                    && !version.isSatisfyOverseasYearMonth(pplVersionGroupRecordItemDO.getBeginBuyDate())){
                continue;
            }
            recordItemDOList.add(pplVersionGroupRecordItemDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(recordItemDOList);

        // 更新versionGroup的关联审批版本信息
        if (ListUtils.isNotEmpty(syncItem)) {
            PplVersionGroupDO updateVersionGroupDO = new PplVersionGroupDO();
            updateVersionGroupDO.setId(versionGroupDO.getId());
            updateVersionGroupDO.setInnerVersionId(syncItem.get(0).getVersionId());
            updateVersionGroupDO.setIsExtendLastVersion(Boolean.TRUE);
            updateVersionGroupDO.setSyncVersionTime(new Date());
            demandDBHelper.update(updateVersionGroupDO);
        }

        // 更新ItemQueue状态
        List<Long> queueIdList = syncItem.stream().map(PplAuditItemQueueDO::getId)
                .collect(Collectors.toList());
        demandDBHelper.executeRaw("update ppl_audit_item_queue set status = 1,consume_time = ? where id in (?)",
                new Date(), queueIdList);

        // 扭转至下一个节点
        PplVersionGroupInfoVO groupInfoVO = demandDBHelper.getByKey(PplVersionGroupInfoVO.class,
                versionGroupDO.getId());
        pplVersionGroupService.import2NextRecord(groupInfoVO, "同步行业最新数据,自动扭转流程", Boolean.FALSE, null);

    }

    private boolean pplIsBeforeVersionBeginMonth(PplVersionDO version, PplVersionGroupRecordItemDO item) {
        if (version == null || item == null || item.getBeginBuyDate() == null
                || version.getDemandBeginMonth() == null || version.getDemandBeginYear() == null) {
            return false;
        }
        LocalDate versionBegin = LocalDate.of(version.getDemandBeginYear(), version.getDemandBeginMonth(), 1);
        return item.getBeginBuyDate().isBefore(versionBegin);
    }

    @Override
    public String sendAuditorMail() {
        List<PplInnerProcessVersionVO> pplInnerProcessVersionVOS = innerVersionService.queryAllProcessingVersion(
                Boolean.FALSE);
        List<Long> versionIds = pplInnerProcessVersionVOS.stream().map(PplInnerProcessVersionDO::getId).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(versionIds)) {
            return "暂无生效版本数据";
        }

        Map<String, Boolean> isVersionSendMap = new HashMap<>();
        pplInnerProcessVersionVOS.forEach(e -> {
            isVersionSendMap.putIfAbsent(e.getIndustryDept(), Boolean.FALSE);
        });

        // 查询出当前还在草稿箱未预提交的所有草稿单;
        Date nowDate = new Date();
        String beginDate = DateUtils.getYear(nowDate) + "-" + String.format("%tm", nowDate) + "-01";
        WhereContent draftItemSql = new WhereContent();
        draftItemSql.andEqual(PplItemDraftDO::getDraftStatus, PplOrderDraftStatusEnum.DRAFT.getCode());
        draftItemSql.andGTE(PplItemDraftDO::getBeginBuyDate, beginDate);
        draftItemSql.andEqual("deleted", 0);
        List<String> orders = demandDBHelper.getRaw(String.class,
                "select distinct ppl_order from ppl_item_draft" + draftItemSql.getSql(), draftItemSql.getParams());
        List<PplOrderDraftDO> allDraftList = demandDBHelper.getAll(PplOrderDraftDO.class,
                "where draft_status = ? and ppl_order in(?) ", PplOrderDraftStatusEnum.DRAFT.getCode(), orders);

        // 查询出当前需要审批人审批的
        List<PplOrderDO> allAuditDOList = demandDBHelper.getAll(PplOrderDO.class,
                "where node_code is not null  and node_code != '' and audit_status = ?",
                PplOrderAuditStatusEnum.WAIT.getCode());
        // 查询出当前需要被驳回人处理的
        List<PplOrderDO> allRejectOrderList = demandDBHelper.getAll(PplOrderDO.class,
                "where node_code is not null  and node_code != '' and audit_status = ?",
                PplOrderAuditStatusEnum.REJECT_MODIFY.getCode());

        // 开始对各行业的相关处理人进行对应邮件提醒
        Map<Long, List<PplInnerProcessVersionSlaDO>> auditNodeMap = demandDBHelper.getAll(
                        PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", versionIds).stream()
                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));
        for (PplInnerProcessVersionVO version : pplInnerProcessVersionVOS) {
            if (Boolean.TRUE.equals(isVersionSendMap.get(version.getIndustryDept()))) {
                continue;
            }

            List<PplInnerProcessVersionSlaDO> slaDOList = auditNodeMap.get(version.getId());
            PplInnerProcessVersionSlaDO firstAuditNode = slaDOList.stream()
                    .filter(node -> "ENTER".equals(node.getDeadlineType())).findFirst().orElse(null);
            List<PplInnerProcessVersionSlaDO> versionSlas = slaDOList.stream()
                    .filter(node -> "EXIT".equals(node.getDeadlineType())).collect(
                            Collectors.toList());

            boolean isTest = Strings.isNotBlank(testEnv.get());
            //  测试环境关掉
            String prefix = isTest ? "exp-" : "";

            // a) 如果此行业的当前审批版本处于需求沟通阶段，则进行 待提交 邮件提醒;
            Map<String, List<PplOrderDraftDO>> submitUserToPpl = new HashMap<>();
            if (firstAuditNode == null || firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime()
                    .after(new Date())) {
                List<PplOrderDraftDO> draftList = allDraftList.stream()
                        .filter(e -> version.getIndustryDept().equals(e.getIndustryDept()))
                        .filter(e -> e.getSubmitUser() != null).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(draftList)) {
                    submitUserToPpl = draftList.stream()
                            .collect(Collectors.groupingBy(PplOrderDraftDO::getSubmitUser));
                }
                submitUserToPpl.forEach((k, v) -> {
                    if (isTest && (k.contains("kaijiazhang") || k.contains("laurenpeng"))) {
                        k = "kaijiazhang;oliverychen;laurenpeng";
                    }
                    List<String> products = v.stream().map(PplOrderDraftDO::getProduct).distinct()
                            .collect(Collectors.toList());
                    Map<String, Object> templateParams = new HashMap<>();
                    templateParams.put("size", v.size());
                    templateParams.put("products", products);
                    dictService.eventNotice(CrpEventEnum.SUBMIT_USER_WAIT_COMMIT_NOTICE.getCode(),
                            null, null, templateParams, k);
                });
                isVersionSendMap.put(version.getIndustryDept(), Boolean.TRUE);
                continue;
            }

            // b) 如果此行业的当前审批版本处于审批阶段，则进行 待审批&驳回处理 邮件提醒
            Set<String> auditorSet = new HashSet<>();
            Set<String> rejectSet = new HashSet<>();
            Map<String, Integer> auditorToCount = new HashMap<>();
            Map<String, Date> auditorToDeadLine = new HashMap<>();
            Map<String, Integer> rejectorToCount = new HashMap<>();
            Map<String, Date> rejectorToDeadLine = new HashMap<>();

            Map<String, PplInnerProcessVersionSlaDO> versionSlaMap = ListUtils.toMap(versionSlas, o -> o.getNodeCode(),
                    o -> o);

            List<PplOrderDO> auditDOList = allAuditDOList.stream()
                    .filter(e -> version.getIndustryDept().equals(e.getIndustryDept())).collect(
                            Collectors.toList());
            for (PplOrderDO pplOrderDO : auditDOList) {
                List<String> auditor = Arrays.asList(pplOrderDO.getCurrentProcessor().split(";"));
                auditorSet.addAll(auditor);
                PplInnerProcessVersionSlaDO pplInnerProcessVersionSlaDO = versionSlaMap.get(pplOrderDO.getNodeCode());
                Date deadlineTime = null;
                if (pplInnerProcessVersionSlaDO != null) {
                    deadlineTime = pplInnerProcessVersionSlaDO.getDeadlineTime();
                }
                for (String s : auditor) {
                    auditorToCount.merge(s, 1, Integer::sum);
                    if (deadlineTime != null) {
                        if (auditorToDeadLine.get(s) == null) {
                            auditorToDeadLine.put(s, deadlineTime);
                        } else {
                            if (auditorToDeadLine.get(s).after(deadlineTime)) {
                                auditorToDeadLine.put(s, deadlineTime);
                            }
                        }
                    }
                }
            }

//            List<PplOrderDO> rejectOrderList = allRejectOrderList.stream()
//                    .filter(e -> version.getIndustryDept().equals(e.getIndustryDept())).collect(Collectors.toList());
//            for (PplOrderDO pplOrderDO : rejectOrderList) {
//                List<String> rejector = Arrays.asList(pplOrderDO.getRejectProcessor().split(";"));
//                rejectSet.addAll(rejector);
//                PplInnerProcessVersionSlaDO pplInnerProcessVersionSlaDO = versionSlaMap.get(pplOrderDO.getNodeCode());
//                Date deadlineTime = null;
//                if (pplInnerProcessVersionSlaDO != null) {
//                    deadlineTime = pplInnerProcessVersionSlaDO.getDeadlineTime();
//                }
//                for (String s : rejector) {
//                    if (rejectorToCount.get(s) == null) {
//                        rejectorToCount.put(s, 1);
//                    } else {
//                        rejectorToCount.put(s, rejectorToCount.get(s) + 1);
//                    }
//                    if (deadlineTime != null) {
//                        if (rejectorToDeadLine.get(s) == null) {
//                            rejectorToDeadLine.put(s, deadlineTime);
//                        } else {
//                            if (rejectorToDeadLine.get(s).after(deadlineTime)) {
//                                rejectorToDeadLine.put(s, deadlineTime);
//                            }
//                        }
//                    }
//                }
//            }

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            for (String s : auditorSet) {
                if (isTest && (s.contains("kaijiazhang") || s.contains("laurenpeng"))) {
                    s = "kaijiazhang;oliverychen;laurenpeng";
                }
                Map<String, Object> templateParams = new HashMap<>();
                templateParams.put("size", MapUtils.getObject(auditorToCount, s, 0));
                templateParams.put("industryDept", version.getIndustryDept());
                templateParams.put("auditorToDeadLine",
                        auditorToDeadLine.get(s) == null ? "无" : formatter.format(auditorToDeadLine.get(s)));
                templateParams.put("product", version.getProduct());
                dictService.eventNotice(CrpEventEnum.INNER_PROCESS_WAIT_AUDIT_NOTICE.getCode(),
                        null, null, templateParams, s);
            }

//            for (String s : rejectSet) {
//                if (isTest && (s.contains("kaijiazhang") || s.contains("laurenpeng"))) {
//                    s = "kaijiazhang;oliverychen;laurenpeng";
//                }
//                alert.sendMail(s, "行业内部PPL驳回待处理通知",
//                        "您当前在【" + version.getIndustryDept() + "】有 " + rejectorToCount.get(s)
//                                + " 条需求被驳回，请前往CRP系统处理，"
//                                + "<a href='https://" + prefix
//                                + "crp.woa.com/13ppl/approval-process/approval?tab=MyApproval&MyApprovalTab=MY_REJECT'>点击前往</a>。<br />"
//                                + (rejectorToDeadLine.get(s) == null ? "<br/>"
//                                : "处理截止时间: " + formatter.format(rejectorToDeadLine.get(s)) + " <br />")
//                                + "请注意：如果您未在截止时间内审批，系统将默认您拒绝。");
//            }
            isVersionSendMap.put(version.getIndustryDept(), Boolean.TRUE);
        }
        return "通知成功";

    }

    @Override
    public String sendExpiredMail() {
        List<PplInnerProcessVersionVO> pplInnerProcessVersionVOS = innerVersionService.queryAllProcessingVersion(
                Boolean.FALSE);
        List<Long> versionIds = pplInnerProcessVersionVOS.stream().map(PplInnerProcessVersionDO::getId).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(versionIds)) {
            return "暂无生效版本";
        }

        // 一个行业有多个产品，任意产品下有过期需求，按行业维度提醒一次即可
        Map<String, Boolean> isVersionSendMap = new HashMap<>();
        pplInnerProcessVersionVOS.forEach(e -> {
            isVersionSendMap.putIfAbsent(e.getIndustryDept(), Boolean.FALSE);
        });

        // 查出当前版本的所有待处理过期需求
        List<PplOrderDO> toDealOrderDOs = demandDBHelper.getRaw(PplOrderDO.class, "select distinct t2.* "
                        + " from ppl_order_expired_record t1 "
                        + " inner join ppl_order t2 on t1.ppl_order = t2.ppl_order and t2.deleted = 0 "
                        + " inner join ppl_item t3 on t1.ppl_order = t3.ppl_order and t3.deleted = 0 "
                        + " where t1.deleted = 0 and t1.version_id in(?) "
                        + " and t1.expired_operate_type = ? "
                        + " and t2.source = ? and t2.input_status = ? "
                        + " and t2.submit_user != 'dreamxin' and t2.customer_short_name not like '%云运管%'"
                        + " and t3.status != 'APPLIED' and t3.instance_num > 0 ",
                versionIds, PplOrderExpiredOperateTypeEnum.TO_DEAL.getCode(),
                PplOrderSourceTypeEnum.IMPORT.getCode(), PplOrderInputStatusEnum.NORMAL_EXPIRED.getCode());

        // 开始对各行业的相关处理人进行对应邮件提醒
        Map<Long, List<PplInnerProcessVersionSlaDO>> auditNodeMap = demandDBHelper.getAll(
                        PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", versionIds).stream()
                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));
        for (PplInnerProcessVersionVO version : pplInnerProcessVersionVOS) {
            if (Boolean.TRUE.equals(isVersionSendMap.get(version.getIndustryDept()))) {
                continue;
            }

            List<PplInnerProcessVersionSlaDO> slaDOList = auditNodeMap.get(version.getId());
            PplInnerProcessVersionSlaDO firstAuditNode = slaDOList.stream()
                    .filter(node -> "ENTER".equals(node.getDeadlineType())).findFirst().orElse(null);

            Boolean isTest = Strings.isNotBlank(testEnv.get());
            String prefix = isTest ? "exp-" : "";

            // 如果此行业的当前审批版本处于需求沟通阶段，则进行 待提交 邮件提醒；否则不作处理
            Map<String, List<PplOrderDO>> submitUserToPpl = new HashMap<>();
            if (firstAuditNode == null || firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime()
                    .after(new Date())) {
                List<PplOrderDO> orderDOList = toDealOrderDOs.stream()
                        .filter(e -> version.getIndustryDept().equals(e.getIndustryDept()))
                        .filter(e -> e.getSubmitUser() != null).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(orderDOList)) {
                    continue;
                }
                submitUserToPpl = orderDOList.stream()
                        .collect(Collectors.groupingBy(PplOrderDO::getSubmitUser));
                submitUserToPpl.forEach((k, v) -> {
                    if (isTest) {
                        k = "kaijiazhang;laurenpeng;oliverychen;samuelssu";
                    }
                    alert.sendMail(k, "PPL过期提醒", "您当前在【" + version.getIndustryDept() + "】"
                            + "有未处理的过期需求，请尽快前往CRP确认，"
                            + "<a href='https://" + prefix
                            + "crp.woa.com/13ppl/approval-process/forecast'>点击前往</a>。<br/>"
                            + ((firstAuditNode == null || firstAuditNode.getDeadlineTime() == null) ? "<br/>"
                            : "<span style = \"color:red\">过期需求处理截止时间：" + new SimpleDateFormat(
                                    "yyyy-MM-dd HH:mm:ss").format(
                                    firstAuditNode.getDeadlineTime()) + "</span><br/>"));
                });
                isVersionSendMap.put(version.getIndustryDept(), Boolean.TRUE);
            }
        }

        return "通知成功";
    }

    /**
     * 查询节点的审批人（预计）
     * node_code -> attribute_value -> processor list
     */
    private Map<String, Map<String, Set<String>>> getNodeProcessor(String industryDept) {
        List<PplInnerProcessNodeDO> nodes = demandDBHelper.getAll(PplInnerProcessNodeDO.class,
                "where process_id in (select id from ppl_inner_process where deleted=0 and industry_dept=?)",
                industryDept);
        if (ListUtils.isEmpty(nodes)) {
            return new HashMap<>();
        }
        List<String> approveRoles = ListUtils.transform(nodes, o -> o.getApproveRole());
        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where role in (?)", approveRoles);
        Map<String, List<IndustryDemandAuthDO>> byRoles = ListUtils.groupBy(auths, o -> o.getRole());

        Map<String, Map<String, Set<String>>> result = new HashMap<>();
        for (PplInnerProcessNodeDO node : nodes) {
            Map<String, Set<String>> attrMap = new HashMap<>();
            result.put(node.getNodeCode(), attrMap);

            List<IndustryDemandAuthDO> auth = byRoles.get(node.getApproveRole());
            ListUtils.forEach(auth, o -> {
                String attribute = "";
                if (PplInnerProcessAttributeEnum.CUSTOMER.getCode().equals(node.getRoleAttribute())) {
                    attribute = o.getCommonCustomerName();
                } else if (PplInnerProcessAttributeEnum.WAR_ZONE.getCode().equals(node.getRoleAttribute())) {
                    attribute = o.getWarZoneName();
                } else if (PplInnerProcessAttributeEnum.CENTER.getCode().equals(node.getRoleAttribute())) {
                    attribute = o.getCenterName();
                } else if (PplInnerProcessAttributeEnum.DEPT.getCode().equals(node.getRoleAttribute())) {
                    attribute = o.getIndustry();
                }
                String[] split = attribute.split(";");
                for (String s : split) {
                    if (StringTools.isBlank(s)) {
                        continue;
                    }
                    Set<String> processors = attrMap.computeIfAbsent(s, k -> new HashSet<>());
                    processors.add(o.getUserName());
                }
            });
        }
        return result;
    }

    /**
     * @param hadProcess 是否已经出现过已处理的节点，如果出现过，那么后面的节点都是DONE的节点
     */
    private void fillData(List<QueryProjectMonitorDetailResp.Node> roots,
            boolean hadProcess,
            Map<String, PplInnerProcessVersionSlaDO> versionSlaMap,
            Map<String, List<PplOrderAuditRecordDO>> pplOrderRecords) {
        if (roots == null) {
            return;
        }
        for (QueryProjectMonitorDetailResp.Node root : roots) {
            PplInnerProcessVersionSlaDO slaDO = versionSlaMap.get(root.getNodeCode());
            if (slaDO != null) {
                root.setDeadline(DateUtils.format(slaDO.getDeadlineTime()));
            }

            // 设置节点的进入时间
            if (root.getPplOrderRecords() != null) {
                Date enter = NumberUtils.min(root.getPplOrderRecords(), o -> o.getCreateTime());
                root.setEnterAuditTime(DateUtils.format(enter));
            }

            boolean thisProcessing = false;
            if ("PROCESSING".equals(root.getStatusCode())) {
                thisProcessing = true;
                root.setPplOrderDoneCount(
                        root.getPplOrderCount() - root.getPplOrderProcessingCount() - root.getPplOrderRefuseCount());
                // 对于已经全部完成的，状态从PROCESSSING置为DONE
                if (root.getPplOrderProcessingCount() == 0) {
                    root.setStatusCode("DONE");
                }
            }
            if (StringTools.isBlank(root.getStatusCode())) {
                if (hadProcess) {
                    root.setStatusCode("DONE");
                    root.setStatusName("已完成");
                    root.setPplOrderDoneCount(root.getPplOrderCount() - root.getPplOrderRefuseCount());

                    if (root.getPplOrderRecords() != null) {
                        Date finish = NumberUtils.max(root.getPplOrderRecords(), o -> o.getApproveTime());
                        root.setFinishAuditTime(DateUtils.format(finish));
                    }

                    Set<String> processor = new HashSet<>();
                    if (root.getPplOrders() != null) {
                        for (PplOrderDO pplOrderDO : root.getPplOrders()) {
                            List<PplOrderAuditRecordDO> records = pplOrderRecords.get(pplOrderDO.getPplOrder());
                            records = ListUtils.filter(records, o -> root.getNodeCode().equals(o.getNodeCode()));
                            ListUtils.forEach(records, o -> {
                                if (StringTools.isNotBlank(o.getOperateUser())) {
                                    processor.add(o.getOperateUser());
                                }
                            });
                        }
                    }
                    if (processor.size() > 0) {
                        root.setProcessors(String.join(";", processor));
                    } else {
                        root.setProcessors("");
                    }
                } else {
                    root.setPplOrderDoneCount(0);
                    root.setStatusCode("FUTURE");
                    root.setStatusName("审批未开始");
                    if (root.getPplOrderRefuseCount() != 0) {
                        root.setStatusName("已完成");
                        root.setStatusCode("DONE");
                    }
                    if (root.getPplOrderCount() == 0) {
                        root.setStatusName("已完成");
                        root.setStatusCode("DONE");
                    }
                }
            }

            if (root.getChildren() != null) {
                fillData(root.getChildren(), hadProcess || thisProcessing,
                        versionSlaMap, pplOrderRecords);
            }
        }
    }

    private String getProcessor(List<PplOrderDO> list, Function<PplOrderDO, String> mapper) {
        Set<String> processor = new HashSet<>();
        List<String> processors = ListUtils.transform(list, mapper);
        ListUtils.forEach(processors, o -> {
            if (StringTools.isNotBlank(o)) {
                String[] split = o.split(";");
                processor.addAll(Arrays.asList(split));
            }
        });
        return String.join(";", processor);
    }

    private List<QueryProjectMonitorDetailResp.Node> build(
            List<PplInnerProcessVersionSlaDO> nodeDOList,
            List<PplOrderDO> pplOrders,
            Map<String, Map<String, Set<String>>> nodeProcessor,
            Map<String, List<PplOrderAuditRecordDO>> pplOrderRecords,
            Map<String, List<PplOrderWithAuditRecordVO>> nodeToRefuseMap,
            Long versionId) {
        if (nodeDOList.isEmpty()) {
            return null;
        }
        PplInnerProcessVersionSlaDO node = nodeDOList.get(nodeDOList.size() - 1);
        Function<PplOrderDO, String> mapper = null;
        Function<PplOrderWithAuditRecordVO, String> refuseMapper = null;
        PplInnerProcessAttributeEnum attrEnum = PplInnerProcessAttributeEnum.getByCode(node.getRoleAttribute());
        if (attrEnum == null) {
            log.error("unknown roleAttribute={}", node.getRoleAttribute());
            return null; // 异常的节点，不处理
        }
        String nodeAttributeName = attrEnum.getName();
        switch (attrEnum) {
            case DEPT:
                mapper = o -> o.getIndustryDept();
                refuseMapper = o -> o.getPplOrderDO().getIndustryDept();
                break;
            case CENTER:
                mapper = o -> o.getCenter();
                refuseMapper = o -> o.getPplOrderDO().getCenter();
                break;
            case WAR_ZONE:
                mapper = o -> o.getWarZone();
                refuseMapper = o -> o.getPplOrderDO().getWarZone();
                break;
            case CUSTOMER:
                mapper = o -> o.getCustomerShortName();
                refuseMapper = o -> o.getPplOrderDO().getCustomerShortName();
                break;
            default:
        }

        Map<String, List<PplOrderDO>> groups = ListUtils.groupBy(pplOrders, mapper);
        Map<String, List<PplOrderWithAuditRecordVO>> refuseGroups = new HashMap<>();
        if (nodeToRefuseMap.get(node.getNodeCode()) != null) {
            refuseGroups = ListUtils.groupBy(nodeToRefuseMap.get(node.getNodeCode()), refuseMapper);
        }
        List<QueryProjectMonitorDetailResp.Node> results = new ArrayList<>();
        for (Map.Entry<String, List<PplOrderDO>> e : groups.entrySet()) {
            QueryProjectMonitorDetailResp.Node n = new QueryProjectMonitorDetailResp.Node();
            n.setNodeCode(node.getNodeCode());
            n.setNodeName(node.getNodeName());
            n.setProcessorRoleCode(node.getApproveRole());
            if (StringTools.isNotBlank(node.getApproveRole())) {
                IndustryDemandAuthRoleEnum role = IndustryDemandAuthRoleEnum.getByCode(node.getApproveRole());
                if (role != null) {
                    n.setProcessorRoleName(role.getName());
                } else {
                    n.setProcessorRoleName(node.getApproveRole());
                }
            }

            n.setAttributeName(nodeAttributeName);
            n.setAttributeValue(e.getKey());

            // 判断当前节点的状态，只记录进行中的就可以，DONE和FUTURE后处理
            List<PplOrderDO> currentOrder = ListUtils.filter(e.getValue(),
                    o -> Objects.equals(node.getNodeCode(), o.getNodeCode()));

            if (ListUtils.isNotEmpty(currentOrder)) {
                n.setStatusCode("PROCESSING");
                n.setStatusName("审批进行中");
                n.setProcessors(getProcessor(e.getValue(), PplOrderDO::getCurrentProcessor));

                // 计算当前审批中还没完成的数量
                n.setPplOrderProcessingCount(ListUtils.filter(
                        currentOrder, o -> PplOrderAuditStatusEnum.isProcessing(o.getAuditStatus())).size());
                if (n.getPplOrderProcessingCount() == 0) {
                    n.setStatusName("已审完，等待同级节点审完");
                }
            } else {
                // 从流程定义中拿审批人，在后处理时，对于已经被审批的，会替换成真实的审批人
                Map<String, Set<String>> m = nodeProcessor.get(node.getNodeCode());
                if (m != null) {
                    Set<String> processors = m.get(e.getKey());
                    if (processors != null) {
                        n.setProcessors(String.join(";", processors));
                    }
                }
                n.setPplOrderProcessingCount(0);
            }
            List<PplOrderDO> value = e.getValue();

            // 当前节点+当前分区（行业/中心/战区）拒绝的ppl数量。
            List<PplInnerProcessVersionSlaDO> subNodeList = nodeDOList.subList(0, nodeDOList.size() - 1);
            n.setPplOrderRefuseCount(refuseGroups.get(e.getKey()) != null ? refuseGroups.get(e.getKey()).size() : 0);
            n.setPplOrderCount(e.getValue().size() - queryHistoryRefuseRecord(e.getValue(), subNodeList, versionId));
            n.setPplOrders(e.getValue());

            // 写入当前节点的审批记录
            List<PplOrderAuditRecordDO> records = new ArrayList<>();
            for (PplOrderDO order : e.getValue()) {
                List<PplOrderAuditRecordDO> rds = pplOrderRecords.get(order.getPplOrder());
                if (rds != null) {
                    records.addAll(ListUtils.filter(rds, o -> Objects.equals(o.getNodeCode(), node.getNodeCode())));
                }
            }
            n.setPplOrderRecords(records);
            n.setChildren(build(nodeDOList.subList(0, nodeDOList.size() - 1),
                    e.getValue(), nodeProcessor, pplOrderRecords, nodeToRefuseMap, versionId));

            results.add(n);
        }

        return results;
    }

    Integer queryHistoryRefuseRecord(List<PplOrderDO> pplOrder, List<PplInnerProcessVersionSlaDO> subNodeList,
            Long versionId) {
        if (CollectionUtils.isEmpty(subNodeList) || CollectionUtils.isEmpty(pplOrder)) {
            return 0;
        }
        List<String> nodeCodeList = ListUtils.transform(subNodeList, PplInnerProcessVersionSlaDO::getNodeCode);
        List<String> pplOrderList = ListUtils.transform(pplOrder, PplOrderDO::getPplOrder);
        List<PplOrderAuditRecordDO> all = demandDBHelper.getAll(PplOrderAuditRecordDO.class,
                "where audit_status = ? and ppl_order in (?) and node_code in (?) and version_id = ?",
                PplOrderAuditStatusEnum.REFUSE.getCode(), pplOrderList, nodeCodeList, versionId);
        if (CollectionUtils.isEmpty(all)) {
            return 0;
        }
        return all.size();
    }

    @Override
    public QueryAuditOverviewResp queryAuditOverview(QueryAuditOverviewReq req) {
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
        List<PplInnerProcessVersionDO> currentVersions = new ArrayList<>();

        // 根据传入的versionId拿到对应的VersionDO
        PplInnerProcessVersionDO selectedVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where id = ? ", req.getVersionId());
        if (selectedVersion == null) {
            throw new BizException("选择的版本不存在！请联系olivery 或 lauren核查");
        }
        currentVersions.add(selectedVersion);

        // 是否没有选择最新版本: true-没有选择最新版；false-选择了最新版
        boolean noSelectLasted = true;
        // 判断传入的versionId是否为当前版本versionId
        if (selectedVersion.getStatus().equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode())) {
            noSelectLasted = false;
        }

        Map<String, Object> params = new HashMap<>();
        params.put("overSeaRegions", overSeaRegions);
        params.put("pplOrder", req.getPplOrder());
        params.put("currentVersionIds", ListUtils.transform(currentVersions, o -> o.getId()));

        // 处理其它条件
        String whereSql = req.transToSQL().getSQL();
        whereSql = whereToAnd(whereSql);
        Map<String, Object> whereParams = req.transToParamMap();
        params.putAll(whereParams);

        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/audit_overview.sql");
        sql = sql.replace("${FILTER}", whereSql);

        List<QueryAuditOverviewVO> list = demandDBHelper.getRaw(QueryAuditOverviewVO.class, sql, params);

        QueryAuditOverviewResp resp = new QueryAuditOverviewResp();
        resp.setData(new ArrayList<>());

        // 若无数据，则判断是否在当前版本
        if (CollectionUtils.isEmpty(list)) {
            // 获取当前版本
            if (CollectionUtils.isEmpty(currentVersions)) {
                return resp;
            }

            // 当 没有选择版本（默认查当前版本的所有的versionId） /  选择了版本且为当前最新版本 时，获取需求沟通的草稿数据
            if (req.getVersionId() != null && noSelectLasted) {
                return resp;
            }

            PplInnerProcessVersionDO processVersionDO = currentVersions.stream()
                    .filter(e -> e.getStatus().equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode()))
                    .collect(Collectors.toList()).get(0);
            if (processVersionDO != null) {
                // 判断当前版本是否在版本审批日期之前（当前时间是否在第一个节点的deadline_time之前），若在则获取草稿数据
                PplInnerProcessVersionSlaDO firstAuditNode =
                        demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                                "where version_id= ? and deadline_type='ENTER'", processVersionDO.getId());
                if (firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime().after(new Date())) {
                    // 查出需求沟通的草稿数据返回
                    return queryDraftOverview(req);
                }
            }


        }

        Map<String, List<QueryAuditOverviewVO>> groups = ListUtils.groupBy(list,
                o -> QueryAuditOverviewVO.keyForGroup(o));
        for (List<QueryAuditOverviewVO> group : groups.values()) {
            QueryAuditOverviewResp.Item item = QueryAuditOverviewVO.transTo(group.get(0));
            resp.getData().add(item);
            // 补齐和计算数据
            QueryAuditOverviewVO.calForResp(item, group);
        }

        return resp;
    }

    @Override
    public QueryAuditOverviewResp queryDraftOverview(QueryAuditOverviewReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        Map<String, Object> params = new HashMap<>();
        params.put("overSeaRegions", overSeaRegions);
        params.put("pplOrder", req.getPplOrder());

        // 处理其它条件
        String whereSql = req.transToSQL().getSQL();
        whereSql = whereToAnd(whereSql);
        Map<String, Object> whereParams = req.transToParamMap();
        params.putAll(whereParams);

        String afterSql = ORMUtils.getSql("/sql/ppl13week/inner_process/draft_audit_overview.sql");
        afterSql = afterSql.replace("${FILTER}", whereSql);
        // 先查出AFTER的数据
        List<QueryAuditOverviewVO> afterList = demandDBHelper.getRaw(QueryAuditOverviewVO.class, afterSql, params);

        // 查出最新一次已生效的版本
        PplInnerProcessVersionDO one = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where industry_dept = ? and product = ? and status = ? order by id desc",
                req.getIndustryDept(), currentVersion.getProduct(), PplInnerProcessVersionStatusEnum.DONE.getCode());
        if (one != null) {
            String beforeSql = ORMUtils.getSql("/sql/ppl13week/inner_process/audit_overview.sql");
            beforeSql = beforeSql.replace("${FILTER}", whereSql);
            params.put("currentVersionIds", Arrays.asList(one.getId()));
            // 再查出before的数据
            List<QueryAuditOverviewVO> list = demandDBHelper.getRaw(QueryAuditOverviewVO.class, beforeSql, params);
            if (!CollectionUtils.isEmpty(list)) {
                //上一版本的after数据，实际上为当前版本的before数据
                List<QueryAuditOverviewVO> before = list.stream().filter(v -> v.getRecordType().equals("AFTER"))
                        .collect(Collectors.toList());
                for (QueryAuditOverviewVO queryAuditOverviewVO : before) {
                    queryAuditOverviewVO.setRecordType("BEFORE");
                }
                afterList.addAll(before);
            }
        }

        QueryAuditOverviewResp resp = new QueryAuditOverviewResp();
        resp.setData(new ArrayList<>());

        Map<String, List<QueryAuditOverviewVO>> groups = ListUtils.groupBy(afterList,
                o -> QueryAuditOverviewVO.keyForGroup(o));

        for (List<QueryAuditOverviewVO> group : groups.values()) {
            QueryAuditOverviewResp.Item item = QueryAuditOverviewVO.transTo(group.get(0));
            resp.getData().add(item);
            // 补齐和计算数据
            QueryAuditOverviewVO.calForResp(item, group);
        }

        return resp;
    }


    private static String whereToAnd(String whereSql) {
        if (StringTools.isBlank(whereSql)) {
            return "";
        }
        whereSql = whereSql.trim();
        if (whereSql.toLowerCase().startsWith("where")) {
            whereSql = whereSql.substring("where".length());
        }
        if (StringTools.isNotBlank(whereSql)) {
            whereSql = " AND (" + whereSql + ")";
        }
        return whereSql;
    }

    @Override
    public List<PplOrderVo> queryInnerPplOrderList(QueryInnerPplOrderListReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        // 进行中版本
        PplInnerProcessVersionDO processingVersion = innerVersionService.queryProcessingVersionByDeptAndProduct(
                currentVersion.getIndustryDept(), currentVersion.getProduct());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        List<String> versionProductList = new ArrayList<>(Arrays.asList(currentVersion.getProduct().split(";")));
        if (ListUtils.isNotEmpty(req.getProductList())) {
            req.getProductList().retainAll(versionProductList);
        } else {
            req.setProductList(versionProductList);
        }
        if (StringUtils.isNotBlank(req.getProduct())) {
            req.getProductList().retainAll(new ArrayList<>(Arrays.asList(req.getProduct())));
        }

        Map<String, String> nodeCodeNameMap = innerVersionService.getNodeCodeMap(currentVersion.getIndustryDept(),
                currentVersion.getProduct(), currentVersion.getId());

        String userName =
                Strings.isNotBlank(req.getUsername()) ? req.getUsername() : LoginUtils.getUserNameWithSystem();
        WhereContent whereContent = new WhereContent();
        List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaList(req.getVersionId());
        if (CollectionUtils.isEmpty(versionSlaList)) {
            throw new BizException("该行业未初始化流程");
        }

        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        if (req.getQueryType().equals(PplOrderQueryTypeEnum.MY_AUDIT.getCode()) ||
                req.getQueryType().equals(PplOrderQueryTypeEnum.MY_REJECT.getCode())) {
            whereContent.andEqual(PplOrderVo::getIndustryDept, req.getIndustryDept());
            if (req.getQueryType().equals(PplOrderQueryTypeEnum.MY_AUDIT.getCode())) {
                whereContent.andEqual(PplOrderVo::getAuditStatus, PplOrderAuditStatusEnum.WAIT.getCode());
                whereContent.addAnd("current_processor like ?", "%" + userName + "%");
            } else {
                whereContent.andEqual(PplOrderVo::getAuditStatus, PplOrderAuditStatusEnum.REJECT_MODIFY.getCode());
                whereContent.addAnd("current_processor like ? or reject_processor = ?", "%" + userName + "%", userName);
            }

            if (versionSlaList.get(0).getDeadlineTime() == null || versionSlaList.get(0).getDeadlineTime()
                    .after(new Date())) {
                // 如果当前没配审批时间 或者 还没到审批时间直接返回
                return new ArrayList<>();
            }

            if (!CollectionUtils.isEmpty(req.getProductList())) {
                whereContent.addAnd("ppl_order in (select ppl_order from ppl_order_audit_record_item where "
                        + "deleted = 0 and product in (?))", req.getProductList());
            }
            List<PplOrderVo> all = demandDBHelper.getAll(PplOrderVo.class, whereContent.getSql(),
                    whereContent.getParams());
            if (CollectionUtils.isEmpty(all)) {
                return new ArrayList<>();
            }

            // 优先只查出第一个节点的PplOrder
            Map<String, List<PplOrderVo>> codeToOrderMap = all.stream()
                    .collect(Collectors.groupingBy(PplOrderVo::getNodeCode));
            List<PplOrderVo> filterVo = new ArrayList<>();
            for (PplInnerProcessVersionSlaDO innerProcessVersionSlaDO : versionSlaList) {
                List<PplOrderVo> pplOrderVos = codeToOrderMap.get(innerProcessVersionSlaDO.getNodeCode());
                if (pplOrderVos != null) {
                    filterVo.addAll(pplOrderVos);
                    break;
                }
            }
            List<String> pplOrders = filterVo.stream().map(PplOrderVo::getPplOrder).collect(Collectors.toList());
            Map<String, PplOrderAuditRecordVO> orderToVO = auditRecordService.queryLatestAuditRecordAndItem(pplOrders,
                            currentVersion.getId())
                    .stream().collect(Collectors.toMap(PplOrderAuditRecordVO::getPplOrder, v -> v));
            List<PplOrderVo> result = new ArrayList<>();

            List<PplOrderAuditListVO> raw = auditRecordService.buildPplOrderAuditListVO(pplOrders);
            Map<String, Integer> orderToChangeCore = auditRecordService.queryPplOrderChangeCore(pplOrders, raw);
            Map<String, Integer> orderToChangeGpu = auditRecordService.queryPplOrderChangeGpuNum(pplOrders, raw);
            Map<String, Integer> orderToChangeInstanceNum = auditRecordService.queryPplOrderChangeInstanceNum(
                    pplOrders, raw);
            Map<String, Integer> orderToChangeMemory = auditRecordService.queryPplOrderChangeMemory(pplOrders, raw);
            Map<String, BigDecimal> orderToChangeCos = auditRecordService.queryPplOrderChangeCosStorage(pplOrders, raw);
            Map<String, BigDecimal> orderToChangeDatabase = auditRecordService.queryPplOrderChangeDatabaseStorage(pplOrders, raw);

            Map<String, String> rejectNoteMap = auditRecordService.queryRejectNote(pplOrders);
            for (PplOrderVo pplOrderVo : filterVo) {
                PplOrderAuditRecordVO pplOrderAuditRecordVo = orderToVO.get(pplOrderVo.getPplOrder());
                if (pplOrderAuditRecordVo != null) {
                    PplOrderAuditRecordItemVO.preHandle(pplOrderAuditRecordVo.getItemVOList(), overSeaRegions);
                    pplOrderVo.setItemVOList(pplOrderAuditRecordVo.getItemVOList());
                    PplOrderVo.convertPplOrderVo(pplOrderVo);
                    pplOrderVo.setApproveNote(rejectNoteMap.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeCore(orderToChangeCore.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeGpu(orderToChangeGpu.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeInstanceNum(orderToChangeInstanceNum.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeMemory(orderToChangeMemory.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeCosStorage(orderToChangeCos.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setChangeDatabaseStorage(orderToChangeDatabase.get(pplOrderVo.getPplOrder()));
                    pplOrderVo.setApproveNodeName(nodeCodeNameMap.get(pplOrderVo.getApproveNodeCode()));
                    pplOrderVo.setNodeName(nodeCodeNameMap.get(pplOrderVo.getNodeCode()));
                    pplOrderVo.setPplSource(PplOrderSourceEnum.getNameByPplOrder(pplOrderVo.getPplOrder()));
                    result.add(pplOrderVo);
                }
            }

            // 添加供应方案、共识信息
            pplDraftService.addConsensusSupplyPlanForPplOrderVo(result);
            // 设置ppl_item的预约状态（即上一版本继承下来的草稿单所关联的ppl_item的预约状态；若是当前版本新增的草稿单，即还未生效过-ppl_item表中没有记录，则预约状态默认为未预约-VALID）
            completeItemApplyInfo(result);

            return result;
        } else if (req.getQueryType().equals(PplOrderQueryTypeEnum.MY_REFUSE.getCode())) {

            //查出被拒绝的单据
            List<PplOrderAuditRecordVO> all = demandDBHelper.getRaw(PplOrderAuditRecordVO.class,
                    "SELECT * from ppl_order_audit_record where deleted = 0 and id in (SELECT max(id) from ppl_order_audit_record "
                            + "where version_id = ? and operate_user is not null and approve_result = ? GROUP BY ppl_order)",
                    currentVersion.getId(), PplOrderAuditStatusEnum.REFUSE.getCode());
            //过滤掉PPLOrder为空的
            List<PplOrderAuditRecordVO> auditedRecord = all.stream().filter(v -> v.getPplOrderDO() != null)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(auditedRecord)) {
                return Collections.EMPTY_LIST;
            }
            List<PplOrderAuditRecordVO> collect = auditedRecord.stream()
                    .filter(v -> v.getOperateUser().equals(userName) || v.getPplOrderDO().getSubmitUser()
                            .equals(userName)).collect(Collectors.toList());
            List<PplOrderVo> pplOrderVos = PplOrderVo.auditOrderVoToPplOrderVo(collect, overSeaRegions);
            pplOrderVos.forEach(e -> {
                e.setApproveNodeName(nodeCodeNameMap.get(e.getApproveNodeCode()));
                e.setNodeName(nodeCodeNameMap.get(e.getNodeCode()));
            });
            // 添加供应方案、共识信息
            pplDraftService.addConsensusSupplyPlanForPplOrderVo(pplOrderVos);
            return pplOrderVos;
        } else if (req.getQueryType().equals(PplOrderQueryTypeEnum.MY_AUDITED.getCode())) {
            List<PplOrderAuditRecordVO> all = demandDBHelper.getRaw(PplOrderAuditRecordVO.class,
                    "SELECT * from ppl_order_audit_record where deleted = 0 and id in (SELECT max(id) from ppl_order_audit_record "
                            + "where operate_user = ? and approve_time is not null GROUP BY ppl_order)", userName);
            //过滤掉PPLOrder为空的
            List<PplOrderAuditRecordVO> auditedRecord = all.stream().filter(v -> v.getPplOrderDO() != null
                            && v.getPplOrderDO().getIndustryDept().equals(req.getIndustryDept()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(auditedRecord)) {
                return Collections.EMPTY_LIST;
            }
            List<PplOrderVo> pplOrderVos = PplOrderVo.auditOrderVoToPplOrderVo(auditedRecord, overSeaRegions);
            pplOrderVos.forEach(e -> {
                e.setApproveNodeName(nodeCodeNameMap.get(e.getApproveNodeCode()));
                e.setNodeName(nodeCodeNameMap.get(e.getNodeCode()));
            });
            // 添加供应方案、共识信息
            pplDraftService.addConsensusSupplyPlanForPplOrderVo(pplOrderVos);
            return pplOrderVos;
        } else if (req.getQueryType().equals(PplOrderQueryTypeEnum.ALL.getCode())) {
            // 是否为进行中的版本
            Boolean isProcessingVersion = currentVersion.getId().equals(processingVersion.getId());
            if (isProcessingVersion) {
                // 如果是当前版本，看看当前是否在版本审批日期之前
                PplInnerProcessVersionSlaDO firstAuditNode =
                        demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                                "where version_id= ? and deadline_type='ENTER'", currentVersion.getId());
                if (firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime().after(new Date())) {
                    // 查出需求沟通的内容返回
                    QueryPplDraftReq draftReq = BeanUtil.copyProperties(req, QueryPplDraftReq.class);
                    if (ListUtils.isNotEmpty(req.getProductList())) {
                        draftReq.setProduct(req.getProductList());
                    }
                    List<PplListVo> pplListVos = pplDraftService.queryPreSubmitDraftData(draftReq);

                    if (CollectionUtils.isEmpty(pplListVos)) {
                        return Collections.EMPTY_LIST;
                    }
                    List<PplOrderVo> result = new ArrayList<>();
                    for (PplListVo pplListVo : pplListVos) {
                        PplOrderVo pplOrderVo = new PplOrderVo();
                        BeanUtils.copyProperties(pplListVo, pplOrderVo);
                        pplOrderVo.setCustomerUin(pplListVo.getCustomerUin());
                        pplOrderVo.setAuditStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
                        pplOrderVo.setAllCore(pplListVo.getTotalCore());
                        pplOrderVo.setSubmitUser(pplListVo.getSubmitUser());
                        pplOrderVo.setChangeCore(pplListVo.getChangeCore());
                        pplOrderVo.setBeginBuyDate(LocalDate.parse(pplListVo.getBeginBuyDate()));
                        pplOrderVo.setEndBuyDate(LocalDate.parse(pplListVo.getEndBuyDate()));
                        pplOrderVo.setWinRate(pplListVo.getWinRate());
                        pplOrderVo.setProduct(pplListVo.getProduct());
                        pplOrderVo.setItemVOList(transToAuditItem(pplListVo.getPplItems(), pplOrderVo.getPplOrder()));
                        result.add(pplOrderVo);
                    }
                    return result;
                }
            }
            whereContent = checkPermission(userName, req.getIndustryDept(), "", currentVersion.getId());
            if (whereContent == null) {
                return Collections.EMPTY_LIST;
            }
            List<PplOrderAuditRecordVO> pplOrderAuditRecordVOS = auditRecordService.queryVersionPplOrder(
                    req.getVersionId(), req.getIndustryDept(), null, null, null, whereContent);
            // 过滤出对应产品
            if (ListUtils.isNotEmpty(req.getProductList())) {
                List<PplOrderAuditRecordVO> filterVO = new ArrayList<>();
                for (PplOrderAuditRecordVO pplOrderAuditRecordVO : pplOrderAuditRecordVOS) {
                    if (req.getProductList().contains(pplOrderAuditRecordVO.getItemVOList().get(0).getProduct())) {
                        filterVO.add(pplOrderAuditRecordVO);
                    }
                }
                pplOrderAuditRecordVOS = filterVO;
            }
            List<PplOrderVo> pplOrderVos = PplOrderVo.auditOrderVoToPplOrderVo(pplOrderAuditRecordVOS, overSeaRegions);
            // 添加供应方案、共识信息
            pplDraftService.addConsensusSupplyPlanForPplOrderVo(pplOrderVos);
            if (!req.getIsNeedDetail()) {
                return pplOrderVos;
            }
            List<String> pplOrders = ListUtils.transform(pplOrderVos, PplOrderVo::getPplOrder);

            List<PplOrderAuditListVO> raw = auditRecordService.buildPplOrderAuditListVO(pplOrders);
            Map<String, Integer> orderToChangeCore = auditRecordService.queryPplOrderChangeCore(pplOrders, raw);
            Map<String, Integer> orderToChangeGpu = auditRecordService.queryPplOrderChangeGpuNum(pplOrders, raw);
            Map<String, Integer> orderToChangeInstanceNum = auditRecordService.queryPplOrderChangeInstanceNum(
                    pplOrders, raw);
            Map<String, Integer> orderToChangeMemory = auditRecordService.queryPplOrderChangeMemory(pplOrders, raw);
            Map<String, BigDecimal> orderToChangeCos = auditRecordService.queryPplOrderChangeCosStorage(pplOrders, raw);
            Map<String, BigDecimal> orderToChangeDatabase = auditRecordService.queryPplOrderChangeDatabaseStorage(pplOrders, raw);

            List<String> orderNumberList = pplOrderVos.stream().flatMap(v -> v.getItemVOList().stream())
                    .filter(v -> v.getYunxiaoOrderId() != null &&
                            (v.getYunxiaoOrderId().startsWith("OE") || v.getYunxiaoOrderId().startsWith("ON")))
                    .map(PplOrderAuditRecordItemVO::getYunxiaoOrderId).distinct().collect(Collectors.toList());
            List<OrderInfoDO> orderList = demandDBHelper.getAll(OrderInfoDO.class,
                    "where available_status = ? and order_number in (?)",
                    OrderAvailableStatusEnum.AVAILABLE.getCode(), orderNumberList);
            Map<String, String> orderMap = orderList.stream()
                    .collect(Collectors.toMap(OrderInfoDO::getOrderNumber, OrderInfoDO::getOrderNodeCode));

            for (PplOrderVo pplOrderVo : pplOrderVos) {
                pplOrderVo.setPplSource(PplOrderSourceEnum.getNameByPplOrder(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeCore(orderToChangeCore.get(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeGpu(orderToChangeGpu.get(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeInstanceNum(orderToChangeInstanceNum.get(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeMemory(orderToChangeMemory.get(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeCosStorage(orderToChangeCos.get(pplOrderVo.getPplOrder()));
                pplOrderVo.setChangeDatabaseStorage(orderToChangeDatabase.get(pplOrderVo.getPplOrder()));
                if (!Objects.equals(req.getVersionId(), currentVersion.getId())) {
                    // 如果不是当前版本 则将审批人 审批节点置空， 并调整至已完结
                    pplOrderVo.setNodeCode("");
                    pplOrderVo.setCurrentProcessor("");
                    pplOrderVo.setAuditStatus(null);
                }
                pplOrderVo.setApproveNodeName(nodeCodeNameMap.get(pplOrderVo.getApproveNodeCode()));
                pplOrderVo.setNodeName(nodeCodeNameMap.get(pplOrderVo.getNodeCode()));
                pplOrderVo.setAuditStatus(
                        isProcessingVersion ? pplOrderVo.getAuditStatus() : PplOrderAuditStatusEnum.AUDITED.getCode());

                OrderAppliedStatusDeal(pplOrderVo, orderMap);
            }

            // 设置ppl_item的预约状态（即上一版本继承下来的草稿单所关联的ppl_item的预约状态；若是当前版本新增的草稿单，即还未生效过-ppl_item表中没有记录，则预约状态默认为未预约-VALID）
//            completeItemApplyInfo(pplOrderVos);

            return pplOrderVos;
        } else {
            throw new BizException("查询类型不合规");
        }
    }


    public void OrderAppliedStatusDeal(PplOrderVo pplOrderVo, Map<String, String> orderMap) {
        List<PplOrderAuditRecordItemVO> items = pplOrderVo.getItemVOList();
        List<PplOrderAuditRecordItemVO> appliedList = items.stream()
                .filter(v -> v.getStatus().equals(PplItemStatusEnum.APPLIED.getCode())).collect(
                        Collectors.toList());

        if (ListUtils.isNotEmpty(appliedList)) {
            String yunxiaoOrderId = appliedList.get(0).getYunxiaoOrderId();
            pplOrderVo.setAppliedStatus(PplItemStatusEnum.APPLIED.getCode());
            pplOrderVo.setYunxiaoOrderId(yunxiaoOrderId);
            if (yunxiaoOrderId.startsWith("OE") || yunxiaoOrderId.startsWith("ON")) {
                pplOrderVo.setYunxiaoOrderId(yunxiaoOrderId);
                String orderNodeCode = orderMap.get(yunxiaoOrderId);
                pplOrderVo.setCurrentOrderStatus(orderNodeCode == null ?
                        "" : OrderNodeCodeEnum.getNameByCode(orderNodeCode));
            } else {
                pplOrderVo.setCurrentOrderStatus("");
            }

        } else {
            pplOrderVo.setAppliedStatus(PplItemStatusEnum.VALID.getCode());
        }
    }

    /**
     * result 的 ppl-item维度 补充 预约信息
     * （result - 审批记录）
     */
    private void completeItemApplyInfo(List<PplOrderVo> result) {
        List<PplOrderAuditRecordItemVO> allItems = result.stream().flatMap(e -> e.getItemVOList().stream())
                .collect(Collectors.toList());
        List<String> pplIds = allItems.stream().map(PplOrderAuditRecordItemVO::getPplId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pplIds)) {
            return;
        }

        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_id in(?)",
                pplIds);
        pplItemDOList.addAll(demandDBHelper.getAll(PplItemDO.class,
                "where parent_ppl_id in (?) ", pplIds));
        pplItemDOList = pplItemDOList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<String, PplItemDO> itemDOMap = pplItemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<PplItemDO>> itemDOGroupMap = pplItemDOList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplItemDO::getParentPplId));

        Map<String, List<PplOrderAuditRecordItemVO>> splitItemDOMap = allItems.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplOrderAuditRecordItemVO::getParentPplId));

        allItems.forEach(e -> {
            PplItemDO currentItemDO = itemDOMap.get(e.getPplId());

            boolean isUnAppliedDraft =
                    currentItemDO == null && !PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus());
            boolean isUnAppliedValid =
                    currentItemDO != null && !PplItemStatusEnum.APPLIED.getCode().equals(currentItemDO.getStatus());
            if (isUnAppliedDraft || isUnAppliedValid) {
                // 1、非预约单，直接设置预约相关信息为默认值
                e.setStatus(PplItemStatusEnum.VALID.getCode());
                e.setYunxiaoOrderId("");
                e.setYunxiaoDetailId(0L);
                e.setYunxiaoOrderStatus("");

                e.setAfterNewApplyTotalCore(0);
                e.setBeforeNewApplyTotalCore(0);
                e.setDiffNewApplyTotalCore(0);

                e.setAfterNewApplyTotalGpuNum(BigDecimal.ZERO);
                e.setBeforeNewApplyTotalGpuNum(BigDecimal.ZERO);
                e.setDiffNewApplyTotalGpuNum(BigDecimal.ZERO);
                return;
            }

            // 2、预约单
            if (currentItemDO == null) {
                // 2.1、无对应生效的PN单，则直接取当前记录的预约信息
                e.setAfterNewApplyTotalCore(e.getTotalCoreApplyAfter() == null ? 0 : e.getTotalCoreApplyAfter());
                e.setBeforeNewApplyTotalCore(e.getTotalCoreApplyBefore() == null ? 0 : e.getTotalCoreApplyBefore());
                e.setDiffNewApplyTotalCore(e.getAfterNewApplyTotalCore() - e.getBeforeNewApplyTotalCore());

                e.setAfterNewApplyTotalGpuNum(
                        e.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO : e.getTotalGpuNumApplyAfter());
                e.setBeforeNewApplyTotalGpuNum(
                        e.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO : e.getTotalGpuNumApplyBefore());
                e.setDiffNewApplyTotalGpuNum(
                        e.getAfterNewApplyTotalGpuNum().subtract(e.getBeforeNewApplyTotalGpuNum()));
                return;
            }

            // 2.2、有对应生效的PN单，则取生效PN单的预约信息
            // 考虑拆单情况：若未拆单/是子单/是母单且审批流中有拆单信息(isCurrentItem=true)，则取本单的预约信息及预约量即可；若是母单(isCurrentItem=false)，且拆单信息未同步到审批流，则取母单的预约信息，预约量聚合获得
            boolean isCurrentItem =
                    (StringUtils.isBlank(currentItemDO.getParentPplId())
                            || !currentItemDO.getPplId().equals(currentItemDO.getParentPplId())) || (
                            splitItemDOMap.get(currentItemDO.getParentPplId()) != null
                                    && splitItemDOMap.get(currentItemDO.getParentPplId()).size() > 1);

            // 若拆单(isSplit = true)，则获取同一母单下的所有ppl-item
            boolean isSplit = StringUtils.isNotBlank(currentItemDO.getParentPplId());
            List<PplItemDO> applyItemDOs =
                    isSplit ? (itemDOGroupMap.get(currentItemDO.getParentPplId()) == null ? Collections.emptyList()
                            : itemDOGroupMap.get(currentItemDO.getParentPplId()).stream()
                                    .filter(o -> PplItemStatusEnum.APPLIED.getCode().equals(o.getStatus()))
                                    .collect(Collectors.toList())) : Collections.emptyList();

            // 设置预约相关信息
            e.setParentPplId(currentItemDO.getParentPplId());

            e.setStatus(currentItemDO.getStatus());
            e.setYunxiaoOrderId(currentItemDO.getYunxiaoOrderId());
            e.setYunxiaoDetailId(currentItemDO.getYunxiaoDetailId());
            e.setYunxiaoOrderStatus(currentItemDO.getYunxiaoOrderStatus());

            e.setAfterNewApplyTotalCore(isCurrentItem ? (currentItemDO.getTotalCoreApplyAfter() == null ? 0
                    : currentItemDO.getTotalCoreApplyAfter())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getTotalCoreApplyAfter() != null ? o.getTotalCoreApplyAfter() : 0)
                            .sum());
            e.setBeforeNewApplyTotalCore(isCurrentItem ? (currentItemDO.getTotalCoreApplyBefore() == null ? 0
                    : currentItemDO.getTotalCoreApplyBefore())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getTotalCoreApplyBefore() != null ? o.getTotalCoreApplyBefore() : 0)
                            .sum());
            e.setDiffNewApplyTotalCore(e.getAfterNewApplyTotalCore() - e.getBeforeNewApplyTotalCore());

            if (Ppl13weekProductTypeEnum.GPU.getName().equals(e.getProduct())) {
                e.setAfterNewApplyTotalGpuNum(
                        isCurrentItem ? (currentItemDO.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                                : currentItemDO.getTotalGpuNumApplyAfter())
                                : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyAfter)
                                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                e.setBeforeNewApplyTotalGpuNum(
                        isCurrentItem ? (currentItemDO.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO
                                : currentItemDO.getTotalGpuNumApplyBefore())
                                : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyBefore)
                                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                e.setDiffNewApplyTotalGpuNum(
                        e.getAfterNewApplyTotalGpuNum().subtract(e.getBeforeNewApplyTotalGpuNum()));
            }
        });

    }

    public List<PplOrderAuditRecordItemVO> transToAuditItem(List<SavePplDraftReq.DraftItemDTO> draftItemDTOList,
            String pplOrder) {
        List<PplOrderAuditRecordItemVO> list = new ArrayList<>();
        for (SavePplDraftReq.DraftItemDTO draftItemDTO : draftItemDTOList) {
            PplOrderAuditRecordItemVO pplOrderAuditRecordItemVO = new PplOrderAuditRecordItemVO();
            BeanUtils.copyProperties(draftItemDTO, pplOrderAuditRecordItemVO);
            pplOrderAuditRecordItemVO.setTotalCore(draftItemDTO.getTotalCoreNum());
            pplOrderAuditRecordItemVO.setTotalDisk(draftItemDTO.getTotalDiskNum());
            pplOrderAuditRecordItemVO.setBeginBuyDate(LocalDate.parse(draftItemDTO.getBeginBuyDate()));
            pplOrderAuditRecordItemVO.setEndBuyDate(LocalDate.parse(draftItemDTO.getEndBuyDate()));
            if (Strings.isNotBlank(draftItemDTO.getBeginElasticDate())) {
                pplOrderAuditRecordItemVO.setBeginElasticDate(LocalTime.parse(draftItemDTO.getBeginElasticDate()));
            }
            if (Strings.isNotBlank(draftItemDTO.getEndElasticDate())) {
                pplOrderAuditRecordItemVO.setEndElasticDate(LocalTime.parse(draftItemDTO.getEndElasticDate()));
            }
            if (ListUtils.isNotEmpty(draftItemDTO.getAlternativeZoneName())) {
                pplOrderAuditRecordItemVO.setAlternativeZoneName(String.join(";", draftItemDTO.getAlternativeZoneName()));
                pplOrderAuditRecordItemVO.setAlternativeZoneNameList(draftItemDTO.getAlternativeZoneName());
            }
            pplOrderAuditRecordItemVO.setPplOrder(pplOrder);
            list.add(pplOrderAuditRecordItemVO);
        }
        return list;
    }

    @Override
    public List<PplInnerDeptVersionVO> queryInnerVersionList(QueryInnerVersionListReq req) {
        WhereSQL whereSQL = new WhereSQL();
        if (StringTools.isNotBlank(req.getIndustryDept())) {
            whereSQL.and("industry_dept=?", req.getIndustryDept());
        }
        if (StringTools.isNotBlank(req.getProduct())) {
            whereSQL.and("product like ?", "%" + req.getProduct() + "%");
            if (req.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
                whereSQL.and("product not like ?", "%" + Ppl13weekProductTypeEnum.GPU.getName() + "%");
            }
        }
        if (StringTools.isNotBlank(req.getStatus())) {
            whereSQL.and("status=?", req.getStatus());
        } else {
            // 默认不查出未开启的版本
            whereSQL.and("status != ?", PplInnerProcessVersionStatusEnum.NEW.getCode());
        }
        whereSQL.addOrderBy("id desc");
        List<PplInnerDeptVersionVO> innerDeptVersionVOs = demandDBHelper.getAll(PplInnerDeptVersionVO.class,
                whereSQL.getSQL(), whereSQL.getParams());

        // 补充审批版本 关联13周产品版本的信息
        completeInnerDeptVersions(innerDeptVersionVOs, Arrays.asList(req.getProduct()));

        return innerDeptVersionVOs;
    }

    // 补充审批版本 关联13周产品版本的信息
    @Override
    public void completeInnerDeptVersions(List<PplInnerDeptVersionVO> innerDeptVersionVOs, List<String> products) {
        List<Long> versionIds = innerDeptVersionVOs.stream().map(PplInnerProcessVersionDO::getId)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(versionIds)) {
            return;
        }

        // 查出审批版本列表对应的 审批节点配置信息
        List<PplInnerProcessVersionSlaDO> firstSlaList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?) and deadline_type = 'ENTER'", versionIds);
        Map<Long, PplInnerProcessVersionSlaDO> versionIdToEnterSla = firstSlaList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getVersionId, v -> v, (v1, v2) -> v1));

        // 查出关联了审批版本 的 13周产品版本分组
        List<PplVersionGroupDO> versionGroupDOList = demandDBHelper.getAll(PplVersionGroupDO.class,
                "where inner_version_id in(?) and product in(?) ", versionIds, products);
        Map<Long, List<PplVersionGroupDO>> versionIdOfGroupMap = versionGroupDOList.stream()
                .collect(Collectors.groupingBy(PplVersionGroupDO::getInnerVersionId));

        // 查出未消费的最新审批版本的item
        List<PplAuditItemQueueDO> waitSyncItemQueueDOList = demandDBHelper.getAll(PplAuditItemQueueDO.class,
                "where status = 0 and version_id in "
                        + "(SELECT max(version_id) from ppl_audit_item_queue where deleted = 0 GROUP BY industry_dept,product)");

        Map<Long, List<PplAuditItemQueueDO>> waitSyncByVersionMap = waitSyncItemQueueDOList.stream()
                .collect(Collectors.groupingBy(PplAuditItemQueueDO::getVersionId));

        for (PplInnerDeptVersionVO version : innerDeptVersionVOs) {

            PplInnerProcessVersionSlaDO slaDO = versionIdToEnterSla.get(version.getId());

            // 设置关联产品版本状态
            // 1、进行中版本（根据第一个节点的截止时间，判断是在 需求沟通 / 审批中）
            if (PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(version.getStatus())) {
                if (slaDO == null || slaDO.getDeadlineTime() == null || slaDO.getDeadlineTime().after(new Date())) {
                    version.setVersionRelateStatus(PplVersionRelateGroupStatusEnum.PRE_SUBMIT.getName());
                } else {
                    version.setVersionRelateStatus(PplVersionRelateGroupStatusEnum.AUDIT.getName());
                }
                continue;
            }

            // 2、已完结版本
            if (PplInnerProcessVersionStatusEnum.DONE.getCode().equals(version.getStatus())) {
                // 2.1、ppl_version_group表有对应审批版本id时，则为 已同步产品，设置其他相关同步信息
                List<PplVersionGroupDO> groupDOList = versionIdOfGroupMap.get(version.getId());
                if (ListUtils.isNotEmpty(groupDOList)) {
                    version.setVersionRelateStatus(PplVersionRelateGroupStatusEnum.SYNC_GROUP.getName());
                    // 设置已关联产品版本相关信息
                    List<PplVersionRelateGroupVO> relateGroupVOList = new ArrayList<>();
                    groupDOList.stream().distinct().forEach(groupDO -> {
                        PplVersionRelateGroupVO relateGroupVO = new PplVersionRelateGroupVO();
                        relateGroupVO.setVersionGroupId(groupDO.getId());
                        relateGroupVO.setGroupVersionCode(groupDO.getVersionCode());
                        relateGroupVO.setVersionGroupStatus(
                                PplVersionGroupStatusEnum.getNameByCode(groupDO.getStatus()));
                        relateGroupVO.setIsExtendLastVersion(
                                groupDO.getIsExtendLastVersion() != null && groupDO.getIsExtendLastVersion());
                        relateGroupVO.setSyncVersionTime(groupDO.getSyncVersionTime() == null ? groupDO.getCreateTime()
                                : groupDO.getSyncVersionTime());
                        relateGroupVO.setProduct(groupDO.getProduct());
                        relateGroupVOList.add(relateGroupVO);
                    });
                    version.setVersionRelateGroupVOList(relateGroupVOList);
                    continue;
                }

                // 2.2、ppl_version_group表无对应审批版本id时，根据 当前版本id 判断是否是queue里最新未消费的版本
                // 若 是则为 待同步产品，无则为 过期未标记
                version.setVersionRelateStatus(ListUtils.isNotEmpty(waitSyncByVersionMap.get(version.getId()))
                        ? PplVersionRelateGroupStatusEnum.WAIT_SYNC_GROUP.getName()
                        : PplVersionRelateGroupStatusEnum.NO_SYNC_GROUP.getName());
            }
        }
    }

    @Override
    @Synchronized(namespace = "ppl-batchDealPplOrderList", throwExceptionIfNotGetLock = true, waitLockMillisecond = 5000, keyScript = "args[0].versionId")
    @Transactional(value = "demandTransactionManager")
    public void batchDealPplOrderList(BatchDealInnerPplOrderReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        PplInnerProcessVersionSlaDO slaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = 'ENTER'", currentVersion.getId());
        if (slaDO == null || slaDO.getDeadlineTime() == null || slaDO.getDeadlineTime().after(new Date())) {
            throw new BizException("架构师录入未到截止时间,无法审批");
        }

        // 含有已预约明细的PPL单，限制不允许被拒绝，只能通过
        if (!req.getApproveResult()) {
            List<PplItemDO> itemAppliedDOs = demandDBHelper.getAll(PplItemDO.class,
                    "where ppl_order in(?) and status = ? ", req.getPplOrderList(),
                    PplItemStatusEnum.APPLIED.getCode());
            if (!CollectionUtils.isEmpty(itemAppliedDOs)) {
                throw new BizException("ppl单" + itemAppliedDOs.stream().map(PplItemDO::getPplOrder).distinct().collect(
                        Collectors.toList()) + "的明细中含有已预约数据，不允许拒绝！");
            }
        }

        //查询出 PplOrder 及 对应Record、RecordItem
        WhereContent whereContent = new WhereContent();
        whereContent.andIn(PplOrderDO::getPplOrder, req.getPplOrderList());
        List<PplOrderDO> allPplOrderDOList = demandDBHelper.getAll(PplOrderDO.class, whereContent.getSql(),
                whereContent.getParams());

        List<PplOrderDO> dealedList = allPplOrderDOList.stream()
                .filter(v -> (!v.getNodeCode().equals(req.getNodeCode()))
                        || (!v.getAuditStatus().equals(PplInnerVersionStatusEnum.WAIT.getCode())))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dealedList)) {
            throw new BizException("当前提交的单据已被处理，请刷新页面");
        }

        // 获取审批人
        String approveUsername =
                Strings.isNotBlank(req.getUsername()) ? req.getUsername() : LoginUtils.getUserNameWithSystem();

        // 获取当前节点和下个节点
        PplInnerProcessVersionSlaDTO currentNode = innerVersionService.getCurrentNode(req.getVersionId(),
                req.getNodeCode());
        String waitAttribute = currentNode.getNextVersionSlaDO() != null ?
                currentNode.getNextVersionSlaDO().getRoleAttribute() : currentNode.getRoleAttribute();

        // 根据各层级 分级处理pplOrder(行业-中心-战区-客户)
        List<List<PplOrderDO>> list = pplOrderListGroupByLevel(waitAttribute, allPplOrderDOList);

        List<PplOrderAuditRecordItemChangeDTO> changeDTOList = new ArrayList<>();
        for (List<PplOrderDO> pplOrderDOList : list) {
            List<String> subPplOrder = ListUtils.transform(pplOrderDOList, PplOrderDO::getPplOrder);
            List<PplOrderAuditRecordVO> pplOrderAuditRecordVos = auditRecordService.queryLatestAuditRecordAndItem(
                    subPplOrder, currentVersion.getId());
            List<Long> recordId = pplOrderAuditRecordVos.stream().map(PplOrderAuditRecordVO::getId)
                    .collect(Collectors.toList());

            // 判断是否需要等待
            Boolean isWait = Strings.isBlank(waitAttribute) ? Boolean.FALSE : Boolean.TRUE;
            String approveResult = req.getApproveResult() ?
                    (isWait ? PplInnerVersionStatusEnum.AUDITED.getCode() : PplInnerVersionStatusEnum.PASS.getCode())
                    : PplInnerVersionStatusEnum.REFUSE.getCode();

            // 更新 auditRecord 信息
            demandDBHelper.executeRaw(
                    "update ppl_order_audit_record set operate_user = ?,"
                            + "approve_time = ?,"
                            + "approve_result=?,"
                            + "approve_note=?,"
                            + "audit_status =? where id in (?)", approveUsername, new Date(), approveResult,
                    req.getApproveNote(),
                    approveResult, recordId);
            if (req.getApproveResult()) {
                // 审批通过
                // 更新PplOrder信息
                demandDBHelper.executeRaw(
                        "update ppl_order set audit_status = ?,current_processor = ? where deleted = 0 and ppl_order in (?)",
                        approveResult, approveUsername, subPplOrder);

                // 校验是否需要等待
                checkIsWaitAndDeal(pplOrderDOList, currentNode, pplOrderAuditRecordVos, isWait, approveUsername,
                        req.getApproveNote());

                // 检查是否数据变更过 若变更过则发邮件推送相应用户
                List<PplOrderAuditRecordItemChangeDTO> changeDTOS = auditRecordService.diffPplItem(
                        req, pplOrderAuditRecordVos, currentVersion.getId());
                if (!CollectionUtils.isEmpty(changeDTOS)) {
                    changeDTOList.addAll(changeDTOS);
                }
            } else {
                checkIsWaitAndDeal(pplOrderDOList, currentNode, pplOrderAuditRecordVos, isWait, approveUsername,
                        req.getApproveNote());
                refuse(pplOrderAuditRecordVos, currentNode, pplOrderDOList, approveUsername, currentVersion,
                        req.getApproveNote());
            }
            for (PplOrderAuditRecordItemChangeDTO changeDTO : changeDTOList) {
                // 异步处理
                executor.execute(() -> {
//                    buildUpdateContent(changeDTO, currentNode.getNodeCode());
                    // 使用企业微信消息提醒
                    auditUpdatePplNotice(changeDTO);
                });
            }
        }
    }

    private void auditUpdatePplNotice(PplOrderAuditRecordItemChangeDTO changeDTO) {
        if (changeDTO == null) {
            return;
        }
        StringJoiner instanceTypes = new StringJoiner(",");
        StringJoiner zoneNames = new StringJoiner(",");
        StringJoiner contents = new StringJoiner(" ; ");
        if (changeDTO.getChangeItemDTOList() != null) {
            for (ChangeItemDTO item : changeDTO.getChangeItemDTOList()) {
                if (item == null) {
                    continue;
                }
                instanceTypes.add(item.getInstanceType());
                zoneNames.add(item.getZoneName());
                contents.add(item.getChangeContent());
            }
        }
        String beforeTotalCoreOrTotalGpuNum;
        String afterTotalCoreOrTotalGpuNum;
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(changeDTO.getProduct())) {
            beforeTotalCoreOrTotalGpuNum = changeDTO.getBeforeGpu() + "卡";
            afterTotalCoreOrTotalGpuNum = changeDTO.getAfterGpu() + "卡";
        } else {
            beforeTotalCoreOrTotalGpuNum = changeDTO.getBeforeCore() + "核";
            afterTotalCoreOrTotalGpuNum = changeDTO.getAfterCore() + "核";
        }
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("customerShortName", changeDTO.getCustomerShortName());
        templateParams.put("product", changeDTO.getProduct());
        templateParams.put("demandYearMonth", changeDTO.getYearMonth());
        templateParams.put("instanceType", instanceTypes);
        templateParams.put("zoneName", zoneNames);
        templateParams.put("beforeTotalCoreOrTotalGpuNum", beforeTotalCoreOrTotalGpuNum);
        templateParams.put("afterTotalCoreOrTotalGpuNum", afterTotalCoreOrTotalGpuNum);
        templateParams.put("modifyReason", changeDTO.getModifyReason() == null ? "" : changeDTO.getModifyReason());
        templateParams.put("modifyContent", contents);
        templateParams.put("operator", changeDTO.getAuditor());
        templateParams.put("pplOrder", changeDTO.getPplOrder());
        dictService.eventNotice(CrpEventEnum.INNER_PROCESS_AUDIT_PPL_MODIFY.getCode(),
                null, null, templateParams, changeDTO.getSendUser());
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void batchAdjustZero(BatchAdjustZeroReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        PplInnerProcessVersionSlaDO slaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = 'ENTER'", currentVersion.getId());
        if (slaDO == null || slaDO.getDeadlineTime() == null || slaDO.getDeadlineTime().after(new Date())) {
            throw new BizException("架构师录入未到截止时间,无法拒绝");
        }

        List<String> pplOrder = req.getReqList().stream().map(UpdatePplOrderItemListReq::getPplOrder)
                .collect(Collectors.toList());

        // 含有已预约明细的PPL单，限制不允许被拒绝，只能通过
        List<PplItemDO> itemAppliedDOs = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_order in(?) and status = ?", pplOrder,
                PplItemStatusEnum.APPLIED.getCode());
        List<String> on = itemAppliedDOs.stream().map(PplItemBaseDO::getYunxiaoOrderId)
                .filter(yunxiaoOrderId -> yunxiaoOrderId.startsWith("OE")).distinct().collect(Collectors.toList());
        List<PplItemDO> yunxiao = itemAppliedDOs.stream().filter(v ->
                        (v.getYunxiaoOrderId().startsWith("order") || v.getYunxiaoOrderId().startsWith("OE")))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(yunxiao)) {
            throw new BizException("ppl单" + yunxiao.stream().map(PplItemDO::getPplOrder).distinct().collect(
                    Collectors.toList()) + "的明细中含有迁移订单/云霄预约单的数据，不允许拒绝！");
        }

        // 记录日志
        List<String> pplOrderList = ListUtils.transform(req.getReqList(), UpdatePplOrderItemListReq::getPplOrder);
        List<PplOrderAuditRecordVO> pplOrderAuditRecordVos = auditRecordService.queryLatestAuditRecordAndItem(
                pplOrderList, currentVersion.getId());
        recordAuditRefuse(pplOrderAuditRecordVos);

        UpdatePplOrderItemListReq firstReq = req.getReqList().get(0);
        List<Long> recordIds = req.getReqList().stream().map(UpdatePplOrderItemListReq::getRecordId)
                .collect(Collectors.toList());
        String userName = LoginUtils.getUserName();
        String modifyReason = userName + " : " + firstReq.getModifyReason() + "<br />";

        // 更新所有item数据
        demandDBHelper.executeRaw(
                "update ppl_order_audit_record_item set instance_num = 0,total_disk = 0,total_core = 0,total_gpu_num = 0 "
                        + "where deleted = 0 and audit_record_id in (?)", recordIds);
        // 更新所有record记录
        demandDBHelper.executeRaw(
                "update ppl_order_audit_record set modify_reason = ? where deleted = 0 and id in (?)", modifyReason,
                recordIds);

        demandDBHelper.executeRaw(
                "update ppl_order set change_type = ? where deleted = 0 and ppl_order in (?)",
                OperateTypeEnum.UPDATE.getCode(), pplOrder);

        BatchDealInnerPplOrderReq batchDealInnerPplOrderReq = new BatchDealInnerPplOrderReq();
        batchDealInnerPplOrderReq.setApproveNote(firstReq.getModifyReason());
        batchDealInnerPplOrderReq.setPplOrderList(pplOrder);
        batchDealInnerPplOrderReq.setNodeCode(req.getNodeCode());
        batchDealInnerPplOrderReq.setIndustryDept(firstReq.getIndustryDept());
        batchDealInnerPplOrderReq.setApproveResult(Boolean.TRUE);
        batchDealInnerPplOrderReq.setVersionId(req.getVersionId());
        PplInnerProcessService innerProcessService = SpringUtil.getBean(PplInnerProcessService.class);
        innerProcessService.batchDealPplOrderList(batchDealInnerPplOrderReq);

        for (String s : on) {
            industryProcessService.cancelPplTransformOrder(s);
        }

    }

    private List<List<PplOrderDO>> pplOrderListGroupByLevel(String waitAttribute,
            List<PplOrderDO> allPplOrderDOList) {
        List<List<PplOrderDO>> list = new ArrayList<>();

        if (waitAttribute.equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
            list = new ArrayList<>(allPplOrderDOList.stream()
                    .collect(Collectors.groupingBy(PplOrderDO::getCustomerShortName)).values());
        } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
            list = new ArrayList<>(allPplOrderDOList.stream()
                    .collect(Collectors.groupingBy(PplOrderDO::getWarZone)).values());
        } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.CENTER.getCode())) {
            list = new ArrayList<>(allPplOrderDOList.stream()
                    .collect(Collectors.groupingBy(PplOrderDO::getCenter)).values());
        } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.DEPT.getCode())) {
            list = new ArrayList<>(allPplOrderDOList.stream()
                    .collect(Collectors.groupingBy(PplOrderDO::getIndustryDept)).values());
        }
        return list;
    }


    public void refuse(List<PplOrderAuditRecordVO> pplOrderAuditRecordVos, PplInnerProcessVersionSlaDTO currentNode,
            List<PplOrderDO> pplOrderDOList, String approveUserName, PplInnerProcessVersionDO currentVersion,
            String refuseNote) {
        String normalRefuseNote = refuseNote;
        // 过滤出非取消的ppl单（取消的PPL单不能进行拒绝操作）
        List<String> noDeletedPplOrders = pplOrderDOList.stream()
                .filter(e -> !OperateTypeEnum.DELETED.getCode().equals(e.getChangeType())).map(PplOrderDO::getPplOrder)
                .collect(Collectors.toList());
        pplOrderAuditRecordVos = pplOrderAuditRecordVos.stream()
                .filter(e -> noDeletedPplOrders.contains(e.getPplOrder())).collect(
                        Collectors.toList());

        HashMap<String, Object> map = new HashMap<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String deadlineTime = formatter.format(new Date());
        map.put("refuseUser", approveUserName);
        map.put("refuseTime", deadlineTime);
        map.put("refuseNote", refuseNote);
        refuseNote = Json.toJson(map);
        // 更新pplOrder
        for (PplOrderAuditRecordVO pplOrderAuditRecordVo : pplOrderAuditRecordVos) {
            String pplOrder = pplOrderAuditRecordVo.getPplOrder();
            PplOrderDO pplOrderDO = pplOrderAuditRecordVo.getPplOrderDO();
            List<PplOrderAuditRecordItemVO> itemVOList = pplOrderAuditRecordVo.getItemVOList();

            // 将草稿还原 --
//            demandDBHelper.executeRaw(
//                    "update ppl_order_draft set deleted = 0,modify_reason = ?, draft_status = ? where ppl_order =? and draft_status = ?",
//                    Json.toJson(map), PplOrderDraftStatusEnum.DRAFT.getCode(), pplOrder,
//                    PplOrderDraftStatusEnum.SUBMITTED.getCode());
//            demandDBHelper.executeRaw("update ppl_item_draft set deleted=0 where ppl_order =? ", pplOrder);
            // 5-12 需将ppl-Item和pplOrder修改的 转换draft返回 并添加refuseNote
            SavePplDraftReq savePplDraftReq = transToDraftReq(pplOrderDO, pplOrderAuditRecordVo.getItemVOList());
            savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.DRAFT.getCode());
            savePplDraftReq.setIsRecord(Boolean.FALSE);
            pplDraftService.saveDraft(savePplDraftReq);
            demandDBHelper.executeRaw(
                    "update ppl_order_draft set modify_reason = ? where ppl_order =? and deleted = 0",
                    refuseNote, pplOrder);

            // 拒绝代表ppl单流程已结束；nodeCode 和 auditStatus清空
            pplOrderDO.setNodeCode("");
            pplOrderDO.setCurrentProcessor("");
            pplOrderDO.setAuditStatus("");
            pplOrderDO.setCurrentRole("");
            demandDBHelper.update(pplOrderDO);

            // 更新所有审批记录中item的信息 所有item的 实例数量 核数 卡数 调整为0
            if (!CollectionUtils.isEmpty(itemVOList)) {
                List<Long> recordItemIdList = itemVOList.stream().map(PplOrderAuditRecordItemDO::getId)
                        .collect(Collectors.toList());
                demandDBHelper.executeRaw("update ppl_order_audit_record_item" +
                                " set instance_num = 0,total_core = 0,total_disk = 0,total_gpu_num = 0 where id in (?)"
                        , recordItemIdList);
            }

//            List<PplOrderAuditRecordVO> pplOrderAuditRecordVOS = auditRecordService.queryHistoryAuditRecord(
//                    Collections.singletonList(pplOrderDO.getPplOrder()), currentVersion.getId());
//            if (CollectionUtils.isEmpty(pplOrderAuditRecordVOS)) {
//                return;
//            }
//            Set<String> operateUserList = pplOrderAuditRecordVOS.stream()
//                    .filter(v -> v.getOperateUser() != null)
//                    .map(PplOrderAuditRecordVO::getOperateUser).collect(Collectors.toSet());
//            String join = Strings.join(operateUserList, ';');
//            sendMail(join, approveUserName, "REFUSE", pplOrderDO.getNodeCode(), currentVersion.getId(),
//                    Collections.singletonList(pplOrderDO.getPplOrder()), null);
            // 企业微信消息提醒
            refuseNotice(pplOrderAuditRecordVo, normalRefuseNote, approveUserName);

            // 记录日志
            recordAuditRefuse(pplOrderAuditRecordVos);
        }

        // 记录日志
        recordAuditRefuse(pplOrderAuditRecordVos);
    }

    private void refuseNotice(PplOrderAuditRecordVO ppl, String refuseNote,
            String approveUserName) {
        StringJoiner instanceTypes = new StringJoiner(",");
        StringJoiner zoneNames = new StringJoiner(",");
        Set<String> products = new HashSet<>();
        BigDecimal totalCore = BigDecimal.ZERO;
        BigDecimal totalGpuNum = BigDecimal.ZERO;
        Set<String> demandYearMonths = new HashSet<>();
        if (ppl.getItemVOList() != null) {
            for (PplOrderAuditRecordItemVO item : ppl.getItemVOList()) {
                if (item == null) {
                    continue;
                }
                if (Strings.isNotBlank(item.getProduct())) {
                    products.add(item.getProduct());
                }
                instanceTypes.add(item.getInstanceType());
                zoneNames.add(item.getZoneName());
                if (Ppl13weekProductTypeEnum.GPU.getName().equals(item.getProduct())) {
                    totalGpuNum = NumberUtil.add(item.getTotalGpuNum());
                } else {
                    totalCore = NumberUtil.add(item.getTotalCore());
                }
                if (item.getBeginBuyDate() != null) {
                    String yearMonth = DateTimeFormatter.ofPattern("yyyy年MM月").format(item.getBeginBuyDate());
                    demandYearMonths.add(yearMonth);
                }
            }
        }
        StringJoiner totalCoreOrTotalGpuNum = new StringJoiner(",");
        if (!BigDecimal.ZERO.equals(totalCore)) {
            totalCoreOrTotalGpuNum.add(totalCore.toString() + "核");
        }
        if (!BigDecimal.ZERO.equals(totalGpuNum)) {
            totalCoreOrTotalGpuNum.add(totalGpuNum.toString() + "卡");
        }
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("customerShortName", ppl.getPplOrderDO().getCustomerShortName());
        templateParams.put("product", Strings.join(products, ','));
        templateParams.put("demandYearMonth", Strings.join(demandYearMonths, ','));
        templateParams.put("instanceType", instanceTypes);
        templateParams.put("zoneName", zoneNames);
        templateParams.put("totalCoreOrTotalGpuNum", totalCoreOrTotalGpuNum.toString());
        templateParams.put("refuseNote", refuseNote);
        templateParams.put("operator", approveUserName);
        templateParams.put("pplOrder", ppl.getPplOrder());
        dictService.eventNotice(CrpEventEnum.INNER_PROCESS_AUDIT_PPL_REFUSE.getCode(),
                null, null, templateParams, ppl.getPplOrderDO().getSubmitUser());
    }

    // 记录 审批拒绝 日志
    private void recordAuditRefuse(List<PplOrderAuditRecordVO> pplOrderAuditRecordVos) {
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        pplOrderAuditRecordVos.forEach(auditDO -> auditDO.getItemVOList().forEach(auditItemDO -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(auditDO.getPplOrderDO(), beforeAllFieldDTO);
            BeanUtils.copyProperties(auditItemDO, beforeAllFieldDTO);
            beforeAllFieldDTO.setStatus(Strings.isBlank(auditItemDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                    : auditItemDO.getStatus());
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(auditDO.getPplOrderDO(), afterAllFieldDTO);
            BeanUtils.copyProperties(auditItemDO, afterAllFieldDTO);
            afterAllFieldDTO.setInstanceNum(0);
            afterAllFieldDTO.setTotalCore(0);
            afterAllFieldDTO.setTotalGpuNum(BigDecimal.valueOf(0));
            afterAllFieldDTO.setTotalDisk(0);
            afterAllFieldDTO.setStatus(Strings.isBlank(auditItemDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                    : auditItemDO.getStatus());
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            newDTOs.add(recordNewDTO);
        }));
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PPL_AUDIT_REJECT.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));
    }

    // 驳回待处理  暂时弃用
    public void rejectModify(List<PplOrderAuditRecordVO> pplOrderAuditRecordVos,
            PplInnerProcessVersionSlaDTO currentNode,
            List<PplOrderDO> pplOrderDOList, String approveUserName, PplInnerProcessVersionDO currentVersion) {
        List<String> pplOrderList = ListUtils.transform(pplOrderDOList, PplOrderDO::getPplOrder);
        // 审批驳回
        Map<String, String> lastProcessorMap = auditRecordService.getLastProcessorMap(pplOrderList);
        // 插入新的auditRecord 和 Item
        auditRecordService.addNewAuditRecordAndItemByOldRecord(pplOrderAuditRecordVos, currentNode,
                PplOrderAuditStatusEnum.REJECT_MODIFY.getCode());

        // 更新PplOrder信息
        for (PplOrderDO pplOrderDO : pplOrderDOList) {
            pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.REJECT_MODIFY.getCode());
            pplOrderDO.setCurrentProcessor(approveUserName);
            String rejectProcessor = lastProcessorMap.get(pplOrderDO.getPplOrder());
            if (rejectProcessor == null) {
                throw new BizException("PplOrder:" + pplOrderDO.getPplOrder() + " 未找到上一个处理单据的人,无法驳回");
            }
            pplOrderDO.setRejectProcessor(rejectProcessor);
            demandDBHelper.update(pplOrderDO);
            sendMail(rejectProcessor, approveUserName, "REJECT_MODIFY", pplOrderDO.getNodeCode(),
                    currentVersion.getId(),
                    Collections.singletonList(pplOrderDO.getPplOrder()), null);
        }
    }

    @Override
    public List<PplOrderAuditRecordItemDO> updateRecordItemList(UpdatePplOrderItemListReq req) {

        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        PplInnerProcessVersionSlaDO slaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = 'ENTER'", currentVersion.getId());
        if (slaDO == null || slaDO.getDeadlineTime() == null || slaDO.getDeadlineTime().after(new Date())) {
            throw new BizException("架构师录入未到截止时间,无法干预");
        }

        // 若审批单的明细为已预约数据，则不允许编辑此审批单明细
        Map<String, PplOrderAuditRecordItemDO> itemAppliedMap = new HashMap<>();
        if (StringUtils.isNotBlank(req.getPplOrder())) {
            List<String> appliedPplIds = demandDBHelper.getAll(PplItemDO.class, "where ppl_order = ? and status = ? ",
                    req.getPplOrder(), PplItemStatusEnum.APPLIED.getCode()).stream().map(PplItemDO::getPplId).collect(
                    Collectors.toList());
            if (ListUtils.isNotEmpty(appliedPplIds)) {
                List<PplOrderAuditRecordItemDO> oldItemDOList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                        "where audit_record_id = ? and ppl_id in(?)", req.getRecordId(), appliedPplIds);
                itemAppliedMap = oldItemDOList.stream().collect(
                        Collectors.toMap(PplOrderAuditRecordItemDO::getPplId, Function.identity(), (v1, v2) -> v1));
                log.info("updateRecordItemList, 行业-{}, 审批修改时忽略已预约明细pplIds的改动-{}",
                        req.getIndustryDept(),
                        appliedPplIds);
            }
        }
        Map<String, PplOrderAuditRecordItemDO> finalItemAppliedMap = itemAppliedMap;

        Set<String> databaseSet = new HashSet<>();
        List<String> errors = new ArrayList<>();
        boolean isDatabase = ListUtils.isNotEmpty(req.getItemVOList())
                && Ppl13weekProductTypeEnum.DATABASE.getName().equals(req.getItemVOList().get(0).getProduct());
        List<PplOrderAuditRecordItemDO> itemDOList = req.getItemVOList().stream().map(v -> {
            // EMR、ES、云数仓才能使用 大数据云盘、高IO型云硬盘
            Tuple2<Boolean, String> res = PplDiskTypeEnum.checkDataDiskForPpl(v.getDataDiskType(), v.getProduct());
            if (!res._1) {
                throw new BizException(res._2);
            }
            if (isDatabase) {
                databaseSet.add(v.getDatabaseName());
                v.paramsCheckDataBase(errors);
            }

            PplOrderAuditRecordItemDO pplOrderAuditRecordItemDO = null;
            // 已预约明细pplIds的改动 以表里的值为准（即如果前端没限制住，后端也仍旧会忽略已预约明细的改动）
            if (Strings.isNotBlank(v.getPplId()) && finalItemAppliedMap.get(v.getPplId()) != null) {
                pplOrderAuditRecordItemDO = BeanUtil.copyProperties(finalItemAppliedMap.get(v.getPplId()),
                        PplOrderAuditRecordItemDO.class);
            } else {
                // 未预约明细pplIds的改动 以前端传递为准
                pplOrderAuditRecordItemDO = BeanUtil.copyProperties(v,
                        PplOrderAuditRecordItemDO.class);
            }

            pplOrderAuditRecordItemDO.setId(null);
            pplOrderAuditRecordItemDO.setCreateTime(null);
            pplOrderAuditRecordItemDO.setUpdateTime(null);
            pplOrderAuditRecordItemDO.setPplOrder(req.getPplOrder());
            pplOrderAuditRecordItemDO.setAuditRecordId(req.getRecordId());
            if (Strings.isBlank(v.getPplId())) {
                String pplId = pplCommonService.generatePplItemId(req.getPplOrder());
                pplOrderAuditRecordItemDO.setPplId(pplId);
            }
            return pplOrderAuditRecordItemDO;
        }).collect(Collectors.toList());
        if (databaseSet.size() > 1) {
            throw new BizException("单个pplOrder仅支持录入单个数据库产品");
        }
        if (ListUtils.isNotEmpty(errors)) {
            throw new BizException(String.join(",", errors));
        }
        // 逻辑删除掉原来的
        demandDBHelper.executeRaw("update ppl_order_audit_record_item set deleted = 1 where audit_record_id = ?",
                req.getRecordId());
        // 插入更新后的
        demandDBHelper.insertBatchWithoutReturnId(itemDOList);

        // 更新pplOrder
        PplOrderDO one = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order = ?", req.getPplOrder());
        if (one.getChangeType().equals(OperateTypeEnum.ORIGINAL.getCode())) {
            one.setChangeType(OperateTypeEnum.UPDATE.getCode());
        }
        demandDBHelper.update(one);

        return itemDOList;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void modifyPplItem(UpdatePplOrderItemListReq req) {
        String userName = LoginUtils.getUserName();
        List<PplOrderAuditRecordItemDO> oldItemDOList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                "where audit_record_id = ? ", req.getRecordId());

        List<PplOrderAuditRecordItemDO> updateItemDOList = updateRecordItemList(req);
        // 更新ppl_order_audit_record
        PplOrderAuditRecordDO one = demandDBHelper.getOne(PplOrderAuditRecordDO.class, "where id = ?",
                req.getRecordId());
//        String modifyReason = userName + " : " + req.getModifyReason() + "<br />";
//        one.setModifyReason(
//                Strings.isBlank(one.getModifyReason()) ? modifyReason : one.getModifyReason() + modifyReason);
        one.setModifyReason(req.getModifyReason());
        demandDBHelper.update(one);

        // 记录日志
        recordAuditLog(one, oldItemDOList, updateItemDOList);
    }

    private void recordAuditLog(PplOrderAuditRecordDO auditRecordDO, List<PplOrderAuditRecordItemDO> oldItemDOList,
            List<PplOrderAuditRecordItemDO> updateItemDOList) {
        Map<String, PplOrderAuditRecordItemDO> historyItemDOMap = oldItemDOList.stream()
                .collect(Collectors.toMap(PplOrderAuditRecordItemDO::getPplId, Function.identity(), (o1, o2) -> o1));
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        List<PplItemChangeRecordNewDTO> updateDTOs = new ArrayList<>();
        updateItemDOList.forEach(itemAudit -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplOrderAuditRecordItemDO historyItemAudit = historyItemDOMap.get(itemAudit.getPplId());
            if (historyItemAudit == null) {
                PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(auditRecordDO, afterAllFieldDTO);
                BeanUtils.copyProperties(itemAudit, afterAllFieldDTO);
                afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
                recordNewDTO.setAfterItem(afterAllFieldDTO);
                recordNewDTO.setOperateNote(auditRecordDO.getModifyReason());
                newDTOs.add(recordNewDTO);
            } else {
                PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(auditRecordDO, beforeAllFieldDTO);
                BeanUtils.copyProperties(historyItemAudit, beforeAllFieldDTO);
                beforeAllFieldDTO.setStatus(
                        Strings.isBlank(historyItemAudit.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                : historyItemAudit.getStatus());
                recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(auditRecordDO, afterAllFieldDTO);
                BeanUtils.copyProperties(itemAudit, afterAllFieldDTO);
                afterAllFieldDTO.setStatus(Strings.isBlank(itemAudit.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                        : itemAudit.getStatus());
                recordNewDTO.setOperateNote(auditRecordDO.getModifyReason());
                recordNewDTO.setAfterItem(afterAllFieldDTO);
                updateDTOs.add(recordNewDTO);
            }
        });
        if (!CollectionUtils.isEmpty(newDTOs)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_AUDIT_ADD.getCode(), LoginUtils.getUserNameWithSystem(),
                            newDTOs));
        }
        if (!CollectionUtils.isEmpty(updateDTOs)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_AUDIT_EDIT.getCode(), LoginUtils.getUserNameWithSystem(),
                            updateDTOs));
        }
    }

    /**
     * 校验是否需要等待
     * 无需等待的 需生成新的auditRecord 和 RecordItem
     * 需等待的 仅需改状态， 且改完状态后 判断当前是否还存在其他未处理的， 如果没有，则新增auditRecord和recordItem
     *
     * @param pplOrderDOList
     * @param currentNode
     * @param pplOrderAuditRecordVos
     * @param isWait
     */
    void checkIsWaitAndDeal(List<PplOrderDO> pplOrderDOList, PplInnerProcessVersionSlaDTO currentNode,
            List<PplOrderAuditRecordVO> pplOrderAuditRecordVos, Boolean isWait, String userNameWithSystem,
            String approveNote) {
        Long versionId = pplOrderAuditRecordVos.get(0).getVersionId();
        String waitAttribute = currentNode.getNextVersionSlaDO() != null ?
                currentNode.getNextVersionSlaDO().getRoleAttribute() : currentNode.getRoleAttribute();
        PplInnerProcessVersionSlaDTO nextNode = currentNode.getNextVersionSlaDO();
        String industryDept = pplOrderDOList.get(0).getIndustryDept();
        // 检查是否当前节点都扭转完;
        if (isWait) {
            Boolean allFinish = Boolean.FALSE;
            List<PplOrderAuditRecordVO> nodeAllRecord = new ArrayList<>();
            // 查出各层级的所有Order
            if (waitAttribute.equals(PplInnerProcessAttributeEnum.DEPT.getCode())) {
                nodeAllRecord = auditRecordService.queryVersionPplOrder(versionId,
                        industryDept, null, null, null, null);
            } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.CENTER.getCode())) {
                String center = pplOrderDOList.get(0).getCenter();
                nodeAllRecord = auditRecordService.queryVersionPplOrder(versionId,
                        industryDept, center, null, null, null);
            } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
                String warZone = pplOrderDOList.get(0).getWarZone();
                nodeAllRecord = auditRecordService.queryVersionPplOrder(versionId,
                        industryDept, null, warZone, null, null);
            } else if (waitAttribute.equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
                String customerShortName = pplOrderDOList.get(0).getCustomerShortName();
                nodeAllRecord = auditRecordService.queryVersionPplOrder(versionId,
                        industryDept, null, null, customerShortName, null);
            }
            List<PplOrderDO> allPplOrderDOList = nodeAllRecord.stream().map(PplOrderAuditRecordVO::getPplOrderDO)
                    .collect(Collectors.toList());
            List<Long> nodeAllRecordId = nodeAllRecord.stream().map(PplOrderAuditRecordVO::getId)
                    .collect(Collectors.toList());
            // 还不是已审批状态的pplOrder
            List<PplOrderDO> notAuditedList = allPplOrderDOList.stream()
                    .filter(v -> !v.getAuditStatus().equals(PplOrderAuditStatusEnum.AUDITED.getCode())).collect(
                            Collectors.toList());
            Map<String, List<PplOrderDO>> auditStatusMap = allPplOrderDOList.stream()
                    .collect(
                            Collectors.groupingBy(v -> StringTools.join("@", v.getNodeCode(), v.getAuditStatus())));

            if (auditStatusMap.size() == 1 && CollectionUtils.isEmpty(notAuditedList)) {
                allFinish = Boolean.TRUE;
                // 更新auditStatus 为PASS
                demandDBHelper.executeRaw(
                        "update ppl_order_audit_record set audit_status = ? where id in (?)",
                        PplOrderAuditStatusEnum.PASS.getCode(), nodeAllRecordId);
                pplOrderAuditRecordVos = nodeAllRecord;
                pplOrderDOList = allPplOrderDOList;
            }
            if (!allFinish) {
                //不再往下执行
                return;
            }

        }

        PplInnerProcessService innerProcessService = SpringUtil.getBean(PplInnerProcessService.class);

        if (currentNode.getNextSlaId() != null && currentNode.getNextSlaId() != 0) {
            // 如果不是最后一个节点

            // 查询出该节点历史审批人
            Set<String> nodeHistoryUser = pplOrderAuditRecordVos.stream().map(PplOrderAuditRecordVO::getOperateUser)
                    .collect(Collectors.toSet());

            // 插入新的auditRecord 和 Item
            auditRecordService.addNewAuditRecordAndItemByOldRecord(pplOrderAuditRecordVos, nextNode,
                    PplOrderAuditStatusEnum.WAIT.getCode());

            String nodeApproveUser = getNodeApproveUser(nextNode, pplOrderDOList.get(0));

            // 更新pplOrder
            List<String> pplOrderList = ListUtils.transform(pplOrderDOList, PplOrderDO::getPplOrder);
            demandDBHelper.executeRaw("update ppl_order set audit_status = ?,current_processor = ?,current_role = ?,"
                            + " node_code = ? where deleted = 0 and ppl_order in (?)",
                    PplOrderAuditStatusEnum.WAIT.getCode(), nodeApproveUser,
                    nextNode.getApproveRole(), nextNode.getNodeCode(), pplOrderList);

//            sendMail(nodeApproveUser, null, "AUDIT", nextNode.getNodeCode(), versionId, pplOrderList, null);
            // 替换为企业微信消息提醒
            waitAuditNotice(nodeApproveUser, versionId, pplOrderList, nextNode.getNodeCode());

            String warZone = null;
            String customer = null;
            if (nextNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
                warZone = pplOrderDOList.get(0).getWarZone();
            } else if (nextNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
                warZone = pplOrderDOList.get(0).getWarZone();
                customer = pplOrderDOList.get(0).getCustomerShortName();
            }
            innerProcessService.sendTodoAndMoa(nodeApproveUser, nextNode, versionId, industryDept, warZone, customer,
                    approveNote);
            // 校验是否开启上下审批人相同自动过单， 并检查是否满足条件
            PplInnerProcessVersionDO versionDO = demandDBHelper.getByKey(PplInnerProcessVersionDO.class, versionId);
            PplInnerProcessDO processDO = demandDBHelper.getByKey(PplInnerProcessDO.class, versionDO.getProcessId());
            // 如果
            // 1、process不为空
            // 2、开启审批人相同自动过单
            // 3、审批人不是 kaijiazhang 或 system
            // 4、节点历史审批人需要只有一个 并且等于当前审批人
            // 则进行下一步检查
            if (processDO != null && processDO.getSameApproveAutoPass().equals(Boolean.TRUE)
                    && !userNameWithSystem.equals("kaijiazhang") && !userNameWithSystem.equals("system")
                    && nodeHistoryUser.size() == 1 && nodeHistoryUser.contains(userNameWithSystem)) {
                PplInnerProcessVersionSlaDTO nextNodeSla = currentNode.getNextVersionSlaDO();
                if (!nextNodeSla.getRoleAttribute().equals(PplInnerProcessAttributeEnum.DEPT.getCode())
                        && nodeApproveUser.contains(userNameWithSystem)) {
                    // 5、下节点数据权限不为行业级别时才自动跳过
                    // 6、当前审批人也包含在下次审批人里面
                    // 发起自动过单请求
                    BatchDealInnerPplOrderReq req = new BatchDealInnerPplOrderReq();
                    req.setIndustryDept(nextNodeSla.getIndustryDept());
                    req.setNodeCode(nextNodeSla.getNodeCode());
                    req.setPplOrderList(pplOrderList);
                    req.setApproveResult(Boolean.TRUE);
                    req.setApproveNote("上下两节点均为同一审批人，自动跳转");
                    req.setVersionId(versionId);
                    innerProcessService.batchDealPplOrderList(req);
                }

            }

        } else {
            // 如果是最后一个节点 且全部已经处理完
            // 将版本内所有PPL_ORDER的各种状态清空
            List<String> pplOrderList = ListUtils.transform(pplOrderDOList, PplOrderDO::getPplOrder);
            demandDBHelper.executeRaw("update ppl_order set audit_status = ?,current_processor = ?,current_role = ?,"
                            + " node_code = ? where deleted = 0 and ppl_order in (?)",
                    "", "", "", "", pplOrderList);
//            // 将新增的拒绝单据全部删除； 将调整的拒绝单据全部去除auditStatus；
//            demandDBHelper.executeRaw("update ppl_order set deleted = 1 where audit_status = ? and change_type = ?",
//                    PplInnerVersionStatusEnum.REFUSE.getCode(), OperateTypeEnum.INSERT.getCode());
//            demandDBHelper.executeRaw(
//                    "update ppl_order set deleted = 1,audit_status = '' where audit_status = ? and change_type = ?",
//                    PplInnerVersionStatusEnum.REFUSE.getCode(), OperateTypeEnum.UPDATE.getCode());

            // 处理邮件
            List<PplOrderAuditRecordVO> allRecord = auditRecordService.queryHistoryAuditRecord(null, versionId);
            Set<String> operateUserList = allRecord.stream().filter(v -> v.getOperateUser() != null)
                    .map(PplOrderAuditRecordVO::getOperateUser).collect(Collectors.toSet());
            String join = Strings.join(operateUserList, ';');

            //完成旧版本，开启新版本
            innerVersionService.autoInitNewVersion(industryDept, versionId);

//            sendMail(join, null, "FINISH", null, versionId, null, null);
            finishVersionNotice(versionId, join);

            // mq触发同步 版本数据转换为已生效，下发至13周; 并将新版本需求年月范围已生效数据移至新版本的需求沟通中
            rabbitTemplate.convertAndSend("erp.topic", QueueDefinition.SYNC_INNER_PROCESS_ITEM_ROUTING_KEY, versionId);

        }

    }

    private void finishVersionNotice(Long versionId, String noticeUsers) {
        // 当前已完结的版本
        PplInnerProcessVersionDO one = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where id = ? and status = ? ", versionId, PplInnerProcessVersionStatusEnum.DONE.getCode());
        if (one == null) {
            return;
        }
        // 查询下一个版本
        PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ?  and id > ? and status = ? ",
                one.getProcessId(), versionId, PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        if (nextVersion == null) {
            return;
        }
        // 录入截止时间
        PplInnerProcessVersionSlaDO versionSlaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and node_code = ? ",
                nextVersion.getId(), PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
        String deadlineTime = "";
        if (versionSlaDO != null && versionSlaDO.getDeadlineTime() != null) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
            deadlineTime = formatter.format(versionSlaDO.getDeadlineTime());
        }

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("product", nextVersion.getProduct());
        templateParams.put("deadlineTime", deadlineTime);
        templateParams.put("demandBeginYear", nextVersion.getDemandBeginYear());
        templateParams.put("demandBeginMonth", nextVersion.getDemandBeginMonth());
        templateParams.put("demandEndYear", nextVersion.getDemandEndYear());
        templateParams.put("demandEndMonth", nextVersion.getDemandEndMonth());

        dictService.eventNotice(CrpEventEnum.INNER_PROCESS_VERSION_FINISH.getCode(),
                null, null, templateParams, noticeUsers);
    }

    void waitAuditNotice(String nodeApproveUser, Long versionId, List<String> orders, String nodeCode) {
        if (versionId == null || ListUtils.isEmpty(orders)) {
            return;
        }
        List<PplInnerProcessVersionSlaDO> versionSlas =
                demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                        "where version_id=? and deadline_type='EXIT'", versionId);
        Map<String, PplInnerProcessVersionSlaDO> versionSlaMap = ListUtils.toMap(versionSlas,
                PplInnerProcessVersionSlaDO::getNodeCode,
                o -> o);

        List<PplOrderAuditRecordVO> records = auditRecordService.queryLatestAuditRecordAndItem(orders, versionId);
        Map<String, Set<PplOrderAuditRecordVO>> productMap = new HashMap<>();
        for (PplOrderAuditRecordVO record : records) {
            if (record == null || ListUtils.isEmpty(record.getItemVOList())) {
                continue;
            }
            for (PplOrderAuditRecordItemVO item : record.getItemVOList()) {
                if (item == null) {
                    continue;
                }
                Set<PplOrderAuditRecordVO> set = productMap.computeIfAbsent(item.getProduct(), k -> new HashSet<>());
                set.add(record);
            }
        }

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日 HH:mm:ss");
        String deadlineTime = (versionSlaMap.get(nodeCode) == null
                || versionSlaMap.get(nodeCode).getDeadlineTime() == null)
                ? "无" : formatter.format(versionSlaMap.get(nodeCode).getDeadlineTime());

        String format = "【{}】\n"
                + "未审批 {} 条\n"
                + "截止时间 {}\n";
        StringJoiner content = new StringJoiner("\n");
        productMap.forEach((product, recodeSet) -> {
            String info = StrUtil.format(format, product, CollUtil.size(recodeSet), deadlineTime);
            content.add(info);
        });

        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("size", CollUtil.size(orders));
        templateParams.put("productContent", content.toString());
        dictService.eventNotice(CrpEventEnum.INNER_PROCESS_ENTER_WAIT_AUDIT_NOTICE.getCode(),
                null, null, templateParams, nodeApproveUser);
    }

    public String getNodeApproveUser(PplInnerProcessVersionSlaDO nodeDO, PplOrderDO pplOrderDO) {
        String industry = null;
        String center = null;
        String warZone = null;
        String customer = null;
        if (nodeDO.getNodeCode().equals("PRE_SUBMIT")) {
            // 需求沟通直接返回
            return "";
        }
        if (nodeDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.DEPT.getCode())) {
            industry = pplOrderDO.getIndustryDept();
        } else if (nodeDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CENTER.getCode())) {
            industry = pplOrderDO.getIndustryDept();
            center = pplOrderDO.getCenter();
        } else if (nodeDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
            industry = pplOrderDO.getIndustryDept();
            warZone = pplOrderDO.getWarZone();
        } else if (nodeDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
            industry = pplOrderDO.getIndustryDept();
            customer = pplOrderDO.getCustomerShortName();
        }
        return pplCommonService.getApproveUser(nodeDO.getApproveRole(), industry, center, warZone, customer);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void changePplOrderInnerProcess(ChangePplOrderInnerStatusReq req) {
        //查出当前节点所有未处理的pplOrder
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        PplInnerProcessVersionSlaDTO currentNode = innerVersionService.getCurrentNode(req.getVersionId(),
                req.getNodeCode());
//        demandDBHelper.getOne(PplInnerProcessNodeDO.class, "where node_code = ?")
        List<PplOrderDO> all = demandDBHelper.getAll(PplOrderDO.class,
                "where node_code = ? and industry_dept = ? and audit_status = ?"
                , req.getNodeCode(), req.getIndustryDept(), PplOrderAuditStatusEnum.WAIT.getCode());
        if (CollectionUtils.isEmpty(all)) {
            return;
        }
        if (CollectionUtils.isEmpty(all)) {
            throw new BizException(
                    "当前没有" + req.getIndustryDept() + "的nodeCode为" + req.getNodeCode() + "的待处理单据");
        }
        List<String> pplOrderList = ListUtils.transform(all, PplOrderDO::getPplOrder);
        List<PplOrderAuditRecordVO> pplOrderAuditRecordVos = auditRecordService.queryLatestAuditRecordAndItem(
                pplOrderList, currentVersion.getId());
        List<Long> recordId = ListUtils.transform(pplOrderAuditRecordVos, PplOrderAuditRecordVO::getId);
        // 更新 auditRecord 信息
        demandDBHelper.executeRaw(
                "update ppl_order_audit_record set operate_user = ?,"
                        + "approve_time = ?,"
                        + "approve_result=?,"
                        + "approve_note=?,"
                        + "audit_status =? where id in (?)", "system", new Date(),
                PplOrderAuditStatusEnum.REFUSE.getCode(),
                "审批超时,单据自动驳回",
                PplOrderAuditStatusEnum.REFUSE.getCode(), recordId);

        refuse(pplOrderAuditRecordVos, currentNode, all, "system", currentVersion, "审批超时,单据自动驳回");
        checkIsWaitAndDeal(all, currentNode, pplOrderAuditRecordVos, Boolean.TRUE, LoginUtils.getUserNameWithSystem(),
                "审批超时,单据自动驳回");
    }

    @Override
    @Transactional("demandTransactionManager")
    public void refusePreSubmitPplOrderDraft(BatchDealInnerPplOrderReq req) {
        List<String> pplOrders = req.getPplOrderList();

        // 含有已预约明细的PPL单，限制不允许被打回，只能通过
        List<PplItemDO> itemAppliedDOs = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_order in(?) and status = ?", pplOrders,
                PplItemStatusEnum.APPLIED.getCode());
        List<String> on = itemAppliedDOs.stream().map(PplItemBaseDO::getYunxiaoOrderId)
                .filter(yunxiaoOrderId -> yunxiaoOrderId.startsWith("ON")).distinct().collect(Collectors.toList());
        List<PplItemDO> yunxiao = itemAppliedDOs.stream().filter(v -> v.getYunxiaoOrderId().startsWith("order") ||
                        v.getYunxiaoOrderId().startsWith("OE"))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(yunxiao)) {
            throw new BizException("ppl单" + yunxiao.stream().map(PplItemDO::getPplOrder).distinct().collect(
                    Collectors.toList()) + "的明细中含有迁移订单/云霄预约单的数据，不允许打回！");
        }

        List<PplOrderDraftDO> deletedPreSubmitDOs = demandDBHelper.getAll(PplOrderDraftDO.class,
                "where ppl_order in(?) and draft_status = ? and source = ? ", pplOrders,
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), OperateTypeEnum.DELETED.getCode());
        List<String> deletedPplOrders = deletedPreSubmitDOs.stream().map(PplOrderDraftDO::getPplOrder)
                .collect(Collectors.toList());
        pplOrders.removeAll(deletedPplOrders);

        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        HashMap<String, Object> map = new HashMap<>();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String refuseTime = formatter.format(new Date());
        map.put("refuseUser", userNameWithSystem);
        map.put("refuseTime", refuseTime);
        map.put("refuseNote", req.getApproveNote());
        String refuseNote = Json.toJson(map);

        // 打回前的 ppl 信息
        List<PplOrderDraftVO> beforePplList = demandDBHelper.getAll(PplOrderDraftVO.class,
                " where ppl_order in(?) and draft_status = ? ",
                pplOrders, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());

        for (String pplOrder : pplOrders) {
            Long draftOrderId = pplDraftService.withdrawPreSubmitPplOrder(pplOrder, refuseNote);
        }
        List<PplOrderDraftVO> all = demandDBHelper.getAll(PplOrderDraftVO.class,
                "where ppl_order in (?) and draft_status = ?", pplOrders,
                PplOrderDraftStatusEnum.DRAFT.getCode());

        // 打回操作的deadline时间 单独查询。
        PplInnerProcessVersionSlaDO versionSla =
                demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                        "where version_id=? and deadline_type='ENTER'", req.getVersionId());
        String deadlineTime = versionSla == null || versionSla.getDeadlineTime() == null
                ? "" : formatter.format(versionSla.getDeadlineTime());
        for (PplOrderDraftVO item : all) {
            if (item == null) {
                continue;
            }
            refusePreSubmitPplOrderDraftNotice(item, req, deadlineTime, userNameWithSystem);
        }

        // 记录 需求沟通-打回 日志
        recordRejectPreSubmit(beforePplList, req.getApproveNote());

        for (String s : on) {
            industryProcessService.cancelPplTransformOrder(s);
        }
    }

    private void refusePreSubmitPplOrderDraftNotice(PplOrderDraftVO ppl, BatchDealInnerPplOrderReq req,
            String deadlineTime, String userNameWithSystem) {
        StringJoiner instanceTypes = new StringJoiner(",");
        StringJoiner zoneNames = new StringJoiner(",");
        BigDecimal totalCoreOrTotalGpuNum = BigDecimal.ZERO;
        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(ppl.getProduct());
        if (ppl.getPplItemDrafts() != null) {
            for (PplItemDraftDO itemDraft : ppl.getPplItemDrafts()) {
                if (itemDraft == null) {
                    continue;
                }
                instanceTypes.add(itemDraft.getInstanceType());
                zoneNames.add(itemDraft.getZoneName());
                if (isGpu) {
                    totalCoreOrTotalGpuNum = NumberUtil.add(itemDraft.getTotalGpuNum());
                } else {
                    totalCoreOrTotalGpuNum = NumberUtil.add(itemDraft.getTotalCore());
                }
            }
        }
        Map<String, Object> templateParams = new HashMap<>();
        templateParams.put("customerShortName", ppl.getCustomerShortName());
        templateParams.put("product", ppl.getProduct());
        java.time.YearMonth yearMonth = ppl.getBeginDate() == null ? null :
                java.time.YearMonth.parse(ppl.getBeginDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        templateParams.put("demandYearMonth",
                yearMonth == null ? "" : (yearMonth.getYear() + "年" + yearMonth.getMonthValue() + "月"));
        templateParams.put("instanceType", instanceTypes);
        templateParams.put("zoneName", zoneNames);
        templateParams.put("totalCoreOrTotalGpuNum", totalCoreOrTotalGpuNum.toString() + (isGpu ? "卡" : "核"));
        templateParams.put("refuseNote", req.getApproveNote());
        templateParams.put("deadlineTime", deadlineTime);
        templateParams.put("operator", userNameWithSystem);
        templateParams.put("pplOrder", ppl.getPplOrder());
        dictService.eventNotice(CrpEventEnum.PRE_SUBMIT_PPL_REFUSE.getCode(),
                null, null, templateParams, ppl.getSubmitUser());
    }

    // 记录 需求沟通-打回 日志
    private void recordRejectPreSubmit(List<PplOrderDraftVO> beforePplList, String operateNote) {
        if (CollectionUtils.isEmpty(beforePplList)) {
            return;
        }
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        beforePplList.forEach(orderDraftDO ->
                orderDraftDO.getPplItemDrafts().forEach(itemDraftDO -> {
                    PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                    recordNewDTO.setOperateNote(operateNote);
                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, beforeAllFieldDTO);
                    BeanUtils.copyProperties(itemDraftDO, beforeAllFieldDTO);
                    beforeAllFieldDTO.setStatus(
                            Strings.isBlank(itemDraftDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                    : itemDraftDO.getStatus());
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraftDO, afterAllFieldDTO);
                    afterAllFieldDTO.setInstanceNum(0);
                    afterAllFieldDTO.setTotalCore(0);
                    afterAllFieldDTO.setTotalGpuNum(BigDecimal.valueOf(0));
                    afterAllFieldDTO.setTotalDisk(0);
                    afterAllFieldDTO.setStatus(
                            Strings.isBlank(itemDraftDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                    : itemDraftDO.getStatus());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    newDTOs.add(recordNewDTO);
                }));
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PRE_SUBMIT_REJECT.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));
    }

    /**
     * mq触发同步
     * step1: 版本数据转换为已生效，并下发至13周;
     * step2: 将新版本需求年月范围已生效数据移至新版本的需求沟通中
     *
     * @param message
     * @param channel
     * @param versionId
     * @throws IOException
     */
    @RabbitListener(queues = QueueDefinition.SYNC_INNER_PROCESS_ITEM_QUEUE, ackMode = "MANUAL")
    @Transactional("demandTransactionManager")
    @WithReport
    public void syncInnerProcessItem(Message message, Channel channel, Long versionId)
            throws IOException, InterruptedException {
        Boolean isSuccess = Boolean.TRUE;
        // 存在mq消息已过来 , 但是调用方的事物还没提交的情况 为了避免查询为空 等待10s
        Thread.sleep(10000);
        try {
            PplInnerProcessVersionDO innerProcessVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    "where id = ?", versionId);
            Map<String, PplOrderAuditRecordItemVO> historyItemMap = new HashMap<>();
            List<PplOrderAuditRecordItemVO> itemVOList =
                    auditRecordService.queryFirstRoundAuditRecordAndItem(innerProcessVersion.getId());
            if (!CollectionUtils.isEmpty(itemVOList)) {
                historyItemMap = itemVOList.stream()
                        .collect(Collectors.toMap(PplOrderAuditRecordItemVO::getPplId, v -> v));
            }
            // 查出行业所有走完当前版本的PplOrderAuditRecordVO 作为已生效数据
            List<PplOrderAuditRecordVO> pplOrderAuditRecordVOS = auditRecordService.queryEffectPplOrder(
                    innerProcessVersion.getIndustryDept(), versionId);
            if (CollectionUtils.isEmpty(pplOrderAuditRecordVOS)) {
                String msg = "【MQ消费--同步行业内部单据 -- 异常】version_id : "
                        + versionId + "exception: 接收到mq消费消息，但未查到已生效数据";
                AlarmRobotUtil.doAlarm("syncInnerProcessItem", msg, null, false);
            }

            for (PplOrderAuditRecordVO pplOrderAuditRecordVo : pplOrderAuditRecordVOS) {
                if (!CollectionUtils.isEmpty(pplOrderAuditRecordVo.getItemVOList())) {
                    // 查询出已存在的Item
                    List<String> pplIdList = pplOrderAuditRecordVo.getItemVOList().stream()
                            .map(PplOrderAuditRecordItemVO::getPplId).collect(
                                    Collectors.toList());
                    List<PplItemDO> updateItems = demandDBHelper.getAll(PplItemDO.class, "and ppl_id in (?)",
                            pplIdList);
                    Map<String, PplItemDO> updateMap = new HashMap<>();
                    if (!CollectionUtils.isEmpty(updateItems)) {
                        updateMap = updateItems.stream()
                                .collect(Collectors.toMap(PplItemDO::getPplId, v -> v));
                    }

                    // transToItemQueue
                    List<PplAuditItemQueueDO> pplAuditItemQueues = new ArrayList<>();
                    List<PplItemDO> pplItemDOList = new ArrayList<>();
                    String submitUser = pplOrderAuditRecordVo.getPplOrderDO().getSubmitUser();
                    for (PplOrderAuditRecordItemVO vo : pplOrderAuditRecordVo.getItemVOList()) {
                        PplItemDO existPplItemDO = updateMap.get(vo.getPplId());
                        PplAuditItemQueueDO itemQueue = new PplAuditItemQueueDO();
                        PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO = null;
                        if (existPplItemDO != null && Strings.isNotBlank(existPplItemDO.getStatus())
                                && existPplItemDO.getStatus().equals(PplItemStatusEnum.APPLIED.getCode())) {
                            // 如果是已经被预约的ppl 则本周期的改动均不生效， 用pplItem表中的数据
                            pplVersionGroupRecordItemDO
                                    = PplOrderAuditRecordItemVO.transToGroupRecordItemDO(existPplItemDO);
                        } else {
                            pplVersionGroupRecordItemDO
                                    = PplOrderAuditRecordItemVO.transToGroupRecordItemDO(vo);
                        }
                        pplVersionGroupRecordItemDO.setCreator(submitUser);

                        itemQueue.setIndustryDept(innerProcessVersion.getIndustryDept());
                        itemQueue.setProduct(vo.getProduct());
                        itemQueue.setVersionId(versionId);
                        itemQueue.setPplOrder(pplOrderAuditRecordVo.getPplOrder());
                        itemQueue.setItemJson(JsonUtil.prettyToString(pplVersionGroupRecordItemDO));
                        itemQueue.setStatus(0);
                        pplAuditItemQueues.add(itemQueue);

                        PplItemDO pplItemDO = PplOrderAuditRecordItemVO.transToPplItemDO(vo);
                        pplItemDOList.add(pplItemDO);

                        if (historyItemMap.get(pplItemDO.getPplId()) != null) {
                            // 将走审批的数据 从map中删除， 那么historyPplIdMap中剩下的就是上版本的数据且不生效的 全部清空。
                            historyItemMap.remove(pplItemDO.getPplId());
                        }
                    }

                    demandDBHelper.insertBatchWithoutReturnId(pplAuditItemQueues);

                    List<PplItemDO> insertList = new ArrayList<>();
                    for (PplItemDO pplItemDO : pplItemDOList) {
                        PplItemDO existPplItemDO = updateMap.get(pplItemDO.getPplId());
                        if (existPplItemDO != null) {
                            if (existPplItemDO.getStatus().equals(PplItemStatusEnum.APPLIED.getCode())) {
                                // 如果该条需求已预约,则直接废弃掉
                                log.info("该条ppl状态已被预约,本次调整不生效,ppl-id: " + existPplItemDO.getPplId());
                            } else {
                                pplItemDO.setId(existPplItemDO.getId());
                                // 是否被干预字段不能被覆盖
                                pplItemDO.setIsComd(existPplItemDO.getIsComd());
                                demandDBHelper.update(pplItemDO);
                            }
                        } else {
                            insertList.add(pplItemDO);
                        }
                    }
                    if (!CollectionUtils.isEmpty(insertList)) {
                        demandDBHelper.insertBatchWithoutReturnId(insertList);
                    }
                }
            }

            // 将需求年月内所有以往版本的数据，在这版本没走到流程最后的数据置为无效数据
            Set<String> clearDataPplIds = historyItemMap.keySet();
            if (!CollectionUtils.isEmpty(clearDataPplIds)) {
                demandDBHelper.executeRaw(
                        "update ppl_item set instance_num = 0,total_core = 0,total_disk = 0," +
                                "total_gpu_num = 0 where deleted = 0 and ppl_id in (?)", clearDataPplIds);
            }

            // 获取下版本需求范围内已生效数据 转换为草稿箱数据，并提交至需求沟通
            PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    "where status = ? and industry_dept = ? and product = ?",
                    PplInnerProcessVersionStatusEnum.PROCESSING.getCode(),
                    innerProcessVersion.getIndustryDept(), innerProcessVersion.getProduct());
            // 将新版本需求年月范围已生效数据移至新版本的需求沟通中（批量）
            syncNewVersionPpl(nextVersion.getId());

            // 处理过期需求（上一版：innerProcessVersion，当前版本：nextVersion）
            processExpiredDemand(innerProcessVersion, nextVersion);

            // 标记此版本已被mq消费
            PplInnerProcessVersionDO processVersionDO = new PplInnerProcessVersionDO();
            processVersionDO.setId(versionId);
            processVersionDO.setConsumeStatus(1);
            processVersionDO.setConsumeTime(new Date());
            demandDBHelper.update(processVersionDO);

            // 5秒后 填充内部业务部-基准数据
            scheduledExecutor.schedule(() -> modelForecastForPackageService.fillZeroBaseData(nextVersion.getId()),
                    5, TimeUnit.SECONDS);
        } catch (Exception e) {
            isSuccess = Boolean.FALSE;
            String msg = "【MQ消费--同步行业内部单据 -- 失败】version_id : " + versionId + "exception: " + e;
            log.error(msg);
            AlarmRobotUtil.doAlarm("syncInnerProcessItem", msg, null, false);
            throw new BizException("同步行业内部单据失败");
        } finally {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), isSuccess);
        }

    }

    /**
     * 将生效版本需求年月范围的已生效数据移至此版本的需求沟通中 <br/>
     * 将草稿箱中下期生效数据预提交到本周期 <br/>
     * @param versionId 版本id（即当前行业[生效]版本id）
     */
    @Override
    @Transactional(value = "demandTransactionManager")
    public void syncNewVersionPpl(Long versionId) {
        PplInnerProcessVersionDO processVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where id = ? ", versionId);

        if (processVersion == null) {
            return;
        }

        List<PplItemDO> versionValidPplItem = getVersionValidPplItem(processVersion);
        if (CollectionUtils.isEmpty(versionValidPplItem)) {
            return;
        }

        Map<String, List<PplItemDO>> pplOrderToItemMap = versionValidPplItem.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));

        Map<String, PplOrderDO> pplOrderDOMap = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in(?) ",
                pplOrderToItemMap.keySet()).stream().collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v));

        List<SavePplDraftReq> draftReqList = new ArrayList<>();
        pplOrderToItemMap.forEach((k, v) -> {
            PplOrderDO one = pplOrderDOMap.get(k);
            SavePplDraftReq savePplDraftReq = pplItemTransToDraftReq(one, v);
            draftReqList.add(savePplDraftReq);
        });

        // 批量保存草稿并扭转至需求沟通
        pplDraftService.batchSaveDraft(draftReqList, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), Boolean.FALSE,
                null,
                null, null);

        // 将草稿箱中下期生效数据预提交到本周期
        pplDraftService.preSubmitDraftForStartNewVersion(versionId);
    }

    /**
     * 将预约数据同步至草稿表（需求沟通）
     *
     * @return message
     */
    @Override
    public void syncAppliedToDraft() {
        List<PplInnerProcessVersionDO> all = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where status = ?", PplInnerProcessVersionStatusEnum.PROCESSING.getCode());

        if (CollectionUtils.isEmpty(all)) {
            log.info("syncAppliedToDraft, 各行业都无生效中审批版本, 无需同步");
            return;
        }

        List<PplInnerProcessVersionSlaDO> enterSlaDOList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in(?) and deadline_type = 'ENTER'",
                all.stream().map(PplInnerProcessVersionDO::getId).collect(
                        Collectors.toList()));
        Map<Long, PplInnerProcessVersionSlaDO> enterSlaDOMap = enterSlaDOList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getVersionId, Function.identity()));

        for (PplInnerProcessVersionDO versionDO : all) {

            PplInnerProcessVersionSlaDO enterSlaDO = enterSlaDOMap.get(versionDO.getId());
            if (enterSlaDO == null) {
                continue;
            }
            // 开启审批后，版本不再拉取最新的预约数据，保留启动审批时所拉取的预约数据进入审批流即可
            if (enterSlaDO.getDeadlineTime() != null && enterSlaDO.getDeadlineTime().before(new Date())) {
                continue;
            }

            CompletableFuture.runAsync(() -> {
                log.info("syncAppliedToDraft, versionId-{}, 开始同步来自云霄的预约单", versionDO.getId());

                String beginBuyDate = versionDO.getDemandBeginYear().toString()
                        + "-" + (versionDO.getDemandBeginMonth().toString()) + "-" + 1;
                List<String> supportProduct = Arrays.asList(versionDO.getProduct().split(";"));
                List<PplItemAppliedVO> versionValidPplItemAppliedList = demandDBHelper.getAll(PplItemAppliedVO.class,
                        "where ppl_order_source = ? and begin_buy_date >= ? and product in (?) and status = 'APPLIED' "
                                + "and yunxiao_order_status not in ('CREATED', 'CANCELED', 'BAD_CANCELED', 'REJECTED') "
                                + "and ppl_order in (select ppl_order from ppl_order " +
                                "where deleted = 0 and industry_dept = ? and source = ? )",
                        PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode(), beginBuyDate,
                        supportProduct, versionDO.getIndustryDept(),
                        PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode());

                if (CollectionUtils.isEmpty(versionValidPplItemAppliedList)) {
                    return;
                }

                log.info("syncAppliedToDraft, 需同步的预约单, ids-{}",
                        versionValidPplItemAppliedList.stream().map(PplItemAppliedVO::getId).collect(
                                Collectors.toList()));

                Map<String, List<PplItemAppliedVO>> pplOrderToItemAppliedMap = versionValidPplItemAppliedList.stream()
                        .collect(Collectors.groupingBy(PplItemAppliedVO::getPplOrder));

                List<PplOrderDO> orderDOList = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in(?) ",
                        pplOrderToItemAppliedMap.keySet());
                completeWarZone(orderDOList);

                Map<String, PplOrderDO> pplOrderDOMap = orderDOList.stream()
                        .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v));

                List<SavePplDraftReq> draftReqList = new ArrayList<>();
                pplOrderToItemAppliedMap.forEach((k, v) -> {
                    PplOrderDO one = pplOrderDOMap.get(k);
                    SavePplDraftReq savePplDraftReq = pplItemAppliedTransToDraftReq(one, v);
                    draftReqList.add(savePplDraftReq);
                });

                // 批量保存草稿并扭转至需求沟通
                pplDraftService.batchSaveDraft(draftReqList, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                        Boolean.FALSE, null, null, null);
            });

        }

    }

    /**
     * 该方法为 多产品的pplItem 返回  用于初始化数据至需求沟通中
     *
     * @param versionDO
     * @return
     */
    @Override
    public List<PplItemDO> getVersionValidPplItem(PplInnerProcessVersionDO versionDO) {

        LocalDate beginBuyDate = cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonthLocalDate(
                cloud.demand.app.common.utils.DateUtils.getYearMonthStr(versionDO.getDemandBeginYear(),versionDO.getDemandBeginMonth()));

        List<String> supportProduct = Arrays.asList(versionDO.getProduct().split(";"));
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" instance_num > 0");
        whereSQL.and(" product in (?)",supportProduct);

        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        whereSQL.and(" begin_buy_date >= ?",beginBuyDate);


        whereSQL.and("ppl_order in ( select distinct ppl_order from ppl_order"
                + " where deleted = 0 and industry_dept = ? and source = ? )",versionDO.getIndustryDept(),PplOrderSourceTypeEnum.IMPORT.getCode());

        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,whereSQL.getSQL(),whereSQL.getParams());

        // 剔除掉 海外地域 且 非退回 且 非 不满足海外年月 的数据
        if (ListUtils.isNotEmpty(pplItemDOList)){
            pplItemDOList.removeIf(v -> {
                return overSeaRegions.contains(v.getRegionName())
                        && !v.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                        && !versionDO.isSatisfyOverseasYearMonth(v.getBeginBuyDate());
            });
        }

        // 只继承 PN单（未预约、已预约）
       /* pplItemDOList = pplItemDOList.stream().filter(e -> e.getPplOrder().startsWith("PN"))
                .collect(Collectors.toList());*/
        return pplItemDOList;
    }

    /**
     * 该方法为 多产品的pplItem 返回  用于创建过期数据
     *
     * @param versionDO
     * @return
     */
    @Override
    public List<PplItemDO> getExpiredPplItemForVersion(PplInnerProcessVersionDO versionDO,PplInnerProcessVersionDO lastVersion) {

        if (versionDO.getInCountryDemandYearMonth().equals(lastVersion.getInCountryDemandYearMonth())
        && versionDO.getOverseasDemandYearMonth().equals(lastVersion.getOverseasDemandYearMonth())) {
            return new ArrayList<>();
        }
        List<String> supportProduct = Arrays.asList(versionDO.getProduct().split(";"));
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and(" status != ? ",PplItemStatusEnum.APPLIED.getCode());
        whereSQL.and(" instance_num > 0 ");
        whereSQL.and(" product in (?) ",supportProduct);
        whereSQL.and(" demand_type != ?",PplDemandTypeEnum.RETURN.getCode());
        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        whereSQL.and(" (region_name not in (?) and begin_buy_date >= ? and begin_buy_date < ?) or "
                + "(region_name in (?) and begin_buy_date >= ? and begin_buy_date < ?)"
                ,overSeaRegions,lastVersion.getInCountryDemandYearMonthFirstDay(),versionDO.getInCountryDemandYearMonthFirstDay()
                ,overSeaRegions,lastVersion.getOverseasDemandYearMonthFirstDay(),versionDO.getOverseasDemandYearMonthFirstDay());

        whereSQL.and(" ppl_order in ( select distinct ppl_order from ppl_order"
                + " where deleted = 0 and industry_dept = ? and source = ? )",versionDO.getIndustryDept(),PplOrderSourceTypeEnum.IMPORT.getCode());

        return demandDBHelper.getAll(PplItemDO.class,whereSQL.getSQL(),whereSQL.getParams());
    }

    /**
     * 该方法为 多产品的pplItem 返回  用于初始化数据至需求沟通中 - 针对 系统自动 生成的PE单
     *
     * @param versionDO
     * @return
     */
    @Override
    public List<PplItemDO> getVersionValidPplItemBySystem(PplInnerProcessVersionDO versionDO) {
        String beginBuyDate = versionDO.getDemandBeginYear().toString()
                + "-" + (versionDO.getDemandBeginMonth().toString()) + "-" + 1;
        List<String> supportProduct = Arrays.asList(versionDO.getProduct().split(";"));
        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where instance_num > 0 " +
                        "and begin_buy_date >= ? and product in (?) and status = ? " +
                        "and yunxiao_order_status not in ('CREATED', 'CANCELED', 'BAD_CANCELED', 'REJECTED') " +
                        "and ppl_order in (select ppl_order from ppl_order " +
                        "where deleted = 0 and industry_dept = ? and customer_short_name not like '%云运管%' " +
                        "and submit_user != 'dreamxin' and source in(?) )",
                beginBuyDate,
                supportProduct, PplItemStatusEnum.APPLIED.getCode(), versionDO.getIndustryDept(),
                Arrays.asList(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode()));

        return pplItemDOList;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void initData(InitDataVo initDataVo) {
        String beginBuyDate = initDataVo.getBeginYearMonth() + "-" + 1;
        String endBuyDate = initDataVo.getEndYearMonth() + "-" + 1;
        List<String> supportProduct = Arrays.asList(initDataVo.getProduct().split(";"));
        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where instance_num > 0 " +
                        "and begin_buy_date >= ? and begin_buy_date < ? and product in (?) " +
                        "and ppl_order in (select ppl_order from ppl_order " +
                        "where deleted = 0 and industry_dept = ? and customer_short_name not like '%云运管%'" +
                        "and submit_user != 'dreamxin' and source = ? )",
                beginBuyDate, endBuyDate,
                supportProduct, initDataVo.getIndustryDept(),
                PplOrderSourceTypeEnum.IMPORT.getCode());

        // 只继承 PN单（未预约、已预约）
        /*pplItemDOList = pplItemDOList.stream().filter(e -> e.getPplOrder().startsWith("PN"))
                .collect(Collectors.toList());*/
        Map<String, List<PplItemDO>> pplOrderToItem = pplItemDOList.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        pplOrderToItem.forEach((k, v) -> {
            // 保存草稿
            PplOrderDO one = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order = ?", k);
            SavePplDraftReq savePplDraftReq = pplItemTransToDraftReq(one, v);
            savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            savePplDraftReq.setIsRecord(Boolean.FALSE);
            pplDraftService.saveDraft(savePplDraftReq);
        });
        // 将提交至草稿箱的数据扭转至需求沟通
        if (!CollectionUtils.isEmpty(pplOrderToItem.keySet())) {
            demandDBHelper.executeRaw("update ppl_order_draft set draft_status = ? "
                            + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                    PplOrderDraftStatusEnum.DRAFT.getCode(), pplOrderToItem.keySet());
            demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? "
                            + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                    PplOrderDraftStatusEnum.DRAFT.getCode(), pplOrderToItem.keySet());
        }


    }

    @Override
    public DayScaleResult dayScaleCurve(HistoricalTrendReq params) {
        PplInnerProcessVersionDO version = checkParamsAndGetVersion(params);
        String user = LoginUtils.getUserName();
        PermissionDTO permission = queryPermission(user, version.getIndustryDept(),
                ListUtils.newArrayList(params.getVersionId()));
        params.checkParamsAndScaleDateHandler(version);
        // 数据权限
        WhereContent permissionWhere = permission.toWhereWithOutVersionId(params.getIndustryDept(), null);
        if (permissionWhere == null) {
            // 没数据权限，返回每一天的零值数据
            DayScaleDTO result = new DayScaleDTO(params.getBeginDemandDateForCkScale(),
                    params.getEndDemandDateForCkScale());
            return result.resultData(null, null);
        }
        // 日规模表有些数据客户简称为(空值)，转成uin去查询日规模表
        List<CustomerItem> list = null;
        if (ListUtils.isNotEmpty(params.getCustomerShortName())) {
            list = queryCustomerByUinOrShortName(new HashSet<>(params.getCustomerShortName()), false);
            Map<String, String> uinMap = ListUtils.toMap(list, CustomerItem::getCustomerShortName,
                    CustomerItem::getCustomerUin);
            // 客户简称 找不到对应的客户uin
            list.removeIf(customerItem -> customerItem == null || Strings.isBlank(customerItem.getCustomerUin()));
        }
        List<String> uinListByShortName = new ArrayList<>();
        if (ListUtils.isNotEmpty(list)) {
            uinListByShortName = ListUtils.transform(list, CustomerItem::getCustomerUin);
        }

        List<ExecItem> execItems;
        if (ListUtils.isEmpty(uinListByShortName) && ListUtils.isNotEmpty(params.getCustomerShortName())) {
            // 客户简称 找不到对应的客户uin, 返回空日规模
            execItems = new ArrayList<>();
        } else {
            // 1，查询起初规模量
            // 2，查询每一天的日执行量
            WhereContent scaleWhere = params.whereForCkScale(uinListByShortName);
            scaleWhere.addAnd(permissionWhere);
            String scaleSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_day_scale.sql");
            scaleSql = scaleSql.replace("${whereCondition}", scaleWhere.getSql());
            execItems = DBList.ckcldStdCrpDBHelper.getRaw(ExecItem.class, scaleSql, scaleWhere.getParams());
        }

        // 3，查询最新ppl预测量
        WhereContent forecastWhere = params.whereForCkVersionForecast();
        forecastWhere.addAnd(permissionWhere);
        String forecastSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_day_forecast.sql");
        forecastSql = forecastSql.replace("${whereCondition}", forecastWhere.getSql());
        List<ForecastItem> forecastItems = DBList.ckcldStdCrpDBHelper
                .getRaw(ForecastItem.class, forecastSql, forecastWhere.getParams());

        DayScaleDTO result = new DayScaleDTO(params.getBeginDemandDateForCkScale(),
                params.getEndDemandDateForCkScale(), params.getBeginDemandDateForCkScaleForecast(),
                params.getEndDemandDateForCkScaleForecast(), params.getCurrentMonthLastDay());
        // 按规定算法写入每日的执行量、预测量
        result.addAndComputeExec(execItems);
        result.addAndComputeForecast(forecastItems);

        // 获取每月版本需求预测量
        String versionSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_version_forecast.sql");
        versionSql = versionSql.replace("${whereCondition}", forecastWhere.getSql());
        List<YearMonthDemand> versionItems = DBList.ckcldStdCrpDBHelper
                .getRaw(YearMonthDemand.class, versionSql, forecastWhere.getParams());

        // 新532的版本
        String sql532 = ORMUtils.getSql("/sql/ppl13week/inner_process/query_version_532_zero_forecast.sql");
        int beginYearMonth532 = params.getBeginDemandDateForCkScale().getYear() * 12
                + params.getBeginDemandDateForCkScale().getMonthValue();
        int endYearMonth532 = params.getEndDemandDateForCkScale().getYear() * 12
                + params.getEndDemandDateForCkScale().getMonthValue();
        List<YearMonthDemand> versionNew532 = DBList.ckcldStdCrpDBHelper
                .getRaw(YearMonthDemand.class, sql532, beginYearMonth532, endYearMonth532);

        return result.resultData(versionItems, versionNew532);
    }

    private PplInnerProcessVersionDO checkParamsAndGetVersion(HistoricalTrendReq params) {
        if (params == null || params.getVersionId() == null) {
            throw BizException.makeThrow("请选择版本");
        }
        PplInnerProcessVersionDO version = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                params.getVersionId());
        if (version == null) {
            throw BizException.makeThrow("版本【%s】不存在", params.getVersionId());
        }
        params.setIndustryDept(version.getIndustryDept());
        if (StringUtils.isNotBlank(params.getProduct())) {
            if (params.getProduct().equals(Ppl13weekProductTypeEnum.PAAS.getName())) {
                params.setProductList(Ppl13weekProductTypeEnum.getPaasProductNameList());
            } else {
                params.setProductList(Arrays.asList(params.getProduct()));
            }
        }
        return version;
    }

    @Override
    public HistoricalTrendsResp queryHistoricalTrends(HistoricalTrendReq params) {
        PplInnerProcessVersionDO version = checkParamsAndGetVersion(params);
        if (!params.isQueryTableData() && !params.isQueryTrendChartData()) {
            throw BizException.makeThrow("接口参数有误，【queryTableData】【queryTrendChartData】不能同时为false");
        }
        String user = LoginUtils.getUserName();
        PermissionDTO permission = queryPermission(user, version.getIndustryDept(),
                ListUtils.newArrayList(params.getVersionId()));
        params.handlerConditionYearMonth(version);

        CountDownLatchWithTrendItems historyDownLatch = null;
        List<TrendItem> history = new ArrayList<>();
        if (params.getStartDemandMonthForCk() > 0) {
            // 根据查询时间范围判断需要查询ck
            historyDownLatch = queryHistoryFromCk(params, version.getIndustryDept(), permission);

        }
        List<TrendItem> current = queryCurrentFromMysql(params, version.getIndustryDept(), permission);
        // 版本13周之外的预测值, L = newForecast13WeekOutside , R = returnForecast13WeekOutside
        Pair<Integer, Integer> outside13Week = forecast13WeekOutside(params, version.getIndustryDept(), permission);

        HistoricalTrendsResp result = new HistoricalTrendsResp();
        result.setProduct(params.getProduct());
        result.setNewForecast13WeekOutside(outside13Week.getLeft());
        result.setReturnForecast13WeekOutside(outside13Week.getRight());
        // 从配置读取
        result.setAimAccuracy(aimAccuracy());
        result.setReturnAimAccuracy(returnAimAccuracy());
        if (historyDownLatch != null) {
            // 最后等待 查询行业数据看板那边的执行量和准确率结果
            try {
                historyDownLatch.getDownLatch().await(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                // non
                result.setAsyncTaskMsg("等待行业数据看板接口响应超时或异常:" + e.getMessage());
            }
            if (ListUtils.isNotEmpty(historyDownLatch.getDatas())) {
                for (List<TrendItem> data : historyDownLatch.getDatas()) {
                    history.addAll(data);
                }
            }
        }
        result.toResponseData(current, history, params);
        return result;
    }

    /**
     * 从配置中心获取实时的新增目标准确率
     */
    private String aimAccuracy() {
        String res = SpringUtil.getApplicationContext().getEnvironment().getProperty("crp.dict.aim.accuracy.new");
        if (Strings.isBlank(res)) {
            res = "35.00%";
        }
        return res;
    }

    /**
     * 从配置中心获取实时的退回目标准确率
     */
    private String returnAimAccuracy() {
        String res = SpringUtil.getApplicationContext().getEnvironment().getProperty("crp.dict.aim.accuracy.return");
        if (Strings.isBlank(res)) {
            res = "35.00%";
        }
        return res;
    }

    private CountDownLatchWithTrendItems queryHistoryFromCk(HistoricalTrendReq params, String industryDept,
            PermissionDTO permission) {
        WhereContent permissionWhere = permission.toWhereWithOutVersionId(industryDept, null);
        if (permissionWhere == null) {
            return CountDownLatchWithTrendItems.empty();
        }
        // 权限客户简称
        Set<String> customerList = permission.permissionCustomerShortNameAllWithWarZone(pplDictService);
        List<String> rangeWarZoneList = new ArrayList<>(); // 选择的战区范围
        if (ListUtils.isNotEmpty(params.getBizRange())) {
            for (String s : params.getBizRange()) {
                // 能选择全行业则不根据战区过滤权限客户
                if (HistoricalTrendReq.INDUSTRY_DEPT_ALL_WAR_ZONE.equals(s)) {
                    customerList = null;
                    rangeWarZoneList.clear();
                    break;
                }
                if (!params.isBizRangeIsCustomer()) {
                    rangeWarZoneList.add(s);
                }
            }
        }
        if (ListUtils.isNotEmpty(params.getWarZone()) || ListUtils.isNotEmpty(rangeWarZoneList)) {
            // 查询参数中的战区转客户简称，取与权限客户简称的交集， 因为行业数据看板不支持按战区查询
            Set<String> queryWarZones = new HashSet<>(rangeWarZoneList);
            if (ListUtils.isNotEmpty(params.getWarZone())) {
                queryWarZones.addAll(params.getWarZone());
            }
            List<String> paramsWarZoneCustomer = pplDictService
                    .getCustomerShortNamesByWarZone(new ArrayList<>(queryWarZones));
            Collection<String> mergeCustomer = CollUtil.intersection(customerList, paramsWarZoneCustomer);
            if (ListUtils.isEmpty(customerList)) {
                mergeCustomer = paramsWarZoneCustomer;
            }
            if (ListUtils.isEmpty(mergeCustomer)) {
                // 交集为空，表示没有可查询的客户，直接返回，不用调行业数据看板
                return CountDownLatchWithTrendItems.empty();
            } else {
                customerList = new HashSet<>(mergeCustomer);
            }
        }

        WhereContent where = params.whereForCk();
        where.addAnd(permissionWhere);
        String normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_customer_demand_group_ck.sql");
        String sql = normalSql.replace("${whereCondition}", where.getSql());
        List<CustomerDemandInfo> list = DBList.ckcldStdCrpDBHelper.getRaw(CustomerDemandInfo.class, sql,
                where.getParams());

        List<TrendItem> result = HistoricalTrendsResp.group(list, params, false);
        // 新532预测量、执行量、准确率，异步调用行业数据看板
        return handlerTrendItem(result, params, industryDept, customerList);
    }

    private CountDownLatchWithTrendItems handlerTrendItem(List<TrendItem> result, HistoricalTrendReq params,
            String industryDept, Set<String> customerList) {
        Map<String, List<TrendItem>> listMap = ListUtils.toMapList(result, TrendItem::getDemandType, item -> item);
        if (ListUtils.isEmpty(listMap)) {
            listMap = new HashMap<>();
        }

        AtomicReference<Boolean> notRunNew = new AtomicReference<>(true);
        AtomicReference<Boolean> notRunReturn = new AtomicReference<>(true);
        boolean needReturn = ListUtils.isEmpty(params.getDemandType())
                || params.getDemandType().contains(PplDemandTypeEnum.RETURN.getCode());
        boolean needNew = ListUtils.isEmpty(params.getDemandType())
                || params.getDemandType().contains(PplDemandTypeEnum.NEW.getCode());
        AtomicInteger taskCount = new AtomicInteger();
        listMap.forEach((demandType, trendItems) -> {
            if (TrendItem.DEMAND_TYPE_NEW.equals(demandType)) {
                notRunNew.set(false);
                taskCount.getAndIncrement();
            } else if (TrendItem.DEMAND_TYPE_RETURN.equals(demandType)) {
                notRunReturn.set(false);
                taskCount.getAndIncrement();
            }
        });
        if (needReturn && notRunReturn.get()) {
            taskCount.getAndIncrement();
        }
        if (needNew && notRunNew.get()) {
            taskCount.getAndIncrement();
        }

        CountDownLatchWithTrendItems res = CountDownLatchWithTrendItems.empty();
        CountDownLatch downLatch = new CountDownLatch(taskCount.get());
        res.setDownLatch(downLatch);
        listMap.forEach((demandType, trendItems) -> {
            if (TrendItem.DEMAND_TYPE_NEW.equals(demandType)) {
                res.getDatas().add(trendItems);
                executor.execute(() ->
                        queryIndustryDataDashboardsAsync(params, false, trendItems, industryDept,
                                customerList, downLatch));
            } else if (TrendItem.DEMAND_TYPE_RETURN.equals(demandType)) {
                res.getDatas().add(trendItems);
                executor.execute(() ->
                        queryIndustryDataDashboardsAsync(params, true, trendItems, industryDept,
                                customerList, downLatch));
            }
        });
        if (needReturn && notRunReturn.get()) {
            List<TrendItem> trendItems = new ArrayList<>();
            res.getDatas().add(trendItems);
            executor.execute(() ->
                    queryIndustryDataDashboardsAsync(params, true, trendItems, industryDept,
                            customerList, downLatch));
        }
        if (needNew && notRunNew.get()) {
            List<TrendItem> trendItems = new ArrayList<>();
            res.getDatas().add(trendItems);
            executor.execute(() ->
                    queryIndustryDataDashboardsAsync(params, false, trendItems, industryDept,
                            customerList, downLatch));
        }
        return res;
    }

    private void queryIndustryDataDashboardsAsync(HistoricalTrendReq params, boolean isReturn,
            List<TrendItem> handlerList, String industryDept, Set<String> customerList, CountDownLatch downLatch) {
        try {
            queryIndustryDataDashboards(params, isReturn, handlerList, industryDept, customerList);
        } finally {
            downLatch.countDown();
        }
    }

    /**
     * 调用行业数据看板接口，匹配填充准确率、执行量
     *
     * @param params 审批查询入参
     * @param isReturn 是否是退回需求查询
     * @param handlerList 需要匹配填充准确率、执行量的数据，对没有预测的数据会根据准确率、执行量自动生成数据并加入此集合
     * @param customerList 权限客户，为空时表示无需客户权限
     */
    private void queryIndustryDataDashboards(HistoricalTrendReq params, boolean isReturn,
            List<TrendItem> handlerList, String industryDept, Set<String> customerList) {
        QueryTableReq req = new QueryTableReq();
        Set<String> customers = new HashSet<>();
        if (ListUtils.isNotEmpty(customerList)) {
            // 根据权限过滤
            customers.addAll(customerList);
        }
        if (ListUtils.isNotEmpty(params.getCustomerShortName())) {
            if (ListUtils.isNotEmpty(customerList)) {
                // 查询参数与权限取交集
                customers.retainAll(params.getCustomerShortName());
            } else {
                customers.addAll(params.getCustomerShortName());
            }
        }
        if (ListUtils.isNotEmpty(params.getCustomerUin())) {
            // uin 转 name 用于查询
            // 由于用户信息宽表根据uin查询到的客户简称可能是(空值)，这边直接用ppl_order、ppl_order_draft来获取
            List<CustomerItem> list = queryCustomerByUinOrShortName(new HashSet<>(params.getCustomerUin()), true);
            if (ListUtils.isNotEmpty(list)) {
                // uni查询参数的客户与上面的客户交集结果继续取交集
                List<String> items = ListUtils.transform(list, CustomerItem::getCustomerShortName);
                if (ListUtils.isNotEmpty(customers)) {
                    // 查询参数与权限取交集
                    customers.retainAll(items);
                } else {
                    customers.addAll(items);
                }
            }
        }
        if (ListUtils.isNotEmpty(customerList) && ListUtils.isEmpty(customers)) {
            // 指定了有权限的客户名称，但是和查询条件没有交集，则表示没有查询权限，不进行查询
            return;
        }
        if (ListUtils.isEmpty(customers) && ListUtils.isNotEmpty(params.getCustomerShortName())) {
            // 输入的查询客户简称有值，但是客户简称交集为空，则表示查询不到数据，不进行查询
            return;
        }
        if (ListUtils.isEmpty(customers) && ListUtils.isNotEmpty(params.getCustomerUin())) {
            // 输入的查询客户uin有值，但是客户简称交集为空，则表示查询不到数据，不进行查询
            return;
        }
        if (ListUtils.isNotEmpty(customers)) {
            req.setCustomerShortNames(new ArrayList<>(customers));
        }
        req.setInstanceTypes(params.getInstanceType());
        req.setCountry(params.getCustomhouseTitle());
        req.setRegionNames(params.getRegionName());
        req.setZoneNames(params.getZoneName());
        req.setIndustryDepts(ListUtils.newArrayList(industryDept));
        req.setIsNewOrderType(true);
        req.setAbTest(false);
        req.setBillingScaleDistinct(true);
        req.setBillingScaleMode("月均切片");
        req.setBizTypes(ListUtils.newArrayList("外部行业"));
        Choose choose = new Choose();
        choose.setBizType("外部行业");
        choose.setProjectType(ProjectTypeEnum.KEY_PROJECT.getName());
        choose.setIndustryOrProduct(industryDept);
        // 分组纬度
        List<HistoricalTrendGroup> groupLatitude = new ArrayList<>();
        List<String> selectFields = new ArrayList<>();
        selectFields.add("industryOrProduct");
        if (ListUtils.isNotEmpty(params.getGroupQueryConditions())) {
            for (GroupQueryCondition condition : params.getGroupQueryConditions()) {
                if (condition == null || condition.getGroup() == null || condition.getValue() == null) {
                    continue;
                }
                switch (condition.getGroup()) {
                    case warZone:
                        choose.setWarZoneName(condition.getValue());
                        selectFields.add(condition.getGroup().getSelectFieldName());
                        groupLatitude.add(condition.getGroup());
                        break;
                    case regionName:
                        choose.setRegionName(condition.getValue());
                        selectFields.add(condition.getGroup().getSelectFieldName());
                        groupLatitude.add(condition.getGroup());
                        break;
                    case instanceType:
                        choose.setGinsFamily(condition.getValue());
                        selectFields.add(condition.getGroup().getSelectFieldName());
                        groupLatitude.add(condition.getGroup());
                        break;
                    case customerShortName:
                        choose.setCustomerShortName(condition.getValue());
                        selectFields.add(condition.getGroup().getSelectFieldName());
                        groupLatitude.add(condition.getGroup());
                        break;
                    default:
                        // non
                }
            }
        }
        if (ListUtils.isNotEmpty(params.getGroupList())) {
            for (HistoricalTrendGroup trendGroup : params.getGroupList()) {
                if (trendGroup == null) {
                    continue;
                }
                if (!groupLatitude.contains(trendGroup)) {
                    groupLatitude.add(trendGroup);
                    selectFields.add(trendGroup.getSelectFieldName());
                }
            }
        }
        req.setChoose(choose);
        req.setCommonInstanceType(true);  // 不合并机型
        // 查询计费执行量、新532核天准确率（同时会查询出核天执行量）
        req.setQueryTargets(ListUtils.newArrayList("forecast_num", "change_bill_num",
                "forecast_match_rate_core_by_day"));
        String demandType;
        if (isReturn) {
            req.setDemandTypes(ListUtils.newArrayList("退回"));
            demandType = TrendItem.DEMAND_TYPE_RETURN;
        } else {
            req.setDemandTypes(ListUtils.newArrayList("新增", "弹性"));
            demandType = TrendItem.DEMAND_TYPE_NEW;
        }
        if (Ppl13weekProductTypeEnum.GPU.getName().equals(params.getProduct())) {
            req.setProduct("GPU");
        } else if (Ppl13weekProductTypeEnum.CVM.getName().equals(params.getProduct())) {
            req.setProduct("CVM");
        } else if (Ppl13weekProductTypeEnum.BM.getName().equals(params.getProduct())) {
            req.setProduct("裸金属");
        } else {
            return;
        }

        req.setIsRendering(false);
        req.setRateSumFunction("执行量");
        req.setBizTypes(ListUtils.newArrayList("外部行业"));
        TimePeriod timePeriod = new TimePeriod();
        timePeriod.setStart(params.getStartDemandYearForCk() + "-"
                + (params.getStartDemandMonthForCk() < 10 ? "0" : "")
                + params.getStartDemandMonthForCk()); // 2023-08
        timePeriod.setEnd(params.getEndDemandYearForCk() + "-"
                + (params.getEndDemandMonthForCk() < 10 ? "0" : "")
                + params.getEndDemandMonthForCk()); // 2023-10
        req.setTimePeriod(timePeriod);
        req.setForecastStatus(ForecastStatus.origin.getName());
        req.setMainInstanceFamilyAndZone(false);
        req.setSelectFields(selectFields);

        MrpV2Cache mrpV2Cache = new MrpV2Cache();
        mrpV2Cache.setEnable(Boolean.TRUE);
        Map<String, List<String>> cacheDim = new HashMap<>();
        cacheDim.put(CacheDimEnum.industryOrProduct.getColumn(), new ArrayList<>());
//        cacheDim.put(CacheDimEnum.demandType.getColumn(), new ArrayList<>());  此处用需求类型会导致出新增和弹性分开计算结果
        mrpV2Cache.setCacheDim(cacheDim);
        req.setCache(mrpV2Cache);

        QueryTableRsp resp = queryDataService.queryTable(req, false);
        Collection<Map> datas = resp.getData();
        if (ListUtils.isEmpty(datas)) {
            return;
        }

        // cvm才显示核天准确率
        boolean needAccuracy = Ppl13weekProductTypeEnum.CVM.getName().equals(params.getProduct());
        // 使用核天执行量，gpu不需要核天执行量
        boolean useCoreDayExec = !HistoricalTrendReq.EXEC_TYPE_MONTH_AVG.equals(params.getExecType())
                && !Ppl13weekProductTypeEnum.GPU.getName().equals(params.getProduct());
        // 使用月均口径执行量，gpu只能使用月均口径
        boolean useMonthAvgExec = HistoricalTrendReq.EXEC_TYPE_MONTH_AVG.equals(params.getExecType())
                || Ppl13weekProductTypeEnum.GPU.getName().equals(params.getProduct());
        java.time.YearMonth start = java.time.YearMonth.of(params.getStartDemandYearForCk(),
                params.getStartDemandMonthForCk());
        java.time.YearMonth end = java.time.YearMonth.of(params.getEndDemandYearForCk(),
                params.getEndDemandMonthForCk());
        List<TrendItem> execItemList = mapToTrendItem(datas, useCoreDayExec, useMonthAvgExec, needAccuracy, start, end,
                params.getLastGroup(), demandType);
        HistoricalTrendGroup lastGroup = ListUtils.isEmpty(groupLatitude) ? null
                : groupLatitude.get(groupLatitude.size() - 1);
        Map<String, TrendItem> dataMap = ListUtils.toMap(execItemList,
                item -> {
                    if (lastGroup == null) {
                        return industryDept + "@" + item.getYearMonth();
                    } else {
                        return lastGroup.getGroupBy().apply(item) + "@" + item.getYearMonth();
                    }
                }, item -> item);

        for (TrendItem item : handlerList) {
            TrendItem exec;
            if (lastGroup == null) {
                exec = dataMap.get(industryDept + "@" + item.getYearMonth());
            } else {
                exec = dataMap.get(lastGroup.getGroupBy().apply(item) + "@" + item.getYearMonth());
            }
            if (exec == null) {
                continue;
            }
            // 匹配到了预测，将值传递给预测，并移除
            execItemList.remove(exec);
            item.setExecTotal(exec.getExecTotal());
            item.setAccuracy(exec.getAccuracy());
            item.setForecastTotalNew532(exec.getForecastTotalNew532());
        }
        if (ListUtils.isNotEmpty(execItemList)) {
            // 只有执行，没有预测的数据
            handlerList.addAll(execItemList);
        }
    }

    private List<CustomerItem> queryCustomer(Set<String> uinOrShortNameSet, String sql, boolean isByUin) {
        if (ListUtils.isEmpty(uinOrShortNameSet)) {
            return new ArrayList<>();
        }
        List<CustomerItem> infoList = demandDBHelper.getRaw(CustomerItem.class, sql, uinOrShortNameSet);
        if (ListUtils.isEmpty(infoList)) {
            return new ArrayList<>();
        }
        for (CustomerItem item : infoList) {
            if (isByUin) {
                uinOrShortNameSet.remove(item.getCustomerUin());
            } else {
                uinOrShortNameSet.remove(item.getCustomerShortName());
            }
        }
        return infoList;
    }

    private List<CustomerItem> queryCustomerByUinOrShortName(Set<String> uinOrShortName, boolean isByUin) {
        if (ListUtils.isEmpty(uinOrShortName)) {
            return new ArrayList<>();
        }
        String sql = "select customer_uin, customer_short_name \n"
                + "from ppl_order\n"
                + "where "
                + (isByUin ? " customer_uin in (?)\n" : " customer_short_name in (?)\n")
                + "group by customer_uin, customer_short_name ";
        Set<String> uinOrShortNameSet = new HashSet<>(uinOrShortName);
        List<CustomerItem> list = queryCustomer(uinOrShortNameSet, sql, isByUin);
        if (ListUtils.isNotEmpty(uinOrShortNameSet)) {
            sql = "select customer_uin, customer_short_name \n"
                    + "from ppl_order_draft\n"
                    + "where "
                    + (isByUin ? " customer_uin in (?)\n" : " customer_short_name in (?)\n")
                    + "group by customer_uin, customer_short_name ";
            List<CustomerItem> other = queryCustomer(uinOrShortNameSet, sql, isByUin);
            if (ListUtils.isNotEmpty(other)) {
                list.addAll(other);
            }
        }
        return list;
    }

    private List<TrendItem> mapToTrendItem(Collection<Map> mapList, boolean useCoreDayExec,
            boolean useMonthAvgExec, boolean needAccuracy, java.time.YearMonth start, java.time.YearMonth end,
            HistoricalTrendGroup groupName, String demandType) {
        List<TrendItem> result = new ArrayList<>();
        if (ListUtils.isEmpty(mapList) || start == null || end == null || start.isAfter(end)) {
            return result;
        }
        BigDecimal thirty = new BigDecimal("30");
        for (Map<Object, Object> map : mapList) {
            if (ListUtils.isEmpty(map)) {
                continue;
            }
            java.time.YearMonth begin = java.time.YearMonth.of(start.getYear(), start.getMonthValue());
            while (!end.isBefore(begin)) {
                TrendItem item = new TrendItem();
                item.setCustomerShortName(MapUtils.getString(map,
                        HistoricalTrendGroup.customerShortName.getSelectFieldName()));
                item.setRegionName(MapUtils.getString(map, HistoricalTrendGroup.regionName.getSelectFieldName()));
                item.setWarZone(MapUtils.getString(map, HistoricalTrendGroup.warZone.getSelectFieldName()));
                item.setInstanceType(MapUtils.getString(map, HistoricalTrendGroup.instanceType.getSelectFieldName()));
                item.setYearMonth(TrendItem.YEAR_MONTH_FORMAT.format(begin));
                item.setDemandType(demandType);
                if (groupName == null) {
                    item.setSummary(true);
                } else {
                    item.setGroupValue(groupName.getGroupBy().apply(item));
                    item.setGroupName(groupName);
                    item.setSummary(false);
                }
                if (useCoreDayExec) {
                    // gpu不需要核天执行量
                    // 审批这边看到的核天执行量需要除以月天数，暂时都除以30
                    // 核天口径执行量  month_distinct_bill_core_by_day_num@2023-09
                    Object coreDayExec = map.get("month_distinct_bill_core_by_day_num@" + item.getYearMonth());
                    item.setExecTotal(coreDayExec == null ? null
                            : (new BigDecimal(coreDayExec.toString()).divide(thirty, 2, RoundingMode.HALF_UP)));
                } else if (useMonthAvgExec) {
                    // gpu使用月均口径执行量
                    Object monthAvgExec = map.get("month_avg_distinct_change_bill_num@" + item.getYearMonth());
                    item.setExecTotal(monthAvgExec == null ? null : new BigDecimal(monthAvgExec.toString()));
                }
                if (needAccuracy) {
                    // cvm才显示核天准确率
                    // 新版532核天准确率  origin_v2_avg_forecast_match_rate_core_by_day@2023-10  0.00%
                    Object coreDayAccuracy = map.get("origin_v2_avg_forecast_match_rate_core_by_day@"
                            + item.getYearMonth());
                    item.setAccuracy(coreDayAccuracy == null ? null : coreDayAccuracy.toString());
                }
                // 新532 需求预测量
                Object forecastTotalNew532 = map.get("origin_v2_avg_forecast_num@" + item.getYearMonth());
                item.setForecastTotalNew532(forecastTotalNew532 == null ? null
                        : new BigDecimal(forecastTotalNew532.toString()));
                result.add(item);
                begin = begin.plusMonths(1);
            }
        }
        return result;
    }

    private List<TrendItem> queryCurrentFromMysql(HistoricalTrendReq params, String industryDept,
            PermissionDTO permission) {
        List<CustomerDemandInfo> list;
        if (params.isPreSubmit()) {
            list = queryCurrentFromMysqlPreSubmit(params, industryDept, permission, false);
        } else {
            list = queryCurrentFromMysqlNotPreSubmit(params, industryDept, permission, false);
        }
        if (ListUtils.isNotEmpty(params.getCustomhouseTitle())) {
            // 境内外赋值与过滤
            fillerService.fill(list);
            list.removeIf(item -> item == null || !params.getCustomhouseTitle().contains(item.getCustomhouseTitle()));
        }
        return HistoricalTrendsResp.group(list, params, true);
    }

    private Pair<Integer, Integer> forecast13WeekOutside(HistoricalTrendReq params, String industryDept,
            PermissionDTO permission) {
        List<CustomerDemandInfo> list;
        if (params.isPreSubmit()) {
            list = queryCurrentFromMysqlPreSubmit(params, industryDept, permission, true);
        } else {
            list = queryCurrentFromMysqlNotPreSubmit(params, industryDept, permission, true);
        }
        if (ListUtils.isNotEmpty(params.getCustomhouseTitle())) {
            // 境内外赋值与过滤
            fillerService.fill(list);
            list.removeIf(item -> item == null || !params.getCustomhouseTitle().contains(item.getCustomhouseTitle()));
        }
        int newForecast13WeekOutside = 0;
        int returnForecast13WeekOutside = 0;
        for (CustomerDemandInfo info : list) {
            if (info == null || info.getTotalCoreOrGpuNum() == null) {
                continue;
            }
            if (TrendItem.DEMAND_TYPE_NEW.equals(info.getReturnOrNew())) {
                newForecast13WeekOutside += info.getTotalCoreOrGpuNum();
            } else if (TrendItem.DEMAND_TYPE_RETURN.equals(info.getReturnOrNew())) {
                returnForecast13WeekOutside += info.getTotalCoreOrGpuNum();
            }
        }
        return Pair.of(newForecast13WeekOutside, returnForecast13WeekOutside);
    }

    private List<CustomerDemandInfo> queryCurrentFromMysqlNotPreSubmit(HistoricalTrendReq params, String industryDept,
            PermissionDTO permission, boolean outside13Week) {
        WhereContent permissionWhere = permission.toWhereWithVersionId("t2.",
                industryDept, "ppl_order.");
        if (permissionWhere == null) {
            return new ArrayList<>();
        }
        WhereContent where = params.whereForMysql(outside13Week);
        where.addAnd(permissionWhere);

        Object[] sqlParams = new Object[where.getParams().length + 1];
        sqlParams[0] = ListUtils.newArrayList(params.getVersionId());
        for (int i = 1; i < sqlParams.length; i++) {
            sqlParams[i] = where.getParams()[i - 1];
        }
        String normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_customer_demand_group_mysql.sql");
        String sql = normalSql.replace("${whereCondition}", where.getSql());
        return demandDBHelper.getRaw(CustomerDemandInfo.class, sql, sqlParams);
    }

    private List<CustomerDemandInfo> queryCurrentFromMysqlPreSubmit(HistoricalTrendReq params, String industryDept,
            PermissionDTO permission, boolean outside13Week) {
        WhereContent permissionWhere = permission.toWhereWithOutVersionId(industryDept, "ppl_order.");
        if (permissionWhere == null) {
            return new ArrayList<>();
        }
        WhereContent where = params.whereForMysqlPreSubmit(outside13Week);
        where.addAnd(permissionWhere);

        String normalSql = ORMUtils.getSql(
                "/sql/ppl13week/inner_process/query_customer_demand_group_mysql_pre_submit.sql");
        String sql = normalSql.replace("${whereCondition}", where.getSql());
        return demandDBHelper.getRaw(CustomerDemandInfo.class, sql, where.getParams());
    }

    @Override
    public ForecastConstituteResp queryForecastConstitute(ForecastConstituteReq params) {
        if (params == null || params.getVersionId() == null) {
            throw BizException.makeThrow("请选择版本");
        }
        if (params.getGroupBy() == null) {
            throw BizException.makeThrow("请选择分组条件");
        }
        if (params.getProduct() == null) {
            throw BizException.makeThrow("请选择产品");
        }
        PplInnerProcessVersionDO version = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                params.getVersionId());
        if (version == null) {
            throw BizException.makeThrow("行业审批版本【%s】不存在", params.getVersionId());
        }
        params.indexNameHandler();
        if (params.getIndustryDept() == null) {
            params.setIndustryDept(version.getIndustryDept());
        }
        if (params.getProduct().get(0).equals(Ppl13weekProductTypeEnum.PAAS.getName())) {
            params.setProduct(Ppl13weekProductTypeEnum.getPaasProductNameList());
        }
        String userName = LoginUtils.getUserName();
        PermissionDTO permissionDTO = queryPermission(userName, version.getIndustryDept(),
                ListUtils.newArrayList(params.getVersionId()));
        WhereContent permissionWhere = permissionDTO.toWhereWithOutVersionId(version.getIndustryDept(), "ppl_order.");
        if (permissionWhere == null) {
            // 没有权限
            return params.group(null);
        }

        List<ForecastConstituteDetail> details;
        WhereContent where;
        String normalSql;
        if (params.isPreSubmit()) {
            // 查询草稿单
            normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_constitute_pre_submit.sql");
            normalSql = normalSql.replace("${indexName}", params.getIndexName());
            where = params.whereContentForPreSubmit();
        } else {
            // 查询行业版本审批数据
            normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_constitute.sql");
            normalSql = normalSql.replace("${indexName}", params.getIndexName());
            where = params.whereContent();
        }
        where.addAnd(permissionWhere);
        details = demandDBHelper.getRaw(ForecastConstituteDetail.class,
                normalSql + where.getSql(), where.getParams());
        // 填充境内外字段值
        fillerService.fill(details);
        if (params.getCustomhouseTitle() != null) {
            details.removeIf(item -> !params.getCustomhouseTitle().equals(item.getCustomhouseTitle()));
        }
        // 按分组条件分组计算后返回
        return params.group(details);
    }

    private List<ForecastConstituteDetail> forecastConstituteDetail(ForecastConstituteReq params,
            WhereContent permissionWhere) {
        List<ForecastConstituteDetail> details;
        WhereContent where;
        String normalSql;
        if (params.isPreSubmit()) {
            // 查询草稿单
            normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_constitute_pre_submit.sql");
            where = params.whereContentForPreSubmit();
        } else {
            // 查询行业版本审批数据
            normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_constitute.sql");
            where = params.whereContent();
        }
        where.addAnd(permissionWhere);
        details = demandDBHelper.getRaw(ForecastConstituteDetail.class,
                normalSql + where.getSql(), where.getParams());
        // 填充境内外字段值
        fillerService.fill(details);
        return details;
    }

    @Override
    public ForecastInstanceTypeConstituteResp queryForecastInstanceConstitute(ForecastConstituteReq params) {
        if (params == null || params.getVersionId() == null) {
            throw BizException.makeThrow("请选择版本");
        }
        if (params.getProduct() == null) {
            throw BizException.makeThrow("请选择产品");
        }
        PplInnerProcessVersionDO version = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                params.getVersionId());
        if (version == null) {
            throw BizException.makeThrow("行业审批版本【%s】不存在", params.getVersionId());
        }
        if (params.getProduct().get(0).equals(Ppl13weekProductTypeEnum.PAAS.getName())) {
            params.setProduct(Ppl13weekProductTypeEnum.getPaasProductNameList());
        }
        params.setIndustryDept(version.getIndustryDept());
        Long lastVersionId = null;
        List<Long> versionIdList = queryLastVersionId(params.getVersionId(), params.getIndustryDept(),
                params.getProduct().get(0), 1);
        if (ListUtils.isNotEmpty(versionIdList)) {
            lastVersionId = versionIdList.get(0);
        }
        String userName = LoginUtils.getUserName();
        PermissionDTO permissionDTO = queryPermission(userName, version.getIndustryDept(),
                ListUtils.newArrayList(params.getVersionId()));
        WhereContent permissionWhere = permissionDTO.toWhereWithOutVersionId(version.getIndustryDept(), "ppl_order.");
        if (permissionWhere == null) {
            // 没有权限
            return ForecastInstanceTypeConstituteResp.result(null, null, version);
        }

        WhereContent where;
        String normalSql;
        if (params.isPreSubmit()) {
            // 本周期查询草稿单
            normalSql = ORMUtils.getSql(
                    "/sql/ppl13week/inner_process/query_forecast_instance_constitute_pre_submit.sql");
            where = params.whereContentForPreSubmit();
        } else {
            // 本周期查询行业版本审批数据
            normalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_instance_constitute.sql");
            where = params.whereContent();
        }
        where.addAnd(permissionWhere);
        String sql = normalSql.replace("${whereCondition}", where.getSql());
        List<ForecastInstanceTypeDetail> currentVersion = demandDBHelper
                .getRaw(ForecastInstanceTypeDetail.class, sql, where.getParams());
        // 填充境内外字段值
        fillerService.fill(currentVersion);

        // 上周期查询行业版本审批数据
        String lastNormalSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_forecast_instance_constitute.sql");
        WhereContent lastWhere = params.whereContent(lastVersionId);
        String lastSql = lastNormalSql.replace("${whereCondition}", lastWhere.getSql());
        List<ForecastInstanceTypeDetail> lastVersion = demandDBHelper
                .getRaw(ForecastInstanceTypeDetail.class, lastSql, lastWhere.getParams());
        // 填充境内外字段值
        fillerService.fill(lastVersion);
        return ForecastInstanceTypeConstituteResp.result(currentVersion, lastVersion, version);
    }

    @Override
    public List<ApprovalProductInfoResp> queryApprovalProductInfo(ApprovalProductInfoReq params) {
        if (params == null || Strings.isBlank(params.getIndustryDept())) {
            throw BizException.makeThrow("请选择行业部门");
        }
        List<PplInnerProcessVersionDO> versions;
        if (ListUtils.isEmpty(params.getVersionList())) {
            versions = innerVersionService.queryProcessingVersionByDept(params.getIndustryDept());
            if (ListUtils.isEmpty(versions)) {
                throw BizException.makeThrow("部门【%s】无正在审批流程中的需求", params.getIndustryDept());
            }
        } else {
            versions = innerVersionService.queryVersionInfoByIdList(params.getIndustryDept(), params.getVersionList());
            if (ListUtils.isEmpty(versions)) {
                throw BizException.makeThrow("部门【%s】下未查询到id在【%s】内的历史审批记录",
                        params.getIndustryDept(), JSON.toJson(params.getVersionList()));
            }
        }

        List<ApprovalProductInfoResp> result = new ArrayList<>();
        for (PplInnerProcessVersionDO version : versions) {
            if (version == null || version.getId() == null) {
                continue;
            }
            boolean inProcess = PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(version.getStatus());
            List<String> productList = PplInnerProcessVersionVO.splitProductFieldToProductList(version);
            for (String product : productList) {
                if (Strings.isBlank(product)) {
                    continue;
                }
                ApprovalProductInfoResp item = new ApprovalProductInfoResp(version, product);
                item.setVersionStatus(version.getStatus());
                if (!inProcess) {
                    // 不在进行的流程中，则无需处理
                    item.setStatusName(StatusNameConstant.STATUS_NOT_NEED_HANDLE);
                }
                result.add(item);
            }
        }
        if (ListUtils.isNotEmpty(result)) {
            fillLastVersion(result, params.getIndustryDept());
            List<Long> allVersionIdList = new ArrayList<>();
            for (ApprovalProductInfoResp resp : result) {
                if (resp == null) {
                    continue;
                }
                if (resp.getVersionId() != null && !allVersionIdList.contains(resp.getVersionId())) {
                    allVersionIdList.add(resp.getVersionId());
                }
                if (resp.getLastVersionId() != null && !allVersionIdList.contains(resp.getLastVersionId())) {
                    allVersionIdList.add(resp.getLastVersionId());
                }
                if (resp.getPreLastVersionId() != null && !allVersionIdList.contains(resp.getPreLastVersionId())) {
                    allVersionIdList.add(resp.getPreLastVersionId());
                }
            }
            String user = LoginUtils.getUserName();
            PermissionDTO permission = queryPermission(user, params.getIndustryDept(), allVersionIdList);

            // 填充当前审批节点
            fillInnerProcessCurrentNode(result, permission, params);
            fillStatusName(result);

            List<ApprovalProductInfoResp> preSubmits = new ArrayList<>();
            for (ApprovalProductInfoResp resp : result) {
                if (resp == null) {
                    continue;
                }
                if (PplInnerProcessVersionStatusEnum.DONE.getCode().equals(resp.getVersionStatus())) {
                    // 已完结的历史版本，审批节点固定为:审批已完成
                    resp.setApprovalNode(PplInnerProcessVersionStatusEnum.DONE.getCode());
                    resp.setApprovalNodeName("审批已完成");
                }
                if (StatusNameConstant.STATUS_PRE_SUBMIT.equals(resp.getStatusName())) {
                    preSubmits.add(resp);
                }
            }

            List<DemandNumByCustomerAndInstanceType> preSubmitCustomerDemands = null;
            List<DemandNumByCustomerAndInstanceType> preSubmitDelayCustomerDemands = null;
            if (ListUtils.isNotEmpty(preSubmits)) {
                // 根据ppl草稿单查询
                Pair<List<DemandNumByCustomerAndInstanceType>, List<DemandNumByCustomerAndInstanceType>> pair = queryNewDemandCustomerTotalForPreSubmit(
                        preSubmits,
                        permission, params);
                preSubmitCustomerDemands = pair.getKey();
                preSubmitDelayCustomerDemands = pair.getValue();
            }
            fillDemandChangeInfo(result, permission, preSubmitCustomerDemands, preSubmitDelayCustomerDemands, params);
        }
        ApprovalProductInfoResp.resultHandler(result);
        buildApprovalIndex(result);
        return result;
    }

    private void buildApprovalIndex(List<ApprovalProductInfoResp> result) {
        if (ListUtils.isEmpty(result)) {
            return;
        }
        List<Long> versionIds = result.stream().map(ApprovalProductInfoResp::getVersionId).distinct()
                .collect(Collectors.toList());
        Map<Long, List<PplInnerProcessVersionSlaDO>> versionToSlaList = new HashMap<>();
        for (Long versionId : versionIds) {
            List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaList(versionId);
            versionToSlaList.put(versionId, versionSlaList);
        }

        for (ApprovalProductInfoResp approvalProductInfoResp : result) {
            List<PplInnerProcessVersionSlaDO> slaDOS = versionToSlaList.get(approvalProductInfoResp.getVersionId());
            if (ListUtils.isEmpty(slaDOS)) {
                continue;
            }
            for (int i = 0; i < slaDOS.size(); i++) {
                if (approvalProductInfoResp.getApprovalNode().equals(slaDOS.get(i).getNodeCode())) {
                    approvalProductInfoResp.setApprovalIndex(i);
                }
            }
        }
    }

    private void fillInnerProcessCurrentNode(List<ApprovalProductInfoResp> list, PermissionDTO permission,
            ApprovalProductInfoReq params) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        List<Long> versionIdList = ListUtils.transform(list, ApprovalProductInfoResp::getVersionId);
        if (ListUtils.isEmpty(versionIdList)) {
            return;
        }
        versionIdList.removeIf(Objects::isNull);
        if (ListUtils.isEmpty(versionIdList)) {
            return;
        }
//        WhereContent permissionWhere = permission.toWhereWithVersionId("t5.", params.getIndustryDept(),
//                "t4.", versionIdList);
//        if (permissionWhere == null) {
//            // 没有权限
//            throw BizException.makeThrow("没有相关行业战区权限");
//        }
//        params.bizRangeHandler(permissionWhere, "t4.");
        // 查询审批节点时不做权限过滤，不然在权限范围内没有PPL数据时会导致获取不到审批节点，从而显示需求沟通环节
        WhereContent permissionWhere = new WhereContent();
        // 查询审批进行中的
        String waitStatusSql = ORMUtils.getSql("/sql/ppl13week/inner_process/ppl_inner_process_current_node.sql");
        waitStatusSql = waitStatusSql.replace("${permission}", permissionWhere.getSql());
        Object[] waitStatusSqlParams = new Object[permissionWhere.getParams().length + 1];
        waitStatusSqlParams[waitStatusSqlParams.length - 1] = versionIdList;
        for (int i = 0; i < waitStatusSqlParams.length - 1; i++) {
            waitStatusSqlParams[i] = permissionWhere.getParams()[i];
        }
        List<VersionAndNode> waitStatusRes = DBList.demandDBHelper
                .getRaw(VersionAndNode.class, waitStatusSql, waitStatusSqlParams);
        Map<Long, VersionAndNode> waitStatusMap = ListUtils.toMap(waitStatusRes, VersionAndNode::getVersionId,
                item -> item);

        // 查询其他的
        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/ppl_inner_process_current_node_2.sql");
        sql = sql.replace("${permission}", permissionWhere.getSql());
        Object[] sqlParams = new Object[permissionWhere.getParams().length + 1];
        sqlParams[sqlParams.length - 1] = versionIdList;
        for (int i = 0; i < sqlParams.length - 1; i++) {
            sqlParams[i] = permissionWhere.getParams()[i];
        }
        List<VersionAndNode> res = DBList.demandDBHelper.getRaw(VersionAndNode.class, sql, sqlParams);
        Map<Long, VersionAndNode> map = ListUtils.toMap(res, VersionAndNode::getVersionId, item -> item);

        if (waitStatusMap == null) {
            waitStatusMap = new HashMap<>();
        }
        if (map == null) {
            map = new HashMap<>();
        }

        for (ApprovalProductInfoResp filler : list) {
            if (filler.getVersionId() == null) {
                continue;
            }
            if (PplInnerProcessVersionStatusEnum.DONE.getCode().equals(filler.getVersionStatus())) {
                // 已完结的历史版本，审批节点固定为:审批已完成
                filler.setApprovalNode(PplInnerProcessVersionStatusEnum.DONE.getCode());
                filler.setApprovalNodeName("审批已完成");
                continue;
            }
            // 审批进行中的
            VersionAndNode current = waitStatusMap.get(filler.getVersionId());
            if (current == null || StringUtils.isBlank(current.getNodeCode())) {
                // 不在审批进行中，则获取其他的
                current = map.get(filler.getVersionId());
            }
            if (current == null || StringUtils.isBlank(current.getNodeCode())) {
                filler.setApprovalNode(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
                filler.setApprovalNodeName(PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
            } else {
                filler.setApprovalNode(current.getNodeCode());
                filler.setApprovalNodeName(
                        StringUtils.isBlank(current.getNodeName()) ? current.getNodeCode() : current.getNodeName());
                filler.setCurrentRoleAttribute(current.getRoleAttribute());
            }
        }
    }

    @Override
    public VersionAndNode queryVersionCurrentWaitNode(Long versionId) {
        // 查询审批进行中的
        String waitStatusSql = ORMUtils.getSql("/sql/ppl13week/inner_process/ppl_inner_process_current_node.sql");
        WhereContent permissionWhere = new WhereContent();
        waitStatusSql = waitStatusSql.replace("${permission}", permissionWhere.getSql());
        Object[] waitStatusSqlParams = new Object[permissionWhere.getParams().length + 1];
        waitStatusSqlParams[waitStatusSqlParams.length - 1] = ListUtils.newArrayList(versionId);
        for (int i = 0; i < waitStatusSqlParams.length - 1; i++) {
            waitStatusSqlParams[i] = permissionWhere.getParams()[i];
        }
        return DBList.demandDBHelper.getRawOne(VersionAndNode.class, waitStatusSql, waitStatusSqlParams);
    }

    private void fillLastVersion(List<ApprovalProductInfoResp> result, String industryDept) {
        for (ApprovalProductInfoResp item : result) {
            if (item == null || item.getVersionId() == null) {
                return;
            }
            lastVersionIdFill(item.getVersionId(), industryDept, item.getProduct(), item);
        }
    }

    @Override
    public PermissionDTO queryPermission(String username, String industryDept, List<Long> versionIds) {
        PermissionDTO result = new PermissionDTO();
        result.setNotAnyPermission(true);
        result.setNotNeedCheckPermission(false);
        if (permissionService.checkIsAdmin(username) ||
                permissionService.isRoleAndAllWarZone(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(),
                        username,industryDept)) {
            // 无需校验权限
            result.setNotAnyPermission(false);
            result.setNotNeedCheckPermission(true);
            return result;
        }
        List<IndustryDemandAuthDO> demandAuthDOList = permissionService.getAllRoleByUsername(username, industryDept);
        if (demandAuthDOList == null || CollectionUtils.isEmpty(demandAuthDOList)) {
            // 没有任何权限
            result.setNotAnyPermission(true);
            return result;
        }
        Map<Long, List<String>> versionRolesMap = innerVersionService.queryVersionsRoles(versionIds);
        Map<String, IndustryDemandAuthDO> roleToAuthMap = demandAuthDOList.stream()
                .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));

        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);
        if (ListUtils.isEmpty(versionRolesMap)) {
            // 无需校验权限
            result.setNotAnyPermission(false);
            result.setNotNeedCheckPermission(true);
            return result;
        }
        boolean hasPermission = false;
        List<PermissionVersionItem> permissions = new ArrayList<>();
        result.setPermissions(permissions);
        for (Entry<Long, List<String>> longListEntry : versionRolesMap.entrySet()) {
            if (longListEntry == null || longListEntry.getKey() == null || longListEntry.getKey() == 0) {
                continue;
            }
            PermissionVersionItem versionPermission = new PermissionVersionItem();
            versionPermission.setVersionId(longListEntry.getKey());
            List<PermissionVersionRoleItem> rolePermissions = new ArrayList<>();
            versionPermission.setRolePermissions(rolePermissions);
            permissions.add(versionPermission);
            List<String> roleList = longListEntry.getValue();
            if (ListUtils.isEmpty(roleList)) {
                continue;
            }
            for (String role : roleList) {
                // roleList，部门审批流相关审批角色,根据审批顺序从后往前排序，与权限大小也相同
                IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(role);
                if (industryDemandAuthDO != null) {
                    PermissionVersionRoleItem roleItem = new PermissionVersionRoleItem();
                    if (!Strings.isEmpty(industryDemandAuthDO.getIndustry())
                            && industryDemandAuthDO.getIndustry().contains(industryDept)) {
                        hasPermission = true;
                        roleItem.setIndustryDept(industryDept);
                        rolePermissions.add(roleItem);
                    } else {
                        // 如果当前角色没有该行业的权限则直接进行下个角色的校验
                        continue;
                    }
                    if (!Strings.isEmpty(industryDemandAuthDO.getWarZoneName())) {
                        roleItem.setWarZoneList(
                                ListUtils.transform(industryDemandAuthDO.getWarZoneName().split(";"), o -> o));
                    }
                    if (!Strings.isEmpty(industryDemandAuthDO.getCommonCustomerName())) {
                        List<String> commonCustomerList = Arrays.asList(
                                industryDemandAuthDO.getCommonCustomerName().split(";"));
                        if (ListUtils.isNotEmpty(commonCustomerList)) {
                            List<String> collect = customerDict.stream().filter(commonCustomerList::contains)
                                    .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                    .collect(Collectors.toList());
                            roleItem.setCustomerShortNameList(collect);
                        }
                    }
                }
            }
        }
        if (hasPermission) {
            result.setNotAnyPermission(false);
        }
        return result;

    }

    private WhereContent permissionWithVersionId(String username, String industryDept, String pplOrderTablePrefix,
            List<Long> versionIds, String versionIdTableNamePrefix) {
        WhereContent whereContent = new WhereContent();
        if (permissionService.checkIsAdmin(username)) {
            whereContent.addAnd("1 = 1");
            return whereContent;
        }
        if (pplOrderTablePrefix == null) {
            pplOrderTablePrefix = "";
        }
        if (versionIdTableNamePrefix == null) {
            versionIdTableNamePrefix = "";
        }
        List<IndustryDemandAuthDO> demandAuthDOList = permissionService.getAllRoleByUsername(username, industryDept);
        if (demandAuthDOList == null || CollectionUtils.isEmpty(demandAuthDOList)) {
            return null;
        }
        Map<Long, List<String>> versionRolesMap = innerVersionService.queryVersionsRoles(versionIds);
        Map<String, IndustryDemandAuthDO> roleToAuthMap = demandAuthDOList.stream()
                .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));
        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);
        if (ListUtils.isEmpty(versionRolesMap)) {
            whereContent.addAnd("1 = 1");
            return whereContent;
        }
        for (Entry<Long, List<String>> longListEntry : versionRolesMap.entrySet()) {
            if (longListEntry == null || longListEntry.getKey() == null || longListEntry.getKey() == 0) {
                continue;
            }
            WhereContent roleContent = new WhereContent();
            roleContent.addAnd(versionIdTableNamePrefix + "version_id = ?", longListEntry.getKey());
            List<String> roleList = longListEntry.getValue();
            if (ListUtils.isEmpty(roleList)) {
                roleContent.addAnd("1 = 1");
                whereContent.addOr(roleContent);
                continue;
            }
            for (String role : roleList) {
                // roleList，部门审批流相关审批角色,根据审批顺序从后往前排序，与权限大小也相同
                IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(role);
                if (industryDemandAuthDO != null) {
                    if (!Strings.isEmpty(industryDemandAuthDO.getIndustry()) &&
                            industryDemandAuthDO.getIndustry().contains(industryDept)) {
                        roleContent.addAnd(pplOrderTablePrefix + "industry_dept = ?", industryDept);
                    } else {
                        // 如果当前角色没有该行业的权限则直接进行下个角色的校验
                        continue;
                    }
                    if (!Strings.isEmpty(industryDemandAuthDO.getWarZoneName())) {
                        roleContent.addAnd(pplOrderTablePrefix + "war_zone in (?)",
                                ListUtils.transform(industryDemandAuthDO.getWarZoneName().split(";"), o -> o));
                    }
                    if (!Strings.isEmpty(industryDemandAuthDO.getCommonCustomerName())) {
                        List<String> commonCustomerList = Arrays.asList(
                                industryDemandAuthDO.getCommonCustomerName().split(";"));
                        if (ListUtils.isNotEmpty(commonCustomerList)) {
                            List<String> collect = customerDict.stream().filter(commonCustomerList::contains)
                                    .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                    .collect(Collectors.toList());
                            roleContent.addAnd(pplOrderTablePrefix + "customer_short_name in (?)", collect);
                        }
                    }
                }
            }
            whereContent.addOr(roleContent);
        }
        if (whereContent.getParams().length == 0) {
            // 代表无任何权限
            return null;
        }
        return whereContent;

    }

    private Pair<List<DemandNumByCustomerAndInstanceType>, List<DemandNumByCustomerAndInstanceType>> queryNewDemandCustomerTotalForPreSubmit(
            List<ApprovalProductInfoResp> preSubmits,
            PermissionDTO permissionDTO, ApprovalProductInfoReq params) {
        if (ListUtils.isEmpty(preSubmits)) {
            return Pair.of(null, null);
        }
        String industryDept = params.getIndustryDept();
        Map<String, Long> productVersionIdMap = new HashMap<>();
        List<String> productList = new ArrayList<>();
        for (ApprovalProductInfoResp preSubmit : preSubmits) {
            productVersionIdMap.put(preSubmit.getProduct(), preSubmit.getVersionId());
            productList.add(preSubmit.getProduct());
        }
        if (ListUtils.isEmpty(productList)) {
            return Pair.of(null, null);
        }
        WhereContent permission = permissionDTO.toWhereWithOutVersionId(industryDept, "t2.");
        if (permission == null) {
            // 没有权限
            return Pair.of(null, null);
        }
        params.bizRangeHandler(permission, "t2.");
        Object[] sqlParams = new Object[permission.getParams().length + 2];
        for (int i = 0; i < sqlParams.length - 2; i++) {
            sqlParams[i] = permission.getParams()[i];
        }
        sqlParams[permission.getParams().length] = productList;
        sqlParams[permission.getParams().length + 1] = industryDept;

        if (!params.isNotFilterPpl()) {
            // 获取当前周期的ppl
            String pplOrderSql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_pre_submit_draft_ppl_id.sql");
            pplOrderSql = pplOrderSql.replace("${permission}", permission.getSql());
            List<String> pplOrderList = demandDBHelper.getRaw(String.class, pplOrderSql, sqlParams);
            params.addFilterPpl(pplOrderList);
        }

        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_pre_submit_draft_demand_customer_total.sql");
        sql = sql.replace("${permission}", permission.getSql());
        List<DemandNumByCustomerAndInstanceType> demandList = demandDBHelper
                .getRaw(DemandNumByCustomerAndInstanceType.class, sql, sqlParams);
        if (ListUtils.isNotEmpty(demandList)) {
            for (DemandNumByCustomerAndInstanceType customer : demandList) {
                customer.setVersionId(productVersionIdMap.get(customer.getProduct()));
            }
        }

        String expiredSql = ORMUtils.getSql(
                        "/sql/ppl13week/inner_process/query_pre_submit_draft_demand_customer_delay_total.sql")
                .replace("${permission}", permission.getSql());
        List<DemandNumByCustomerAndInstanceType> delayDemandList = demandDBHelper
                .getRaw(DemandNumByCustomerAndInstanceType.class, expiredSql, sqlParams);
        if (ListUtils.isNotEmpty(delayDemandList)) {
            for (DemandNumByCustomerAndInstanceType customer : delayDemandList) {
                customer.setVersionId(productVersionIdMap.get(customer.getProduct()));
            }
        }

        return Pair.of(demandList, delayDemandList);
    }

    private void fillDemandChangeInfo(List<ApprovalProductInfoResp> result, PermissionDTO permissionDTO,
            List<DemandNumByCustomerAndInstanceType> preSubmitCustomerDemands,
            List<DemandNumByCustomerAndInstanceType> preSubmitDelayCustomerDemands, ApprovalProductInfoReq params) {
        List<Long> currentVersionIds = result.stream()
                .filter(item -> item != null && item.getVersionId() != null
                        && !StatusNameConstant.STATUS_PRE_SUBMIT.equals(item.getStatusName())) // 预提交的不用在此处查询
                .map(ApprovalProductInfoResp::getVersionId).collect(Collectors.toList());

        List<Long> lastVersionIds = ListUtils.transform(result, ApprovalProductInfoResp::getLastVersionId);
        lastVersionIds.removeIf(Objects::isNull);
        List<Long> preLastVersionIds = ListUtils.transform(result, ApprovalProductInfoResp::getPreLastVersionId);
        preLastVersionIds.removeIf(Objects::isNull);
        List<Long> allVersionIds = new ArrayList<>(currentVersionIds);
        allVersionIds.addAll(lastVersionIds);
        allVersionIds.addAll(preLastVersionIds);

        String industryDept = params.getIndustryDept();
        WhereContent permission = permissionDTO.toWhereWithVersionId("t2.", industryDept,
                "t3.", allVersionIds);
        if (permission == null) {
            // 没有权限
            return;
        }
        params.bizRangeHandler(permission, "t3.");
        // 获取当前周期的ppl
        addVersionPplId(params, permissionDTO, currentVersionIds);
        List<DemandNumByCustomerAndInstanceType> demandList = new ArrayList<>();
        List<DemandNumByCustomerAndInstanceType> delayDemandList = new ArrayList<>();
        if (ListUtils.isNotEmpty(allVersionIds)) {
            // 只查询当前周期存在的ppl
            params.filterPplId(permission, "t1.");
            String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_version_new_demand_total.sql");
            sql = sql.replace("${permission}", permission.getSql());
            Object[] demandSqlParams = new Object[permission.getParams().length + 1];
            demandSqlParams[0] = allVersionIds;
            for (int i = 1; i < demandSqlParams.length; i++) {
                demandSqlParams[i] = permission.getParams()[i - 1];
            }
            demandList = demandDBHelper.getRaw(DemandNumByCustomerAndInstanceType.class, sql, demandSqlParams);

            String delaySql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_version_new_demand_delay_total.sql");
            delaySql = delaySql.replace("${permission}", permission.getSql());
            delayDemandList = demandDBHelper.getRaw(DemandNumByCustomerAndInstanceType.class, delaySql,
                    demandSqlParams);
        }
        if (ListUtils.isNotEmpty(preSubmitCustomerDemands)) {
            // 加上需求沟通阶段查询预提交的草稿单结果
            demandList.addAll(preSubmitCustomerDemands);
        }
        if (ListUtils.isNotEmpty(preSubmitDelayCustomerDemands)) {
            // 加上需求沟通阶段查询预提交的草稿单延期需求结果
            delayDemandList.addAll(preSubmitDelayCustomerDemands);
        }

        // 按 过期年月 聚合处理 延期需求量
        ApprovalProductInfoResp.handleTotalDelayDemand(result, delayDemandList);

        // 当前周期versionId集合
        currentVersionIds = result.stream()
                .filter(item -> item != null && item.getVersionId() != null)
                .map(ApprovalProductInfoResp::getVersionId).collect(Collectors.toList());
        List<DemandNumByCustomerAndInstanceType> currentList = new ArrayList<>(); // 当前版本数据
        List<DemandNumByCustomerAndInstanceType> lastList = new ArrayList<>(); // 上个版本的数据
        List<DemandNumByCustomerAndInstanceType> preLastList = new ArrayList<>(); // 上上个版本的数据
        for (DemandNumByCustomerAndInstanceType item : demandList) {
            if (item == null || item.getVersionId() == null) {
                continue;
            }
            if (currentVersionIds.contains(item.getVersionId())) {
                currentList.add(item);
            }
            if (lastVersionIds.contains(item.getVersionId())) {
                lastList.add(item);
            }
            if (preLastVersionIds.contains(item.getVersionId())) {
                preLastList.add(item);
            }
        }

        // （currentVersionId -> (product -> lastVersionId)）的map
        Map<Long, Map<String, Long>> currentWithLastIdMap = new HashMap<>();
        for (ApprovalProductInfoResp item : result) {
            Map<String, Long> productVersion = currentWithLastIdMap.getOrDefault(item.getVersionId(), new HashMap<>());
            productVersion.put(item.getProduct(), item.getLastVersionId());
            currentWithLastIdMap.put(item.getVersionId(), productVersion);
        }
        // 处理当前版本较上周期版本的变化数据
        ApprovalProductInfoResp.handleCustomerChangeDemand(result, currentList, false, lastList,
                currentWithLastIdMap);
        ApprovalProductInfoResp.handleInstanceChangeDemand(result, currentList, lastList, currentWithLastIdMap);

        // （lastVersionId -> (product -> preLastVersionId)）的map
        Map<Long, Map<String, Long>> lastWithPreLastIdMap = new HashMap<>();
        for (ApprovalProductInfoResp item : result) {
            Map<String, Long> productVersion = lastWithPreLastIdMap.getOrDefault(item.getLastVersionId(),
                    new HashMap<>());
            productVersion.put(item.getProduct(), item.getPreLastVersionId());
            lastWithPreLastIdMap.put(item.getLastVersionId(), productVersion);
        }
        // 处理上周期版本较上上周期版本的变化数据
        ApprovalProductInfoResp.handleCustomerChangeDemand(result, lastList, true, preLastList,
                lastWithPreLastIdMap);
    }

    private void addVersionPplId(ApprovalProductInfoReq params, PermissionDTO permissionDTO, List<Long> versions) {
        if (params.isNotFilterPpl()) {
            return;
        }
        String industryDept = params.getIndustryDept();
        WhereContent permission = permissionDTO.toWhereWithVersionId("t2.", industryDept,
                "t3.", versions);
        if (permission == null) {
            return;
        }
        params.bizRangeHandler(permission, "t3.");
        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_version_ppl_id.sql");
        sql = sql.replace("${permission}", permission.getSql());
        Object[] sqlParams = new Object[permission.getParams().length + 1];
        sqlParams[0] = versions;
        for (int i = 1; i < sqlParams.length; i++) {
            sqlParams[i] = permission.getParams()[i - 1];
        }
        List<String> pplOrderList = demandDBHelper.getRaw(String.class, sql, sqlParams);
        params.addFilterPpl(pplOrderList);
    }

    private void lastVersionIdFill(Long versionId, String industryDept, String product, ApprovalProductInfoResp
            item) {
        List<Long> idList = queryLastVersionId(versionId, industryDept, product, 2);
        if (ListUtils.isNotEmpty(idList)) {
            Long first = idList.get(0);
            Long second = idList.size() > 1 ? idList.get(1) : null;
            boolean setFirst = false;
            if (first != null && first > 0) {
                item.setLastVersionId(first);
                setFirst = true;
            }
            if (second != null && second > 0) {
                if (setFirst) {
                    item.setPreLastVersionId(second);
                } else {
                    item.setLastVersionId(second);
                }
            }
        }

    }

    private List<Long> queryLastVersionId(Long versionId, String industryDept, String product, int limit) {
        if (limit < 0) {
            return new ArrayList<>();
        }
        String notLike = "notLike";
        if (product.equals(Ppl13weekProductTypeEnum.BM.getName())) {
            notLike = Ppl13weekProductTypeEnum.GPU.getName();
        }

        String sql = " select id from ppl_inner_process_version "
                + " where industry_dept = ? and product like ? and product not like ? and deleted = 0 and id < ? "
                + " order by id desc limit " + limit;

        return demandDBHelper.getRaw(Long.class, sql, industryDept, "%" + product + "%", "%" + notLike + "%",
                versionId);
    }

    private void fillStatusName(List<ApprovalProductInfoResp> result) {
        List<Long> noSatusNameList = new ArrayList<>(); // 暂时没有状态名称的版本id集合
        for (ApprovalProductInfoResp item : result) {
            if (PplInnerProcessNodeEnum.PRE_SUBMIT.getCode().equals(item.getApprovalNode())) {
                // 需求沟通阶段
                item.setStatusName(StatusNameConstant.STATUS_PRE_SUBMIT);
            }
            if (Strings.isBlank(item.getStatusName())) {
                noSatusNameList.add(item.getVersionId());
            }
        }
        if (ListUtils.isEmpty(noSatusNameList)) {
            return;
        }
        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/query_wait_audit_version_id.sql");
        String user = LoginUtils.getUserName();
        // 在暂时没有状态名称的版本id集合中查询出为待处理状态的版本id、产品
        List<Map> waitHandle = demandDBHelper.getRaw(Map.class, sql, "%" + user + "%", noSatusNameList);
        for (ApprovalProductInfoResp item : result) {
            if (Strings.isBlank(item.getStatusName())) {
                boolean flag = true;
                for (Map map : waitHandle) {
                    if (item.getVersionId().toString().equals(MapUtils.getString(map, "version_id"))
                            && item.getProduct().equals(MapUtils.getString(map, "product"))) {
                        item.setStatusName(StatusNameConstant.STATUS_WAIT_HANDLE);
                        flag = false;
                        break;
                    }
                }
                if (flag) {
                    item.setStatusName(StatusNameConstant.STATUS_NOT_NEED_HANDLE);
                }
            }
        }
    }

    @Override
    public NewAuditOverviewResp queryHistoryOverview(QueryAuditOverviewReq req) throws InterruptedException {
        NewAuditOverviewResp newAuditOverviewResp = new NewAuditOverviewResp();
        List<QueryAuditOverviewReq> queryAuditOverviewReqs = buildOverviewReqList(req);
        List<Long> versionIds = queryAuditOverviewReqs.stream().map(QueryAuditOverviewReq::getVersionId)
                .collect(Collectors.toList());
        List<QueryAuditOverviewResp> resultList = new ArrayList<>();
        String userName = LoginUtils.getUserNameWithSystem();
        WhereContent where = checkPermission(userName, req.getIndustryDept(), "t1.", req.getVersionId());
        if (where == null) {
            return newAuditOverviewResp;
        }
        // 说明：能出现在record中且使用了该versionId的，就已经是需要的数据，所以这里不需要加industry_dept的条件
        where.addAnd("t2.version_id in (?)", versionIds);
        where.groupBy("t2.version_id,t2.ppl_order");
        // 2. 查询出所有的pplOrder
        List<PplOrderWithAuditRecordVO> pplOrdersWithRecord =
                demandDBHelper.getAll(PplOrderWithAuditRecordVO.class, where.getSql(), where.getParams());
        List<PplOrderAuditRecordDO> allReocrd = pplOrdersWithRecord.stream()
                .map(PplOrderWithAuditRecordVO::getPplOrderRecordDO).collect(Collectors.toList());
        Map<Long, Set<String>> versionIdToPpl = allReocrd.stream()
                .collect(Collectors.groupingBy(PplOrderAuditRecordDO::getVersionId,
                        Collectors.mapping(PplOrderAuditRecordDO::getPplOrder, Collectors.toSet())));

        // 看看是否需要查草稿ppl
        IndustryVersionRsp requestVersion = innerVersionService.getVersionDetailById(req.getVersionId());

        if (requestVersion.getStatus().equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode())
                && (requestVersion.getSlaDOS().get(0).getDeadlineTime() == null
                || requestVersion.getSlaDOS().get(0).getDeadlineTime().after(new Date()))) {
            WhereContent draftSql = checkPermission(userName, req.getIndustryDept(), "",
                    req.getVersionId());
            draftSql.andEqual(PplOrderDraftDO::getDraftStatus, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            draftSql.andEqual(PplOrderDraftDO::getIndustryDept, req.getIndustryDept());
            // 修改为 draft_status
            List<PplItemDraftDO> all = demandDBHelper.getAll(PplItemDraftDO.class,
                    "where draft_status = ? and product in (?)",
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                    Arrays.asList(requestVersion.getProduct().split(";")));
            if (!CollectionUtils.isEmpty(all)) {
                List<String> pplOrder = all.stream().map(PplItemDraftDO::getPplOrder).distinct()
                        .collect(Collectors.toList());
                draftSql.andIn(PplOrderDraftDO::getPplOrder, pplOrder);
                List<PplOrderDraftDO> orders = demandDBHelper.getAll(PplOrderDraftDO.class, draftSql.getSql(),
                        draftSql.getParams());

                Set<String> filterPplOrder = orders.stream().map(PplOrderDraftDO::getPplOrder)
                        .collect(Collectors.toSet());
                versionIdToPpl.put(req.getVersionId(), filterPplOrder);
            }
        }
        CountDownLatch countDownLatch = new CountDownLatch(queryAuditOverviewReqs.size());
        for (QueryAuditOverviewReq queryAuditOverviewReq : queryAuditOverviewReqs) {
            fiberTaskExecutor.getExecutorService().execute(() -> {
                Set<String> versionPplOrder = versionIdToPpl.get(queryAuditOverviewReq.getVersionId());
                if (CollectionUtils.isEmpty(versionPplOrder)) {
                    QueryAuditOverviewResp queryAuditOverviewResp = new QueryAuditOverviewResp();
                    queryAuditOverviewResp.setVersionId(queryAuditOverviewReq.getVersionId());
                    queryAuditOverviewResp.setIndustryVersionCode(queryAuditOverviewReq.getVersionCode());
                    resultList.add(queryAuditOverviewResp);
                    countDownLatch.countDown();
                    return;
                }

                queryAuditOverviewReq.setPplOrder(new ArrayList<>(versionPplOrder));
                QueryAuditOverviewResp queryAuditOverviewResp = queryAuditOverview(queryAuditOverviewReq);
                queryAuditOverviewResp.setVersionId(queryAuditOverviewReq.getVersionId());
                queryAuditOverviewResp.setIndustryVersionCode(queryAuditOverviewReq.getVersionCode());
                resultList.add(queryAuditOverviewResp);
                countDownLatch.countDown();
            });
        }
        boolean r = countDownLatch.await(5, TimeUnit.SECONDS);
        if (!r) {
            throw new BizException("查询超时");
        }

        List<QueryAuditOverviewResp> currentVersion = resultList.stream()
                .filter(v -> !ObjectUtils.isEmpty(v) && v.getVersionId() != null && v.getVersionId()
                        .equals(req.getVersionId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(currentVersion)) {
            return newAuditOverviewResp;
        }
        newAuditOverviewResp.setCurrentOverview(currentVersion.get(0));
        newAuditOverviewResp.setHistoryOverview(resultList.stream()
                .filter(v -> (v != null && v.getVersionId() != null && !v.getVersionId().equals(req.getVersionId())))
                .collect(Collectors.toList()));
        return newAuditOverviewResp;
    }

    public QueryRateWithAuthRsp queryRateWithAuth(QueryRateWithAuth req) throws Exception {
        QueryRateWithAuthRsp rsp = new QueryRateWithAuthRsp();
        rsp.setAimAccuracy(aimAccuracy());
        Long currentVersionId = req.getVersionId();

        PplInnerProcessVersionDO currentVersion = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                currentVersionId);
        // 如果是进行中的版本则取当月， 如果是历史版本则取进入版本的时间
        Boolean isTest = Strings.isNotBlank(testEnv.get());
        LocalDate date = null;
        if (isTest) {
            // 测试环境部分月份没数据先写死
            date = LocalDate.parse("2024-02-01");
        } else {
            date = currentVersion.getStatus().equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode())
                    ? LocalDate.now() : currentVersion.getBeginDate();
        }

        LocalDate start = date.minusMonths(3);
        String startYearMonth = start.getYear() + "-" + (start.getMonthValue() > 9 ? start.getMonthValue()
                : "0" + start.getMonthValue());
        LocalDate mid = date.minusMonths(2);
        String midYearMonth =
                mid.getYear() + "-" + (mid.getMonthValue() > 9 ? mid.getMonthValue() : "0" + mid.getMonthValue());
        LocalDate end = date.minusMonths(1);
        String endYearMonth =
                end.getYear() + "-" + (end.getMonthValue() > 9 ? end.getMonthValue() : "0" + end.getMonthValue());
        Map<String, Result> dataMap = rsp.getDataMap();
        rsp.setStart(startYearMonth);
        rsp.setEnd(endYearMonth);
        rsp.setIndustryDept(currentVersion.getIndustryDept());

        Map<String, QueryTableRsp> queryTableRspMap = new HashMap<>();
        // 0、查准确率和执行量 因为需调看板接口，因此将查询前置 多线程处理
        CountDownLatch countDownLatch = new CountDownLatch(2);
        req.setIndustryDept(Arrays.asList(currentVersion.getIndustryDept()));
        String summaryRate = null;
        executor.execute(() -> {
            QueryTableReq tableReq = buildTableReq(req, start, end);
            queryTableRspMap.put("result", queryDataService.queryTable(tableReq, false));
            countDownLatch.countDown();
        });
        executor.execute(() -> {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            QueryTableReq tableReq = buildTableReq(req, start, end);
            if (ListUtils.isEmpty(req.getBizRange())) {
                Choose choose = new Choose();
                choose.setProjectType(ProjectTypeEnum.KEY_PROJECT.getName());
                tableReq.setChoose(choose);
                tableReq.setSelectFields(Arrays.asList("projectType", "industryOrProduct"));
                queryTableRspMap.put("summary", queryDataService.queryTable(tableReq, false));
            }
            countDownLatch.countDown();
        });

        // 1、获取当前用户有权限的客户
        WhereContent whereContent = checkPermission(LoginUtils.getUserName(), currentVersion.getIndustryDept(), "",
                req.getVersionId());
        if (whereContent == null) {
            return rsp;
        }
        whereContent.andIn(PplOrderDO::getSource, Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode()));
        whereContent.andEqual(PplOrderDO::getIndustryDept, currentVersion.getIndustryDept());
        whereContent.andInIfValueNotEmpty(PplOrderDO::getWarZone, req.getBizRange());
        whereContent.groupBy("customer_short_name");
        List<PplOrderDO> all = demandDBHelper.getAll(PplOrderDO.class, whereContent.getSql(), whereContent.getParams());
        Set<String> authCustomerShortNameList = all.stream().map(PplOrderDO::getCustomerShortName)
                .collect(Collectors.toSet());

        Func1<LatestItemVO, Integer> getResource = null;
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            getResource = LatestItemVO::getTotalGpuNum;
        } else {
            getResource = LatestItemVO::getTotalCore;
        }

        // 2、查过去三个月的预测
        WhereSQL where = new WhereSQL();
        Map<String, Object> params = new HashMap<>();
        where.and("a.begin_buy_date >= :beginBuyDate");
        where.and("a.begin_buy_date <= :endBuyDate");
        where.and("a.product = :product");
        where.and("a.demand_type in (:demandType)");
        where.and("b.industry_dept = :industryDept");
        where.and("b.source in (:source)");

        List<String> demandTypes = new ArrayList<>();
        for (String s : req.getDemandType()) {
            if (PplDemandTypeEnum.getCodeByName(s + "需求") != null) {
                demandTypes.add(PplDemandTypeEnum.getCodeByName(s + "需求"));
            }
        }
        params.put("demandType", demandTypes);
        params.put("beginBuyDate", DateUtils.getFirstDayOfMonth(DateUtils.parse(startYearMonth)));
        params.put("endBuyDate", DateUtils.getLastDayOfMonth(DateUtils.parse(endYearMonth)));
        params.put("product", req.getProduct());
        params.put("industryDept", currentVersion.getIndustryDept());
        params.put("source", Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode(), PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode()));
        String whereSql = whereToAnd(where.getSQL());
        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/latest_item.sql");
        sql = sql.replace("${FILTER}", whereSql);
        List<LatestItemVO> list = demandDBHelper.getRaw(LatestItemVO.class, sql, params);
        list = list.stream().filter(v -> authCustomerShortNameList.contains(v.getCustomerShortName()))
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(list)) {
            for (LatestItemVO latestItemVO : list) {
                Result result = dataMap.get(latestItemVO.getCustomerShortName());
                if (result == null) {
                    result = new Result();
                    dataMap.put(latestItemVO.getCustomerShortName(), result);
                }
                if (latestItemVO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                    result.getForecastNumMap().put(latestItemVO.getYearMonth(), -getResource.call(latestItemVO));
                } else {
                    result.getForecastNumMap().put(latestItemVO.getYearMonth(), getResource.call(latestItemVO));
                }

            }
        }

        // 3、获取过去三个月的执行量  CVM、裸金属指标获取核天   GPU获取月均
        // CVM 需要获取准确率
        String target = "month_distinct_bill_core_by_day_num@";
        Integer i = 30;
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            target = "month_avg_distinct_change_bill_num@";
            i = 1;
        }

        if (!countDownLatch.await(20, TimeUnit.SECONDS)) {
            throw new BizException("调用看板接口超时");
        }

        QueryTableRsp tableRsp = queryTableRspMap.get("result");
        if (!ObjectUtils.isEmpty(tableRsp)) {
            for (Map datum : tableRsp.getData()) {
                String customerShortName = (String) datum.get("customerShortName");
                if (!authCustomerShortNameList.contains(customerShortName)) {
                    // 如果不再权限范围内 则continue
                    continue;
                }
                String rate =
                        datum.get("origin_v2_avg_forecast_match_rate_core_by_day@总计") != null ? (String) datum.get(
                                "origin_v2_avg_forecast_match_rate_core_by_day@总计") : "";
                BigDecimal totalForecastNum532 =
                        datum.get("origin_v2_avg_forecast_num@总计") != null ? new BigDecimal(datum.get(
                                "origin_v2_avg_forecast_num@总计").toString()) : new BigDecimal(0);
                BigDecimal totalExecute =
                        datum.get(target + "总计") != null ? new BigDecimal(datum.get(
                                target + "总计").toString()) : new BigDecimal(0);
                Double firstMonth =
                        datum.get(target + startYearMonth) != null ? (Double) datum.get(target + startYearMonth) : 0.0;
                Double secondMonth =
                        datum.get(target + midYearMonth) != null ? (Double) datum.get(target + midYearMonth) : 0.0;
                Double thirdMonth =
                        datum.get(target + endYearMonth) != null ? (Double) datum.get(target + endYearMonth) : 0.0;
                Result result = dataMap.get(customerShortName);
                if (result == null) {
                    result = new Result();
                    dataMap.put(customerShortName, result);
                }
                BigDecimal thirty = new BigDecimal("30");
                result.setTotalForecastTotalNew532(totalForecastNum532);
                result.setTotalExecute(totalExecute.divide(thirty, 2, RoundingMode.HALF_UP));
                result.getExecuteNumMap().put(startYearMonth, firstMonth / i);
                result.getExecuteNumMap().put(midYearMonth, secondMonth / i);
                result.getExecuteNumMap().put(endYearMonth, thirdMonth / i);
                if (req.getProduct().equals(Ppl13weekProductTypeEnum.CVM.getName())) {
                    // 只有cvm才有准确率
                    result.setRate(rate);
                }
            }
        }

        // 5、根据规则历史履约特征
        dataMap.forEach((k, v) -> {
            QueryRateWithAuthRsp.buildConclusion(v);
            QueryRateWithAuthRsp.buildTotalDeviationValue532(v);
        });

        // 过滤掉云运管的
        dataMap.remove("云运管补充");
        dataMap.remove("云运管矫正");

        rsp.setDataMap(dataMap);
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.CVM.getName())) {
            rsp.buildSummaryRate(queryTableRspMap.get("summary"));
        }
        return rsp;

    }

    public QueryTableReq buildTableReq(QueryRateWithAuth req, LocalDate start, LocalDate end) {
        String product = "";
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            product = req.getProduct();
        } else if (req.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            product = "GPU";
        } else {
            product = "CVM";
        }
        QueryTableReq tableReq = new QueryTableReq();
        QueryTableReq.TimePeriod timePeriod = new QueryTableReq.TimePeriod();
        timePeriod.setStart(start.getYear() + "-" + start.getMonthValue());
        timePeriod.setEnd(end.getYear() + "-" + end.getMonthValue());
        tableReq.setAbTest(false);
        tableReq.setBillingScaleDistinct(true);
        tableReq.setBillingScaleMode("月均切片");
        tableReq.setBizTypes(Arrays.asList("外部行业", "(空值)"));
        tableReq.setCommonInstanceType(true);
        tableReq.setIsNewOrderType(true);
        tableReq.setProjectType(ListUtils.newList(ProjectTypeEnum.KEY_PROJECT.getName()));
        tableReq.setIsBlack(Arrays.asList(1, -1));
        tableReq.setCountry(new ArrayList<>());
        tableReq.setDataSortColName("");
        tableReq.setDataSortRule("desc");
        tableReq.setIsRendering(false);
        tableReq.setMainInstanceFamilyAndZone(false);
        QueryTableReq.Page page = new QueryTableReq.Page();
        page.setPageNo(1);
        page.setPageSize(200);
        tableReq.setPage(page);
        tableReq.setProduct(product);
        tableReq.setQueryTargets(ListUtils.newList("forecast_num", "change_bill_num", "month_distinct_bill_core_by_day_num","forecast_match_rate_core_by_day"));
        tableReq.setRateSumFunction("执行量");
        tableReq.setSelectFields(ListUtils.newList("customerShortName"));
        tableReq.setTimePeriod(timePeriod);
        tableReq.setForecastStatus(ForecastStatus.origin.getName());

        Choose choose = new Choose();
        choose.setBizType("外部行业");
        choose.setProjectType(ProjectTypeEnum.KEY_PROJECT.getName());
        choose.setIndustryOrProduct(req.getIndustryDept().get(0));
        tableReq.setChoose(choose);

        MrpV2Cache mrpV2Cache = new MrpV2Cache();
        mrpV2Cache.setEnable(Boolean.TRUE);
        Map<String, List<String>> cacheDim = new HashMap<>();
        cacheDim.put(CacheDimEnum.industryOrProduct.getColumn(), new ArrayList<>());
//        cacheDim.put(CacheDimEnum.demandType.getColumn(), new ArrayList<>());
        mrpV2Cache.setCacheDim(cacheDim);
        tableReq.setCache(mrpV2Cache);

        tableReq.setDemandTypes(req.getDemandType());
        tableReq.setIndustryDepts(req.getIndustryDept());
        if (ListUtils.isNotEmpty(req.getCustomhouseTitle())) {
            tableReq.setCountry(req.getCustomhouseTitle());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            tableReq.setRegionNames(req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            tableReq.setZoneNames(req.getZoneName());
        }
        if (ListUtils.isNotEmpty(req.getCustomerShortName())) {
            tableReq.setCustomerShortNames(req.getCustomerShortName());
        }
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            tableReq.setInstanceTypes(req.getInstanceType());
        }
        return tableReq;
    }

    @Override
    public List<IndustryPplOrderVO> queryAuditPplOrder(QueryIndustryAuditPplReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }

        String userName =
                Strings.isNotBlank(req.getUsername()) ? req.getUsername() : LoginUtils.getUserNameWithSystem();
        req.setUsername(userName);
        PplInnerProcessVersionSlaDO firstSla = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?) and deadline_type = 'ENTER'", currentVersion.getId());
        if (ObjectUtils.isEmpty(firstSla)) {
            throw new BizException("该行业未初始化流程");
        }
        if (ObjectUtils.isEmpty(firstSla.getDeadlineTime()) || firstSla.getDeadlineTime().after(new Date())) {
            // 如果当前还没设置sla时间，或者还没到录入截止时间 则查草稿箱的数据
            QueryPplDraftReq queryPplDraftReq = QueryIndustryAuditPplReq.convertDraftReq(req);
            List<PplListVo> pplListVos = pplDraftService.queryPreSubmitDraftData(queryPplDraftReq);
            List<IndustryPplOrderVO> result = ListUtils.transform(pplListVos, IndustryPplOrderVO::convertDraftOrder);
            return result;
        } else {
            // 查流程中数据
            return queryAllPplOrder(req);

        }
    }

    @Override
    public IndustryAuthDataResp queryIndustryAuth(IndustryAuthDataReq req) {
        IndustryAuthDataResp resp = new IndustryAuthDataResp();
        PplInnerProcessVersionDO innerProcessVersionDO = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                req.getVersionId());
        String industryDept = innerProcessVersionDO.getIndustryDept();
        List<IndustryDemandIndustryWarZoneDictDO> all = demandDBHelper.getAll(IndustryDemandIndustryWarZoneDictDO.class,
                "where industry = ?", industryDept);
        if (ListUtils.isEmpty(all)) {
            return resp;
        }
        Map<String, List<IndustryDemandIndustryWarZoneDictDO>> childrenMap = new HashMap<>();
        List<String> data = new ArrayList<>();
        String level = "";
        if (industryDept.equals(IndustryDeptEnum.STRATEGY.getName())) {
            level = PplInnerProcessAttributeEnum.CUSTOMER.getCode();
            childrenMap = all.stream()
                    .collect(Collectors.groupingBy(IndustryDemandIndustryWarZoneDictDO::getWarZoneName));
            data = all.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).distinct()
                    .collect(Collectors.toList());
        } else {
            level = PplInnerProcessAttributeEnum.WAR_ZONE.getCode();
            data = all.stream().map(IndustryDemandIndustryWarZoneDictDO::getWarZoneName).distinct()
                    .collect(Collectors.toList());
        }
        resp.setLevel(level);
        String userName = LoginUtils.getUserName();
        if (permissionService.checkIsAdmin(userName) ||
                permissionService.isRoleAndAllWarZone(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode(),
                        userName,innerProcessVersionDO.getIndustryDept())) {
            // 管理员或者拥有 行业数据关注人
            data.add(0, "全行业");
            resp.setData(data);
            // 管理员返回
            return resp;
        }
        List<IndustryDemandAuthDO> demandAuthDOList = permissionService.getAllRoleByUsername(userName,
                innerProcessVersionDO.getIndustryDept());
        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);
        if (demandAuthDOList == null || CollectionUtils.isEmpty(demandAuthDOList)) {
            // 如果没有权限直接返回
            return resp;
        }
        Map<String, IndustryDemandAuthDO> roleToAuthMap = demandAuthDOList.stream()
                .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));
        List<String> childrenData = new ArrayList<>();
        List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaList(req.getVersionId());
        for (int i = versionSlaList.size() - 1; i >= 0; i--) {
            PplInnerProcessVersionSlaDO slaDO = versionSlaList.get(i);
            IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(slaDO.getApproveRole());
            if (industryDemandAuthDO != null) {
                if (slaDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.DEPT.getCode())) {
                    data.add(0, "全行业");
                    childrenData.addAll(data);
                    break;
                } else if (slaDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
                    if (industryDept.equals(IndustryDeptEnum.STRATEGY.getName())) {
                        List<String> stringList = Arrays.asList(industryDemandAuthDO.getWarZoneName().split(";"));
                        List<IndustryDemandIndustryWarZoneDictDO> child = new ArrayList<>();
                        for (String s : stringList) {
                            if (childrenMap.get(s) != null) {
                                child.addAll(childrenMap.get(s));
                            }
                        }
                        if (ListUtils.isNotEmpty(child)) {
                            childrenData.addAll(child.stream()
                                    .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                    .collect(Collectors.toList()));
                        }
                    } else {
                        childrenData.addAll(Arrays.asList(industryDemandAuthDO.getWarZoneName().split(";")));
                    }
                } else if (slaDO.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
                    if (!Strings.isEmpty(industryDemandAuthDO.getCommonCustomerName())) {
                        List<String> commonCustomerList = Arrays.asList(
                                industryDemandAuthDO.getCommonCustomerName().split(";"));
                        if (ListUtils.isNotEmpty(commonCustomerList)) {
                            List<String> collect = customerDict.stream().filter(commonCustomerList::contains)
                                    .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                    .collect(Collectors.toList());
                            childrenData.addAll(collect);
                        }
                    }
                }
            }
        }
        data.retainAll(childrenData);
        resp.setData(data);
        return resp;
    }

    public List<IndustryPplOrderVO> queryAllPplOrder(QueryIndustryAuditPplReq req) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(req.getVersionId());
        Map<String, String> nodeCodeNameMap = innerVersionService.getNodeCodeMap(currentVersion.getIndustryDept(),
                currentVersion.getProduct(), currentVersion.getId());

        // 先查出pplOrder
        WhereContent draftSql = checkPermission(req.getUsername(), req.getIndustryDept(), "", currentVersion.getId());
        if (draftSql == null) {
            return new ArrayList<>();
        }
        draftSql.andInIfValueNotEmpty(PplOrderDO::getWarZone, req.getWarZoneList());
        draftSql.addAnd(
                "ppl_order in (select ppl_order from ppl_order_audit_record where deleted = 0 and version_id = ? group by ppl_order)",
                currentVersion.getId());

        List<PplOrderDO> allPplOrderList = demandDBHelper.getAll(PplOrderDO.class, draftSql.getSql(),
                draftSql.getParams());
        if (ListUtils.isEmpty(allPplOrderList)) {
            return new ArrayList<>();
        }
        List<String> filterPplOrder = allPplOrderList.stream().map(PplOrderDO::getPplOrder)
                .collect(Collectors.toList());
        Map<String, PplOrderDO> pplOrderDOMap = allPplOrderList.stream()
                .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v));

        // 再过滤出Item
        WhereSQL itemSql = req.transToSQL();
        itemSql.and(
                "audit_record_id in (select max(id) from ppl_order_audit_record where deleted = 0 and version_id = ? group by ppl_order)",
                currentVersion.getId());
        List<PplOrderAuditRecordItemDO> allItemList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                itemSql.getSQL(),
                itemSql.getParams());
        List<PplOrderAuditRecordItemDO> filterItem = allItemList.stream()
                .filter(v -> filterPplOrder.contains(v.getPplOrder())).collect(Collectors.toList());
        if (ListUtils.isEmpty(filterItem)) {
            return new ArrayList<>();
        }
        Map<String, List<PplOrderAuditRecordItemDO>> orderToItemMap = filterItem.stream()
                .collect(Collectors.groupingBy(PplOrderAuditRecordItemDO::getPplOrder));

        List<IndustryPplOrderVO> industryPplOrderVOList = new ArrayList<>();
        orderToItemMap.forEach((k, v) -> {
            List<PplOrderAuditRecordItemDO> recordItemDOList = orderToItemMap.get(k);
            if (ListUtils.isEmpty(recordItemDOList)) {
                return;
            }
            PplOrderDO orderDO = pplOrderDOMap.get(k);
            IndustryPplOrderVO industryPplOrderVO = new IndustryPplOrderVO();
            BeanUtils.copyProperties(orderDO, industryPplOrderVO);
            List<IndustryPplItemVO> itemList = new ArrayList<>();
            for (PplOrderAuditRecordItemDO pplOrderAuditRecordItemDO : recordItemDOList) {
                IndustryPplItemVO industryPplItemVO = new IndustryPplItemVO();
                BeanUtils.copyProperties(pplOrderAuditRecordItemDO, industryPplItemVO);
                itemList.add(industryPplItemVO);
            }
            industryPplOrderVO.setItemVOList(itemList);
            industryPplOrderVOList.add(industryPplOrderVO);
        });

        if (ListUtils.isEmpty(industryPplOrderVOList)) {
            return new ArrayList<>();
        }
        List<String> pplOrders = industryPplOrderVOList.stream().map(IndustryPplOrderVO::getPplOrder)
                .collect(Collectors.toList());

        List<PplOrderAuditListVO> raw = auditRecordService.buildPplOrderAuditListVO(pplOrders);
        Map<String, Integer> orderToChangeCore = auditRecordService.queryPplOrderChangeCore(pplOrders, raw);
        Map<String, Integer> orderToChangeGpu = auditRecordService.queryPplOrderChangeGpuNum(pplOrders, raw);
        Map<String, Integer> orderToChangeInstanceNum = auditRecordService.queryPplOrderChangeInstanceNum(
                pplOrders, raw);
        for (IndustryPplOrderVO industryPplOrderVO : industryPplOrderVOList) {
            // 完善数据
            industryPplOrderVO.setChangeCore(orderToChangeCore.get(industryPplOrderVO.getPplOrder()));
            industryPplOrderVO.setChangeGpu(orderToChangeGpu.get(industryPplOrderVO.getPplOrder()));
            industryPplOrderVO.setChangeInstanceNum(orderToChangeInstanceNum.get(industryPplOrderVO.getPplOrder()));
            if (!Objects.equals(req.getVersionId(), currentVersion.getId())) {
                // 如果不是当前版本 则将审批人 审批节点置空， 并调整至已完结
                industryPplOrderVO.setNodeCode("");
                industryPplOrderVO.setCurrentProcessor("");
                industryPplOrderVO.setAuditStatus(null);
            }
            industryPplOrderVO.setApproveNodeName(nodeCodeNameMap.get(industryPplOrderVO.getApproveNodeCode()));
            industryPplOrderVO.setNodeName(nodeCodeNameMap.get(industryPplOrderVO.getNodeCode()));
            IndustryPplOrderVO.convertPplOrderVo(industryPplOrderVO);
        }

        return industryPplOrderVOList;
    }

    public List<QueryAuditOverviewReq> buildOverviewReqList(QueryAuditOverviewReq req) {
        PplInnerProcessVersionDO innerProcessVersionDO = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                req.getVersionId());
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("process_id = ?", innerProcessVersionDO.getProcessId());
        whereSQL.and("status = ?", PplInnerProcessVersionStatusEnum.DONE.getCode());
        whereSQL.and("id < ?", innerProcessVersionDO.getId());
        whereSQL.and("demand_end_year > ? or (demand_end_year = ? and demand_end_month >= ?)",
                innerProcessVersionDO.getDemandBeginYear(), innerProcessVersionDO.getDemandBeginYear(),
                innerProcessVersionDO.getDemandBeginMonth());
        List<PplInnerProcessVersionDO> all = demandDBHelper.getAll(PplInnerProcessVersionDO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        List<QueryAuditOverviewReq> reqList = new ArrayList<>();
        req.setVersionCode(innerProcessVersionDO.getVersionCode());
        reqList.add(req);
        HashMap<String, Integer> versionCodeMap = new HashMap<>();
        for (PplInnerProcessVersionDO versionDO : all) {
            QueryAuditOverviewReq queryAuditOverviewReq = new QueryAuditOverviewReq();
            BeanUtils.copyProperties(req, queryAuditOverviewReq);
            queryAuditOverviewReq.setVersionId(versionDO.getId());
            String versionCode = versionDO.getVersionCode();
            int i = 1;
            while (versionCodeMap.get(versionCode) != null) {
                versionCode = versionDO.getVersionCode() + "_" + i;
                i++;
            }
            versionCodeMap.put(versionCode, 1);
            queryAuditOverviewReq.setVersionCode(versionCode);
            reqList.add(queryAuditOverviewReq);
        }
        return reqList;
    }


    @Override
    @Transactional(value = "demandTransactionManager")
    public void syncAppliedDraftData(PplInnerProcessVersionDO versionDO, List<PplItemDraftDO> pplItemDraftDOS) {
        if (CollectionUtils.isEmpty(pplItemDraftDOS)) {
            return;
        }

        List<String> pplOrders = pplItemDraftDOS.stream().map(PplItemDraftDO::getPplOrder)
                .collect(Collectors.toList());
        Map<String, PplOrderDO> pplOrderDOMap = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in(?) ",
                        pplOrders).stream()
                .collect(Collectors.toMap(PplOrderDO::getPplOrder, Function.identity(), (v1, v2) -> v1));

        List<String> pplIds = pplItemDraftDOS.stream().map(PplItemDraftDO::getPplId)
                .collect(Collectors.toList());
        List<PplItemDO> itemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where instance_num > 0 and (ppl_id in(?) or parent_ppl_id in (?)) ",
                pplIds, pplIds);
        Map<String, PplItemDO> itemDOMap = itemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<PplItemDO>> itemDOGroupMap = itemDOList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplItemDO::getParentPplId));

        Map<String, List<PplItemDraftDO>> splitItemDOMap = pplItemDraftDOS.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplItemDraftDO::getParentPplId));

        List<PplItemDraftDO> updateItemDraftDOList = new ArrayList<>();
        List<PplItemDraftDO> insertSplitDraftItemDOs = new ArrayList<>();

        // 1、处理 source为IMPORT的预测单
        pplItemDraftDOS.forEach(itemDraft -> {
            PplOrderDO pplOrderDO = pplOrderDOMap.get(itemDraft.getPplOrder());
            if (pplOrderDO != null && !PplOrderSourceTypeEnum.IMPORT.getCode().equals(pplOrderDO.getSource())) {
                return;
            }

            PplItemDO currentItemDO = itemDOMap.get(itemDraft.getPplId());

            // 此草稿单是当前审批版本新增的，未生效过，则草稿单无需更新预约信息
            if (currentItemDO == null) {
                return;
            }

            // 考虑拆单情况：若未拆单/是子单/是母单且草稿单中有拆单信息(isCurrentItem=true)，则取本单的预约信息及预约量即可；若是母单(isCurrentItem=false)，且拆单信息未同步到草稿单，则草稿单进行拆单
            // 2024-07-02 增加一种情况，若拆单了 但是实例数量不为0的单只剩一个 isCurrentItem = true
            boolean isCurrentItem =
                    (StringUtils.isBlank(currentItemDO.getParentPplId())
                            || !currentItemDO.getPplId().equals(currentItemDO.getParentPplId())) || (
                            splitItemDOMap.get(currentItemDO.getParentPplId()) != null
                                    && splitItemDOMap.get(currentItemDO.getParentPplId()).size() > 1 ||
                                    itemDOGroupMap.get(currentItemDO.getParentPplId()) != null && itemDOGroupMap.get(
                                            currentItemDO.getParentPplId()).size() == 1);

            // 如果草稿单和生效记录都非预约明细，则草稿单无需更新预约信息；否则草稿单需与生效记录的预约信息保持一致（若草稿单是母单且草稿箱中无拆单信息，则需走下面逻辑进行拆单信息同步进草稿箱）
            if (isCurrentItem && !PplItemStatusEnum.APPLIED.getCode().equals(currentItemDO.getStatus())
                    && !PplItemStatusEnum.APPLIED.getCode().equals(itemDraft.getStatus())) {
                return;
            }

            // 若草稿单已有的预约信息 跟 生效ppl-item的预约信息一致，则无需重复更新（若草稿单是母单，则前提是有拆单信息，否则仍需走下面逻辑进行拆单）
            if (isCurrentItem && Objects.equals(currentItemDO.getYunxiaoOrderId(), itemDraft.getYunxiaoOrderId())
                    && Objects.equals(currentItemDO.getYunxiaoDetailId(), itemDraft.getYunxiaoDetailId())
                    && Objects.equals(currentItemDO.getYunxiaoOrderStatus(), itemDraft.getYunxiaoOrderStatus())
                    && Objects.equals(currentItemDO.getTotalCoreApplyAfter(), itemDraft.getTotalCoreApplyAfter())) {
                return;
            }

            // 1.1、对需要更新的草稿单，构建预约量更新信息
            PplItemDraftDO updateDraftDO = new PplItemDraftDO();
            updateDraftDO.setId(itemDraft.getId());

            updateDraftDO.setParentPplId(currentItemDO.getParentPplId());

            updateDraftDO.setStatus(currentItemDO.getStatus());
            updateDraftDO.setYunxiaoOrderId(currentItemDO.getYunxiaoOrderId());
            updateDraftDO.setYunxiaoOrderStatus(currentItemDO.getYunxiaoOrderStatus());
            updateDraftDO.setYunxiaoDetailId(currentItemDO.getYunxiaoDetailId());

            if (!isCurrentItem) {
                updateDraftDO.setInstanceNum(currentItemDO.getInstanceNum());
                updateDraftDO.setTotalDisk(currentItemDO.getTotalDisk());
                updateDraftDO.setTotalCore(currentItemDO.getTotalCore());
                updateDraftDO.setTotalGpuNum(currentItemDO.getTotalGpuNum());
            }

            updateDraftDO.setInstanceNumApplyBefore(currentItemDO.getInstanceNumApplyBefore());
            updateDraftDO.setInstanceNumApplyAfter(currentItemDO.getInstanceNumApplyAfter());
            updateDraftDO.setTotalCoreApplyBefore(currentItemDO.getTotalCoreApplyBefore());
            updateDraftDO.setTotalCoreApplyAfter(currentItemDO.getTotalCoreApplyAfter());
            updateDraftDO.setTotalGpuNumApplyBefore(currentItemDO.getTotalGpuNumApplyBefore());
            updateDraftDO.setTotalGpuNumApplyAfter(currentItemDO.getTotalGpuNumApplyAfter());
            updateItemDraftDOList.add(updateDraftDO);

            // 1.2.1、若拆单(isSplit = true)，则获取同一母单下的所有ppl-item
            List<PplItemDO> splitItemDOs =
                    itemDOGroupMap.get(currentItemDO.getParentPplId()) == null ? Collections.emptyList()
                            : itemDOGroupMap.get(currentItemDO.getParentPplId());

            // 排除当前的母单草稿，排除草稿箱中已有的子单草稿
            splitItemDOs.remove(currentItemDO);
            splitItemDOs = splitItemDOs.stream().filter(o -> !pplIds.contains(o.getPplId())).distinct()
                    .collect(Collectors.toList());

            // 1.2.2、对需要拆出子单的草稿单，构建拆单信息
            splitItemDOs.forEach(splitItem -> {
                PplItemDraftDO splitDraftItemDO = new PplItemDraftDO();
                BeanUtils.copyProperties(itemDraft, splitDraftItemDO);
                splitDraftItemDO.setId(null);
                splitDraftItemDO.setCreateTime(null);
                splitDraftItemDO.setUpdateTime(null);

                splitDraftItemDO.setPplId(splitItem.getPplId());
                splitDraftItemDO.setParentPplId(splitItem.getParentPplId());

                splitDraftItemDO.setStatus(splitItem.getStatus());
                splitDraftItemDO.setYunxiaoOrderId(splitItem.getYunxiaoOrderId());
                splitDraftItemDO.setYunxiaoOrderStatus(splitItem.getYunxiaoOrderStatus());
                splitDraftItemDO.setYunxiaoDetailId(splitItem.getYunxiaoDetailId());

                splitDraftItemDO.setTotalCore(splitItem.getTotalCore());
                splitDraftItemDO.setTotalGpuNum(splitItem.getTotalGpuNum());
                splitDraftItemDO.setInstanceNum(splitItem.getInstanceNum());
                splitDraftItemDO.setTotalDisk(splitItem.getTotalDisk());

                // 记录预约前后 预测的预约资源量
                boolean isApplied = PplItemStatusEnum.APPLIED.getCode().equals(splitDraftItemDO.getStatus());

                splitDraftItemDO.setInstanceNumApplyBefore(isApplied ? splitItem.getInstanceNumApplyBefore() : 0);
                splitDraftItemDO.setInstanceNumApplyAfter(isApplied ? splitItem.getInstanceNumApplyAfter() : 0);
                splitDraftItemDO.setTotalCoreApplyBefore(isApplied ? splitItem.getTotalCoreApplyBefore() : 0);
                splitDraftItemDO.setTotalCoreApplyAfter(isApplied ? splitItem.getTotalCoreApplyAfter() : 0);
                splitDraftItemDO.setTotalGpuNumApplyBefore(
                        isApplied ? splitItem.getTotalGpuNumApplyBefore() : BigDecimal.ZERO);
                splitDraftItemDO.setTotalGpuNumApplyAfter(
                        isApplied ? splitItem.getTotalGpuNumApplyAfter() : BigDecimal.ZERO);

                insertSplitDraftItemDOs.add(splitDraftItemDO);

            });

        });

        if (!CollectionUtils.isEmpty(updateItemDraftDOList)) {
            demandDBHelper.update(updateItemDraftDOList);
        }

        List<PplItemDraftDO> insertSplitDraftItemDOList = insertSplitDraftItemDOs.stream().distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(insertSplitDraftItemDOList)) {
            demandDBHelper.executeRaw(
                    "update ppl_item_draft set deleted = 1 where ppl_id in(?) and draft_status in(?) ",
                    insertSplitDraftItemDOList.stream().map(PplItemDraftDO::getPplId).collect(
                            Collectors.toList()), Arrays.asList(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                            PplOrderDraftStatusEnum.DRAFT.getCode()));
            demandDBHelper.insertBatchWithoutReturnId(insertSplitDraftItemDOList);
        }

        // 2、处理 source为APPLY_AUTO_FILL的预测单
        // APPLY_AUTO_FILL: 头部客户(系统自动补的时候设置)
        Map<String, PplItemDraftDO> draftItemDOMap = pplItemDraftDOS.stream()
                .collect(Collectors.toMap(PplItemDraftDO::getPplId, Function.identity(), (v1, v2) -> v1));

        List<PplItemDO> versionValidPplItemBySystem = getVersionValidPplItemBySystem(versionDO);
        List<String> noDeleteDraftPplIds = new ArrayList<>();
        versionValidPplItemBySystem = versionValidPplItemBySystem.stream().filter(e -> {
            PplItemDraftDO itemDraftDO = draftItemDOMap.get(e.getPplId());
            // 之前已经插入过 且 预约信息未改变的草稿单，则无需更改，也无需进入下面的 删除新增 逻辑
            if (itemDraftDO != null && Objects.equals(itemDraftDO.getYunxiaoOrderId(), e.getYunxiaoOrderId())
                    && Objects.equals(itemDraftDO.getYunxiaoDetailId(), e.getYunxiaoDetailId())
                    && Objects.equals(itemDraftDO.getYunxiaoOrderStatus(), e.getYunxiaoOrderStatus())
                    && Objects.equals(itemDraftDO.getInstanceNumApplyAfter(), e.getInstanceNumApplyAfter())) {
                noDeleteDraftPplIds.add(itemDraftDO.getPplId());
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        // 先删除之前已同步到需求沟通的 source为APPLY_AUTO_FILL的预测单
        // (先删除，是因为预约单取消时会删除系统自动生成的预测，因此这里不选择[已有更新，没有插入，不存在删除]的方式，而是[直接全部先删除，需要更新/插入的 直接新增]的方式)
        List<PplItemDraftDO> deleteItemDraftDOs = pplItemDraftDOS.stream().filter(e -> {
            PplOrderDO pplOrderDO = pplOrderDOMap.get(e.getPplOrder());
            if (pplOrderDO != null && !noDeleteDraftPplIds.contains(e.getPplId())
                    && PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(pplOrderDO.getSource())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteItemDraftDOs)) {
            demandDBHelper.executeRaw(
                    "update ppl_item_draft set deleted = 1 where ppl_id in(?) and draft_status in(?) ",
                    deleteItemDraftDOs.stream().map(PplItemDraftDO::getPplId).collect(
                            Collectors.toList()), Arrays.asList(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                            PplOrderDraftStatusEnum.DRAFT.getCode()));
            demandDBHelper.executeRaw(
                    "update ppl_order_draft set deleted = 1 where ppl_order in(?) and draft_status in(?) ",
                    deleteItemDraftDOs.stream().map(PplItemDraftDO::getPplOrder).collect(
                            Collectors.toList()), Arrays.asList(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                            PplOrderDraftStatusEnum.DRAFT.getCode()));
        }

        // 再重新插入由最新预约信息的预测单转换的草稿
        if (CollectionUtils.isEmpty(versionValidPplItemBySystem)) {
            return;
        }

        List<PplOrderDraftDO> insertOrderDraftDOList = new ArrayList<>();
        List<PplItemDraftDO> insertItemDraftDOList = new ArrayList<>();

        List<String> pplOrdersBySystem = versionValidPplItemBySystem.stream().map(PplItemDO::getPplOrder)
                .collect(Collectors.toList());
        List<PplOrderDO> orderDOBySystemList = demandDBHelper.getAll(PplOrderDO.class,
                "where ppl_order in(?) ", pplOrdersBySystem);
        completeWarZone(orderDOBySystemList);

        List<String> auditPplOrderList = demandDBHelper.getAll(PplOrderAuditRecordDO.class,
                "where ppl_order in(?) and deleted = 0 group by ppl_order ",
                pplOrdersBySystem).stream().map(o -> o.getPplOrder()).collect(Collectors.toList());

        Map<String, PplOrderDO> pplOrderDOBySystemMap = orderDOBySystemList.stream()
                .collect(Collectors.toMap(PplOrderDO::getPplOrder, Function.identity(), (v1, v2) -> v1));

        Map<String, List<PplItemDO>> pplOrderToItem = versionValidPplItemBySystem.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        pplOrderToItem.forEach((k, v) -> {
            // 保存草稿
            PplOrderDO pplOrder = pplOrderDOBySystemMap.get(k);
            if (pplOrder == null || CollectionUtils.isEmpty(v)) {
                return;
            }

            // PE单同步进草稿箱时，如果此单进入过审批流，则来源设置为ORIGINAL，如果没进入过审批流，则来源设置为INSERT
            String draftSource = auditPplOrderList.contains(k) ? DraftStatusEnum.ORIGINAL.getCode()
                    : DraftStatusEnum.INSERT.getCode();

            PplOrderDraftDO insertOrderDraftDO = new PplOrderDraftDO();
            BeanUtil.copyProperties(pplOrder, insertOrderDraftDO);
            insertOrderDraftDO.setId(null);
            insertOrderDraftDO.setCreateTime(null);
            insertOrderDraftDO.setSource(draftSource);
            insertOrderDraftDO.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            insertOrderDraftDO.setProduct(v.get(0).getProduct());
            insertOrderDraftDO.setDemandType(v.get(0).getDemandType());
            insertOrderDraftDO.setDemandScene(v.get(0).getDemandScene());
            insertOrderDraftDO.setProjectName(v.get(0).getProjectName());
            insertOrderDraftDO.setBeginDate(v.get(0).getBeginBuyDate().toString());
            insertOrderDraftDO.setEndDate(v.get(0).getEndBuyDate().toString());
            insertOrderDraftDOList.add(insertOrderDraftDO);

            v.forEach(itemDO -> {
                PplItemDraftDO insertItemDraftDO = new PplItemDraftDO();
                BeanUtil.copyProperties(itemDO, insertItemDraftDO);
                insertItemDraftDO.setId(null);
                insertItemDraftDO.setCreateTime(null);
                insertItemDraftDO.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
                insertItemDraftDO.setType(draftSource);
                insertItemDraftDOList.add(insertItemDraftDO);
            });
        });
        if (!CollectionUtils.isEmpty(insertOrderDraftDOList) && !CollectionUtils.isEmpty(insertItemDraftDOList)) {
            demandDBHelper.insert(insertOrderDraftDOList);
            demandDBHelper.insert(insertItemDraftDOList);
        }

    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void syncAppliedAuditData(List<PplOrderAuditRecordItemDO> auditRecordItemDOs) {
        List<String> pplIds = auditRecordItemDOs.stream().map(PplOrderAuditRecordItemDO::getPplId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pplIds)) {
            return;
        }

        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_id in(?) or parent_ppl_id in (?) ",
                pplIds, pplIds);
        Map<String, PplItemDO> itemDOMap = pplItemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<PplItemDO>> itemDOGroupMap = pplItemDOList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplItemDO::getParentPplId));

        Map<String, List<PplOrderAuditRecordItemDO>> splitItemDOMap = auditRecordItemDOs.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplOrderAuditRecordItemDO::getParentPplId));

        // 查询审批记录对应的预约数据
        List<String> deletedYunxiaoStatus = Arrays.asList(YunxiaoOrderStatusEnum.CREATED.getCode(),
                YunxiaoOrderStatusEnum.CANCELED.getCode(),
                YunxiaoOrderStatusEnum.BAD_CANCELED.getCode(), YunxiaoOrderStatusEnum.REJECTED.getCode());
        List<PplItemAppliedDO> pplItemAppliedDOList = demandDBHelper.getAll(PplItemAppliedDO.class,
                "where yunxiao_order_id in(?) and yunxiao_order_status not in(?) ",
                auditRecordItemDOs.stream().map(PplItemBaseDO::getYunxiaoOrderId).filter(Objects::nonNull).distinct()
                        .collect(Collectors.toList()), deletedYunxiaoStatus);
        Map<String, PplItemAppliedDO> itemAppliedDOMap = pplItemAppliedDOList.stream().collect(
                Collectors.toMap(e -> String.join("@", e.getYunxiaoOrderId(), String.valueOf(e.getYunxiaoDetailId())),
                        Function.identity(), (v1, v2) -> v2));

        List<PplOrderAuditRecordItemDO> updateItemAuditDOList = new ArrayList<>();
        auditRecordItemDOs.forEach(itemAudit -> {
            PplItemDO currentItemDO = itemDOMap.get(itemAudit.getPplId());

            // 审批的ppl-item 还未生效
            if (currentItemDO == null) {
                // 如果 关联了预约，则更新预约信息（进入审批流的前一刻，重复的预测PN单 和 预约PE单，会进行重匹配，让未生效的PN单也能关联预约）
                if (PplItemStatusEnum.APPLIED.getCode().equals(itemAudit.getStatus())) {
                    PplOrderAuditRecordItemDO updateItemAuditDO = new PplOrderAuditRecordItemDO();
                    updateItemAuditDO.setId(itemAudit.getId());

                    PplItemAppliedDO appliedDO = itemAppliedDOMap.get(String.join("@", itemAudit.getYunxiaoOrderId(),
                            String.valueOf(itemAudit.getYunxiaoDetailId())));
                    if (appliedDO == null
                            || (!appliedDO.getPplId().equals(itemAudit.getPplId()) && !Arrays.asList(
                            appliedDO.getPplId2().split(";")).contains(itemAudit.getPplId()))
                            || deletedYunxiaoStatus.contains(appliedDO.getYunxiaoOrderStatus())) {
                        // 将预约后取消的PN单 的关联状态释放
                        updateItemAuditDO.setStatus(PplItemStatusEnum.VALID.getCode());
                        updateItemAuditDO.setYunxiaoOrderId("");
                        updateItemAuditDO.setYunxiaoOrderStatus("");
                        updateItemAuditDO.setYunxiaoDetailId(0L);

                        updateItemAuditDO.setInstanceNumApplyBefore(itemAudit.getInstanceNumApplyAfter());
                        updateItemAuditDO.setInstanceNumApplyAfter(0);
                        updateItemAuditDO.setTotalCoreApplyBefore(itemAudit.getTotalCoreApplyAfter());
                        updateItemAuditDO.setTotalCoreApplyAfter(0);
                        updateItemAuditDO.setTotalGpuNumApplyBefore(itemAudit.getTotalGpuNumApplyAfter());
                        updateItemAuditDO.setTotalGpuNumApplyAfter(BigDecimal.ZERO);

                        updateItemAuditDOList.add(updateItemAuditDO);
                        return;
                    }

                    // 若审批记录已有的预约信息 跟 预约明细的预约信息一致，则无需重复更新
                    if (Objects.equals(appliedDO.getYunxiaoOrderId(), itemAudit.getYunxiaoOrderId())
                            && Objects.equals(appliedDO.getYunxiaoDetailId(), itemAudit.getYunxiaoDetailId())
                            && Objects.equals(appliedDO.getYunxiaoOrderStatus(), itemAudit.getYunxiaoOrderStatus())) {
                        return;
                    }

                    // 审批记录与其关联的预约单 信息保持一致
                    updateItemAuditDO.setYunxiaoOrderId(appliedDO.getYunxiaoOrderId());
                    updateItemAuditDO.setYunxiaoOrderStatus(appliedDO.getYunxiaoOrderStatus());
                    updateItemAuditDO.setYunxiaoDetailId(appliedDO.getYunxiaoDetailId());
                    updateItemAuditDOList.add(updateItemAuditDO);
                }

                return;
            }

            // 审批的ppl-item已生效，但审批记录和生效记录都非预约明细，则审批记录无需更新预约信息；否则审批记录需与生效记录的预约信息保持一致
            if (!PplItemStatusEnum.APPLIED.getCode().equals(currentItemDO.getStatus())
                    && !PplItemStatusEnum.APPLIED.getCode().equals(itemAudit.getStatus())) {
                return;
            }

            // 考虑拆单情况：若未拆单/是子单/是母单且审批流中有拆单信息(isCurrentItem=true)，则取本单的预约信息及预约量 进行更新 即可；若是母单(isCurrentItem=false)，且拆单信息未同步到审批流，则取母单的预约信息，预约量聚合 进行更新
            boolean isCurrentItem =
                    (StringUtils.isBlank(currentItemDO.getParentPplId())
                            || !currentItemDO.getPplId().equals(currentItemDO.getParentPplId())) || (
                            splitItemDOMap.get(currentItemDO.getParentPplId()) != null
                                    && splitItemDOMap.get(currentItemDO.getParentPplId()).size() > 1);

            // 若审批记录已有的预约信息 跟 生效ppl-item的预约信息一致，则无需重复更新
            if (Objects.equals(currentItemDO.getYunxiaoOrderId(), itemAudit.getYunxiaoOrderId())
                    && Objects.equals(currentItemDO.getYunxiaoDetailId(), itemAudit.getYunxiaoDetailId())
                    && Objects.equals(currentItemDO.getYunxiaoOrderStatus(), itemAudit.getYunxiaoOrderStatus())
                    && Objects.equals(currentItemDO.getInstanceNumApplyAfter(), itemAudit.getInstanceNumApplyAfter())) {
                return;
            }

            // 若拆单(isSplit = true)，则获取同一母单下的所有ppl-item
            boolean isSplit = StringUtils.isNotBlank(currentItemDO.getParentPplId());
            List<PplItemDO> applyItemDOs =
                    isSplit ? (itemDOGroupMap.get(currentItemDO.getParentPplId()) == null ? Collections.emptyList()
                            : itemDOGroupMap.get(currentItemDO.getParentPplId()).stream()
                                    .filter(o -> PplItemStatusEnum.APPLIED.getCode().equals(o.getStatus()))
                                    .collect(Collectors.toList())) : Collections.emptyList();

            // 对需要更新的审批记录，构建预约量更新信息
            PplOrderAuditRecordItemDO updateItemAuditDO = new PplOrderAuditRecordItemDO();
            updateItemAuditDO.setId(itemAudit.getId());

            updateItemAuditDO.setParentPplId(currentItemDO.getParentPplId());

            updateItemAuditDO.setStatus(currentItemDO.getStatus());
            updateItemAuditDO.setYunxiaoOrderId(currentItemDO.getYunxiaoOrderId());
            updateItemAuditDO.setYunxiaoDetailId(currentItemDO.getYunxiaoDetailId());
            updateItemAuditDO.setYunxiaoOrderStatus(currentItemDO.getYunxiaoOrderStatus());

            updateItemAuditDO.setTotalCoreApplyBefore(
                    isCurrentItem ? (currentItemDO.getTotalCoreApplyBefore() == null ? 0
                            : currentItemDO.getTotalCoreApplyBefore())
                            : applyItemDOs.stream().mapToInt(
                                            o -> o.getTotalCoreApplyBefore() != null ? o.getTotalCoreApplyBefore() : 0)
                                    .sum());
            updateItemAuditDO.setTotalCoreApplyAfter(
                    isCurrentItem ? (currentItemDO.getTotalCoreApplyAfter() == null ? 0
                            : currentItemDO.getTotalCoreApplyAfter())
                            : applyItemDOs.stream().mapToInt(
                                            o -> o.getTotalCoreApplyAfter() != null ? o.getTotalCoreApplyAfter() : 0)
                                    .sum());
            updateItemAuditDO.setInstanceNumApplyBefore(
                    isCurrentItem ? (currentItemDO.getInstanceNumApplyBefore() == null ? 0
                            : currentItemDO.getInstanceNumApplyBefore())
                            : applyItemDOs.stream().mapToInt(
                                            o -> o.getInstanceNumApplyBefore() != null ? o.getInstanceNumApplyBefore() : 0)
                                    .sum());
            updateItemAuditDO.setInstanceNumApplyAfter(
                    isCurrentItem ? (currentItemDO.getInstanceNumApplyAfter() == null ? 0
                            : currentItemDO.getInstanceNumApplyAfter())
                            : applyItemDOs.stream().mapToInt(
                                            o -> o.getInstanceNumApplyAfter() != null ? o.getInstanceNumApplyAfter() : 0)
                                    .sum());
            updateItemAuditDO.setTotalGpuNumApplyBefore(
                    isCurrentItem ? (currentItemDO.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO
                            : currentItemDO.getTotalGpuNumApplyBefore())
                            : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyBefore)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            updateItemAuditDO.setTotalGpuNumApplyAfter(
                    isCurrentItem ? (currentItemDO.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                            : currentItemDO.getTotalGpuNumApplyAfter())
                            : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyAfter)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            updateItemAuditDOList.add(updateItemAuditDO);
        });

        if (!CollectionUtils.isEmpty(updateItemAuditDOList)) {
            demandDBHelper.update(updateItemAuditDOList);
        }
    }

    public SavePplDraftReq pplItemTransToDraftReq(PplOrderDO pplOrderDO, List<PplItemDO> pplItemDOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO, savePplDraftReq);
        savePplDraftReq.setType(OperateTypeEnum.ORIGINAL.getCode());
        savePplDraftReq.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
        savePplDraftReq.setProduct(pplItemDOS.get(0).getProduct());
        savePplDraftReq.setSubmitUser(pplOrderDO.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplItemDO itemDO : pplItemDOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setParentPplId(itemDO.getParentPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            if (Strings.isBlank(draftItemDTO.getConsensusStatus())) {
                draftItemDTO.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
                draftItemDTO.setConsensusZoneName(itemDO.getZoneName());
                draftItemDTO.setConsensusInstanceModel(itemDO.getInstanceModel());
            }
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeInstanceType())) {
                draftItemDTO.setAlternativeInstanceType(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeInstanceType()));
            }
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeZoneName())) {
                draftItemDTO.setAlternativeZoneName(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeZoneName()));
            }
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

    public SavePplDraftReq pplItemAppliedTransToDraftReq(PplOrderDO pplOrderDO,
            List<PplItemAppliedVO> pplItemAppliedVOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO, savePplDraftReq);
        savePplDraftReq.setType(OperateTypeEnum.ORIGINAL.getCode());
        savePplDraftReq.setBeginBuyDate(pplItemAppliedVOS.get(0).getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(pplItemAppliedVOS.get(0).getEndBuyDate().toString());
        savePplDraftReq.setProduct(pplItemAppliedVOS.get(0).getProduct());
        savePplDraftReq.setSubmitUser(pplOrderDO.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplItemAppliedVO itemDO : pplItemAppliedVOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setParentPplId(itemDO.getParentPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeInstanceType())) {
                draftItemDTO.setAlternativeInstanceType(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeInstanceType()));
            }
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeZoneName())) {
                draftItemDTO.setAlternativeZoneName(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeZoneName()));
            }
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

    public SavePplDraftReq transToDraftReq(PplOrderDO pplOrderDO, List<PplOrderAuditRecordItemVO> pplItemDOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO, savePplDraftReq);
        if (!pplOrderDO.getChangeType().equals(OperateTypeEnum.INSERT.getCode())) {
            savePplDraftReq.setType(OperateTypeEnum.UPDATE.getCode());
        } else {
            savePplDraftReq.setType(OperateTypeEnum.INSERT.getCode());
        }
        savePplDraftReq.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
        savePplDraftReq.setProduct(pplItemDOS.get(0).getProduct());
        savePplDraftReq.setSubmitUser(savePplDraftReq.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplOrderAuditRecordItemVO itemDO : pplItemDOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setPlacementGroupList(itemDO.placementGroupListGet());
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeInstanceType())) {
                draftItemDTO.setAlternativeInstanceType(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeInstanceType()));
            }
            if (org.nutz.lang.Strings.isNotBlank(itemDO.getAlternativeZoneName())) {
                draftItemDTO.setAlternativeZoneName(
                        Splitter.on(";").trimResults().splitToList(itemDO.getAlternativeZoneName()));
            }
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

    /**
     * 过滤权限 获取 行业-中心-战区 过滤条件
     *
     * 这个条件只适用于ppl_order表的单表查询
     *
     * @param username 当前登录用户
     * @return 返回null表示没有权限
     */
    @Override
    public WhereContent checkPermission(String username, String industryDept, String tablePrefix, Long versionId) {
        WhereContent whereContent = new WhereContent();
        if (permissionService.checkIsAdmin(username) || username.equals(SystemUserConstant.systemUser)) {
            whereContent.addAnd("1 = 1");
            return whereContent;
        }
        if (tablePrefix == null) {
            tablePrefix = "";
        }
        List<IndustryDemandAuthDO> demandAuthDOList = permissionService.getAllRoleByUsername(username, industryDept);
        if (demandAuthDOList == null || CollectionUtils.isEmpty(demandAuthDOList)) {
            return null;
        }
        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);
        List<String> roleList = innerVersionService.queryVersionRoles(versionId);
        Map<String, IndustryDemandAuthDO> roleToAuthMap = demandAuthDOList.stream()
                .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));
        for (String role : roleList) {
            // roleList，部门审批流相关审批角色,根据审批顺序从后往前排序，与权限大小也相同
            IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(role);
            if (industryDemandAuthDO != null) {
                WhereContent roleContent = new WhereContent();
                if (!Strings.isEmpty(industryDemandAuthDO.getIndustry()) &&
                        industryDemandAuthDO.getIndustry().contains(industryDept)) {
                    roleContent.addAnd(tablePrefix + "industry_dept = ?", industryDept);
                } else {
                    // 如果当前角色没有该行业的权限则直接进行下个角色的校验
                    continue;
                }
                if (!Strings.isEmpty(industryDemandAuthDO.getWarZoneName())) {
                    roleContent.addAnd(tablePrefix + "war_zone in (?)",
                            ListUtils.transform(industryDemandAuthDO.getWarZoneName().split(";"), o -> o));
                }
                if (!Strings.isEmpty(industryDemandAuthDO.getCommonCustomerName())) {
                    List<String> commonCustomerList = Arrays.asList(
                            industryDemandAuthDO.getCommonCustomerName().split(";"));
                    if (ListUtils.isNotEmpty(commonCustomerList)) {
                        List<String> collect = customerDict.stream().filter(commonCustomerList::contains)
                                .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                .collect(Collectors.toList());
                        roleContent.addAnd(tablePrefix + "customer_short_name in (?)", collect);
                    }
                }
                whereContent.addOr(roleContent);
            }
        }

        // 增加行业数据关注人权限控制
        if (roleToAuthMap.get(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode()) != null) {

            // 13周数据关注人 可以看到设置行业-战区-客户的相关单据
            WhereContent roleContent = new WhereContent();
            // 行业
            IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(
                    IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode());
            List<String> industryList = Arrays.asList(
                    industryDemandAuthDO.getIndustry().split(";"));
            if (ListUtils.isNotEmpty(industryList)) {
                roleContent.addAnd("industry_dept in (?)", industryList);
            }

            // 战区
            if (!industryDemandAuthDO.getIsAllWarZone()) {
                List<String> warZoneList = new ArrayList<>(Arrays.asList(
                        industryDemandAuthDO.getWarZoneName().split(";")));
                if (industryDemandAuthDO.getIsAllWarZone()) {
                    warZoneList.add("未关联");
                }
                if (ListUtils.isNotEmpty(warZoneList)) {
                    roleContent.addAnd("war_zone in (?)", warZoneList);
                }
            }

            // 客户
            if (!industryDemandAuthDO.getIsAllCustomer()) {
                List<String> commonCustomerList = Arrays.asList(
                        industryDemandAuthDO.getCommonCustomerName().split(";"));

                if (ListUtils.isNotEmpty(commonCustomerList)) {
                    List<String> collect = customerDict.stream()
                            .filter(v -> commonCustomerList.contains(v.getCommonCustomerName()))
                            .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                            .collect(Collectors.toList());
                    roleContent.addAnd("customer_short_name in (?)", collect);
                }
            }

            whereContent.addOr(roleContent);
        }

        if (whereContent.getParams().length == 0) {
            // 代表无任何权限
            return null;
        }
        return whereContent;

    }

    /**
     * 过滤权限 获取 行业-中心-战区 过滤条件
     *
     * 这个条件只适用于ppl_order表的单表查询
     *
     * @param username 当前登录用户
     * @return 返回null表示没有权限
     */
    @Override
    public WhereContent checkPermission(String username, String industryDept, String tablePrefix,
            List<Long> versionIds) {
        WhereContent whereContent = new WhereContent();
        if (permissionService.checkIsAdmin(username) || username.equals(SystemUserConstant.systemUser)) {
            whereContent.addAnd("1 = 1");
            return whereContent;
        }
        if (tablePrefix == null) {
            tablePrefix = "";
        }
        List<IndustryDemandAuthDO> demandAuthDOList = permissionService.getAllRoleByUsername(username,
                industryDept);
        if (demandAuthDOList == null || CollectionUtils.isEmpty(demandAuthDOList)) {
            return null;
        }
        Map<String, IndustryDemandAuthDO> roleToAuthMap = demandAuthDOList.stream()
                .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));
        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);

        Map<Long, List<String>> roleOfVersionMap = innerVersionService.queryVersionsRoles(versionIds);
        Set<String> roleList = new HashSet<>();
        roleOfVersionMap.forEach((key, value) -> roleList.addAll(value));

        for (String role : roleList) {
            // roleList，部门审批流相关审批角色
            IndustryDemandAuthDO industryDemandAuthDO = roleToAuthMap.get(role);
            if (industryDemandAuthDO != null) {
                WhereContent roleContent = new WhereContent();
                if (!Strings.isEmpty(industryDemandAuthDO.getIndustry()) &&
                        industryDemandAuthDO.getIndustry().contains(industryDept)) {
                    roleContent.addAnd(tablePrefix + "industry_dept = ?", industryDept);
                } else {
                    // 如果当前角色没有该行业的权限则直接进行下个角色的校验
                    continue;
                }
                if (!Strings.isEmpty(industryDemandAuthDO.getWarZoneName())) {
                    roleContent.addAnd(tablePrefix + "war_zone in (?)",
                            ListUtils.transform(industryDemandAuthDO.getWarZoneName().split(";"), o -> o));
                }
                if (!Strings.isEmpty(industryDemandAuthDO.getCommonCustomerName())) {
                    List<String> commonCustomerList = Arrays.asList(
                            industryDemandAuthDO.getCommonCustomerName().split(";"));
                    if (ListUtils.isNotEmpty(commonCustomerList)) {
                        List<String> collect = customerDict.stream().filter(commonCustomerList::contains)
                                .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                .collect(Collectors.toList());
                        roleContent.addAnd(tablePrefix + "customer_short_name in (?)", collect);
                    }
                }
                whereContent.addOr(roleContent);
            }
        }
        if (whereContent.getParams().length == 0) {
            // 代表无任何权限
            return null;
        }
        return whereContent;

    }

    @Override
    public List<String> queryProcessAllRoles() {
        List<PplInnerProcessNodeDO> all = demandDBHelper.getAll(PplInnerProcessNodeDO.class);
        return all.stream().map(PplInnerProcessNodeDO::getApproveRole).distinct().collect(Collectors.toList());
    }

    @Override
    public List<QueryApproveNoteResp> queryApproveNote(QueryApproveNoteReq queryApproveNoteReq) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(
                queryApproveNoteReq.getVersionId());
        if (ObjectUtils.isEmpty(currentVersion)) {
            throw new BizException("查找不到当前版本");
        }
        List<QueryApproveNoteResp> list = new ArrayList<>();
        List<PplInnerApproveNodeDTO> highlight = new ArrayList<>();
        List<PplInnerApproveNodeDTO> nodesByVersion = innerVersionService.getNodesByVersionWithoutPreSubmit(
                currentVersion.getId());
        if (!CollectionUtils.isEmpty(queryApproveNoteReq.getNodeCodeList())) {
            highlight = nodesByVersion.stream()
                    .filter(v -> queryApproveNoteReq.getNodeCodeList().contains(v.getNodeCode()))
                    .collect(Collectors.toList());
        } else {
            highlight = nodesByVersion.stream()
                    .filter(v -> v.getIsHighlight().equals(Boolean.TRUE)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(highlight)) {
            return list;
        }
        List<String> nodeCodeList = highlight.stream().map(PplInnerApproveNodeDTO::getNodeCode)
                .collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("b.version_id = :versionId");
        whereSQL.and("b.node_code in (:nodeCodeList)");
        whereSQL.and("b.approve_result is not null");
        whereSQL.and("b.operate_user is not null");
        params.put("versionId", currentVersion.getId());
        params.put("nodeCodeList", nodeCodeList);

        if (ListUtils.isNotEmpty(queryApproveNoteReq.getWarZone())) {
            whereSQL.and("c.war_zone in (:warZone)");
            params.put("warZone", queryApproveNoteReq.getWarZone());
        }

        if (ListUtils.isNotEmpty(queryApproveNoteReq.getCustomerShortName())) {
            whereSQL.and("c.customer_short_name in (:customerShortName)");
            params.put("customerShortName", queryApproveNoteReq.getCustomerShortName());
        }

        if (StringUtils.isNotBlank(queryApproveNoteReq.getProduct())) {
            List<String> productList;
            if (queryApproveNoteReq.getProduct().equals(Ppl13weekProductTypeEnum.PAAS.getName())) {
                productList = Ppl13weekProductTypeEnum.getPaasProductNameList();
            } else {
                productList = Arrays.asList(queryApproveNoteReq.getProduct());
            }
            whereSQL.and("b.ppl_order in (select ppl_order from ppl_order_audit_record_item where deleted = 0 "
                            + "and product in (:product) and audit_record_id in "
                            + "(select id from ppl_order_audit_record where version_id = :versionId) group by ppl_order)",
                    queryApproveNoteReq.getWarZone());
            params.put("product", productList);

        }

        String whereSql = whereToAnd(whereSQL.getSQL());

        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/approve_note.sql");
        sql = sql.replace("${FILTER}", whereSql);

        List<PplApproveNoteVO> raw = demandDBHelper.getRaw(PplApproveNoteVO.class, sql,
                params);
        Map<String, List<PplApproveNoteVO>> map = raw.stream()
                .collect(Collectors.groupingBy(PplApproveNoteVO::getNodeCode));

        Map<String, List<String>> groupByToCustomer = raw.stream()
                .collect(Collectors.groupingBy(
                        v -> String.join("-", v.getNodeCode(), v.getApproveResult(), v.getOperateUser(),
                                v.getApproveNote()), Collectors.mapping(PplApproveNoteVO::getCustomerShortName,
                                Collectors.collectingAndThen(Collectors.toSet(), ArrayList::new))));
        for (PplInnerApproveNodeDTO pplInnerApproveNodeDTO : highlight) {
            QueryApproveNoteResp resp = new QueryApproveNoteResp();
            resp.setNodeCode(pplInnerApproveNodeDTO.getNodeCode());
            resp.setNodeCodeName(pplInnerApproveNodeDTO.getNodeName());
            List<PplApproveNoteVO> approveNoteVOList = map.get(pplInnerApproveNodeDTO.getNodeCode());
            if (!CollectionUtils.isEmpty(approveNoteVOList)) {
                List<ApproveNoteVo> approveNoteVoList = new ArrayList<>();
                for (PplApproveNoteVO pplApproveNoteVO : approveNoteVOList) {
                    List<String> cutomerShortNameList = groupByToCustomer.get(
                            String.join("-", pplApproveNoteVO.getNodeCode(), pplApproveNoteVO.getApproveResult(),
                                    pplApproveNoteVO.getOperateUser(),
                                    pplApproveNoteVO.getApproveNote()));
                    if (CollectionUtils.isEmpty(cutomerShortNameList)) {
                        continue;
                    }
                    // 获取到了这个key就把这个删除掉， 避免重复获取。
                    groupByToCustomer.remove(
                            String.join("-", pplApproveNoteVO.getNodeCode(), pplApproveNoteVO.getApproveResult(),
                                    pplApproveNoteVO.getOperateUser(),
                                    pplApproveNoteVO.getApproveNote()));
                    approveNoteVoList.add(QueryApproveNoteResp.convert(pplApproveNoteVO, cutomerShortNameList));
                }
                resp.setApproveNoteVoList(approveNoteVoList);
            }
            list.add(resp);
        }

        return list;
    }

    @Override
    public void checkAutoPass() {
        PplInnerProcessService innerProcessService = SpringUtil.getBean(PplInnerProcessService.class);
        try {
            // 特殊战区级别自动审批
            innerProcessService.checkWarZoneAutoPass();
        } catch (Exception e){
            AlarmRobotUtil.doAlarm("checkWarZoneAutoPass", "特殊战区级别自动审批失败", null, false);
        }

        Date now = new Date();
        // 1、 查询出当前正在进行中的版本，并过滤数据
        QueryInnerVersionReq versionReq = new QueryInnerVersionReq();
        versionReq.setIsSla(Boolean.TRUE);
        PplInnerVersionResp pplInnerVersionResp = innerVersionService.queryProcessingVersion(
                versionReq);
        List<PplInnerProcessVersionSlaDTO> needPassList = new ArrayList<>();
        for (PplInnerVersionVO innerVersionVO : pplInnerVersionResp.getInnerVersionVOS()) {
            // 过滤 当前状态不属于需求沟通中的、当前审批时间已超时的
            if (innerVersionVO.getCurrentNode() != null && !innerVersionVO.getCurrentNode().equals("需求录入&沟通")
                    && innerVersionVO.getDeadlineTime() != null && now.after(innerVersionVO.getDeadlineTime())) {
                Map<String, PplInnerProcessVersionSlaDO> map = innerVersionVO.getSlaDOList().stream()
                        .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));
                PplInnerProcessVersionSlaDO slaDO = map.get(innerVersionVO.getCurrentNodeCode());
                // 且配置了 到期自动审批 / 到期无变化自动审批的
                if (slaDO != null && !slaDO.getDeadlineAutoPass().equals("CLOSE")) {
                    PplInnerProcessVersionSlaDTO pplInnerProcessVersionSlaDTO = BeanUtil.copyProperties(slaDO,
                            PplInnerProcessVersionSlaDTO.class);
                    pplInnerProcessVersionSlaDTO.setIndustryDept(innerVersionVO.getIndustryDept());
                    needPassList.add(pplInnerProcessVersionSlaDTO);
                }
            }
        }


        // 2、找到处于当前节点的 且audit_status 为 WAIT 的 PPL_ORDER 并且根据 role_attribute 进行分组
        for (PplInnerProcessVersionSlaDTO slaDTO : needPassList) {
            List<String> pplOrder = demandDBHelper.getRaw(String.class,
                    "select distinct ppl_order from ppl_order_audit_record where "
                            + " deleted = 0 and version_id = ? and node_code = ? and audit_status = ?",
                    slaDTO.getVersionId(), slaDTO.getNodeCode(), PplInnerVersionStatusEnum.WAIT.getCode());

            WhereSQL whereSQL = new WhereSQL();
            whereSQL.and("industry_dept = ?", slaDTO.getIndustryDept());
            whereSQL.and("node_code = ?", slaDTO.getNodeCode());
            whereSQL.and("audit_status = ?", PplInnerVersionStatusEnum.WAIT.getCode());
            whereSQL.and("ppl_order in (?)",pplOrder);
            List<PplOrderDO> all = demandDBHelper.getAll(PplOrderDO.class, whereSQL.getSQL(), whereSQL.getParams());
            Map<String, List<PplOrderDO>> groupBy = new HashMap<>();
            if (PplInnerProcessAttributeEnum.CUSTOMER.getCode().equals(slaDTO.getRoleAttribute())) {
                groupBy = all.stream()
                        .collect(Collectors.groupingBy(PplOrderDO::getCustomerShortName));
            } else if (PplInnerProcessAttributeEnum.WAR_ZONE.getCode().equals(slaDTO.getRoleAttribute())) {
                groupBy = all.stream()
                        .collect(Collectors.groupingBy(PplOrderDO::getWarZone));
            } else if (PplInnerProcessAttributeEnum.DEPT.getCode().equals(slaDTO.getRoleAttribute())) {
                groupBy = all.stream()
                        .collect(Collectors.groupingBy(PplOrderDO::getIndustryDept));
            }

            // 3、根据分组 进行批量审批操作
            groupBy.forEach((k, v) -> {
                if (slaDTO.getDeadlineAutoPass().equals(PplDeadlineAutoPassEnum.UNCHANGED_PASS.getCode())) {
                    // 过滤掉不满足 无变化时自动过单的
                    List<String> changeTypeList = new ArrayList<>(
                            v.stream().map(PplOrderDO::getChangeType).collect(Collectors.toSet()));
                    if (changeTypeList.size() != 1 || !changeTypeList.get(0)
                            .equals(OperateTypeEnum.ORIGINAL.getCode())) {
                        return;
                    }
                }
                try {
                    List<String> pplOrderList = v.stream().map(PplOrderDO::getPplOrder).collect(Collectors.toList());
                    BatchDealInnerPplOrderReq req = new BatchDealInnerPplOrderReq();
                    req.setIndustryDept(slaDTO.getIndustryDept());
                    req.setNodeCode(slaDTO.getNodeCode());
                    req.setPplOrderList(pplOrderList);
                    req.setApproveResult(Boolean.TRUE);
                    req.setApproveNote("到达过期时间，系统自动流转");
                    req.setVersionId(slaDTO.getVersionId());
                    innerProcessService.batchDealPplOrderList(req);
                } catch (Exception e) {
                    String exceptionMessage = String.format("行业PPL自动审批失败， 版本ID：%s， 部门：%s， 节点：%s， 异常：%s",
                            slaDTO.getVersionId(),
                            slaDTO.getIndustryDept(),
                            slaDTO.getNodeCode(),
                            e.getMessage());
                    log.error(exceptionMessage, e);
                    AlarmRobotUtil.doAlarm("checkAutoPass", exceptionMessage, null, false);
                }
            });

        }


    }

    /**
     * 特殊的战区自动过单逻辑
     */
    @Override
    public void checkWarZoneAutoPass() {
        QueryInnerVersionReq versionReq = new QueryInnerVersionReq();
        versionReq.setIsSla(Boolean.TRUE);
        versionReq.setIndustryDept(Arrays.asList(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName()));
        PplInnerVersionResp pplInnerVersionResp = innerVersionService.queryProcessingVersion(
                versionReq);

        PplInnerProcessService innerProcessService = SpringUtil.getBean(PplInnerProcessService.class);

        for (PplInnerVersionVO innerVersionVO : pplInnerVersionResp.getInnerVersionVOS()) {
            if (innerVersionVO.getCurrentNode() == null || PplInnerProcessNodeEnum.PRE_SUBMIT.getName().equals(innerVersionVO.getCurrentNode())){
                continue;
            }

            List<PplInnerProcessVersionSlaDO> slaList = innerVersionVO.getSlaDOList().stream()
                    .filter(v -> v.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode()))
                    .collect(Collectors.toList());
            if (ListUtils.isEmpty(slaList)){
                continue;
            }
            for (PplInnerProcessVersionSlaDO slaDO : slaList) {
                List<String> pplOrder = demandDBHelper.getRaw(String.class,
                        "select distinct ppl_order from ppl_order_audit_record where "
                                + " deleted = 0 and version_id = ? and node_code = ? and audit_status = ?",
                        slaDO.getVersionId(), slaDO.getNodeCode(), PplInnerVersionStatusEnum.WAIT.getCode());

                WhereSQL whereSQL = new WhereSQL();
                whereSQL.and("node_code = ?", slaDO.getNodeCode());
                whereSQL.and("war_zone = ?", PplIndustryPackageBaseDataConstant.SUPPLEMENT_WAR_ZONE);
                whereSQL.and("audit_status = ?", PplInnerVersionStatusEnum.WAIT.getCode());
                whereSQL.and("ppl_order in (?)",pplOrder);
                List<PplOrderDO> all = demandDBHelper.getAll(PplOrderDO.class, whereSQL.getSQL(), whereSQL.getParams());
                if (ListUtils.isEmpty(all)){
                    continue;
                }
                try {
                    List<String> pplOrderList = all.stream().map(PplOrderDO::getPplOrder).collect(Collectors.toList());
                    BatchDealInnerPplOrderReq req = new BatchDealInnerPplOrderReq();
                    req.setIndustryDept(innerVersionVO.getIndustryDept());
                    req.setNodeCode(slaDO.getNodeCode());
                    req.setPplOrderList(pplOrderList);
                    req.setApproveResult(Boolean.TRUE);
                    req.setApproveNote("行业专项包预测，战区级审批系统自动流转");
                    req.setVersionId(slaDO.getVersionId());
                    innerProcessService.batchDealPplOrderList(req);
                } catch (Exception e) {
                    String exceptionMessage = String.format("行业PPL-特殊战区自动审批失败， 版本ID：%s， 部门：%s， 节点：%s， 异常：%s",
                            slaDO.getVersionId(),
                            innerVersionVO.getIndustryDept(),
                            slaDO.getNodeCode(),
                            e.getMessage());
                    log.error(exceptionMessage, e);
                    AlarmRobotUtil.doAlarm("checkWarZoneAutoPass", exceptionMessage, null, false);
                }

            }
        }


    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void batchAddProcessingPplOrderForPackageBaseData(List<SavePplDraftReq> reqList) {
        if (ListUtils.isEmpty(reqList)) {
            return;
        }
        Set<Long> versionIdSet = reqList.stream().map(SavePplDraftReq::getVersionId).collect(Collectors.toSet());
        if (versionIdSet.size() != 1) {
            throw new BizException("流程中批量添加PPL单，版本ID不一致，处理失败");
        }
        Long versionId = versionIdSet.stream().findFirst().get();
        List<PplInnerProcessVersionSlaDO> slaList = innerVersionService.getVersionSlaListWithoutPreSubmit(
                versionId);
        // 审批节点排序
        LinkedList<PplInnerProcessVersionSlaDO> sortVersionSlaList = ListUtils2.sortChain(slaList,
                PplInnerProcessVersionSlaDO::getIsBeginNode,
                PplInnerProcessVersionSlaDO::getNextSlaId, PplInnerProcessVersionSlaDO::getId);
        Map<String, PplInnerProcessVersionSlaDO> nodeCodeMap = sortVersionSlaList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));
        List<PplOrderDO> orderDOList = new ArrayList<>();
        Map<String, List<PplOrderAuditRecordItemDO>> itemMap = new HashMap<>();
        Map<String, PplOrderAuditRecordDO> auditRecordMap = new HashMap<>();
        Map<String, SavePplDraftReq> reqMap = new HashMap<>();
        for (SavePplDraftReq req : reqList) {
            PplInnerProcessVersionSlaDO currentNode = nodeCodeMap.get(req.getNodeCode());
            PplOrderDO pplOrderDO = createPplOrder(req, currentNode);
            if (PplInnerProcessAttributeEnum.WAR_ZONE.getCode().equals(currentNode.getRoleAttribute())) {
                pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.AUDITED.getCode());
            } else {
                pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
            }
            orderDOList.add(pplOrderDO);

            List<PplOrderAuditRecordItemDO> auditPplItems = createAuditPplItems(req);
            itemMap.put(pplOrderDO.getPplOrder(), auditPplItems);

            PplOrderAuditRecordDO auditRecordDO = new PplOrderAuditRecordDO();
            auditRecordDO.setVersionId(req.getVersionId());
            auditRecordDO.setPplOrder(pplOrderDO.getPplOrder());
            auditRecordDO.setOperateUser(req.getSubmitUser());
            auditRecordMap.put(pplOrderDO.getPplOrder(), auditRecordDO);

            reqMap.put(req.getPplOrder(), req);
        }

        demandDBHelper.insert(orderDOList);

        for (PplOrderDO pplOrderDO : orderDOList) {
            PplOrderAuditRecordDO auditRecordDO = auditRecordMap.get(pplOrderDO.getPplOrder());
            List<PplOrderAuditRecordItemDO> resources = itemMap.get(pplOrderDO.getPplOrder());
            SavePplDraftReq req = reqMap.get(pplOrderDO.getPplOrder());
            for (PplInnerProcessVersionSlaDO slaDO : sortVersionSlaList) {
                if (slaDO.getNodeCode().equals(req.getNodeCode())) {
                    auditRecordDO.setNodeCode(slaDO.getNodeCode());
                    if (PplInnerProcessAttributeEnum.WAR_ZONE.getCode().equals(slaDO.getRoleAttribute())) {
                         auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.AUDITED.getCode());
                    } else {
                        auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
                    }
                    auditRecordDO.setId(null);
                    auditRecordDO.setOperateUser(null);
                    demandDBHelper.insert(auditRecordDO);
                    batchInsertAuditItem(auditRecordDO.getId(), resources);
                    break;
                } else {
                    auditRecordDO.setNodeCode(slaDO.getNodeCode());
                    auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.PASS.getCode());
                    auditRecordDO.setId(null);
                    auditRecordDO.setOperateUser("system");
                    auditRecordDO.setApproveNote("审批过程中根据包基准一键补充ppl单据,系统初始化默认审批记录");
                    demandDBHelper.insert(auditRecordDO);
                }
            }
        }

    }

    private PplOrderDO createPplOrder(SavePplDraftReq req, PplInnerProcessVersionSlaDO currentNode) {
        String pplOrder = pplCommonService.generatePplOrderId("N");
        req.setPplOrder(pplOrder);
        PplOrderDO pplOrderDO = req.transToPplOrder();
        pplOrderDO.setPplOrder(pplOrder);
        pplOrderDO.setStatus("VALID");
        pplOrderDO.setNodeCode(req.getNodeCode());
        pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        pplOrderDO.setCurrentProcessor(getNodeApproveUser(currentNode, pplOrderDO));
        pplOrderDO.setCurrentRole(currentNode.getApproveRole());
        pplOrderDO.setChangeType(OperateTypeEnum.INSERT.getCode());
        pplOrderDO.setSubmitUser(req.getSubmitUser());
        return  pplOrderDO;
    }

    private List<PplOrderAuditRecordItemDO> createAuditPplItems(SavePplDraftReq req) {
        return req.getResources().stream().map(resource -> {
            PplOrderAuditRecordItemDO pplItemDO = new PplOrderAuditRecordItemDO();
            pplItemDO.setPplOrder(req.getPplOrder());
            pplItemDO.setProduct(resource.getProduct());
            if (org.nutz.lang.Strings.isBlank(resource.getPplId())) {
                String pplId = pplCommonService.generatePplItemId(req.getPplOrder());
                resource.setPplId(pplId);
            }
            pplItemDO.setPplId(resource.getPplId());
            pplItemDO.setDemandType(resource.getDemandType());
            pplItemDO.setDemandScene(resource.getDemandScene());
            pplItemDO.setProjectName(resource.getProjectName());
            pplItemDO.setBillType(resource.getBillType());
            pplItemDO.setWinRate(resource.getWinRate());
            pplItemDO.setImportantDemand(resource.getImportantDemand());
            pplItemDO.setBeginBuyDate(DateUtils.parseLocalDate(resource.getBeginBuyDate()));
            pplItemDO.setEndBuyDate(DateUtils.parseLocalDate(resource.getEndBuyDate()));
            pplItemDO.setBeginElasticDate(DateUtils.parseLocalTime(resource.getBeginElasticDate()));
            pplItemDO.setEndElasticDate(DateUtils.parseLocalTime(resource.getEndElasticDate()));
            pplItemDO.setNote(resource.getNote());
            pplItemDO.setRegionName(resource.getRegionName());
            pplItemDO.setZoneName(resource.getZoneName());
            pplItemDO.setIsStrongDesignateZone(resource.getIsStrongDesignateZone());
            pplItemDO.setInstanceType(resource.getInstanceType());
            pplItemDO.setInstanceModel(resource.getInstanceModel());
            pplItemDO.setInstanceNum(resource.getInstanceNum());
            pplItemDO.setAlternativeInstanceType(
                    org.nutz.lang.Strings.join(";", resource.getAlternativeInstanceType()));
            pplItemDO.setAlternativeZoneName(
                    org.nutz.lang.Strings.join(";", resource.getAlternativeZoneName()));
            pplItemDO.setAffinityType(resource.getAffinityType());
            pplItemDO.setAffinityValue(resource.getAffinityValue());
            pplItemDO.setSystemDiskType(resource.getSystemDiskType());
            pplItemDO.setSystemDiskStorage(resource.getSystemDiskStorage());
            pplItemDO.setSystemDiskNum(resource.getSystemDiskNum());
            pplItemDO.setDataDiskType(resource.getDataDiskType());
            pplItemDO.setDataDiskStorage(resource.getDataDiskStorage());
            pplItemDO.setDataDiskNum(resource.getDataDiskNum());
            pplItemDO.setTotalCore(resource.getTotalCoreNum());
            pplItemDO.setTotalDisk(resource.getTotalDiskNum());
            pplItemDO.setBizId(resource.getBizId());
            pplItemDO.setAppRole(resource.getAppRole());
            // gpu相关字段
            pplItemDO.setGpuProductType(resource.getGpuProductType());
            pplItemDO.setGpuType(resource.getGpuType());
            pplItemDO.setGpuNum(resource.getGpuNum());
            pplItemDO.setIsAcceptAdjust(Boolean.FALSE);
            if (!CollectionUtils.isEmpty(resource.getAcceptGpu())) {
                pplItemDO.setAcceptGpu(org.nutz.lang.Strings.join(";", resource.getAcceptGpu()));
                pplItemDO.setIsAcceptAdjust(resource.getIsAcceptAdjust());
            }
            pplItemDO.setTotalGpuNum(resource.getTotalGpuNum());
            pplItemDO.setBizScene(resource.getBizScene());
            pplItemDO.setBizDetail(resource.getBizDetail());
            pplItemDO.setServiceTime(resource.getServiceTime());
            pplItemDO.placementGroupSet(resource.getPlacementGroupList());

            pplItemDO.setDatabaseName(resource.getDatabaseName());
            pplItemDO.setMoreThanOneAZ(resource.getMoreThanOneAZ());
            pplItemDO.setDatabaseStorageType(resource.getDatabaseStorageType());
            pplItemDO.setDeployType(resource.getDeployType());
            pplItemDO.setFrameworkType(resource.getFrameworkType());
            pplItemDO.setSliceNum(resource.getSliceNum());
            pplItemDO.setReplicaNum(resource.getReplicaNum());
            pplItemDO.setReadOnlyNum(resource.getReadOnlyNum());
            pplItemDO.setDatabaseSpecs(resource.getDatabaseSpecs());
            pplItemDO.setDatabaseStorage(resource.getDatabaseStorage());
            pplItemDO.setTotalDatabaseStorage(resource.getTotalDatabaseStorage());

            pplItemDO.setCosStorageType(resource.getCosStorageType());
            pplItemDO.setCosAZ(resource.getCosAZ());
            pplItemDO.setCosStorage(resource.getCosStorage());
            pplItemDO.setTotalCosStorage(resource.getTotalCosStorage());
            pplItemDO.setBandwidth(resource.getBandwidth());
            pplItemDO.setQps(resource.getQps());

            pplItemDO.setInstanceModelCoreNum(resource.getInstanceModelCoreNum());
            pplItemDO.setInstanceModelRamNum(resource.getInstanceModelRamNum());
            pplItemDO.setTotalMemory(resource.getTotalMemory());

            return pplItemDO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public String addProcessingPplOrder(SavePplDraftReq req) {
        if (req.getVersionId() == null) {
            throw new BizException("versionId 不能为空");
        }

        if (Strings.isBlank(req.getNodeCode())) {
            throw new BizException("nodeCode 不能为空");
        }
        req.check();

        List<PplInnerProcessVersionSlaDO> versionSlaList = innerVersionService.getVersionSlaListWithoutPreSubmit(
                req.getVersionId());
        Map<String, PplInnerProcessVersionSlaDO> nodeCodeMap = versionSlaList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));
        PplInnerProcessVersionSlaDO currentNode = nodeCodeMap.get(req.getNodeCode());

        String pplOrder = pplCommonService.generatePplOrderId("N");
        req.setPplOrder(pplOrder);

        String beginBuyDate = req.getResources().get(0).getBeginBuyDate();
        YearMonth parse = cloud.demand.app.common.utils.DateUtils.parse(beginBuyDate);
        PplOrderDO pplOrderDO = createPplOrder(req, currentNode);

        demandDBHelper.insert(pplOrderDO);

        List<PplOrderAuditRecordItemDO> resources = createAuditPplItems(req);

        PplOrderAuditRecordDO auditRecordDO = new PplOrderAuditRecordDO();
        auditRecordDO.setVersionId(req.getVersionId());
        auditRecordDO.setPplOrder(pplOrderDO.getPplOrder());
        auditRecordDO.setOperateUser(req.getSubmitUser());
        demandDBHelper.insert(auditRecordDO);
        Long preRecordId = auditRecordDO.getId();
        auditRecordDO.setId(null);
        demandDBHelper.insert(auditRecordDO);
        Long curRecordId = auditRecordDO.getId();

        // 构造审批节点的审批记录及item
        for (PplInnerProcessVersionSlaDO slaDO : versionSlaList) {

            if (slaDO.getNodeCode().equals(req.getNodeCode())) {
                auditRecordDO.setNodeCode(slaDO.getNodeCode());
                auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
                auditRecordDO.setId(null);
                auditRecordDO.setOperateUser(null);
                demandDBHelper.insert(auditRecordDO);
                batchInsertAuditItem(auditRecordDO.getId(), resources);
                break;
            } else {
                auditRecordDO.setNodeCode(slaDO.getNodeCode());
                auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.PASS.getCode());
                auditRecordDO.setId(null);
                auditRecordDO.setOperateUser("system");
                auditRecordDO.setApproveNote("审批过程中新增ppl单据,系统初始化默认审批记录");
                demandDBHelper.insert(auditRecordDO);
            }
        }

        // 记录日志
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        resources.forEach(itemAudit -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(pplOrderDO, afterAllFieldDTO);
            BeanUtils.copyProperties(itemAudit, afterAllFieldDTO);
            afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            newDTOs.add(recordNewDTO);
        });
        if (!CollectionUtils.isEmpty(newDTOs)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_AUDIT_ADD.getCode(), LoginUtils.getUserNameWithSystem(),
                            newDTOs));
        }

        return pplOrder;

    }

    private void batchInsertAuditItem(Long recordId,
            List<PplOrderAuditRecordItemDO> resource) {
        for (PplOrderAuditRecordItemDO itemDO : resource) {
            itemDO.setId(null);
            itemDO.setAuditRecordId(recordId);
        }
        demandDBHelper.insertBatchWithoutReturnId(resource);
    }


    public void sendMail(String acceptUser, String sendUser, String sendType, String nodeCode, Long versionId,
            List<String> pplOrderList, String content) {
        log.info("[PplInnerSendMessage] sendType: " + sendType + " acceptUser: " + acceptUser + " nodeCode: "
                + nodeCode);
        Boolean isTest = Strings.isNotBlank(testEnv.get());
        //  测试环境关掉
        if (isTest) {
            if (acceptUser.contains("kaijiazhang") || acceptUser.contains("oliverychen")) {
                // 锴佳能收到测试环境邮件
                acceptUser = "kaijiazhang;oliverychen;erickssu";
            } else {
                return;
            }
        }
        String prefix = isTest ? "exp-" : "";
        String deadlineTime = "";
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (sendType.equals("MODIFY")) {
            alert.sendMail(acceptUser, "行业内部PPL信息干预通知", content);
            return;
        } else if (sendType.equals("FINISH")) {
            alert.sendMail(acceptUser, "行业内部PPL流程完结通知",
                    "本次13周预测版本已经完成，请前往CRP系统提交新周期的需求预测。<a href='https://" + prefix
                            + "crp.woa.com/13ppl/approval-process/forecast'>点击前往</a>。");
            return;
        } else if (sendType.equals("REFUSE")) {
            alert.sendMail(acceptUser, "行业内部PPL信息拒绝通知",
                    "您关注的需求单号为: " + pplOrderList.get(0) + "的需求内容已被拒绝,请留意,详细信息请前往CRP查看。<a href='https://"
                            + prefix + "crp.woa.com/13ppl/approval-process/forecast'>点击前往</a>。<br />");
            return;
        } else if (sendType.equals("REFUSE_PRE_SUBMIT")) {
            // 打回操作的deadline时间 单独查询。
            PplInnerProcessVersionSlaDO versionSla =
                    demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                            "where version_id=? and deadline_type='ENTER'", versionId);
            deadlineTime = formatter.format(versionSla.getDeadlineTime());
            //打回预提交
            alert.sendMail(acceptUser, "行业内部PPL信息打回通知", "您关注的需求单号为: " + pplOrderList.get(0)
                    + "的需求内容已被 " + sendUser + "打回,请务必在: " + deadlineTime
                    + " 前修改并重新提交，详细信息请前往CRP查看操作。<a href='https://" + prefix
                    + "crp.woa.com/13ppl/approval-process/forecast'>点击前往</a>。<br />");
            return;
        }
        List<PplInnerProcessVersionSlaDO> versionSlas =
                demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                        "where version_id=? and deadline_type='EXIT'", versionId);
        Map<String, PplInnerProcessVersionSlaDO> versionSlaMap = ListUtils.toMap(versionSlas, o -> o.getNodeCode(),
                o -> o);
        deadlineTime = formatter.format(versionSlaMap.get(nodeCode).getDeadlineTime());
        if (sendType.equals("AUDIT")) {
            alert.sendMail(acceptUser, "行业内部PPL待审批通知", "您当前新增了 " + pplOrderList.size()
                    + " 条审批任务，请前往CRP系统进行审批，<a href='https://" + prefix
                    + "crp.woa.com/13ppl/approval-process/approval'>点击前往</a>。<br />"
                    + "审批截止时间: " + deadlineTime + " <br />");
        }
    }

    public void sendTodoAndMoa(String acceptUser, PplInnerProcessVersionSlaDO nodeCode, Long versionId,
            String industryDept, String warZone, String customerShortName, String approveNote) {
        Boolean isTest = Strings.isNotBlank(testEnv.get());
        //  测试环境关掉
        if (isTest) {
            if (acceptUser.contains("kaijiazhang") || acceptUser.contains("oliverychen")) {
                // 锴佳能收到测试环境邮件
//                acceptUser = "kaijiazhang;oliverychen;erickssu";
            } else {
                return;
            }
        }
        PplInnerProcessVersionDO versionDO = demandDBHelper.getByKey(PplInnerProcessVersionDO.class, versionId);
        WhereSQL where = new WhereSQL();
        Map<String, Object> params = new HashMap<>();
        where.and("b.version_id = :versionId");
        where.and("b.node_code = :nodeCode");
        where.and("c.industry_dept = :industryDept");
        params.put("versionId", versionId);
        params.put("nodeCode", nodeCode.getNodeCode());
        params.put("industryDept", industryDept);
        if (Strings.isNotBlank(warZone)) {
            where.and("c.war_zone = :warZone");
            params.put("warZone", warZone);
        }
        if (Strings.isNotBlank(customerShortName)) {
            where.and("c.customer_short_name = :customerShortName");
            params.put("customerShortName", customerShortName);
        }
        String whereSql = whereToAnd(where.getSQL());

        String sql = ORMUtils.getSql("/sql/ppl13week/inner_process/ppl_order_join_audit_record_item.sql");
        sql = sql.replace("${FILTER}", whereSql);

        List<PplOrderJoinAuditRecordItemVO> list = demandDBHelper.getRaw(PplOrderJoinAuditRecordItemVO.class, sql,
                params);
        List<String> pplOrderList = list.stream().map(PplOrderJoinAuditRecordItemVO::getPplOrder)
                .collect(Collectors.toList());

        // 查询出上一版本的item
        List<PplOrderJoinAuditRecordItemVO> lastVersionItemList = new ArrayList<>();
        PplInnerProcessVersionDO lastVersion = innerVersionService.getLastVersion(versionDO.getId());
        if (lastVersion != null) {
            List<PplOrderJoinAuditRecordItemVO> auditRecordItemByVersionId = auditRecordService.getAuditRecordItemByVersionId(
                    lastVersion.getId());
            if (!CollectionUtils.isEmpty(auditRecordItemByVersionId)) {
                // 以pplOrder的维度去比较，
                lastVersionItemList = auditRecordItemByVersionId.stream()
                        .filter(v -> pplOrderList.contains(v.getPplOrder())).collect(Collectors.toList());
            }
        }

        Map<String, List<PplOrderJoinAuditRecordItemVO>> lastVersionProductToList = lastVersionItemList.stream()
                .collect(Collectors.groupingBy(PplOrderJoinAuditRecordItemVO::getProduct));

        Map<String, List<PplOrderJoinAuditRecordItemVO>> productToList = list.stream()
                .collect(Collectors.groupingBy(PplOrderJoinAuditRecordItemVO::getProduct));

        productToList.forEach((k, v) -> {
            List<PplOrderJoinAuditRecordItemVO> lastVersionItem = lastVersionProductToList.get(k);
            SummaryData resourceSummaryData = getResourceSummaryData(k, v, lastVersionItem);
            String demandRange = industryDept + (Strings.isBlank(warZone) ? "" : "/" + warZone)
                    + (Strings.isBlank(customerShortName) ? "" : "/" + customerShortName);
            String timeRange = versionDO.getDemandBeginYear() + "年" + versionDO.getDemandBeginMonth() + "月 ～ "
                    + versionDO.getDemandEndYear() + "年" + versionDO.getDemandEndMonth() + "月";
            List<TodoService.ListView> listViewList = Arrays.asList(
                    new TodoService.ListView("需求范围", demandRange),
                    new TodoService.ListView("产品", k),
                    new TodoService.ListView("需求年月", timeRange),
                    new TodoService.ListView("审批节点", nodeCode.getNodeName()),
                    new TodoService.ListView("操作提示",
                            "如果您希望对审批内容进行拒绝、干预、分析，请前往CRP系统网页端操作"),
                    new TodoService.ListView("参考意见", Strings.isNotBlank(approveNote) ? approveNote : "无"),
                    new TodoService.ListView("本周期资源量", resourceSummaryData.getTotalResourceNum()),
                    new TodoService.ListView("较上周期变化量", resourceSummaryData.getTotalChangeResourceNum()),
                    new TodoService.ListView("TOP10客户资源量&变化量",
                            resourceSummaryData.getTopCustomerResourceNum()));

            // orderId = 版本Id + 产品 + 节点名称 + 需求范围
            // String orderId = String.join("-", versionId.toString(), k, nodeCode.getNodeName(), demandRange);
            // 原本想用上面有含义的来表示这个orderId,也能直接查取，结果发现要表示出来实在是太长了。。。
            // 待办的数据表长度都只有50，可能会撑爆，不好直接改别人的,因此直接用uuid
            String orderId = UUID.randomUUID().toString();
            ApprovalData approvalData = new ApprovalData();
            approvalData.setOrderId(orderId);
            approvalData.setTaskId(orderId);
            approvalData.setSourceApp("行业PPL管理");
            approvalData.setSourceEvent("待行业审批");
            approvalData.setSourceAppCnName("行业PPL管理");
            approvalData.setActivity("待行业审批");
            approvalData.setHandler(acceptUser);
            approvalData.setFormUrl(domainSupplier.get() + "/13ppl/approval-process/approval");
            approvalData.setIsCallBackApi(0);
            approvalData.setIsPushMyoa(1);
            approvalData.setCallBackUrl("http://localhost/cloud-demand-app/api/NOT-VALID");
            approvalData.setListView(listViewList);
            approvalData.setSystem("CRP");
            approvalData.setIsRemind(0);
            executor.execute(() -> {
                pplCommonService.createTodo(approvalData);
            });
            todoLog(versionId, nodeCode.getNodeCode(), k, industryDept, warZone,
                    customerShortName, approvalData.getOrderId(), acceptUser);
        });
    }

    @Override
    public void acceptTodoCallback(ApprovalMessageBody messageBody) {
        boolean pass = (messageBody.getApproveResult() == 0);
        Boolean isTest = Strings.isNotBlank(testEnv.get());
        //  测试环境关掉
        String prefix = isTest ? "exp-" : "";
        if (pass) {
            // 如果同意
            IndustryPplTodoRecordDO pplTodoRecordDO = demandDBHelper.getOne(IndustryPplTodoRecordDO.class,
                    "where biz_key = ?",
                    messageBody.getApproverOrder());
            if (pplTodoRecordDO == null) {
                return;
            }
            // 根据record 查出当前是否还存在未审批的item
            List<PplOrderJoinAuditRecordItemVO> list = auditRecordService.getWaitPpl(pplTodoRecordDO.getVersionId(),
                    pplTodoRecordDO.getProduct(), pplTodoRecordDO.getNodeCode(),
                    pplTodoRecordDO.getIndustryDept(), pplTodoRecordDO.getWarZone(),
                    pplTodoRecordDO.getCustomerShortName());
            // 待处理的ppl
            List<String> waitPplOrder = list.stream().map(PplOrderJoinAuditRecordItemVO::getPplOrder).distinct()
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(waitPplOrder)) {
                try {
                    BatchDealInnerPplOrderReq batchDealInnerPplOrderReq = new BatchDealInnerPplOrderReq();
                    batchDealInnerPplOrderReq.setVersionId(pplTodoRecordDO.getVersionId());
                    batchDealInnerPplOrderReq.setNodeCode(pplTodoRecordDO.getNodeCode());
                    batchDealInnerPplOrderReq.setIndustryDept(pplTodoRecordDO.getIndustryDept());
                    batchDealInnerPplOrderReq.setApproveNote("");
                    batchDealInnerPplOrderReq.setPplOrderList(waitPplOrder);
                    batchDealInnerPplOrderReq.setApproveResult(Boolean.TRUE);
                    batchDealInnerPplOrderReq.setUsername(messageBody.getApprover());
                    PplInnerProcessService innerProcessService = SpringUtil.getBean(PplInnerProcessService.class);
                    innerProcessService.batchDealPplOrderList(batchDealInnerPplOrderReq);
                } catch (Exception e) {
                    if (!e.getMessage().equals("当前提交的单据已被处理，请刷新页面")) {
                        AlarmRobotUtil.doAlarm("acceptTodoCallback",
                                "待办完成失败，taskOrderId: " + messageBody.getApproverOrder() + " exception:" + e, null,
                                false);
                        throw new BizException("待办完成失败，taskOrderId: " + messageBody.getApproverOrder());
                    }
                }
            }
        } else {
            // 如果拒绝
            String acceptUser = messageBody.getApprover() + ";kaijiazhang" + ";oliverychen";
//            alert.sendMail(acceptUser, "PPL驳回二次确认提醒",
//                    "您刚刚在【待办中心】/【移动端】操作了批量PPL驳回，PPL驳回后将完全失效，且操作不可逆，请在PC端进行确认操作。"
//                            + "<a href='https://" + prefix
//                            + "crp.woa.com/13ppl/approval-process/approval?tab=MyApproval&MyApprovalTab=MY_REJECT'>点击前往</a>。<br />");
            dictService.eventNotice(CrpEventEnum.INNER_PROCESS_REFUSE_CONFIRM.getCode(),
                    null, null, null, acceptUser);
        }

        todoService.callTodoSubmitTaskResult(messageBody.getApproverOrder(), messageBody.getApprover(),
                Boolean.TRUE, messageBody.getApproveMemo());
    }

    @Override
    public void finishIndustryTodo() {
        List<IndustryPplTodoRecordDO> all = demandDBHelper.getAll(IndustryPplTodoRecordDO.class,
                "where push_status = ?", 1);
        for (IndustryPplTodoRecordDO pplTodoRecordDO : all) {
            try {
                List<PplOrderJoinAuditRecordItemVO> list = auditRecordService.getWaitPpl(pplTodoRecordDO.getVersionId(),
                        pplTodoRecordDO.getProduct(), pplTodoRecordDO.getNodeCode(),
                        pplTodoRecordDO.getIndustryDept(), pplTodoRecordDO.getWarZone(),
                        pplTodoRecordDO.getCustomerShortName());
                if (CollectionUtils.isEmpty(list)) {
                    // 如果都处理完了， 那么完成待办
                    todoService.callTodoSubmitTaskResult(pplTodoRecordDO.getBizKey(), "system",
                            Boolean.TRUE, "系统监控，任务已完成，完成待办");
                    pplTodoRecordDO.setPushStatus(2);
                    demandDBHelper.update(pplTodoRecordDO);
                }
            } catch (Exception e) {
                AlarmRobotUtil.doAlarm("finishIndustryTodo",
                        "行业审批完成待办异常,Industry_ppl_todo_record id :" + pplTodoRecordDO.getId() + " 异常：" + e,
                        null, false);
            }
        }
    }

    public void todoLog(Long versionId, String nodeCode, String product, String industryDept, String warZone,
            String customerShortName, String bizKey, String acceptUser) {
        IndustryPplTodoRecordDO industryPplTodoRecordDO = new IndustryPplTodoRecordDO();
        industryPplTodoRecordDO.setVersionId(versionId);
        industryPplTodoRecordDO.setNodeCode(nodeCode);
        industryPplTodoRecordDO.setProduct(product);
        industryPplTodoRecordDO.setIndustryDept(industryDept);
        industryPplTodoRecordDO.setWarZone(warZone);
        industryPplTodoRecordDO.setCustomerShortName(customerShortName);
        industryPplTodoRecordDO.setBizKey(bizKey);
        industryPplTodoRecordDO.setAcceptUser(acceptUser);
        industryPplTodoRecordDO.setPushStatus(1);
        demandDBHelper.insert(industryPplTodoRecordDO);
    }


    @Data
    @ToString
    public static class SummaryData {

        private String totalResourceNum = "";
        private String totalChangeResourceNum = "";
        private String topCustomerResourceNum = "";
    }

    @Data
    @ToString
    public static class CustomerResourceNum {

        private String customerShortName;
        private Integer num;
    }

    public SummaryData getResourceSummaryData(String product, List<PplOrderJoinAuditRecordItemVO> list,
            List<PplOrderJoinAuditRecordItemVO> lastVersionItemList) {
        SummaryData summaryData = new SummaryData();
        String totalResourceNum = "";
        String totalChangeResourceNum = "";
        StringBuilder topCustomerResourceNum = new StringBuilder();
        String unit;
        Function<PplOrderJoinAuditRecordItemVO, Integer> func;
        if (product.equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            func = PplOrderJoinAuditRecordItemVO::getTotalGpuNum;
            unit = "卡";
        } else if (product.equals(Ppl13weekProductTypeEnum.BM.getName())) {
            func = PplOrderJoinAuditRecordItemVO::getInstanceNum;
            unit = "台";
        } else {
            func = PplOrderJoinAuditRecordItemVO::getTotalCore;
            unit = "核";
        }

        if (lastVersionItemList == null) {
            lastVersionItemList = new ArrayList<>();
        }
        // 客户级资源量map
        Map<String, Integer> customerToResourceNum = new HashMap<>();
        for (PplOrderJoinAuditRecordItemVO pplOrderJoinAuditRecordItemVO : list) {
            Integer resourceNum = func.apply(pplOrderJoinAuditRecordItemVO);
            if (pplOrderJoinAuditRecordItemVO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                resourceNum = -resourceNum;
            }
            if (customerToResourceNum.get(pplOrderJoinAuditRecordItemVO.getCustomerShortName()) != null) {
                customerToResourceNum.put(pplOrderJoinAuditRecordItemVO.getCustomerShortName(),
                        customerToResourceNum.get(pplOrderJoinAuditRecordItemVO.getCustomerShortName()) + resourceNum);
            } else {
                customerToResourceNum.put(pplOrderJoinAuditRecordItemVO.getCustomerShortName(), resourceNum);
            }
        }

        // 上版本客户级资源量map
        Map<String, Integer> lastVersionCustomerToResourceNum = new HashMap<>();
        for (PplOrderJoinAuditRecordItemVO pplOrderJoinAuditRecordItemVO : lastVersionItemList) {
            Integer resourceNum = func.apply(pplOrderJoinAuditRecordItemVO);
            if (pplOrderJoinAuditRecordItemVO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                resourceNum = -resourceNum;
            }
            if (lastVersionCustomerToResourceNum.get(pplOrderJoinAuditRecordItemVO.getCustomerShortName()) != null) {
                lastVersionCustomerToResourceNum.put(pplOrderJoinAuditRecordItemVO.getCustomerShortName(),
                        lastVersionCustomerToResourceNum.get(pplOrderJoinAuditRecordItemVO.getCustomerShortName())
                                + resourceNum);
            } else {
                lastVersionCustomerToResourceNum.put(pplOrderJoinAuditRecordItemVO.getCustomerShortName(), resourceNum);
            }
        }
        // 本周期资源量
        int currentVersionNum = customerToResourceNum.values().stream().mapToInt(v -> v).sum();
        totalResourceNum = currentVersionNum + unit;

        // 较上周变化量
        int lastVersionNum = lastVersionCustomerToResourceNum.values().stream().mapToInt(v -> v).sum();
        totalChangeResourceNum = (currentVersionNum - lastVersionNum) + unit;
        if ((currentVersionNum - lastVersionNum) > 0) {
            totalChangeResourceNum = "+" + totalChangeResourceNum;
        } else if ((currentVersionNum - lastVersionNum) == 0) {
            totalChangeResourceNum = "无变化";
        }

        // top10客户资源量
        List<CustomerResourceNum> customerResourceNumList = new ArrayList<>();
        customerToResourceNum.forEach((k, v) -> {
            CustomerResourceNum customerResourceNum = new CustomerResourceNum();
            customerResourceNum.setCustomerShortName(k);
            customerResourceNum.setNum(v);
            customerResourceNumList.add(customerResourceNum);
        });
        customerResourceNumList.sort(Comparator.comparing(CustomerResourceNum::getNum).reversed());
        for (int i = 1; i <= customerResourceNumList.size(); i++) {
            if (i == 11) {
                break;
            }
            CustomerResourceNum customerResourceNum = customerResourceNumList.get(i - 1);
            String customerShortName = customerResourceNum.getCustomerShortName();
            Integer num = customerResourceNum.getNum();
            int last = lastVersionCustomerToResourceNum.get(customerResourceNum.getCustomerShortName()) == null ? 0
                    : lastVersionCustomerToResourceNum.get(customerResourceNum.getCustomerShortName());
            String changeResourceNum = (num - last) + unit;
            String prefix = "";
            if ((num - last) > 0) {
                prefix = "+";
            } else if ((num - last) == 0) {
                changeResourceNum = "无变化";
            }
            String s = customerShortName + ": " + num + unit
                    + " (" + prefix + changeResourceNum + ") \n";
            if (customerResourceNumList.size() > 1) {
                String serialNumber = i + "、";
                s = serialNumber + s;
            }
            topCustomerResourceNum.append(s);
        }

        summaryData.setTotalResourceNum(totalResourceNum);
        summaryData.setTotalChangeResourceNum("(" + totalChangeResourceNum + ")");
        summaryData.setTopCustomerResourceNum(topCustomerResourceNum.toString());
        return summaryData;
    }

    /**
     * 构建
     *
     * @param changeDTO
     * @param nodeCode
     */
    private void buildUpdateContent(PplOrderAuditRecordItemChangeDTO changeDTO, String nodeCode) {

        String content = "";
        String content1 =
                "<p style='font-size: 14px'>您关注的PPL（单号为 " + changeDTO.getPplOrder()
                        + "）内容在审批流程中发生变更，变更内容如下，请留意：</p>";

        String content2 = "<p style='font-size: 14px'><b>概览</b></p>";

        // 概览
        String unit = Ppl13weekProductTypeEnum.getUnitByProductName(changeDTO.getProduct());
        Integer beforeResource = changeDTO.getProductTypeUnit("BEFORE");
        Integer afterResource = changeDTO.getProductTypeUnit("AFTER");

        List<String> tableTitle = Lang.list("需求年月", "战区", "项目名称", "产品", "客户", "需求类型", "审批人",
                "审批意见", "干预人及干预原因", "变更前" + unit + "数", "变更后" + unit + "数");
        List<String> list = Arrays.asList(
                changeDTO.getYearMonth() != null ? changeDTO.getYearMonth() : " ",
                changeDTO.getWarZone() != null ? changeDTO.getWarZone() : " ",
                changeDTO.getProjectName() != null ? changeDTO.getProjectName() : " ",
                changeDTO.getProduct() != null ? changeDTO.getProduct() : " ",
                changeDTO.getCustomerShortName() != null ? changeDTO.getCustomerShortName() : " ",
                changeDTO.getDemandType() != null ? changeDTO.getDemandType() : " ",
                changeDTO.getAuditor() != null ? changeDTO.getAuditor() : " ",
                changeDTO.getApproveNote() != null ? changeDTO.getApproveNote() : " ",
                changeDTO.getModifyReason() != null ? changeDTO.getModifyReason() : " ",
                beforeResource != null ? beforeResource.toString() : " ",
                afterResource != null ? afterResource.toString() : " ");
        List<List<String>> dataList = new ArrayList<>();
        dataList.add(list);
        String content3 = mailTableService.genVerticalTable(tableTitle, dataList);

        String content4 = "<p style='font-size: 14px'><b>明细</b></p>";

        List<String> tableTitle2 = Lang.list("PPL-ID", "地域", "机型", "变动类型", "变动内容");
        List<List<String>> data = ListUtils.transform(changeDTO.getChangeItemDTOList(),
                (o) -> Lang.list(o.getPplId(), o.getRegionName(), o.getInstanceModel(), o.getChangeType(),
                        o.getChangeContent()));

        String content5 = mailTableService.genHorizontalTable(tableTitle2, data);

        content = content1 + content2 + content3 + content4 + content5;

        sendMail(changeDTO.getSendUser(), changeDTO.getAuditor(), "MODIFY", nodeCode, null,
                Arrays.asList(changeDTO.getPplOrder()), content);

    }

    /**
     * 补充缺失的战区信息
     *
     * @param orderDOList 需补充的orderDOList
     */
    private void completeWarZone(List<PplOrderDO> orderDOList) {
        List<PplOrderDO> emptyWarZoneOrders = orderDOList.stream()
                .filter(e -> StringUtils.isEmpty(e.getWarZone()) || "EMPTY".equals(e.getWarZone())).collect(
                        Collectors.toList());
        if (!CollectionUtils.isEmpty(emptyWarZoneOrders)) {
            log.info("completeWarZone, 缺失战区的PPL单, pplOrders-{}",
                    emptyWarZoneOrders.stream().map(PplOrderDO::getPplOrder).collect(
                            Collectors.toList()));
            List<PplOrderDO> warZoneList = demandDBHelper.getAll(PplOrderDO.class,
                    "where industry_dept = ? and customer_short_name in(?) and war_zone != 'EMPTY' and war_zone != ''  and source = 'IMPORT' and deleted = 0 group by customer_short_name",
                    orderDOList.get(0).getIndustryDept(),
                    emptyWarZoneOrders.stream().map(PplOrderDO::getCustomerShortName)
                            .filter(customerShortName -> !StringUtils.isEmpty(customerShortName))
                            .collect(Collectors.toList()));
            Map<String, String> customerWarZoneMap = warZoneList.stream().collect(
                    Collectors.toMap(PplOrderDO::getCustomerShortName, PplOrderDO::getWarZone, (v1, v2) -> v1));
            orderDOList.forEach(e -> {
                if (StringUtils.isEmpty(e.getWarZone()) || "EMPTY".equals(e.getWarZone())) {
                    String warZone = customerWarZoneMap.get(e.getCustomerShortName());
                    if (!StringUtils.isEmpty(warZone) && !"EMPTY".equals(warZone)) {
                        e.setWarZone(warZone);
                    }
                }
            });
        }
    }

    /**
     * 处理当前版本的过期需求
     *
     * @param lastVersion 上一版本
     * @param currentVersion 当前版本
     */
    @Override
    public void processExpiredDemand(PplInnerProcessVersionDO lastVersion, PplInnerProcessVersionDO currentVersion) {
        if (lastVersion == null || currentVersion == null) {
            return;
        }
        if (!"智慧行业一部".equals(currentVersion.getIndustryDept())) {
            return;
        }
        List<String> versionProducts = Arrays.asList(currentVersion.getProduct().split(";"));
        if (!versionProducts.contains(Ppl13weekProductTypeEnum.GPU.getName())) {
            return;
        }
        String product = Ppl13weekProductTypeEnum.GPU.getName();

        // 1、清除之前版本的过期需求标识
        List<Long> oldExpiredIds = demandDBHelper.getRaw(Long.class,
                "select t1.id from ppl_order t1 "
                        + " inner join ppl_item t2 on t1.ppl_order = t2.ppl_order and t2.deleted = 0 "
                        + " where t1.industry_dept = ? and t2.product in(?) "
                        + "      and t1.input_status in(?) "
                        + "      and t1.deleted = 0  ",
                lastVersion.getIndustryDept(), product,
                Arrays.asList(PplOrderInputStatusEnum.NORMAL_EXPIRED.getCode(),
                        PplOrderInputStatusEnum.DELAY.getCode()));
        oldExpiredIds = oldExpiredIds.stream().distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(oldExpiredIds)) {
            demandDBHelper.executeRaw("update ppl_order set input_status = ? where id in(?)",
                    PplOrderInputStatusEnum.NORMAL.getCode(), oldExpiredIds);
        }

        // 2、找出对于当前版本而言过期的需求，并设置过期标识
        List<PplItemDO> newExpiredItems = getExpiredPplItemForVersion(currentVersion,lastVersion);

        Map<String, PplItemDO> newExpiredItemMap = newExpiredItems.stream()
                .collect(Collectors.toMap(PplItemDO::getPplOrder, Function.identity(), (v1, v2) -> v2));
        List<String> newExpiredPplOrders = newExpiredItems.stream().map(PplItemDO::getPplOrder).distinct()
                .collect(
                        Collectors.toList());

        if (ListUtils.isNotEmpty(newExpiredPplOrders)) {
            // 更新ppl_order的过期状态
            demandDBHelper.executeRaw("update ppl_order set input_status = ? where ppl_order in(?)",
                    PplOrderInputStatusEnum.NORMAL_EXPIRED.getCode(), newExpiredPplOrders);

            // 将相对本版本的过期需求 记录下来（原来同一版本如果记录过则不再插入，避免同一版本下有重复的过期单据）
            List<PplOrderExpiredRecordDO> existExpiredDOs = demandDBHelper.getAll(PplOrderExpiredRecordDO.class,
                    "where version_id = ? and ppl_order in(?)", currentVersion.getId(), newExpiredPplOrders);

            List<String> existExpiredOrders = existExpiredDOs.stream().map(PplOrderExpiredRecordDO::getPplOrder)
                    .collect(
                            Collectors.toList());
            newExpiredPplOrders.removeAll(existExpiredOrders);

            List<PplOrderExpiredRecordDO> insertRecordDOs = new ArrayList<>();
            for (String pplOrder : newExpiredPplOrders) {
                PplOrderExpiredRecordDO recordDO = new PplOrderExpiredRecordDO();
                recordDO.setVersionId(currentVersion.getId());
                recordDO.setIndustryDept(currentVersion.getIndustryDept());

                recordDO.setPplOrder(pplOrder);
                PplItemDO itemDO = newExpiredItemMap.get(pplOrder);
                if (itemDO != null) {
                    recordDO.setProduct(itemDO.getProduct());
                    recordDO.setBeginBuyDate(itemDO.getBeginBuyDate());
                    recordDO.setEndBuyDate(itemDO.getEndBuyDate());
                }
                recordDO.setExpiredOperateType(PplOrderExpiredOperateTypeEnum.TO_DEAL.getCode());
                insertRecordDOs.add(recordDO);
            }
            if (ListUtils.isNotEmpty(insertRecordDOs)) {
                demandDBHelper.insertBatchWithoutReturnId(insertRecordDOs);
            }

        }

    }

    @Override
    public void correctWarZone(String pplOrder, String warZone, Long versionId) {
        PplOrderDO order = demandDBHelper.getOne(PplOrderDO.class, " where ppl_order = ? ", pplOrder);
        if (order == null) {
            throw BizException.makeThrow("未查询到ppl信息，pplOrder: %s", pplOrder);
        }
        List<PplInnerProcessVersionDO> versionDOList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                " where status = ? and industry_dept = ? and id = ?",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode(), order.getIndustryDept(), versionId);
        if (ListUtils.isEmpty(versionDOList)) {
            throw BizException.makeThrow("当前无流程中的审批流信息，versionId: %s ,行业部门: %s",
                    String.valueOf(versionId), order.getIndustryDept());
        }

        // 查询出 当前的 审批记录
        List<PplOrderAuditRecordDO> currentAuditRecords = demandDBHelper.getAll(PplOrderAuditRecordDO.class,
                " where deleted = 0 and ppl_order = ? and version_id = ? order by id asc ",
                pplOrder, versionId);
        // 查询出 实际上的审批节点，当前版本指定战区的审批记录中最新的审批记录对应的审批节点
        String sql = "select node_code from ppl_order_audit_record "
                + " where id = ( "
                + " select max(a.id) from ppl_order_audit_record a "
                + " left join ppl_order b on a.ppl_order = b.ppl_order "
                + " where   b.war_zone = ? and a.version_id = ? "
                + " ) ";
        String newestWarZoneNode = demandDBHelper.getRawOne(String.class, sql, warZone, versionId);
        if (Strings.isBlank(newestWarZoneNode)) {
            // 直接使用当前的审批数据，直接修改战区
            order.setWarZone(warZone);
            demandDBHelper.update(order);
            return;
        }
        // 查询出 完全的审批节点
        List<PplInnerProcessVersionSlaDO> nodeDataList = getProcessByVersionId(versionId);
        PplInnerProcessVersionSlaDO newestWarZoneNodeData = null;
        for (PplInnerProcessVersionSlaDO versionSlaDO : nodeDataList) {
            if (newestWarZoneNode.equals(versionSlaDO.getNodeCode())) {
                newestWarZoneNodeData = versionSlaDO;
                break;
            }
        }
        if (newestWarZoneNodeData == null) {
            throw BizException.makeThrow("未查询到审批节点信息，审批节点: %s", newestWarZoneNode);
        }
        // 比较当前审批记录是否走在了实际审批节点前面，删除走在实际审批节点前面（包括实际审批节点）的审批记录，新增一条实际审批节点待审批的记录
        // 比较当前审批记录是否走在了实际审批节点后面，直接新增一条实际审批节点待审批的记录
        boolean needDelete = false;
        List<Long> deleteIds = new ArrayList<>();
        for (PplOrderAuditRecordDO record : currentAuditRecords) {
            if (needDelete) {
                deleteIds.add(record.getId());
                continue;
            }
            if (newestWarZoneNode.equals(record.getNodeCode())) {
                needDelete = true;
                deleteIds.add(record.getId());
            }
        }
        if (ListUtils.isNotEmpty(deleteIds)) {
            // 删除走在实际审批节点前面（包括实际审批节点）的审批记录
            demandDBHelper.delete(PplOrderAuditRecordDO.class, " where id in (?) ", deleteIds);
        }

        // 新增一条实际审批节点待审批的记录
        PplOrderAuditRecordDO newData = new PplOrderAuditRecordDO();
        newData.setNodeCode(newestWarZoneNode);
        newData.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        newData.setVersionId(versionId);
        newData.setPplOrder(pplOrder);
        demandDBHelper.insert(newData);

        // 修改ppl的战区、审批人
        order.setWarZone(warZone);
        order.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        order.setCurrentProcessor(getNodeApproveUser(newestWarZoneNodeData, order));
        order.setCurrentRole(newestWarZoneNodeData.getApproveRole());
        order.setNodeCode(newestWarZoneNodeData.getNodeCode());
        demandDBHelper.update(order);
    }
}
