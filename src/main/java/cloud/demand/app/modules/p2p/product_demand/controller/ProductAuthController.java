package cloud.demand.app.modules.p2p.product_demand.controller;

import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandAuthService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import java.util.List;

@Tag(name = "产品全年需求模块-权限模块")
@JsonrpcController("/product/annual/api")
@Slf4j
public class ProductAuthController {

    @Resource
    ProductDemandAuthService productDemandAuthService;

    @RequestMapping
    public Object queryPlanProductUserPermission(@JsonrpcParam QueryPlanProductUserPermissionReq req) {
        return productDemandAuthService.queryPlanProductUserPermission(req.getUsers());
    }

    @RequestMapping
    public Object hasAdmin(@CurrentUser TofUser user) {
        return productDemandAuthService.hasAdmin(user.getUsername());
    }

    @Data
    static class QueryPlanProductUserPermissionReq {

        private List<String> users;
    }
}
