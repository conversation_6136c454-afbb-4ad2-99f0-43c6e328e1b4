package cloud.demand.app.modules.p2p.longterm.service;

import cloud.demand.app.modules.p2p.longterm.controller.dto.LongtermTimeDTO;
import cloud.demand.app.modules.p2p.longterm.controller.req.GenTimesReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermGroupItemReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.ReplaceIntervened13weekReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveLongtermGroupItemReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SaveLongtermGroupReasonReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermGroupItemResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.ReplaceIntervened13weekResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SaveLongtermGroupItemResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SaveLongtermGroupReasonResp;

import java.util.List;

/**
 * 负责分组明细数据的查询和编辑
 */
public interface LongtermGroupItemService {

    /**
     * 查询分组明细数据
     */
    QueryLongtermGroupItemResp queryLongtermGroupItem(QueryLongtermGroupItemReq req);

    /**
     * 全量保存明细数据
     */
    SaveLongtermGroupItemResp saveLongtermGroupItem(SaveLongtermGroupItemReq req);

    /**
     * 用13周已干预的版本替换13周未干预的版本
     */
    ReplaceIntervened13weekResp replaceIntervened13week(ReplaceIntervened13weekReq req);

    /**
     * 全量保存推导逻辑
     */
    SaveLongtermGroupReasonResp saveLongtermGroupReason(SaveLongtermGroupReasonReq req);

    /**
     * 全量复制分组下的item，并不会返回新的item的id，会实际操作数据库进行插入。该方法无注解事务，请在外层加。
     * @param oldRecordId 老的分组id
     * @param newRecordId 新的分组id
     */
    void copyGroupRecordItem(Long oldRecordId, Long newRecordId);

    /**
     * 生成各季度各月份需求来源
     * @param req
     * @param if35 是否构建35范围
     * @return
     */
    List<LongtermTimeDTO> genTimes(GenTimesReq req, boolean if35);

}
