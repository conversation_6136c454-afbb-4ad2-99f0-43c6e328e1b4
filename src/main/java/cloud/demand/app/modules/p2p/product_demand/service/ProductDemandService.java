package cloud.demand.app.modules.p2p.product_demand.service;

import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.dto.ValidateResultDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ExtendsBudgetDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.ListProductDemandGroupReq;
import cloud.demand.app.modules.p2p.product_demand.dto.PlanProductMapStatusDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.QueryDemandInfoReq;
import cloud.demand.app.modules.p2p.product_demand.dto.SaveItemDTO;
import cloud.demand.app.modules.p2p.product_demand.dto.SummaryStatisticReq;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupWithVersionVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 产品全年需求服务接口
 */
public interface ProductDemandService {

    ValidateResultDTO overrideSave(ProductDemandVersionDO versionDO, String planProduct,
            List<SaveItemDTO> saveItemDTOS);

    /**
     * 查询当前版本各分组的录入情况
     *
     * @return 当前版本各分组的录入情况
     */
    List<PlanProductMapStatusDTO> queryCurrentVersionPlanProductMapStatus(String username);

    /**
     * 查询预测分组列表
     *
     * @param req 筛选项
     * @return 预测分组列表VO
     */
    List<ProductDemandVersionGroupWithVersionVO> listGroup(ListProductDemandGroupReq req);

    /**
     * id查询预测分组
     *
     * @param id id
     * @return 预测分组列表VO
     */
    ProductDemandVersionGroupDO getGroupById(Long id);

    /**
     * 查询预测详情（不包含汇总分析）
     *
     * @param req 筛选项
     * @return 预测详情
     */
    Map<String, Object> queryDemandInfoReq(QueryDemandInfoReq req, String username);

    /**
     * 导出填写模板
     *
     * @return 填写模板
     */
    FileNameAndBytesDTO exportTemplate();

    /**
     * 解析需求EXCEL
     *
     * @return 解析出来的DTO
     */
    List<Map<String, Object>> decodeDemandExcel(MultipartFile multipartFile, String version);

    /**
     * 渲染结果为excel
     *
     * @param req 查询参数
     * @param username 用户名
     * @return 渲染后的excel的二进制
     */
    FileNameAndBytesDTO exportDemandDetail(QueryDemandInfoReq req, String username);

    Map<String, Object> summaryStatistic(SummaryStatisticReq req);

    Map<String, Object> summaryStatisticByField(SummaryStatisticReq req);

    List<Map<String, Object>> listVersionDemandItems(SummaryStatisticReq req);

    List<String> listComparableVersionList();

    void forceRollBackDemandGroup(ProductDemandVersionGroupDO versionGroupDO, String approveMemo, String username);

    /**
     * 通过继承预算内容得到预测
     * @param req 请求参数
     * @return
     */
    Object extendsBudget(ExtendsBudgetDTO req);
}
