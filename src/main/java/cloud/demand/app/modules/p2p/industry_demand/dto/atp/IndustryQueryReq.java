package cloud.demand.app.modules.p2p.industry_demand.dto.atp;

import cloud.demand.app.modules.p2p.industry_demand.dto.dict.RegionDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import lombok.Data;
import org.nutz.lang.Strings;
import yunti.boot.exception.BizException;

@Data
public class IndustryQueryReq {

    private String product;

    private String industryDept;

    private String customerName;

    private YearMonth yearMonth;

    /** 前端传值，地域编码，例如：ap-guangzhou */
    private String region;

    /** 前端无需传值，地域编码简化，例如：gz */
    private String regionShort;

    /** 前端无需传值，地域名称，例如：上海 */
    private String regionName;

    /** 前端无需传值，带区域的地域名称，例如：华东地区(上海) */
    private String regionNameWithArea;

    private String instanceType;

    private Integer demandCore;

    private String queryRemark;

    public void check() {
        if (Strings.isBlank(region)) {
             throw BizException.makeThrow("地域编码不能为空");
        }
        if (Strings.isBlank(instanceType)) {
             throw BizException.makeThrow("实例类型不能为空");
        }
        if (yearMonth == null) {
             throw BizException.makeThrow("年月不能为空");
        }
        if (demandCore == null) {
             throw BizException.makeThrow("需求核数不能为空");
        }
        if (demandCore < 1) {
             throw BizException.makeThrow("需求核数不能小于1");
        }
    }

    public boolean in13Week() {
        return advanceMonth() <= 3;
    }

    public int advanceMonth() {
        long data = YearMonth.now().until(yearMonth, ChronoUnit.MONTHS);
        return (int) data;
    }

    public QueryListReq toQueryListReq() {
        QueryListReq req = new QueryListReq();
        req.setRegionList(ListUtils.newArrayList(region));
        req.setInstanceTypeList(ListUtils.newArrayList(instanceType));
        req.setBeginYearMonth(yearMonth);
        req.setEndYearMonth(yearMonth);
        return req;
    }

    public void regionDataSetByRegion(List<RegionDTO> regionList) {
        if (regionList == null || regionList.isEmpty()) {
            throw BizException.makeThrow("未能获取地域信息");
        }
        boolean isFind = false;
        for (RegionDTO regionDTO : regionList) {
            if (Objects.equals(region, regionDTO.getRegionStrId())) {
                regionName = regionDTO.getRegionShortChName();
                regionNameWithArea = regionDTO.getRegionName();
                regionShort = regionDTO.getRegionShortName();
                isFind = true;
                break;
            }
        }
        if (!isFind) {
           throw BizException.makeThrow("地域编码[%s]不存在", region);
        }
    }

}
