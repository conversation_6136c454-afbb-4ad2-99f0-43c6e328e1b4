package cloud.demand.app.modules.p2p.longterm.service;

import cloud.demand.app.modules.p2p.longterm.controller.req.OperateLongtermAuditReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermAuditDictReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermAuditListReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.SubmitLongtermAuditReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.WithdrawLongtermSubmitReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.OperateLongtermAuditResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermAuditDictResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermAuditListResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.SubmitLongtermAuditResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.WithdrawLongtermSubmitResp;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;

/**
 * 负责审批的流程
 */
public interface LongtermVersionApprovalService {

    /**
     * 查询中长期审批列表
     */
    QueryLongtermAuditListResp queryLongtermAuditList(QueryLongtermAuditListReq req);

    /**
     * 获取数据权限
     */
    WhereSQL getDataAuth(Boolean isAddIndustryDataFollower);

    /**
     * 相关字典表
     */
    QueryLongtermAuditDictResp queryLongtermAuditDict(QueryLongtermAuditDictReq req);

    /**
     * 提交审批
     */
    SubmitLongtermAuditResp submitLongtermAudit(SubmitLongtermAuditReq req);

    /**
     * 撤回审批
     */
    WithdrawLongtermSubmitResp withdrawLongtermSubmit(WithdrawLongtermSubmitReq req);

    /**
     * 获取节点处理人
     */
    String getNodeCodeProcessor(String nodeCode,String industryDept,String bizGroup);

    /**
     * 审批接口
     */
    OperateLongtermAuditResp operateLongtermAudit(OperateLongtermAuditReq req);

    /**
     * 将指定版本下的所有分组转移到指定的状态。
     * @param versionCode 版本编码
     * @param targetStatus 要设置的分组状态
     * @param reason 状态流转的原因
     */
    void allGroupToStatus(String versionCode, LongtermVersionGroupStatusEnum targetStatus, String reason);


    Boolean isAllowApprove(LongtermVersionGroupDO groupDO,String operateUser);

}
