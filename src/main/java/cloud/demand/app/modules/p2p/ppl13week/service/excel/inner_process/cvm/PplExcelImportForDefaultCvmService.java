package cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.cvm;

import cloud.demand.app.common.excel.DotExcelReadUtil;
import cloud.demand.app.common.excel.DotExcelReadUtil.DotExcelBuilder;
import cloud.demand.app.common.excel.ExcelGroupEnum;
import cloud.demand.app.common.excel.checker.NotBlankColumnChecker;
import cloud.demand.app.common.excel.convert.String2StringOrNumberCellConverterForExport;
import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.ImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.PplDefaultCvmImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplExportWriteHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.CBSRelevantChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.DemandAndBillTypeChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.PplIdInDraftOrValidChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.PplInnerVersionRelevantChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.QueryInfoByUinRspHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.StringColumnValueCollect;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.StrongDesignateZoneChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.YunXiaoRelevantChecker;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp.ErrorMessage;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.checker.CustomerTypeNameChecker;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.checker.WarZoneChecker;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.checker.WinRateChecker;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.AbstractInnerPplExcelParseService;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.io.IOUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

/**
 * 战略客户部CVM导入行业内excel流程
 */
@Service
@Slf4j
public class PplExcelImportForDefaultCvmService extends AbstractInnerPplExcelParseService {

    @Resource
    private PplDictService pplDictService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplDraftService pplDraftService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PplInnerVersionService innerVersionService;

    @Override
    public boolean support(String productType, String industryDept) {
        return (productType.equals(Ppl13weekProductTypeEnum.CVM.getName())
                || productType.equals(Ppl13weekProductTypeEnum.BM.getName())
                || productType.equals(Ppl13weekProductTypeEnum.EMR.getName())
                || productType.equals(Ppl13weekProductTypeEnum.EKS.getName())
                || productType.equals(Ppl13weekProductTypeEnum.ES.getName())
                || productType.equals(Ppl13weekProductTypeEnum.CDW.getName())
                || productType.equals(Ppl13weekProductTypeEnum.PAAS.getName())) &&
                (!industryDept.equals(IndustryDeptEnum.STRATEGY.getName()));
    }

    @Override
    public List<PplImportExcelDTO> decodeExcel(MultipartFile file, String product) {
        List<PplImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplDefaultCvmImportExcelDTO.class,
                    new AnalysisEventListener<PplDefaultCvmImportExcelDTO>() {
                        @Override
                        public void invoke(PplDefaultCvmImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplImportExcelDTO i = PplDefaultCvmImportExcelDTO.copyToNewDTO(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }

    @SneakyThrows
    private PplItemImportRsp byBuilder(MultipartFile file, ImportExcelDTO importExcelDTO) {
        String industryDept = importExcelDTO.getIndustryDept();
        String product = importExcelDTO.getProduct();
        String userName = LoginUtils.getUserName();
        YearMonth startYearMonth = DateUtils.parse(importExcelDTO.getStartYearMonth());


        DotExcelBuilder<PplImportExcelDTO> builder = DotExcelReadUtil
                .createBuilder(PplImportExcelDTO.class, // 需要返回的数据类型
                        ExcelGroupEnum.DEFAULT_CVM_PPL_IMPORT, // 所属的 excel 组，会根据这个组取获取所有此组的对应注册字段信息
                        2 // 表头所在的行，用于获取到表头之后，根据注册字段信息的映射关系找到此表头对应的 java 字段信息
                );

        PplImportExcelDTO demo = new PplImportExcelDTO();
        NotBlankColumnChecker notBlankColumnChecker = new NotBlankColumnChecker();
        Set<String> pplIdSet = new HashSet<>();
        // 1.声明字段校验器
        // 客户类型校验
        builder.registerValueCheckerByGetter(demo::getCustomerTypeName, new CustomerTypeNameChecker())
                // 项目名称校验
                .registerValueCheckerByGetter(demo::getProjectName, notBlankColumnChecker)
                // 赢率校验
                .registerValueCheckerByGetter(demo::getWinRate, new WinRateChecker())
                // 获取uin，根据uin异步查询出对应的完整uin信息放入上下文供  PplExcelResultHandler 中使用
                .registerValueCheckerByGetter(demo::getCustomerUin, new QueryInfoByUinRspHandler())
                .registerValueCheckerByGetter(demo::getPplId,
                        // 检查pplId是否在草稿单或者已生效的数据中
                        new PplIdInDraftOrValidChecker(industryDept, startYearMonth, product, userName),
                        // 收集 pplId 到 pplIdSet 中
                        new StringColumnValueCollect(pplIdSet));

        if (!IndustryDeptEnum.SMART_INDUSTRY_ONE.getName().equals(industryDept)) {
            // 不是智慧行业一部的需要进行战区校验
            builder.registerValueCheckerByGetter(demo::getWarZone, new WarZoneChecker(industryDept));
        }

        CheckYunxiaoReq checkYunxiaoReq = buildCheckYunxiaoReq();
        PplExcelResultHandler resultHandler = new PplExcelResultHandler(importExcelDTO, pplIdSet);
        // 2.声明行校验器
        // 需求场景、账单类型校验
        builder.registerRowChecker(new DemandAndBillTypeChecker())
                // 云霄方CVM相关校验 地域/可用区/实例类型/实例配置/核心数/内存数
                .registerRowChecker(new YunXiaoRelevantChecker(checkYunxiaoReq, product))
                // CBS方相关校验  磁盘类型/数量
                .registerRowChecker(new CBSRelevantChecker(product))
                // 版本相关校验，（海外8周处理）
                .registerRowChecker(new PplInnerVersionRelevantChecker
                        (innerVersionService.queryVersionVO(industryDept,product),pplDictService.queryAllRegionName(true)))
                .registerRowChecker(new StrongDesignateZoneChecker())
                // uin、客户类型、客户简称混合校验，设置QueryInfoByUinRsp、以及智慧行业一部战区,转换结果集
                .registerResultChecker(resultHandler)
                .inputStream(file.getInputStream());

        // 3. 读数据,并开始校验
        ReadResult<PplImportExcelDTO> result = DotExcelReadUtil.read(builder);

        List<ErrorMessage> errors = ErrorMessage.from(result.getErrorsAndSort());
        List<GroupItemDTO> datas = resultHandler.getResult();
        return new PplItemImportRsp(errors.size() == 0, errors, datas);
    }

    @Override
    public PplItemImportRsp execute(MultipartFile file, ImportExcelDTO importExcelDTO) {
        return byBuilder(file, importExcelDTO);
    }


    @Override
    public DownloadBean export(String industryDept, String product, List<GroupItemDTO> groupItemDTOList) {
        InputStream templateIn = IOUtils.readClasspathResourceInputStream(
                "excel/inner_process/default_cvm_import.xlsx");
        String fileName = industryDept + "-" + product + "-PPL数据导出";

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        HashMap<String, Integer> config = new HashMap<>();

        List data = new ArrayList<>();
        List dictData = new ArrayList();

        data = PplDefaultCvmImportExcelDTO.transFrom(groupItemDTOList);
        dictData.addAll(getDefaultCvmDictData(industryDept, product, config));

        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new PplExportWriteHandler(config))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .registerConverter(new String2StringOrNumberCellConverterForExport()).build();
        WriteSheet dictSheet = EasyExcel.writerSheet("字典").build();

        excelWriter.
                fill(new FillWrapper("item", data), writeSheet)
                .write(dictData, dictSheet)
                .finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                fileName + com.pugwoo.wooutils.lang.DateUtils.format(new Date(), "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }


    ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 默认行业CVM的EXCEL字典
     *
     * @param config
     * @return
     */
    @SneakyThrows
    private List<PplDefaultCvmImportExcelDTO> getDefaultCvmDictData(String industryDept, String product,
            HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return pplDictService.queryAllCityName(null);
        });
        Future<List<IndustryDemandRegionZoneInstanceTypeDictDO>> zonesFuture = executorService.submit(() -> {
            return pplDictService.queryAllZoneName();
        });
        Future<List<String>> instanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceType("");
        });
        Future<List<String>> instanceModelFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceModel(null, null);
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });

        Boolean isBM = product.equals(Ppl13weekProductTypeEnum.BM.getName());
        List<String> warZoneList = getIndustryWarZone(industryDept);
        List<IndustryDemandRegionZoneInstanceTypeDictDO> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> instanceType = instanceTypeFuture.get();
        List<String> instanceModel = instanceModelFuture.get();
        instanceType = isBM ? instanceType.stream().filter(v -> v.startsWith("BM")).collect(Collectors.toList()) :
                instanceType.stream().filter(v -> !v.startsWith("BM")).collect(Collectors.toList());
        instanceModel = isBM ? instanceModel.stream().filter(v -> v.startsWith("BM")).collect(Collectors.toList()) :
                instanceModel.stream().filter(v -> !v.startsWith("BM")).collect(Collectors.toList());
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();

        List<String> AffType = Lang.list("母机", "交换机", "机柜");
        List<String> systemDiskTypes = Lang.list("SSD云硬盘", "高性能云硬盘", "通用型SSD云硬盘");
        List<String> dateDiskTypes = Lang.list("SSD云硬盘", "高性能云硬盘", "通用型SSD云硬盘", "增强型SSD云硬盘");
        List<String> billType = Lang.list("包年包月", "按量计费", "竞价实例");

        List<String> winRateRange = Lang.list();

        config.put("E", warZoneList.size());
        config.put("F", demandType.size());
        config.put("G", demandScene.size());
        config.put("I", billType.size());
        config.put("J", 101);
        config.put("P", cityNames.size());
        config.put("Q", zones.size());
        config.put("R", instanceType.size());
        config.put("S", instanceModel.size());
        config.put("T", parseCpu.size());
        config.put("U", parseRam.size());
        config.put("X", 2);
        config.put("Z", AffType.size());
        config.put("AB", systemDiskTypes.size());
        config.put("AD", dateDiskTypes.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }

        List<PplDefaultCvmImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplDefaultCvmImportExcelDTO one = new PplDefaultCvmImportExcelDTO();

            if (i < warZoneList.size()) {
                one.setWarZone(warZoneList.get(i));
            }

            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(parseCpu.get(i));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(parseRam.get(i));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setIsAcceptAlternative("是");
                one.setZoneName("随机可用区");
            }
            if (i == 1) {
                one.setIsAcceptAlternative("否");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i < instanceType.size()) {
                one.setInstanceType(instanceType.get(i));
            }
            if (i < instanceModel.size()) {
                one.setInstanceModel(instanceModel.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i).getZoneName());
            }
            if (i < AffType.size()) {
                one.setAffinityType(AffType.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < systemDiskTypes.size()) {
                one.setSystemDiskType(systemDiskTypes.get(i));
            }
            if (i < dateDiskTypes.size()) {
                one.setDataDiskType(dateDiskTypes.get(i));
            }
            ret.add(one);
        }
        return ret;
    }

}
