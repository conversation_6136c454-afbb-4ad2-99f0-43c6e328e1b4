package cloud.demand.app.modules.p2p.ppl13week_forecast.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 模型预测预测的产品类型枚举
 */
@Getter
public enum Ppl13weekForecastProductEnum {

    CVM("CVM", "CVM", "核心数"),

    EMR("EMR", "大数据EMR", "核心数"),

    EKS("EKS", "EKS", "核心数"),

    CDB("CDB", "CDB", "内存数(GB)"),

    CBS("CBS", "CBS", "磁盘量(GB)")

    ;

    final private String code;
    final private String name;
    final private String unitName;

    Ppl13weekForecastProductEnum(String code, String name, String unitName) {
        this.code = code;
        this.name = name;
        this.unitName = unitName;
    }

    public static Ppl13weekForecastProductEnum getByCode(String code) {
        for (Ppl13weekForecastProductEnum e : Ppl13weekForecastProductEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        Ppl13weekForecastProductEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}