package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderExpiredOperateReasonEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderExpiredOperateTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_order_expired_record")
public class PplOrderExpiredRecordDO extends BaseDO {

    /**
     * 审批版本id（需求延期时所在的审批版本）
     */
    @Column("version_id")
    private Long versionId;

    @Column("industry_dept")
    private String industryDept;

    @Column("product")
    private String product;

    @Column("ppl_order")
    private String pplOrder;

    /**
     * 开始购买日期（原始）
     */
    @Column("begin_buy_date")
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期（原始）
     */
    @Column("end_buy_date")
    private LocalDate endBuyDate;

    /**
     * 延期操作后的新pplOrder（历史生效数据过期后延期 会复制新增pplOrder，本周期数据延期 则直接在当前pplOrder上改动，不过此表目前仅记录 历史生效数据的过期操作）
     */
    @Column("expired_ppl_order")
    private String expiredPplOrder;

    /**
     * 相对当前版本而言的过期处理方式
     *
     * @see PplOrderExpiredOperateTypeEnum
     */
    @Column("expired_operate_type")
    private String expiredOperateType;

    /**
     * 延期操作人
     */
    @Column("expired_operate_user")
    private String expiredOperateUser;

    /**
     * 延期操作原因
     *
     * @see PplOrderExpiredOperateReasonEnum
     */
    @Column("expired_operate_reason")
    private String expiredOperateReason;

    /**
     * 延期后的需求年
     */
    @Column("expired_year")
    private Integer expiredYear;

    /**
     * 延期后的需求月
     */
    @Column("expired_month")
    private Integer expiredMonth;

}
