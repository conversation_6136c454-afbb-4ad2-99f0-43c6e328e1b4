package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.CustomerLevelVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.HistoryTrendQueryReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.InitModelForecastPackageReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.InnerVersionParams;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.ModifyItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.QueryBaseDataReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.RefreshVersionPackageReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.RemoveCustomerConfigVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.SupplementPplReq;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryPackageBaseDataService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplModelForecastForPackageService;
import cloud.demand.app.web.model.common.DownloadBean;
import com.google.common.collect.ImmutableMap;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 行业包管理相关接口
 */
@JsonrpcController("/ppl13week/packageBaseData")
@Slf4j
public class PplIndustryPackageBaseDataController {

    @Resource
    private PplIndustryPackageBaseDataService pplIndustryPackageBaseDataService;

    @Resource
    private PplModelForecastForPackageService modelForecastForPackageService;

    @RequestMapping
    public Object importData(@RequestParam("file") MultipartFile file,
            @RequestParam Map<String, Object> params) {
        return pplIndustryPackageBaseDataService.importData(file, params);
    }

    @RequestMapping
    public Object exportBaseData(@JsonrpcParam QueryBaseDataReq req) {
        req.setOnlyBaseData(true);
        FileNameAndBytesDTO res = pplIndustryPackageBaseDataService.exportBaseData(req);
        return new DownloadBean(res.getFileName(), res.getBytes());
    }

    @RequestMapping
    public Object queryPackageBaseData(@JsonrpcParam QueryBaseDataReq req) {
        req.setOnlyBaseData(false);
        if (req.getIsPreSubmit()){
            if (StringUtils.isBlank(req.getProduct())){
                throw BizException.makeThrow("产品不能为空");
            }
            return modelForecastForPackageService.queryPackageBaseDataForDraft(req);
        }else {
            return pplIndustryPackageBaseDataService.queryPackageBaseData(req);
        }

    }

    @RequestMapping
    public Object queryHistoryTrend(@JsonrpcParam HistoryTrendQueryReq req) {
        return pplIndustryPackageBaseDataService.queryHistoryTrend(req);
    }

    @RequestMapping
    public Object queryRemoveCustomer(@JsonrpcParam InnerVersionParams req) {
        return pplIndustryPackageBaseDataService.queryRemoveCustomer(req);
    }


    @RequestMapping
    public Object saveRemoveCustomer(@JsonrpcParam RemoveCustomerConfigVO req) {
        pplIndustryPackageBaseDataService.saveRemoveCustomer(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object queryCustomerLevel(@JsonrpcParam InnerVersionParams req) {
        return pplIndustryPackageBaseDataService.queryCustomerLevel(req);
    }

    @RequestMapping
    public Object saveCustomerLevel(@JsonrpcParam CustomerLevelVO req) {
        pplIndustryPackageBaseDataService.saveCustomerLevel(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object modifyPackageBaseData(@JsonrpcParam ModifyItemReq req) {
        pplIndustryPackageBaseDataService.modifyPackageBaseData(req);
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object supplementPpl(@JsonrpcParam SupplementPplReq req) {
        if (req.getIsPreSubmit()){
            if (StringUtils.isBlank(req.getProduct())){
                throw BizException.makeThrow("产品不能为空");
            }
            modelForecastForPackageService.supplementPpl(req);
        }else {
            pplIndustryPackageBaseDataService.supplementPpl(req);
        }
        return ImmutableMap.of("result", "ok");
    }

    // 模型预测新增controller
    @RequestMapping
    public Object initForecastDataForPackage(@JsonrpcParam InitModelForecastPackageReq req) {
        return modelForecastForPackageService.initForecastDataForPackage(req.getVersionId(),req.getProduct());
    }

    @RequestMapping
    public Object inheritOrRefreshBaseData(@JsonrpcParam RefreshVersionPackageReq req)  {
        pplIndustryPackageBaseDataService.inheritOrRefreshBaseData(req.getNewVersionId(), req.getOldVersionId());
        return ImmutableMap.of("result", "ok");
    }

    @RequestMapping
    public Object fillZeroBaseData(@JsonrpcParam InitModelForecastPackageReq req) {
        modelForecastForPackageService.fillZeroBaseData(req.getVersionId());
        return ImmutableMap.of("result", "ok");
    }



}
