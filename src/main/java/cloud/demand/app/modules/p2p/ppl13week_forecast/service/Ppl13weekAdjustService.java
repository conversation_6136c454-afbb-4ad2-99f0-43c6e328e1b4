package cloud.demand.app.modules.p2p.ppl13week_forecast.service;


import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryPpl13weekAdjustReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryPpl13weekAdjustDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QueryPpl13weekAdjustParamsRsp;

/**
 * 干预调整相关的接口放到这里
 *
 * <AUTHOR>
 */

public interface Ppl13weekAdjustService {


    /**
     * 查询字典接口
     *
     * @param req taskId
     * @return dict
     */
    QueryPpl13weekAdjustParamsRsp queryAdjustParams(QueryPpl13weekAdjustReq req);

    /**
     * 查询明细
     *
     * @param req taskId
     * @return ret
     */
    QueryPpl13weekAdjustDetailRsp queryAdjustDetail(QueryPpl13weekAdjustReq req);

}
