package cloud.demand.app.modules.p2p.ppl13week.dto.consensus;


import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class BatchConsensusReq {

    @NotBlank
    private String industryDept;

    @NotBlank
    private String versionCode;

    @NotBlank
    private String product;
    /**
     * 共识结果， true 为接受引导， false为拒绝引导
     */
    @NotNull
    private Boolean consensusResult;

    private String consensusRemark;

    /**
     * 共识的ppl
     */
    private List<SupplyPlanDetailVO> dealData;

}
