package cloud.demand.app.modules.p2p.ppl13week.service.excel;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplCvmImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplCvmExcelParseService extends Abstract13WeekPplExcelParseService {

    @Resource
    private PplDictService pplDictService;

    @Override
    public boolean support(String productType) {
        return !Ppl13weekProductTypeEnum.GPU.getName().equals(productType);
    }

    public List<PplImportExcelDTO> decodeExcel(MultipartFile file, String product) {
        List<PplImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplCvmImportExcelDTO.class,
                    new AnalysisEventListener<PplCvmImportExcelDTO>() {
                        @Override
                        public void invoke(PplCvmImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplImportExcelDTO i = PplCvmImportExcelDTO.copyToNewDTO(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }

    @Override
    public PplItemImportRsp execute(MultipartFile file, YearMonth startYearMonth, YearMonth endYearMonth,
            String product) {

        //解析excel
        List<PplImportExcelDTO> uploadData = decodeExcel(file, product);

        //获取uin
        List<String> uins = uploadData.stream()
                .map(PplImportExcelDTO::getCustomerUin)
                .filter(Strings::isNotBlank)
                .distinct().collect(Collectors.toList());
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = queryInfoByUinRspHashMap(uins);

        List<GroupItemDTO> retData = Lang.list();
        List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();

        CheckYunxiaoReq checkYunxiaoReq = buildCheckYunxiaoReq();

        for (int i = 0; i < uploadData.size(); i++) {
            PplImportExcelDTO oneData = uploadData.get(i);
            int row = i + 3;
            int beginErrorSize = errors.size();
            PplItemImportRsp.ErrorMessage error = null;

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getCustomerTypeName,
                    CustomerTypeEnum.names());
            errors.add(error);

            //校验uin
            QueryInfoByUinRsp queryInfoByUinRsp = checkUin(errors, oneData, row, uinMapInfo);

            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());

            //校验需求
            checkDemandAndBillType(errors, oneData, row);

            // 校验项目名
            checkProjectName(errors, oneData, row);

            //校验赢率
            BigDecimal winRate = checkWinRate(errors, oneData, row);

            //各时间参数校验
            DateDTO dateDTO = checkTime(errors, oneData, row, startYearMonth, endYearMonth);

            // 云霄方CVM相关校验 地域/可用区/实例类型/实例配置/核心数/内存数
            List<String> alternativeInstances = new ArrayList<>();
            checkYunXiaoRelevant(errors, oneData, row, alternativeInstances, product, false, checkYunxiaoReq);
            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            Integer num = NumberUtils.parseInt(oneData.getInstanceNum());

            // CBS方相关校验  磁盘类型/数量
            checkCBSRelevant(errors, oneData, row, product);
            Integer systemStorage = NumberUtils.parseInt(oneData.getSystemDiskStorage());
            Integer diskNum = NumberUtils.parseInt(oneData.getDataDiskNum());
            Integer diskStorage = NumberUtils.parseInt(oneData.getDataDiskStorage());

            int oneDiskSize = 0;
            if (systemStorage != null) {
                oneDiskSize += systemStorage;
            }
            if (diskNum != null && diskStorage != null) {
                oneDiskSize += diskNum * diskStorage;
            }

            // 如果该条ppl没有产生error 则直接转换为 DTO
            if (beginErrorSize == errors.size()) {

                GroupItemDTO tmp = GroupItemDTO.trans(oneData);
                retData.add(tmp);
                if (customerTypeEnum != null) {
                    tmp.setCustomerType(customerTypeEnum.getCode());
                }
                tmp.setYear(dateDTO.getYear());
                tmp.setMonth(dateDTO.getMonth());
                tmp.setYearMonth(dateDTO.getYearMonth());
                tmp.setCustomerShortName(oneData.getCustomerShortName());
                if (queryInfoByUinRsp != null) {
                    if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                        tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
                    }
                    if (Strings.isBlank(tmp.getCustomerShortName())) {
                        tmp.setCustomerTypeName(tmp.getCustomerName());
                    }
                    tmp.setIndustry(queryInfoByUinRsp.getIndustry());
                    tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
                    tmp.setWarZone(queryInfoByUinRsp.getWarZone());
                    tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
                }
                tmp.setWinRate(winRate);
                tmp.setBeginBuyDate(dateDTO.getBeginBuyDateRet());
                tmp.setEndBuyDate(dateDTO.getEndBuyDateRet());
                tmp.setBeginElasticDate(dateDTO.getBeginElasticDateRet());
                tmp.setEndElasticDate(dateDTO.getEndElasticDateRet());
                tmp.setTotalCoreNum(coreNum * (num == null ? 0 : num));
                tmp.setTotalDiskNum(oneDiskSize * (num == null ? 0 : num));
                tmp.setInstanceModelCoreNum(coreNum);
                tmp.setInstanceModelRamNum(ramNum);
                tmp.setStatus(PplItemStatusEnum.VALID.getCode());
                tmp.setStatusName(PplItemStatusEnum.VALID.getName());
                tmp.setAffinityValue(NumberUtils.parseBigDecimal(oneData.getAffinityValue()));
                if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
                    tmp.setAlternativeInstanceType(alternativeInstances);
                }
                tmp.setAlternativeInstanceType(alternativeInstances);
                tmp.setProduct(product);

                // 2022-12-13 增加推荐机型
                List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(tmp.getZoneName());

                boolean isMainInstanceType = mainInstanceTypes.contains(tmp.getInstanceType());
                tmp.setIsRecommendedInstanceType(isMainInstanceType);


            }
        }

        return new PplItemImportRsp(errors.size() == 0, errors, retData);

    }
}
