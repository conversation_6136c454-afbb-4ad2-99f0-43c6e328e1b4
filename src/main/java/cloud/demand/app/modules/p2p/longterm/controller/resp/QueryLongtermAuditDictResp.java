package cloud.demand.app.modules.p2p.longterm.controller.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class QueryLongtermAuditDictResp {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class KeyValue {
        private String key;
        private Object value;
    }

    /**供前端下拉框选择的字典*/
    private List<KeyValue> statusDict;

    /**
     * 版本编码字典
     */
    private List<String> versionCodeDict;

    /**
     * 行业部门字典
     */
    private List<String>industryDeptDict;

    /**业务分组字典*/
    private List<String> bizGroupDict;

    /**年份范围，也即版本范围*/
    private List<Integer> yearRangeDict;

}
