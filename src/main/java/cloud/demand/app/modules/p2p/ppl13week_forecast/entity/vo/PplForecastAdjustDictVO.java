package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.vo;

import cloud.demand.app.common.utils.ListUtils2;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.string.StringTools;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;

/**
 * group详情字典表
 *
 * <AUTHOR>
 */
@Data
public class PplForecastAdjustDictVO {

    @Column("region_name")
    private String regionName;
    @Column("customhouse_title_to_region_name")
    private String customhouseTitleToRegionName;
    @Column("zone_name")
    private String zoneName;
    @Column("gins_family")
    private String ginsFamily;
    @Column("type")
    private String type;


    public List<String> getZoneNameDict() {
        return parse(zoneName);
    }

    public List<String> getRegionNameDict() {
        return parse(regionName);
    }

    public List<String> getGinsFamilyDict() {
        return parse(ginsFamily);
    }

    public Map<String, List<String>> getCustomhouseTitleToRegionNameDict() {
        List<String> parse = parse(customhouseTitleToRegionName);
        return ListUtils2.groupAndApply(
                parse.stream().map((o) -> o.split("@@@")).filter((o) -> o.length == 2)
                        .collect(Collectors.toList()),
                (k) -> k[0],
                (k, list) -> list.stream().map((o) -> o[1]).collect(Collectors.toList())
        );
    }

    public List<String> getTypeDict() {
        return parse(type);
    }

    private static List<String> parse(String str) {
        if (StringTools.isBlank(str)) {
            return new ArrayList<>();
        }
        return Arrays.stream(str.split("\\|\\|\\|")).filter(StringTools::isNotBlank).collect(Collectors.toList());
    }

}
