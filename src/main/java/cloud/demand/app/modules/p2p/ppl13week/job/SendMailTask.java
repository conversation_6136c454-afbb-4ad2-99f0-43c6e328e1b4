package cloud.demand.app.modules.p2p.ppl13week.job;


import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.modules.p2p.ppl13week.dto.other.MailSumDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SendMailTask {


    @Resource
    DBHelper demandDBHelper;
    @Resource
    Alert alert;

    /**
     * 每天早上 9 点发邮件
     *
     * 上一天22:00～当天22:00提交的PPL信息
     * 1，PPL提交，每天邮件汇总战区干预人
     *
     * 2023-07-31 确认废弃
     */
    @Synchronized(waitLockMillisecond = 10, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 22 * * ?")
    public void sendPplMeddleMail() {
//        log.info("begin sendMail");
//
//        LocalTime time2200 = LocalTime.of(22, 0);
//        LocalDateTime today = LocalDateTime.of(LocalDate.now(), time2200);
//        LocalDateTime yesToday = LocalDateTime.of(LocalDate.now().minusDays(1), time2200);
//
//        String sql = "where status='WAIT_MEDDLE' and create_time between ? and ?";
//
//        List<PplOrderDO> orders = demandDBHelper.getAll(PplOrderDO.class, sql,
//                DateUtils.format(yesToday, "yyyy-MM-dd HH:mm:ss"),
//                DateUtils.format(today, "yyyy-MM-dd HH:mm:ss"));
//
//        Map<String, List<PplOrderDO>> user2orders = new HashMap<>();
//        for (PplOrderDO order : orders) {
//            if (Strings.isNotBlank(order.getCurrentProcessor())) {
//                String[] users = order.getCurrentProcessor().split(";");
//                for (String user : users) {
//                    List<PplOrderDO> tmp = user2orders.getOrDefault(user, Lang.list());
//                    if (!tmp.contains(order)) {
//                        tmp.add(order);
//                    }
//                    user2orders.put(user, tmp);
//                }
//            }
//        }
//        for (String key : user2orders.keySet()) {
//            List<PplOrderDO> userOrders = user2orders.get(key);
//            Map<String, List<PplOrderDO>> industryDept2Orders = ListUtils.groupBy(userOrders,
//                    (o) -> o.getIndustryDept());
//            for (String industryDept : industryDept2Orders.keySet()) {
//                sendMail(key, industryDept, industryDept2Orders.get(industryDept));
//            }
//        }
//        log.info("end sendMail");
    }

//    private void sendMail(String user, String industryDept, List<PplOrderDO> pplOrders) {
//
//        String titleStr = "[%s]每日PPL提交汇总(%s)";
//        String title = String.format(titleStr, industryDept, DateUtils.format(new Date(), "yyyy-MM-dd"));
//
//        String contentStr = "<br/><br/>hi all，今日[%s]提交的PPL信息汇总如下： <br/><br/>";
//        String content = genTitle(title, "") + String.format(contentStr, industryDept);
//
//        Map<Tuple2<String, String>, List<PplOrderDO>> yearMonthWarZone2Order = ListUtils.groupBy(pplOrders,
//                (o) -> {
//                    LocalDate ym = LocalDate.of(o.getYear(), o.getMonth(), 1);
//                    return Tuple.of(ym.getYear() + "年" + ym.getMonth().getValue() + "月", o.getWarZone());
//                });
//
//        List<List<String>> data = Lang.list();
//        String sql = ORMUtils.getSql("/sql/ppl13week/send_ppl_meddle_mail.sql");
//        for (Tuple2<String, String> yearMonthWarZone : yearMonthWarZone2Order.keySet()) {
//            List<PplOrderDO> pplOrderDOS = yearMonthWarZone2Order.get(yearMonthWarZone);
//            List<String> orders = ListUtils.transform(pplOrderDOS, PplOrderDO::getPplOrder).stream().distinct()
//                    .collect(Collectors.toList());
//            List<MailSumDTO> raw = demandDBHelper.getRaw(MailSumDTO.class, sql, orders);
//            List<MailSumDTO> newSum = ListUtils.filter(raw,
//                    (o) -> Strings.equals(PplDemandTypeEnum.NEW.getCode(), o.getDemandType()));
//            if (Lang.isEmpty(newSum)) {
//                newSum.add(getZero());
//            }
//            List<MailSumDTO> retSum = ListUtils.filter(raw,
//                    (o) -> Strings.equals(PplDemandTypeEnum.RETURN.getCode(), o.getDemandType()));
//            if (Lang.isEmpty(retSum)) {
//                retSum.add(getZero());
//            }
//            List<MailSumDTO> elaSum = ListUtils.filter(raw,
//                    (o) -> Strings.equals(PplDemandTypeEnum.ELASTIC.getCode(), o.getDemandType()));
//            if (Lang.isEmpty(elaSum)) {
//                elaSum.add(getZero());
//            }
//
//            List<String> list = Lang.list(
//                    yearMonthWarZone._1, yearMonthWarZone._2, pplOrders.size() + "",
//                    newSum.get(0).getInstanceNum() + "", newSum.get(0).getTotalCore() + "",
//                    newSum.get(0).getTotalDisk() + "",
//                    elaSum.get(0).getInstanceNum() + "", elaSum.get(0).getTotalCore() + "",
//                    elaSum.get(0).getTotalDisk() + "",
//                    retSum.get(0).getInstanceNum() + "", retSum.get(0).getTotalCore() + "",
//                    retSum.get(0).getTotalDisk() + ""
//            );
//            data.add(list);
//        }
//
//        content += genTable2(data);
//
//        if (Strings.equals(DynamicProperties.sendPplMeddleMail(), "true")) {
//            alert.sendMail(user, title, content);
//        } else {
//            alert.sendMail("fireflychen;kaijiazhang;brightwwu", title + "\t [实际发送人：" + user + "]", content);
//        }
//    }


    private MailSumDTO getZero() {
        MailSumDTO mailSumDTO = new MailSumDTO();
        mailSumDTO.setInstanceNum(0);
        mailSumDTO.setTotalDisk(0);
        mailSumDTO.setTotalCore(0);
        return mailSumDTO;
    }

    private String genTitle(String title, String align) {
        String ret = "<table width=\"100%\" border=\"0\" cellspacing=\"0\" cellpadding=\"0\" align=\"center\">\n"
                + "  <tr>\n"
                + "    <td align=\"center\" class=\"biaoti\" height=\"60\">" + title + "</td>\n"
                + "  </tr>\n"
                + "  <tr>\n"
                + "    <td align=\"right\" height=\"25\">" + align + "</td>\n"
                + "  </tr>\n"
                + "</table>";
        return ret;
    }

    private String genTable(List<String> title, List<List<String>> data) {
        String table = "<table width=\"100%\" border=\"0\" cellspacing=\"1\" cellpadding=\"4\" bgcolor=\"#cccccc\" class=\"tabtop13\" align=\"center\">\n";
        table += " <tr>";
        for (String s : title) {
            table += "<td  class=\"btbg font-center\"> " + s + "</td>";
        }
        table += "  </tr>";

        for (List<String> datum : data) {
            table += " <tr>";
            for (String s : datum) {
                table += "   <td>" + s + "</td>";
            }
            table += "  </tr>";
        }
        table += "</table>";
        return table;
    }

    private String genTable2(List<List<String>> data) {
        String table = "<table  style=\"color: #000000d9; width: 100%; border-collapse: collapse; border-spacing: 0; text-align: center;\">\n";

        table += "<thead style=\"background: #fafafa; font-weight: 500;\">\n"
                + "    <tr>\n"
                + "      <th rowspan=\"2\" style=\"padding: 10px; border: 1px solid #f0f0f0; white-space: nowrap;\"> 规划月份</th>\n"
                + "      <th rowspan=\"2\" style=\"padding: 10px; border: 1px solid #f0f0f0; white-space: nowrap;\"> 战区</th>\n"
                + "      <th rowspan=\"2\" style=\"padding: 10px; border: 1px solid #f0f0f0;\"> PPL单数量</th>\n"
                + "      <th colspan=\"3\" style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 新增资源</th>\n"
                + "      <th colspan=\"3\" style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 弹性资源</th>\n"
                + "      <th colspan=\"3\" style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 退回资源</th>\n"
                + "    </tr>\n"
                + "    <tr>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 实例台数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 核心数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 容量(GB)</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 实例台数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 核心数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 容量(GB)</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 实例台数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 核心数</th>\n"
                + "      <th style=\"padding: 10px; border: 1px solid #f0f0f0;\"> 容量(GB)</th>\n"
                + "    </tr>\n"
                + "  </thead>";
        table += "  <tbody>\n";
        for (List<String> datum : data) {
            table += " <tr>";
            for (String s : datum) {
                table += "  <td style=\"padding: 10px; border-bottom: 1px solid #f0f0f0;\">" + s + "</td>";
            }
            table += "  </tr>";
        }
        table += "  </tbody>\n";

        table += "</table>";
        return table;
    }

    private String genTable1(List<String> title, List<List<String>> data) {
        String table = "<table  style=\"color: #000000d9; width: 100%; border-collapse: collapse; border-spacing: 0; text-align: center;\">\n";
        table += "  <thead style=\"background: #fafafa; font-weight: 500;\">\n"
                + "    <tr>\n";
        for (String s : title) {
            table += "<th style=\"padding: 10px; border: 1px solid #f0f0f0;\">" + s + "</th>";
        }
        table += "     </tr>\n"
                + "  </thead>\n";

        table += "  <tbody>\n";
        for (List<String> datum : data) {
            table += " <tr>";
            for (String s : datum) {
                table += "  <td style=\"padding: 10px; border-bottom: 1px solid #f0f0f0;\">" + s + "</td>";
            }
            table += "  </tr>";
        }
        table += "  </tbody>\n";

        table += "</table>";
        return table;
    }


}
