package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum;

/**
 * 拆分相关的服务
 * <AUTHOR>
 */
public interface Ppl13weekSplitService {

    /**
     * 创建拆分版本
     * @param taskId 任务id
     * @param name 拆分版本名称
     * @param type 拆分版本类型
     * @param desc 描述
     * @return 创建的拆分版本DO
     */
    PplForecastPredictTaskOutputVersionDO createOutputVersion(Long taskId, String name,
                                                              Ppl13weekForecastOutputVersionTypeEnum type, String desc);

    /**
     * 更新拆分服务状态
     * @param outputVersionId 版本id
     * @param status 状态名称
     */
    void updateStatus(Long outputVersionId, String status,String appendDesc);

    /**
     * 带版本记录拆分
     * @param taskId taskId
     * @param name 拆分名称
     * @param note 备注
     */
    void splitProjectCustomBgDeviceGroup(Long taskId, String name, String note);

    /**
     * 拆分一个task 会记录到拆分表，注意只有新增的数据，不兼容退回的
     * @param outputVersionId outputVersionId
     */
    void splitProjectCustomBgDeviceGroup(Long outputVersionId);

    /**
     * 拆分腰部的预测结果
     * @param taskId 预测的任务
     * @return log 信息
     */
    String splitCloudMiddle(Long taskId, Long outputVersionId);


    /**
     * 拆分EMR中长尾的预测结果
     * @param taskId 预测的任务
     * @param outputVersionId id
     * @return log 信息
     */
    String splitEmrLongTail(Long taskId, Long outputVersionId);

    /**
     * 拆分新中长尾的预测结果
     * 拆分可用区+大小核心， 行业部门和周不用拆
     * @param taskId 预测的任务
     * @return log 信息
     */
    String splitNewLongTail(Long taskId, Long outputVersionId);

    /**
     * 拆分EKS的预测结果，做一个对冲操作
     * @param taskId   预测的任务
     * @param outputVersionId id
     * @return log 信息
     */
    String splitEksLongTail(Long taskId, Long outputVersionId);

    /**
     * 拆分CDB的预测结果，做一个对冲操作
     * @param taskId   预测的任务
     * @param outputVersionId id
     * @return log 信息
     */
    String splitCdbLongTail(Long taskId, Long outputVersionId);
}
