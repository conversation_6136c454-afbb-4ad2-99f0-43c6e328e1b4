package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.ForecastInputSqlDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastProductEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastRangeEnumDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekPredictServiceImpl.InstanceTypeInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 执行13周需求预测的任务，并保存到输出结果
 * <AUTHOR>
 */
public interface Ppl13weekPredictService {


    /**
     * 执行预测
     * @param rangeEnums 业务范围
     * @param product 预测产品
     * @param category 方案名称
     * @param inputId 输入id
     * @param spikeUuid 毛刺uuid
     * @param algorithm 预测算法
     * @param algorithmParams 预测算法参数
     * @param predictN 预测的周期数
     * @param isEnable 是否启动
     * @param serialInterval 数据周期
     * @param sqlDTO sql
     * @param retAlgorithmParams 退回使用单独的算法参数
     * @return 返回预测的任务id
     */
    Long predict(Ppl13weekForecastRangeEnumDTO rangeEnums,
                 Ppl13weekForecastProductEnum product,
                 String category,
                 Long inputId, String spikeUuid,
                 String algorithm, String algorithmParams, int predictN,
                 boolean isEnable, SerialIntervalEnum serialInterval, ForecastInputSqlDTO sqlDTO,
                 String retAlgorithmParams);

    /**
     * 同步预测结果回来，这个是自动的，不需要参数
     * @return 返回本次同步中，完成预测的任务id
     */
    List<Long> syncPredictResult();

    /**
     * 同步任务
     * @return
     */
    boolean syncPredictResult(PplForecastPredictTaskDO task);


    /**
     * 获得机型大类对应大小核心机型等信息
     * @return map
     *
     */
    Map<String, InstanceTypeInfo> getInstanceTypeInfo();

}
