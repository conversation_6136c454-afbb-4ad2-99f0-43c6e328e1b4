package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionProgressResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpCommonHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import java.time.LocalDate;
import java.util.List;

public interface UnificatedVersionService {

    CrpCommonHolidayWeekDO queryNextWeek(LocalDate localDate);

    List<UnificatedVersionDTO> queryUnificatedVersionList(UnificatedVersionReq req);

    UnificatedVersionProgressResp queryUnificatedVersionProgress(Long id);

    UnificatedVersionDTO getUnificatedVersionDTOById(Long id);

    void createUnificatedVersion();

    void deleteUnificatedVersion(Long id);

    void saveUnificatedVersion(UnificatedVersionDTO version);

    // 开启大版本
    void startUnificatedVersion(UnificatedVersionDTO version,
            UnificatedVersionEventDO event);

    void startVersionForDemandVersion(UnificatedVersionDO version,
            UnificatedVersionEventDO event);

    // 到达PPL13周截止录入时间
    void ppl13WeekEnterDeadline(UnificatedVersionEventDO event);

    // 开启库存对冲
    void startStockSupply(UnificatedVersionDO version, UnificatedVersionEventDO event);

    // 关闭大版本
    void closeUnificatedVersion(UnificatedVersionDTO version, UnificatedVersionEventDO event);

    void closeVersionForDemandVersion(UnificatedVersionDO version, UnificatedVersionDTO nextVersion,
            UnificatedVersionEventDO event);

    void checkUnificatedVersionInit();

    void product13WeekEnterDeadline(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO);

    void productFullYearEnterDeadline(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO);

    void firstInit();

    void passFullYearDemandForecastToErp(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO)
            throws InterruptedException;

    void pass13WeeksDemandForecastToErp(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO)
            throws InterruptedException;

    void forceCloseUnificatedVersion(Long id) throws InterruptedException;

    void dataCheck(UnificatedVersionEventDO currentEvent, UnificatedVersionDTO versionDTO);

    /**
     * 查询进行中的大版本
     *
     * @return
     */
    UnificatedVersionDTO queryProcessingUnificatedVersion();

    void deviceEnterDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO);

    void deviceApproveDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO);

    void devicePassDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO) throws InterruptedException;

    void deviceReviewDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO);
}
