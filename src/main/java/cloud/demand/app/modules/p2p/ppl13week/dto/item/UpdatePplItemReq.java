package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SubmitPplReq.PplOrderDetailDTO;
import java.util.List;
import lombok.Data;

@Data
public class UpdatePplItemReq {

    /**
     * 用户名
     */
    String userName;


    String pplOrder;

    List<PplOrderDetailDTO> resources;

    /**
     * 编辑场景，当是预约单时编辑，传reservation
     */
    private String editScene;

}
