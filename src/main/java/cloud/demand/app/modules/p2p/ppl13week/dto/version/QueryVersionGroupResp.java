package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplGroupJoinVersionVO;
import lombok.Data;

import java.util.List;

@Data
public class QueryVersionGroupResp {

    /**总数*/
    private int total;

    @Data
    public static class VersionGroupDTO {
        /**version分组的唯一标识*/
        private Long groupId;

        private String versionCode;
        private String versionName;
        private String industryDept;
        private String product;
        private Integer demandBeginYear;
        private Integer demandBeginMonth;
        private Integer demandEndYear;
        private Integer demandEndMonth;
        private String versionType;
        private String versionTypeName;
        private String status;
        private String statusName;

        public static VersionGroupDTO from(PplGroupJoinVersionVO d) {
            VersionGroupDTO versionGroupDTO = new VersionGroupDTO();
            versionGroupDTO.setGroupId(d.getVersionGroupDO().getId());
            versionGroupDTO.setVersionCode(d.getVersionGroupDO().getVersionCode());
            versionGroupDTO.setVersionName(d.getVersionDO() == null ? "" : d.getVersionDO().getVersionName());
            versionGroupDTO.setIndustryDept(d.getVersionGroupDO().getIndustryDept());
            versionGroupDTO.setProduct(d.getVersionGroupDO().getProduct());
            versionGroupDTO.setDemandBeginYear(d.getVersionDO() == null ? null : d.getVersionDO().getDemandBeginYear());
            versionGroupDTO.setDemandBeginMonth(d.getVersionDO() == null ? null : d.getVersionDO().getDemandBeginMonth());
            versionGroupDTO.setDemandEndYear(d.getVersionDO() == null ? null : d.getVersionDO().getDemandEndYear());
            versionGroupDTO.setDemandEndMonth(d.getVersionDO() == null ? null : d.getVersionDO().getDemandEndMonth());
            versionGroupDTO.setVersionType(d.getVersionDO().getVersionType());
            versionGroupDTO.setVersionTypeName(PplVersionTypeEnum.getNameByCode(d.getVersionDO().getVersionType()));
            versionGroupDTO.setStatus(d.getVersionGroupDO().getStatus());
            versionGroupDTO.setStatusName(PplVersionGroupStatusEnum.getNameByCode(d.getVersionGroupDO().getStatus()));
            return versionGroupDTO;
        }
    }

    private List<VersionGroupDTO> data;

}
