package cloud.demand.app.modules.p2p.longterm.controller;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.p2p.longterm.controller.req.ParseImportExcelReq;
import cloud.demand.app.modules.p2p.longterm.controller.req.QueryLongtermDictReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.DownloadExcelResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.ImportFromExcelResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.QueryLongtermImportDictResp;
import cloud.demand.app.modules.p2p.longterm.service.DatabaseAccess;
import cloud.demand.app.modules.p2p.longterm.service.LongtermVersionImportService;
import cloud.demand.app.modules.p2p.longterm.vo.VersionGroupRecordVO;
import cloud.demand.app.web.model.common.DownloadBean;
import com.pugwoo.wooutils.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

@JsonrpcController("/longterm")
@Slf4j
public class LongtermVersionImportController {

    @Resource
    LongtermVersionImportService longtermVersionImportService;

    @Resource
    DatabaseAccess databaseAccess;

    /**
     * 导出中长期 的excel
     *
     * @param req 参数
     * @return 下载bean
     */
    @RequestMapping
    public DownloadBean downloadExcel(@JsonrpcParam ParseImportExcelReq req) {
        DownloadExcelResp resp = longtermVersionImportService.downloadExcel(req);
        return new DownloadBean(resp.getFileName(), resp.getBytes());
    }


    /**
     * 解析导出的excel, 返回解析的数据
     *
     * @param excelFile file
     * @param params req
     * @return 解析的数据
     */
    @RequestMapping
    public ImportFromExcelResp importFromExcel(
            @RequestParam("file") MultipartFile excelFile,
            @RequestParam String params) {

        ParseImportExcelReq req = JSON.parse(params, ParseImportExcelReq.class);
        req.setExcelFile(excelFile);
        VersionGroupRecordVO vgrInfo = databaseAccess.queryVersionAndGroupRecord(req.getRecordId());
        if (vgrInfo.getVersionGroupDO() == null) {
            throw new WrongWebParameterException(
                    "分组Id:" + req.getGroupId() + "不存在，可能已经被删除，请回退到中长期版本列表");
        }
        req.setGroupId(vgrInfo.getVersionGroupDO().getId());
        return longtermVersionImportService.importFromExcel(req);
    }


    /**
     * 查询首页的字典接口
     *
     * @return resp
     */
    @RequestMapping
    public QueryLongtermImportDictResp queryLongtermImportDict() {
        return longtermVersionImportService.queryLongtermImportDict();
    }

    @RequestMapping
    public Object queryLongtermImportZoneNameDict(@JsonrpcParam QueryLongtermDictReq req) {
        return longtermVersionImportService.queryLongtermImportZoneNameDict(req);
    }

    @RequestMapping
    public Object queryLongtermImportInstanceTypeDict(@JsonrpcParam QueryLongtermDictReq req) {
        return longtermVersionImportService.queryLongtermImportInstanceTypeDict(req);
    }


}
