package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import lombok.Data;

/**
 * 查询订单返回的订单信息
 */
@Data
public class OrderDTO {

    private Long id;
    private String orderId;
    private String creator;
    private String architect;
    private String uin;
    private String appId;
    private String appRole;
    private String appName;
    private String appShortName;
    private String organizationName;
    private String industryType;
    private String region;
    private String regionAlias;
    private String reasonType;
    private String reason;
    private String expectTime;
    private String latestExpectTime;
    private String orderType;
    private String orderCategory; // 订单分类
    private String asStartTime; // 弹性开始时间
    private String asEndTime; // 弹性结束时间
    private String status;
    private String cvmMatchType;
    private String cvmMatchDetail;
    private String cbsMatchType;
    private String cbsMatchDetail;
    private String feedback;
    private String createTime;
    private String updateTime;
    private String flowId;
    private Long totalCpuCount;
    private Long totalCbsSize;

}
