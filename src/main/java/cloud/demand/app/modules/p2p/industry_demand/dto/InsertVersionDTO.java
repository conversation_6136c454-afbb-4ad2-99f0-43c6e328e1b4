package cloud.demand.app.modules.p2p.industry_demand.dto;

import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandVersionStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class InsertVersionDTO {

    @NotEmpty(message = "版本名称不能为空")
    private String name;
    @NotEmpty(message = "版本编码不能为空")
    private String demandVersion;
    private String desc;
    @NotNull(message = "开放开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportOpenDate;
    @NotNull(message = "开放结束时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportCloseDate;
    @NotNull(message = "规划范围开始年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastFrom;
    @NotNull(message = "规划范围结束年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastTo;

    public static IndustryDemandVersionDO toDO(InsertVersionDTO dto) {
        IndustryDemandVersionDO industryDemandVersionDO = new IndustryDemandVersionDO();
        industryDemandVersionDO.setStatus(IndustryDemandVersionStatusEnum.DISABLED.getCode());
        industryDemandVersionDO.setDemandVersion(dto.getDemandVersion());
        industryDemandVersionDO.setName(dto.getName());
        industryDemandVersionDO.setDesc(dto.getDesc() == null ? "" : dto.getDesc());
        industryDemandVersionDO.setDemandImportOpenDate(DateUtils.toLocalDate(dto.getDemandImportOpenDate()));
        industryDemandVersionDO.setDemandImportCloseDate(DateUtils.toLocalDate(dto.getDemandImportCloseDate()));
        industryDemandVersionDO.setForecastFromYear(DateUtils.getYear(dto.getForecastFrom()));
        industryDemandVersionDO.setForecastFromMonth(DateUtils.getMonth(dto.getForecastFrom()));
        industryDemandVersionDO.setForecastToYear(DateUtils.getYear(dto.getForecastTo()));
        industryDemandVersionDO.setForecastToMonth(DateUtils.getMonth(dto.getForecastTo()));
        return industryDemandVersionDO;
    }
}
