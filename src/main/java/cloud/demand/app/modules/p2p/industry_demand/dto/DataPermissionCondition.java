package cloud.demand.app.modules.p2p.industry_demand.dto;

import java.util.List;

public interface DataPermissionCondition {

    /** 行业部门查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> industryDeptListGet();

    /** 设置行业部门查询条件（数据权限检查通过后），直接更改list的引用 */
    void industryDeptListSet(List<String> industryDeptList);

    /** 是否根据行业部门查询，为 true 时才会 调用 {@link #industryDeptListSet(List)} 进行权限条件填入 */
    default boolean queryByIndustryDeptList() {
        return true;
    }

    /** 产品查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> productListGet();

    /** 设置产品查询条件（数据权限检查通过后），直接更改list的引用 */
    void productListSet(List<String> productList);

    /** 是否根据产品查询，为 true 时才会 调用 {@link #productListSet(List)} 进行权限条件填入 */
    default boolean queryByProductList() {
        return true;
    }

    /** 行业的战区查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> warZoneListGet();

    /** 设置战区查询条件（数据权限检查通过后），直接更改list的引用 */
    void warZoneListSet(List<String> warZoneList);

    /** 是否根据战区查询，为 true 时才会 调用 {@link #warZoneListSet(List)} 进行权限条件填入 */
    default boolean queryByWarZoneList() {
        return true;
    }

    /** 客户uin查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> customerUinListGet();

    /** 设置客户uin查询条件（数据权限检查通过后），直接更改list的引用 */
    void customerUinListSet(List<String> customerUinList);

    /** 是否根据客户uin查询，为 true 时才会 调用 {@link #customerUinListSet(List)} 进行权限条件填入 */
    default boolean queryByCustomerUinList() {
        return true;
    }

    /** 客户简称查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> customerShortNameListGet();

    /** 设置客户简称查询条件（数据权限检查通过后），直接更改list的引用 */
    void customerShortNameListSet(List<String> customerShortNameList);

    /** 是否根据客户简称查询，为 true 时才会 调用 {@link #customerShortNameListSet(List)} 进行权限条件填入 */
    default boolean queryByCustomerShortNameList() {
        return true;
    }

    /** 行通用客户简称查询条件，用于进行数据权限检查，不会对原list进行操作 */
    List<String> commonCustomerNameListGet();

    /** 设置通用客户简称查询条件（数据权限检查通过后），直接更改list的引用 */
    void commonCustomerNameListSet(List<String> commonCustomerNameList);

    /** 是否根据通用客户简称查询，为 true 时才会 调用 {@link #commonCustomerNameListSet(List)} 进行权限条件填入 */
    default boolean queryByCommonCustomerNameList() {
        return true;
    }

}
