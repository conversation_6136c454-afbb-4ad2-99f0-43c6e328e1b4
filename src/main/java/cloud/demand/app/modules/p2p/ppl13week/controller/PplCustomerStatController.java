package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.industry_cockpit.v3.service.IndustryCockpitV3DictService;
import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryApplyDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDistributedReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDistributedResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerGlobalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerGlobalResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryDictReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigStatIndustryDeptClassDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCustomerStatService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.string.StringTools;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 客户构成统计相关接口
 */
@JsonrpcController("/ppl13week")
@Slf4j
public class PplCustomerStatController {

    @Resource
    private PplCustomerStatService pplCustomerStatService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplVersionService pplVersionService;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private IndustryReportDictService industryReportDictService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private IndustryCockpitV3DictService v3DictService;

    // 提供版本号和需求年月的查询，返回同https://exp-crp.woa.com/strategy/13ppl-version
    @RequestMapping
    public QueryVersionResp queryDictVersionList() {
        return pplVersionService.queryAllVersionNoAuth();
    }

    // 提供需求类型的枚举接口
    @RequestMapping
    public List<Map<String, Object>> queryDictDemandType() {
        PplDemandTypeEnum[] values = PplDemandTypeEnum.values();
        return ListUtils.transform(values, o -> MapUtils.of("name", o.getName(), "value", o.getCode()));
    }

    // 提供预约单状态的枚举接口
    @RequestMapping
    public List<Map<String, Object>> queryDictApplyStatus() {
        YunxiaoOrderStatusEnum[] values = YunxiaoOrderStatusEnum.values();
        return ListUtils.transform(values, o -> MapUtils.of("name", o.getName(), "value", o.getCode()));
    }

    /**
     * 查询客户构成概览
     */
    @RequestMapping
    public QueryCustomerGlobalResp queryCustomerGlobal(@JsonrpcParam QueryCustomerGlobalReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }

        return pplCustomerStatService.queryCustomerGlobal(req);
    }

    /**
     * 客户构成 - 分布
     */
    @RequestMapping
    public QueryCustomerDistributedResp queryCustomerDistributed(@JsonrpcParam QueryCustomerDistributedReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }
        return pplCustomerStatService.queryCustomerDistributed(req);
    }

    /**
     * 客户构成 - 明细
     */
    @RequestMapping
    public QueryCustomerDetailResp queryCustomerDistributedDetail(@JsonrpcParam QueryCustomerDistributedReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }
        return pplCustomerStatService.queryCustomerDistributedDetail(req);
    }

    /**
     * 预约明细，用于导出excel
     */
    @RequestMapping
    public QueryApplyDetailResp queryApplyDetail(@JsonrpcParam QueryCustomerDistributedReq req) {
        if (req == null) {
            throw new WrongWebParameterException("缺少请求参数");
        }
        return pplCustomerStatService.queryApplyDetail(req);
    }

    // 下面是各种字典接口

    @RequestMapping
    public List<String> queryDictDemandScene() {
        return pplDictService.queryAllDemandSceneDict();
    }

    @RequestMapping
    public List<String> queryDictRegionName() {
        return pplDictService.queryAllCityName();
    }

    @RequestMapping
    public List<String> queryDictZoneName() {
        return pplDictService.queryAllZoneName(null);
    }

    @RequestMapping
    public List<Map<String, String>> queryDictRegionInfoMap() {
        //  从字典接口中拿
        List<RegionInfoDTO> raw = demandDBHelper.getRaw(RegionInfoDTO.class,
                "select distinct region_short_ch_name, zone_name\n" +
                        "from industry_demand_region_zone_instance_type_dict\n" +
                        "where deleted = 0");
        List<Map<String, String>> result = Lang.list();
        for (RegionInfoDTO each : raw) {
            HashMap<String, String> map = new HashMap<>();
            map.put("region_name", each.getRegionName());
            map.put("zone_name", each.getZoneName());
            result.add(map);
        }
        return result;
    }

    @Data
    public static class RegionInfoDTO {

        @Column("region_short_ch_name")
        private String regionName;

        @Column("zone_name")
        private String zoneName;
    }

    @RequestMapping
    public List<String> queryDictWarZone() {
        return demandDBHelper.getRaw(String.class, "SELECT DISTINCT war_zone FROM `ppl_order`");
    }

    /** 带鉴权 */
    @RequestMapping
    public List<String> queryDictWarZoneWithAuth() {
        List<String> raw = demandDBHelper.getRaw(String.class, "SELECT DISTINCT war_zone FROM `ppl_order`");
        return v3DictService.filterWarZone(raw);
    }

    @RequestMapping
    public List<String> queryDictInstanceType() {
        return pplDictService.queryAllInstanceType();
    }

    @RequestMapping
    public List<String> queryDictInstanceModel() {
        return pplDictService.queryAllInstanceModel();
    }

    @RequestMapping
    public List<String> queryDictCustomerShortName() {
        return demandDBHelper.getRaw(String.class, "SELECT DISTINCT customer_short_name FROM `ppl_order`");
    }

    @RequestMapping
    public List<String> queryDictCustomerUin() {
        return demandDBHelper.getRaw(String.class, "SELECT DISTINCT customer_uin FROM `ppl_order`");
    }

    @RequestMapping
    public List<String> queryDictCategory(@JsonrpcParam QueryDictReq req) {
        //  1、获取全量映射表
        List<PplConfigStatIndustryDeptClassDO> all = demandDBHelper.getAll(PplConfigStatIndustryDeptClassDO.class);
//        all = ListUtils.filter(all, o -> !Objects.equals(o.getCategory(), "中长尾"));
        List<String> result = Lang.list();

        //  2、鉴权
        String userName = LoginUtils.getUserName();
        //  2.1、若为no 或 UNKNOWN 或ADMIN角色，则默认最高权限
        if ("no".equalsIgnoreCase(userName) || "UNKNOWN".equalsIgnoreCase(userName)) {
            result = ListUtils.toList(ListUtils.toSet(all, o -> o.getCategory()));
            result.add("未分类");
            //  对于未分类进行去重
            result = result.stream().distinct().collect(Collectors.toList());
            return result;
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(userName);
        List<IndustryDemandAuthDO> adminAuth =
                ListUtils.filter(auths, o -> IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(o.getRole()));
        if (!adminAuth.isEmpty()) {
            result = ListUtils.toList(ListUtils.toSet(all, o -> o.getCategory()));
            result.add("未分类");
            //  对于未分类进行去重
            result = result.stream().distinct().collect(Collectors.toList());
            return result;
        }

        //  2.2、查询用户针对某产品的权限
        List<IndustryDemandAuthDO> authList = Lang.list();
        //  2.2、查询用户针对某产品的权限
        List<String> productList = req.getProduct() == null ? Lang.list() : req.getProduct();
        for (String product : productList) {
            if (StringTools.isNotBlank(product)) {
                auths = ListUtils.filter(auths, o -> (o.getProduct() != null && o.getProduct().contains(product)));
            } else {
                // 默认鉴权
                auths = ListUtils.filter(auths, o -> o.getProduct() != null &&
                        (o.getProduct().contains("CVM") || o.getProduct().contains("CVM&CBS")));
            }
            authList.addAll(auths);
        }

        //  3、整合所有角色的行业部门
        Set<String> industryDepts = new HashSet<>();
        for (IndustryDemandAuthDO auth : auths) {
            String industry = auth.getIndustry();
            if (StringTools.isNotBlank(industry)) {
                String[] strs = industry.split(";");
                for (String str : strs) {
                    if (StringTools.isNotBlank(str)) {
                        industryDepts.add(str);
                    }
                }
            }
        }

        //  4、通过用户的行业部门权限获取对应的category，如果行业部门映射不到category，则枚举中新增未分类枚举
        Set<String> tempRet = new HashSet<>();
        for (String industryDept : industryDepts) {
            List<PplConfigStatIndustryDeptClassDO> filter =
                    ListUtils.filter(all, o -> Objects.equals(o.getIndustryDept(), industryDept));
            if (filter.isEmpty()) {
                tempRet.add("未分类");
            }
            tempRet.addAll(ListUtils.toSet(filter, o -> o.getCategory()));
        }

        return tempRet.stream().distinct().collect(Collectors.toList());
    }

    @RequestMapping
    public List<Map<String, Object>> queryDictIndustryDept(@JsonrpcParam QueryDictReq req) {
        String sql = "select distinct industry_dept from ppl_order where deleted = 0 order by industry_dept";
        //  1、获取全量映射表
        List<String> all = demandDBHelper.getRaw(String.class, sql);
        all = ListUtils.filter(all, o -> !Objects.equals(o, "中长尾") && StringTools.isNotBlank(o));
        List<Map<String, Object>> list = Lang.list();

        for (String industryDept : all) {
            Map<String, Object> map = new HashMap<>();
            map.put("industryDept", industryDept);
            map.put("category", industryReportDictService.getCategoryByDept(industryDept));
            list.add(map);
        }

        if (ListUtils.isEmpty(list)) {
            return list;
        }

        //  2、鉴权
        String userName = LoginUtils.getUserName();
        //  2.1、若为no 或 UNKNOWN 或ADMIN角色，则默认最高权限
        if ("no".equalsIgnoreCase(userName) || "UNKNOWN".equalsIgnoreCase(userName)) {
            return list;
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(userName);

        // 1、是否为管理员
        List<IndustryDemandAuthDO> adminAuth =
                ListUtils.filter(auths, o -> IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(o.getRole()));
        if (!adminAuth.isEmpty()) {
            return list;
        }

        List<IndustryDemandAuthDO> authList = Lang.list();

        // 2、是否为行业数据关注人
        List<IndustryDemandAuthDO> industryDataAuth =
                ListUtils.filter(auths,
                        o -> IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode().equals(o.getRole()));

        if (!industryDataAuth.isEmpty()) {
            authList.add(industryDataAuth.get(0));
        }

        //  3、整合所有角色的行业部门
        Set<String> industryDepts = new HashSet<>();
        for (IndustryDemandAuthDO auth : authList) {
            String industry = auth.getIndustry();
            if (StringTools.isNotBlank(industry)) {
                String[] strs = industry.split(";");
                for (String str : strs) {
                    if (StringTools.isNotBlank(str)) {
                        industryDepts.add(str);
                    }
                }
            }
        }

//        String belongDept = pplDictService.queryIndustryDept(userName).get(0);
//        industryDepts.add(belongDept);

        //  4、通过用户的行业部门权限获取数据
        List<Map<String, Object>> result = Lang.list();
        for (Map<String, Object> map : list) {
            List<String> filter = ListUtils.filter(industryDepts, o -> Objects.equals(o, map.get("industryDept")));
            if (ListUtils.isNotEmpty(filter)) {
                result.add(map);
            }
        }
        return ListUtils.toList(result);
    }

    @RequestMapping
    public List<String> queryDictProduct() {
        return demandDBHelper.getRaw(String.class, "SELECT DISTINCT product FROM `ppl_item`");
    }

}
