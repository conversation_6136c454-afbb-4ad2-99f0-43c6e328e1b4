package cloud.demand.app.modules.p2p.ppl13week.dto.apply2;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class QueryUuidInfosResp {

    private List<UuidInfo> uuidInfos;

    @Data
    public static class UuidInfo {

        private String uuid;

        private List<PplInfo> pplInfos;
    }

    @Data
    public static class PplInfo {

        private String uuid;

        private String pplOrder;

        private String pplId;

        private String parentPplId;

        private String yunxiaoOrderId;

        private Long yunxiaoDetailId;

        private String customerUin;

        private String customerShortName;

        // 地域
        private String regionName;

        // 可用区
        private String zoneName;

        private String product;

        private String instanceType;

        private String instanceModel;

        private String gpuType;

        private Integer instanceNum;

        private Integer totalCore;

        private BigDecimal totalGpuNum;

        // 是否预约（true-已预约，false-未预约）
        private Boolean isApplied;

        // 预测来源，用户报备/系统自动补充
        private String pplSource;

        // ppl需求状态
        private String status;

        // ppl来源
        private String source;

    }

}
