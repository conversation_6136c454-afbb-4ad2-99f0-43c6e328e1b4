package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessNodeDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessTemplateDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import java.util.List;
import lombok.Data;

@Data
public class PplProcessTemplateVO extends PplInnerProcessTemplateDO {

    @RelatedColumn(localColumn = "id", remoteColumn = "template_id")
    private List<PplInnerProcessNodeDO> nodeDOList;

}
