package cloud.demand.app.modules.p2p.ppl13week.dto.order.req;

import cloud.demand.app.modules.p2p.industry_demand.dto.DataPermissionCondition;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.web.model.common.Page;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryPplDraftReq implements DataPermissionCondition {

    Page page;
    /**
     * 需求年月
     */
    private String startYearMonth;
    /**
     * 需求年月
     */
    private String endYearMonth;

    /**
     * 客户uin
     */
    private List<String> customerUin;

    /**
     * 客户简称
     */
    private List<String> customerShortName;

    /**
     * 项目名称
     */
    private List<String> projectName;

    /**
     * 查询指定的ppl单
     */
    private List<String> pplOrder;

    /**
     * 地域
     */
    private List<String> regionName;

    /**
     * 地域类型，单选，值为：国内、境外
     */
    private String regionType;

    /**
     * 可用区
     */
    private List<String> zoneName;
    /**
     * 实例类型/机型
     */
    private List<String> instanceType;
    /**
     * 战区
     */
    private List<String> warZone;

    /**
     * 需求类型
     */
    private List<String> demandType;

    /**
     * 产品
     */
    private List<String> product;

    /**
     * 单据状态 'saved' 草稿  | 'inProgress' 流程中| 'finished' 已生效
     */
    String status;

    // @NotBlank // 架构师页面支持全行业
    private String industryDept;

    /**
     * 客户范围：名单、报备、中长尾
     */
    private List<String> customerScope;

    private Long versionId;

    private String username;

    private String itemStatus;

    /**
     * 查询待处理列表时候会传未true
     */
    private Boolean queryIsNeedDeal = Boolean.FALSE;

    /**
     *  跳过 提单人 为 登陆用户的查询条件
     */
    private boolean skipLoginUser = false;

    private Map<String, PplListVo> nextVersionValidMap = new HashMap<>();


    private List<String> databaseName;

    @Override
    public List<String> industryDeptListGet() {
        if (this.getIndustryDept() == null) {
            return null;
        }
        return ListUtils.newArrayList(getIndustryDept());
    }

    @Override
    public void industryDeptListSet(List<String> industryDeptList) {
        // 查询入参只有一个 行业部门，所以不用设置
    }

    @Override
    public List<String> productListGet() {
        return getProduct();
    }

    @Override
    public void productListSet(List<String> productList) {
        setProduct(productList);
    }

    @Override
    public List<String> warZoneListGet() {
        return getWarZone();
    }

    @Override
    public void warZoneListSet(List<String> warZoneList) {
        setWarZone(warZoneList);
    }

    @Override
    public List<String> customerUinListGet() {
        return getCustomerUin();
    }

    @Override
    public void customerUinListSet(List<String> customerUinList) {
        setCustomerUin(customerUinList);
    }

    @Override
    public List<String> customerShortNameListGet() {
        return getCustomerShortName();
    }

    @Override
    public void customerShortNameListSet(List<String> customerShortNameList) {
        setCustomerShortName(customerShortNameList);
    }

    @Override
    public List<String> commonCustomerNameListGet() {
        return null;
    }

    @Override
    public void commonCustomerNameListSet(List<String> commonCustomerNameList) {

    }

    @Override
    public boolean queryByCommonCustomerNameList() {
        return false;
    }
}
