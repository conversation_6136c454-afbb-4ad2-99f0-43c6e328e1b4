package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;// package a.b.c;

import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * <AUTHOR>
 */

@Data
@ToString
public class PplForecastInputWeekAvgTailDTO {

    @Column(value = "week_year")
    private Integer weekYear;

    @Column(value = "week_month")
    private Integer weekMonth;

    @Column(value = "week")
    private Integer week;

    @Column(value = "week_start")
    private LocalDate weekStart;

    @Column(value = "week_end")
    private LocalDate weekEnd;

    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region")
    private String region;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "last_day_num")
    private BigDecimal lastDayNum;

    @Column(value = "new_diff")
    private BigDecimal newDiff;

    @Column(value = "ret_diff")
    private BigDecimal retDiff;

}