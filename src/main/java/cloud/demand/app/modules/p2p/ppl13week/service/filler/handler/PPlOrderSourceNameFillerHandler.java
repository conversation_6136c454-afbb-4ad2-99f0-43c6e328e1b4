package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.PPlOrderSourceCategoryFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class PPlOrderSourceNameFillerHandler implements FillerHandler<PPlOrderSourceCategoryFiller> {

    @Override
    public void fill(List<PPlOrderSourceCategoryFiller> obj) {
        for (PPlOrderSourceCategoryFiller filler : obj) {
            filler.fillSourceCategory(getPPlOrderSourceName(filler.provideSource()));
        }
    }

    public static String getPPlOrderSourceName(String pPlOrderSource) {
        PplOrderSourceTypeEnum typeEnum = PplOrderSourceTypeEnum.getByCode(pPlOrderSource);
        return typeEnum == null ? pPlOrderSource : typeEnum.getCategory();
    }
}
