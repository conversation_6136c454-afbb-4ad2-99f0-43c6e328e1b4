package cloud.demand.app.modules.p2p.longterm.enums;

import lombok.Getter;

import java.util.Objects;

@Getter
public enum LongtermVersionGroupItemTimeUnitEnum {

    MONTH("MONTH", "月"),

    QUARTER("QUARTER", "季度"),

    YEAR("YEAR", "年度"),
    ;

    final private String code;
    final private String name;

    LongtermVersionGroupItemTimeUnitEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static LongtermVersionGroupItemTimeUnitEnum getByCode(String code) {
        for (LongtermVersionGroupItemTimeUnitEnum e : LongtermVersionGroupItemTimeUnitEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        LongtermVersionGroupItemTimeUnitEnum e = getByCode(code);
        return e == null ? (code == null ? "" : code) : e.getName();
    }

}