package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemSatisfySupplyVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemSummaryVO;
import com.pugwoo.wooutils.string.StringTools;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 版本资源统计返回
 */
@Data
public class VersionGroupStatResp {

    /**
     * 当前比较的版本，当此值为空时，表示没有上一个版本，页面要对应提示
     */
    private String currentCompareVersion;
    /**
     * 可比较的所有版本
     */
    private List<VersionDTO> comparableVersions;

    // 各下拉框枚举
    private List<String> dictRegionName;
    private List<String> dictWarZone;
    private List<String> dictInstanceType;
    private List<String> dictDemandScene;
    private List<String> dictCustomerShortName;
    private List<String> dictCustomerUin;

    /**
     * 整体预测情况
     */
    private List<SummaryDTO> summary;

    /**
     * 新增需求预计满足情况
     */
    private List<SummaryDTO> satisfy;
    /**
     * 是否还没有供应记录
     */
    private Boolean isNoSupply;

    @Data
    public static class VersionDTO {

        private String versionCode;
        private String versionName;

        public static VersionDTO from(PplVersionDO version) {
            VersionDTO versionDTO = new VersionDTO();
            versionDTO.setVersionCode(version.getVersionCode());
            versionDTO.setVersionName(version.getVersionName());
            return versionDTO;
        }
    }

    public static String getResourceTypeUnit(String resourceType) {
        if (resourceType == null) {
            return "核";
        }
        switch (resourceType) {
            case "CVM":
                return "台";
            case "CBS":
                return "GB";
            case "GPU":
                return "卡";
            default:
                return "核";
        }
    }

    @Data
    public static class SummaryDTO {

        private Integer year;
        private Integer month;
        private String week; // 13周属性
        private String demandType; // 需求类型：新增/弹性/退回；需求类型：未执行/已执行/    库存/采购/搬迁
        private String summaryType; // 预测 已预约 实际对冲
        private Integer value;
        private Integer diffValue; // 相比于上期的变化量
        private String unit; // 单位
        private String time; // 代表13周外的

        private Boolean isSupplyType = false; // 是否是库存对冲满足类型

        public static String getUnit(String resourceType) {
            switch (resourceType) {
                case "CVM":
                    return "台";
                case "CBS":
                    return "GB";
                case "GPU":
                    return "卡";
                default:
                    return "核";
            }
        }

        public static List<SummaryDTO> from(PplVersionGroupItemSummaryVO v, String resourceType, String type) {
            List<SummaryDTO> list = new ArrayList<>();
            SummaryDTO predictSummary = new SummaryDTO();
            SummaryDTO applySummary = null;
            predictSummary.setYear(v.getYear());
            predictSummary.setMonth(v.getMonth());
            predictSummary.setWeek(""); // 13周属性不要了
            predictSummary.setSummaryType("干预后预测");
            if (type.equals("summary")) {
                predictSummary.setDemandType(PplDemandTypeEnum.getNameByCode(v.getDemandType()));
            } else {
                predictSummary.setDemandType(PplItemStatusEnum.getNameByCode(v.getDemandType()));
            }
            switch (resourceType) {
                case "CVM":
                    predictSummary.setValue(v.getV2Num());
                    predictSummary.setDiffValue(v.getV2Num() - v.getV1Num());
                    predictSummary.setUnit("台");
                    applySummary = new SummaryDTO();
                    BeanUtils.copyProperties(predictSummary, applySummary);
                    applySummary.setSummaryType("已预约");
                    applySummary.setValue(v.getV2ApplyInstanceNum());
                    applySummary.setDiffValue(v.getV2ApplyInstanceNum() - v.getV1ApplyInstanceNum());
                    break;
                case "CBS":
                    predictSummary.setValue(v.getV2Storage());
                    predictSummary.setDiffValue(v.getV2Storage() - v.getV1Storage());
                    predictSummary.setUnit("GB");
                    break;
                case "GPU":
                    predictSummary.setValue(v.getV2Gpu());
                    predictSummary.setDiffValue(v.getV2Gpu() - v.getV1Gpu());
                    predictSummary.setUnit("卡");
                    break;
                default:
                    predictSummary.setValue(v.getV2Core());
                    predictSummary.setDiffValue(v.getV2Core() - v.getV1Core());
                    predictSummary.setUnit("核");
                    applySummary = new SummaryDTO();
                    BeanUtils.copyProperties(predictSummary, applySummary);
                    applySummary.setSummaryType("已预约");
                    applySummary.setValue(v.getV2ApplyCore());
                    applySummary.setDiffValue(v.getV2ApplyCore() - v.getV1ApplyCore());
            }
            list.add(predictSummary);
            if (applySummary != null && !applySummary.getDemandType().equals(PplDemandTypeEnum.RETURN.getName())) {
                list.add(applySummary);
            }
            return list;
        }

        /**
         * 只保存 干预前预测 数据
         *
         * @param v
         * @param resourceType
         * @param type
         * @return
         */
        public static List<SummaryDTO> from2(PplVersionGroupItemSummaryVO v, String resourceType, String type) {
            List<SummaryDTO> list = new ArrayList<>();
            SummaryDTO predictSummary = new SummaryDTO();
            predictSummary.setYear(v.getYear());
            predictSummary.setMonth(v.getMonth());
            predictSummary.setWeek(""); // 13周属性不要了
            predictSummary.setSummaryType("干预前预测");
            if (type.equals("summary")) {
                predictSummary.setDemandType(PplDemandTypeEnum.getNameByCode(v.getDemandType()));
            } else {
                predictSummary.setDemandType(PplItemStatusEnum.getNameByCode(v.getDemandType()));
            }
            switch (resourceType) {
                case "CVM":
                    predictSummary.setValue(v.getV2Num());
                    predictSummary.setDiffValue(v.getV2Num() - v.getV1Num());
                    predictSummary.setUnit("台");
                    break;
                case "CBS":
                    predictSummary.setValue(v.getV2Storage());
                    predictSummary.setDiffValue(v.getV2Storage() - v.getV1Storage());
                    predictSummary.setUnit("GB");
                    break;
                case "GPU":
                    predictSummary.setValue(v.getV2Gpu());
                    predictSummary.setDiffValue(v.getV2Gpu() - v.getV1Gpu());
                    predictSummary.setUnit("卡");
                    break;
                default:
                    predictSummary.setValue(v.getV2Core());
                    predictSummary.setDiffValue(v.getV2Core() - v.getV1Core());
                    predictSummary.setUnit("核");
            }
            list.add(predictSummary);
            return list;
        }

        public static SummaryDTO from(PplVersionGroupItemSatisfySupplyVO v, String resourceType) {
            SummaryDTO summaryDTO = new SummaryDTO();
            summaryDTO.setYear(v.getYear());
            summaryDTO.setMonth(v.getMonth());
            summaryDTO.setWeek(""); // 13周属性不要了

            String matchName = PplStockSupplyMatchTypeEnum.getNameByCode(v.getMatchType());
            if (StringTools.isBlank(matchName)) {
                matchName = v.getMatchType();
            }
            summaryDTO.setDemandType(matchName);
            summaryDTO.setIsSupplyType(true);

            switch (resourceType) {
                case "CVM":
                    summaryDTO.setValue(v.getTotalInstance());
                    summaryDTO.setUnit("台");
                    break;
                case "CBS":
                    summaryDTO.setValue(v.getTotalDisk());
                    summaryDTO.setUnit("GB");
                    break;
                default:
                    summaryDTO.setValue(v.getTotalCore());
                    summaryDTO.setUnit("核");
            }

            return summaryDTO;
        }
    }

}
