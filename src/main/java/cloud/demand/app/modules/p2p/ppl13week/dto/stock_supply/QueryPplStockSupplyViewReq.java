package cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply;


import java.util.List;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class QueryPplStockSupplyViewReq {


    // 查询的维度
    List<String> dimensions;

    // 预测版本
    @NotBlank(message = "预测版本不可以为空")
    String versionCode;

    /**
     * CVM,CBS
     */
    @NotBlank(message = "类型不可以为空")
    String type;
    /**
     * PPL明细ID
     */
    String pplId;
    // 开始年月   yyyyMM
    String beginYearMonth;
    // 结束年月       yyyyMM
    String endYearMonth;
    // 行业部门
    List<String> industryDept;
    // 客户简称
    List<String> customerShortName;
    // 战区
    List<String> warZone;

    // 国家
    List<String> countryName;

    // 地域
    List<String> regionName;

    // 可用区
    List<String> zoneName;

    // 实例类型
    List<String> instanceType;

    // 实例规则
    List<String> instanceModel;

    /**
     * 下面这个是 物理机的
     */
    List<String> deviceType;
    List<String> planProductName;


}
