package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.VersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyDBDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplStockSupplyDBDetailVO;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

/**
 *   裸金属对冲
 */
public interface PplDataBaseStockSupplyService {

    /**
     *   开始某个版本的数据库对冲
     * @param versionCode ppl13周版本
     */
    void startNewStockSupply(String versionCode);

    /** 查询某个版本是否已经启动对冲了 */
    boolean hasStartedStockSupply(String versionCode);

    /**
     *   导入裸金属对冲结果进行校验 <br/>
     *   校验通过后进行数据入库，并返回解析excel后得到的对冲数据; <br/>
     *   校验不通过，则返回校验结果和excel解析数据 <br/>
     * @param file 裸金属对冲结果excel
     * @param versionCode ppl13周版本
     */
    ReadResult<PplStockSupplyDBDetailVO> importStockSupplyAndCheck(MultipartFile file, String versionCode);

    /**
     *  versionCode 有值-导出对应最新的对冲结果，不能为空 <br/>
     *  industryDept 指定部门 , 为空查则不添加部门过滤条件<br/>
     */
    FileNameAndBytesDTO downloadStockSupply(VersionGroupReq req);

    /** 导出空白模版 */
    FileNameAndBytesDTO downloadDBStockSupplyTemplate();

    /**
     *   对冲完成
     * @param versionCode ppl13周版本
     */
    void finishStockSupply(String versionCode);

    /**
     *   是否已经对冲完成
     * @param versionCode ppl13周版本
     */
    boolean hasFinishStockSupply(String versionCode);

    /**
     *   查询指定版本分组内指定pplId的裸金属对冲结果
     * @param versionGroupId 版本分组id
     * @param pplIdList 需要查询的pplId
     */
    List<PplStockSupplyDBDetailDO> queryStockSupply(Long versionGroupId, List<String> pplIdList);

    /**
     *   导出版本裸金属需求明细
     * @param versionCode ppl13周版本
     */
    FileNameAndBytesDTO downloadDBDemandDetails(String versionCode);

}
