package cloud.demand.app.modules.p2p.ppl13week.service.impl;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDataBaseFrameworkEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDatabaseStorageEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDeloyTypeEnum;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.constant.PplIndustryPackageBaseDataConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.BaseDataWithPplDraftVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.QueryBaseDataReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.SupplementPplReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplIndustryPackageBaseDataDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplIndustryPackageBaseDataImportVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftQueryTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Product2ModelForecastConfigEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryPackageBaseDataService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplModelForecastForPackageService;
import cloud.demand.app.modules.p2p.ppl13week.vo.FillIndustryPplVO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySplitDetailForPplReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp.Detail;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13WeekRateViewService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.Model;
import net.sf.jsqlparser.util.validation.feature.DatabaseType;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplModelForecastForPackageServiceImpl implements PplModelForecastForPackageService {

    @Resource
    private Ppl13WeekRateViewService ppl13WeekRateViewService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private DictService dictService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private PplIndustryPackageBaseDataService pplIndustryPackageBaseDataService;

    @Resource
    private PplInnerVersionService pplInnerVersionService;

    @Resource
    private PplDraftService pplDraftService;

    final static Integer cvmThresholdCore = 8;


    @Override
    @Transactional("demandTransactionManager")
    public List<PplIndustryPackageBaseDataDO> initForecastDataForPackage(Long versionId,String product) {

        PplInnerProcessVersionDO versionDO = pplInnerVersionService.getByVersionId(versionId);
        if (versionDO == null) {
            throw BizException.makeThrow("审批版本【%s】不存在，请刷新后重试", versionId);
        }
        QuerySplitDetailForPplReq req = new QuerySplitDetailForPplReq();
        Product2ModelForecastConfigEnum modelForecastConfigEnum = Product2ModelForecastConfigEnum.getByName(product);
        if (modelForecastConfigEnum == null){
            throw new BizException(String.format("产品【%s】不支持模型预测", product));
        }
        req.setProduct(modelForecastConfigEnum.getCode());
        QuerySplitDetailForPplResp querySplitDetailForPplResp = ppl13WeekRateViewService.querySplitDetailForPpl(req);

        if (querySplitDetailForPplResp == null || ListUtils.isEmpty(querySplitDetailForPplResp.getItems())) {
            return new ArrayList<>();
        }
        Map<String, String> instanceTypeToCommonInstanceType = pplDictService.queryInstanceType2CommonInstanceType();
        List<String> overseasRegin = pplDictService.queryAllRegionName(true);
        Map<String, String> regionNameMap = dictService.getRegionNameMap();

        Function<Object, String> groupFunc = Product2ModelForecastConfigEnum.getGroupKeyByProduct(product,
                instanceTypeToCommonInstanceType);

        Map<String, List<Detail>> group = querySplitDetailForPplResp.getItems().stream()
                .filter(v -> v.getCoreNum().compareTo(BigDecimal.ZERO) != 0)
                // 如果非数据库需求， 机型必须要在中长尾机型配置表里有。
                .filter(v -> (modelForecastConfigEnum.getWarZone().equals("数据库（内部需求）") ||
                        instanceTypeToCommonInstanceType.get(v.getInstanceType()) != null))
                .collect(Collectors.groupingBy(groupFunc));

        // 处理历史版本基准数据
        String updateSql = "update ppl_industry_package_base_data set latest = 0 "
                + " where inner_version_id = ? and latest = 1 and product = ? and deleted = 0";
        demandDBHelper.executeRaw(updateSql, versionDO.getId(), product);

        // 先插入导入版本
        PplIndustryPackageBaseDataImportVersionDO importVersion = new PplIndustryPackageBaseDataImportVersionDO();
        importVersion.setImportUser(LoginUtils.getUserNameWithSystem());
        importVersion.setInnerVersionId(versionDO.getId());
        importVersion.setIndustryDept(versionDO.getIndustryDept());
        importVersion.setProduct(product);
        demandDBHelper.insert(importVersion);

        List<PplIndustryPackageBaseDataDO> packageList = new ArrayList<>();
        group.forEach((k,v) -> {
            Detail detail = v.get(0);
            // 过滤掉不再范围内的年月
            if (isNotInVersionDemandRange(detail,versionDO,overseasRegin)) {
                return;
            }
            PplIndustryPackageBaseDataDO baseDataDO = new PplIndustryPackageBaseDataDO();
            baseDataDO.setDemandType("RET".equals(detail.getType()) ? PplDemandTypeEnum.RETURN.getCode() : PplDemandTypeEnum.NEW.getCode());
            baseDataDO.setDemandTypeName(PplDemandTypeEnum.getName2ByCode(baseDataDO.getDemandType()));

            BigDecimal originalBaseLine = v.stream().map(o -> o.getCoreNum().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            // 如果是Mysql需要做一个除2的特殊处理 （双节点）
            originalBaseLine = product.equals(Product2ModelForecastConfigEnum.CDB.getName()) ?
                    originalBaseLine.divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP) : originalBaseLine;

            // 不同产品 - 不同计算基准标准
            BigDecimal baseLine = calculateBaseLineByProduct(originalBaseLine, product,detail.getInstanceModel());

            baseDataDO.setRegionName(detail.getRegionName());
            baseDataDO.setRegion(regionNameMap.get(detail.getRegionName()));
            baseDataDO.setCustomhouseTitle(overseasRegin.contains(detail.getRegionName()) ? "境外" : "境内");
            baseDataDO.setCommonInstanceType(
                    instanceTypeToCommonInstanceType.getOrDefault(detail.getInstanceType(), detail.getInstanceType()));
            baseDataDO.setDemandYearMonth(DateUtils.getYearMonthStr(detail.getYear(), detail.getMonth()));
            baseDataDO.setCreateUser(LoginUtils.getUserNameWithSystem());
            baseDataDO.setImportVersionId(importVersion.getId());
            baseDataDO.setLatest(true);
            baseDataDO.setIndustryDept(versionDO.getIndustryDept());
            baseDataDO.setProduct(product);
            baseDataDO.setInnerVersionId(versionDO.getId());
            baseDataDO.setOriginalBaseCore(originalBaseLine);
            baseDataDO.setBaseCore(baseLine);

            packageList.add(baseDataDO);
        });
        fillInstanceModelByProduct(packageList,product);
        demandDBHelper.insertBatchWithoutReturnId(packageList);
        return packageList;
    }

    /**
     * 填充实例规格 通过产品
     * @param baseDataList
     * @param product
     */
    public void fillInstanceModelByProduct(List<PplIndustryPackageBaseDataDO> baseDataList,String product){
        if (Product2ModelForecastConfigEnum.CDB.getName().equals(product)){
            for (PplIndustryPackageBaseDataDO pplIndustryPackageBaseDataDO : baseDataList) {
                pplIndustryPackageBaseDataDO.setPossibleInstanceModel(pplIndustryPackageBaseDataDO.getCommonInstanceType());
            }
        }else {
            pplIndustryPackageBaseDataService.fillInstanceModel(baseDataList,
                    cvmThresholdCore,cvmThresholdCore*2);
        }
    }

    /**
     * 计算基准 通过产品
     * @param originalBaseLine
     * @param product
     */
    public BigDecimal calculateBaseLineByProduct(BigDecimal originalBaseLine,String product,String instanceModel){
        if (originalBaseLine.compareTo(BigDecimal.ZERO) == 0){
            return originalBaseLine;
        }
        BigDecimal baseLine = BigDecimal.ZERO;
        if (Product2ModelForecastConfigEnum.CDB.getName().equals(product)){
            Integer memoryThreshold;
            if (PplIndustryPackageBaseDataConstant.CDB_SMALL_INSTANCE.equals(instanceModel)) {
                memoryThreshold = 64;
            } else if (PplIndustryPackageBaseDataConstant.CDB_BIG_INSTANCE.equals(instanceModel)){
                memoryThreshold = 128;
            }else {
                throw new BizException(String.format("CDB不支持的实例规格【%s】", instanceModel));
            }

            if(originalBaseLine.compareTo(BigDecimal.valueOf(memoryThreshold/2)) > 0){
                // 如果聚合后内存大于 阈值的一半, 才进行处理。
                Integer fillInstanceNum = originalBaseLine.intValue() / memoryThreshold
                        + (originalBaseLine.intValue() % memoryThreshold > memoryThreshold/2 ? 1 : 0);
                baseLine = BigDecimal.valueOf(fillInstanceNum * memoryThreshold);
            }
        }else {
            if(originalBaseLine.compareTo(BigDecimal.valueOf(cvmThresholdCore/2)) > 0){
                // 如果聚合后核心数大于 阈值的一半, 才进行处理。
                Integer fillInstanceNum = originalBaseLine.intValue() / cvmThresholdCore
                        + (originalBaseLine.intValue() % cvmThresholdCore > cvmThresholdCore/2 ? 1 : 0);
                baseLine = BigDecimal.valueOf(fillInstanceNum * cvmThresholdCore);
            }
        }

        return baseLine;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void supplementPpl(SupplementPplReq req) {
        PplInnerProcessVersionDO innerVersion = pplInnerVersionService.getByVersionId(req.getVersionId());
        if (innerVersion == null) {
            throw BizException.makeThrow("版本不存在");
        }
        if (!PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(innerVersion.getStatus())) {
            throw BizException.makeThrow("仅审批中版本可进行 PPL模型预测一键补充");
        }
        PplOrderAuditRecordDO recordDO = demandDBHelper.getOne(PplOrderAuditRecordDO.class, "where version_id = ?",
                innerVersion.getId());
        if (recordDO != null) {
            throw BizException.makeThrow("仅需求录入沟通环节可进行 PPL模型预测一键补充");
        }
        // 获取包基准数据
        QueryBaseDataReq queryBaseDataReq = new QueryBaseDataReq();
        queryBaseDataReq.setVersionId(innerVersion.getId());
        queryBaseDataReq.setBaseDataIds(req.getPackageBaseDataIdList());
        queryBaseDataReq.setProduct(req.getProduct());
        YearMonth begin =  YearMonth.of(innerVersion.getDemandBeginYear(),innerVersion.getDemandBeginMonth());
        YearMonth end =  YearMonth.of(innerVersion.getDemandEndYear(),innerVersion.getDemandEndMonth());
        queryBaseDataReq.setBeginYearMonth(begin);
        queryBaseDataReq.setEndYearMonth(end);
        List<BaseDataWithPplDraftVO> raw = queryPackageBaseDataForDraft(queryBaseDataReq);
        if (ListUtils.isEmpty(raw)){
            throw BizException.makeThrow("模型基准数据为空，仅允许在版本录入年月范围内【%s~%s】进行一键补充", begin, end);
        }
        if (raw.size() != req.getPackageBaseDataIdList().size()){
            throw BizException.makeThrow("模型基准数据不全，仅允许在版本录入年月范围内【%s~%s】进行一键补充", begin, end);
        }

        // 构建数据处理对象
        FillIndustryPplVO waitHandlerPplDraft = createWaitHandlerPplDraft(raw, req, innerVersion);

        if(ListUtils.isNotEmpty(waitHandlerPplDraft.getInsertList())){
            pplDraftService.batchSaveDraft(waitHandlerPplDraft.getInsertList(),
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), true,
                    null,null,null);
        }
        if (ListUtils.isNotEmpty(waitHandlerPplDraft.getDeletePplOrders())){
            pplDraftService.deletePplDraftForPreSubmit(waitHandlerPplDraft.getDeletePplOrders(),"PPL模型预测一键干预");
        }

    }

    public List<BaseDataWithPplDraftVO> queryPackageBaseDataForDraft(QueryBaseDataReq req) {

        PplInnerProcessVersionDO innerVersion = pplInnerVersionService.getByVersionId(req.getVersionId());
        if (innerVersion == null) {
            throw BizException.makeThrow("审批版本【%s】不存在，请刷新后重试", req.getVersionId());
        }
        // 获取包基准数据
        String sql = ORMUtils.getSql("/sql/ppl13week/package_base_data/query_base_data.sql");

        WhereSQL where = req.toWhere();

        sql = sql + where.getSQL();
        List<BaseDataWithPplDraftVO> baseDataList = demandDBHelper.getRaw(BaseDataWithPplDraftVO.class, sql, where.getParams());
        if (req.isOnlyBaseData()) {
            return baseDataList;
        }
        if (ListUtils.isEmpty(baseDataList) && ListUtils.isNotEmpty(req.getBaseDataIds())) {
            return baseDataList;
        }

        // 查询草稿中的数据
        QueryPplDraftReq draftReq = new QueryPplDraftReq();
        draftReq.setVersionId(req.getVersionId());
        if (req.getProduct().equals(Product2ModelForecastConfigEnum.CDB.getName())){
            draftReq.setProduct(Arrays.asList(Ppl13weekProductTypeEnum.DATABASE.getName()));
            draftReq.setDatabaseName(Arrays.asList(req.getProduct()));
        }else {
            draftReq.setProduct(Arrays.asList(req.getProduct()));
        }
        draftReq.setIndustryDept(innerVersion.getIndustryDept());
        draftReq.setStatus(PplOrderDraftQueryTypeEnum.PRE_SUBMIT.getCode());
        List<PplListVo> pplListVos = pplDraftService.queryCurrentVersionData(draftReq);

        Map<String, String> instanceType2CommonInstanceTypeMap = pplDictService.instanceType2CommonInstanceTypeMap();
        Map<String, String> regionName2CodeMap = dictService.getRegionNameMap();
        Map<String, String> regionName2CustomhouseTitleMap = dictService.getRegion2CustomhouseTitleMap();
        BaseDataWithPplDraftVO.baseDataAddPpl(pplListVos, instanceType2CommonInstanceTypeMap, baseDataList,
                regionName2CodeMap, regionName2CustomhouseTitleMap, req);

        return baseDataList;
    }

    @Override
    public void RefreshModelPackageOnStartVersion(Long versionId) {
        PplInnerProcessVersionDO versionDO = pplInnerVersionService.getByVersionId(versionId);
        try {
            for (Product2ModelForecastConfigEnum product : Product2ModelForecastConfigEnum.values()) {
                if (versionDO.getProduct().contains(product.getName())){
                    initForecastDataForPackage(versionId, product.getName());
                }
            }
            // 数据库-mysql单独处理
            if (versionDO.getProduct().contains(Ppl13weekProductTypeEnum.DATABASE.getName())){
                initForecastDataForPackage(versionId, Product2ModelForecastConfigEnum.CDB.getName());
            }
        } catch (Exception e) {
            String msg = StrUtil.format("新版本ID【{}】，刷新模型预测包基准数据失败，异常信息：{}",
                    versionId , ExceptionUtil.getMessage(e));
            AlarmRobotUtil.doAlarm("RefreshModelPackageOnStartVersion", msg, null, false);
        }
    }


    @Override
    public void fillZeroBaseData(Long versionId) {
        PplInnerProcessVersionDO versionDO = pplInnerVersionService.getByVersionId(versionId);
        if (!IndustryDeptEnum.INNER_DEPT.getName().equals(versionDO.getIndustryDept())){
            return;
        }
        List<PplIndustryPackageBaseDataDO> zeroBaseDataList = new ArrayList<>();
        for (Product2ModelForecastConfigEnum product : Product2ModelForecastConfigEnum.values()) {
            if (versionDO.getProduct().contains(product.getName())){
                zeroBaseDataList.addAll(fillZeroBaseDataByProduct(versionDO,product));
            }
        }
        // 数据库-mysql单独处理
        if (versionDO.getProduct().contains(Ppl13weekProductTypeEnum.DATABASE.getName())){
            zeroBaseDataList.addAll(fillZeroBaseDataByProduct(versionDO,Product2ModelForecastConfigEnum.CDB));
        }
        if (ListUtils.isNotEmpty(zeroBaseDataList)){
            demandDBHelper.insertBatchWithoutReturnId(zeroBaseDataList);
        }
    }


    private List<PplIndustryPackageBaseDataDO> fillZeroBaseDataByProduct(
            PplInnerProcessVersionDO versionDO,Product2ModelForecastConfigEnum product) {
        Long versionId = versionDO.getId();
        PplIndustryPackageBaseDataDO one = demandDBHelper.getOne(PplIndustryPackageBaseDataDO.class,
                "where inner_version_id = ? and latest = 1"
                        + " and product = ?", versionId, product.getName());

        QueryBaseDataReq queryBaseDataReq = new QueryBaseDataReq();
        queryBaseDataReq.setVersionId(versionId);
        queryBaseDataReq.setProduct(product.getName());
        queryBaseDataReq.setBeginYearMonth(YearMonth.of(versionDO.getDemandBeginYear(),versionDO.getDemandBeginMonth()));
        queryBaseDataReq.setEndYearMonth(YearMonth.of(versionDO.getDemandEndYear(),versionDO.getDemandEndMonth()));
        List<BaseDataWithPplDraftVO> baseDataWithPplDraftVOS = queryPackageBaseDataForDraft(queryBaseDataReq);

        if (one == null || ListUtils.isEmpty(baseDataWithPplDraftVOS)){
            return new ArrayList<>();
        }

        List<PplIndustryPackageBaseDataDO> zeroBaseDataList = new ArrayList<>();

        Long importVersionId = one.getImportVersionId();

        List<BaseDataWithPplDraftVO> needFillList = baseDataWithPplDraftVOS.stream().filter(o -> o.getId() == null)
                .collect(Collectors.toList());

        for (BaseDataWithPplDraftVO source : needFillList) {
            PplIndustryPackageBaseDataDO target = new PplIndustryPackageBaseDataDO();
            target.setDemandTypeName(source.getDemandTypeName());
            target.setDemandType(source.getDemandType());
            target.setRegionName(source.getRegionName());
            target.setRegion(source.getRegion());
            target.setCustomhouseTitle(source.getCustomhouseTitle());
            target.setCommonInstanceType(source.getCommonInstanceType());
            target.setDemandYearMonth(source.getDemandYearMonth());
            // 补充0的包基准 用开发的rtx
            target.setCreateUser("oliverychen");
            target.setImportVersionId(importVersionId);
            target.setLatest(true);
            target.setIndustryDept(versionDO.getIndustryDept());
            target.setProduct(product.getName());
            target.setInnerVersionId(versionDO.getId());
            target.setOriginalBaseCore(BigDecimal.ZERO);
            target.setBaseCore(BigDecimal.ZERO);
            zeroBaseDataList.add(target);
        }
        return zeroBaseDataList;
    }



    public boolean isNotInVersionDemandRange(Detail detail,PplInnerProcessVersionDO versionDO,List<String> overseasRegin){
        int demandYear = detail.getYear().intValue();
        int demandMonth = detail.getMonth().intValue();
        if (demandYear < versionDO.getDemandBeginYear() ||
                (demandYear == versionDO.getDemandBeginYear() && demandMonth < versionDO.getDemandBeginMonth())
                || demandYear > versionDO.getDemandEndYear() ||
                (demandYear == versionDO.getDemandEndYear() && demandMonth > versionDO.getDemandEndMonth())) {
            return true;
        }

        if (detail.getCoreNum().compareTo(BigDecimal.ZERO) >= 0
                && overseasRegin.contains(detail.getRegionName())
                && !versionDO.isSatisfyOverseasYearMonth(detail.getBeginBuyDate())){
            return true;
        }
        return false;
    }

    /**
     * 一键补充PPL数据生成
     */
    public FillIndustryPplVO createWaitHandlerPplDraft(List<BaseDataWithPplDraftVO> raws, SupplementPplReq req,
            PplInnerProcessVersionDO innerVersion) {

        FillIndustryPplVO fillIndustryPplVO = new FillIndustryPplVO();

        // 仅保留需要补充的数据
        raws = raws.stream()
                .filter(BaseDataWithPplDraftVO::getIsAllowSupplement).collect(Collectors.toList());
        if (ListUtils.isEmpty(raws)){
            return fillIndustryPplVO;
        }

        String userName = LoginUtils.getUserNameWithSystem();

        Product2ModelForecastConfigEnum modelForecastConfigEnum = Product2ModelForecastConfigEnum.getByName(req.getProduct());
        // 补充PPL
        List<SavePplDraftReq> insertList = new ArrayList<>();
        // 删除原有的补充PPL
        List<String> deletePplOrderList = new ArrayList<>();
        for (BaseDataWithPplDraftVO item : raws) {
            if (item.getShouldAdjustTarget().compareTo(BigDecimal.ZERO) == 0) {
                // 如果需调整量 == 0 ，则不进行补充
                continue;
            }

            // baseline > 0 才进行新增补充
            BigDecimal baseline = item.getBaseCore().subtract(item.getPplTarget());
            if (baseline.compareTo(BigDecimal.ZERO) > 0) {
                SavePplDraftReq save = new SavePplDraftReq();

                save.setType(OperateTypeEnum.INSERT.getCode());
                save.setProduct(req.getProduct());
                save.setWarZone(modelForecastConfigEnum.getWarZone());
                save.setVersionId(innerVersion.getId());
                String demandScene = PplDemandTypeEnum.RETURN.getCode().equals(item.getDemandType()) ? "自然退回" : "自然增长";
                save.setDemandScene(demandScene);
                YearMonth  yearMonth = YearMonth.parse(item.getDemandYearMonth());
                LocalDate beginDate = yearMonth.atDay(15);
                LocalDate endDate = yearMonth.atDay(22);
                save.setBeginBuyDate(beginDate.toString());
                save.setEndBuyDate(endDate.toString());
                save.setDemandType(item.getDemandType());

                save.setIndustryDept(innerVersion.getIndustryDept());
                save.setIndustry(innerVersion.getIndustryDept());

                save.setCustomerUin(modelForecastConfigEnum.getUin());
                save.setCustomerType(StringUtils.isNotBlank(save.getCustomerUin()) ? CustomerTypeEnum.EXISTING.getCode() :
                        CustomerTypeEnum.WIN_BACK.getCode());
                save.setCustomerShortName(modelForecastConfigEnum.getCustomerShortName());
                save.setCustomerName(modelForecastConfigEnum.getCustomerShortName());
                save.setProjectName(modelForecastConfigEnum.getProjectName());
                save.setSubmitUser(userName);

                List<DraftItemDTO> resources = new ArrayList<>();
                DraftItemDTO ppl = new DraftItemDTO();
                ppl.setProduct(req.getProduct());
                ppl.setStatus(PplItemStatusEnum.VALID.getCode());
                ppl.setDemandType(item.getDemandType());
                ppl.setDemandScene(demandScene);
                ppl.setProjectName(modelForecastConfigEnum.getProjectName());
                ppl.setBillType(Ppl13weekForecastBillTypeEnum.MONTHLY_SUBS.getName());
                ppl.setWinRate(BigDecimal.valueOf(10));
                ppl.setBeginBuyDate(beginDate.toString());
                ppl.setEndBuyDate(endDate.toString());
                ppl.setRegionName(item.getRegionName());
                ppl.setZoneName(PplIndustryPackageBaseDataConstant.SUPPLEMENT_ZONE_NAME);
                ppl.setIsStrongDesignateZone(false);

                // 数据库字段
                if (req.getProduct().equals(Product2ModelForecastConfigEnum.CDB.getName())){
                    // 覆盖上面的product字段
                    ppl.setProduct(Ppl13weekProductTypeEnum.DATABASE.getName());
                    ppl.setDatabaseName(req.getProduct());
                    ppl.setMoreThanOneAZ(Boolean.TRUE);
                    ppl.setDeployType(PplDeloyTypeEnum.UNIVERSAL.getName());
                    ppl.setFrameworkType(PplDataBaseFrameworkEnum.CDB_DOUBLE.getName());
                    ppl.setSliceNum(1);
                    ppl.setReplicaNum(1);
                    ppl.setReadOnlyNum(0);
                    ppl.setDatabaseStorageType(PplDatabaseStorageEnum.UNIVERSAL.getName());
                    ppl.setDatabaseSpecs(item.getCommonInstanceType());
                    ppl.setDatabaseStorage(BigDecimal.ZERO);
                    ppl.setInstanceModelCoreNum(Product2ModelForecastConfigEnum.parseDbSpecsCore(ppl.getDatabaseSpecs()));
                    ppl.setInstanceModelRamNum(Product2ModelForecastConfigEnum.parseDbSpecsMemory(ppl.getDatabaseSpecs()));

                    Integer fillInstanceNum = baseline.intValue() / ppl.getInstanceModelRamNum()
                            + (baseline.intValue() % ppl.getInstanceModelRamNum() > 0 ? 1 : 0);

                    ppl.setInstanceNum(fillInstanceNum);
                    ppl.setTotalDatabaseStorage(BigDecimal.valueOf(fillInstanceNum * ppl.getDatabaseStorage().intValue()));

                    ppl.setTotalMemory(fillInstanceNum * ppl.getInstanceModelRamNum());
                    ppl.setTotalCoreNum(fillInstanceNum * ppl.getInstanceModelCoreNum());
                } else {
                    // CVM 相关字段
                    Tuple2<Integer, Integer> infos = P2PInstanceModelParse.parseInstanceModel(item.getPossibleInstanceModel());
                    Integer parseCore = infos._1;
                    Integer parseRam = infos._2;

                    Integer fillInstanceNum = baseline.intValue() / parseCore
                            + (baseline.intValue() % parseCore > 0 ? 1 : 0);

                    ppl.setInstanceModel(item.getPossibleInstanceModel());
                    ppl.setInstanceType(item.getPossibleInstanceModel().split("\\.")[0]);
                    ppl.setInstanceNum(fillInstanceNum);
                    ppl.setTotalCoreNum(fillInstanceNum * parseCore);
                    ppl.setTotalDiskNum(0);
                    ppl.setInstanceModelCoreNum(parseCore);
                    ppl.setInstanceModelRamNum(parseRam);
                }

                resources.add(ppl);
                save.setResources(resources);
                insertList.add(save);
            }


            // 无论 baseline 是什么情况都将原有的删除
            for (PplListVo pplListVo : item.getOrderMap().values()) {
                List<DraftItemDTO> items = item.getPplMap().get(pplListVo.getPplOrder());
                DraftItemDTO itemVO = items.get(0);
                if (IndustryDeptEnum.INNER_DEPT.getName().equals(pplListVo.getIndustryDept())) {
                    if ((!PplIndustryPackageBaseDataConstant.MODEL_FORECAST_PROJECT_LIST.contains(
                            Optional.ofNullable(pplListVo.getProjectName()).orElse("")))
                            || (itemVO != null && PplItemStatusEnum.APPLIED.getCode().equals(itemVO.getStatus()))){
                        // 如果项目名称不等于 模型预测-EMR / 模型预测EKS 或者 已经转单了的单据， 不进行处理。
                        continue;
                    }
                }else {
                    if (!Objects.equals(PplIndustryPackageBaseDataConstant.SUPPLEMENT_CUSTOMER, pplListVo.getCustomerShortName())
                            || (itemVO != null && PplItemStatusEnum.APPLIED.getCode().equals(itemVO.getStatus()))) {
                        // 如果客户名称不等于 行业专项包预测 或者 已经转单了的单据， 不进行处理。
                        continue;
                    }
                }

                // 历史包预测生成的PPL 调整为 0
                if (ListUtils.isNotEmpty(items)) {
                    deletePplOrderList.add(pplListVo.getPplOrder());
                }
            }
        }
        fillIndustryPplVO.setInsertList(insertList);
        fillIndustryPplVO.setDeletePplOrders(deletePplOrderList);
        return fillIndustryPplVO;

    }
}
