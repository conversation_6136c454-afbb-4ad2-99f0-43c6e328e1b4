package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 剔除的毛刺明细，最新版
 */
@Data
@ToString
@Table("ppl_forecast_input_excluded_spike_latest")
public class PplForecastInputExcludedSpikeLatestDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 是否内部客户，1是<br/>Column: [is_inner] */
    @Column(value = "is_inner")
    private Boolean isInner;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /** 对应的任务id<br/>Column: [task_id] */
    @Column(value = "task_id")
    private Long taskId;

    /** 毛刺的年<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 毛刺的月<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 客户简称，简称不一定有<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 机型大类<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 新增核心数<br/>Column: [new_core] */
    @Column(value = "new_core")
    private BigDecimal newCore;

    /** 退回核心数<br/>Column: [ret_core] */
    @Column(value = "ret_core")
    private BigDecimal retCore;

}