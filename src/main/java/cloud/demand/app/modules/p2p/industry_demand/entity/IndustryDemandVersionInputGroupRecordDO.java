package cloud.demand.app.modules.p2p.industry_demand.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 * 录入动态分组过程log
 */
@Data
@ToString
@Table("industry_demand_version_input_group_record")
public class IndustryDemandVersionInputGroupRecordDO extends BaseDO {

    /** 录入分组id<br/>Column: [input_group_id] */
    @Column(value = "input_group_id")
    private Long inputGroupId;

    /** 当前状态<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 审批人<br/>Column: [approver] */
    @Column(value = "approver")
    private String approver;

    /** 处理备注<br/>Column: [approve_msg] */
    @Column(value = "approve_msg")
    private String approveMsg;

    /** 处理结果<br/>Column: [action] */
    @Column(value = "action")
    private String action;

}