package cloud.demand.app.modules.p2p.longterm.dto;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.longterm.entity.vo.LongtermVersionGroupRecordItemVO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemSourceTypeEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.YearMonth;

@Data
@ToString
public class LongtermPpl13weekDataDTO {

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "biz_group")
    private String bizGroup;

    @Column(value = "product1")
    private String product;

    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    @Column(value = "is_top_customer")
    private Integer isTopCustomer;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name1")
    private String zoneName;

    @Column(value = "instance_family")
    private String instanceFamily;

    @Column(value = "instance_type1")
    private String instanceType;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "gpu_type1")
    private String gpuType;

    @Column(value = "paas_product")
    private String paasProduct;

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "total_core1")
    private BigDecimal totalCore;

    @Column(value = "total_gpu_num1")
    private BigDecimal totalGpuNum;

    @Column(value = "ppl_ids")
    private String pplIds;

    public Integer getQuarter() {
        return (this.month + 2) / 3;
    }

    public static LongtermVersionGroupRecordItemVO toItem(LongtermPpl13weekDataDTO dto, YearMonth ppl13weekStartYearMonth) {
        LongtermVersionGroupRecordItemVO itemDO = new LongtermVersionGroupRecordItemVO();
        itemDO.setProduct(dto.getProduct());
        itemDO.setCustomerShortName(dto.getCommonCustomerShortName());
        itemDO.setIsTopCustomer(dto.getIsTopCustomer());
        itemDO.setCustomhouseTitle(dto.getCustomhouseTitle());
        itemDO.setRegionName(dto.getRegionName());
        itemDO.setZoneName(dto.getZoneName());
        itemDO.setInstanceFamily(dto.getInstanceFamily());
        itemDO.setInstanceType(dto.getInstanceType());
        if (ppl13weekStartYearMonth.isAfter(YearMonth.of(dto.getYear(), dto.getMonth()))) {
            itemDO.setSourceType(LongtermVersionGroupItemSourceTypeEnum.ORDER.getCode());
        } else {
            itemDO.setSourceType(LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.getCode());
        }
        itemDO.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode());
        itemDO.setDemandType(dto.getDemandType());
        itemDO.setDemandYear(dto.getYear());
        itemDO.setDemandQuarter((dto.getMonth() + 2) / 3);
        itemDO.setDemandMonth(dto.getMonth());
        itemDO.setCoreNum(dto.getTotalCore() == null ? BigDecimal.ZERO : dto.getTotalCore());
        itemDO.setGpuNum(dto.getTotalGpuNum() == null ? BigDecimal.ZERO : dto.getTotalGpuNum());
        itemDO.setGpuType(dto.getGpuType());
        itemDO.setPaasProduct(dto.getPaasProduct());
        itemDO.setNote("");
        itemDO.setIndustryDept(dto.getIndustryDept());
        itemDO.setBizGroup(dto.getBizGroup());
        itemDO.setAutomateNote("来自PPL:" + dto.getPplIds());
        return itemDO;
    }


    public static LongtermVersionGroupRecordItemDO toDO(LongtermPpl13weekDataDTO dto,
                                                        LongtermVersionGroupRecordDO recordDO,
                                                        boolean isComdIntervened, YearMonth ppl13weekStartYearMonth) {
        LongtermVersionGroupRecordItemDO itemDO = new LongtermVersionGroupRecordItemDO();
        itemDO.setVersionCode(recordDO.getVersionCode());
        itemDO.setVersionGroupId(recordDO.getVersionGroupId());
        itemDO.setVersionGroupRecordId(recordDO.getId());
        itemDO.setProduct(dto.getProduct());
        itemDO.setCustomerShortName(dto.getCommonCustomerShortName());
        itemDO.setIsTopCustomer(dto.getIsTopCustomer());
        itemDO.setCustomhouseTitle(dto.getCustomhouseTitle());
        itemDO.setRegionName(dto.getRegionName());
        itemDO.setZoneName(dto.getZoneName());
        itemDO.setInstanceFamily(dto.getInstanceFamily());
        itemDO.setInstanceType(dto.getInstanceType());
        if (ppl13weekStartYearMonth.isAfter(YearMonth.of(dto.getYear(), dto.getMonth()))) {
            itemDO.setSourceType(LongtermVersionGroupItemSourceTypeEnum.ORDER.getCode());
        } else {
            itemDO.setSourceType(LongtermVersionGroupItemSourceTypeEnum.PPL_13_WEEK.getCode());
        }
        itemDO.setDemandType(dto.getDemandType());
        itemDO.setDemandYear(dto.getYear());
        itemDO.setDemandQuarter((dto.getMonth() + 2) / 3);
        itemDO.setDemandMonth(dto.getMonth());
        itemDO.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.MONTH.getCode());
        itemDO.setCoreNum(dto.getTotalCore() == null ? BigDecimal.ZERO : dto.getTotalCore());
        itemDO.setGpuNum(dto.getTotalGpuNum() == null ? BigDecimal.ZERO : dto.getTotalGpuNum());
        itemDO.setGpuType(dto.getGpuType());
        itemDO.setPaasProduct(dto.getPaasProduct());
        itemDO.setNote("");
        itemDO.setAutomateNote("来自PPL(" + (isComdIntervened ? "干预后" : "干预前") + "):" + dto.getPplIds());
        return itemDO;
    }

}
