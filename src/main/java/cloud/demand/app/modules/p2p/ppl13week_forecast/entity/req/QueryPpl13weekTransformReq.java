package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req;


import java.util.List;
import lombok.Data;
import org.springframework.http.ResponseEntity;

/**
 * <AUTHOR>
 */
@Data
public class QueryPpl13weekTransformReq {

    /**
     * outputVersionIds
     */
    private List<Long> outputVersionIds;

    private Boolean isAppend;

    private Boolean isLimitByDate;


    private List<Long> taskIds;

    /**
     * yyyy-MM
     * 开始预测日期
     */
    private String startYearMonth;

    private String endYearMonth;


    private String versionCode;


    private String desc;


}
