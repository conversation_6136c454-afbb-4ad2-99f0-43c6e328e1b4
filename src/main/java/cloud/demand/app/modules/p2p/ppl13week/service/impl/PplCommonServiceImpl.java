package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.operation_view.inventory_health.entity.InventoryHealthMainZoneNameConfigDO;
import cloud.demand.app.modules.operation_view.inventory_health.enums.InventoryHealthZoneType;
import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionDto;
import cloud.demand.app.modules.p2p.industry_demand.dto.UserPermissionReq;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.ppl13week.dto.ArchitectPermission;
import cloud.demand.app.modules.p2p.ppl13week.dto.ProductGpuConfigDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.auth.QueryArchitectAuthResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.mck_data.CustomerForecastDataDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.mck_data.LongtailModelTaskDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.mck_data.NewOrderDataDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.mck_data.YunxiaoOrderDataDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.other.PplToDoLogDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.InstanceTypeRelateDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpCommonHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.DimArchitectUinRelationDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.HeadCustomer;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigUinTypeDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderSumDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PublicIndustryInstanceModelEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.W13CustomerDataDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.W13DataDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.AdsMckForecastInOutDfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.AdsMckForecastSummaryDfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.report_proxy.anno.TaskRunSql;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil;
import cloud.demand.app.modules.tencent_cloud_utils.CommonUtil;
import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.client.JsonrpcReq;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class PplCommonServiceImpl implements PplCommonService {

    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper gpuDBHelper;
    @Resource
    OrderCommonService orderCommonService;
    @Resource
    private DBHelper resplanDBHelper;
    @Resource
    private RedisHelper redisHelper;
    @Autowired
    JsonrpcClient jsonrpcClient;
    @Resource
    private TodoService todoService;
    @Resource
    PermissionService permissionService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private Alert alert;
    @Resource
    private SoeCommonService soeCommonService;

    @Resource
    private MrpV2DictService mrpV2DictService;

    Supplier<String> todoUrlSupplier = DynamicProperty.create("app.url.todo-url", "");


    @Override
    @HiSpeedCache(expireSecond = 3600)
    public List<IndustryReportAppidInfoLatestWithoutJsonDO> getInnerCustomer() {
        return demandDBHelper.getAll(IndustryReportAppidInfoLatestWithoutJsonDO.class,
                "WHERE uin IN (\n" +
                        "SELECT uin FROM `ppl_forecast_config_customer_defines_latest` WHERE source_type = 'INNER'\n" +
                        ")");
    }

    /**
     * P          N                221019         4839            -001
     *
     * 固定前缀    N:Normal正常单     PPL创建年月   PPL order id    PPL明细自增ID
     * 代表PPL    E:Emergency紧急单   日YYMMDD
     * type                            id
     *
     * @param type
     * @return
     */
    @Override
    public String generatePplOrderId(String type) {
        Long i = redisHelper.getAutoIncrementId("ppl-order-" + DateUtils.format(new Date(), "yyyy-MM-dd"));
        if (i < 10000) {
            return String.format("P%s%s%04d", type, DateUtils.format(new Date(), "yyMMdd"), i);
        } else {
            return String.format("P%s%s%08d", type, DateUtils.format(new Date(), "yyMMdd"), i);
        }
    }

    @Override
    public List<String> generatePplOrderId(String type, int batchNum) {
        String namespace = "ppl-order-" + DateUtils.format(new Date(), "yyyy-MM-dd");
        String key = namespace + "_ID";
        Long id = redisHelper.execute(jedis -> jedis.incrBy(key, batchNum));
        if (id == null) {
            return null;
        }

        List<String> result = new ArrayList<>();
        for (long i = id - batchNum + 1; i <= id; i++) {
            if (i < 10000) {
                result.add(String.format("P%s%s%04d", type, DateUtils.format(new Date(), "yyMMdd"), i));
            } else {
                result.add(String.format("P%s%s%08d", type, DateUtils.format(new Date(), "yyMMdd"), i));
            }
        }
        return result;
    }

    @Override
    public String generatePplItemId(String pplOrderId) {
        Long autoIncrementId = redisHelper.getAutoIncrementId("ppl-generatePplOrderId-" + pplOrderId);
        return String.format("%s-%03d", pplOrderId, autoIncrementId);
    }

    @Override
    public Integer getOrderRecordVersion() {
        return redisHelper.getAutoIncrementId("ppl-getOrderRecordVersion").intValue() + 1_0000;
    }

    @Override
    public Integer getOrderRecordBatchNumber() {
        return redisHelper.getAutoIncrementId("ppl-getOrderRecordBatchNumber").intValue() + 10_0000;
    }

    @Override
    public Integer getVersionGroupRecordVersion() {
        return redisHelper.getAutoIncrementId("ppl-getVersionGroupRecordVersion").intValue() + 1_000;
    }

    @Override
    public List<cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO> getAuthByUsername(
            String userName, IndustryDemandAuthRoleEnum role) {

        return demandDBHelper.getAll(cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO.class,
                "where user_name=? and role=?", userName, role.getCode());
    }


    @Override
    public List<IndustryDemandAuthDO> getAuthRole(String userName) {

        if (Strings.isBlank(userName)) {
            return Lang.list();
        }
        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where user_name=?", userName);
        return auths;
    }

    @Override
    public String getApproveUser(IndustryDemandAuthRoleEnum role) {
        if (role == null) {
            return "";
        }
        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where role=?", role.getCode());
        return StringTools.join(ListUtils.transform(auths, o -> o.getUserName()), ";");
    }

    @Override
    public String getApproveUser(List<IndustryDemandAuthRoleEnum> role) {
        if (role == null) {
            return "";
        }
        List<String> transform = ListUtils.transform(role, (o) -> o.getCode());

        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where role in (?)", transform);
        return StringTools.join(ListUtils.transform(auths, o -> o.getUserName()), ";");
    }

    @Override
    public void requireRole(IndustryDemandAuthRoleEnum role) {
        if (role == null) {
            return;
        }
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        if (userNameWithSystem.equals("system")) {
            return;
        }
        List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where role=?", role.getCode());
        if (!ListUtils.toSet(auths, o -> o.getUserName()).contains(userNameWithSystem)) {
            throw new BizException("您没有" + role.getName() + "的角色权限，无法进行操作，请联系管理员开通权限");
        }
    }

    @Override
    public String getApproveUser(IndustryDemandAuthRoleEnum role, String industry) {

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(IndustryDemandAuthDO::getRole, role.getCode());

        List<IndustryDemandAuthDO> all = ORMUtils.db(demandDBHelper).getAll(IndustryDemandAuthDO.class, whereContent);

        List<IndustryDemandAuthDO> ret = ListUtils.filter(all, (o) -> {
            if (Strings.isNotBlank(o.getIndustry())) {
                if (!o.getIndustry().contains(industry)) {
                    return false;
                }
            }
            return true;
        });
        List<String> collect = ListUtils.transform(ret, IndustryDemandAuthDO::getUserName);
        return Strings.join(";", collect);
    }

    @Override
    public String getApproveUser(IndustryDemandAuthRoleEnum role, String industry, String product) {

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(IndustryDemandAuthDO::getRole, role.getCode());

        List<IndustryDemandAuthDO> all = ORMUtils.db(demandDBHelper).getAll(IndustryDemandAuthDO.class, whereContent);

        final String finalProduct = product;
        List<IndustryDemandAuthDO> ret = ListUtils.filter(all, (o) -> {
            if (Strings.isNotBlank(o.getIndustry())) {
                if (!o.getIndustry().contains(industry)) {
                    return false;
                }
            }
            if (Strings.isNotBlank(o.getProduct())) {
                if (!o.getProduct().contains(finalProduct)) {
                    return false;
                }
            }
            return true;
        });

        if (ListUtils.isEmpty(ret)) {
            Supplier<String> defaultRole = DynamicProperty.create("app.default.role.user", "");
            return defaultRole.get();
        }

        List<String> collect = ListUtils.transform(ret, IndustryDemandAuthDO::getUserName);
        return Strings.join(";", collect);
    }

    @Override
    public String getApproveUser(String roleCode, String industry, String center, String warZone, String customer) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(IndustryDemandAuthDO::getRole, roleCode);
        if (Strings.isNotBlank(industry)) {
            whereContent.addAnd("industry like ?", "%" + industry + "%");
        }
        if (Strings.isNotBlank(center)) {
            whereContent.addAnd("center_name like ?", "%" + center + "%");
        }
        if (Strings.isNotBlank(warZone)) {
            whereContent.addAnd("war_zone_name like ?", "%" + warZone + "%");
        }
        if (Strings.isNotBlank(customer)) {
            whereContent.addAnd("common_customer_name like ?", "%" + customer + "%");
        }
        List<IndustryDemandAuthDO> all = ORMUtils.db(demandDBHelper).getAll(IndustryDemandAuthDO.class, whereContent);

        if (ListUtils.isEmpty(all)) {
            Supplier<String> defaultRole = DynamicProperty.create("app.default.role.user", "");
            return defaultRole.get();
        }
        List<String> collect = ListUtils.transform(all, IndustryDemandAuthDO::getUserName);
        return Strings.join(";", collect);
    }

    @Override
    public boolean isInVersionProcess(int year, int month) {

        PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class, "where status='PROCESS'");
        if (one == null) {
            return false;
        }

        if (one.getDemandEndYear() < year) {
            return false;
        }
        if (one.getDemandBeginYear() > year) {
            return false;
        }
        if (one.getDemandEndMonth() < month) {
            return false;
        }
        if (one.getDemandBeginMonth() > month) {
            return false;
        }
        return true;
    }

    @Override
    public boolean createTodo(TodoService.ApprovalData approvalData) {
        approvalData.setSystem("YUNTI"); // 这里必须写死为YUNTI，不然邮件点击跳转到
        try {
            log.info("begin create todo:{}", JSON.toJson(approvalData));
            // 这个返回结果没啥用，都是空的
            JsonrpcReq req = jsonrpcClient.jsonrpc("createTask").uri(todoUrlSupplier.get())
                    .id(UUID.randomUUID().toString())
                    .params(approvalData).noInjectServletRequest();
            String resp = req.postForObject(String.class);
            log.info("end create todo:{}, resp:{}", JSON.toJson(approvalData), resp);

            //记录Todo接口调用
            PplToDoLogDTO pplToDoLogDTO = new PplToDoLogDTO();
            pplToDoLogDTO.setUrl(todoUrlSupplier.get());
            pplToDoLogDTO.setMethod("createTask");
            pplToDoLogDTO.setAcceptUser(approvalData.getHandler());
            pplToDoLogDTO.setReq(JSON.toJson(approvalData));
            pplToDoLogDTO.setResp(resp);
            todoService.addTodoLog(pplToDoLogDTO);
            return true;
        } catch (Exception e) {
            log.error("createTodo fail, param:{}", JSON.toJson(approvalData), e);
            return false;
        }
    }

    @Override
    public boolean finishTodo(TodoService.ApprovalMessageBody msg) {
        try {
            Map params = ImmutableMap.of("taskId", msg.getApproverOrder(), "processResult", 0, "approver",
                    msg.getApprover(), "approveResult", msg.getApproveResult(), "msg",
                    msg.getApproveMemo());
            log.info("begin finish todo:{}", JSON.toJson(params));

            // 这个返回结果没啥用，都是空的
            JsonrpcReq req = jsonrpcClient.jsonrpc("submitTaskResult").uri(todoUrlSupplier.get())
                    .id(UUID.randomUUID().toString()).params(params).noInjectServletRequest();
            String resp = req.postForObject(String.class);
            log.info("end finish todo:{}, resp:{}", JSON.toJson(params), resp);

            //记录Todo接口调用
            PplToDoLogDTO pplToDoLogDTO = new PplToDoLogDTO();
            pplToDoLogDTO.setUrl(todoUrlSupplier.get());
            pplToDoLogDTO.setMethod("submitTaskResult");
            pplToDoLogDTO.setSendUser(msg.getApprover());
//            pplToDoLogDTO.setAcceptUser(approvalData.getHandler());
            pplToDoLogDTO.setReq(JSON.toJson(params));
            pplToDoLogDTO.setResp(resp);
            todoService.addTodoLog(pplToDoLogDTO);
            return true;
        } catch (Exception e) {
            log.error("finishTodo fail, param:{}", JSON.toJson(msg), e);
            return false;
        }
    }

    @Resource
    DictService dictService;

    @Override
    public String updateTotalAmount() {
        List<PplItemDO> all = demandDBHelper.getAll(PplItemDO.class);
        for (PplItemDO pplItemDO : all) {
            updateTotalAmount(pplItemDO);
        }

        List<PplOrderRecordItemDO> orderItems = demandDBHelper.getAll(PplOrderRecordItemDO.class);
        for (PplOrderRecordItemDO pplItemDO : orderItems) {
            updateTotalAmount(pplItemDO);
        }

        String sql =
                "select ppl_order, sum(if(demand_type='RETURN',-total_core,total_core)) as all_core_sum,sum(if(demand_type='RETURN',-total_disk,total_disk)) as all_disk_sum\n"
                        + "from ppl_order_record_item where\n"
                        + "record_version in (select max(record_version) from ppl_order_record where deleted=0  group by ppl_order) group by ppl_order";

        List<PplOrderSumDO> raw = demandDBHelper.getRaw(PplOrderSumDO.class, sql);

        Map<String, PplOrderSumDO> stringPplOrderSumDOMap = ListUtils.toMap(raw, PplOrderSumDO::getPplOrder, (o) -> o);

        List<PplOrderDO> orders = demandDBHelper.getAll(PplOrderDO.class);

        for (PplOrderDO order : orders) {
            PplOrderSumDO pplOrderSumDO = stringPplOrderSumDOMap.get(order.getPplOrder());
            if (pplOrderSumDO != null) {
                order.setAllDisk(pplOrderSumDO.getAllDiskSum());
                order.setAllCore(pplOrderSumDO.getAllCoreSum());
            }
        }
        demandDBHelper.update(orders);

        return "success";
    }

    @Override
    public void syncCustomerUinType(boolean force) {
        List<String> uins = null;
        if (force) {
            uins = demandDBHelper.getRaw(String.class,
                    "SELECT DISTINCT customer_uin FROM `ppl_order` WHERE deleted=0 AND customer_uin!=''");
        } else {
            // 只增量部分
            uins = demandDBHelper.getRaw(String.class,
                    "SELECT DISTINCT customer_uin FROM `ppl_order` WHERE deleted=0 AND customer_uin!=''"
                            + " and customer_uin not in (select customer_uin from ppl_config_uin_type)");
        }

        for (String uin : uins) {
            try {
                try {
                    AccountUtil.AccountInfoDTO accountInfoDTO =
                            AccountUtil.queryUinAccountInfo(uin, CommonUtil.defaultSecret(),
                                    ListUtils.newList("account_info"));
                    Integer customerType = accountInfoDTO.getField("account_info.customer_type", Integer.class);
                    if (customerType != null && customerType >= 0) {
                        PplConfigUinTypeDO one = demandDBHelper.getOne(PplConfigUinTypeDO.class,
                                "where customer_uin=?", uin);
                        if (one != null) {
                            one.setCustomerType(customerType);
                            demandDBHelper.update(one);
                        } else {
                            one = new PplConfigUinTypeDO();
                            one.setCustomerUin(uin);
                            one.setCustomerType(customerType);
                            demandDBHelper.insert(one);
                        }
                    }
                } catch (IllegalArgumentException e) {
                    log.error("query account IllegalArgumentException:{}, {}", uin, e.getMessage());
                }
            } catch (Exception e) {
                log.error("query account info fail for uin:{}", uin, e);
            }
        }
    }

    @Override
    public PplVersionDO getPplVersionDO(String versionCode, boolean throwException) {

        PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class, "where version_code=?", versionCode);
        if (one == null && throwException) {
            throw BizException.makeThrow("未找到version: %s", versionCode);
        }
        return one;
    }

    @Override
    public ArchitectPermission getArchitectPermission(String user) {
        UserPermissionReq req = new UserPermissionReq();
        req.setUsers(Lists.newArrayList(user));
        ArchitectPermission perm = new ArchitectPermission();
        try {
            UserPermissionDto p = permissionService.getMergePermission(req);
            perm.setUser(user);
            perm.setRole(p.getRole());
            perm.setIndustry(p.getIndustry());
            perm.setProduct(p.getProduct());
            perm.setCenter(p.getCenter());
            perm.setWarZone(p.getWarZone());
            perm.setIsAllIndustry(p.getIsAllIndustry());
        } catch (Exception e) {
            log.info("没有权限会报错，这里忽略");
        }
        List<String> uin = demandDBHelper.getAll(DimArchitectUinRelationDO.class, "where architect=? ", user).stream()
                .map(DimArchitectUinRelationDO::getUin).collect(Collectors.toList());
        perm.setAuthUin(uin);
        return perm;
    }

    private void updateTotalAmount(PplOrderRecordItemDO pplItemDO) {
        Integer instanceCoreNum = dictService.getInstanceCoreNum(pplItemDO.getInstanceModel());
        Integer instanceNum = pplItemDO.getInstanceNum();
        instanceNum = instanceNum == null ? 0 : instanceNum;

        pplItemDO.setTotalCore(instanceCoreNum * instanceNum);

        int totalDiskNum = 0;
        if (pplItemDO.getSystemDiskStorage() != null && pplItemDO.getSystemDiskNum() != null) {
            totalDiskNum += pplItemDO.getSystemDiskStorage() * pplItemDO.getSystemDiskNum();
        }
        if (pplItemDO.getDataDiskStorage() != null && pplItemDO.getDataDiskNum() != null) {
            totalDiskNum += pplItemDO.getDataDiskStorage() * pplItemDO.getDataDiskNum();
        }
        pplItemDO.setTotalDisk(totalDiskNum * instanceNum);
        demandDBHelper.update(pplItemDO);
    }

    private void updateTotalAmount(PplItemDO pplItemDO) {
        Integer instanceCoreNum = dictService.getInstanceCoreNum(pplItemDO.getInstanceModel());
        Integer instanceNum = pplItemDO.getInstanceNum();
        instanceNum = instanceNum == null ? 0 : instanceNum;

        pplItemDO.setTotalCore(instanceCoreNum * instanceNum);

        int totalDiskNum = 0;
        if (pplItemDO.getSystemDiskStorage() != null && pplItemDO.getSystemDiskNum() != null) {
            totalDiskNum += pplItemDO.getSystemDiskStorage() * pplItemDO.getSystemDiskNum();
        }
        if (pplItemDO.getDataDiskStorage() != null && pplItemDO.getDataDiskNum() != null) {
            totalDiskNum += pplItemDO.getDataDiskStorage() * pplItemDO.getDataDiskNum();
        }
        pplItemDO.setTotalDisk(totalDiskNum * instanceNum);
        demandDBHelper.update(pplItemDO);
    }

    @Override
    public WhereSQL getAuthForArchitect(String username, Boolean isNeedSubmitUser) {
        WhereSQL whereSQL = new WhereSQL();

        ArchitectPermission auth = getArchitectPermission(username);
        if ((IndustryDemandAuthRoleEnum.getByCode(auth.getRole()) != IndustryDemandAuthRoleEnum.ADMIN)) {
            //战区权限 or uin 权限
            if (ListUtils.isEmpty(auth.getWarZone()) && ListUtils.isEmpty(auth.getAuthUin())) {
                //没有权限 设置个查不到的条件。不报错，查不到数据即可
                return new WhereSQL("1=0");
            }
            if (ListUtils.isNotEmpty(auth.getWarZone())) {
                whereSQL.or("war_zone in (?)", auth.getWarZone());
            }
            if (ListUtils.isNotEmpty(auth.getAuthUin())) {
                whereSQL.or("customer_uin in (?)", auth.getAuthUin());
            }
            if (isNeedSubmitUser) {
                whereSQL.or("submit_user = ?", username);
            }
        }
        // 管理员不限制

        log.info("getAuthForArchitect condition:{}, params:{}", whereSQL.getSQL(), JSON.toJson(whereSQL.getParams()));
        return whereSQL;
    }

    @Override
    public QueryArchitectAuthResp queryArchitectAuth(String username) {
        QueryArchitectAuthResp resp = new QueryArchitectAuthResp();
        ArchitectPermission auth = getArchitectPermission(username);
        if ((IndustryDemandAuthRoleEnum.getByCode(auth.getRole()) != IndustryDemandAuthRoleEnum.ADMIN)) {
            //战区权限  or uin 权限
            if (!CollectionUtils.isEmpty(auth.getWarZone())) {
                resp.setWarZone(auth.getWarZone());
            }
            if (!CollectionUtils.isEmpty(auth.getAuthUin())) {
                resp.setCustomerUin(auth.getAuthUin());
            }
            resp.setIsAdmin(false);
        } else {
            resp.setIsAdmin(true);
            // 找管理员的战区
            List<IndustryDemandAuthDO> auths = demandDBHelper.getAll(IndustryDemandAuthDO.class);
            Set<String> warZones = new HashSet<>();
            for (IndustryDemandAuthDO a : auths) {
                String warZoneName = a.getWarZoneName();
                if (StringTools.isNotBlank(warZoneName)) {
                    String[] split = warZoneName.split(";");
                    for (String s : split) {
                        if (StringTools.isNotBlank(s)) {
                            warZones.add(s);
                        }
                    }
                }
            }
            resp.setWarZoneForAdmin(ListUtils.toList(warZones));
        }
        return resp;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void checkInstanceTypeConfig() {
        try {
            List<PublicIndustryInstanceModelEnumDO> all = demandDBHelper.getAll(PublicIndustryInstanceModelEnumDO.class,
                    "where is_apply_change = 1");

            if (ListUtils.isEmpty(all)) {
                return;
            }
            Map<String, List<PublicIndustryInstanceModelEnumDO>> map = all.stream()
                    .collect(Collectors.groupingBy(PublicIndustryInstanceModelEnumDO::getProduct));
            List<String> sendMsgList = new ArrayList<>();
            List<IndustryDemandRegionZoneInstanceTypeDictDO> cvmInsertList = new ArrayList<>();
            List<PplGpuRegionZoneDO> gpuInsertList = new ArrayList<>();
            List<PublicIndustryInstanceModelEnumDO> updateList = new ArrayList<>();
            map.forEach((k, v) -> {
                if (k.equals("CVM")) {
                    for (PublicIndustryInstanceModelEnumDO publicIndustryInstanceModelEnumDO : v) {
                        // 如果需要变更的实例类型存在已生效数据， 那么需人工变更，系统不做变更
                        if (checkExist(publicIndustryInstanceModelEnumDO.getInstanceModel())) {
                            sendMsgList.add(publicIndustryInstanceModelEnumDO.getInstanceModel());
                        } else {
                            // 更新到配置表中，只新增 不更新
                            cvmInsertList.add(publicIndustryInstanceModelEnumDO.transToCVMDO());
                            updateList.add(publicIndustryInstanceModelEnumDO);
                        }
                    }
                } else if (k.equals("GPU")) {
                    for (PublicIndustryInstanceModelEnumDO publicIndustryInstanceModelEnumDO : v) {
                        // 如果需要变更的实例类型存在已生效数据， 那么需人工变更，系统不做变更
                        if (checkExist(publicIndustryInstanceModelEnumDO.getInstanceModel())) {
                            sendMsgList.add(publicIndustryInstanceModelEnumDO.getInstanceModel());
                        } else {
                            // 更新到配置表中，只新增 不更新
                            gpuInsertList.add(publicIndustryInstanceModelEnumDO.transToGPUDO());
                            updateList.add(publicIndustryInstanceModelEnumDO);
                        }
                    }
                } else {
                    // todo
                }
            });
            if (ListUtils.isNotEmpty(cvmInsertList)) {
                demandDBHelper.insertBatchWithoutReturnId(cvmInsertList);
            }
            if (ListUtils.isNotEmpty(gpuInsertList)) {
                demandDBHelper.insertBatchWithoutReturnId(gpuInsertList);
            }
            if (ListUtils.isNotEmpty(updateList)) {
                List<Long> idList = updateList.stream().map(PublicIndustryInstanceModelEnumDO::getId)
                        .collect(Collectors.toList());
                demandDBHelper.executeRaw(
                        "update public_industry_instance_model_enum set is_apply_change = 0 where id in (?)", idList);
            }
            if (ListUtils.isNotEmpty(sendMsgList)) {
                String msg = "以下规格系统中已存在生效数据，请检查: " + String.join(";", sendMsgList);
                alert.sendRtx("oliverychen;kaijiazhang", "实例规格白名单配置更新检查", msg);
            }
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("checkInstanceTypeConfig", "检查实例规格白名单程序出错" + e.getMessage(), null,
                    false);
            throw new BizException("checkInstanceTypeConfig 检查实例规格白名单程序出错");
        }
    }

    @Override
    public void reverseInstanceToPublicInstanceTypeConfig() {
        List<String> cvmInstanceModelList = demandDBHelper.getRaw(String.class,
                "select distinct instance_model from public_industry_instance_model_enum where product = ?","CVM");
        cvmInstanceModelList.add("");

        // CVM
        List<IndustryDemandRegionZoneInstanceTypeDictDO> cvmList = demandDBHelper.getAll(
                IndustryDemandRegionZoneInstanceTypeDictDO.class,
                "where instance_model not in (?) and parse_core != 0 and parse_ram != 0 and gpu_num = 0 group by instance_model",
                cvmInstanceModelList);
        if (ListUtils.isNotEmpty(cvmList)) {
            List<PublicIndustryInstanceModelEnumDO> insertList = new ArrayList<>();
            for (IndustryDemandRegionZoneInstanceTypeDictDO industryDemandRegionZoneInstanceTypeDictDO : cvmList) {
                PublicIndustryInstanceModelEnumDO publicIndustryInstanceModelEnumDO = new PublicIndustryInstanceModelEnumDO();
                publicIndustryInstanceModelEnumDO.setProduct("CVM");
                publicIndustryInstanceModelEnumDO.setInstanceType(
                        industryDemandRegionZoneInstanceTypeDictDO.getInstanceType());
                publicIndustryInstanceModelEnumDO.setInstanceModel(
                        industryDemandRegionZoneInstanceTypeDictDO.getInstanceModel());
                publicIndustryInstanceModelEnumDO.setCpuCore(industryDemandRegionZoneInstanceTypeDictDO.getParseCore());
                publicIndustryInstanceModelEnumDO.setMemory(industryDemandRegionZoneInstanceTypeDictDO.getParseRam());
                publicIndustryInstanceModelEnumDO.setGpuNum(0);
                publicIndustryInstanceModelEnumDO.setIsApplyChange(Boolean.FALSE);
                publicIndustryInstanceModelEnumDO.setInstanceSource("YUNXIAO");
                insertList.add(publicIndustryInstanceModelEnumDO);
            }
            demandDBHelper.insertBatchWithoutReturnId(insertList);
        }

        // GPU
        List<String> gpuInstanceModelList = demandDBHelper.getRaw(String.class,
                "select distinct instance_model from public_industry_instance_model_enum where product = ?","GPU");
        gpuInstanceModelList.add("");
        List<PplGpuRegionZoneDO> gpuList = demandDBHelper.getAll(
                PplGpuRegionZoneDO.class,
                "where not_show = 0 and instance_model not in (?) and cpu_num != 0 and gpu_num != 0 and memory != 0 and gpu_type is not null group by instance_model",
                gpuInstanceModelList);
        if (ListUtils.isNotEmpty(gpuList)) {
            List<PublicIndustryInstanceModelEnumDO> insertList = new ArrayList<>();
            for (PplGpuRegionZoneDO gpuRegionZoneDO : gpuList) {
                PublicIndustryInstanceModelEnumDO publicIndustryInstanceModelEnumDO = new PublicIndustryInstanceModelEnumDO();
                publicIndustryInstanceModelEnumDO.setProduct("GPU");
                publicIndustryInstanceModelEnumDO.setInstanceType(
                        gpuRegionZoneDO.getInstanceType());
                publicIndustryInstanceModelEnumDO.setInstanceModel(
                        gpuRegionZoneDO.getInstanceModel());
                publicIndustryInstanceModelEnumDO.setCpuCore(gpuRegionZoneDO.getCpuNum());
                publicIndustryInstanceModelEnumDO.setMemory(gpuRegionZoneDO.getMemory());
                publicIndustryInstanceModelEnumDO.setGpuNum(gpuRegionZoneDO.getGpuNum());
                publicIndustryInstanceModelEnumDO.setIsApplyChange(Boolean.FALSE);
                publicIndustryInstanceModelEnumDO.setGpuType(gpuRegionZoneDO.getGpuType());
                publicIndustryInstanceModelEnumDO.setGpuProductType(gpuRegionZoneDO.getGpuProductType());
                publicIndustryInstanceModelEnumDO.setInstanceSource("云官网");
                insertList.add(publicIndustryInstanceModelEnumDO);
            }
            demandDBHelper.insertBatchWithoutReturnId(insertList);
        }
    }

    @Override
    public void syncYunxiaoGpuToConfig() {
        // 同步云霄GPU规格数据到Gpu配置
        List<IndustryDemandRegionZoneInstanceTypeDictDO> all = demandDBHelper.getRaw(
                IndustryDemandRegionZoneInstanceTypeDictDO.class,
                "select * from industry_demand_region_zone_instance_type_dict where deleted = 0 and gpu_num != 0"
                        + " and instance_model not in (select distinct instance_model from ppl_gpu_region_zone where deleted = 0)");

        List<PplGpuRegionZoneDO> result = new ArrayList<>();
        Map<String, String> map = pplDictService.queryGpuInstanceType2GpuType();
        for (IndustryDemandRegionZoneInstanceTypeDictDO industryDemandRegionZoneInstanceTypeDictDO : all) {
            PplGpuRegionZoneDO pplGpuRegionZoneDO = new PplGpuRegionZoneDO();
            pplGpuRegionZoneDO.setRegionName("随机地域");
            pplGpuRegionZoneDO.setRegion("region");
            pplGpuRegionZoneDO.setZoneName("随机可用区");
            pplGpuRegionZoneDO.setZone("zone");
            pplGpuRegionZoneDO.setInstanceType(industryDemandRegionZoneInstanceTypeDictDO.getInstanceType());
            pplGpuRegionZoneDO.setInstanceModel(industryDemandRegionZoneInstanceTypeDictDO.getInstanceModel());
            pplGpuRegionZoneDO.setCpuNum(industryDemandRegionZoneInstanceTypeDictDO.getParseCore());
            pplGpuRegionZoneDO.setMemory(industryDemandRegionZoneInstanceTypeDictDO.getParseRam());
            pplGpuRegionZoneDO.setGpuNum(industryDemandRegionZoneInstanceTypeDictDO.getGpuNum());
            pplGpuRegionZoneDO.setGpuCount(BigDecimal.valueOf(industryDemandRegionZoneInstanceTypeDictDO.getGpuNum()));
            pplGpuRegionZoneDO.setGpuType(map.get(industryDemandRegionZoneInstanceTypeDictDO.getInstanceType()));
            pplGpuRegionZoneDO.setGpuProductType("CVM_GPU");
            pplGpuRegionZoneDO.setIsWhite(false);
            pplGpuRegionZoneDO.setNotShow(pplGpuRegionZoneDO.getGpuType() == null);
            result.add(pplGpuRegionZoneDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    public void checkInstanceTypeData() {
        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class, "where instance_type like '%型%'");
        if (ListUtils.isNotEmpty(pplItemDOList)) {
            List<String> list = pplItemDOList.stream().map(v -> v.getInstanceType()).distinct()
                    .collect(Collectors.toList());
            AlarmRobotUtil.doAlarm("checkInstanceTypeData-ppl_item", "非标实例类型数据" + list, null,
                    false);
//            demandDBHelper.executeRaw("update ppl_item set instance_type = "
//                    + "SUBSTRING(instance_type, INSTR(instance_type, '型') + 1) where deleted = 0 and instance_type like '%型%'");
        }
        List<PplItemDraftDO> pplItemDraftDOList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where instance_type like '%型%'");
        if (ListUtils.isNotEmpty(pplItemDraftDOList)) {
            List<String> list = pplItemDraftDOList.stream().map(v -> v.getInstanceType()).distinct()
                    .collect(Collectors.toList());
            AlarmRobotUtil.doAlarm("checkInstanceTypeData-ppl_item_darft", "非标实例类型数据" + list, null,
                    false);
//            demandDBHelper.executeRaw("update ppl_item_darft set instance_type = "
//                    + "SUBSTRING(instance_type, INSTR(instance_type, '型') + 1) where deleted = 0 and instance_type like '%型%'");
        }

        List<PplOrderAuditRecordItemDO> pplOrderAuditRecordItemDOList = demandDBHelper.getAll(
                PplOrderAuditRecordItemDO.class, "where instance_type like '%型%'");
        if (ListUtils.isNotEmpty(pplOrderAuditRecordItemDOList)) {
            List<String> list = pplOrderAuditRecordItemDOList.stream().map(v -> v.getInstanceType()).distinct()
                    .collect(Collectors.toList());
            AlarmRobotUtil.doAlarm("checkInstanceTypeData-ppl_order_audit_record_item", "非标实例类型数据" + list, null,
                    false);
//            demandDBHelper.executeRaw("update ppl_order_audit_record_item set instance_type = "
//                    + "SUBSTRING(instance_type, INSTR(instance_type, '型') + 1) where deleted = 0 and instance_type like '%型%'");
        }

        List<PplVersionGroupRecordItemDO> pplVersionGroupRecordItemDOList = demandDBHelper.getAll(
                PplVersionGroupRecordItemDO.class, "where instance_type like '%型%'");
        if (ListUtils.isNotEmpty(pplVersionGroupRecordItemDOList)) {
            List<String> list = pplVersionGroupRecordItemDOList.stream().map(v -> v.getInstanceType()).distinct()
                    .collect(Collectors.toList());
            AlarmRobotUtil.doAlarm("checkInstanceTypeData-ppl_version_group_record_item", "非标实例类型数据" + list,
                    null,
                    false);
//            demandDBHelper.executeRaw("update ppl_version_group_record_item set instance_type = "
//                    + "SUBSTRING(instance_type, INSTR(instance_type, '型') + 1) where deleted = 0 and instance_type like '%型%'");
        }
    }

//    @Override
//    public void rebuild0520Data(){
//        LocalDate dataDate = LocalDate.parse("2024-05-20");
//
//        DBList.ckcldStdCrpDBHelper.executeRaw(
//                "ALTER TABLE std_crp.ads_mck_forecast_summary_df_local ON CLUSTER default_cluster DROP PARTITION ?",
//                dataDate);
//
//        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
//        // 海外地域
//        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
//
//        List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList = demandDBHelper.getAll(
//                InventoryHealthMainZoneNameConfigDO.class,
//                "where date = ?", LocalDate.now());
//        List<ResPlanHolidayWeekDO> holidayWeekDOList = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class,
//                "order by start asc");
//        String customerDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/customer_0520.sql");
//        List<CustomerForecastDataDTO> customerList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
//                customerDataSql);
//        List<AdsMckForecastSummaryDfDO> customerData = buildCustomerData(customerList,
//                zoneNameConfigDOList, holidayWeekDOList);
//        result.addAll(customerData);
//
//        String longTailDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/long_tail_0520.sql");
//        List<CustomerForecastDataDTO> longTailList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
//                longTailDataSql);
//        List<AdsMckForecastSummaryDfDO> longTailData = buildLongTailData(longTailList, holidayWeekDOList);
//        result.addAll(longTailData);
//
//        String yunxiaoOrderDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/yunxiao_order.sql");
//        List<YunxiaoOrderDataDTO> yunxiaoList = DBList.ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDataDTO.class,
//                yunxiaoOrderDataSql);
//        List<AdsMckForecastSummaryDfDO> yunxiaoData = buildYunxiaoData(yunxiaoList, holidayWeekDOList);
//        result.addAll(yunxiaoData);
//
//        // 计算 min/max
//        computeMinMax(customerData, yunxiaoData);
//
//        // 判断国内外
//        for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : result) {
//            if (overSeaRegions.contains(adsMckForecastSummaryDfDO.getRegionName())) {
//                adsMckForecastSummaryDfDO.setCustomhouseTitle("境外");
//            } else {
//                adsMckForecastSummaryDfDO.setCustomhouseTitle("境内");
//            }
//        }
//
//        result.forEach(item->item.setStatTime(dataDate));
//
//        DBList.ckcldStdCrpDBHelper.insert(result);
//    }

    @Override
    @Synchronized
    @TaskRunSql(namespace = "FORECAST", nameScript = "'initAdsMckForecastSummaryDF'", keyScript = "args[0]",logArgs = true)
    @TaskLog(taskName = "initAdsMckForecastSummaryDF")
    public void initAdsMckForecastSummaryDF(LocalDate dataDate) {

        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.ads_mck_forecast_summary_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                dataDate);

        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where status = ?",
                PplVersionStatusEnum.PROCESS.getCode());

        List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList = demandDBHelper.getAll(
                InventoryHealthMainZoneNameConfigDO.class,
                "where date = ?", LocalDate.now());
        List<ResPlanHolidayWeekDO> holidayWeekDOList = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class,
                "order by start asc");
        String customerDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/customer.sql");
        List<CustomerForecastDataDTO> customerList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
                customerDataSql);
        List<AdsMckForecastSummaryDfDO> customerData = buildCustomerData(customerList,
                zoneNameConfigDOList, holidayWeekDOList);
        result.addAll(customerData);

        String longTailDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/long_tail.sql");
        List<CustomerForecastDataDTO> longTailList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
                longTailDataSql);
        List<AdsMckForecastSummaryDfDO> longTailData = buildLongTailData(longTailList, holidayWeekDOList);
        result.addAll(longTailData);

        String yunxiaoOrderDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/yunxiao_order.sql");
        List<YunxiaoOrderDataDTO> yunxiaoList = DBList.ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDataDTO.class,
                yunxiaoOrderDataSql);
        List<AdsMckForecastSummaryDfDO> yunxiaoData = buildYunxiaoData(yunxiaoList, holidayWeekDOList);

        String newOrderSql = ORMUtils.getSql("/sql/ppl13week/mck_data/new_order.sql");
        List<NewOrderDataDTO> newOrderList = demandDBHelper.getRaw(NewOrderDataDTO.class,
                newOrderSql);
        List<AdsMckForecastSummaryDfDO> newOrderData = buildNewOrderData(newOrderList, holidayWeekDOList);
        newOrderData.addAll(yunxiaoData);
        result.addAll(newOrderData);

        // 计算 min/max
        computeMinMax(customerData, newOrderData);

        // 判断国内外
        for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : result) {
            adsMckForecastSummaryDfDO.setStatTime(dataDate);
            if (overSeaRegions.contains(adsMckForecastSummaryDfDO.getRegionName())) {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境外");
            } else {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境内");
            }
        }

        DBList.ckcldStdCrpDBHelper.insert(result);
    }

    @Override
    public void dataComplete(LocalDate localDate) {

        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.ads_mck_forecast_summary_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                localDate);

        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);

        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where status = ?",
                PplVersionStatusEnum.PROCESS.getCode());



        List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList = demandDBHelper.getAll(
                InventoryHealthMainZoneNameConfigDO.class,
                "where date = ?", LocalDate.now());
        List<ResPlanHolidayWeekDO> holidayWeekDOList = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class,
                "order by start asc");


        LocalDate nextDay = localDate.plusDays(1);

        // 创建 DateTimeFormatter，指定格式为 yyyyMM
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

        // 将 LocalDate 格式化为字符串
        String formattedDate = nextDay.format(formatter);

        String customerDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/customer-fix.sql");
        List<CustomerForecastDataDTO> customerList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
                customerDataSql,formattedDate);
        List<AdsMckForecastSummaryDfDO> customerData = buildCustomerData(customerList,
                zoneNameConfigDOList, holidayWeekDOList);
        result.addAll(customerData);


        String longTailDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/long_tail-fix.sql");
        List<CustomerForecastDataDTO> longTailList = DBList.ckcldStdCrpDBHelper.getRaw(CustomerForecastDataDTO.class,
                longTailDataSql,formattedDate);
        List<AdsMckForecastSummaryDfDO> longTailData = buildLongTailData(longTailList, holidayWeekDOList);
        result.addAll(longTailData);

        String yunxiaoOrderDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/yunxiao_order.sql");
        List<YunxiaoOrderDataDTO> yunxiaoList = DBList.ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDataDTO.class,
                yunxiaoOrderDataSql);
        List<AdsMckForecastSummaryDfDO> yunxiaoData = buildYunxiaoData(yunxiaoList, holidayWeekDOList);


        LocalDate plusDate = localDate.plusDays(1);
        ZoneId zoneId = ZoneId.systemDefault();
        ZonedDateTime zdt = plusDate.atStartOfDay(zoneId);
        Date date = Date.from(zdt.toInstant());

        String newOrderSql = ORMUtils.getSql("/sql/ppl13week/mck_data/new_order-fix.sql");
        List<NewOrderDataDTO> newOrderList = demandDBHelper.getRaw(NewOrderDataDTO.class,
                newOrderSql,date,date);
        List<AdsMckForecastSummaryDfDO> newOrderData = buildNewOrderData(newOrderList, holidayWeekDOList);


        newOrderData.addAll(yunxiaoData);
        result.addAll(newOrderData);

        // 计算 min/max
        computeMinMax(customerData, newOrderData);

        // 判断国内外
        for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : result) {
            adsMckForecastSummaryDfDO.setStatTime(localDate);
            if (overSeaRegions.contains(adsMckForecastSummaryDfDO.getRegionName())) {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境外");
            } else {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境内");
            }
        }

        DBList.ckcldStdCrpDBHelper.insert(result);
    }

    @Override
    public void fixData() {
        LocalDate dataDate = LocalDate.now().minusDays(1);
        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.ads_mck_forecast_summary_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                dataDate);

        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
        List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList = demandDBHelper.getAll(
                InventoryHealthMainZoneNameConfigDO.class,
                "where date = ?", LocalDate.now());
        List<ResPlanHolidayWeekDO> holidayWeekDOList = resplanDBHelper.getAll(ResPlanHolidayWeekDO.class,
                "order by start asc");
        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        List<AdsMckForecastSummaryDfDO> customerData = DBList.ckcldStdCrpDBHelper.getAll(
                AdsMckForecastSummaryDfDO.class,
                "where stat_time = '2024-07-01' and demand_source = '大客户'");

        List<AdsMckForecastSummaryDfDO> longtailData = DBList.ckcldStdCrpDBHelper.getAll(
                AdsMckForecastSummaryDfDO.class,
                "where stat_time = '2024-07-01' and demand_source = '中长尾'");

        result.addAll(customerData);
        result.addAll(longtailData);

        String yunxiaoOrderDataSql = ORMUtils.getSql("/sql/ppl13week/mck_data/yunxiao_order.sql");
        List<YunxiaoOrderDataDTO> yunxiaoList = DBList.ckcldStdCrpDBHelper.getRaw(YunxiaoOrderDataDTO.class,
                yunxiaoOrderDataSql);
        List<AdsMckForecastSummaryDfDO> yunxiaoData = buildYunxiaoData(yunxiaoList, holidayWeekDOList);

        String newOrderSql = ORMUtils.getSql("/sql/ppl13week/mck_data/new_order.sql");
        List<NewOrderDataDTO> newOrderList = demandDBHelper.getRaw(NewOrderDataDTO.class,
                newOrderSql);
        List<AdsMckForecastSummaryDfDO> newOrderData = buildNewOrderData(newOrderList, holidayWeekDOList);
        newOrderData.addAll(yunxiaoData);
        result.addAll(newOrderData);

        // 计算 min/max
        computeMinMax(customerData, newOrderData);

        // 判断国内外
        for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : result) {
            adsMckForecastSummaryDfDO.setStatTime(LocalDate.now().minusDays(1));
            if (overSeaRegions.contains(adsMckForecastSummaryDfDO.getRegionName())) {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境外");
            } else {
                adsMckForecastSummaryDfDO.setCustomhouseTitle("境内");
            }
        }

        DBList.ckcldStdCrpDBHelper.insert(result);
    }

    @Override
    @TaskLog(taskName = "initForecastInOutTask")
    public void initForecastInOutTask(LocalDate statTime) {

        List<AdsMckForecastSummaryDfDO> customerData =
                DBList.ckcldStdCrpDBHelper.getAll(AdsMckForecastSummaryDfDO.class,
                        "where stat_time = ? and product not in ('GPU(裸金属&CVM)','裸金属')"
                                + " and demand_source = '大客户'", statTime);
        List<AdsMckForecastSummaryDfDO> appliedData =
                DBList.ckcldStdCrpDBHelper.getAll(AdsMckForecastSummaryDfDO.class,
                        "where stat_time = ? and product not in ('GPU(裸金属&CVM)','裸金属')"
                                + " and demand_source = '预约单'", statTime);

        List<AdsMckForecastSummaryDfDO> longTailData =
                DBList.ckcldStdCrpDBHelper.getAll(AdsMckForecastSummaryDfDO.class,
                        "where stat_time = ? and product not in ('GPU(裸金属&CVM)','裸金属')"
                                + " and demand_source = '中长尾'", statTime);

        if (ListUtils.isEmpty(customerData) && ListUtils.isEmpty(appliedData) && ListUtils.isEmpty(longTailData)) {
            AlarmRobotUtil.doAlarm("initForecastInOutTask", "ads_mck_forecast_summary_df 数据未生成", null, false);
            return;
        }

        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.ads_mck_forecast_in_out_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                statTime);

        Map<String, List<AdsMckForecastSummaryDfDO>> customerMap = ListUtils.toMapList(customerData,
                v -> Strings.join("@",
                        Arrays.asList(v.getDemandType()
                                , v.getZoneName(), v.getCustomerShortName(), v.getInstanceType(),
                                v.getBeginBuyYear(), v.getBeginBuyWeek(), v.getIndustryDept())), v -> v);

        Map<String, List<AdsMckForecastSummaryDfDO>> appliedMap = ListUtils.toMapList(appliedData,
                v -> Strings.join("@",
                        Arrays.asList(v.getDemandType()
                                , v.getZoneName(), v.getCustomerShortName(), v.getInstanceType(),
                                v.getBeginBuyYear(), v.getBeginBuyWeek(), v.getIndustryDept())), v -> v);

        Map<String, List<AdsMckForecastSummaryDfDO>> longTailMap = ListUtils.toMapList(longTailData,
                v -> Strings.join("@",
                        Arrays.asList(v.getDemandType()
                                , v.getZoneName(), v.getCustomerShortName(), v.getInstanceType(),
                                v.getBeginBuyYear(), v.getBeginBuyWeek(), v.getIndustryDept())), v -> v);

        List<AdsMckForecastInOutDfDO> result = new ArrayList<>();
        customerMap.forEach((k, v) -> {
            AdsMckForecastInOutDfDO adsMckForecastInOutDfDO = new AdsMckForecastInOutDfDO();
            int sum = v.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            String[] split = k.split("@");
            adsMckForecastInOutDfDO.setStatTime(statTime);
            adsMckForecastInOutDfDO.setDemandType(split[0]);
            adsMckForecastInOutDfDO.setZoneName(split[1]);
            adsMckForecastInOutDfDO.setCustomerShortName(split[2]);
            adsMckForecastInOutDfDO.setInstanceType(split[3]);
            adsMckForecastInOutDfDO.setBeginBuyYear(Integer.parseInt(split[4]));
            adsMckForecastInOutDfDO.setBeginBuyWeek(Integer.parseInt(split[5]));
            adsMckForecastInOutDfDO.setIndustryDept(split[6]);
            adsMckForecastInOutDfDO.setRegionName(v.get(0).getRegionName());
            adsMckForecastInOutDfDO.setCustomHouseTitle(v.get(0).getCustomhouseTitle());

            Integer forecastCore = sum;
            Integer applyCore = getValue(k, appliedMap);
            if (adsMckForecastInOutDfDO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                //如果是退回需求 case：预测为 -100 预约为0
                // 预测内 0 预测外 0 未预约 100
                adsMckForecastInOutDfDO.setTotalCore(0);
                adsMckForecastInOutDfDO.setInForecastTotalCore(0);
                adsMckForecastInOutDfDO.setOutForecastTotalCore(0);
                adsMckForecastInOutDfDO.setNotApplyTotalCore(forecastCore);
            } else {
                if (forecastCore >= applyCore) {
                    // 如果预测量 >= 预约量， 那么预测内 = 预约量， 预约外 = 0， 未转预约预测 = 预测量 - 预约量
                    adsMckForecastInOutDfDO.setTotalCore(forecastCore);
                    adsMckForecastInOutDfDO.setInForecastTotalCore(applyCore);
                    adsMckForecastInOutDfDO.setOutForecastTotalCore(0);
                    adsMckForecastInOutDfDO.setNotApplyTotalCore(forecastCore - applyCore);
                } else {
                    adsMckForecastInOutDfDO.setTotalCore(applyCore);
                    // 如果预测量 < 预约量， 那么预测内 = 预测量， 预约外 = 预约量 - 预测量， 未转预约预测 = 0
                    adsMckForecastInOutDfDO.setInForecastTotalCore(forecastCore);
                    adsMckForecastInOutDfDO.setOutForecastTotalCore(applyCore - forecastCore);
                    adsMckForecastInOutDfDO.setNotApplyTotalCore(0);
                }
            }
            result.add(adsMckForecastInOutDfDO);
            // 移除预约中的key
            appliedMap.remove(k);
        });

        appliedMap.forEach((k, v) -> {
            AdsMckForecastInOutDfDO adsMckForecastInOutDfDO = new AdsMckForecastInOutDfDO();
            int sum = v.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            String[] split = k.split("@");
            adsMckForecastInOutDfDO.setStatTime(statTime);
            adsMckForecastInOutDfDO.setDemandType(split[0]);
            adsMckForecastInOutDfDO.setZoneName(split[1]);
            adsMckForecastInOutDfDO.setCustomerShortName(split[2]);
            adsMckForecastInOutDfDO.setInstanceType(split[3]);
            adsMckForecastInOutDfDO.setBeginBuyYear(Integer.parseInt(split[4]));
            adsMckForecastInOutDfDO.setBeginBuyWeek(Integer.parseInt(split[5]));
            adsMckForecastInOutDfDO.setIndustryDept(split[6]);
            adsMckForecastInOutDfDO.setRegionName(v.get(0).getRegionName());
            adsMckForecastInOutDfDO.setCustomHouseTitle(v.get(0).getCustomhouseTitle());

            Integer forecastCore = getValue(k, customerMap);
            Integer applyCore = sum;
            if (forecastCore >= applyCore) {
                // 如果预测量 >= 预约量， 那么预测内 = 预约量， 预约外 = 0， 未转预约预测 = 预测量 - 预约量
                adsMckForecastInOutDfDO.setTotalCore(forecastCore);
                adsMckForecastInOutDfDO.setInForecastTotalCore(applyCore);
                adsMckForecastInOutDfDO.setOutForecastTotalCore(0);
                adsMckForecastInOutDfDO.setNotApplyTotalCore(forecastCore - applyCore);
            } else {
                adsMckForecastInOutDfDO.setTotalCore(applyCore);
                // 如果预测量 < 预约量， 那么预测内 = 预测量， 预约外 = 预约量 - 预测量， 未转预约预测 = 0
                adsMckForecastInOutDfDO.setInForecastTotalCore(forecastCore);
                adsMckForecastInOutDfDO.setOutForecastTotalCore(applyCore - forecastCore);
                adsMckForecastInOutDfDO.setNotApplyTotalCore(0);
            }
            result.add(adsMckForecastInOutDfDO);
        });

        longTailMap.forEach((k, v) -> {
            AdsMckForecastInOutDfDO adsMckForecastInOutDfDO = new AdsMckForecastInOutDfDO();
            int sum = v.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            String[] split = k.split("@");
            adsMckForecastInOutDfDO.setStatTime(statTime);
            adsMckForecastInOutDfDO.setDemandType(split[0]);
            adsMckForecastInOutDfDO.setZoneName(split[1]);
            adsMckForecastInOutDfDO.setCustomerShortName(split[2]);
            adsMckForecastInOutDfDO.setInstanceType(split[3]);
            adsMckForecastInOutDfDO.setBeginBuyYear(Integer.parseInt(split[4]));
            adsMckForecastInOutDfDO.setBeginBuyWeek(Integer.parseInt(split[5]));
            adsMckForecastInOutDfDO.setIndustryDept(split[6]);
            adsMckForecastInOutDfDO.setRegionName(v.get(0).getRegionName());
            adsMckForecastInOutDfDO.setCustomHouseTitle(v.get(0).getCustomhouseTitle());

            //中长尾totalCore和预测内均为v，预测外以及未转预约预测为0
            adsMckForecastInOutDfDO.setTotalCore(sum);
            adsMckForecastInOutDfDO.setInForecastTotalCore(sum);
            adsMckForecastInOutDfDO.setOutForecastTotalCore(0);
            adsMckForecastInOutDfDO.setNotApplyTotalCore(0);
            result.add(adsMckForecastInOutDfDO);
        });

        DBList.ckcldStdCrpDBHelper.insertBatchWithoutReturnId(result);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void syncGpuType() {

        List<PplGpuRegionZoneDO> all = demandDBHelper.getAll(PplGpuRegionZoneDO.class,"where gpu_type is not null");
        Map<String, String> gpuMap = all.stream().collect(
                Collectors.toMap(PplGpuRegionZoneDO::getInstanceType, PplGpuRegionZoneDO::getGpuType, (v1, v2) -> v2));



        List<PplItemDO> itemList = demandDBHelper.getAll(PplItemDO.class,
                "where create_time > '2024-04-01' and gpu_type is null and product = 'GPU(裸金属&CVM)'");
        List<PplItemDraftDO> itemDraftList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where create_time > '2024-04-01' and gpu_type is null and product = 'GPU(裸金属&CVM)'");
        List<PplOrderAuditRecordItemDO> auditRecordItemList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                "where create_time > '2024-04-01' and gpu_type is null and product = 'GPU(裸金属&CVM)'");
        List<OrderConsensusDemandDetailDO> consensusItemList = demandDBHelper.getRaw(OrderConsensusDemandDetailDO.class,"select\n"
                + "    *\n"
                + "from order_consensus_demand_detail t1\n"
                + "         left join order_info t2 on t1.order_number = t2.order_number\n"
                + "WHERE t1.deleted = 0 and t2.deleted = 0 and t1.available_status = 'available' AND t2.available_status = 'available'\n"
                + "and t2.order_category = 'GPU' and t1.demand_gpu_type is null");

        OrderQueryReq orderQueryReq = new OrderQueryReq();
        orderQueryReq.setOrderCategory(Arrays.asList("GPU"));
        List<OrderDetailResp> orderDetailResps = orderCommonService.queryOrder(orderQueryReq);
        List<OrderItemDTO> orderItemList = orderDetailResps.stream().filter(v -> ListUtils.isNotEmpty(v.getItemList()))
                .flatMap(v -> v.getItemList().stream()).filter(v -> StringUtils.isBlank(v.getGpuType()))
                .collect(Collectors.toList());

        Set<String> alertList = new HashSet<>();
        for (PplItemDO pplItemDO : itemList) {
            String s = gpuMap.get(pplItemDO.getInstanceType());
            if (StringUtils.isBlank(s)) {
                alertList.add(pplItemDO.getInstanceType());
                continue;
            }
            pplItemDO.setGpuType(gpuMap.get(pplItemDO.getInstanceType()));
        }

        for (PplItemDraftDO itemDraftDO : itemDraftList) {
            String s = gpuMap.get(itemDraftDO.getInstanceType());
            if (StringUtils.isBlank(s)) {
                alertList.add(itemDraftDO.getInstanceType());
                continue;
            }
            itemDraftDO.setGpuType(gpuMap.get(itemDraftDO.getInstanceType()));
        }

        for (PplOrderAuditRecordItemDO auditRecordItemDO : auditRecordItemList) {
            String s = gpuMap.get(auditRecordItemDO.getInstanceType());
            if (StringUtils.isBlank(s)) {
                alertList.add(auditRecordItemDO.getInstanceType());
                continue;
            }
            auditRecordItemDO.setGpuType(gpuMap.get(auditRecordItemDO.getInstanceType()));
        }

        for (OrderItemDTO orderItemDTO : orderItemList) {
            String s = gpuMap.get(orderItemDTO.getInstanceType());
            if (StringUtils.isBlank(s)) {
                alertList.add(orderItemDTO.getInstanceType());
                continue;
            }
            orderItemDTO.setGpuType(gpuMap.get(orderItemDTO.getInstanceType()));
        }

        for (OrderConsensusDemandDetailDO orderConsensusDemandDetailDO : consensusItemList) {
            String s = gpuMap.get(orderConsensusDemandDetailDO.getDemandInstanceType());
            if (StringUtils.isBlank(s)) {
                alertList.add(orderConsensusDemandDetailDO.getDemandInstanceType());
                continue;
            }
            orderConsensusDemandDetailDO.setDemandGpuType(gpuMap.get(orderConsensusDemandDetailDO.getDemandInstanceType()));
        }

        demandDBHelper.update(itemList);
        demandDBHelper.update(itemDraftList);
        demandDBHelper.update(auditRecordItemList);
        demandDBHelper.update(orderItemList);
        demandDBHelper.update(consensusItemList);

        List<String> needGpuTypeConfig = demandDBHelper.getRaw(String.class,
                "select distinct instance_type from ppl_gpu_region_zone where deleted = 0 and gpu_type is null");
        if (ListUtils.isNotEmpty(needGpuTypeConfig)){
            alertList.addAll(needGpuTypeConfig);
        }

        if (ListUtils.isNotEmpty(alertList)) {
            AlarmRobotUtil.doAlarmForComd("syncGpuType", "缺失gpu实例类型配置,请检查" + Strings.join(";", alertList), null,
                    true);
        }
    }

    @Override
    @TaskLog(taskName = "updateGpuConfigByProductGpu")
    @Transactional(value = "demandTransactionManager")
    public void updateGpuConfigByProductGpu() {
        List<PplGpuRegionZoneDO> list = demandDBHelper.getAll(PplGpuRegionZoneDO.class,
                "where original_gpu_type is null");
        List<String> instanceType = list.stream().map(PplGpuRegionZoneDO::getInstanceType).distinct()
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(instanceType)){
            List<ProductGpuConfigDTO> raw = gpuDBHelper.getRaw(ProductGpuConfigDTO.class,
                    "select a.host_type as host_type,instance_family,gpu_type as original_gpu_type,SUBSTRING_INDEX(gpu_type, '-' , 1) as gpu_type,"
                            + " card_type from t_config_host_type a join t_config_ins_host_map b"
                            + " on a.host_type=b.host_type"
                            + " where"
                            + " instance_family in (?) "
                            + " group by  host_type,instance_family,gpu_type,card_type", instanceType);
            for (ProductGpuConfigDTO productGpuConfigDTO : raw) {
                demandDBHelper.executeRaw("update ppl_gpu_region_zone set gpu_type = ?,original_gpu_type = ?"
                                + ",card_type = ? where deleted = 0 and instance_type = ?",
                        productGpuConfigDTO.getGpuType(),productGpuConfigDTO.getOriginalGpuType(),
                        productGpuConfigDTO.getCardType(),productGpuConfigDTO.getInstanceType());
            }
        }

    }


    public int getValue(String key, Map<String, List<AdsMckForecastSummaryDfDO>> map) {
        List<AdsMckForecastSummaryDfDO> adsMckForecastSummaryDfDOS = map.get(key);
        if (adsMckForecastSummaryDfDOS != null) {
            return adsMckForecastSummaryDfDOS.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
        }
        return 0;
    }

    @Override
    public void w13Task() {
        List<CrpCommonHolidayWeekDO> raw = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2024 and month <= 3 order by week asc");
        List<SoeRegionNameCountryDO> region2Country = soeCommonService.getRegion2Country();
        List<CrpCommonHolidayWeekDO> pastWeek = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2023 order by week desc limit 14");
        Map<String, String> instanceTypeToCommonInstanceTypeMap = getInstanceTypeToCommonInstanceTypeMap();
        Map<String, String> regionToCountry = region2Country.stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName,
                        SoeRegionNameCountryDO::getCountryName));
        for (CrpCommonHolidayWeekDO crpCommonHolidayWeekDO : raw) {
            String start = crpCommonHolidayWeekDO.getStart();
            String end = crpCommonHolidayWeekDO.getEnd();
            // 获取预约数据
            List<DwdCrpPplItemCfDO> appliedData = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                    "where status = ? and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? and source in (?) and product = ? and demand_type in (?)",
                    PplItemStatusEnum.APPLIED.getCode(),
                    start, end,
                    Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                            PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode())
                    , Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));

            Map<String, List<DwdCrpPplItemCfDO>> appliedCollect = appliedData.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            // 获取w-13预测数据
            CrpCommonHolidayWeekDO w13 = pastWeek.get(pastWeek.size() - 1);
            PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w13.getEnd());

            List<DwdCrpPplItemVersionCfDO> w13Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    one.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    start, end);
            pastWeek.remove(pastWeek.size() - 1);

            Map<String, List<DwdCrpPplItemVersionCfDO>> w13Collect = w13Data.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            Map<String, W13DataDO> map = new HashMap<>();

            appliedCollect.forEach((k, v) -> {
                W13DataDO w13DataDO = new W13DataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemCfDO dwdCrpPplItemCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemCfDO.getIndustryDept());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemCfDO.getInstanceType()));
                w13DataDO.setAppliedTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                w13DataDO.setVersionCode(one.getVersionCode());
                map.put(k, w13DataDO);
            });

            w13Collect.forEach((k, v) -> {
                W13DataDO w13DataDO = new W13DataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemVersionCfDO dwdCrpPplItemVersionCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemVersionCfDO.getIndustryDept());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemVersionCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemVersionCfDO.getInstanceType()));
                w13DataDO.setW13PredictTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                if (!w13DataDO.getW13PredictTotalCore().equals(0) && !w13DataDO.getAppliedTotalCore().equals(0)) {
                    w13DataDO.setRate(new BigDecimal(w13DataDO.getAppliedTotalCore()).divide(
                            new BigDecimal(w13DataDO.getW13PredictTotalCore()), 4, RoundingMode.HALF_UP));
                }
                w13DataDO.setVersionCode(one.getVersionCode());
                map.put(k, w13DataDO);
            });

            demandDBHelper.insertBatchWithoutReturnId(map.values());
        }

    }


    @Override
    public void addw13CustomerTask() {
        List<CrpCommonHolidayWeekDO> raw = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2024 and month <= 3 order by week asc");
        List<SoeRegionNameCountryDO> region2Country = soeCommonService.getRegion2Country();
        List<CrpCommonHolidayWeekDO> pastWeek = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2023 order by week desc limit 14");
        Map<String, String> instanceTypeToCommonInstanceTypeMap = getInstanceTypeToCommonInstanceTypeMap();
        Map<String, String> regionToCountry = region2Country.stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName,
                        SoeRegionNameCountryDO::getCountryName));
        for (CrpCommonHolidayWeekDO crpCommonHolidayWeekDO : raw) {
            String start = crpCommonHolidayWeekDO.getStart();
            String end = crpCommonHolidayWeekDO.getEnd();
            // 获取预约数据
            List<DwdCrpPplItemCfDO> appliedData = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                    "where status = ? and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? and source in (?) and product = ? and demand_type in (?)",
                    PplItemStatusEnum.APPLIED.getCode(),
                    start, end,
                    Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                            PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode())
                    , Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));

            Map<String, List<DwdCrpPplItemCfDO>> appliedCollect = appliedData.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            v.getCustomerShortName(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            // 获取w-13预测数据
            CrpCommonHolidayWeekDO w13 = pastWeek.get(pastWeek.size() - 1);
            PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w13.getEnd());

            List<DwdCrpPplItemVersionCfDO> w13Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    one.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    start, end);
            pastWeek.remove(pastWeek.size() - 1);

            Map<String, List<DwdCrpPplItemVersionCfDO>> w13Collect = w13Data.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            v.getCustomerShortName(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            Map<String, W13CustomerDataDO> map = new HashMap<>();

            appliedCollect.forEach((k, v) -> {
                W13CustomerDataDO w13DataDO = new W13CustomerDataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemCfDO dwdCrpPplItemCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemCfDO.getIndustryDept());
                w13DataDO.setCustomerShortName(dwdCrpPplItemCfDO.getCustomerShortName());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemCfDO.getInstanceType()));
                w13DataDO.setAppliedTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                w13DataDO.setVersionCode(one.getVersionCode());
                map.put(k, w13DataDO);
            });

            w13Collect.forEach((k, v) -> {
                W13CustomerDataDO w13DataDO = new W13CustomerDataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemVersionCfDO dwdCrpPplItemVersionCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemVersionCfDO.getIndustryDept());
                w13DataDO.setCustomerShortName(dwdCrpPplItemVersionCfDO.getCustomerShortName());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemVersionCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemVersionCfDO.getInstanceType()));
                w13DataDO.setW13PredictTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                if (!w13DataDO.getW13PredictTotalCore().equals(0) && !w13DataDO.getAppliedTotalCore().equals(0)) {
                    w13DataDO.setRate(new BigDecimal(w13DataDO.getAppliedTotalCore()).divide(
                            new BigDecimal(w13DataDO.getW13PredictTotalCore()), 4, RoundingMode.HALF_UP));
                }
                w13DataDO.setVersionCode(one.getVersionCode());
                map.put(k, w13DataDO);
            });

            demandDBHelper.insertBatchWithoutReturnId(map.values());
        }

    }

    @Override
    public void updateW13CustomerTask() {
        List<SoeRegionNameCountryDO> region2Country = soeCommonService.getRegion2Country();
        Map<String, String> instanceTypeToCommonInstanceTypeMap = getInstanceTypeToCommonInstanceTypeMap();
        Map<String, String> regionToCountry = region2Country.stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName,
                        SoeRegionNameCountryDO::getCountryName));
        List<W13CustomerDataDO> all = demandDBHelper.getAll(W13CustomerDataDO.class,
                "where year_month_1 in ('2024-01','2024-02','2024-03') order by week");
        Map<Integer, List<W13CustomerDataDO>> map = all.stream()
                .collect(Collectors.groupingBy(W13CustomerDataDO::getWeek));
        List<CrpCommonHolidayWeekDO> weekDOList = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year in (2023,2024) order by year,week asc");
        List<W13CustomerDataDO> updateList = new ArrayList<>();
        map.forEach((k, v) -> {
            CrpCommonHolidayWeekDO beginWeek = weekDOList.stream().filter(u -> u.getWeek().equals(k)
                    && u.getYear().equals(2024)).collect(Collectors.toList()).get(0);
            CrpCommonHolidayWeekDO w6 = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class, "where id = ?",
                    beginWeek.getId() - 6);
            CrpCommonHolidayWeekDO w5 = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class, "where id = ?",
                    beginWeek.getId() - 5);

            PplVersionDO w6Version = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w6.getEnd());
            PplVersionDO w5Version = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w5.getEnd());

            List<DwdCrpPplItemVersionCfDO> w6Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    w6Version.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    beginWeek.getStart(), beginWeek.getEnd());

            Map<String, List<DwdCrpPplItemVersionCfDO>> w6Collect = w6Data.stream()
                    .collect(Collectors.groupingBy(z -> Strings.join("@", z.getIndustryDept(),
                            z.getCustomerShortName(),
                            instanceTypeToCommonInstanceTypeMap.get(z.getInstanceType()),
                            regionToCountry.get(z.getRegionName()))));

            List<DwdCrpPplItemVersionCfDO> w5Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    w5Version.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    beginWeek.getStart(), beginWeek.getEnd());

            Map<String, List<DwdCrpPplItemVersionCfDO>> w5Collect = w5Data.stream()
                    .collect(Collectors.groupingBy(z -> Strings.join("@", z.getIndustryDept(),
                            z.getCustomerShortName(),
                            instanceTypeToCommonInstanceTypeMap.get(z.getInstanceType()),
                            regionToCountry.get(z.getRegionName()))));

            for (W13CustomerDataDO w13DataDO : v) {
                w13DataDO.setW6PredictTotalCore(0);
                w13DataDO.setW5PredictTotalCore(0);
                List<DwdCrpPplItemVersionCfDO> w6List = w6Collect.get(
                        Strings.join("@", w13DataDO.getIndustryDept(), w13DataDO.getCustomerShortName(),
                                w13DataDO.getCommonInstanceType(),
                                w13DataDO.getConutry()));
                if (ListUtils.isNotEmpty(w6List)) {
                    Integer w6sum = w6List.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();
                    w13DataDO.setW6PredictTotalCore(w6sum);
                }

                List<DwdCrpPplItemVersionCfDO> w5List = w5Collect.get(
                        Strings.join("@", w13DataDO.getIndustryDept(), w13DataDO.getCustomerShortName(),
                                w13DataDO.getCommonInstanceType(),
                                w13DataDO.getConutry()));

                if (ListUtils.isNotEmpty(w5List)) {
                    Integer w5sum = w5List.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();
                    w13DataDO.setW5PredictTotalCore(w5sum);
                }
                updateList.add(w13DataDO);
            }
        });

        List<W13CustomerDataDO> collect = updateList.stream()
                .filter(v -> v.getW5PredictTotalCore() != null || v.getW6PredictTotalCore() != null)
                .collect(Collectors.toList());
        demandDBHelper.update(collect);

    }

    @Override
    public void updateW13Task() {
        List<SoeRegionNameCountryDO> region2Country = soeCommonService.getRegion2Country();
        Map<String, String> instanceTypeToCommonInstanceTypeMap = getInstanceTypeToCommonInstanceTypeMap();
        Map<String, String> regionToCountry = region2Country.stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName,
                        SoeRegionNameCountryDO::getCountryName));
        List<W13DataDO> all = demandDBHelper.getAll(W13DataDO.class,
                "where year_month_1 in ('2024-01','2024-02','2024-03') order by week");
        Map<Integer, List<W13DataDO>> map = all.stream().collect(Collectors.groupingBy(W13DataDO::getWeek));
        List<CrpCommonHolidayWeekDO> weekDOList = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year in (2023,2024) order by year,week asc");
        List<W13DataDO> updateList = new ArrayList<>();
        map.forEach((k, v) -> {
            CrpCommonHolidayWeekDO beginWeek = weekDOList.stream().filter(u -> u.getWeek().equals(k)
                    && u.getYear().equals(2024)).collect(Collectors.toList()).get(0);
            CrpCommonHolidayWeekDO w6 = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class, "where id = ?",
                    beginWeek.getId() - 6);
            CrpCommonHolidayWeekDO w5 = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class, "where id = ?",
                    beginWeek.getId() - 5);

            PplVersionDO w6Version = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w6.getEnd());
            PplVersionDO w5Version = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w5.getEnd());

            List<DwdCrpPplItemVersionCfDO> w6Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    w6Version.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    beginWeek.getStart(), beginWeek.getEnd());

            Map<String, List<DwdCrpPplItemVersionCfDO>> w6Collect = w6Data.stream()
                    .collect(Collectors.groupingBy(z -> Strings.join("@", z.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(z.getInstanceType()),
                            regionToCountry.get(z.getRegionName()))));

            List<DwdCrpPplItemVersionCfDO> w5Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    w5Version.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    beginWeek.getStart(), beginWeek.getEnd());

            Map<String, List<DwdCrpPplItemVersionCfDO>> w5Collect = w5Data.stream()
                    .collect(Collectors.groupingBy(z -> Strings.join("@", z.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(z.getInstanceType()),
                            regionToCountry.get(z.getRegionName()))));

            for (W13DataDO w13DataDO : v) {
                w13DataDO.setW6PredictTotalCore(0);
                w13DataDO.setW5PredictTotalCore(0);
                List<DwdCrpPplItemVersionCfDO> w6List = w6Collect.get(
                        Strings.join("@", w13DataDO.getIndustryDept(), w13DataDO.getCommonInstanceType(),
                                w13DataDO.getConutry()));
                if (ListUtils.isNotEmpty(w6List)) {
                    Integer w6sum = w6List.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();
                    w13DataDO.setW6PredictTotalCore(w6sum);
                }

                List<DwdCrpPplItemVersionCfDO> w5List = w5Collect.get(
                        Strings.join("@", w13DataDO.getIndustryDept(), w13DataDO.getCommonInstanceType(),
                                w13DataDO.getConutry()));

                if (ListUtils.isNotEmpty(w5List)) {
                    Integer w5sum = w5List.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();
                    w13DataDO.setW5PredictTotalCore(w5sum);
                }
                updateList.add(w13DataDO);
            }
        });

        List<W13DataDO> collect = updateList.stream()
                .filter(v -> v.getW5PredictTotalCore() != null || v.getW6PredictTotalCore() != null)
                .collect(Collectors.toList());
        demandDBHelper.update(collect);

    }


    @Override
    public void w13Task456() {
        List<CrpCommonHolidayWeekDO> raw = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2024 and month >= 4 and month <= 6 order by week asc");
        List<SoeRegionNameCountryDO> region2Country = soeCommonService.getRegion2Country();

        CrpCommonHolidayWeekDO extraWeek = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class,
                "where year = 2023  order by week desc limit 1");
        List<CrpCommonHolidayWeekDO> pastWeek = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where year = 2024 and month >= 1 and month <= 3 order by week desc");
        pastWeek.add(extraWeek);
        Map<String, String> instanceTypeToCommonInstanceTypeMap = getInstanceTypeToCommonInstanceTypeMap();
        Map<String, String> regionToCountry = region2Country.stream()
                .collect(Collectors.toMap(SoeRegionNameCountryDO::getRegionName,
                        SoeRegionNameCountryDO::getCountryName));
        for (CrpCommonHolidayWeekDO crpCommonHolidayWeekDO : raw) {
            String start = crpCommonHolidayWeekDO.getStart();
            String end = crpCommonHolidayWeekDO.getEnd();
            // 获取预约数据
            List<DwdCrpPplItemCfDO> appliedData = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                    "where status = ? and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? and source in (?) and product = ? and demand_type in (?)",
                    PplItemStatusEnum.APPLIED.getCode(),
                    start, end,
                    Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
                            PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode())
                    , Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()));

            Map<String, List<DwdCrpPplItemCfDO>> appliedCollect = appliedData.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            // 获取w-13预测数据
            CrpCommonHolidayWeekDO w13 = pastWeek.get(pastWeek.size() - 1);
            PplVersionDO one = demandDBHelper.getOne(PplVersionDO.class,
                    "where end_audit_time > ? order by id asc limit 1", w13.getEnd());

            List<DwdCrpPplItemVersionCfDO> w13Data = DBList.ckcldStdCrpDBHelper.getAll(
                    DwdCrpPplItemVersionCfDO.class, "where version_code = ? and "
                            + " is_comd != 1 and source != ? and product = ? and "
                            + "(customer_short_name !='云运管补充' or (customer_short_name ='云运管补充' and is_lock = 0))"
                            + " and demand_type in (?)"
                            + "and begin_buy_date >= ? and "
                            + "begin_buy_date <= ? ",
                    one.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),
                    Ppl13weekProductTypeEnum.CVM.getName(),
                    Arrays.asList(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode()),
                    start, end);
            pastWeek.remove(pastWeek.size() - 1);

            Map<String, List<DwdCrpPplItemVersionCfDO>> w13Collect = w13Data.stream()
                    .collect(Collectors.groupingBy(v -> Strings.join("@", v.getIndustryDept(),
                            instanceTypeToCommonInstanceTypeMap.get(v.getInstanceType()),
                            regionToCountry.get(v.getRegionName()))));

            System.out.println("hhh");
            Map<String, W13DataDO> map = new HashMap<>();

            appliedCollect.forEach((k, v) -> {
                W13DataDO w13DataDO = new W13DataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemCfDO dwdCrpPplItemCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemCfDO.getIndustryDept());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemCfDO.getInstanceType()));
                w13DataDO.setAppliedTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                w13DataDO.setVersionCode(one.getVersionCode());
                w13DataDO.setFlag("456");
                map.put(k, w13DataDO);
            });

            w13Collect.forEach((k, v) -> {
                W13DataDO w13DataDO = new W13DataDO();
                if (map.get(k) != null) {
                    w13DataDO = map.get(k);
                }
                DwdCrpPplItemVersionCfDO dwdCrpPplItemVersionCfDO = v.get(0);
                Integer sum = v.stream().mapToInt(DwdCrpPplItemVersionCfDO::getTotalCore).sum();

                w13DataDO.setYearMonth(crpCommonHolidayWeekDO.getYear() +
                        "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonthOrDay(
                        crpCommonHolidayWeekDO.getMonth()));
                w13DataDO.setWeek(crpCommonHolidayWeekDO.getWeek());
                w13DataDO.setIndustryDept(dwdCrpPplItemVersionCfDO.getIndustryDept());
                w13DataDO.setConutry(regionToCountry.get(dwdCrpPplItemVersionCfDO.getRegionName()));
                w13DataDO.setCommonInstanceType(
                        instanceTypeToCommonInstanceTypeMap.get(dwdCrpPplItemVersionCfDO.getInstanceType()));
                w13DataDO.setW13PredictTotalCore(sum);
                w13DataDO.setRate(BigDecimal.ZERO);
                if (!w13DataDO.getW13PredictTotalCore().equals(0) && !w13DataDO.getAppliedTotalCore().equals(0)) {
                    w13DataDO.setRate(new BigDecimal(w13DataDO.getAppliedTotalCore()).divide(
                            new BigDecimal(w13DataDO.getW13PredictTotalCore()), 4, RoundingMode.HALF_UP));
                }
                w13DataDO.setVersionCode(one.getVersionCode());
                w13DataDO.setFlag("456");
                map.put(k, w13DataDO);
            });

            demandDBHelper.insertBatchWithoutReturnId(map.values());
        }

    }

    @Override
    public void updateCommonCustomerAuth() {
        List<IndustryDemandAuthDO> all = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where common_customer_name is not null and common_customer_name != ''");
        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class, "where customer_name is not null and customer_name != ''");
        Map<String, String> map = customerDict.stream()
                .collect(Collectors.toMap(IndustryDemandIndustryWarZoneDictDO::getCustomerName,
                        IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName, (v1, v2) -> v2));
        for (IndustryDemandAuthDO industryDemandAuthDO : all) {
            List<String> customerName = Arrays.asList(industryDemandAuthDO.getCommonCustomerName().split(";"));
            Set<String> commonCustomerName = new HashSet<>();
            for (String s : customerName) {
                if (map.get(s) != null) {
                    commonCustomerName.add(map.get(s));
                }
            }
            if (ListUtils.isEmpty(commonCustomerName)) {
                industryDemandAuthDO.setCommonCustomerName("");
            } else {
                industryDemandAuthDO.setCommonCustomerName(Strings.join(";", commonCustomerName));
            }
        }
        demandDBHelper.update(all);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void refreshHeadCustomer() {

        List<HeadCustomer> all = demandDBHelper.getAll(HeadCustomer.class);
        List<IndustryDemandIndustryWarZoneDictDO> result = new ArrayList<>();
        for (HeadCustomer headCustomer : all) {
            List<String> customerShortNameList = new ArrayList<>(
                    Arrays.asList(headCustomer.getCustomerShortName().split(";")));
            for (String s : customerShortNameList) {
                IndustryDemandIndustryWarZoneDictDO industryDemandIndustryWarZoneDictDO = new IndustryDemandIndustryWarZoneDictDO();
                industryDemandIndustryWarZoneDictDO.setIndustry(headCustomer.getIndustryDept());
                industryDemandIndustryWarZoneDictDO.setWarZoneName(headCustomer.getWarZone());
                industryDemandIndustryWarZoneDictDO.setCommonCustomerName(headCustomer.getCommonCustomerShortName());
                industryDemandIndustryWarZoneDictDO.setCustomerName(s);
                industryDemandIndustryWarZoneDictDO.setIsBigCustomer(Boolean.TRUE);
                industryDemandIndustryWarZoneDictDO.setIsEnable(Boolean.TRUE);
                result.add(industryDemandIndustryWarZoneDictDO);
            }
        }
        List<String> customer = result.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                .collect(Collectors.toList());
        demandDBHelper.executeRaw(
                "update industry_demand_industry_war_zone_dict set deleted = 1 where customer_name in (?)", customer);
        demandDBHelper.insertBatchWithoutReturnId(result);

    }

    public Map<String, String> getInstanceTypeToCommonInstanceTypeMap() {
        Map<String, String> instanceConfigMap = new HashMap<>();
        List<Mrpv2CommonInstanceTypeConfigDO> configDOS = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast = 1");
        List<InstanceTypeRelateDTO> instanceTypeRelateDTOList = configDOS.stream().map(configDO -> {
            InstanceTypeRelateDTO relateDTO = new InstanceTypeRelateDTO();
            relateDTO.setCommonInstanceType(
                    org.springframework.util.StringUtils.trimWhitespace(configDO.getCommonInstanceType()));

            relateDTO.setInstanceTypes(
                    Stream.of(configDO.getInstanceTypes().split("[,|，]"))
                            .map(org.springframework.util.StringUtils::trimWhitespace)
                            .collect(Collectors.toList()));

            for (String instanceType : relateDTO.getInstanceTypes()) {
                instanceConfigMap.put(instanceType, relateDTO.getCommonInstanceType());
            }
            return relateDTO;
        }).collect(Collectors.toList());
        return instanceConfigMap;
    }

    public void computeMinMax(List<AdsMckForecastSummaryDfDO> customerData,
            List<AdsMckForecastSummaryDfDO> yunxiaoData) {
        // 根据 可用区 - 客户简称 - 实例类型 - 购买年 - 购买周 分组
        Map<String, List<AdsMckForecastSummaryDfDO>> customerMap = customerData.stream()
                .collect(Collectors.groupingBy(v -> Strings.join("@",
                        Arrays.asList(v.getZoneName(), v.getCustomerShortName(), v.getInstanceType(),
                                v.getBeginBuyYear(),
                                v.getBeginBuyWeek()))));

        Map<String, List<AdsMckForecastSummaryDfDO>> yunxiaoMap = yunxiaoData.stream()
                .collect(Collectors.groupingBy(v -> Strings.join("@",
                        Arrays.asList(v.getZoneName(), v.getCustomerShortName(), v.getInstanceType(),
                                v.getBeginBuyYear(),
                                v.getBeginBuyWeek()))));

        customerMap.forEach((k, v) -> {
            List<AdsMckForecastSummaryDfDO> yunxiao = yunxiaoMap.get(k);
            if (ListUtils.isEmpty(yunxiao)) {
                // 如果预约单没有， 则直接将大客户提报的设置为MAX
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                return;
            }
            Integer yunxiaoSum = yunxiao.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            Integer customerSum = v.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            if (customerSum >= yunxiaoSum) {
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : yunxiao) {
                    adsMckForecastSummaryDfDO.setCompare("MIN");
                }
            } else {
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : yunxiao) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MIN");
                }
            }
            // 已经匹配过的 删除该key
            yunxiaoMap.remove(k);
        });

        yunxiaoMap.forEach((k, v) -> {
            List<AdsMckForecastSummaryDfDO> customer = customerMap.get(k);
            if (ListUtils.isEmpty(customer)) {
                // 如果大客户填报没有， 则直接将预约单的设置为MAX
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                return;
            }
            Integer yunxiaoSum = v.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            Integer customerSum = customer.stream().mapToInt(AdsMckForecastSummaryDfDO::getTotalCore).sum();
            if (customerSum >= yunxiaoSum) {
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : customer) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MIN");
                }
            } else {
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : v) {
                    adsMckForecastSummaryDfDO.setCompare("MAX");
                }
                for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : customer) {
                    adsMckForecastSummaryDfDO.setCompare("MIN");
                }
            }
        });
    }

    public List<AdsMckForecastSummaryDfDO> buildNewOrderData(List<NewOrderDataDTO> newOrderData
            , List<ResPlanHolidayWeekDO> holidayWeekDOList) {
        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        Map<String, StaticZoneDO> regionMap = dictService.getAllPlanZoneInfosGroupByRegion();
        HashSet<String> headCustomer = new HashSet<>(mrpV2DictService.getBigCustomerShortName());
        for (NewOrderDataDTO newOrderDataDTO : newOrderData) {
            AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO = BeanUtil.copyProperties(newOrderDataDTO,
                    AdsMckForecastSummaryDfDO.class);
            adsMckForecastSummaryDfDO.setDemandSource("预约单");
            adsMckForecastSummaryDfDO.setStatTime(LocalDate.now().minusDays(1));
            // 预约单没有退回， 无须判断退回
            if (newOrderDataDTO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                adsMckForecastSummaryDfDO.setTotalCore(-newOrderDataDTO.getTotalCore());
            }
            // 获取周差
            ResPlanHolidayWeekDO submitWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    newOrderDataDTO.getSubmitDate().toString());
            ResPlanHolidayWeekDO beginBuyWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    newOrderDataDTO.getBeginBuyDate().toString());
            adsMckForecastSummaryDfDO.setSubmitYear(submitWeek.getYear());
            adsMckForecastSummaryDfDO.setSubmitWeek(submitWeek.getWeek());
            adsMckForecastSummaryDfDO.setBeginBuyYear(beginBuyWeek.getYear());
            adsMckForecastSummaryDfDO.setBeginBuyWeek(beginBuyWeek.getWeek());
            adsMckForecastSummaryDfDO.setWeekGap(
                    ((beginBuyWeek.getYear() - submitWeek.getYear()) * 52)
                            + beginBuyWeek.getWeek() - submitWeek.getWeek());
            // 设置area_name
            adsMckForecastSummaryDfDO.setAreaName(getAreaName(adsMckForecastSummaryDfDO));

            // 刷新地域的名称 (仅订单需要处理)
            StaticZoneDO staticZoneDO = regionMap.get(newOrderDataDTO.getRegion());
            adsMckForecastSummaryDfDO.setRegionName(staticZoneDO != null ? staticZoneDO.getRegionName() : "");
            adsMckForecastSummaryDfDO.setYunxiaoOrderId(newOrderDataDTO.getOrderNumber());
            adsMckForecastSummaryDfDO.setOrderNodeCode(
                    OrderNodeCodeEnum.getNameByCode(newOrderDataDTO.getOrderNodeCode()));
            //设置是否为大客户
            adsMckForecastSummaryDfDO.setIsHead(
                    headCustomer.contains(adsMckForecastSummaryDfDO.getCustomerShortName()) ? 1 : 0);
            result.add(adsMckForecastSummaryDfDO);
        }
        return result;
    }

    public List<AdsMckForecastSummaryDfDO> buildYunxiaoData(List<YunxiaoOrderDataDTO> yunxiaoList
            , List<ResPlanHolidayWeekDO> holidayWeekDOList) {
        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        HashSet<String> headCustomer = new HashSet<>(mrpV2DictService.getBigCustomerShortName());
        for (YunxiaoOrderDataDTO yunxiaoOrderDataDTO : yunxiaoList) {
            AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO = BeanUtil.copyProperties(yunxiaoOrderDataDTO,
                    AdsMckForecastSummaryDfDO.class);
            adsMckForecastSummaryDfDO.setDemandSource("预约单");
            adsMckForecastSummaryDfDO.setStatTime(LocalDate.now().minusDays(1));
            // 预约单没有退回， 无须判断退回
            if (yunxiaoOrderDataDTO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                adsMckForecastSummaryDfDO.setTotalCore(-yunxiaoOrderDataDTO.getTotalCore());
            }
            // 获取周差
            ResPlanHolidayWeekDO submitWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    yunxiaoOrderDataDTO.getSubmitDate().toString());
            ResPlanHolidayWeekDO beginBuyWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    yunxiaoOrderDataDTO.getBeginBuyDate().toString());
            adsMckForecastSummaryDfDO.setSubmitYear(submitWeek.getYear());
            adsMckForecastSummaryDfDO.setSubmitWeek(submitWeek.getWeek());
            adsMckForecastSummaryDfDO.setBeginBuyYear(beginBuyWeek.getYear());
            adsMckForecastSummaryDfDO.setBeginBuyWeek(beginBuyWeek.getWeek());
            adsMckForecastSummaryDfDO.setWeekGap(
                    ((beginBuyWeek.getYear() - submitWeek.getYear()) * 52)
                            + beginBuyWeek.getWeek() - submitWeek.getWeek());
            //设置area_name
            adsMckForecastSummaryDfDO.setAreaName(getAreaName(adsMckForecastSummaryDfDO));
            //设置是否为头部客户
            adsMckForecastSummaryDfDO.setIsHead(
                    headCustomer.contains(adsMckForecastSummaryDfDO.getCustomerShortName()) ? 1 : 0);
            result.add(adsMckForecastSummaryDfDO);
        }
        return result;
    }

    public List<AdsMckForecastSummaryDfDO> buildLongTailData(List<CustomerForecastDataDTO> longtailList,
            List<ResPlanHolidayWeekDO> holidayWeekDOList) {
        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        String longTailModelTaskSql = ORMUtils.getSql("/sql/ppl13week/mck_data/long_tail_model_task.sql");
        List<LongtailModelTaskDTO> raw = demandDBHelper.getRaw(LongtailModelTaskDTO.class, longTailModelTaskSql);
        Map<String, LongtailModelTaskDTO> pplOrderToModelTask = raw.stream()
                .collect(Collectors.toMap(LongtailModelTaskDTO::getPplOrder, v -> v));
        HashSet<String> headCustomer = new HashSet<>(mrpV2DictService.getBigCustomerShortName());
        for (CustomerForecastDataDTO customerForecastDataDTO : longtailList) {
            AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO = BeanUtil.copyProperties(customerForecastDataDTO,
                    AdsMckForecastSummaryDfDO.class);
            adsMckForecastSummaryDfDO.setDemandSource("中长尾");
            adsMckForecastSummaryDfDO.setStatTime(LocalDate.now().minusDays(1));
            if (customerForecastDataDTO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                adsMckForecastSummaryDfDO.setTotalCore(-customerForecastDataDTO.getTotalCore());
            }
            LongtailModelTaskDTO longtailModelTaskDTO = pplOrderToModelTask.get(customerForecastDataDTO.getPplOrder());
            if (longtailModelTaskDTO != null) {
                adsMckForecastSummaryDfDO.setTaskId(longtailModelTaskDTO.getTaskId());
                adsMckForecastSummaryDfDO.setInputDateBegin(longtailModelTaskDTO.getInputDateBegin());
                adsMckForecastSummaryDfDO.setInputDateEnd(longtailModelTaskDTO.getInputDateEnd());
            }
            // 获取周差
            ResPlanHolidayWeekDO submitWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    customerForecastDataDTO.getSubmitDate().toString());
            ResPlanHolidayWeekDO beginBuyWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    customerForecastDataDTO.getBeginBuyDate().toString());
            adsMckForecastSummaryDfDO.setSubmitYear(submitWeek.getYear());
            adsMckForecastSummaryDfDO.setSubmitWeek(submitWeek.getWeek());
            adsMckForecastSummaryDfDO.setBeginBuyYear(beginBuyWeek.getYear());
            adsMckForecastSummaryDfDO.setBeginBuyWeek(beginBuyWeek.getWeek());
            adsMckForecastSummaryDfDO.setWeekGap(
                    ((beginBuyWeek.getYear() - submitWeek.getYear()) * 52)
                            + beginBuyWeek.getWeek() - submitWeek.getWeek());

            //设置area_name
            adsMckForecastSummaryDfDO.setAreaName(getAreaName(adsMckForecastSummaryDfDO));
            //设置是否为头部客户
            adsMckForecastSummaryDfDO.setIsHead(
                    headCustomer.contains(adsMckForecastSummaryDfDO.getCustomerShortName()) ? 1 : 0);
            result.add(adsMckForecastSummaryDfDO);
        }
        return result;
    }


    public List<AdsMckForecastSummaryDfDO> buildCustomerData(List<CustomerForecastDataDTO> customerList,
            List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList,
            List<ResPlanHolidayWeekDO> holidayWeekDOList) {
        List<AdsMckForecastSummaryDfDO> result = new ArrayList<>();
        Set<String> notFoundZoneRegionSet = new HashSet<>();
        HashSet<String> headCustomer = new HashSet<>(mrpV2DictService.getBigCustomerShortName());
        for (CustomerForecastDataDTO customerForecastDataDTO : customerList) {
            AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO = BeanUtil.copyProperties(customerForecastDataDTO,
                    AdsMckForecastSummaryDfDO.class);
            adsMckForecastSummaryDfDO.setDemandSource("大客户");
            adsMckForecastSummaryDfDO.setStatTime(LocalDate.now().minusDays(1));
            adsMckForecastSummaryDfDO.setZoneNameOriginal(customerForecastDataDTO.getZoneName());

            if (customerForecastDataDTO.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())) {
                adsMckForecastSummaryDfDO.setTotalCore(-customerForecastDataDTO.getTotalCore());
            }

            // 获取周差
            ResPlanHolidayWeekDO submitWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    customerForecastDataDTO.getSubmitDate().toString());
            ResPlanHolidayWeekDO beginBuyWeek = binarySearchHolidayWeek(holidayWeekDOList,
                    customerForecastDataDTO.getBeginBuyDate().toString());
            adsMckForecastSummaryDfDO.setSubmitYear(submitWeek.getYear());
            adsMckForecastSummaryDfDO.setSubmitWeek(submitWeek.getWeek());
            adsMckForecastSummaryDfDO.setBeginBuyYear(beginBuyWeek.getYear());
            adsMckForecastSummaryDfDO.setBeginBuyWeek(beginBuyWeek.getWeek());
            adsMckForecastSummaryDfDO.setWeekGap(
                    ((beginBuyWeek.getYear() - submitWeek.getYear()) * 52)
                            + beginBuyWeek.getWeek() - submitWeek.getWeek());

            // 随机可用区相关处理
            if (customerForecastDataDTO.getZoneName().equals("随机可用区")) {
                List<String> zoneNameList = getZone(customerForecastDataDTO.getRegionName(), zoneNameConfigDOList);
                if (ListUtils.isEmpty(zoneNameList)) {
                    if (!customerForecastDataDTO.getRegionName().equals("随机地域")) {
                        notFoundZoneRegionSet.add(customerForecastDataDTO.getRegionName());
                    }
                    continue;
                } else if (zoneNameList.size() == 1) {
                    adsMckForecastSummaryDfDO.setZoneName(zoneNameList.get(0));
                } else {
                    // 如果存在多个，则需将一条PPL进行分摊成多条
                    adsMckForecastSummaryDfDO.setTotalCore(
                            adsMckForecastSummaryDfDO.getTotalCore() / zoneNameList.size());
                    // 卡数分摊
                    adsMckForecastSummaryDfDO.setTotalGpuNum(
                            ObjectUtils.defaultIfNull(adsMckForecastSummaryDfDO.getTotalGpuNum(), 0)
                                    / zoneNameList.size());
                    for (int i = 0; i < zoneNameList.size() - 1; i++) {
                        AdsMckForecastSummaryDfDO otherZone = BeanUtil.copyProperties(
                                adsMckForecastSummaryDfDO, AdsMckForecastSummaryDfDO.class);
                        otherZone.setZoneName(zoneNameList.get(i));
                        otherZone.setAreaName(getAreaName(otherZone));
                        otherZone.setIsHead(headCustomer.contains(otherZone.getCustomerShortName()) ? 1 : 0);
                        result.add(otherZone);
                    }
                    // 放入最后一个
                    adsMckForecastSummaryDfDO.setZoneName(zoneNameList.get(zoneNameList.size() - 1));
                }
            }
            adsMckForecastSummaryDfDO.setAreaName(getAreaName(adsMckForecastSummaryDfDO));
            //按照新的客户分类设置是否为头部客户
            adsMckForecastSummaryDfDO.setIsHead(
                    headCustomer.contains(adsMckForecastSummaryDfDO.getCustomerShortName()) ? 1 : 0);
            result.add(adsMckForecastSummaryDfDO);
        }
        if (ListUtils.isNotEmpty(notFoundZoneRegionSet)) {
            AlarmRobotUtil.doAlarm("buildCustomerData",
                    Strings.join(";", notFoundZoneRegionSet) + "找不到有效的可用区", null, false);
        }
        return result;
    }

    private String getAreaName(AdsMckForecastSummaryDfDO otherZone) {
        //先通过zone_name获取area_name
        Map<String, StaticZoneDO> allPlanZoneInfosGroupByName = dictService.getAllPlanZoneInfosGroupByName();
        String zoneName = otherZone.getZoneName();
        if (zoneName != null) {
            StaticZoneDO staticZoneDO = allPlanZoneInfosGroupByName.get(zoneName);
            if (staticZoneDO == null) {
                //使用region_name查
                Map<String, StaticZoneDO> allRegion = dictService.getAllPlanZoneInfosGroupByRegionName();
                if (otherZone.getRegionName() != null) {
                    StaticZoneDO zd = allRegion.get(otherZone.getRegionName());
                    if (zd != null) {
                        return zd.getAreaName();
                    }
                }
            } else {
                return staticZoneDO.getAreaName();
            }
        }
        return null;
    }


    public ResPlanHolidayWeekDO binarySearchHolidayWeek(List<ResPlanHolidayWeekDO> list, String date) {
        int left = 0;
        int right = list.size() - 1;
        while (left <= right) {
            int mid = left + (right - left) / 2;
            ResPlanHolidayWeekDO midDO = list.get(mid);
            if (midDO.contains(date)) {
                return midDO;
            } else if (midDO.isBefore(date)) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }
        return null;
    }

    private List<String> getZone(String regionName, List<InventoryHealthMainZoneNameConfigDO> zoneNameConfigDOList) {
        // 先看看主力园区有没有
        List<InventoryHealthMainZoneNameConfigDO> principalList = zoneNameConfigDOList.stream()
                .filter(v -> v.getRegionName().equals(regionName) && v.getTypeName()
                        .equals(InventoryHealthZoneType.PRINCIPAL.getName())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(principalList)) {
            return principalList.stream().map(InventoryHealthMainZoneNameConfigDO::getZoneName).distinct()
                    .collect(Collectors.toList());
        }

        // 再看看辅助园区有没有
        List<InventoryHealthMainZoneNameConfigDO> secondryList = zoneNameConfigDOList.stream()
                .filter(v -> v.getRegionName().equals(regionName) && v.getTypeName()
                        .equals(InventoryHealthZoneType.SECONDARY.getName())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(secondryList)) {
            return secondryList.stream().map(InventoryHealthMainZoneNameConfigDO::getZoneName).distinct()
                    .collect(Collectors.toList());
        }

        // 最后看看待收敛园区有没有
        List<InventoryHealthMainZoneNameConfigDO> withdrawingList = zoneNameConfigDOList.stream()
                .filter(v -> v.getRegionName().equals(regionName) && v.getTypeName()
                        .equals(InventoryHealthZoneType.WITHDRAWING.getName())).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(withdrawingList)) {
            return withdrawingList.stream().map(InventoryHealthMainZoneNameConfigDO::getZoneName).distinct()
                    .collect(Collectors.toList());
        }

        // 如果都没有 则直接取所有可用区进行分摊
        List<InventoryHealthMainZoneNameConfigDO> allZone = zoneNameConfigDOList.stream()
                .filter(v -> v.getRegionName().equals(regionName)).collect(
                        Collectors.toList());
        if (ListUtils.isNotEmpty(allZone)) {
            return allZone.stream().map(InventoryHealthMainZoneNameConfigDO::getZoneName).distinct()
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();

    }

    public Boolean checkExist(String instanceModel) {
        PplItemDraftDO one = demandDBHelper.getOne(PplItemDraftDO.class, "where instance_model = ?", instanceModel);
        if (one != null) {
            return Boolean.TRUE;
        }

        PplOrderAuditRecordItemDO two = demandDBHelper.getOne(PplOrderAuditRecordItemDO.class,
                "where instance_model = ?", instanceModel);
        if (two != null) {
            return Boolean.TRUE;
        }

        PplVersionGroupRecordItemDO three = demandDBHelper.getOne(PplVersionGroupRecordItemDO.class,
                "where instance_model = ?", instanceModel);
        if (three != null) {
            return Boolean.TRUE;
        }

        PplItemDO four = demandDBHelper.getOne(PplItemDO.class,
                "where instance_model = ?", instanceModel);
        if (four != null) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }
}
