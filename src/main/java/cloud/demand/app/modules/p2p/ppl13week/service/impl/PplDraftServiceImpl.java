package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.enums.OrderAvailableStatusEnum;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.RegionDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.ArchitectPermission;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.PplChangeEffectConsensusDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.PplIdListConsensusSupplyPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.SupplyPlanDetailVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.SupplyPlanGroupField;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.BatchAdjustPplOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ImportDataToDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplDraftItemJoinDraftOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerProcessVersionSlaDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplRelateEnumDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplApplyRematchDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeAllFieldDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeRecordNewDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplOrderItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplOrderRequiredDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplOverviewDo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryExpiredDemandReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.RefreshExpiredReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.RequiredDemandRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.SavePplDraftRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.InstanceTypeRelateDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PaasProductUinWhiteListDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessNodeDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionSlaDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderExpiredRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplOrderBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CpqTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.DraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderAuditStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderExpiredOperateReasonEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderExpiredOperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderInputStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessAttributeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.event.PplChangeEvent;
import cloud.demand.app.modules.p2p.ppl13week.service.PplChangeRecordService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplImportService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderDraftVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderVo;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderWithPplItemVO;
import cloud.demand.app.modules.tencent_cloud_utils.AccountUtil;
import cloud.demand.app.modules.tencent_cloud_utils.CommonUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.chrono.ChronoLocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplDraftServiceImpl implements PplDraftService {

    @Autowired
    PplCommonService pplCommonService;
    @Autowired
    PplImportService pplImportService;
    @Autowired
    PplInnerProcessService pplInnerProcessService;
    @Autowired
    PplInnerVersionService innerVersionService;
    @Resource
    private DBHelper demandDBHelper;
    @Autowired
    private PplDictService pplDictService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplConsensusService pplConsensusService;

    @Resource
    private PplChangeRecordService pplChangeRecordService;

    @Resource
    DictService dictService;

    @Resource
    OrderCommonService orderCommonService;

//    static ExecutorService executor = Executors.newFixedThreadPool(20);

    private static final ScheduledExecutorService scheduledExecutor = Executors.newScheduledThreadPool(2);

    @Override
    public void checkSubmit(SavePplDraftReq req) {
        String userName = LoginUtils.getUserNameWithSystem();

        if (OperateTypeEnum.getByCode(req.getType()) == OperateTypeEnum.INSERT) {
            val pplNo = Strings.isNotBlank(req.getPplOrder()) ? req.getPplOrder()
                    : pplCommonService.generatePplOrderId("N");
            req.setPplOrder(pplNo);
            //只有新增的时候才会可能出现新增客户的场景
            CustomerTypeEnum type = CustomerTypeEnum.getByCode(req.getCustomerType());
            // 新增草稿时，移除审批信息（前端复制导致的）
            req.setModifyReason(null);
            if (type == CustomerTypeEnum.WIN_BACK) {
                log.info("新增客户");
                if (Strings.isBlank(req.getWarZone())) {
                    throw new WrongWebParameterException("战区为空");
                }
                IndustryDemandIndustryWarZoneDictDO raw = demandDBHelper.getOne(
                        IndustryDemandIndustryWarZoneDictDO.class,
                        "where war_zone_name =?", req.getWarZone());
                if (raw == null) {
                    throw new WrongWebParameterException("本地未配置战区中心");
                }
                req.setCustomerSource("");
                req.setIndustry("");
                req.setCustomerName(req.getCustomerShortName());
            } else {
                log.info("使用存量 UIN 查询客户信息");
                fillCustomerInfoByUin(req);
            }
        } else {
            log.info("使用 ppl order 填充数据");
            if (Strings.isBlank(req.getPplOrder())) {
                throw new WrongWebParameterException("PPL order 为空！");
            }
            PplOrderDO pplOrderDO = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order =?", req.getPplOrder());
            if (pplOrderDO == null) {
                throw new WrongWebParameterException("PPL order 不存在！");
            }
            // 审批过程中也可以修改，下面的校验移除
//            if (Strings.isNotBlank(pplOrderDO.getNodeCode())) {
//                throw new WrongWebParameterException("PPL order 正在流程中不允许重复调整！" + pplOrderDO.getPplOrder());
//            }
            if (Strings.isNotBlank(req.getCustomerUin())) {
                if (!pplOrderDO.getCustomerUin().equals(req.getCustomerUin())) {
                    throw new WrongWebParameterException("PPL order uin 和提交数据不一致！");
                }
            }
            req.setCustomerName(pplOrderDO.getCustomerName());
            req.setCustomerShortName(pplOrderDO.getCustomerShortName());
            req.setCenter(pplOrderDO.getCenter());
            req.setWarZone(pplOrderDO.getWarZone());
            req.setIndustry(pplOrderDO.getIndustry());
            req.setIndustryDept(pplOrderDO.getIndustryDept());
        }

        // 时间检查
        LocalDate bDate = LocalDate.parse(req.getBeginBuyDate());
        LocalDate eDate = LocalDate.parse(req.getEndBuyDate());

        if (bDate.isAfter(eDate)) {
            throw new WrongWebParameterException("结束时间比开始时间早！");
        }
//        LocalDate nextMonth = LocalDate.now().plusMonths(1);
//        LocalDate nextMonthDay1 = LocalDate.of(nextMonth.getYear(), nextMonth.getMonthValue(), 1);
//        if (bDate.isBefore(nextMonthDay1)) {
//            throw new WrongWebParameterException("只能提下个月的单据");
//        }

        req.getResources().forEach(row -> {
            row.setBeginBuyDate(req.getBeginBuyDate());
            row.setEndBuyDate(req.getEndBuyDate());
            gpuCheck(row);
        });

        if (IndustryDeptEnum.INNER_DEPT.getName().equals(req.getIndustryDept())) {
            // 内部业务部校验规则
            innerIndustryCheck(req);
        }

        if (permissionService.checkIsAdmin(userName)) {
            return;
        }
        PplInnerProcessVersionDO versionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(
                req.getIndustryDept(), req.getProduct());
        WhereContent draftSql = pplInnerProcessService.checkPermission(userName, req.getIndustryDept(), "",
                versionDO.getId());
        if (draftSql == null) {
            // 无任何审批权限
            if (req.getIndustryDept().equals("智慧行业一部")) {
                // 校验customerUin
                if (Strings.isNotBlank(req.getCustomerUin())) {
                    if (!pplDictService.checkUinAndArch(req.getCustomerUin(), userName)) {
                        throw new WrongWebParameterException("当前架构师未关联上该uin,请在磐石上关联");
                    }
                }
            }
//            else {
//                String userByRole = permissionService.getUserByRole(
//                        IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE.getCode(),
//                        req.getIndustryDept(), req.getProduct(), req.getWarZone());
//                if (!userByRole.contains(userName)) {
//                    throw new BizException(
//                            "当前用户没有" + req.getIndustryDept() + "-" + req.getWarZone() + "的需求权限");
//                }
//            }
        }

    }

    public void innerIndustryCheck(SavePplDraftReq req) {

        List<String> checkUinProduct = Arrays.asList(Ppl13weekProductTypeEnum.ES.getName(),
                Ppl13weekProductTypeEnum.CDW.getName(), Ppl13weekProductTypeEnum.CS.getName(),
                Ppl13weekProductTypeEnum.DLC.getName());
        if (checkUinProduct.contains(req.getProduct())) {

            if (CustomerTypeEnum.WIN_BACK.getCode().equals(req.getCustomerType())) {
                throw new BizException("内部业务部，客户类型仅允许录入： 存量客户");
            }

            if (StringUtils.isBlank(req.getCustomerUin())) {
                throw new BizException("内部业务部，uin为必填");
            }

            List<PaasProductUinWhiteListDO> all = demandDBHelper.getAll(PaasProductUinWhiteListDO.class,
                    "where product = ?", req.getProduct());

            if (ListUtils.isEmpty(all)) {
                throw new BizException("当前提交的产品未配置uin白名单,请联系 mimengyang 配置");
            }
            List<String> uin = all.stream().map(PaasProductUinWhiteListDO::getUin).distinct()
                    .collect(Collectors.toList());
            if (!uin.contains(req.getCustomerUin())) {
                throw new BizException("当前提交的产品uin未在 " + req.getProduct() + " 白名单中，"
                        + "当前可用uin为: " + String.join(";", uin) + " 如有疑问请咨询 mimengyang");
            }
        }

    }

    private void gpuCheck(DraftItemDTO resource) {
        if (resource == null) {
            return;
        }

        if (resource.getSaleDurationYear() != null
                && resource.getSaleDurationYear().remainder(new BigDecimal("0.5")).compareTo(BigDecimal.ZERO) != 0) {
            throw new WrongWebParameterException("包销时长(年)最小填写单位为0.5");
        }
        if (resource.getApplyDiscount() != null && (new BigDecimal("10").compareTo(resource.getApplyDiscount()) < 0
                || new BigDecimal("0.1").compareTo(resource.getApplyDiscount()) > 0)) {
            throw new WrongWebParameterException("申请折扣范围值[0.1,10]");
        }
        if (StringUtils.isNotBlank(resource.getBusinessCpq())
                && CpqTypeEnum.getByCode(resource.getBusinessCpq()) == null) {
            throw new WrongWebParameterException("商务进展编码识别有误，请联系erickssu检查");
        }

    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public SavePplDraftRsp saveDraft(SavePplDraftReq req) {
        log.info("保存草稿 {}", req.getPplOrder());
        if (Strings.isBlank(req.getDraftStatus())) {
            throw new BizException("ppl单状态不能为空");
        }
        req.check();
        if (req.getDraftStatus().equals(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode())) {
            PplInnerProcessVersionDO versionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(
                    req.getIndustryDept(), req.getProduct());
            PplInnerProcessVersionSlaDTO enterNode = innerVersionService.getEnterNode(versionDO.getId());
            if (enterNode.getDeadlineTime() != null && enterNode.getDeadlineTime().before(new Date())) {
                // 如果已经截止录入
                throw new BizException("已到达版本截止录入时间，无法提交数据到需求沟通中");
            }

            LocalDate beginYearMonth = LocalDate.parse(
                    versionDO.getDemandBeginYear() + "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonth(
                            versionDO.getDemandBeginMonth()) + "-01");
            if (LocalDate.parse(req.getBeginBuyDate()).isBefore(beginYearMonth)) {
                throw new BizException("本周期" + req.getProduct() + " 产品可录月份为"
                        + versionDO.getDemandBeginYear() + "年" + versionDO.getDemandBeginMonth() + "月之后"
                        + " 请校正购买日期后再提交。");
            }
        }

        // 若草稿单的明细为已预约数据，则不允许编辑此草稿单的明细
        List<String> pplIds = req.getResources().stream().map(DraftItemDTO::getPplId)
                .filter(StringUtils::isNotEmpty).collect(
                        Collectors.toList());
        List<String> draftPplIds = demandDBHelper.getAll(PplItemDraftDO.class,
                        "where ppl_order =? and draft_status = ? ",
                        req.getPplOrder(), req.getDraftStatus()).stream().map(PplItemDraftDO::getPplId)
                .collect(
                        Collectors.toList());

        // 战略客户部除外，以导入数据为准
        if (!"战略客户部".equals(req.getIndustryDept())) {
            List<String> appliedPplIds = demandDBHelper.getAll(PplItemDO.class, "where ppl_order = ? and status = ? ",
                    req.getPplOrder(), PplItemStatusEnum.APPLIED.getCode()).stream().map(PplItemDO::getPplId).collect(
                    Collectors.toList());
            List<String> appliedExistDraftIds = draftPplIds.stream().filter(appliedPplIds::contains).collect(
                    Collectors.toList());
            log.info("saveDraft, 行业-{}, 保存草稿时忽略已预约明细pplIds-{}", req.getIndustryDept(),
                    appliedExistDraftIds);
            pplIds.removeAll(appliedExistDraftIds);
            draftPplIds.removeAll(appliedExistDraftIds);
        }

        List<DraftItemDTO> noAppliedItemDTOS = req.getResources().stream()
                .filter(e -> StringUtils.isBlank(e.getPplId()) || pplIds.contains(e.getPplId())).collect(
                        Collectors.toList());

        List<PplItemDraftDO> items = noAppliedItemDTOS.stream().map(resource -> {
            PplItemDraftDO pplItemDO = new PplItemDraftDO();
            pplItemDO.setPplOrder(req.getPplOrder());
            pplItemDO.setProduct(resource.getProduct());
            if (Strings.isBlank(resource.getPplId())) {
                String pplId = pplCommonService.generatePplItemId(req.getPplOrder());
                resource.setPplId(pplId);
            }
            pplItemDO.setPplId(resource.getPplId());
            pplItemDO.setParentPplId(resource.getParentPplId());
            pplItemDO.setStatus(PplItemStatusEnum.VALID.getCode());
            pplItemDO.setDemandType(resource.getDemandType());
            pplItemDO.setDemandScene(resource.getDemandScene());
            pplItemDO.setProjectName(resource.getProjectName());
            pplItemDO.setBillType(resource.getBillType());
            pplItemDO.setImportantDemand(resource.getImportantDemand());
            pplItemDO.setWinRate(resource.getWinRate());
            pplItemDO.setBeginBuyDate(DateUtils.parseLocalDate(resource.getBeginBuyDate()));
            pplItemDO.setEndBuyDate(DateUtils.parseLocalDate(resource.getEndBuyDate()));
            pplItemDO.setBeginElasticDate(DateUtils.parseLocalTime(resource.getBeginElasticDate()));
            pplItemDO.setEndElasticDate(DateUtils.parseLocalTime(resource.getEndElasticDate()));
            pplItemDO.setNote(resource.getNote());
            pplItemDO.setRegionName(resource.getRegionName());
            pplItemDO.setIsStrongDesignateZone(resource.getIsStrongDesignateZone());
            pplItemDO.setZoneName(resource.getZoneName());
            pplItemDO.setInstanceType(resource.getInstanceType());
            pplItemDO.setInstanceModel(resource.getInstanceModel());
            pplItemDO.setInstanceNum(resource.getInstanceNum());
            pplItemDO.setAlternativeInstanceType(Strings.join(";", resource.getAlternativeInstanceType()));
            if (ListUtils.isNotEmpty(resource.getAlternativeZoneName())) {
                pplItemDO.setAlternativeZoneName(String.join(";", resource.getAlternativeZoneName()));
            }else {
                pplItemDO.setAlternativeZoneName("");
            }
            pplItemDO.setAffinityType(resource.getAffinityType());
            pplItemDO.setAffinityValue(resource.getAffinityValue());
            pplItemDO.setSystemDiskType(resource.getSystemDiskType());
            pplItemDO.setSystemDiskStorage(resource.getSystemDiskStorage());
            pplItemDO.setSystemDiskNum(resource.getSystemDiskNum());
            pplItemDO.setDataDiskType(resource.getDataDiskType());
            pplItemDO.setDataDiskStorage(resource.getDataDiskStorage());
            pplItemDO.setDataDiskNum(resource.getDataDiskNum());
            pplItemDO.setCbsIo(resource.getCbsIo());
            pplItemDO.setTotalCore(resource.getTotalCoreNum());
            pplItemDO.setTotalDisk(resource.getTotalDiskNum());
            pplItemDO.setDraftStatus(req.getDraftStatus());
            pplItemDO.setType(resource.getType());
            pplItemDO.setBizId(resource.getBizId());
            pplItemDO.setAppRole(resource.getAppRole());
            // gpu相关字段
            pplItemDO.setGpuProductType(resource.getGpuProductType());
            pplItemDO.setGpuType(resource.getGpuType());
            pplItemDO.setGpuNum(resource.getGpuNum());
            pplItemDO.setIsAcceptAdjust(Boolean.FALSE);
            if (!CollectionUtils.isEmpty(resource.getAcceptGpu())) {
                pplItemDO.setAcceptGpu(Strings.join(";", resource.getAcceptGpu()));
                pplItemDO.setIsAcceptAdjust(resource.getIsAcceptAdjust());
            }
            pplItemDO.setTotalGpuNum(resource.getTotalGpuNum());
            pplItemDO.setBizScene(resource.getBizScene());
            pplItemDO.setBizDetail(resource.getBizDetail());
            pplItemDO.setServiceTime(resource.getServiceTime());
            pplItemDO.setSaleDurationYear(resource.getSaleDurationYear());
            pplItemDO.setBusinessCpq(resource.getBusinessCpq());
            pplItemDO.setApplyDiscount(resource.getApplyDiscount());
            pplItemDO.placementGroupSet(resource.getPlacementGroupList());

            pplItemDO.setDatabaseName(resource.getDatabaseName());
            pplItemDO.setMoreThanOneAZ(resource.getMoreThanOneAZ());
            pplItemDO.setDatabaseStorageType(resource.getDatabaseStorageType());
            pplItemDO.setDeployType(resource.getDeployType());
            pplItemDO.setFrameworkType(resource.getFrameworkType());
            pplItemDO.setSliceNum(resource.getSliceNum());
            pplItemDO.setReplicaNum(resource.getReplicaNum());
            pplItemDO.setReadOnlyNum(resource.getReadOnlyNum());
            pplItemDO.setDatabaseSpecs(resource.getDatabaseSpecs());
            pplItemDO.setDatabaseStorage(resource.getDatabaseStorage());
            pplItemDO.setTotalDatabaseStorage(resource.getTotalDatabaseStorage());

            pplItemDO.setCosStorageType(resource.getCosStorageType());
            pplItemDO.setCosAZ(resource.getCosAZ());
            pplItemDO.setCosStorage(resource.getCosStorage());
            pplItemDO.setTotalCosStorage(resource.getTotalCosStorage());
            pplItemDO.setBandwidth(resource.getBandwidth());
            pplItemDO.setQps(resource.getQps());

            pplItemDO.setInstanceModelCoreNum(resource.getInstanceModelCoreNum());
            pplItemDO.setInstanceModelRamNum(resource.getInstanceModelRamNum());
            pplItemDO.setTotalMemory(resource.getTotalMemory());

            return pplItemDO;
        }).collect(Collectors.toList());

        // 保存历史记录
        WhereContent queryContent = new WhereContent();
        queryContent.andEqualIfValueNotEmpty(PplOrderDraftVO::getPplOrder, req.getPplOrder());
        queryContent.andEqual(PplOrderDraftVO::getDraftStatus, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        PplOrderDraftVO historyPplOrder = demandDBHelper.getOne(PplOrderDraftVO.class,
                queryContent.getSql(), queryContent.getParams());
        Map<String, PplItemDraftDO> historyItemDOMap;
        if (historyPplOrder != null) {
            historyItemDOMap = historyPplOrder.getPplItemDrafts().stream()
                    .collect(Collectors.toMap(PplItemDraftDO::getPplId, Function.identity(), (o1, o2) -> o1));
        } else {
            historyItemDOMap = new HashMap<>();
        }

        log.info("清空对应草稿数据 插入新数据");
        List<String> noAppliedPplIds = items.stream().filter(e -> StringUtils.isNotEmpty(e.getPplId()))
                .map(PplItemDraftDO::getPplId).collect(
                        Collectors.toList());
        draftPplIds.addAll(noAppliedPplIds);
        removeDraft(Lists.newArrayList(req.getPplOrder()), draftPplIds, false, req.getDraftStatus());

        //单据部分
        PplOrderDraftDO orderDraftDO = req.transTo();
        orderDraftDO.setSubmitUser(req.getSubmitUser());
        orderDraftDO.setDraftStatus(req.getDraftStatus());
        //明细部分
        if (!CollectionUtils.isEmpty(items)) {
            demandDBHelper.insert(items);
        }
        demandDBHelper.insert(orderDraftDO);

        val rsp = new SavePplDraftRsp();
        rsp.setPplOrder(orderDraftDO.getPplOrder());
        rsp.setPplItemDraftDOList(items);
        rsp.setOrderDraftDO(orderDraftDO);
        rsp.setPplIds(
                items.stream().map(PplItemDraftDO::getPplId).filter(Objects::nonNull).collect(Collectors.toList()));

        // 记录日志
        if (req.getIsRecord()) {
            List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
            List<PplItemChangeRecordNewDTO> updateDTOs = new ArrayList<>();
            items.forEach(itemDraft -> {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();

                PplItemDraftDO historyPplItemDO = historyItemDOMap.get(itemDraft.getPplId());
                if (historyPplItemDO == null) {
                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote(req.getModifyReason());
                    newDTOs.add(recordNewDTO);
                } else {
                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(historyPplOrder, beforeAllFieldDTO);
                    BeanUtils.copyProperties(historyPplItemDO, beforeAllFieldDTO);
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(
                            StringUtils.isBlank(itemDraft.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                    : itemDraft.getStatus());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote(req.getModifyReason());
                    updateDTOs.add(recordNewDTO);
                }
            });
            if (!CollectionUtils.isEmpty(newDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                PplRecordChangeEventEnum.PPL_IMPORT_ADD.getCode(), LoginUtils.getUserNameWithSystem(),
                                newDTOs));
            }
            if (!CollectionUtils.isEmpty(updateDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                PplRecordChangeEventEnum.PPL_IMPORT_EDIT.getCode(), LoginUtils.getUserNameWithSystem(),
                                updateDTOs));
            }
        }
        // 修改PPL触发共识状态变化
        effectConsensus(items, req.getIndustryDept(), LoginUtils.getUserNameWithSystem());
        return rsp;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public SavePplDraftRsp saveToNextVersionInApproval(SavePplDraftReq req) {
        // 仅行业审批时才允许修改保存为下期生效
        PplInnerProcessVersionDO current = innerVersionService.queryProcessingVersionByDeptAndProduct(
                req.getIndustryDept(), req.getProduct());
        PplInnerProcessVersionSlaDTO enterNode = innerVersionService.getEnterNode(current.getId());
        if (enterNode.getDeadlineTime() != null && enterNode.getDeadlineTime().after(new Date())) {
            throw new BizException("需求沟通中，无法修改保存为下期生效数据");
        }
        // 在下周期录入范围内（下周期需要审批时）才允许修改保存为下期生效
        PplInnerProcessVersionDO nextVersion = innerVersionService.nextVersion(current);
        if (nextVersion == null || nextVersion.getDemandBeginYear() == null
                || nextVersion.getDemandBeginMonth() == null) {
            throw new BizException("未配置下周期录入版本或录入范围，无法修改保存为下期生效数据");
        }
        LocalDate nextVersionDemandBeginDate = YearMonth.of(nextVersion.getDemandBeginYear(),
                nextVersion.getDemandBeginMonth()).atDay(1);
        LocalDate beginBuyDate = DateUtils.parseLocalDate(req.getBeginBuyDate());
        if (beginBuyDate == null || beginBuyDate.isBefore(nextVersionDemandBeginDate)) {
            throw new BizException("开始购买日期不在下周期版本录入范围内，无法修改保存为下期生效数据");
        }


        // 先保存为下期生效
        req.setIsRecord(false);
        req.setDraftStatus(PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        SavePplDraftRsp rsp = saveDraft(req);
        // 审批流中的PPL标识为下期生效
        List<PplOrderAuditRecordItemDO> auditRecorItemList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                "where audit_record_id in (select max(id) from ppl_order_audit_record where "
                        + "deleted = 0 and version_id = ? and ppl_order = ? group by ppl_order)",
                current.getId(), rsp.getPplOrder());
        if (ListUtils.isNotEmpty(auditRecorItemList)) {
            List<PplOrderAuditRecordItemDO> updateList = new ArrayList<>();
            for (PplOrderAuditRecordItemDO itemDO : auditRecorItemList) {
                PplOrderAuditRecordItemDO updateItem = new PplOrderAuditRecordItemDO();
                updateItem.setId(itemDO.getId());
                // 审批流中的PPL标识为下期生效
                updateItem.setModifyNextVersionValid(true);
                updateList.add(updateItem);
            }
            demandDBHelper.update(updateList);
        }
        // PPL变化日志
        logPplSaveToNextVersion(req, current, rsp.getPplItemDraftDOList(), rsp.getOrderDraftDO());
        return rsp;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void removeNextVersionValid(PplListVo ppl) {
        // 仅行业审批时才能撤销下期生效
        PplInnerProcessVersionDO current = innerVersionService.queryProcessingVersionByDeptAndProduct(
                ppl.getIndustryDept(), ppl.getProduct());
        PplInnerProcessVersionSlaDTO enterNode = innerVersionService.getEnterNode(current.getId());
        if (enterNode.getDeadlineTime() != null && enterNode.getDeadlineTime().after(new Date())) {
            throw new BizException("仅行业审批时才能撤销下期生效");
        }
        List<PplItemDraftDO> pplItemDraftDOList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where ppl_order = ? and draft_status = ?",
                ppl.getPplOrder(), PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        // 已转订单的，不允许撤销下期生效
        for (PplItemDraftDO pplItemDraftDO : pplItemDraftDOList) {
            if (Strings.isNotBlank(pplItemDraftDO.getYunxiaoOrderId())) {
                throw new BizException("已转订单的PPL，不允许撤销下期生效");
            }
        }
        PplOrderDraftDO orderDraftDO = demandDBHelper.getOne(PplOrderDraftDO.class,
                "where ppl_order = ? and draft_status = ?",
                ppl.getPplOrder(), PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        if (DraftStatusEnum.INSERT.getCode().equals(orderDraftDO.getSource())) {
            // 新增下期生效的，改为草稿状态
            demandDBHelper.executeRaw(" update ppl_item_draft set draft_status = ? "
                            + " where ppl_order = ? and draft_status = ? and deleted = 0",
                    PplOrderDraftStatusEnum.DRAFT.getCode(),
                    ppl.getPplOrder(),
                    PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
            demandDBHelper.executeRaw(" update ppl_order_draft set draft_status = ? "
                            + " where ppl_order = ? and draft_status = ? and deleted = 0",
                    PplOrderDraftStatusEnum.DRAFT.getCode(),
                    ppl.getPplOrder(),
                    PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
            // 撤销 新增下期生效PPL 的变化日志
            logRemoveNextVersionValidForAdd(pplItemDraftDOList, orderDraftDO);
        } else {
            // 本周期修改下期生效的，直接删除
            demandDBHelper.delete(PplItemDraftDO.class, "where ppl_order = ? and draft_status = ? and id in (?)",
                    ppl.getPplOrder(), PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode(),
                    ListUtils.transform(pplItemDraftDOList, BaseDO::getId));
            demandDBHelper.delete(PplOrderDraftDO.class, "where ppl_order = ? and draft_status = ? and id = ?",
                    ppl.getPplOrder(), PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode(), orderDraftDO.getId());

            // 审批流中的PPL下期生效的标识移除
            List<PplOrderAuditRecordItemDO> auditRecorItemList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                    "where audit_record_id in (select max(id) from ppl_order_audit_record where "
                            + "deleted = 0 and version_id = ? and ppl_order = ? group by ppl_order)",
                    current.getId(), ppl.getPplOrder());
            if (ListUtils.isNotEmpty(auditRecorItemList)) {
                List<PplOrderAuditRecordItemDO> updateList = new ArrayList<>();
                for (PplOrderAuditRecordItemDO itemDO : auditRecorItemList) {
                    PplOrderAuditRecordItemDO updateItem = new PplOrderAuditRecordItemDO();
                    updateItem.setId(itemDO.getId());
                    // 审批流中的PPL下期生效的标识移除
                    updateItem.setModifyNextVersionValid(false);
                    updateList.add(updateItem);
                }
                demandDBHelper.update(updateList);
            }

            // 撤销 已修改下期生效PPL的变化日志
            logRemoveNextVersionValidForModify(current, pplItemDraftDOList, orderDraftDO);
        }
    }

    private void logRemoveNextVersionValidForAdd(List<PplItemDraftDO> pplItemDraftDOList, PplOrderDraftDO orderDraftDO) {
        if (ListUtils.isEmpty(pplItemDraftDOList)) {
            return;
        }
        List<PplItemChangeRecordNewDTO> changeRecordNewDTOS = new ArrayList<>();
        pplItemDraftDOList.forEach(pplItem -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
            BeanUtils.copyProperties(pplItem, afterAllFieldDTO);
            afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            recordNewDTO.setOperateNote("撤销新增的下期生效");
            changeRecordNewDTOS.add(recordNewDTO);
        });
        String user = LoginUtils.getUserNameWithSystem();
        if (!CollectionUtils.isEmpty(changeRecordNewDTOS)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD",
                            PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_NEXT_VERSION_VALID_WITHDRAW.getCode(),
                            user, changeRecordNewDTOS));
        }
    }

    private void logRemoveNextVersionValidForModify(PplInnerProcessVersionDO currentVersion,
            List<PplItemDraftDO> pplItemDraftDOList, PplOrderDraftDO orderDraftDO) {
        // 审批时的PPL数据
        List<PplOrderAuditRecordItemDO> recordItemDOList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                "where audit_record_id = ("
                        + "select max(id) from ppl_order_audit_record where ppl_order = ? and version_id = ?) ",
                orderDraftDO.getPplOrder(), currentVersion.getId());
        PplOrderDO currentPplOrder = demandDBHelper.getOne(PplOrderDO.class,
                "where ppl_order = ?", orderDraftDO.getPplOrder());
        Map<String, PplItemDraftDO> historyItemDOMap = ListUtils.toMap(pplItemDraftDOList,
                PplItemBaseDO::getPplId, Function.identity());
        if (ListUtils.isNotEmpty(recordItemDOList)) {
            List<PplItemChangeRecordNewDTO> changeRecordNewDTOS = new ArrayList<>();
            recordItemDOList.forEach(pplItem -> {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();

                PplItemDraftDO historyPplItemDO = historyItemDOMap.get(pplItem.getPplId());
                if (historyPplItemDO == null) {
                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(currentPplOrder, afterAllFieldDTO);
                    BeanUtils.copyProperties(pplItem, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote("撤销修改的下期生效");
                    changeRecordNewDTOS.add(recordNewDTO);
                } else {
                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, beforeAllFieldDTO);
                    BeanUtils.copyProperties(historyPplItemDO, beforeAllFieldDTO);
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(currentPplOrder, afterAllFieldDTO);
                    BeanUtils.copyProperties(pplItem, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(
                            StringUtils.isBlank(pplItem.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                    : pplItem.getStatus());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote("撤销修改的下期生效");
                    changeRecordNewDTOS.add(recordNewDTO);
                }
            });
            String user = LoginUtils.getUserNameWithSystem();
            if (!CollectionUtils.isEmpty(changeRecordNewDTOS)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                PplRecordChangeEventEnum.PPL_NEXT_VERSION_VALID_WITHDRAW.getCode(),
                                user, changeRecordNewDTOS));
            }
        }
    }

    private void logPplSaveToNextVersion(SavePplDraftReq req, PplInnerProcessVersionDO currentVersion,
            List<PplItemDraftDO> pplItemDraftDOList, PplOrderDraftDO orderDraftDO) {
        // 审批时的PPL数据
        List<PplOrderAuditRecordItemDO> oldItemDOList = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class,
                "where audit_record_id = ("
                        + "select max(id) from ppl_order_audit_record where ppl_order = ? and version_id = ?) ",
                req.getPplOrder(), currentVersion.getId());
        PplOrderDO historyPplOrder = demandDBHelper.getOne(PplOrderDO.class,
                "where ppl_order = ?", req.getPplOrder());
        Map<String, PplOrderAuditRecordItemDO> historyItemDOMap = ListUtils.toMap(oldItemDOList,
                PplItemBaseDO::getPplId, Function.identity());
        if (ListUtils.isNotEmpty(pplItemDraftDOList)) {
            List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
            List<PplItemChangeRecordNewDTO> updateDTOs = new ArrayList<>();
            pplItemDraftDOList.forEach(itemDraft -> {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();

                PplOrderAuditRecordItemDO historyPplItemDO = historyItemDOMap.get(itemDraft.getPplId());
                if (historyPplItemDO == null) {
                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(PplItemStatusEnum.VALID.getCode());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote(req.getModifyReason());
                    newDTOs.add(recordNewDTO);
                } else {
                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(historyPplOrder, beforeAllFieldDTO);
                    BeanUtils.copyProperties(historyPplItemDO, beforeAllFieldDTO);
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    afterAllFieldDTO.setStatus(
                            StringUtils.isBlank(itemDraft.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                    : itemDraft.getStatus());
                    recordNewDTO.setAfterItem(afterAllFieldDTO);
                    recordNewDTO.setOperateNote(req.getModifyReason());
                    updateDTOs.add(recordNewDTO);
                }
            });
            String user = LoginUtils.getUserNameWithSystem();
            if (!CollectionUtils.isEmpty(newDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                PplRecordChangeEventEnum.PPL_NEXT_VERSION_VALID_ADD.getCode(),
                                user, newDTOs));
            }
            if (!CollectionUtils.isEmpty(updateDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                PplRecordChangeEventEnum.PPL_NEXT_VERSION_VALID_EDIT.getCode(),
                                user, updateDTOs));
            }
        }
    }

    /**
     * 修改PPL触发共识状态变化
     *
     * @param draftDOList 修改后的ppl明细信息，必须是同一个部门的ppl
     * @param industryDept ppl所在的部门
     * @param userName 修改ppl的操作人
     */
    private void effectConsensus(List<PplItemDraftDO> draftDOList, String industryDept, String userName) {
        if (StringUtils.isBlank(industryDept) || ListUtils.isEmpty(draftDOList)) {
            return;
        }
        Map<String, List<PplChangeEffectConsensusDTO>> productPplMap = new HashMap<>();
        draftDOList.forEach(itemDraft -> {
            if (itemDraft == null || Strings.isBlank(itemDraft.getProduct())) {
                return;
            }
            List<PplChangeEffectConsensusDTO> list = productPplMap.get(itemDraft.getProduct());
            if (list == null) {
                list = new ArrayList<>();
            }
            PplChangeEffectConsensusDTO consensusDTO = PplChangeEffectConsensusDTO.from(itemDraft);
            list.add(consensusDTO);
            productPplMap.put(itemDraft.getProduct(), list);
        });
        if (ListUtils.isEmpty(productPplMap)) {
            return;
        }
        for (Entry<String, List<PplChangeEffectConsensusDTO>> entry : productPplMap.entrySet()) {
            List<PplChangeEffectConsensusDTO> pplList = entry.getValue();
            String product = entry.getKey();
            if (ListUtils.isNotEmpty(pplList)) {
                // 修改PPL触发共识状态变化
                PplInnerProcessVersionDO innerVersion = innerVersionService
                        .queryProcessingVersionByDeptAndProduct(industryDept, product);
                String consensusVersionCode = innerVersion == null ? null : innerVersion.getConsensusVersion();
                pplConsensusService.updateConsensusInfoForPplChanged(pplList, consensusVersionCode, userName);
            }
        }
    }

    /**
     * 批量保存草稿
     *
     * @param draftReqs （草稿单据&名细列表）列表
     * @param draftStatus 草稿单状态（保存至草稿箱-PplOrderDraftStatusEnum.DRAFT.getCode()；
     *         保存至需求沟通-PplOrderDraftStatusEnum.PRE_SUBMIT.getCode()）
     * @param isRecord 是否需要记录ppl变化追踪
     * @param changeEvent 记录变化追踪时，传递具体的变更事件（传null时方法自行用默认事件）
     * @param noteByOrderMap 记录变化追踪时，传递Map[pplOrder, 具体的操作备注]（传null时方法自行用默认备注）
     * @param dateByOrderMap 新增需求为延期需求时，记录其原始需求的开始购买日期
     */
    @Override
    @Transactional(value = "demandTransactionManager")
    public void batchSaveDraft(List<SavePplDraftReq> draftReqs, String draftStatus, Boolean isRecord,
            String changeEvent, Map<String, String> noteByOrderMap, Map<String, LocalDate> dateByOrderMap) {
        if (isRecord == null) {
            isRecord = Boolean.TRUE;
        }
        List<String> pplOrders = draftReqs.stream().map(SavePplDraftReq::getPplOrder).collect(
                Collectors.toList());
        log.info("batchSaveDraft-批量保存草稿, pplOrders-{}", pplOrders);

        List<PplItemDraftDO> beforeDraftPplList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where draft_status = ? and ppl_order in (?)", draftStatus, pplOrders);
        Map<String, PplItemDraftDO> pplIdToDraftItem = beforeDraftPplList.stream()
                .collect(Collectors.toMap(PplItemDraftDO::getPplId, v -> v));

        //  明细部分 item
        List<PplItemDraftDO> draftDOList = new ArrayList<>();
        Map<SavePplDraftReq, List<PplItemDraftDO>> reqDraftMap = new HashMap<>();

        List<SavePplDraftReq> insertReq = draftReqs.stream().filter(req -> StringUtils.isBlank(req.getPplOrder()))
                .collect(Collectors.toList());
        List<String> newPplOrderList = new ArrayList<>();
        if (ListUtils.isNotEmpty(insertReq)) {
            newPplOrderList = pplCommonService.generatePplOrderId("N", insertReq.size());
        }

//        Map<String, Integer> orderToIdNum = newPplOrderList.stream().collect(Collectors.toMap(o -> o, o -> 1));
//        Map<String, List<String>> newPplIdMap = pplCommonService.batchGeneratePplItemId(orderToIdNum);

        int flag = 0;
        for (int i = 0; i < draftReqs.size(); i++) {
            SavePplDraftReq draftReq = draftReqs.get(i);
            if (StringUtils.isBlank(draftReq.getPplOrder())){
                draftReq.setPplOrder(newPplOrderList.get(flag));
                flag++;
            }
            List<PplItemDraftDO> items = draftReq.getResources().stream().map(resource -> {
                PplItemDraftDO pplItemDraftDO = new PplItemDraftDO();
                pplItemDraftDO.setPplOrder(draftReq.getPplOrder());
                pplItemDraftDO.setProduct(resource.getProduct());
                pplItemDraftDO.setStatus(resource.getStatus());
                pplItemDraftDO.setYunxiaoOrderId(resource.getYunxiaoOrderId());
                pplItemDraftDO.setYunxiaoDetailId(resource.getYunxiaoDetailId());
                pplItemDraftDO.setOrderNumberId(resource.getOrderNumberId());
                pplItemDraftDO.setTotalCoreApplyAfter(resource.getAfterNewApplyTotalCore());
                pplItemDraftDO.setTotalGpuNumApplyAfter(resource.getAfterNewApplyTotalGpuNum());
                if (Strings.isBlank(resource.getPplId())) {
//                    String pplId = newPplIdMap.get(draftReq.getPplOrder()).get(0);
                    String pplId = pplCommonService.generatePplItemId(draftReq.getPplOrder());
                    resource.setPplId(pplId);
                }
                pplItemDraftDO.setPplId(resource.getPplId());
                pplItemDraftDO.setParentPplId(resource.getParentPplId());
                pplItemDraftDO.setDemandType(resource.getDemandType());
                pplItemDraftDO.setDemandScene(resource.getDemandScene());
                pplItemDraftDO.setProjectName(resource.getProjectName());
                pplItemDraftDO.setBillType(resource.getBillType());
                pplItemDraftDO.setImportantDemand(resource.getImportantDemand());
                pplItemDraftDO.setWinRate(resource.getWinRate());
                pplItemDraftDO.setBeginBuyDate(DateUtils.parseLocalDate(resource.getBeginBuyDate()));
                pplItemDraftDO.setEndBuyDate(DateUtils.parseLocalDate(resource.getEndBuyDate()));
                pplItemDraftDO.setBeginElasticDate(DateUtils.parseLocalTime(resource.getBeginElasticDate()));
                pplItemDraftDO.setEndElasticDate(DateUtils.parseLocalTime(resource.getEndElasticDate()));
                pplItemDraftDO.setNote(resource.getNote());
                pplItemDraftDO.setRegionName(resource.getRegionName());
                pplItemDraftDO.setZoneName(resource.getZoneName());
                pplItemDraftDO.setInstanceType(resource.getInstanceType());
                pplItemDraftDO.setInstanceModel(resource.getInstanceModel());
                pplItemDraftDO.setInstanceNum(resource.getInstanceNum());
                pplItemDraftDO.setAlternativeInstanceType(Strings.join(";", resource.getAlternativeInstanceType()));
                if (ListUtils.isNotEmpty(resource.getAlternativeZoneName())) {
                    pplItemDraftDO.setAlternativeZoneName(String.join(";", resource.getAlternativeZoneName()));
                }else {
                    pplItemDraftDO.setAlternativeZoneName("");
                }
                pplItemDraftDO.setAffinityType(resource.getAffinityType());
                pplItemDraftDO.setAffinityValue(resource.getAffinityValue());
                pplItemDraftDO.setSystemDiskType(resource.getSystemDiskType());
                pplItemDraftDO.setSystemDiskStorage(resource.getSystemDiskStorage());
                pplItemDraftDO.setSystemDiskNum(resource.getSystemDiskNum());
                pplItemDraftDO.setDataDiskType(resource.getDataDiskType());
                pplItemDraftDO.setDataDiskStorage(resource.getDataDiskStorage());
                pplItemDraftDO.setDataDiskNum(resource.getDataDiskNum());
                pplItemDraftDO.setTotalCore(resource.getTotalCoreNum());
                pplItemDraftDO.setTotalDisk(resource.getTotalDiskNum());
                pplItemDraftDO.setDraftStatus(draftStatus);
                pplItemDraftDO.setType(resource.getType());
                pplItemDraftDO.setBizId(resource.getBizId());
                pplItemDraftDO.setAppRole(resource.getAppRole());
                pplItemDraftDO.setCbsIo(resource.getCbsIo());

                LocalDate expiredBeginBuyDate = null;
                if (dateByOrderMap != null) {
                    expiredBeginBuyDate = dateByOrderMap.get(pplItemDraftDO.getPplOrder());
                }
                pplItemDraftDO.setExpiredBeginBuyDate(expiredBeginBuyDate);

                // gpu相关字段
                pplItemDraftDO.setGpuProductType(resource.getGpuProductType());
                pplItemDraftDO.setGpuType(resource.getGpuType());
                pplItemDraftDO.setGpuNum(resource.getGpuNum());
                pplItemDraftDO.setIsAcceptAdjust(Boolean.FALSE);
                if (!CollectionUtils.isEmpty(resource.getAcceptGpu())) {
                    pplItemDraftDO.setAcceptGpu(Strings.join(";", resource.getAcceptGpu()));
                    pplItemDraftDO.setIsAcceptAdjust(resource.getIsAcceptAdjust());
                }
                pplItemDraftDO.setTotalGpuNum(resource.getTotalGpuNum());
                pplItemDraftDO.setBizScene(resource.getBizScene());
                pplItemDraftDO.setBizDetail(resource.getBizDetail());
                pplItemDraftDO.setServiceTime(resource.getServiceTime());
                pplItemDraftDO.setSaleDurationYear(resource.getSaleDurationYear());
                pplItemDraftDO.setBusinessCpq(resource.getBusinessCpq());
                pplItemDraftDO.setApplyDiscount(resource.getApplyDiscount());
                pplItemDraftDO.placementGroupSet(resource.getPlacementGroupList());

                pplItemDraftDO.setDatabaseName(resource.getDatabaseName());
                pplItemDraftDO.setMoreThanOneAZ(resource.getMoreThanOneAZ());
                pplItemDraftDO.setDatabaseStorageType(resource.getDatabaseStorageType());
                pplItemDraftDO.setDeployType(resource.getDeployType());
                pplItemDraftDO.setFrameworkType(resource.getFrameworkType());
                pplItemDraftDO.setSliceNum(resource.getSliceNum());
                pplItemDraftDO.setReplicaNum(resource.getReplicaNum());
                pplItemDraftDO.setReadOnlyNum(resource.getReadOnlyNum());
                pplItemDraftDO.setDatabaseSpecs(resource.getDatabaseSpecs());
                pplItemDraftDO.setDatabaseStorage(resource.getDatabaseStorage());
                pplItemDraftDO.setTotalDatabaseStorage(resource.getTotalDatabaseStorage());

                pplItemDraftDO.setCosStorageType(resource.getCosStorageType());
                pplItemDraftDO.setCosAZ(resource.getCosAZ());
                pplItemDraftDO.setCosStorage(resource.getCosStorage());
                pplItemDraftDO.setTotalCosStorage(resource.getTotalCosStorage());
                pplItemDraftDO.setBandwidth(resource.getBandwidth());
                pplItemDraftDO.setQps(resource.getQps());

                pplItemDraftDO.setInstanceModelCoreNum(resource.getInstanceModelCoreNum());
                pplItemDraftDO.setInstanceModelRamNum(resource.getInstanceModelRamNum());
                pplItemDraftDO.setTotalMemory(resource.getTotalMemory());

                return pplItemDraftDO;
            }).collect(Collectors.toList());

            reqDraftMap.put(draftReq, items);
            draftDOList.addAll(items);
        }

        log.info("清空对应草稿预提交数据 插入新数据");

        if (PplOrderDraftStatusEnum.PRE_SUBMIT.getCode().equals(draftStatus)) {
            removePreSubmitDraft(pplOrders);
        } else {
            removeDraft(pplOrders);
        }

        //单据部分 order
        List<PplOrderDraftDO> orderDraftDOList = new ArrayList<>();
        draftReqs.forEach(req -> {
            PplOrderDraftDO orderDraftDO = req.transTo();
            orderDraftDO.setSubmitUser(req.getSubmitUser());
            orderDraftDO.setDraftStatus(draftStatus);
            if (PplRecordChangeEventEnum.PPL_EXPIRED_COPY_ADD.getCode().equals(changeEvent)) {
                orderDraftDO.setInputStatus(PplOrderInputStatusEnum.DELAY.getCode());
            }
            orderDraftDOList.add(orderDraftDO);
        });

        log.info("batchSaveDraft, 插入ppl_item_draft, pplIds-{}",
                draftDOList.stream().map(PplItemDraftDO::getPplId).collect(Collectors.toList()));
        log.info("batchSaveDraft, 插入ppl_order_draft, pplOrders-{}",
                orderDraftDOList.stream().map(PplOrderDraftDO::getPplOrder).collect(
                        Collectors.toList()));

        demandDBHelper.insertBatchWithoutReturnId(draftDOList);
        demandDBHelper.insertBatchWithoutReturnId(orderDraftDOList);

        if (isRecord) {
            List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
            List<PplItemChangeRecordNewDTO> updateDTOs = new ArrayList<>();
            Map<String, PplOrderDraftDO> orderToOrderDO = orderDraftDOList.stream()
                    .collect(Collectors.toMap(PplOrderDraftDO::getPplOrder, v -> v));
            draftDOList.forEach(itemDraft -> {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                PplOrderDraftDO orderDraftDO = orderToOrderDO.get(itemDraft.getPplOrder());
                PplItemDraftDO historyItemDraft = pplIdToDraftItem.get(itemDraft.getPplId());
                if (historyItemDraft == null) {
                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    recordNewDTO.setAfterItem(afterAllFieldDTO);

                    if (noteByOrderMap != null) {
                        recordNewDTO.setOperateNote(noteByOrderMap.get(itemDraft.getPplOrder()));
                    }
                    newDTOs.add(recordNewDTO);
                } else {

                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, beforeAllFieldDTO);
                    BeanUtils.copyProperties(historyItemDraft, beforeAllFieldDTO);
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);

                    PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                    BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                    recordNewDTO.setAfterItem(afterAllFieldDTO);

                    if (noteByOrderMap != null) {
                        recordNewDTO.setOperateNote(noteByOrderMap.get(itemDraft.getPplOrder()));
                    }
                    updateDTOs.add(recordNewDTO);
                }
            });

            if (!CollectionUtils.isEmpty(newDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                changeEvent == null ? PplRecordChangeEventEnum.PPL_IMPORT_ADD.getCode() : changeEvent,
                                LoginUtils.getUserNameWithSystem(),
                                newDTOs));
            }
            if (!CollectionUtils.isEmpty(updateDTOs)) {
                SpringUtil.getApplicationContext().publishEvent(
                        new PplChangeEvent(new Object(), "CHANGE_RECORD",
                                PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                                changeEvent == null ? PplRecordChangeEventEnum.PPL_IMPORT_EDIT.getCode() : changeEvent,
                                LoginUtils.getUserNameWithSystem(),
                                updateDTOs));
            }
        }
        String userName = LoginUtils.getUserNameWithSystem();
        List<PplItemDraftDO> effectConsensusItemList = new ArrayList<>();
        for (Entry<SavePplDraftReq, List<PplItemDraftDO>> entry : reqDraftMap.entrySet()) {
            SavePplDraftReq req = entry.getKey();
            if (OperateTypeEnum.INSERT.getCode().equals(req.getType())){
                continue;
            }
            List<PplItemDraftDO> list = entry.getValue();
            if (ListUtils.isEmpty(list)) {
                continue;
            }
            effectConsensusItemList.addAll(list);
        }
        // 修改PPL触发共识状态变化
        scheduledExecutor.schedule(
                () -> effectConsensus(effectConsensusItemList, draftReqs.get(0).getIndustryDept(), userName),
                5, TimeUnit.SECONDS);
    }


    @Override
    public void removeDraft(List<String> pplOrders, boolean force) {
        log.info("删除草稿信息{}", pplOrders);
        if (pplOrders.isEmpty()) {
            return;
        }
//        if (force) {
//            demandDBHelper.turnOffSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
        demandDBHelper.delete(PplItemDraftDO.class, "where ppl_order in (?) and draft_status = ?", pplOrders,
                PplOrderDraftStatusEnum.DRAFT.getCode());
        demandDBHelper.delete(PplOrderDraftDO.class, "where ppl_order in (?) and draft_status = ?", pplOrders,
                PplOrderDraftStatusEnum.DRAFT.getCode());
//        if (force) {
//            demandDBHelper.turnOnSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
    }

    @Override
    public void removeDraft(List<String> pplOrders, List<String> pplIds, boolean force, String draftStatus) {
        log.info("删除草稿信息, pplOrders-{}, pplIds-{}", pplOrders, pplIds);
        if (pplOrders.isEmpty()) {
            return;
        }
//        if (force) {
//            demandDBHelper.turnOffSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
        if (!CollectionUtils.isEmpty(pplIds)) {
            demandDBHelper.delete(PplItemDraftDO.class, "where ppl_order in (?) and ppl_id in (?) and draft_status = ?",
                    pplOrders, pplIds, draftStatus);
        }
        demandDBHelper.delete(PplOrderDraftDO.class, "where ppl_order in (?) and draft_status = ?", pplOrders,
                draftStatus);
//        if (force) {
//            demandDBHelper.turnOnSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void removePreSubmitDraft(List<String> pplOrders, boolean force) {
        log.info("删除草稿预提交信息{}", pplOrders);
        if (pplOrders.isEmpty()) {
            return;
        }
//        if (force) {
//            demandDBHelper.turnOffSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
        demandDBHelper.delete(PplItemDraftDO.class, "where ppl_order in (?) and draft_status = ?", pplOrders,
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        demandDBHelper.delete(PplOrderDraftDO.class, "where ppl_order in (?) and draft_status = ?", pplOrders,
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
//        if (force) {
//            demandDBHelper.turnOnSoftDelete(PplItemDraftDO.class, PplOrderDraftDO.class);
//        }
    }

    @Override
    @Transactional("demandTransactionManager")
    public void preSubmitDraft(String industryDept, List<String> pplOrders) {
        if (ListUtils.isEmpty(pplOrders)) {
            return;
        }
        // 1、前置数据查询
        List<PplInnerProcessVersionDO> versionDOList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where industry_dept = ? and status = ?  group by product ",
                industryDept, PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        List<Long> processVersionIds = versionDOList.stream().map(PplInnerProcessVersionDO::getId).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(versionDOList)) {
            throw new WrongWebParameterException("当前行业暂无进行中审批版本！");
        }

        Map<Long, PplInnerProcessVersionDO> versionDOMap = versionDOList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionDO::getId, Function.identity()));
        Map<Long, List<String>> productProcessVersionMap = versionDOList.stream().collect(
                Collectors.toMap(PplInnerProcessVersionDO::getId, e -> Arrays.asList(e.getProduct().split(";"))));

        WhereContent whereContent = new WhereContent();
        whereContent.andIn(PplInnerProcessVersionSlaDO::getVersionId, processVersionIds);
        whereContent.andEqual(PplInnerProcessVersionSlaDO::getDeadlineType, "ENTER");
        Map<Long, PplInnerProcessVersionSlaDO> enterSlaDOMap = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                        whereContent.getSql(), whereContent.getParams())
                .stream().collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getVersionId, Function.identity()));

        // 草稿、下期生效的 可以预提交
        List<String> toPreSubmitStatus = ListUtils.newArrayList(PplOrderDraftStatusEnum.DRAFT.getCode(),
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        List<PplItemDraftDO> draftDOList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where draft_status in (?) and ppl_order in (?)",
                toPreSubmitStatus, pplOrders);

        // 2、过滤出 在审批版本的需求沟通截止日期之前 可以提交的草稿单
        List<String> forbidSubmitProducts = new ArrayList<>();
        enterSlaDOMap.forEach((k, v) -> {
            if (v.getDeadlineTime() != null && v.getDeadlineTime().before(new Date())) {
                // 此产品 的审批版本 配置显示，当前月份已截止需求提交，结束需求沟通环节，无法预提交，等待下月初开启
                forbidSubmitProducts.addAll(productProcessVersionMap.get(k));
            }
        });
        // 在录入截止时间之后的草稿单，提交到下期数据
        List<PplItemDraftDO> nextVersionDrafts = new ArrayList<>();
        draftDOList = draftDOList.stream()
                .filter(e -> {
                    if (forbidSubmitProducts.contains(e.getProduct())) {
                        nextVersionDrafts.add(e);
                        return false;
                    } else {
                        return true;
                    }
                })
                .collect(Collectors.toList());

        List<PplItemChangeRecordNewDTO> toNextLogs = new ArrayList<>();
        List<PplItemChangeRecordNewDTO> toCurrentLogs = new ArrayList<>();
        if (ListUtils.isNotEmpty(nextVersionDrafts)) {
            // 需要提交到下一周期
            List<PplItemChangeRecordNewDTO> logs = submitDraftToNextVersion(versionDOList, nextVersionDrafts);
            if (ListUtils.isNotEmpty(logs)) {
                toNextLogs = logs;
            }
        }

        if (ListUtils.isNotEmpty(draftDOList)) {
            // 预提交到当前周期
            List<PplItemChangeRecordNewDTO> logs = preSubmitDraft(draftDOList, productProcessVersionMap, versionDOMap);
            if (ListUtils.isNotEmpty(logs)) {
                toCurrentLogs = logs;
            }
        }

        if (ListUtils.isNotEmpty(toNextLogs)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_NEXT_VERSION_VALID_SUBMITTED.getCode(), LoginUtils.getUserNameWithSystem(),
                            toNextLogs));
        }
        if (ListUtils.isNotEmpty(toCurrentLogs)) {
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_IMPORT_SUBMITTED.getCode(), LoginUtils.getUserNameWithSystem(),
                            toCurrentLogs));
        }
    }

    private List<PplItemChangeRecordNewDTO> preSubmitDraft(List<PplItemDraftDO> draftDOList,
            Map<Long, List<String>> productProcessVersionMap, Map<Long, PplInnerProcessVersionDO> versionDOMap) {
        if (ListUtils.isEmpty(draftDOList)) {
            return new ArrayList<>();
        }
        List<String> pplOrders = draftDOList.stream().map(PplItemDraftDO::getPplOrder).collect(Collectors.toList());
        // 3、检查提交的草稿单 是否存在 当前版本配置的需求年月之前的明细，存在则抛错提示
        Map<String, List<PplItemDraftDO>> draftDOsByProduct = draftDOList.stream()
                .collect(Collectors.groupingBy(PplItemDraftDO::getProduct));
        productProcessVersionMap.forEach((versionId, products) -> {
            PplInnerProcessVersionDO versionDO = versionDOMap.get(versionId);
            List<PplItemDraftDO> submitDraftDOs = new ArrayList<>();
            products.forEach(e -> {
                List<PplItemDraftDO> draftDOs = draftDOsByProduct.get(e);
                if (!CollectionUtils.isEmpty(draftDOs)) {
                    submitDraftDOs.addAll(draftDOs);
                }
            });

            LocalDateTime beginYearMonth = DateUtils.parseLocalDateTime(
                    versionDO.getDemandBeginYear() + "-" + versionDO.getDemandBeginMonth() + "-" + "01 00:00:00");
            LocalDateTime endYearMonth = DateUtils.parseLocalDateTime(
                    versionDO.getDemandEndYear() + "-" + versionDO.getDemandEndMonth() + "-" + "01 00:00:00");

            Map<String, List<PplItemDraftDO>> pplOrderToItemMap = submitDraftDOs.stream()
                    .filter(v -> v.getBeginBuyDate().isBefore(ChronoLocalDate.from(beginYearMonth)))
                    .collect(Collectors.groupingBy(PplItemDraftDO::getPplOrder));
            StringBuilder pplOrderMonthDetail = new StringBuilder();
            for (String pplOrder : pplOrderToItemMap.keySet()) {
                PplItemDraftDO itemDraftDO = pplOrderToItemMap.get(pplOrder).get(0);
                String yearMonth =
                        itemDraftDO.getBeginBuyDate().getYear() + "年" + itemDraftDO.getBeginBuyDate().getMonthValue()
                                + "月\n";
                pplOrderMonthDetail.append(pplOrder).append(" 的需求月份为").append(yearMonth);
            }
            if (Strings.isNotBlank(pplOrderMonthDetail.toString())) {
                throw new BizException("本周期" + versionDO.getProduct() + " 产品可录月份为"
                        + beginYearMonth.getYear() + "年" + beginYearMonth.getMonthValue() + "月 ～ "
                        + endYearMonth.getYear() + "年" + endYearMonth.getMonthValue() + "月"
                        + "。您的数据中: \n"
                        + pplOrderMonthDetail
                        + " 请校正后再提交。(如您需要顺延需求，可在列表中复制PPL后修改需求日期)");
            }


        });

        // 将现在的 草稿、下期生效的 改为预提交单据
        List<String> toPreSubmitStatus = ListUtils.newArrayList(PplOrderDraftStatusEnum.DRAFT.getCode(),
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        // 4、开始真正提交草稿单操作
        WhereContent queryContent = new WhereContent();
        queryContent.andIn(PplOrderDraftVO::getDraftStatus, toPreSubmitStatus);
        queryContent.andInIfValueNotEmpty(PplOrderDraftVO::getPplOrder, pplOrders);
        List<PplOrderDraftVO> beforeDraftPpls = demandDBHelper.getAll(PplOrderDraftVO.class,
                queryContent.getSql(), queryContent.getParams());
        // 草稿、下期生效状态的PPL状态改为 预提交
        Set<String> draftOrders = ListUtils.toSet(beforeDraftPpls, PplOrderBaseDO::getPplOrder);

        // 如果该单据在原来已有，则先直接删除
        demandDBHelper.executeRaw("update ppl_order_draft set deleted = 1 " +
                        "where ppl_order in (?) and draft_status = ? and deleted = 0", draftOrders,
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        demandDBHelper.executeRaw("update ppl_item_draft set deleted = 1 " +
                        "where ppl_order in (?) and draft_status = ? and deleted = 0", draftOrders,
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());

        demandDBHelper.executeRaw("update ppl_order_draft set draft_status = ? ,modify_reason = '' "
                        + "where draft_status in (?) and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), toPreSubmitStatus, draftOrders);
        demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? "
                        + "where draft_status in (?) and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), toPreSubmitStatus, draftOrders);

        // 记录日志
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        beforeDraftPpls.forEach(orderDraftVO ->
                orderDraftVO.getPplItemDrafts().forEach(itemDraft -> {
                            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                            BeanUtils.copyProperties(orderDraftVO, afterAllFieldDTO);
                            BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                            afterAllFieldDTO.setStatus(
                                    StringUtils.isBlank(itemDraft.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                            : itemDraft.getStatus());
                            recordNewDTO.setAfterItem(afterAllFieldDTO);
                            newDTOs.add(recordNewDTO);
                        }
                ));
        return newDTOs;
    }

    /**
     * 当前版本无法提交的草稿 提交到下一个版本（这里只做PPL草稿的状态更改，到达下个版本时由任务去自动提交）
     *
     * @param currenVersions 当前版本
     * @param drafts 当前版本无法提交的草稿
     * @return PPL变化日志，在方法调用方写入
     */
    private List<PplItemChangeRecordNewDTO> submitDraftToNextVersion(List<PplInnerProcessVersionDO> currenVersions,
            List<PplItemDraftDO> drafts) {
        if (ListUtils.isEmpty(drafts)) {
            return new ArrayList<>();
        }
        List<String> overseas = pplDictService.queryAllRegionName(true);
        Map<Long, PplInnerProcessVersionDO> nextVersionMap = new HashMap<>();
        Map<Long, List<String>> productNextVersionMap = new HashMap<>();
        // 获取当前审批版本的下一版本
        List<PplInnerProcessVersionDO> nextVersions = new ArrayList<>();
        for (PplInnerProcessVersionDO curren : currenVersions) {
            PplInnerProcessVersionDO next = innerVersionService.nextVersion(curren);
            if (next != null) {
                nextVersions.add(next);
                nextVersionMap.put(next.getId(), next);
                productNextVersionMap.put(next.getId(), Arrays.asList(next.getProduct().split(";")));
            } else {
                nextVersions.add(curren);
                nextVersionMap.put(curren.getId(), curren);
                productNextVersionMap.put(curren.getId(), Arrays.asList(curren.getProduct().split(";")));
            }
        }

        Map<String, List<PplItemDraftDO>> draftDOsByProduct = drafts.stream()
                .collect(Collectors.groupingBy(PplItemDraftDO::getProduct));
        Set<String> pplOrders = new HashSet<>();
        // 校验PPL草稿是否满足提交到下一版本的条件
        // PPL的开始购买日期要 大于等于 产品的下一审批版本录入的开始需求年月
        productNextVersionMap.forEach((versionId, products) -> {
            PplInnerProcessVersionDO versionDO = nextVersionMap.get(versionId);
            List<PplItemDraftDO> submitDraftDOs = new ArrayList<>();
            products.forEach(e -> {
                List<PplItemDraftDO> draftDOs = draftDOsByProduct.get(e);
                if (!CollectionUtils.isEmpty(draftDOs)) {
                    submitDraftDOs.addAll(draftDOs);
                    pplOrders.addAll(ListUtils.toSet(draftDOs, PplItemBaseDO::getPplOrder));
                }
            });

            LocalDateTime beginYearMonth = DateUtils.parseLocalDateTime(
                    versionDO.getDemandBeginYear() + "-" + versionDO.getDemandBeginMonth() + "-" + "01 00:00:00");
            LocalDateTime endYearMonth = DateUtils.parseLocalDateTime(
                    versionDO.getDemandEndYear() + "-" + versionDO.getDemandEndMonth() + "-" + "01 00:00:00");

            Map<String, List<PplItemDraftDO>> pplOrderToItemMap = submitDraftDOs.stream()
                    .filter(v -> v.getBeginBuyDate().isBefore(ChronoLocalDate.from(beginYearMonth)))
                    .collect(Collectors.groupingBy(PplItemDraftDO::getPplOrder));
            StringBuilder pplOrderMonthDetail = new StringBuilder();
            for (String pplOrder : pplOrderToItemMap.keySet()) {
                PplItemDraftDO itemDraftDO = pplOrderToItemMap.get(pplOrder).get(0);
                String yearMonth =
                        itemDraftDO.getBeginBuyDate().getYear() + "年" + itemDraftDO.getBeginBuyDate().getMonthValue()
                                + "月\n";
                pplOrderMonthDetail.append(pplOrder).append(" 的需求月份为").append(yearMonth);
            }
            if (Strings.isNotBlank(pplOrderMonthDetail.toString())) {
                throw new BizException("下周期" + versionDO.getProduct() + " 产品可录月份为"
                        + beginYearMonth.getYear() + "年" + beginYearMonth.getMonthValue() + "月 ～ "
                        + endYearMonth.getYear() + "年" + endYearMonth.getMonthValue() + "月"
                        + "。您的数据中: \n"
                        + pplOrderMonthDetail
                        + " 请校正后再提交。(如您需要顺延需求，可在列表中复制PPL后修改需求日期)");
            }
            // 过滤出 海外且非退回的 且过期的
            List<PplItemDraftDO> overseasExpiredPpl = submitDraftDOs.stream()
                    .filter(v -> overseas.contains(v.getRegionName()) && !v.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                    && !versionDO.isSatisfyOverseasYearMonth(v.getBeginBuyDate()))
                    .collect(Collectors.toList());
            if (ListUtils.isNotEmpty(overseasExpiredPpl)){
                String join = String.join(";", overseasExpiredPpl.stream().map(PplItemDraftDO::getPplOrder).distinct()
                        .collect(Collectors.toList()));
                throw new BizException("以下PPL早于下周期海外需求可录入月份: "+ versionDO.getOverseasDemandYearMonth() +
                        "; PPL: " + join
                + " 请校正后再提交。(如您需要顺延需求，可在列表中复制PPL后修改需求日期)");
            }
        });
        if (ListUtils.isEmpty(pplOrders)) {
            return new ArrayList<>();
        }

        WhereContent queryContent = new WhereContent();
        queryContent.andEqual(PplOrderDraftVO::getDraftStatus, PplOrderDraftStatusEnum.DRAFT.getCode());
        queryContent.andInIfValueNotEmpty(PplOrderDraftVO::getPplOrder, new ArrayList<>(pplOrders));
        List<PplOrderDraftVO> beforeDraftPpls = demandDBHelper.getAll(PplOrderDraftVO.class,
                queryContent.getSql(), queryContent.getParams());
        // 草稿状态的PPL状态改为下期生效
        Set<String> draftOrders = ListUtils.toSet(beforeDraftPpls, PplOrderBaseDO::getPplOrder);

        // 如果该单据在原来已有，则先直接删除
        demandDBHelper.executeRaw("update ppl_order_draft set deleted = 1 " +
                        "where ppl_order in (?) and draft_status = ? and deleted = 0", draftOrders,
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        demandDBHelper.executeRaw("update ppl_item_draft set deleted = 1 " +
                        "where ppl_order in (?) and draft_status = ? and deleted = 0", draftOrders,
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());

        // 改为下期生效
        demandDBHelper.executeRaw(
                "update ppl_order_draft set draft_status = ? , modify_reason = '' "
                        + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode(),
                PplOrderDraftStatusEnum.DRAFT.getCode(), draftOrders);
        demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? "
                        + "where draft_status = ? and ppl_order in (?) and deleted = 0 ",
                PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode(),
                PplOrderDraftStatusEnum.DRAFT.getCode(), draftOrders);

        // 日志
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        beforeDraftPpls.forEach(orderDraftVO ->
                orderDraftVO.getPplItemDrafts().forEach(itemDraft -> {
                            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                            BeanUtils.copyProperties(orderDraftVO, afterAllFieldDTO);
                            BeanUtils.copyProperties(itemDraft, afterAllFieldDTO);
                            afterAllFieldDTO.setStatus(
                                    StringUtils.isBlank(itemDraft.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                                            : itemDraft.getStatus());
                            recordNewDTO.setAfterItem(afterAllFieldDTO);
                            newDTOs.add(recordNewDTO);
                        }
                ));
        return newDTOs;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    @TaskLog(taskName = "preSubmitDraftForStartNewVersion")
    public void preSubmitDraftForStartNewVersion(Long versionId) {
        String industryDept = null;
        List<String> products = new ArrayList<>();
        try {
            // 获取审批流版本信息
            PplInnerProcessVersionDO version = innerVersionService.getByVersionId(versionId);
            if (!PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(version.getStatus())) {
                throw BizException.makeThrow("传入的审批流版本不是生效中状态");
            }
            industryDept = version.getIndustryDept();
            products = version.parseToProductList();
            WhereSQL where = new WhereSQL()
                    .and(" draft_status = ? ", PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode())
                    .and(" industry_dept = ? ", industryDept)
                    .and(" product in (?) ", products);
            // 查询出下期生效的数据
            List<PplOrderDraftDO> draftPplList = demandDBHelper.getAll(PplOrderDraftDO.class,
                    where.getSQL(), where.getParams());
            Set<String> pplOrders = ListUtils.toSet(draftPplList, PplOrderDraftDO::getPplOrder);
            if (ListUtils.isEmpty(pplOrders)) {
                return;
            }
            // 预提交到最新周期
            preSubmitDraft(version.getIndustryDept(), new ArrayList<>(pplOrders));
        } catch (Exception e) {
            // 告警通知
            String msgFmt = "将PPL草稿箱中下期生效数据预提交到本周期时异常，versionId【{}】，行业【{}】，产品【{}】，原因：{}";
            String message = StrUtil.format(msgFmt, versionId, industryDept, products, ExceptionUtil.getMessage(e));
            AlarmRobotUtil.doAlarm("preSubmitDraftForStartNewVersion", message, null, false);
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void submitDraft(Long versionId, List<String> pplOrders) {
        val poList = demandDBHelper.getAll(PplOrderDraftDO.class, "where draft_status = ? and ppl_order in (?)",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), pplOrders);
        val fail = new ArrayList<String>();

        for (PplOrderDraftDO po : poList) {
            try {
                submitDraft(versionId, po);
            } catch (Exception e) {
                fail.add(po.getPplOrder());
                log.error("提交失败", e);
            }
        }
        if (!fail.isEmpty()) {
            throw new WrongWebParameterException(String.format("部分单据提交失败%s", fail));
        }

        // 发送moa待办
        List<PplOrderDO> allPplOrder = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)", pplOrders);
        PplInnerProcessVersionSlaDTO firstNode = innerVersionService.getFirstNode(versionId);
        Map<String, List<PplOrderDO>> keyToList = new HashMap<>();
        Function<PplOrderDO, String> func;
        if (firstNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
            func = PplOrderDO::getWarZone;
        } else if (firstNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
            func = PplOrderDO::getCustomerShortName;
        } else {
            func = PplOrderDO::getIndustryDept;
        }
        keyToList = allPplOrder.stream()
                .collect(Collectors.groupingBy(func));
        keyToList.forEach((k, v) -> {
            PplOrderDO case1 = v.get(0);
            String warZone = null;
            String customer = null;
            if (firstNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.WAR_ZONE.getCode())) {
                warZone = case1.getWarZone();
            } else if (firstNode.getRoleAttribute().equals(PplInnerProcessAttributeEnum.CUSTOMER.getCode())) {
                warZone = case1.getWarZone();
                customer = case1.getCustomerShortName();
            }
            String nodeApproveUser = pplInnerProcessService.getNodeApproveUser(firstNode, case1);
            pplInnerProcessService.sendTodoAndMoa(nodeApproveUser, firstNode, versionId, case1.getIndustryDept(),
                    warZone, customer, "");
        });


    }

    /**
     * 提交草稿单
     * 插入或/更新 ppl_order 信息
     * 生成 ppl_order_audit_record 3 条 修改前， 修改后， 审批用
     * 生成 ppl_order_audit_record_item  新增场景 2 * item 数量 ，修改 3 * item数量 对应上面数据
     */
    @Transactional(value = "demandTransactionManager")
    public void submitDraft(Long versionId, PplOrderDraftDO orderDraftDO) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.getByVersionId(versionId);
        val verId = currentVersion.getId();
        val oderNo = orderDraftDO.getPplOrder();
        log.info(" {} -> {} 提交草稿数据", oderNo, verId);
        PplOrderDO pplOrderDO = null;
        pplOrderDO = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order =?", orderDraftDO.getPplOrder());
        if (orderDraftDO.getSource().equals(OperateTypeEnum.INSERT.getCode())) {
            log.info("{} 新增ppl order", oderNo);
            // 如果是被拒绝的pplOrder 则不需要new
            if (pplOrderDO == null) {
                pplOrderDO = new PplOrderDO();
                if (oderNo.startsWith("PN")) {
                    pplOrderDO.setSource(PplOrderSourceTypeEnum.IMPORT.getCode());
                }
            }
            pplOrderDO.setChangeType(OperateTypeEnum.INSERT.getCode());
        } else if (orderDraftDO.getSource().equals(OperateTypeEnum.UPDATE.getCode())
                || orderDraftDO.getSource().equals(OperateTypeEnum.DELETED.getCode())
                || orderDraftDO.getSource().equals(OperateTypeEnum.ORIGINAL.getCode())) {
            if (pplOrderDO == null) {
                throw new BizException("原PPL" + pplOrderDO.getPplOrder() + "单据已失效，请删除草稿");
            } else if (Strings.isNotBlank(pplOrderDO.getNodeCode())) {
                throw new BizException("单据已经在流程中，不允许提交:" + pplOrderDO.getPplOrder());
            }
            pplOrderDO.setChangeType(orderDraftDO.getSource());
        }

        PplInnerProcessVersionSlaDTO node = innerVersionService.getFirstNode(currentVersion.getId());

        orderDraftDO.toPplOrder(pplOrderDO);
        pplOrderDO.setStatus("VALID");
        pplOrderDO.setNodeCode(node.getNodeCode());
        pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        pplOrderDO.setCurrentProcessor(pplInnerProcessService.getNodeApproveUser(node, pplOrderDO));
        pplOrderDO.setCurrentRole(node.getApproveRole());
        pplOrderDO.setInputStatus(orderDraftDO.getInputStatus());

        demandDBHelper.insertOrUpdate(pplOrderDO);
        log.info("{}变更 pplOrderDO信息 ", oderNo);
        PplOrderAuditRecordDO auditRecordDO = new PplOrderAuditRecordDO();
        auditRecordDO.setVersionId(verId);
        auditRecordDO.setPplOrder(pplOrderDO.getPplOrder());
        auditRecordDO.setOperateUser(orderDraftDO.getSubmitUser());
        auditRecordDO.setInputStatus(orderDraftDO.getInputStatus());
        demandDBHelper.insert(auditRecordDO);
        Long preRecordId = auditRecordDO.getId();
        auditRecordDO.setId(null);
        auditRecordDO.setModifyReason(orderDraftDO.getModifyReason());
        demandDBHelper.insert(auditRecordDO);
        Long curRecordId = auditRecordDO.getId();

        auditRecordDO.setNodeCode(node.getNodeCode());
        auditRecordDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        auditRecordDO.setId(null);
        demandDBHelper.insert(auditRecordDO);
        Long aftRecordId = auditRecordDO.getId();
        log.info("{} 插入 order audit record {} ,{}, {}", oderNo, preRecordId, curRecordId, aftRecordId);

        List<PplOrderAuditRecordItemDO> oriRecords = Lists.newArrayList();
//        if (orderDraftDO.getSource().equals(OperateTypeEnum.UPDATE.getCode())
//                || orderDraftDO.getSource().equals(OperateTypeEnum.DELETED.getCode())
//                || orderDraftDO.getSource().equals(OperateTypeEnum.ORIGINAL.getCode())) {
//
//        }
        log.info("{}【更新/删除操作】记录原始变更前的数据{}", oderNo);
        oriRecords = recordOriPplItem(preRecordId, oderNo);
        log.info("{} 插入变更后数据 {}", oderNo);
        recordDraftPplItem(curRecordId, oderNo, oriRecords);
        log.info("{} 插入变更后数据-for审批", oderNo);
        recordDraftPplItem(aftRecordId, oderNo, oriRecords);

        // 统一去除拒绝信息
        demandDBHelper.executeRaw(
                "update ppl_order_draft set modify_reason = '', draft_status = ? where id = ? and deleted = 0",
                PplOrderDraftStatusEnum.SUBMITTED.getCode(), orderDraftDO.getId());
        demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? where ppl_order in (?) and deleted = 0",
                PplOrderDraftStatusEnum.SUBMITTED.getCode(), Lists.newArrayList(oderNo));
        log.info("{} 清空草稿数据", oderNo);
        removeDraft(Lists.newArrayList(oderNo), false);

    }

    @Override
    @Transactional("demandTransactionManager")
    public List<String> matchRepeatAppliedDraft(String industryDept, List<String> products) {
        // 按行业+产品 查询 需求沟通 中的所有提交的草稿数据
        WhereSQL draftSql = new WhereSQL();
        draftSql.and("t2.draft_status = ?",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        draftSql.and("t2.industry_dept = ?", industryDept);

        draftSql.and("t1.draft_status = ?",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        draftSql.and("t1.product in(?)", products);
        List<PplDraftItemJoinDraftOrderVO> draftVOList = demandDBHelper.getAll(PplDraftItemJoinDraftOrderVO.class,
                draftSql.getSQL(), draftSql.getParams());
        draftVOList = draftVOList.stream().filter(e -> e.getItemDraftDO() != null && e.getOrderDraftDO() != null)
                .collect(Collectors.toList());
        Map<Long, List<PplDraftItemJoinDraftOrderVO>> allDraftByOrderMap = draftVOList.stream()
                .collect(Collectors.groupingBy(e -> e.getOrderDraftDO().getId()));

        // 查询草稿对应的已生效数据
        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_order in(?) ",
                draftVOList.stream().map(e -> e.getOrderDraftDO().getPplOrder()).distinct().collect(
                        Collectors.toList()));
        Map<String, List<PplItemDO>> itemDOByOrderMap = pplItemDOList.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        Map<String, PplItemDO> itemDOMap = pplItemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v2));

        // 查询草稿对应的预约数据
        List<String> deletedYunxiaoStatus = Arrays.asList(YunxiaoOrderStatusEnum.CREATED.getCode(),
                YunxiaoOrderStatusEnum.CANCELED.getCode(),
                YunxiaoOrderStatusEnum.BAD_CANCELED.getCode(), YunxiaoOrderStatusEnum.REJECTED.getCode());
        List<PplItemAppliedDO> pplItemAppliedDOList = demandDBHelper.getAll(PplItemAppliedDO.class,
                "where yunxiao_order_id in(?) and yunxiao_order_status not in(?) ",
                draftVOList.stream().map(e -> e.getItemDraftDO().getYunxiaoOrderId()).filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList()), deletedYunxiaoStatus);
        Map<String, PplItemAppliedDO> itemAppliedDOMap = pplItemAppliedDOList.stream().collect(
                Collectors.toMap(e -> String.join("@", e.getYunxiaoOrderId(), String.valueOf(e.getYunxiaoDetailId())),
                        Function.identity(), (v1, v2) -> v2));

        // 初始化匹配结果
        List<String> reMatchPnItemPpls = new ArrayList<>();

        List<String> deletedPeOrderPpls = new ArrayList<>();
        List<String> deletedPeItemPpls = new ArrayList<>();

        List<Long> deletedPeOrderIds = new ArrayList<>();
        List<Long> deletedPeItemIds = new ArrayList<>();

        List<PplItemDraftDO> updateDraftItems = new ArrayList<>();
        List<PplItemDO> updateItems = new ArrayList<>();
        List<PplItemAppliedDO> updateAppliedItems = new ArrayList<>();

        // 获取各预约明细选择的实例类型 的 其通用实例类型列表内的所有实例类型
        Pair<List<InstanceTypeRelateDTO>, Map<String, String>> pair = pplDictService.queryInstanceTypeRelate();
        // <instanceType, commonInstanceType>
        Map<String, String> instanceConfigMap = pair.getValue();
        // 按客户级主key匹配 pn、pe单
        Map<String, List<PplDraftItemJoinDraftOrderVO>> draftMap = ListUtils
                .toMapList(draftVOList, e -> forecastApplyMatchKey(instanceConfigMap, e), item -> item);
        for (Map.Entry<String, List<PplDraftItemJoinDraftOrderVO>> entry : draftMap.entrySet()) {
            List<PplDraftItemJoinDraftOrderVO> pnDraftVos = entry.getValue().stream()
                    .filter(e -> e.getOrderDraftDO().getPplOrder().startsWith("PN"))
                    .collect(Collectors.toList());
            List<PplDraftItemJoinDraftOrderVO> peDraftVos = entry.getValue().stream()
                    .filter(e -> e.getOrderDraftDO().getPplOrder().startsWith("PE")).collect(Collectors.toList());
            if (ListUtils.isEmpty(pnDraftVos) || ListUtils.isEmpty(peDraftVos)) {
                continue;
            }

            //a. 已生效的PN单，未预约
            List<PplDraftItemJoinDraftOrderVO> pnValidDraftVos = pnDraftVos.stream()
                    .filter(e -> itemDOMap.get(e.getItemDraftDO().getPplId()) != null
                            && !PplItemStatusEnum.APPLIED.getCode()
                            .equals(itemDOMap.get(e.getItemDraftDO().getPplId()).getStatus()))
                    .collect(Collectors.toList());
            // b. 未生效的PN单，未预约
            List<PplDraftItemJoinDraftOrderVO> pnUnValidDraftVos = pnDraftVos.stream()
                    .filter(e -> itemDOMap.get(e.getItemDraftDO().getPplId()) == null
                            && !PplItemStatusEnum.APPLIED.getCode().equals(e.getItemDraftDO().getStatus()))
                    .collect(Collectors.toList());

            // 执行匹配
            for (PplDraftItemJoinDraftOrderVO peDraft : peDraftVos) {
                PplOrderDraftDO peOrderDraft = peDraft.getOrderDraftDO();
                PplItemDraftDO peItemDraft = peDraft.getItemDraftDO();
                if (peOrderDraft == null || peItemDraft == null) {
                    continue;
                }
                PplItemAppliedDO appliedDO = itemAppliedDOMap.get(String.join("@", peItemDraft.getYunxiaoOrderId(),
                        String.valueOf(peItemDraft.getYunxiaoDetailId())));
                if (appliedDO != null) {
                    appliedDO.setPplId(appliedDO.getPplId() == null ? "" : appliedDO.getPplId());
                    appliedDO.setPplId2(appliedDO.getPplId2() == null ? "" : appliedDO.getPplId2());
                }
                if (appliedDO == null
                        || (!appliedDO.getPplId().equals(peItemDraft.getPplId()) && !Arrays.asList(
                        appliedDO.getPplId2().split(";")).contains(peItemDraft.getPplId()))
                        || deletedYunxiaoStatus.contains(appliedDO.getYunxiaoOrderStatus())) {
                    // 本应是定时任务syncAppliedToVersionLine在需求沟通时期 定时 将预约后取消的PE单删除的
                    // 但在从需求沟通流转到审批流的前一刻，需求沟通 还存在 取消预约的PE单，说明是在定时任务最后一次处理后 操作取消的预约，因此这里对 非有效预约状态的PE单 进行删除操作
                    deletedPeOrderPpls.add(peOrderDraft.getPplOrder());
                    deletedPeItemPpls.add(peItemDraft.getPplId());
                    deletedPeOrderIds.add(peOrderDraft.getId());
                    deletedPeItemIds.add(peItemDraft.getId());
                    continue;
                }

                // 目前开放了GPU、裸金属，CVM（CVM、EMR、EKS）进行预约单关联，且这些产品都是审批版本中允许审批的产品
                PplApplyRematchDTO rematchDTO = null;
                // 1、优先匹配已生效的PN单
                PplApplyRematchDTO rematchPnDTO = matchRepeatDraftPpl(peOrderDraft, peItemDraft, pnValidDraftVos,
                        appliedDO,
                        itemDOMap);
                if (rematchPnDTO != null) {
                    rematchDTO = rematchPnDTO;
                } else {
                    // 2、其次匹配未生效的PN单
                    rematchDTO = matchRepeatDraftPpl(peOrderDraft, peItemDraft, pnUnValidDraftVos, appliedDO,
                            itemDOMap);
                }

                // 3、记录匹配的更改信息
                if (rematchDTO != null) {
                    // 记录 重匹配PN单
                    reMatchPnItemPpls.add(rematchDTO.getReMatchPnItemPpl());
                    // 删除 生效PE单
                    deletedPeOrderPpls.add(rematchDTO.getDeletedPeOrderPpl());
                    deletedPeItemPpls.add(rematchDTO.getDeletedPeItemPpl());
                    // 删除 草稿PE单
                    deletedPeOrderIds.add(rematchDTO.getDeletedPeOrderId());
                    deletedPeItemIds.add(rematchDTO.getDeletedPeItemId());
                    // 更新 草稿PN单、生效PN单、预约单 关联关系
                    updateDraftItems.add(rematchDTO.getUpdateDraftItem());
                    updateAppliedItems.add(rematchDTO.getUpdateAppliedItem());
                    updateItems.add(rematchDTO.getUpdateItem());
                }

            }

        }

        // 操作匹配结果
        // 1、删除 草稿PE单
        List<Long> deletedPeOrderIdList = new ArrayList<>();
        deletedPeItemIds = deletedPeItemIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(deletedPeItemIds)) {
            demandDBHelper.executeRaw(
                    "update ppl_item_draft set deleted = 1 where id in(?)", deletedPeItemIds);

            // 如果 PE草稿单据只有一条明细 / 有多条明细且全都会删除，那么此时可以 同时删除 PE草稿单据
            deletedPeOrderIds = deletedPeOrderIds.stream().filter(Objects::nonNull).distinct()
                    .collect(Collectors.toList());
            for (Long deletedOrderId : deletedPeOrderIds) {
                List<PplDraftItemJoinDraftOrderVO> draftItemDOs = allDraftByOrderMap.get(deletedOrderId);
                List<Long> draftItemIds = draftItemDOs.stream().map(e -> e.getItemDraftDO().getId())
                        .collect(Collectors.toList());
                draftItemIds.removeAll(deletedPeItemIds);
                if (ListUtils.isEmpty(draftItemIds)) {
                    deletedPeOrderIdList.add(deletedOrderId);
                }
            }
            if (ListUtils.isNotEmpty(deletedPeOrderIdList)) {
                demandDBHelper.executeRaw(
                        "update ppl_order_draft set deleted = 1 where id in(?)", deletedPeOrderIdList);
            }
        }
        // 删除 生效PE单
        List<String> deletedPeOrders = new ArrayList<>();
        deletedPeItemPpls = deletedPeItemPpls.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(deletedPeItemPpls)) {
            demandDBHelper.executeRaw(
                    "update ppl_item set deleted = 1 where ppl_id in(?)", deletedPeItemPpls);

            // 如果 PE单单据只有一条明细 / 有多条明细且全都会删除，那么此时可以 同时删除 PE单据
            deletedPeOrderPpls = deletedPeOrderPpls.stream().filter(Objects::nonNull).distinct()
                    .collect(Collectors.toList());
            for (String deletedPplOrder : deletedPeOrderPpls) {
                List<PplItemDO> itemDOs = itemDOByOrderMap.get(deletedPplOrder);
                List<String> itemPpls = itemDOs.stream().map(PplItemDO::getPplId).collect(Collectors.toList());
                itemPpls.removeAll(deletedPeItemPpls);
                if (ListUtils.isEmpty(itemPpls)) {
                    deletedPeOrders.add(deletedPplOrder);
                }
            }
            if (ListUtils.isNotEmpty(deletedPeOrders)) {
//                demandDBHelper.executeRaw(
//                        "update ppl_order set deleted = 1 where ppl_order in(?)", deletedPeOrders);
            }
        }
        // 2、更新 生效PN单 的 预约关联信息
        updateItems = updateItems.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(updateItems)) {
            demandDBHelper.update(updateItems);
        }
        // 3、更新 草稿PN单 的 预约关联信息
        updateDraftItems = updateDraftItems.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(updateDraftItems)) {
            demandDBHelper.update(updateDraftItems);
        }
        // 4、更新 预约单 的 预测关联信息
        updateAppliedItems = updateAppliedItems.stream().filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(updateAppliedItems)) {
            demandDBHelper.update(updateAppliedItems);
        }

        // 发邮件提醒，系统进行了重匹配
        reMatchPnItemPpls = reMatchPnItemPpls.stream().filter(Objects::nonNull).distinct().sorted()
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(reMatchPnItemPpls)) {
            String users = null;
            if ("智慧行业一部".equals(industryDept)) {
                users = "damianren";
            }
            dictService.eventNotice(CrpEventEnum.REPEAT_APPLIED_ORDER.getCode(), null,
                    "【" + industryDept + "】的【" + Strings.join(";", products) + "】有重复的预测预约，系统已自动进行重匹配，重匹配的单据明细为："
                            + reMatchPnItemPpls, null, users);
        }

        return deletedPeOrders;

    }

    /**
     * 匹配重复的预测预约数据（需求沟通阶段 匹配PN、PE单）
     *
     * @param peOrderDraft 草稿PE单
     * @param peItemDraft 草稿PE明细
     * @param pnDraftVos 未预约的 所有草稿PN单列表
     * @param appliedDO 草稿PE单关联的预约单
     * @param itemDOMap 草稿PN单对应的生效PN单列表
     * @return PN、PE单匹配更新信息
     */
    private PplApplyRematchDTO matchRepeatDraftPpl(PplOrderDraftDO peOrderDraft, PplItemDraftDO peItemDraft,
            List<PplDraftItemJoinDraftOrderVO> pnDraftVos, PplItemAppliedDO appliedDO,
            Map<String, PplItemDO> itemDOMap) {
        if (ListUtils.isEmpty(pnDraftVos)) {
            return null;
        }

        // GPU：按卡数匹配；非GPU产品：按核数匹配
        boolean isGPU = Ppl13weekProductTypeEnum.GPU.getName().equals(peItemDraft.getProduct());

        //  目前这里的匹配，主要是匹配 资源量相同的（暂不考虑资源量不同时的 拆单 / 系统补单 场景）
        //  优先匹配实例规格相同的
        //  （实例规格相同的 PN单和PE单，对资源量相同的判定可以用 资源量(卡数&核数) / 台数；允许用台数判定的原因是，有的预约明细是匹配了其他的生效预测后，剩余不足的资源量，再系统自动补单补全的，这时的系统补单PE单的资源量就不是按 实例规格*台数算出的，可能少于 实例规格*台数）
        List<PplDraftItemJoinDraftOrderVO> matchedVos = new ArrayList<>();
        matchedVos.addAll(pnDraftVos.stream().filter(e ->
                        e.getItemDraftDO().getInstanceModel().equals(peItemDraft.getInstanceModel())
                                && ((isGPU ? e.getItemDraftDO().getTotalGpuNum().compareTo(peItemDraft.getTotalGpuNum()) == 0
                                : e.getItemDraftDO().getTotalCore().compareTo(peItemDraft.getTotalCore()) == 0)
                                || (e.getInstanceNum().compareTo(peItemDraft.getInstanceNum()) == 0)))
                .collect(Collectors.toList()));
        matchedVos.addAll(pnDraftVos.stream().filter(e ->
                        !e.getItemDraftDO().getInstanceModel().equals(peItemDraft.getInstanceModel())
                                && (isGPU ? e.getItemDraftDO().getTotalGpuNum().compareTo(peItemDraft.getTotalGpuNum()) == 0
                                : e.getItemDraftDO().getTotalCore().compareTo(peItemDraft.getTotalCore()) == 0))
                .collect(Collectors.toList()));
        if (ListUtils.isEmpty(matchedVos)) {
            return null;
        }

        PplDraftItemJoinDraftOrderVO matchedVo = matchedVos.get(0);

        peItemDraft.setInstanceNum(
                peItemDraft.getInstanceNum() == null ? 0 : peItemDraft.getInstanceNum());
        peItemDraft.setTotalCore(
                peItemDraft.getTotalCore() == null ? 0 : peItemDraft.getTotalCore());
        peItemDraft.setTotalGpuNum(peItemDraft.getTotalGpuNum() == null ? BigDecimal.ZERO
                : peItemDraft.getTotalGpuNum());

        // 将匹配到的PN从列表移除，避免重复匹配
        pnDraftVos.remove(matchedVo);

        // 更新 草稿PN单 预约关联信息
        PplItemDraftDO updateDraftItemDO = new PplItemDraftDO();
        updateDraftItemDO.setId(matchedVo.getItemDraftDO().getId());
        updateDraftItemDO.setStatus(PplItemStatusEnum.APPLIED.getCode());
        updateDraftItemDO.setYunxiaoOrderId(peItemDraft.getYunxiaoOrderId());
        updateDraftItemDO.setYunxiaoDetailId(peItemDraft.getYunxiaoDetailId());
        updateDraftItemDO.setYunxiaoOrderStatus(appliedDO.getYunxiaoOrderStatus());
        updateDraftItemDO.setInstanceNumApplyBefore(0);
        updateDraftItemDO.setInstanceNumApplyAfter(peItemDraft.getInstanceNum());
        updateDraftItemDO.setTotalCoreApplyBefore(0);
        updateDraftItemDO.setTotalCoreApplyAfter(peItemDraft.getTotalCore());
        updateDraftItemDO.setTotalGpuNumApplyBefore(BigDecimal.ZERO);
        updateDraftItemDO.setTotalGpuNumApplyAfter(peItemDraft.getTotalGpuNum());

        // 更新 预约单 预测关联信息
        PplItemAppliedDO updateAppliedItem = new PplItemAppliedDO();
        updateAppliedItem.setId(appliedDO.getId());
        if (appliedDO.getPplId().equals(peItemDraft.getPplId())) {
            updateAppliedItem.setPplId(matchedVo.getItemDraftDO().getPplId());
            updateAppliedItem.setPplOrder(matchedVo.getItemDraftDO().getPplOrder());
            updateAppliedItem.setPplOrderSource(PplOrderSourceTypeEnum.IMPORT.getCode());
        } else {
            List<String> pplId2 = Stream.of(appliedDO.getPplId2().split(";")).collect(
                    Collectors.toList());
            pplId2.remove(peItemDraft.getPplId());
            pplId2.add(matchedVo.getItemDraftDO().getPplId());

            List<String> pplOrder2 = Stream.of(appliedDO.getPplOrder2().split(";")).collect(
                    Collectors.toList());
            pplOrder2.remove(peItemDraft.getPplOrder());
            pplOrder2.add(matchedVo.getItemDraftDO().getPplOrder());
            pplOrder2 = pplOrder2.stream().distinct().collect(Collectors.toList());

            updateAppliedItem.setPplId2(String.join(";", pplId2));
            updateAppliedItem.setPplOrder2(String.join(";", pplOrder2));
            updateAppliedItem.setPplOrder2Source(
                    pplOrder2.size() == 1 ? PplOrderSourceTypeEnum.IMPORT.getCode() : null);
        }
        updateAppliedItem.setInstanceNumApplyAfter(
                appliedDO.getInstanceNumApplyAfter() == null ? peItemDraft.getInstanceNum()
                        : appliedDO.getInstanceNumApplyAfter() + peItemDraft.getInstanceNum());
        updateAppliedItem.setTotalCoreApplyAfter(
                appliedDO.getTotalCoreApplyAfter() == null ? peItemDraft.getTotalCore()
                        : appliedDO.getTotalCoreApplyAfter() + peItemDraft.getTotalCore());
        updateAppliedItem.setTotalGpuNumApplyAfter(
                appliedDO.getTotalGpuNumApplyAfter() == null ? peItemDraft.getTotalGpuNum()
                        : appliedDO.getTotalGpuNumApplyAfter().add(peItemDraft.getTotalGpuNum()));

        // 更新 生效PN单 预约关联信息
        PplItemDO updateItemDO = null;
        PplItemDO itemDO = itemDOMap.get(matchedVo.getItemDraftDO().getPplId());
        if (itemDO != null) {
            updateItemDO = new PplItemDO();
            updateItemDO.setId(itemDO.getId());
            updateItemDO.setStatus(PplItemStatusEnum.APPLIED.getCode());
            updateItemDO.setYunxiaoOrderId(peItemDraft.getYunxiaoOrderId());
            updateItemDO.setYunxiaoDetailId(peItemDraft.getYunxiaoDetailId());
            updateItemDO.setBeginBuyDate(matchedVo.getItemDraftDO().getBeginBuyDate());
            updateItemDO.setEndBuyDate(matchedVo.getItemDraftDO().getEndBuyDate());
            updateItemDO.setYunxiaoOrderStatus(appliedDO.getYunxiaoOrderStatus());
            updateItemDO.setInstanceNumApplyBefore(0);
            updateItemDO.setInstanceNumApplyAfter(peItemDraft.getInstanceNum());
            updateItemDO.setTotalCoreApplyBefore(0);
            updateItemDO.setTotalCoreApplyAfter(peItemDraft.getTotalCore());
            updateItemDO.setTotalGpuNumApplyBefore(BigDecimal.ZERO);
            updateItemDO.setTotalGpuNumApplyAfter(peItemDraft.getTotalGpuNum());
        }

        // 返回
        PplApplyRematchDTO rematchDTO = new PplApplyRematchDTO();
        // 记录 重匹配PN单
        rematchDTO.setReMatchPnItemPpl(matchedVo.getItemDraftDO().getPplId());
        // 删除 生效PE单
        rematchDTO.setDeletedPeOrderPpl(peOrderDraft.getPplOrder());
        rematchDTO.setDeletedPeItemPpl(peItemDraft.getPplId());
        // 删除 草稿PE单
        rematchDTO.setDeletedPeOrderId(peOrderDraft.getId());
        rematchDTO.setDeletedPeItemId(peItemDraft.getId());
        // 更新 草稿PN单、生效PN单、预约单 关联关系
        rematchDTO.setUpdateDraftItem(updateDraftItemDO);
        rematchDTO.setUpdateAppliedItem(updateAppliedItem);
        if (updateItemDO != null) {
            rematchDTO.setUpdateItem(updateItemDO);
        }

        return rematchDTO;

    }

    // 按客户级主key匹配
    private String forecastApplyMatchKey(Map<String, String> instanceConfigMap, PplDraftItemJoinDraftOrderVO item) {
        String instanceType = item.getInstanceType();
        String commonInstanceType = instanceConfigMap.get(item.getInstanceType());
        if (StringUtils.isNotBlank(commonInstanceType)) {
            instanceType = commonInstanceType;
        }
        return new StringJoiner("@")
                .add(item.getProduct())
                .add(item.getDemandType())
                .add(item.getIndustryDept())
                .add(item.getOrderDraftDO().getCustomerShortName())
                .add(String.valueOf(item.getItemDraftDO().getBeginBuyDate().getYear()))
                .add(String.valueOf(item.getItemDraftDO().getBeginBuyDate().getMonthValue()))
                .add(instanceType)
                .add(item.getRegionName())
                .toString();
    }

    private Map<String, PplListVo> nextVersionValidDataMap(QueryPplDraftReq req) {
        QueryPplDraftReq copyReq = new QueryPplDraftReq();
        BeanUtils.copyProperties(req, copyReq);
        copyReq.setStatus(PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode());
        List<PplListVo> list = queryDraftData(copyReq);
        if (ListUtils.isEmpty(list)) {
            return new HashMap<>();
        }
        return ListUtils.toMap(list, PplListVo::getPplOrder, Function.identity());
    }

    private void addNextVersionValid(Map<String, PplListVo> nextVersionValidMap, List<PplListVo> result,
            PplInnerProcessVersionDO versionDO) {
        if (ListUtils.isNotEmpty(nextVersionValidMap) && result != null && versionDO != null) {
            List<String> productList = versionDO.parseToProductList();
            // 剩余的都是本周期没有、下周期新增的下期生效数据
            nextVersionValidMap.forEach((k, v) -> {
                if (v != null && v.getProduct() != null && productList.contains(v.getProduct())) {
                    v.setNextVersionValid(true);
                    result.add(v);
                }
            });
        }
    }

    /**
     * @param req
     * @return
     */
    public List<PplListVo> queryPendingData(QueryPplDraftReq req) {
        if (req.getVersionId() == null) {
            throw new BizException("未传版本Id");
        }
        PplInnerProcessVersionDO versionDO = innerVersionService.getByVersionId(req.getVersionId());
        String userName = LoginUtils.getUserNameWithSystem();

        WhereContent whereContent = pplInnerProcessService.checkPermission(userName, req.getIndustryDept(), "",
                req.getVersionId());
        if (whereContent == null) {
            whereContent = new WhereContent();
            whereContent.andEqual(PplOrderDO::getSubmitUser, userName);
        }

        WhereContent queryMinSql = new WhereContent();

        WhereContent querySql = new WhereContent();

        WhereContent itemWhere = new WhereContent();

        if (req.getQueryIsNeedDeal()){
            PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    "where process_id = ? and id > ? order by id asc limit 1 ",
                    versionDO.getProcessId(), versionDO.getId());
            if (nextVersion == null) {
                return new ArrayList<>();
            }
            querySql.addAnd("demand_type != ?",PplDemandTypeEnum.RETURN.getCode());
            List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
            querySql.addAnd(" (region_name not in (?) and begin_buy_date < ?) or "
                            + "(region_name in (?) and begin_buy_date < ?)",
                    overSeaRegions,nextVersion.getInCountryDemandYearMonthFirstDay(),
                    overSeaRegions,nextVersion.getOverseasDemandYearMonthFirstDay());

            whereContent.addAnd("source != ?",DraftStatusEnum.DELETED.getCode());
        }

        whereContent.andNotEqual(PplOrderDO::getNodeCode, "");
        whereContent.andInIfValueNotEmpty(PplOrderDO::getCustomerUin, req.getCustomerUin());
        whereContent.andInIfValueNotEmpty(PplOrderDO::getCustomerShortName, req.getCustomerShortName());
        whereContent.andInIfValueNotEmpty(PplOrderDO::getPplOrder, req.getPplOrder());
        whereContent.andEqualIfValueNotEmpty(PplOrderDO::getIndustryDept, req.getIndustryDept());
        whereContent.andInIfValueNotEmpty(PplOrderDO::getWarZone, req.getWarZone());
        log.info("针对单据级别的筛选");
        Map<String, PplOrderDO> pplOrderMap = demandDBHelper.getAll(PplOrderDO.class, whereContent.getSql(),
                whereContent.getParams()).stream().collect(Collectors.toMap(PplOrderDO::getPplOrder, s -> s));

        // 下期生效的数据
        Map<String, PplListVo> nextVersionValidMap = req.getNextVersionValidMap();

        if (pplOrderMap.isEmpty()) {
            List<PplListVo> result = new ArrayList<>();
            // 剩余的都是本周期没有、下周期新增的下期生效数据
            addNextVersionValid(nextVersionValidMap, result, versionDO);
            return result;
        }

        List<String> ids = demandDBHelper.getRaw(String.class,
                "select MAX(id) as id  from  ppl_order_audit_record where version_id = ? and ppl_order in (?)"
                        + "group by ppl_order ", req.getVersionId(), pplOrderMap.keySet());
        if (ids.isEmpty()) {
            List<PplListVo> result = new ArrayList<>();
            // 剩余的都是本周期没有、下周期新增的下期生效数据
            addNextVersionValid(nextVersionValidMap, result, versionDO);
            return result;
        }

        List<String> minIds = demandDBHelper.getRaw(String.class,
                "select MIN(id) as id  from  ppl_order_audit_record where version_id = ? and ppl_order in (?)"
                        + "group by ppl_order ", req.getVersionId(), pplOrderMap.keySet());

        itemWhere.andInIfValueNotEmpty(PplItemDO::getRegionName, req.getRegionName());
        itemWhere.andInIfValueNotEmpty(PplItemDO::getZoneName, req.getZoneName());
        itemWhere.andInIfValueNotEmpty(PplItemDO::getInstanceType, req.getInstanceType());

        itemWhere.andInIfValueNotEmpty(PplItemDO::getProjectName, req.getProjectName());
        itemWhere.andInIfValueNotEmpty(PplItemDO::getDemandType, req.getDemandType());
        itemWhere.andInIfValueNotEmpty(PplItemDO::getProduct, req.getProduct());
        itemWhere.andInIfValueNotEmpty(PplItemDO::getDatabaseName, req.getDatabaseName());
        itemWhere.andEqualIfValueNotEmpty(PplItemDO::getStatus, req.getItemStatus());

        if ("国内".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            itemWhere.andNotIn(PplItemDraftDO::getRegionName, overSeaRegion);
        } else if ("境外".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            itemWhere.andIn(PplItemDraftDO::getRegionName, overSeaRegion);
        }

//        if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
//            itemWhere.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
//        }
        if (Strings.isNotBlank(req.getStartYearMonth())) {
            itemWhere.addAnd("begin_buy_date  >=  ? ", req.getStartYearMonth());
        }
        if (Strings.isNotBlank(req.getEndYearMonth())) {
            itemWhere.addAnd("begin_buy_date  <=  ? ", req.getEndYearMonth());
        }
        if (itemWhere.getParams().length > 0) {
            log.info("根明细条件反查单据");
            itemWhere.addAnd("audit_record_id in (?)", ids);
            itemWhere.addAnd("deleted =0");
            List<String> orderList = demandDBHelper.getRaw(String.class,
                    "select  ppl_order from ppl_order_audit_record_item" + itemWhere.getSql(),
                    itemWhere.getParams());
            Set<String> orders = new HashSet<>(orderList);
            if (orders.isEmpty()) {
                return Lists.newArrayList();
            }
            querySql.addAnd(" ppl_order in (?)", orders);
            queryMinSql.addAnd(" ppl_order in (?)", orders);
        }
        querySql.addAnd("audit_record_id in (?)", ids);
        queryMinSql.addAnd("audit_record_id in (?)", minIds);


        Map<String, List<PplOrderAuditRecordItemDO>> oldAuditMap = demandDBHelper.getAll(
                PplOrderAuditRecordItemDO.class, queryMinSql.getSql(), queryMinSql.getParams()).stream().collect(
                Collectors.groupingBy(PplOrderAuditRecordItemDO::getPplOrder));

        Map<String, PplInnerProcessNodeDO> nodeMap = getNodeMap(req.getIndustryDept());

        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_order in (?)", pplOrderMap.keySet());

        //doto 待补齐过滤条件
        List<PplListVo> result = demandDBHelper.getAll(PplOrderAuditRecordItemDO.class, querySql.getSql(),
                        querySql.getParams())
                .stream().collect(Collectors.groupingBy(PplOrderAuditRecordItemDO::getPplOrder)).entrySet().stream()
                .map(entry -> {
                    PplListVo pto = new PplListVo();
                    PplOrderDO orderDO = pplOrderMap.get(entry.getKey());
                    PplOrderAuditRecordItemDO anyOne = entry.getValue().get(0);

                    PplListVo nextVersionValid = nextVersionValidMap.remove(orderDO.getPplOrder());
                    if (nextVersionValid != null) {
                        pto.setNextVersionValidData(nextVersionValid);
                        pto.setNextVersionValid(true);
                    }
                    pto.setUpdateTime(orderDO.getUpdateTime());
                    pto.setSubmitUser(orderDO.getSubmitUser());
                    pto.setChangeType(OperateTypeEnum.getByCode(orderDO.getChangeType()).getName());
                    pto.setType(orderDO.getChangeType());
                    pto.setCustomerUin(orderDO.getCustomerUin());
                    pto.setCustomerShortName(orderDO.getCustomerShortName());
                    pto.setCustomerType(orderDO.getCustomerType());
                    pto.setIndustryDept(orderDO.getIndustryDept());
                    pto.setProjectName(anyOne.getProjectName());
                    pto.setDemandType(PplDemandTypeEnum.getByCode(anyOne.getDemandType()).getName());
                    pto.setBeginBuyDate(anyOne.getBeginBuyDate().toString());
                    pto.setEndBuyDate(anyOne.getEndBuyDate().toString());
                    pto.setWarZone(orderDO.getWarZone());
                    pto.setPplOrder(entry.getKey());
                    pto.setPplSource(PplOrderSourceEnum.getNameByPplOrder(pto.getPplOrder()));
                    if (nodeMap.containsKey(orderDO.getNodeCode())) {
                        val np = nodeMap.get(orderDO.getNodeCode());
                        pto.setApprovalNode(np.getNodeName());
                        if (np.getIsBeginNode() && PplOrderAuditStatusEnum.WAIT.getCode()
                                .equals(orderDO.getAuditStatus())) {
                            pto.setWithdrawable(1);
                        }
                    } else {
                        pto.setApprovalNode(orderDO.getNodeCode());
                    }
                    pto.setApprover(orderDO.getCurrentProcessor());
                    pto.setApprovalStatus(PplOrderAuditStatusEnum.getNameByCode(orderDO.getAuditStatus()));

                    val items = entry.getValue().stream().map(item -> {

                        DraftItemDTO row = new DraftItemDTO();
                        row.setPplId(item.getPplId());
                        row.setParentPplId(item.getParentPplId());

                        row.setStatus(item.getStatus());
                        row.setYunxiaoOrderId(item.getYunxiaoOrderId());
                        row.setYunxiaoDetailId(item.getYunxiaoDetailId());
                        row.setOrderNumberId(item.getOrderNumberId());
                        row.setYunxiaoOrderStatus(item.getYunxiaoOrderStatus());
                        row.setBeforeNewApplyTotalInstanceNum(item.getInstanceNumApplyBefore());
                        row.setAfterNewApplyTotalInstanceNum(item.getInstanceNumApplyAfter());
                        row.setBeforeNewApplyTotalCore(item.getTotalCoreApplyBefore());
                        row.setAfterNewApplyTotalCore(item.getTotalCoreApplyAfter());
                        row.setBeforeNewApplyTotalGpuNum(item.getTotalGpuNumApplyBefore());
                        row.setAfterNewApplyTotalGpuNum(item.getTotalGpuNumApplyAfter());

                        row.setProduct(item.getProduct());
                        row.setDemandType(item.getDemandType());
                        row.setDemandScene(item.getDemandScene());
                        row.setProjectName(item.getProjectName());
                        row.setBillType(item.getBillType());
                        row.setWinRate(item.getWinRate());
                        row.setImportantDemand(item.getImportantDemand());

                        row.setBeginBuyDate(item.getBeginBuyDate().toString());
                        row.setEndBuyDate(item.getEndBuyDate().toString());

                        row.setBeginElasticDate(DateUtils.format(item.getBeginElasticDate(), "HH:mm"));
                        row.setEndElasticDate(DateUtils.format(item.getEndElasticDate(), "HH:mm"));
                        row.setNote(item.getNote());
                        row.setRegionName(item.getRegionName());
                        row.setIsStrongDesignateZone(item.getIsStrongDesignateZone());
                        row.setZoneName(item.getZoneName());
                        row.setInstanceType(item.getInstanceType());
                        row.setInstanceModel(item.getInstanceModel());
                        row.setInstanceNum(item.getInstanceNum());
                        if (Strings.isNotBlank(item.getAlternativeInstanceType())) {
                            row.setAlternativeInstanceType(
                                    Splitter.on(";").trimResults().splitToList(item.getAlternativeInstanceType()));
                        }
                        if (Strings.isNotBlank(item.getAlternativeZoneName())) {
                            row.setAlternativeZoneName(
                                    Splitter.on(";").trimResults().splitToList(item.getAlternativeZoneName()));
                        }
                        row.setAffinityType(item.getAffinityType());
                        row.setAffinityValue(item.getAffinityValue());
                        row.setSystemDiskType(item.getSystemDiskType());
                        row.setSystemDiskStorage(item.getSystemDiskStorage());
                        if (item.getSystemDiskNum() == null) {
                            row.setSystemDiskNum(1);
                        }
                        row.setSystemDiskNum(item.getSystemDiskNum());
                        row.setDataDiskType(item.getDataDiskType());
                        row.setDataDiskStorage(item.getDataDiskStorage());
                        row.setDataDiskNum(item.getDataDiskNum());
                        row.setCbsIo(item.getCbsIo());
                        row.setTotalCoreNum(item.getTotalCore());
                        row.setTotalDiskNum(item.getTotalDisk());
                        row.setType(OperateTypeEnum.UPDATE.getCode());
                        row.setProduct(item.getProduct());
                        // gpu相关字段
                        row.setGpuProductType(item.getGpuProductType());
                        row.setGpuType(item.getGpuType());
                        row.setGpuNum(item.getGpuNum());
                        if (Strings.isNotBlank(item.getAcceptGpu())) {
                            row.setAcceptGpu(Splitter.on(";").trimResults().splitToList(item.getAcceptGpu()));

                        }
                        row.setIsAcceptAdjust(item.getIsAcceptAdjust());
                        row.setTotalGpuNum(item.getTotalGpuNum());
                        row.setBizScene(item.getBizScene());
                        row.setBizDetail(item.getBizDetail());
                        row.setServiceTime(item.getServiceTime());
                        row.setSaleDurationYear(item.getSaleDurationYear());
                        row.setBusinessCpq(item.getBusinessCpq());
                        row.setApplyDiscount(item.getApplyDiscount());

                        row.setBizId(item.getBizId());
                        row.setPlacementGroupList(item.placementGroupListGet());

                        row.setDatabaseName(item.getDatabaseName());
                        row.setMoreThanOneAZ(item.getMoreThanOneAZ());
                        row.setDatabaseStorageType(item.getDatabaseStorageType());
                        row.setDeployType(item.getDeployType());
                        row.setFrameworkType(item.getFrameworkType());
                        row.setSliceNum(item.getSliceNum());
                        row.setReplicaNum(item.getReplicaNum());
                        row.setReadOnlyNum(item.getReadOnlyNum());
                        row.setDatabaseSpecs(item.getDatabaseSpecs());
                        row.setDatabaseStorage(item.getDatabaseStorage());
                        row.setTotalDatabaseStorage(item.getTotalDatabaseStorage());

                        row.setCosStorageType(item.getCosStorageType());
                        row.setCosAZ(item.getCosAZ());
                        row.setCosStorage(item.getCosStorage());
                        row.setTotalCosStorage(item.getTotalCosStorage());
                        row.setBandwidth(item.getBandwidth());
                        row.setQps(item.getQps());

                        row.setInstanceModelCoreNum(item.getInstanceModelCoreNum());
                        row.setInstanceModelRamNum(item.getInstanceModelRamNum());
                        row.setTotalMemory(item.getTotalMemory());

                        return row;
                    }).collect(Collectors.toList());
                    pto.setPplItems(items);
                    if (!CollectionUtils.isEmpty(items)) {
                        pto.setProduct(items.get(0).getProduct());
                    }
                    // 计算一些pplOrder级别的总指标数据
                    pto.calcAndSetOrderLevelIndex(oldAuditMap.get(entry.getKey()), true);
                    return pto;
                }).collect(Collectors.toList());

        if (nextVersionValidMap.size() > 0) {
            // 剩余的都是本周期没有、下周期新增的下期生效数据
            addNextVersionValid(nextVersionValidMap, result, versionDO);
        }

        // pplOrder维度加两个字段：共识状态、供应方案, 并设置ppl明细的共识信息
        addConsensusSupplyPlan(result);

        // 处理数据
        List<String> orderNumberList = result.stream().flatMap(v -> v.getPplItems().stream())
                .filter(v -> v.getYunxiaoOrderId() != null &&
                        (v.getYunxiaoOrderId().startsWith("OE") || v.getYunxiaoOrderId().startsWith("ON")))
                .map(PplOrderItemDTO::getYunxiaoOrderId).distinct().collect(Collectors.toList());

        List<OrderInfoDO> orderList = demandDBHelper.getAll(OrderInfoDO.class,
                "where available_status = ? and order_number in (?)",
                OrderAvailableStatusEnum.AVAILABLE.getCode(), orderNumberList);
        Map<String, String> orderMap = orderList.stream()
                .collect(Collectors.toMap(OrderInfoDO::getOrderNumber, OrderInfoDO::getOrderNodeCode));

        // 设置ppl_item的预约状态
//        completeItemApplyInfo(result);

        // 获取 order 级别的总预约核心数等
        for (PplListVo pplListVo : result) {
            List<DraftItemDTO> items = pplListVo.getPplItems();
            pplListVo.setApplyTotalCore(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalCore).intValue());
            pplListVo.setApplyInstanceNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalInstanceNum).intValue());
            pplListVo.setApplyTotalGpuNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalGpuNum).intValue());

            pplListVo.setNotApplyInstanceNum(pplListVo.getTotalInstanceNum() - pplListVo.getApplyInstanceNum());
            pplListVo.setNotApplyTotalCore(pplListVo.getTotalCore() - pplListVo.getApplyTotalCore());
            pplListVo.setNotApplyTotalGpuNum(pplListVo.getTotalGpu() - pplListVo.getApplyTotalGpuNum());

            orderAppliedStatusDeal(pplListVo, orderMap);

        }

        return result;

    }

    @Override
    public List<PplListVo> queryCurrentVersionData(QueryPplDraftReq req) {
        if (req.getVersionId() == null) {
            throw new BizException("版本Id不能为空");
        }
        if (req.getQueryIsNeedDeal()) {
            // 去除年月查询条件
            req.setStartYearMonth(null);
            req.setEndYearMonth(null);
            Boolean isSameYearMonth = innerVersionService.checkNextVersionIsSameYearMonth(req.getVersionId());
            // 如果当前版本和下一个版本是相同年月，代表当前并不是需求年月录入的最后窗口期，
            if (isSameYearMonth) {
                return new ArrayList<>();
            }
        }

        PplInnerProcessVersionSlaDTO enterNode = innerVersionService.getEnterNode(req.getVersionId());
        List<PplListVo> currentVersionPpl = new ArrayList<>();
        // 判断查draftItem 还是 auditRecordItem
        if (enterNode.getDeadlineTime() == null || enterNode.getDeadlineTime().after(new Date())) {
            // 还没到需求沟通截止时间 查draftItem
            currentVersionPpl = queryPreSubmitDraftData(req);
        } else {
            // 进入审批 查auditRecordItem
            currentVersionPpl = queryPendingData(req);
        }

        return currentVersionPpl;
    }


    @Override
    public List<PplListVo> queryNewIndustryDemandDetail(QueryPplDraftReq req) {
        List<PplListVo> result = new ArrayList<>();
        List<String> products = req.getProduct();
        if (CollectionUtils.isEmpty(products)) {
            products = Ppl13weekProductTypeEnum.getAllProductNameList();
        }

        // 下期生效的数据
        Map<String, PplListVo> nextVersionValidMap = nextVersionValidDataMap(req);
        req.setNextVersionValidMap(nextVersionValidMap);

        // 查询当前行业是否有两个进行中的版本
        List<PplInnerProcessVersionDO> allVersion = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where status = ? and industry_dept = ?",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode(), req.getIndustryDept());
        if (allVersion.size() == 1) {
            req.setVersionId(allVersion.get(0).getId());
            result = queryCurrentVersionData(req);
        } else {
            List<PplListVo> all = new ArrayList<>();
            // 如果有多个版本的话,就分开查产品 如行业一部
            if (ListUtils.isNotEmpty(products)) {
                // CVM 请求
                for (PplInnerProcessVersionDO version : allVersion) {
                    List<String> allProducts = new ArrayList<>(products);
                    allProducts.retainAll(version.parseToProductList());
                    if (ListUtils.isEmpty(allProducts)) {
                        continue;
                    }
                    req.setProduct(allProducts);
                    req.setVersionId(version.getId());
                    List<PplListVo> data = queryCurrentVersionData(req);
                    if (ListUtils.isNotEmpty(data)) {
                        all.addAll(data);
                    }
                }
            }
            result = all;
        }
        return result.stream().sorted(Comparator.comparing(PplListVo::getUpdateTime).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 需要保障明细的完整性
     *
     * @param req
     * @return
     */
    @Override
    public List<PplListVo> queryDraftData(QueryPplDraftReq req) {
        WhereContent draftSql = new WhereContent();
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerUin, req.getCustomerUin());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerShortName, req.getCustomerShortName());
        if (!req.isSkipLoginUser()) {
            draftSql.andEqual(PplOrderDraftDO::getSubmitUser, LoginUtils.getUserName());
        }
        draftSql.andEqualIfValueNotEmpty(PplOrderDraftDO::getIndustryDept, req.getIndustryDept());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getWarZone, req.getWarZone());
        draftSql.andEqual(PplOrderDraftDO::getDraftStatus, req.getStatus());

        WhereContent draftItemSql = new WhereContent();
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getRegionName, req.getRegionName());
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getZoneName, req.getZoneName());
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getInstanceType, req.getInstanceType());
        draftItemSql.andEqual(PplItemDraftDO::getDraftStatus, req.getStatus());

        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getProjectName, req.getProjectName());
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getDemandType, req.getDemandType());
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getProduct, req.getProduct());
        draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getDatabaseName, req.getDatabaseName());

        if ("国内".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            draftItemSql.andNotIn(PplItemDraftDO::getRegionName, overSeaRegion);
        } else if ("境外".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            draftItemSql.andIn(PplItemDraftDO::getRegionName, overSeaRegion);
        }

        if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
            draftItemSql.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
        }
        if (draftItemSql.getParams().length > 0) {
            log.info("需要从明细过滤单据");
            draftItemSql.andEqual("deleted", 0);
            List<String> orderList = demandDBHelper.getRaw(String.class,
                    "select ppl_order from ppl_item_draft" + draftItemSql.getSql(),
                    draftItemSql.getParams());
            Set<String> orders = new HashSet<>(orderList);

            // 如果用户指定了pplOrder，则取交集
            if (ListUtils.isNotEmpty(req.getPplOrder())) {
                orders.retainAll(req.getPplOrder());
            }

            if (orders.isEmpty()) {
                return Lists.newArrayList();
            }
            draftSql.andIn(PplOrderDraftDO::getPplOrder, orders);
        }

        val orders = demandDBHelper.getAll(PplOrderDraftDO.class, draftSql.getSql(), draftSql.getParams());

        Map<String, List<PplItemDO>> oldAuditMap;
        if (ListUtils.isNotEmpty(orders)) {
            oldAuditMap = demandDBHelper.getAll(
                            PplItemDO.class, "where deleted = 0 and  ppl_order in (?) ",
                            ListUtils.transform(orders, PplOrderBaseDO::getPplOrder))
                    .stream().collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        } else {
            oldAuditMap = new HashMap<>();
        }

        List<PplListVo> result = orders.stream().map(d -> {
            PplListVo dto = new PplListVo();
            dto.setChangeType(DraftStatusEnum.getNameByCode(d.getSource()));
            dto.setType(d.getSource());
            dto.setCustomerUin(d.getCustomerUin());
            dto.setCustomerShortName(d.getCustomerShortName());
            dto.setIndustryDept(d.getIndustryDept());
            dto.setProjectName(d.getProjectName());
            dto.setDemandType(PplDemandTypeEnum.getNameByCode(d.getDemandType()));
            dto.setWarZone(d.getWarZone());
            dto.setPplOrder(d.getPplOrder());
            dto.setPplSource(PplOrderSourceEnum.getNameByPplOrder(dto.getPplOrder()));
            dto.setCustomerType(d.getCustomerType());
            dto.setModifyReason(d.getModifyReason());
            dto.setSubmitUser(d.getSubmitUser());
            dto.setUpdateTime(d.getUpdateTime());
            dto.setBeginBuyDate(d.getBeginDate());
            dto.setEndBuyDate(d.getEndDate());
            if (PplOrderDraftStatusEnum.NEXT_VERSION_VALID.getCode().equals(d.getDraftStatus())) {
                dto.setNextVersionValid(true);
            }

            val items = demandDBHelper.getAll(PplItemDraftDO.class, "where ppl_order = ? and draft_status = ?",
                    d.getPplOrder(), req.getStatus()).stream().map(item -> {
                DraftItemDTO row = new DraftItemDTO();
                row.setProduct(item.getProduct());
                row.setDemandType(item.getDemandType());
                row.setDemandScene(item.getDemandScene());
                row.setProjectName(item.getProjectName());
                row.setBillType(item.getBillType());
                row.setWinRate(item.getWinRate());
                row.setImportantDemand(item.getImportantDemand());

                row.setBeginBuyDate(item.getBeginBuyDate().toString());
                if (item.getEndBuyDate() != null) {
                    row.setEndBuyDate(item.getEndBuyDate().toString());
                }

                row.setBeginElasticDate(DateUtils.format(item.getBeginElasticDate(), "HH:mm"));

                row.setEndElasticDate(DateUtils.format(item.getEndElasticDate(), "HH:mm"));

                row.setNote(item.getNote());
                row.setRegionName(item.getRegionName());
                row.setIsStrongDesignateZone(item.getIsStrongDesignateZone());
                row.setZoneName(item.getZoneName());
                row.setInstanceType(item.getInstanceType());
                row.setInstanceModel(item.getInstanceModel());
                row.setInstanceNum(item.getInstanceNum());
                if (Strings.isNotBlank(item.getAlternativeInstanceType())) {
                    row.setAlternativeInstanceType(
                            Splitter.on(";").trimResults().splitToList(item.getAlternativeInstanceType()));
                }
                if (Strings.isNotBlank(item.getAlternativeZoneName())) {
                    row.setAlternativeZoneName(
                            Splitter.on(";").trimResults().splitToList(item.getAlternativeZoneName()));
                }
                row.setAffinityType(item.getAffinityType());
                row.setAffinityValue(item.getAffinityValue());
                row.setSystemDiskType(item.getSystemDiskType());
                row.setSystemDiskStorage(item.getSystemDiskStorage());
                if (item.getSystemDiskNum() == null) {
                    row.setSystemDiskNum(1);
                }
                row.setSystemDiskNum(item.getSystemDiskNum());
                row.setDataDiskType(item.getDataDiskType());
                row.setDataDiskStorage(item.getDataDiskStorage());
                row.setDataDiskNum(item.getDataDiskNum());
                row.setCbsIo(item.getCbsIo());
                row.setTotalCoreNum(item.getTotalCore());
                row.setTotalDiskNum(item.getTotalDisk());
                row.setType(item.getType());
                row.setPplId(item.getPplId());
                row.setParentPplId(item.getParentPplId());

                row.setStatus(item.getStatus());
                row.setYunxiaoOrderId(item.getYunxiaoOrderId());
                row.setYunxiaoDetailId(item.getYunxiaoDetailId());
                row.setYunxiaoOrderStatus(item.getYunxiaoOrderStatus());
                row.setBeforeNewApplyTotalInstanceNum(item.getInstanceNumApplyBefore());
                row.setAfterNewApplyTotalInstanceNum(item.getInstanceNumApplyAfter());
                row.setBeforeNewApplyTotalCore(item.getTotalCoreApplyBefore());
                row.setAfterNewApplyTotalCore(item.getTotalCoreApplyAfter());
                row.setBeforeNewApplyTotalGpuNum(item.getTotalGpuNumApplyBefore());
                row.setAfterNewApplyTotalGpuNum(item.getTotalGpuNumApplyAfter());

                row.setProduct(item.getProduct());
                // gpu相关字段
                row.setGpuProductType(item.getGpuProductType());
                row.setGpuType(item.getGpuType());
                row.setGpuNum(item.getGpuNum());
                if (Strings.isNotBlank(item.getAcceptGpu())) {
                    row.setAcceptGpu(Splitter.on(";").trimResults().splitToList(item.getAcceptGpu()));

                }
                row.setIsAcceptAdjust(item.getIsAcceptAdjust());
                row.setTotalGpuNum(item.getTotalGpuNum());
                row.setBizScene(item.getBizScene());
                row.setBizDetail(item.getBizDetail());
                row.setServiceTime(item.getServiceTime());
                row.setSaleDurationYear(item.getSaleDurationYear());
                row.setBusinessCpq(item.getBusinessCpq());
                row.setApplyDiscount(item.getApplyDiscount());

                //战略客户部独有字段
                row.setBizId(item.getBizId());
                row.setPlacementGroupList(item.placementGroupListGet());

                row.setDatabaseName(item.getDatabaseName());
                row.setMoreThanOneAZ(item.getMoreThanOneAZ());
                row.setDatabaseStorageType(item.getDatabaseStorageType());
                row.setDeployType(item.getDeployType());
                row.setFrameworkType(item.getFrameworkType());
                row.setSliceNum(item.getSliceNum());
                row.setReplicaNum(item.getReplicaNum());
                row.setReadOnlyNum(item.getReadOnlyNum());
                row.setDatabaseSpecs(item.getDatabaseSpecs());
                row.setDatabaseStorage(item.getDatabaseStorage());
                row.setTotalDatabaseStorage(item.getTotalDatabaseStorage());

                row.setCosStorageType(item.getCosStorageType());
                row.setCosAZ(item.getCosAZ());
                row.setCosStorage(item.getCosStorage());
                row.setTotalCosStorage(item.getTotalCosStorage());
                row.setBandwidth(item.getBandwidth());
                row.setQps(item.getQps());

                row.setInstanceModelCoreNum(item.getInstanceModelCoreNum());
                row.setInstanceModelRamNum(item.getInstanceModelRamNum());
                row.setTotalMemory(item.getTotalMemory());

                return row;
            }).collect(Collectors.toList());
            dto.setPplItems(items);
            if (!CollectionUtils.isEmpty(items)) {
                dto.setProduct(items.get(0).getProduct());
                dto.setWinRate(items.get(0).getWinRate());
                dto.setBeginBuyDate(items.get(0).getBeginBuyDate());
                dto.setEndBuyDate(items.get(0).getEndBuyDate());
            }
            // 计算一些pplOrder级别的总指标数据
            dto.calcAndSetOrderLevelIndex(oldAuditMap.get(d.getPplOrder()), true);
            return dto;
        }).collect(Collectors.toList());

        // pplOrder维度加两个字段：共识状态、供应方案,并设置ppl明细的共识信息
        addConsensusSupplyPlan(result);

        // 设置ppl_item的预约状态
        completeItemApplyInfo(result);

        List<String> orderNumberList = result.stream().flatMap(v -> v.getPplItems().stream())
                .filter(v -> v.getYunxiaoOrderId() != null &&
                        (v.getYunxiaoOrderId().startsWith("OE") || v.getYunxiaoOrderId().startsWith("ON")))
                .map(PplOrderItemDTO::getYunxiaoOrderId).distinct().collect(Collectors.toList());

        List<OrderInfoDO> orderList = demandDBHelper.getAll(OrderInfoDO.class,
                "where available_status = ? and order_number in (?)",
                OrderAvailableStatusEnum.AVAILABLE.getCode(), orderNumberList);
        Map<String, String> orderMap = orderList.stream()
                .collect(Collectors.toMap(OrderInfoDO::getOrderNumber, OrderInfoDO::getOrderNodeCode));

        // 获取 order 级别的总预约核心数等
        for (PplListVo pplListVo : result) {
            List<DraftItemDTO> items = pplListVo.getPplItems();
            pplListVo.setApplyTotalCore(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalCore).intValue());
            pplListVo.setApplyInstanceNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalInstanceNum).intValue());
            pplListVo.setApplyTotalGpuNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalGpuNum).intValue());

            pplListVo.setNotApplyInstanceNum(pplListVo.getTotalInstanceNum() - pplListVo.getApplyInstanceNum());
            pplListVo.setNotApplyTotalCore(pplListVo.getTotalCore() - pplListVo.getApplyTotalCore());
            pplListVo.setNotApplyTotalGpuNum(pplListVo.getTotalGpu() - pplListVo.getApplyTotalGpuNum());

            orderAppliedStatusDeal(pplListVo, orderMap);
        }

        return result;
    }

    /**
     * result 的 ppl-item维度 补充 预约信息
     * (result - 草稿 / 审批记录)
     */
    private void completeItemApplyInfo(List<PplListVo> result) {
        List<DraftItemDTO> allItems = result.stream().flatMap(e -> e.getPplItems().stream())
                .collect(Collectors.toList());
        List<String> pplIds = allItems.stream().map(DraftItemDTO::getPplId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pplIds)) {
            return;
        }

        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class,
                "where ppl_id in(?) ",
                pplIds);
        pplItemDOList.addAll(demandDBHelper.getAll(PplItemDO.class,
                "where parent_ppl_id is not null and parent_ppl_id in (?) ", pplIds));
        pplItemDOList = pplItemDOList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<String, PplItemDO> itemDOMap = pplItemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<PplItemDO>> itemDOGroupMap = pplItemDOList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(PplItemDO::getParentPplId));

        Map<String, List<DraftItemDTO>> splitItemDOMap = allItems.stream()
                .filter(e -> StringUtils.isNotBlank(e.getParentPplId()))
                .collect(Collectors.groupingBy(DraftItemDTO::getParentPplId));

        allItems.forEach(e -> {
            PplItemDO currentItemDO = itemDOMap.get(e.getPplId());

            boolean isUnAppliedDraft =
                    currentItemDO == null && !PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus());
            boolean isUnAppliedValid =
                    currentItemDO != null && !PplItemStatusEnum.APPLIED.getCode().equals(currentItemDO.getStatus());
            if (isUnAppliedDraft || isUnAppliedValid) {
                // 1、非预约单，直接设置预约相关信息为默认值
                e.setStatus(PplItemStatusEnum.VALID.getCode());
                e.setYunxiaoOrderId("");
                e.setYunxiaoDetailId(0L);
                e.setYunxiaoOrderStatus("");

                e.setAfterNewApplyTotalInstanceNum(0);
                e.setBeforeNewApplyTotalInstanceNum(0);
                e.setDiffNewApplyTotalInstanceNum(0);

                e.setAfterNewApplyTotalCore(0);
                e.setBeforeNewApplyTotalCore(0);
                e.setDiffNewApplyTotalCore(0);

                e.setAfterNewApplyTotalGpuNum(BigDecimal.ZERO);
                e.setBeforeNewApplyTotalGpuNum(BigDecimal.ZERO);
                e.setDiffNewApplyTotalGpuNum(BigDecimal.ZERO);
                return;
            }

            // 2、预约单
            if (currentItemDO == null) {
                // 2.1、无对应生效的PN单，则直接取当前记录的预约信息
                e.setAfterNewApplyTotalInstanceNum(
                        e.getAfterNewApplyTotalInstanceNum() == null ? 0 : e.getAfterNewApplyTotalInstanceNum());
                e.setBeforeNewApplyTotalInstanceNum(
                        e.getBeforeNewApplyTotalInstanceNum() == null ? 0 : e.getBeforeNewApplyTotalInstanceNum());
                e.setDiffNewApplyTotalInstanceNum(
                        e.getAfterNewApplyTotalInstanceNum() - e.getBeforeNewApplyTotalInstanceNum());

                e.setAfterNewApplyTotalCore(e.getAfterNewApplyTotalCore() == null ? 0 : e.getAfterNewApplyTotalCore());
                e.setBeforeNewApplyTotalCore(
                        e.getBeforeNewApplyTotalCore() == null ? 0 : e.getBeforeNewApplyTotalCore());
                e.setDiffNewApplyTotalCore(e.getAfterNewApplyTotalCore() - e.getBeforeNewApplyTotalCore());

                e.setAfterNewApplyTotalGpuNum(
                        e.getAfterNewApplyTotalGpuNum() == null ? BigDecimal.ZERO : e.getAfterNewApplyTotalGpuNum());
                e.setBeforeNewApplyTotalGpuNum(
                        e.getBeforeNewApplyTotalGpuNum() == null ? BigDecimal.ZERO : e.getBeforeNewApplyTotalGpuNum());
                e.setDiffNewApplyTotalGpuNum(
                        e.getAfterNewApplyTotalGpuNum().subtract(e.getBeforeNewApplyTotalGpuNum()));
                return;
            }

            // 2.2、有对应生效的PN单，则取生效PN单的预约信息
            // 考虑拆单情况：若未拆单/是子单/是母单且需求沟通中有拆单信息(isCurrentItem=true)，则取本单的预约信息及预约量即可；若是母单(isCurrentItem=false)，且拆单信息未同步到需求沟通，则取母单的预约信息，预约量聚合获得
            boolean isCurrentItem =
                    (StringUtils.isBlank(currentItemDO.getParentPplId())
                            || !currentItemDO.getPplId().equals(currentItemDO.getParentPplId())) || (
                            splitItemDOMap.get(currentItemDO.getParentPplId()) != null
                                    && splitItemDOMap.get(currentItemDO.getParentPplId()).size() > 1);

            // 若拆单(isSplit = true)，则获取同一母单下的所有ppl-item
            boolean isSplit = StringUtils.isNotBlank(currentItemDO.getParentPplId());
            List<PplItemDO> applyItemDOs =
                    isSplit ? (itemDOGroupMap.get(currentItemDO.getParentPplId()) == null ? Collections.emptyList()
                            : itemDOGroupMap.get(currentItemDO.getParentPplId()).stream()
                                    .filter(o -> PplItemStatusEnum.APPLIED.getCode().equals(o.getStatus()))
                                    .collect(Collectors.toList())) : Collections.emptyList();

            // 设置预约相关信息
            e.setParentPplId(currentItemDO.getParentPplId());

            e.setStatus(currentItemDO.getStatus());
            e.setYunxiaoOrderId(currentItemDO.getYunxiaoOrderId());
            e.setYunxiaoDetailId(currentItemDO.getYunxiaoDetailId());
            e.setYunxiaoOrderStatus(currentItemDO.getYunxiaoOrderStatus());

            e.setAfterNewApplyTotalInstanceNum(isCurrentItem ? (currentItemDO.getInstanceNumApplyAfter() == null ? 0
                    : currentItemDO.getInstanceNumApplyAfter())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getInstanceNumApplyAfter() != null ? o.getInstanceNumApplyAfter() : 0)
                            .sum());
            e.setBeforeNewApplyTotalInstanceNum(isCurrentItem ? (currentItemDO.getInstanceNumApplyBefore() == null ? 0
                    : currentItemDO.getInstanceNumApplyBefore())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getInstanceNumApplyBefore() != null ? o.getInstanceNumApplyBefore() : 0)
                            .sum());
            e.setDiffNewApplyTotalInstanceNum(
                    e.getAfterNewApplyTotalInstanceNum() - e.getBeforeNewApplyTotalInstanceNum());

            e.setAfterNewApplyTotalCore(isCurrentItem ? (currentItemDO.getTotalCoreApplyAfter() == null ? 0
                    : currentItemDO.getTotalCoreApplyAfter())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getTotalCoreApplyAfter() != null ? o.getTotalCoreApplyAfter() : 0)
                            .sum());
            e.setBeforeNewApplyTotalCore(isCurrentItem ? (currentItemDO.getTotalCoreApplyBefore() == null ? 0
                    : currentItemDO.getTotalCoreApplyBefore())
                    : applyItemDOs.stream().mapToInt(
                                    o -> o.getTotalCoreApplyBefore() != null ? o.getTotalCoreApplyBefore() : 0)
                            .sum());
            e.setDiffNewApplyTotalCore(e.getAfterNewApplyTotalCore() - e.getBeforeNewApplyTotalCore());

            if (Ppl13weekProductTypeEnum.GPU.getName().equals(e.getProduct())) {
                e.setAfterNewApplyTotalGpuNum(
                        isCurrentItem ? (currentItemDO.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                                : currentItemDO.getTotalGpuNumApplyAfter())
                                : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyAfter)
                                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                e.setBeforeNewApplyTotalGpuNum(
                        isCurrentItem ? (currentItemDO.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO
                                : currentItemDO.getTotalGpuNumApplyBefore())
                                : applyItemDOs.stream().map(PplItemDO::getTotalGpuNumApplyBefore)
                                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
                e.setDiffNewApplyTotalGpuNum(
                        e.getAfterNewApplyTotalGpuNum().subtract(e.getBeforeNewApplyTotalGpuNum()));
            }
        });

    }

    /**
     * <a href="https://tapd.woa.com/69994695/prong/stories/view/1069994695887006941">
     * pplOrder维度加两个字段：共识状态、供应方案</a> <br/>
     * 并设置ppl明细的共识信息
     */
    private void addConsensusSupplyPlan(List<PplListVo> result) {
        if (ListUtils.isEmpty(result)) {
            return;
        }
        List<String> pplIdList = new ArrayList<>();
        result.forEach(pplListVo -> {
            if (pplListVo != null && ListUtils.isNotEmpty(pplListVo.getPplItems())) {
                pplListVo.getPplItems().forEach(draftItemDTO -> {
                    if (draftItemDTO != null && draftItemDTO.getPplId() != null) {
                        pplIdList.add(draftItemDTO.getPplId());
                    }
                });
            }
        });
        PplIdListConsensusSupplyPlanReq supplyPlanReq = new PplIdListConsensusSupplyPlanReq();
        supplyPlanReq.setPplIdList(pplIdList);
        List<SupplyPlanDetailVO> supplyPlans = pplConsensusService.queryPplIdListSupplyPlan(supplyPlanReq);
        supplyPlans = supplyPlans == null ? new ArrayList<>() : supplyPlans;
        Map<String, List<SupplyPlanDetailVO>> supplyMap = ListUtils
                .toMapList(supplyPlans, SupplyPlanGroupField::getPplId, item -> item);
        for (PplListVo pplListVo : result) {
            if (ListUtils.isEmpty(pplListVo.getPplItems())) {
                continue;
            }
            List<String> consensusStatusList = new ArrayList<>();
            StringJoiner consensusSupplyPlan = new StringJoiner(";");
            for (DraftItemDTO pplItem : pplListVo.getPplItems()) {
                List<SupplyPlanDetailVO> planDetails = supplyMap.get(pplItem.getPplId());
                if (planDetails == null) {
                    planDetails = new ArrayList<>();
                }
                pplItem.setSupplyPlans(planDetails);
                StringJoiner matchType = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
                StringJoiner matchTypeName = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
                SupplyPlanDetailVO suggestSupply = null;
                for (SupplyPlanDetailVO planDetail : planDetails) {
                    if (planDetail == null) {
                        continue;
                    }
                    consensusStatusList.add(planDetail.getConsensusStatus());
                    String plan = planDetail.consensusSupplyPlan();
                    if (Strings.isNotBlank(plan)) {
                        consensusSupplyPlan.add(plan);
                    }
                    // 设置ppl明细的共识信息
                    consensusInfoSet(pplItem, planDetail);
                    PplStockSupplyMatchTypeEnum matchTypeEnum = PplStockSupplyMatchTypeEnum
                            .getByCode(planDetail.getMatchType());
                    if (PplStockSupplyMatchTypeEnum.SUGGEST.equals(matchTypeEnum)
                            || PplStockSupplyMatchTypeEnum.SUGGEST_BUY.equals(matchTypeEnum)) {
                        suggestSupply = planDetail;
                    }
                    if (matchTypeEnum != null) {
                        matchType.add(matchTypeEnum.getCode());
                        matchTypeName.add(matchTypeEnum.getName());
                    }
                }
                // 设置ppl明细的共识信息
                pplItem.setConsensusMatchType(matchType.toString());
                pplItem.setConsensusMatchTypeName(matchTypeName.toString());
                if (suggestSupply != null) {
                    consensusInfoSet(pplItem, suggestSupply);
                }
                // 没有供应方案时，设置ppl明细默认的共识信息
                defaultConsensusSet(pplItem);
            }
            if (ListUtils.isEmpty(consensusStatusList)) {
                pplListVo.setOrderConsensusStatus("未共识");
            } else {
                if (!consensusStatusList.contains(PplConsensusStatusEnum.WAIT_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.NOT_CONSENSUS.getCode())) {
                    pplListVo.setOrderConsensusStatus("已共识");
                } else if (!consensusStatusList.contains(PplConsensusStatusEnum.ACCEPT_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.REFUSE_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.NOT_NEED_CONSENSUS.getCode())) {
                    pplListVo.setOrderConsensusStatus("未共识");
                } else {
                    pplListVo.setOrderConsensusStatus("部分共识");
                }
            }
            pplListVo.setOrderConsensusSupplyPlan(consensusSupplyPlan.toString());
        }
    }

    private void consensusInfoSet(DraftItemDTO pplItem, SupplyPlanDetailVO planDetail) {
        if (pplItem == null || planDetail == null) {
            return;
        }
        pplItem.setConsensusStatus(planDetail.getConsensusStatus());
        pplItem.setConsensusStatusName(planDetail.getConsensusStatusName());
        pplItem.setConsensusZoneName(planDetail.getConsensusZoneName());
        pplItem.setConsensusInstanceModel(planDetail.getMatchInstanceType());
        pplItem.setConsensusInstanceType(planDetail.getConsensusInstanceType());
        if (planDetail.getConsensusDemandDate() != null) {
            pplItem.setConsensusDemandDate(LocalDateTimeUtil
                    .format(planDetail.getConsensusDemandDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        }
    }

    // 没有供应方案时，设置ppl明细默认的共识信息
    private void defaultConsensusSet(DraftItemDTO pplItem) {
        if (pplItem == null || ListUtils.isNotEmpty(pplItem.getSupplyPlans())) {
            return;
        }
        pplItem.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
        pplItem.setConsensusStatusName(PplConsensusStatusEnum.NOT_CONSENSUS.getName());
        pplItem.setConsensusZoneName(pplItem.getZoneName());
        pplItem.setConsensusInstanceModel(pplItem.getInstanceModel());
        pplItem.setConsensusInstanceType(pplItem.getInstanceType());
        pplItem.setConsensusDemandDate(pplItem.getBeginBuyDate());
    }

    // 没有供应方案时，设置ppl明细默认的共识信息
    private void defaultConsensusSet(PplOrderAuditRecordItemVO pplItem) {
        if (pplItem == null || ListUtils.isNotEmpty(pplItem.getSupplyPlans())) {
            return;
        }
        pplItem.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
        pplItem.setConsensusStatusName(PplConsensusStatusEnum.NOT_CONSENSUS.getName());
        pplItem.setConsensusZoneName(pplItem.getZoneName());
        pplItem.setConsensusInstanceModel(pplItem.getInstanceModel());
        pplItem.setConsensusInstanceType(pplItem.getInstanceType());
        pplItem.setConsensusDemandDate(pplItem.getBeginBuyDate() == null ? null
                : LocalDateTimeUtil.format(pplItem.getBeginBuyDate(), DateTimeFormatter.ISO_DATE));
    }

    @Override
    public void addConsensusSupplyPlanForPplOrderVo(List<PplOrderVo> result) {
        if (ListUtils.isEmpty(result)) {
            return;
        }
        List<String> pplIdList = new ArrayList<>();
        result.forEach(pplListVo -> {
            if (pplListVo != null && ListUtils.isNotEmpty(pplListVo.getItemVOList())) {
                pplListVo.getItemVOList().forEach(itemVO -> {
                    if (itemVO != null && itemVO.getPplId() != null) {
                        pplIdList.add(itemVO.getPplId());
                    }
                });
            }
        });
        PplIdListConsensusSupplyPlanReq supplyPlanReq = new PplIdListConsensusSupplyPlanReq();
        supplyPlanReq.setPplIdList(pplIdList);
        List<SupplyPlanDetailVO> supplyPlans = pplConsensusService.queryPplIdListSupplyPlan(supplyPlanReq);
        supplyPlans = supplyPlans == null ? new ArrayList<>() : supplyPlans;
        Map<String, List<SupplyPlanDetailVO>> supplyMap = ListUtils
                .toMapList(supplyPlans, SupplyPlanGroupField::getPplId, item -> item);
        for (PplOrderVo pplListVo : result) {
            if (ListUtils.isEmpty(pplListVo.getItemVOList())) {
                continue;
            }
            List<String> consensusStatusList = new ArrayList<>();
            StringJoiner consensusSupplyPlan = new StringJoiner(";");
            for (PplOrderAuditRecordItemVO pplItem : pplListVo.getItemVOList()) {
                List<SupplyPlanDetailVO> planDetails = supplyMap.get(pplItem.getPplId());
                if (planDetails == null) {
                    planDetails = new ArrayList<>();
                }
                pplItem.setSupplyPlans(planDetails);
                StringJoiner matchType = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
                StringJoiner matchTypeName = new StringJoiner(ConsensusConstant.CONSENSUS_DELIMITER);
                SupplyPlanDetailVO suggestSupply = null;
                for (SupplyPlanDetailVO planDetail : planDetails) {
                    if (planDetail == null) {
                        continue;
                    }
                    consensusStatusList.add(planDetail.getConsensusStatus());
                    String plan = planDetail.consensusSupplyPlan();
                    if (Strings.isNotBlank(plan)) {
                        consensusSupplyPlan.add(plan);
                    }
                    // 设置ppl明细的共识信息
                    pplItem.setConsensusStatus(planDetail.getConsensusStatus());
                    pplItem.setConsensusStatusName(planDetail.getConsensusStatusName());
                    pplItem.setConsensusZoneName(planDetail.getConsensusZoneName());
                    pplItem.setConsensusInstanceModel(planDetail.getMatchInstanceType());
                    PplStockSupplyMatchTypeEnum matchTypeEnum = PplStockSupplyMatchTypeEnum
                            .getByCode(planDetail.getMatchType());
                    if (PplStockSupplyMatchTypeEnum.SUGGEST.equals(matchTypeEnum)
                            || PplStockSupplyMatchTypeEnum.SUGGEST_BUY.equals(matchTypeEnum)) {
                        suggestSupply = planDetail;
                    }
                    if (matchTypeEnum != null) {
                        matchType.add(matchTypeEnum.getCode());
                        matchTypeName.add(matchTypeEnum.getName());
                    }
                }
                // 设置ppl明细的共识信息
                pplItem.setConsensusMatchType(matchType.toString());
                pplItem.setConsensusMatchTypeName(matchTypeName.toString());
                if (suggestSupply != null) {
                    pplItem.setConsensusStatus(suggestSupply.getConsensusStatus());
                    pplItem.setConsensusStatusName(suggestSupply.getConsensusStatusName());
                    pplItem.setConsensusZoneName(suggestSupply.getConsensusZoneName());
                    pplItem.setConsensusInstanceModel(suggestSupply.getMatchInstanceType());
                }
                // 没有供应方案时，设置ppl明细默认的共识信息
                defaultConsensusSet(pplItem);
            }
            if (ListUtils.isEmpty(consensusStatusList)) {
                pplListVo.setOrderConsensusStatus("未共识");
            } else {
                if (!consensusStatusList.contains(PplConsensusStatusEnum.WAIT_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.NOT_CONSENSUS.getCode())) {
                    pplListVo.setOrderConsensusStatus("已共识");
                } else if (!consensusStatusList.contains(PplConsensusStatusEnum.ACCEPT_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.REFUSE_CONSENSUS.getCode())
                        && !consensusStatusList.contains(PplConsensusStatusEnum.NOT_NEED_CONSENSUS.getCode())) {
                    pplListVo.setOrderConsensusStatus("未共识");
                } else {
                    pplListVo.setOrderConsensusStatus("部分共识");
                }
            }
            pplListVo.setOrderConsensusSupplyPlan(consensusSupplyPlan.toString());
        }
    }

    @Override
    public List<PplListVo> queryPreSubmitDraftData(QueryPplDraftReq req) {
        if (req.getVersionId() == null) {
            throw new BizException("未传版本Id");
        }
        PplInnerProcessVersionDO versionDO = innerVersionService.getByVersionId(req.getVersionId());
        if (CollectionUtils.isEmpty(req.getProduct())) {
            List<String> versionProduct = Arrays.asList(versionDO.getProduct().split(";"));
            req.setProduct(versionProduct);
        }
        String userName = Strings.isNotBlank(req.getUsername()) ? req.getUsername() : LoginUtils.getUserNameWithSystem();
        WhereContent draftSql = pplInnerProcessService.checkPermission(userName, req.getIndustryDept(), "",
                versionDO.getId());
        if (draftSql == null) {
            // 如果没有任何权限，则只看得到自己的，也就是架构师层面
            draftSql = new WhereContent();
            draftSql.andEqual(PplOrderDraftDO::getSubmitUser, userName);
        }
        draftSql.andEqual(PplOrderDraftDO::getDraftStatus, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        draftSql.andEqualIfValueNotEmpty(PplOrderDraftDO::getIndustryDept, req.getIndustryDept());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getWarZone, req.getWarZone());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerUin, req.getCustomerUin());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerShortName, req.getCustomerShortName());

        WhereContent itemSql = new WhereContent();
        itemSql.andEqual(PplItemDraftDO::getDraftStatus, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getProduct, req.getProduct());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getDatabaseName, req.getDatabaseName());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getDemandType, req.getDemandType());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getInstanceType, req.getInstanceType());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getRegionName, req.getRegionName());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getZoneName, req.getZoneName());
        itemSql.andInIfValueNotEmpty(PplItemDraftDO::getProjectName, req.getProjectName());
        itemSql.andEqualIfValueNotEmpty(PplItemDraftDO::getStatus, req.getItemStatus());
        if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
            itemSql.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
        }
        if (req.getQueryIsNeedDeal()){
            PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    "where process_id = ? and id > ? order by id asc limit 1 ",
                    versionDO.getProcessId(), versionDO.getId());
            if (nextVersion == null) {
                return new ArrayList<>();
            }
            itemSql.addAnd("demand_type != ?",PplDemandTypeEnum.RETURN.getCode());
            List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
            itemSql.addAnd(" (region_name not in (?) and begin_buy_date < ?) or "
                    + "(region_name in (?) and begin_buy_date < ?)",overSeaRegions,nextVersion.getInCountryDemandYearMonthFirstDay(),
                    overSeaRegions,nextVersion.getOverseasDemandYearMonthFirstDay());

            draftSql.addAnd("source != ?",DraftStatusEnum.DELETED.getCode());
        }
        List<PplItemDraftDO> all = demandDBHelper.getAll(PplItemDraftDO.class,
                itemSql.getSql(), itemSql.getParams());
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        List<String> pplOrder = all.stream().map(PplItemDraftDO::getPplOrder).distinct().collect(Collectors.toList());
        if (ListUtils.isNotEmpty(req.getPplOrder())) {
            pplOrder.retainAll(req.getPplOrder());
        }
        draftSql.andIn(PplOrderDraftDO::getPplOrder, pplOrder);
        List<PplOrderDraftDO> orders = demandDBHelper.getAll(PplOrderDraftDO.class, draftSql.getSql(),
                draftSql.getParams());
        if (ListUtils.isEmpty(orders)) {
            return new ArrayList<>();
        }

        List<String> filterPplOrder = orders.stream().map(PplOrderDraftDO::getPplOrder).distinct()
                .collect(Collectors.toList());
        Map<String, List<PplItemDO>> oldAuditMap = demandDBHelper.getAll(
                        PplItemDO.class, "where deleted = 0 and  ppl_order in (?) ",
                        ListUtils.transform(orders, PplOrderBaseDO::getPplOrder))
                .stream().collect(Collectors.groupingBy(PplItemDO::getPplOrder));

        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
        List<PplItemDraftDO> allItemDraftList = all.stream().filter(v ->
                filterPplOrder.contains(v.getPplOrder())).collect(Collectors.toList());
        Map<String, List<PplItemDraftDO>> orderToItemList = allItemDraftList.stream()
                .collect(Collectors.groupingBy(PplItemDraftDO::getPplOrder));

        List<PplListVo> result = orders.stream().map(d -> {
            PplListVo dto = new PplListVo();
            dto.setUpdateTime(d.getUpdateTime());
            dto.setChangeType(DraftStatusEnum.getNameByCode(d.getSource()));
            dto.setType(d.getSource());
            dto.setCustomerUin(d.getCustomerUin());
            dto.setCustomerShortName(d.getCustomerShortName());
            dto.setIndustryDept(d.getIndustryDept());
            dto.setProjectName(d.getProjectName());
            dto.setDemandType(PplDemandTypeEnum.getByCode(d.getDemandType()).getName());
            dto.setWarZone(d.getWarZone());
            dto.setPplOrder(d.getPplOrder());
            dto.setPplSource(PplOrderSourceEnum.getNameByPplOrder(dto.getPplOrder()));
            dto.setCustomerType(d.getCustomerType());
            dto.setModifyReason(d.getModifyReason());
            dto.setSubmitUser(d.getSubmitUser());

            List<PplItemDraftDO> draftItems = orderToItemList.get(d.getPplOrder());
            List<DraftItemDTO> items = draftItems.stream().map(item -> {
                DraftItemDTO row = new DraftItemDTO();
                row.setProduct(item.getProduct());
                row.setDemandType(item.getDemandType());
                row.setDemandScene(item.getDemandScene());
                row.setProjectName(item.getProjectName());
                row.setBillType(item.getBillType());
                row.setWinRate(item.getWinRate());
                row.setImportantDemand(item.getImportantDemand());

                row.setBeginBuyDate(item.getBeginBuyDate().toString());
                row.setEndBuyDate(item.getEndBuyDate().toString());

                row.setBeginElasticDate(DateUtils.format(item.getBeginElasticDate(), "HH:mm"));

                row.setEndElasticDate(DateUtils.format(item.getEndElasticDate(), "HH:mm"));

                row.setNote(item.getNote());
                row.setRegionName(item.getRegionName());
                row.setIsStrongDesignateZone(item.getIsStrongDesignateZone());
                row.setZoneName(item.getZoneName());
                row.setInstanceType(item.getInstanceType());
                row.setInstanceModel(item.getInstanceModel());
                row.setInstanceNum(item.getInstanceNum());
                if (Strings.isNotBlank(item.getAlternativeInstanceType())) {
                    row.setAlternativeInstanceType(
                            Splitter.on(";").trimResults().splitToList(item.getAlternativeInstanceType()));
                }
                if (Strings.isNotBlank(item.getAlternativeZoneName())) {
                    row.setAlternativeZoneName(
                            Splitter.on(";").trimResults().splitToList(item.getAlternativeZoneName()));
                }
                row.setAffinityType(item.getAffinityType());
                row.setAffinityValue(item.getAffinityValue());
                row.setSystemDiskType(item.getSystemDiskType());
                row.setSystemDiskStorage(item.getSystemDiskStorage());
                if (item.getSystemDiskNum() == null) {
                    row.setSystemDiskNum(1);
                }
                row.setSystemDiskNum(item.getSystemDiskNum());
                row.setDataDiskType(item.getDataDiskType());
                row.setDataDiskStorage(item.getDataDiskStorage());
                row.setDataDiskNum(item.getDataDiskNum());
                row.setCbsIo(item.getCbsIo());
                row.setTotalCoreNum(item.getTotalCore());
                row.setTotalDiskNum(item.getTotalDisk());
                row.setType(item.getType());
                row.setPplId(item.getPplId());
                row.setParentPplId(item.getParentPplId());

                row.setStatus(item.getStatus());
                row.setYunxiaoOrderId(item.getYunxiaoOrderId());
                row.setYunxiaoDetailId(item.getYunxiaoDetailId());
                row.setOrderNumberId(item.getOrderNumberId());
                row.setYunxiaoOrderStatus(item.getYunxiaoOrderStatus());
                row.setBeforeNewApplyTotalInstanceNum(item.getInstanceNumApplyBefore());
                row.setAfterNewApplyTotalInstanceNum(item.getInstanceNumApplyAfter());
                row.setBeforeNewApplyTotalCore(item.getTotalCoreApplyBefore());
                row.setAfterNewApplyTotalCore(item.getTotalCoreApplyAfter());
                row.setBeforeNewApplyTotalGpuNum(item.getTotalGpuNumApplyBefore());
                row.setAfterNewApplyTotalGpuNum(item.getTotalGpuNumApplyAfter());

                row.setProduct(item.getProduct());
                // gpu相关字段
                row.setGpuProductType(item.getGpuProductType());
                row.setGpuType(item.getGpuType());
                row.setGpuNum(item.getGpuNum());
                if (Strings.isNotBlank(item.getAcceptGpu())) {
                    row.setAcceptGpu(Splitter.on(";").trimResults().splitToList(item.getAcceptGpu()));

                }
                row.setIsAcceptAdjust(item.getIsAcceptAdjust());
                row.setTotalGpuNum(item.getTotalGpuNum());
                row.setBizScene(item.getBizScene());
                row.setBizDetail(item.getBizDetail());
                row.setServiceTime(item.getServiceTime());
                row.setSaleDurationYear(item.getSaleDurationYear());
                row.setBusinessCpq(item.getBusinessCpq());
                row.setApplyDiscount(item.getApplyDiscount());
                row.setBizId(item.getBizId());
                if (overSeaRegions.contains(row.getRegionName())) {
                    row.setCustomhouseTitle("境外");
                } else {
                    row.setCustomhouseTitle("国内");
                }
                row.setPlacementGroupList(item.placementGroupListGet());

                row.setDatabaseName(item.getDatabaseName());
                row.setMoreThanOneAZ(item.getMoreThanOneAZ());
                row.setDatabaseStorageType(item.getDatabaseStorageType());
                row.setDeployType(item.getDeployType());
                row.setFrameworkType(item.getFrameworkType());
                row.setSliceNum(item.getSliceNum());
                row.setReplicaNum(item.getReplicaNum());
                row.setReadOnlyNum(item.getReadOnlyNum());
                row.setDatabaseSpecs(item.getDatabaseSpecs());
                row.setDatabaseStorage(item.getDatabaseStorage());
                row.setTotalDatabaseStorage(item.getTotalDatabaseStorage());

                row.setCosStorageType(item.getCosStorageType());
                row.setCosAZ(item.getCosAZ());
                row.setCosStorage(item.getCosStorage());
                row.setTotalCosStorage(item.getTotalCosStorage());
                row.setBandwidth(item.getBandwidth());
                row.setQps(item.getQps());

                row.setInstanceModelCoreNum(item.getInstanceModelCoreNum());
                row.setInstanceModelRamNum(item.getInstanceModelRamNum());
                row.setTotalMemory(item.getTotalMemory());

                return row;
            }).collect(Collectors.toList());
            dto.setPplItems(items);
            if (!CollectionUtils.isEmpty(items)) {
                dto.setProduct(items.get(0).getProduct());
                dto.setWinRate(items.get(0).getWinRate());
                dto.setBeginBuyDate(items.get(0).getBeginBuyDate());
                dto.setEndBuyDate(items.get(0).getEndBuyDate());
            }
            // 计算一些pplOrder级别的总指标数据
            dto.calcAndSetOrderLevelIndex(oldAuditMap.get(d.getPplOrder()), true);
            return dto;
        }).collect(Collectors.toList());
        // pplOrder维度加两个字段：共识状态、供应方案,并设置ppl明细的共识信息
        addConsensusSupplyPlan(result);

        // 设置ppl_item的预约状态
//        completeItemApplyInfo(result);

        // 处理数据
        List<String> orderNumberList = result.stream().flatMap(v -> v.getPplItems().stream())
                .filter(v -> v.getYunxiaoOrderId() != null &&
                        (v.getYunxiaoOrderId().startsWith("OE") || v.getYunxiaoOrderId().startsWith("ON")))
                .map(PplOrderItemDTO::getYunxiaoOrderId).distinct().collect(Collectors.toList());

        List<OrderInfoDO> orderList = demandDBHelper.getAll(OrderInfoDO.class,
                "where available_status = ? and order_number in (?)",
                OrderAvailableStatusEnum.AVAILABLE.getCode(), orderNumberList);
        Map<String, String> orderMap = orderList.stream()
                .collect(Collectors.toMap(OrderInfoDO::getOrderNumber, OrderInfoDO::getOrderNodeCode));

        // 获取 order 级别的总预约核心数等
        for (PplListVo pplListVo : result) {
            List<DraftItemDTO> items = pplListVo.getPplItems();
            pplListVo.setApplyTotalCore(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalCore).intValue());
            pplListVo.setApplyInstanceNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalInstanceNum).intValue());
            pplListVo.setApplyTotalGpuNum(
                    NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalGpuNum).intValue());

            pplListVo.setNotApplyInstanceNum(pplListVo.getTotalInstanceNum() - pplListVo.getApplyInstanceNum());
            pplListVo.setNotApplyTotalCore(pplListVo.getTotalCore() - pplListVo.getApplyTotalCore());
            pplListVo.setNotApplyTotalGpuNum(pplListVo.getTotalGpu() - pplListVo.getApplyTotalGpuNum());

            orderAppliedStatusDeal(pplListVo, orderMap);

        }

        return result;
    }

    public void orderAppliedStatusDeal(PplListVo pplListVo, Map<String, String> orderMap) {
        List<DraftItemDTO> items = pplListVo.getPplItems();
        List<DraftItemDTO> appliedList = items.stream()
                .filter(v -> PplItemStatusEnum.APPLIED.getCode().equals(v.getStatus())).collect(
                        Collectors.toList());

        if (ListUtils.isNotEmpty(appliedList)) {
            String yunxiaoOrderId = appliedList.get(0).getYunxiaoOrderId();
            pplListVo.setAppliedStatus(PplItemStatusEnum.APPLIED.getCode());
            pplListVo.setYunxiaoOrderId(yunxiaoOrderId);
            if (yunxiaoOrderId.startsWith("OE") || yunxiaoOrderId.startsWith("ON")) {
                pplListVo.setYunxiaoOrderId(yunxiaoOrderId);
                String orderNodeCode = orderMap.get(yunxiaoOrderId);
                pplListVo.setCurrentOrderStatus(orderNodeCode == null ?
                        "" : OrderNodeCodeEnum.getNameByCode(orderNodeCode));
            } else {
                pplListVo.setCurrentOrderStatus("");
            }

        } else {
            pplListVo.setAppliedStatus(PplItemStatusEnum.VALID.getCode());
        }
    }

    @Override
    public List<PplListVo> queryValidData(QueryPplDraftReq req) {

        // 头部和中长尾靠source来区分
        List<String> source = new ArrayList<>();
        if (ListUtils.isEmpty(req.getCustomerScope())) {
            source.add(PplOrderSourceTypeEnum.IMPORT.getCode());
            source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode());
            source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode());
        } else {
            // 目前ppl没有区分是头部里的名单还是报备，所以这里也不区分，只要传了名单或报备，就都查
            if (req.getCustomerScope().contains("名单") || req.getCustomerScope().contains("报备")) {
                source.add(PplOrderSourceTypeEnum.IMPORT.getCode());
                source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode());
            }
            if (req.getCustomerScope().contains("中长尾")) {
                source.add(PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode());
            }
        }

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("t2.source in (?)", source);

        Boolean isQueryUin = ListUtils.isEmpty(req.getCustomerUin()) ? Boolean.FALSE : Boolean.TRUE;

        wrapAuth(req);

        if (ListUtils.isNotEmpty(req.getWarZone())) {
            whereSQL.and("t2.war_zone in (?)", req.getWarZone());
        }

        if (ListUtils.isNotEmpty(req.getCustomerUin())) {
            if (isQueryUin) {
                whereSQL.and("t2.customer_uin in (?)", req.getCustomerUin());
            } else {
                whereSQL.and("(t2.customer_uin in (?) ) or t2.submit_user = ?", req.getCustomerUin(),
                        LoginUtils.getUserName());
            }
        }

        whereSQL.and("t2.customer_short_name not like '%云运管%'");

        if (ListUtils.isNotEmpty(req.getCustomerShortName())) {
            whereSQL.and("t2.customer_short_name in (?)", req.getCustomerShortName());
        }
        if (StringUtils.isNotBlank(req.getIndustryDept())) {
            whereSQL.and("t2.industry_dept = ?", req.getIndustryDept());
        }
        if (ListUtils.isNotEmpty(req.getPplOrder())) {
            whereSQL.and("t2.ppl_order in (?)", req.getPplOrder());
        }


        // 处理item查询参数
        if (StringUtils.isNotBlank(req.getItemStatus())){
            whereSQL.and("t1.status = ?", req.getItemStatus());
        }
        if (ListUtils.isNotEmpty(req.getRegionName())) {
            whereSQL.and("t1.region_name in (?)", req.getRegionName());
        }
        if (ListUtils.isNotEmpty(req.getZoneName())) {
            whereSQL.and("t1.zone_name in (?) ", req.getZoneName());
        }
        if (ListUtils.isNotEmpty(req.getInstanceType())) {
            whereSQL.and("t1.instance_type in (?)", req.getInstanceType());
        }
        if (ListUtils.isNotEmpty(req.getProjectName())) {
            whereSQL.and("t1.project_name in (?)", req.getProjectName());
        }
        if (ListUtils.isNotEmpty(req.getProduct())) {
            whereSQL.and("t1.product in (?)", req.getProduct());
        }
        if (ListUtils.isNotEmpty(req.getDatabaseName())) {
            whereSQL.and("t1.database_name in (?)", req.getDatabaseName());
        }
        if (ListUtils.isNotEmpty(req.getDemandType())) {
            whereSQL.and("t1.demand_type in (?)", req.getDemandType());
        }

        whereSQL.and("t1.instance_num > 0");

        if ("国内".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            whereSQL.and("t1.region_name not in (?)",overSeaRegion);
        } else if ("境外".equals(req.getRegionType())) {
            List<String> overSeaRegion = pplDictService.queryAllRegionName(true);
            whereSQL.and("t1.region_name in (?)",overSeaRegion);
        }
        if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
            whereSQL.and("t1.begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
        }

        // 已预约的已取消的云霄单不要
        whereSQL.and(
                "t1.status!='APPLIED' or (t1.status='APPLIED' and t1.yunxiao_order_status not in ('CREATED', 'CANCELED', 'BAD_CANCELED', 'REJECTED'))");


        List<PplItemJoinOrderVO> list = demandDBHelper.getAll(PplItemJoinOrderVO.class,
                whereSQL.getSQL(), whereSQL.getParams());

        //按单据聚合
        List<RegionDTO> regionDTOS = industryDemandDictService.listAllRegion();
        final Map<String, String> regionToCustomhouseTitle =
                ListUtils.toMap(regionDTOS, o -> o.getRegionShortChName(), o -> o.getLand());
        List<PplListVo> lls = list.stream().collect(Collectors.groupingBy(s -> s.getPplOrderDO().getPplOrder()))
                .entrySet().stream().map(entry -> {
                    PplListVo pto = new PplListVo();
                    PplItemJoinOrderVO pjo = entry.getValue().get(0);
                    pto.setChangeType(DraftStatusEnum.UPDATE.getName());
                    pto.setType(DraftStatusEnum.UPDATE.getCode());
                    pto.setSource(pjo.getPplOrderDO().getSource());
                    pto.setCustomerUin(pjo.getCustomerUin());
                    pto.setCustomerShortName(pjo.getPplOrderDO().getCustomerShortName());
                    pto.setCustomerType(pjo.getPplOrderDO().getCustomerType());
                    pto.setCustomerTypeName(CustomerTypeEnum.getNameByCode(pjo.getPplOrderDO().getCustomerType()));
                    pto.setIndustryDept(pjo.getPplOrderDO().getIndustryDept());
                    pto.setSubmitUser(pjo.getPplOrderDO().getSubmitUser());
                    pto.setProjectName(pjo.getItemDO().getProjectName());
                    pto.setDemandType(PplDemandTypeEnum.getByCode(pjo.getDemandType()).getName());
                    pto.setBeginBuyDate(pjo.getItemDO().getBeginBuyDate().toString());
                    pto.setEndBuyDate(pjo.getItemDO().getEndBuyDate().toString());
                    pto.setWarZone(pjo.getPplOrderDO().getWarZone());
                    pto.setPplOrder(entry.getKey());
                    pto.setPplSource(PplOrderSourceEnum.getNameByPplOrder(pto.getPplOrder()));
                    // pto.setTotalCore(pjo.getPplOrderDO().getAllCore()); 不要用pplOrder的allCore

                    val items = entry.getValue().stream().map(pitem -> {

                        DraftItemDTO row = new DraftItemDTO();

                        PplItemDO item = pitem.getItemDO();

                        row.setPplId(item.getPplId());
                        row.setParentPplId(item.getParentPplId());

                        // 设置ppl_item的预约状态
                        row.setStatus(item.getStatus());

                        row.setYunxiaoOrderId(item.getYunxiaoOrderId());
                        row.setYunxiaoDetailId(item.getYunxiaoDetailId());
                        row.setYunxiaoOrderStatus(item.getYunxiaoOrderStatus());
                        row.setProduct(item.getProduct());
                        row.setDemandType(item.getDemandType());
                        row.setDemandScene(item.getDemandScene());
                        row.setProjectName(item.getProjectName());
                        row.setBillType(item.getBillType());
                        row.setWinRate(item.getWinRate());
                        row.setImportantDemand(item.getImportantDemand());

                        row.setBeginBuyDate(item.getBeginBuyDate().toString());
                        row.setEndBuyDate(item.getEndBuyDate().toString());

                        row.setBeginElasticDate(DateUtils.format(item.getBeginElasticDate(), "HH:mm"));
                        row.setEndElasticDate(DateUtils.format(item.getEndElasticDate(), "HH:mm"));
                        row.setNote(item.getNote());
                        row.setRegionName(item.getRegionName());
                        row.setCustomhouseTitle(regionToCustomhouseTitle.getOrDefault(item.getRegionName(), "未知"));
                        row.setIsStrongDesignateZone(item.getIsStrongDesignateZone());
                        row.setZoneName(item.getZoneName());
                        row.setInstanceType(item.getInstanceType());
                        row.setInstanceModel(item.getInstanceModel());
                        row.setInstanceNum(item.getInstanceNum());
                        if (Strings.isNotBlank(item.getAlternativeInstanceType())) {
                            row.setAlternativeInstanceType(
                                    Splitter.on(";").trimResults().splitToList(item.getAlternativeInstanceType()));
                        }
                        if (Strings.isNotBlank(item.getAlternativeZoneName())) {
                            row.setAlternativeZoneName(
                                    Splitter.on(";").trimResults().splitToList(item.getAlternativeZoneName()));
                        }
                        row.setAffinityType(item.getAffinityType());
                        row.setAffinityValue(item.getAffinityValue());
                        row.setSystemDiskType(item.getSystemDiskType());
                        row.setSystemDiskStorage(item.getSystemDiskStorage());
                        if (item.getSystemDiskNum() == null) {
                            row.setSystemDiskNum(1);
                        }
                        row.setSystemDiskNum(item.getSystemDiskNum());
                        row.setDataDiskType(item.getDataDiskType());
                        row.setDataDiskStorage(item.getDataDiskStorage());
                        row.setDataDiskNum(item.getDataDiskNum());
                        row.setCbsIo(item.getCbsIo());
                        row.setTotalCoreNum(item.getTotalCore());
                        row.setTotalDiskNum(item.getTotalDisk());
                        row.setType(OperateTypeEnum.UPDATE.getCode());
                        row.setWinRate(item.getWinRate());
                        row.setImportantDemand(item.getImportantDemand());
                        row.setBizId(item.getBizId());

                        row.setProduct(item.getProduct());

                        // gpu相关字段
                        row.setGpuProductType(item.getGpuProductType());
                        row.setGpuType(item.getGpuType());
                        row.setGpuNum(item.getGpuNum());
                        if (Strings.isNotBlank(item.getAcceptGpu())) {
                            row.setAcceptGpu(Splitter.on(";").trimResults().splitToList(item.getAcceptGpu()));

                        }
                        row.setIsAcceptAdjust(item.getIsAcceptAdjust());
                        row.setTotalGpuNum(item.getTotalGpuNum());
                        row.setBizScene(item.getBizScene());
                        row.setBizDetail(item.getBizDetail());
                        row.setServiceTime(item.getServiceTime());
                        row.setSaleDurationYear(item.getSaleDurationYear());
                        row.setBusinessCpq(item.getBusinessCpq());
                        row.setApplyDiscount(item.getApplyDiscount());

                        row.setIsUnexpectedPpl(PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(pto.getSource())
                                || PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode().equals(pto.getSource()));
                        if (item.getBeginBuyDate() != null) {
                            row.setIsExpiredPpl(item.getBeginBuyDate().isBefore(getBeginOfThisMonth()));
                        }

                        row.setAfterNewApplyTotalInstanceNum(
                                item.getInstanceNumApplyAfter() == null ? 0 : item.getInstanceNumApplyAfter());
                        row.setBeforeNewApplyTotalInstanceNum(
                                item.getInstanceNumApplyBefore() == null ? 0 : item.getInstanceNumApplyBefore());
                        row.setDiffNewApplyTotalInstanceNum(
                                row.getAfterNewApplyTotalInstanceNum() - row.getBeforeNewApplyTotalInstanceNum());

                        row.setAfterNewApplyTotalCore(
                                item.getTotalCoreApplyAfter() == null ? 0 : item.getTotalCoreApplyAfter());
                        row.setBeforeNewApplyTotalCore(
                                item.getTotalCoreApplyBefore() == null ? 0 : item.getTotalCoreApplyBefore());
                        row.setDiffNewApplyTotalCore(
                                row.getAfterNewApplyTotalCore() - row.getBeforeNewApplyTotalCore());

                        row.setAfterNewApplyTotalGpuNum(item.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                                : item.getTotalGpuNumApplyAfter());
                        row.setBeforeNewApplyTotalGpuNum(item.getTotalGpuNumApplyBefore() == null ? BigDecimal.ZERO
                                : item.getTotalGpuNumApplyBefore());
                        row.setDiffNewApplyTotalGpuNum(
                                row.getAfterNewApplyTotalGpuNum().subtract(row.getBeforeNewApplyTotalGpuNum()));
                        row.setPlacementGroupList(item.placementGroupListGet());

                        row.setDatabaseName(item.getDatabaseName());
                        row.setMoreThanOneAZ(item.getMoreThanOneAZ());
                        row.setDatabaseStorageType(item.getDatabaseStorageType());
                        row.setDeployType(item.getDeployType());
                        row.setFrameworkType(item.getFrameworkType());
                        row.setSliceNum(item.getSliceNum());
                        row.setReplicaNum(item.getReplicaNum());
                        row.setReadOnlyNum(item.getReadOnlyNum());
                        row.setDatabaseSpecs(item.getDatabaseSpecs());
                        row.setDatabaseStorage(item.getDatabaseStorage());
                        row.setTotalDatabaseStorage(item.getTotalDatabaseStorage());

                        row.setCosStorageType(item.getCosStorageType());
                        row.setCosAZ(item.getCosAZ());
                        row.setCosStorage(item.getCosStorage());
                        row.setTotalCosStorage(item.getTotalCosStorage());
                        row.setBandwidth(item.getBandwidth());
                        row.setQps(item.getQps());

                        row.setInstanceModelCoreNum(item.getInstanceModelCoreNum());
                        row.setInstanceModelRamNum(item.getInstanceModelRamNum());
                        row.setTotalMemory(item.getTotalMemory());

                        return row;
                    }).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(items)) {
                        pto.setProduct(items.get(0).getProduct());
                        pto.setWinRate(items.get(0).getWinRate());
                    }
                    Integer totalCore = 0;
                    Integer totalGpu = 0;
                    Integer totalInstanceNum = 0;
                    for (DraftItemDTO item : items) {
                        totalCore = totalCore + item.getTotalCoreNum();
                        totalGpu = totalGpu + (ObjectUtils.isEmpty(item.getTotalGpuNum()) ? 0
                                : item.getTotalGpuNum().intValue());
                        totalInstanceNum = totalInstanceNum + item.getInstanceNum();
                    }
                    pto.setTotalCore(totalCore);
                    pto.setTotalGpu(totalGpu);
                    pto.setTotalInstanceNum(totalInstanceNum);

                    pto.setPplItems(items);
                    pto.calcAndSetOrderLevelIndex(null, false);

                    pto.setTotalCore(NumberUtils.sum(items, PplOrderItemDTO::getTotalCoreNum).intValue());
                    pto.setApplyTotalCore(
                            NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalCore).intValue());
                    pto.setApplyInstanceNum(
                            NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalInstanceNum).intValue());
                    pto.setApplyTotalGpuNum(
                            NumberUtils.sum(items, PplOrderItemDTO::getAfterNewApplyTotalGpuNum).intValue());

                    pto.setNotApplyInstanceNum(pto.getTotalInstanceNum() - pto.getApplyInstanceNum());
                    pto.setNotApplyTotalCore(pto.getTotalCore() - pto.getApplyTotalCore());
                    pto.setNotApplyTotalGpuNum(pto.getTotalGpu() - pto.getApplyTotalGpuNum());

                    return pto;
                }).collect(Collectors.toList());
        // pplOrder维度加两个字段：共识状态、供应方案,并设置ppl明细的共识信息
        addConsensusSupplyPlan(lls);

        // 处理数据
        List<String> orderNumberList = lls.stream().flatMap(v -> v.getPplItems().stream())
                .filter(v -> v.getYunxiaoOrderId() != null && (
                        v.getYunxiaoOrderId().startsWith("OE") || v.getYunxiaoOrderId().startsWith("ON")))
                .map(PplOrderItemDTO::getYunxiaoOrderId).distinct().collect(Collectors.toList());
        List<OrderInfoDO> orderList = demandDBHelper.getAll(OrderInfoDO.class,
                "where available_status = ? and order_number in (?)",
                OrderAvailableStatusEnum.AVAILABLE.getCode(), orderNumberList);
        Map<String, String> orderMap = orderList.stream()
                .collect(Collectors.toMap(OrderInfoDO::getOrderNumber, OrderInfoDO::getOrderNodeCode));

        for (PplListVo ll : lls) {
            orderAppliedStatusDeal(ll, orderMap);
        }

        return lls;
    }

    private LocalDate getBeginOfThisMonth() {
        LocalDate now = LocalDate.now();
        return LocalDate.of(now.getYear(), now.getMonth(), 1);
    }

    /**
     * 全局数据权限 》私人数据， 先查全局数据
     *
     * @param req
     * @return
     */
    @Override
    public List<PplOverviewDo> queryOverview(QueryPplDraftReq req) {

        String userName = LoginUtils.getUserName();
        log.info("获取待提交数据");
        WhereContent draftSql = new WhereContent();
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerUin, req.getCustomerUin());
        draftSql.andInIfValueNotEmpty(PplOrderDraftDO::getCustomerShortName, req.getCustomerShortName());
        draftSql.andEqualIfValueNotEmpty(PplOrderDraftDO::getIndustryDept, req.getIndustryDept());
        draftSql.andEqual(PplOrderDraftDO::getSubmitUser, LoginUtils.getUserName());
        val draftOrders = demandDBHelper.getAll(PplOrderDraftDO.class, draftSql.getSql(), draftSql.getParams()).stream()
                .map(PplOrderDraftDO::getPplOrder).collect(Collectors.toList());

        WhereContent draftItemSql = new WhereContent();
        draftItemSql.addAnd("deleted = 0");
        if (draftOrders.isEmpty()) {
            draftItemSql.addAnd("1 = 2");
        } else {
            draftItemSql.andIn(PplItemDraftDO::getPplOrder, draftOrders);
            draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getProduct, req.getProduct());
            draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getRegionName, req.getRegionName());
            draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getZoneName, req.getZoneName());
            draftItemSql.andInIfValueNotEmpty(PplItemDraftDO::getInstanceType, req.getInstanceType());
            if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
                draftItemSql.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
            }
        }

        log.info("获取已生效数据");
        wrapAuth(req);
        //todo 权限补充-基本是客户基本权限
        WhereContent orderSql = new WhereContent();
        orderSql.andEqual(PplOrderDO::getNodeCode, "");
        orderSql.andEqualIfValueNotEmpty(PplOrderDO::getIndustryDept, req.getIndustryDept());
        if (!CollectionUtils.isEmpty(req.getCustomerUin())) {
            orderSql.addAnd("(customer_uin in (?) ) or submit_user = ?", req.getCustomerUin(), userName);
        }
        orderSql.andInIfValueNotEmpty(PplOrderDO::getCustomerShortName, req.getCustomerShortName());
        val orders = demandDBHelper.getAll(PplOrderDO.class, orderSql.getSql(), orderSql.getParams()).stream()
                .map(PplOrderDO::getPplOrder).collect(Collectors.toList());
        WhereContent orderItemSql = new WhereContent();
        orderItemSql.addAnd("deleted = 0");
        if (orders.isEmpty()) {
            orderItemSql.addAnd("1 = 2 ");//短路处理
        } else {
            orderItemSql.andIn("ppl_order", orders);
            orderItemSql.andInIfValueNotEmpty(PplItemDO::getRegionName, req.getRegionName());
            orderItemSql.andInIfValueNotEmpty(PplItemDO::getZoneName, req.getZoneName());
            orderItemSql.andInIfValueNotEmpty(PplItemDO::getInstanceType, req.getInstanceType());
            orderItemSql.andInIfValueNotEmpty(PplItemDraftDO::getProduct, req.getProduct());
            if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
                orderItemSql.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
            }
        }

        log.info("获取流程中数据");
        WhereContent auditSql = new WhereContent();
        auditSql.andEqualIfValueNotEmpty(PplOrderDO::getIndustryDept, req.getIndustryDept());
        auditSql.addAnd("node_code is not null");
        auditSql.andNotEqual(PplOrderDO::getNodeCode, "");
        auditSql.andEqual(PplOrderDO::getSubmitUser, LoginUtils.getUserName());
        auditSql.andInIfValueNotEmpty(PplOrderDO::getCustomerUin, req.getCustomerUin());
        auditSql.andInIfValueNotEmpty(PplOrderDO::getCustomerShortName, req.getCustomerShortName());

        val auditOrders = demandDBHelper.getAll(PplOrderDO.class, auditSql.getSql(), auditSql.getParams()).stream()
                .map(PplOrderDO::getPplOrder).collect(Collectors.toList());
        WhereContent auditIemSql = new WhereContent();
        auditIemSql.addAnd("deleted = 0");
        if (auditOrders.isEmpty()) {
            auditIemSql.addAnd("1=2");//短路处理
        } else {
            List<String> ids = demandDBHelper.getRaw(String.class,
                    "select MAX(id) as id  from  ppl_order_audit_record where ppl_order in (?) group by ppl_order ",
                    auditOrders);
            auditIemSql.andIn("audit_record_id", ids);
            auditIemSql.andInIfValueNotEmpty(PplItemDO::getRegionName, req.getRegionName());
            auditIemSql.andInIfValueNotEmpty(PplItemDO::getZoneName, req.getZoneName());
            auditIemSql.andInIfValueNotEmpty(PplItemDO::getInstanceType, req.getInstanceType());
            auditIemSql.andInIfValueNotEmpty(PplItemDO::getProduct, req.getProduct());
            if (Strings.isNotBlank(req.getStartYearMonth()) && Strings.isNotBlank(req.getEndYearMonth())) {
                auditIemSql.addAnd("begin_buy_date  BETWEEN  ? and ?", req.getStartYearMonth(), req.getEndYearMonth());
            }
        }

        String sql = ORMUtils.getSql("/sql/ppl13week/overview.sql");
        sql = sql.replace("${draft_sql}", draftItemSql.getSql());
        sql = sql.replace("${order_sql}", orderItemSql.getSql());
        sql = sql.replace("${audit_sql}", auditIemSql.getSql());

        Object[] params = new Object[draftItemSql.getParams().length + orderItemSql.getParams().length
                + auditIemSql.getParams().length];
        System.arraycopy(draftItemSql.getParams(), 0, params, 0, draftItemSql.getParams().length);
        System.arraycopy(orderItemSql.getParams(), 0, params, draftItemSql.getParams().length,
                orderItemSql.getParams().length);
        System.arraycopy(auditIemSql.getParams(), 0, params,
                draftItemSql.getParams().length + orderItemSql.getParams().length, auditIemSql.getParams().length);

        List<PplOverviewDo> ls = demandDBHelper.getRaw(PplOverviewDo.class, sql, params);
        return ls;
    }

    /**
     * 撤回单据
     */
    @Override
    public void withdrawPplOrder(String industryDept, String product, String pplOrder) {
        PplOrderDO pplOrderDO = demandDBHelper.getOne(PplOrderDO.class, "where ppl_order=?", pplOrder);
        if (pplOrderDO == null) {
            throw new WrongWebParameterException("单据不存在:" + pplOrder);
        }
        if (!LoginUtils.getUserName().equals(pplOrderDO.getSubmitUser())) {
            throw new WrongWebParameterException("无权限:" + pplOrder);
        }

        PplInnerProcessVersionDO currentVersion = innerVersionService.queryProcessingVersionByDeptAndProduct(
                industryDept, product);

        PplInnerProcessVersionSlaDTO node = innerVersionService.getFirstNode(currentVersion.getId());

        if (node.getNodeCode().equals(pplOrderDO.getNodeCode()) && PplOrderAuditStatusEnum.WAIT.getCode()
                .equals(pplOrderDO.getAuditStatus())) {
            List<PplOrderAuditRecordDO> pplOrderAllRecord = demandDBHelper.getAll(PplOrderAuditRecordDO.class,
                    "where ppl_order = ? and version_id = ?", pplOrder,
                    currentVersion.getId());
            List<Long> recordIdList = ListUtils.transform(pplOrderAllRecord, PplOrderAuditRecordDO::getId);
            //状态判断
            demandDBHelper.delete(PplOrderAuditRecordDO.class, "where ppl_order = ? and version_id = ?", pplOrder,
                    currentVersion.getId());
            demandDBHelper.delete(PplOrderAuditRecordItemDO.class, "where audit_record_id in (?)", recordIdList);
            demandDBHelper.executeRaw("update ppl_order_draft set deleted=0 where ppl_order =? ", pplOrder);
            demandDBHelper.executeRaw("update ppl_item_draft set deleted=0 where ppl_order =? ", pplOrder);
            val draftDO = demandDBHelper.getOne(PplOrderDraftDO.class, "where ppl_order=?", pplOrder);
            if (OperateTypeEnum.INSERT.getCode().equals(draftDO.getSource())) {
                log.info("{} 为本次新增，撤回 ppl Order", pplOrder);
                demandDBHelper.delete(PplOrderDO.class, "where ppl_order = ?", pplOrder);
            } else {
                log.info("{} 为本次调整，回滚 ppl Order状态", pplOrder);
                pplOrderDO.setNodeCode("");
                pplOrderDO.setCurrentProcessor("");
                pplOrderDO.setAuditStatus("");
                demandDBHelper.update(pplOrderDO);
            }

        } else {
            throw new WrongWebParameterException("单据当前状态无法撤回:" + pplOrder);
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public Long withdrawPreSubmitPplOrder(String pplOrder, String refuseNote) {

        PplItemDO itemAppliedDO = demandDBHelper.getOne(PplItemDO.class, "where ppl_order = ? and status = ? ",
                pplOrder, PplItemStatusEnum.APPLIED.getCode());
        if (itemAppliedDO != null) {
            throw new BizException("此ppl单(" + pplOrder + ")的明细(" + itemAppliedDO.getPplId()
                    + ")中含有已预约数据，不允许撤回/打回！");
        }

        PplOrderDraftDO preSubmitDO = demandDBHelper.getOne(PplOrderDraftDO.class,
                "where ppl_order = ? and draft_status = ?", pplOrder, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        // 如果是新增的需求 则直接从需求沟通 转移至 草稿箱
        if (preSubmitDO.getSource().equals(OperateTypeEnum.INSERT.getCode())) {
            demandDBHelper.executeRaw("update ppl_order_draft set draft_status = ?, modify_reason = ? "
                            + "where ppl_order in (?) and draft_status = ? and deleted = 0 ",
                    PplOrderDraftStatusEnum.DRAFT.getCode(), refuseNote, pplOrder,
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            demandDBHelper.executeRaw("update ppl_item_draft set draft_status = ? "
                            + "where ppl_order in (?) and draft_status = ?  and deleted = 0 ",
                    PplOrderDraftStatusEnum.DRAFT.getCode()
                    , pplOrder, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        } else {
            // 如果是其他需求(已生效需求)，则复制一条到草稿箱， 并将当前这条标记为删除
            PplOrderDraftDO newDraft = new PplOrderDraftDO();
            BeanUtils.copyProperties(preSubmitDO, newDraft);
            PplOrderDraftDO draftDO = demandDBHelper.getOne(PplOrderDraftDO.class,
                    "where ppl_order = ? and draft_status = ? ", pplOrder, PplOrderDraftStatusEnum.DRAFT.getCode());
            if (draftDO != null) {
                // 如果原来草稿箱存在相同的 则直接删除
                List<PplItemDraftDO> all = demandDBHelper.getAll(PplItemDraftDO.class,
                        "where ppl_order = ? and draft_status = ?", pplOrder, PplOrderDraftStatusEnum.DRAFT.getCode());
                demandDBHelper.delete(draftDO);
                demandDBHelper.delete(all);
            }
            newDraft.setId(null);
            newDraft.setDraftStatus(PplOrderDraftStatusEnum.DRAFT.getCode());
            newDraft.setModifyReason(refuseNote);
            newDraft.setSource(OperateTypeEnum.UPDATE.getCode());
            demandDBHelper.insert(newDraft);
            List<PplItemDraftDO> all = demandDBHelper.getAll(PplItemDraftDO.class,
                    "where ppl_order = ? and draft_status = ?", pplOrder, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            for (PplItemDraftDO pplItemDraftDO : all) {
                PplItemDraftDO newItemDraft = new PplItemDraftDO();
                BeanUtils.copyProperties(pplItemDraftDO, newItemDraft);
                newItemDraft.setId(null);
                newItemDraft.setDraftStatus(PplOrderDraftStatusEnum.DRAFT.getCode());
                demandDBHelper.insert(newItemDraft);
            }

            // 将原来这条标记为删除 所有item的 核数 调整为0
            preSubmitDO.setSource(OperateTypeEnum.DELETED.getCode());
            preSubmitDO.setAllCore(0);
            preSubmitDO.setAllDisk(0);
            demandDBHelper.update(preSubmitDO);
            demandDBHelper.executeRaw("update ppl_item_draft" +
                            " set instance_num = 0,total_core = 0,total_disk = 0,total_gpu_num = 0 where  deleted = 0 and ppl_order = ? and draft_status =?"
                    , pplOrder, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());

        }

        return preSubmitDO.getId();
    }

//    @Override
//    @Transactional(value = "demandTransactionManager")
//    public void withdrawPreSubmit(String pplOrder, String refuseNote) {
//        Long draftOrderId = withdrawPreSubmitPplOrder(pplOrder, "");
//        recordWithdrawPreSubmit(draftOrderId);
//    }

    // 记录 需求沟通-撤回 日志
    private void recordWithdrawPreSubmit(Long draftOrderId) {
        PplOrderDraftVO preSubmitDO = demandDBHelper.getOne(PplOrderDraftVO.class,
                "where id = ?", draftOrderId);
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        preSubmitDO.getPplItemDrafts().forEach(itemDraftDO -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(preSubmitDO, beforeAllFieldDTO);
            BeanUtils.copyProperties(itemDraftDO, beforeAllFieldDTO);
            beforeAllFieldDTO.setStatus(StringUtils.isBlank(itemDraftDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                    : itemDraftDO.getStatus());
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(preSubmitDO, afterAllFieldDTO);
            BeanUtils.copyProperties(itemDraftDO, afterAllFieldDTO);
            afterAllFieldDTO.setInstanceNum(0);
            afterAllFieldDTO.setTotalCore(0);
            afterAllFieldDTO.setTotalGpuNum(BigDecimal.valueOf(0));
            afterAllFieldDTO.setTotalDisk(0);
            afterAllFieldDTO.setStatus(StringUtils.isBlank(itemDraftDO.getStatus()) ? PplItemStatusEnum.VALID.getCode()
                    : itemDraftDO.getStatus());
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            newDTOs.add(recordNewDTO);
        });
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PRE_SUBMIT_WITHDRAW.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));
    }

    @Override
    @Transactional("demandTransactionManager")
    public void revertPplOrder(List<String> pplOrderList) {
        // 先查出当前的item情况 记录变更日志用
        List<PplItemDraftDO> itemDraftList = demandDBHelper.getAll(PplItemDraftDO.class,
                "where draft_status = ? and ppl_order in (?)",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), pplOrderList);

        List<PplOrderDO> orderDOList = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)", pplOrderList);
        List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class, "where ppl_order in (?)", pplOrderList);
        Map<String, PplOrderDO> orderToMap = orderDOList.stream()
                .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v));

        Map<String, List<PplItemDO>> pplOrderToItemList = pplItemDOList.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));
        Map<String, PplItemDO> pplIdToItem = pplItemDOList.stream()
                .collect(Collectors.toMap(PplItemDO::getPplId, v -> v));

        List<String> notValidPpl = new ArrayList<>();
        for (String pplOrder : pplOrderList) {
            if (pplOrderToItemList.get(pplOrder) == null) {
                notValidPpl.add(pplOrder);
            }
        }
        if (ListUtils.isNotEmpty(notValidPpl)) {
            String join = Strings.join(",", notValidPpl);
            throw new BizException("新增需求无法撤销 [" + join + "]");
        }
        for (PplOrderDO orderDO : orderDOList) {
            List<PplItemDO> itemDOList = pplOrderToItemList.get(orderDO.getPplOrder());
            removePreSubmitDraft(Arrays.asList(orderDO.getPplOrder()), false);
            SavePplDraftReq savePplDraftReq = SavePplDraftReq.pplItemTransToDraftReq(orderDO, itemDOList);
            savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            savePplDraftReq.setIsRecord(Boolean.FALSE);
            saveDraft(savePplDraftReq);
        }

        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        itemDraftList.forEach(beforeItemDO -> {
            PplOrderDO orderDO = orderToMap.get(beforeItemDO.getPplOrder());
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, beforeAllFieldDTO);
            BeanUtils.copyProperties(beforeItemDO, beforeAllFieldDTO);
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, afterAllFieldDTO);
            PplItemDO afterItemDO = pplIdToItem.get(beforeItemDO.getPplId());
            if (afterItemDO == null) {
                // 如果after为空 说明之后是没有的。 那么复制之前的item并且全部设置为0
                BeanUtils.copyProperties(beforeItemDO, beforeAllFieldDTO);
                afterAllFieldDTO.setInstanceNum(0);
                afterAllFieldDTO.setTotalCore(0);
                afterAllFieldDTO.setTotalGpuNum(BigDecimal.valueOf(0));
                afterAllFieldDTO.setTotalDisk(0);
            } else {
                BeanUtils.copyProperties(afterItemDO, afterAllFieldDTO);
            }
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            newDTOs.add(recordNewDTO);
        });
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PPL_IMPORT_REVERT.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));

    }

    @Override
    @Transactional("demandTransactionManager")
    public void withdrawPplOrders(List<String> pplOrders, String modifyReason) {
        List<PplItemDO> itemDOList = demandDBHelper.getAll(PplItemDO.class, "where ppl_order in(?) ", pplOrders);

        // 已预约的PPL不能取消
        List<PplItemDO> itemAppliedDOList = itemDOList.stream()
                .filter(e -> PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus())).collect(
                        Collectors.toList());
        if (!CollectionUtils.isEmpty(itemAppliedDOList)) {
            throw new BizException(
                    "选择的ppl单-" + itemAppliedDOList.stream().map(PplItemDO::getPplOrder).distinct().collect(
                            Collectors.toList()) + "的明细中含有已预约数据，不允许取消！");
        }

        // 过去（按开始购买时间判断）的PPL不能取消 23-12-26 该逻辑取消
//        List<PplItemDO> oldItemDOList = itemDOList.stream().filter(e -> LocalDate.now().isAfter(e.getBeginBuyDate()))
//                .collect(
//                        Collectors.toList());
//        if (!CollectionUtils.isEmpty(oldItemDOList)) {
//            throw new BizException(
//                    "过去的ppl单-" + oldItemDOList.stream().map(PplItemDO::getPplOrder).distinct().collect(
//                            Collectors.toList()) + "，不允许取消！");
//        }

        List<PplOrderWithPplItemVO> pplOrderDOList = demandDBHelper.getAll(PplOrderWithPplItemVO.class,
                "where ppl_order in(?) ", pplOrders);

        List<String> inProcessOrderList = new ArrayList<>();
        // 如果当前正在审批中，则不能取消
        for (PplOrderWithPplItemVO pplOrderDO : pplOrderDOList) {
            if (Strings.isNotBlank(pplOrderDO.getNodeCode())) {
                inProcessOrderList.add(pplOrderDO.getPplOrder());
            }
        }
        if (ListUtils.isNotEmpty(inProcessOrderList)) {
            throw new BizException("单据已经在审批流程中，不允许取消PPL。请在下个需求沟通环节取消PPL。"
                    + JSON.toJson(inProcessOrderList));
        }

        List<PplOrderDraftDO> draftDOs = demandDBHelper.getAll(PplOrderDraftDO.class,
                "where ppl_order in(?) and draft_status = ? ", pplOrders, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        if (!CollectionUtils.isEmpty(draftDOs)) {
            // 如果原来草稿箱存在相同的 则直接删除
            demandDBHelper.executeRaw("update ppl_order_draft set deleted = 1 where id in(?) ",
                    draftDOs.stream().map(PplOrderDraftDO::getId).collect(
                            Collectors.toList()));
            demandDBHelper.executeRaw(
                    "update ppl_item_draft set deleted = 1 where ppl_order in(?) and draft_status = ? ",
                    draftDOs.stream().map(PplOrderDraftDO::getPplOrder).collect(
                            Collectors.toList()), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        }

        //  取消 已生效需求：
        //  1、复制到草稿箱; 2、将当前的ppl单标记为删除；实例类型、总核数、总卡数设为0
        Map<String, List<PplItemDO>> pplItemDOMap = itemDOList.stream()
                .collect(Collectors.groupingBy(PplItemDO::getPplOrder));

        List<PplOrderDraftDO> insertDraftOrders = new ArrayList<>();
        pplOrderDOList.forEach(pplOrderDO -> {
            PplOrderDraftDO newDraft = new PplOrderDraftDO();
            BeanUtils.copyProperties(pplOrderDO, newDraft);
            newDraft.setId(null);
            newDraft.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            newDraft.setModifyReason(modifyReason);
            newDraft.setSource(OperateTypeEnum.DELETED.getCode());
            newDraft.setAllCore(0);
            newDraft.setAllDisk(0);
            newDraft.setUpdateTime(new Date());

            PplItemDO itemDO = pplItemDOMap.get(pplOrderDO.getPplOrder()).get(0);
            newDraft.setDemandType(itemDO.getDemandType());
            newDraft.setDemandScene(itemDO.getDemandScene());
            newDraft.setProjectName(itemDO.getProjectName());
            newDraft.setProduct(itemDO.getProduct());
            newDraft.setBeginDate(itemDO.getBeginBuyDate().toString());
            newDraft.setEndDate(itemDO.getEndBuyDate().toString());

            insertDraftOrders.add(newDraft);
        });

        List<PplItemDraftDO> insertDraftItemDOs = new ArrayList<>();
        for (PplItemDO itemDO : itemDOList) {
            PplItemDraftDO newItemDraft = new PplItemDraftDO();
            BeanUtils.copyProperties(itemDO, newItemDraft);
            newItemDraft.setId(null);
            newItemDraft.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            newItemDraft.setInstanceNum(0);
            newItemDraft.setTotalCore(0);
            newItemDraft.setTotalDisk(0);
            newItemDraft.setTotalGpuNum(BigDecimal.valueOf(0));

            newItemDraft.setType(OperateTypeEnum.DELETED.getCode());

            insertDraftItemDOs.add(newItemDraft);
        }

        demandDBHelper.insert(insertDraftOrders);
        demandDBHelper.insert(insertDraftItemDOs);

        // 记录日志
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        pplOrderDOList.forEach(orderDO -> orderDO.getPplItems().forEach(itemDO -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, beforeAllFieldDTO);
            BeanUtils.copyProperties(itemDO, beforeAllFieldDTO);
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, afterAllFieldDTO);
            BeanUtils.copyProperties(itemDO, afterAllFieldDTO);
            afterAllFieldDTO.setInstanceNum(0);
            afterAllFieldDTO.setTotalCore(0);
            afterAllFieldDTO.setTotalGpuNum(BigDecimal.valueOf(0));
            afterAllFieldDTO.setTotalDisk(0);
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            recordNewDTO.setOperateNote(modifyReason);
            newDTOs.add(recordNewDTO);
        }));
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PPL_IMPORT_CANCEL.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));
    }

    @Override
    public RequiredDemandRsp queryExpiredDemandList(QueryExpiredDemandReq req) {
        String industryDept = req.getIndustryDept();
        List<String> products = req.getProducts();

        List<PplOrderRequiredDTO> toDealList = new ArrayList<>();
        List<PplOrderRequiredDTO> dealedList = new ArrayList<>();
        RequiredDemandRsp resp = new RequiredDemandRsp();
        resp.setToDealList(toDealList);
        resp.setDealedList(dealedList);

        // 获取行业各产品当前进行中版本
        Map<String, Long> productOfVersionMap = innerVersionService.queryProcessingVersionMap(industryDept);
        List<Long> queryVersionIds = new ArrayList<>();
        productOfVersionMap.forEach((key, value) -> {
            if (products.contains(key)) {
                queryVersionIds.add(value);
            }
        });
        if (ListUtils.isEmpty(queryVersionIds)) {
            return resp;
        }
        // 获取相对此版本的过期需求列表
        List<PplOrderExpiredRecordDO> expiredRecordDOList = demandDBHelper.getAll(PplOrderExpiredRecordDO.class,
                "where version_id in(?) and industry_dept = ? and product in(?)", queryVersionIds, industryDept,
                products);
        List<String> expiredPplOrders = expiredRecordDOList.stream().map(PplOrderExpiredRecordDO::getPplOrder)
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(expiredPplOrders)) {
            return resp;
        }
        // 获取具体的单据信息
        String userName = LoginUtils.getUserNameWithSystem();
        WhereContent querySql = pplInnerProcessService.checkPermission(userName, req.getIndustryDept(), "t2.",
                queryVersionIds);
        if (ObjectUtils.isEmpty(querySql)) {
            querySql = new WhereContent();
            // 代表无任何权限，此时只看得到自己的，也就是架构师层面过滤
            querySql.addAnd("t2.submit_user = ?", userName);
        }
        querySql.addAnd("t2.ppl_order in(?)", expiredPplOrders);
        querySql.addAnd("t2.industry_dept = ? ", industryDept);
        querySql.andInIfValueNotEmpty("t1.product", products);
        querySql.addAnd("t1.status != 'APPLIED'");
        querySql.addAnd("t1.instance_num > 0");
        querySql.addAnd("t2.input_status = ?", PplOrderInputStatusEnum.NORMAL_EXPIRED.getCode());
        querySql.addAnd("t2.source in (?)", Collections.singletonList(PplOrderSourceTypeEnum.IMPORT.getCode()));
        querySql.addAnd("t2.submit_user != 'dreamxin'");
        querySql.addAnd("t2.customer_short_name not like '%云运管%'");
        List<PplItemJoinOrderVO> pplList = demandDBHelper.getAll(PplItemJoinOrderVO.class,
                querySql.getSql(), querySql.getParams());
        Map<String, List<PplItemJoinOrderVO>> pplMap = pplList.stream()
                .collect(Collectors.groupingBy(e -> e.getPplOrderDO().getPplOrder()));

        Map<String, PplOrderExpiredRecordDO> expiredPplMap = expiredRecordDOList.stream()
                .collect(Collectors.toMap(PplOrderExpiredRecordDO::getPplOrder, Function.identity(), (v1, v2) -> v2));
        for (Map.Entry<String, PplOrderExpiredRecordDO> entry : expiredPplMap.entrySet()) {
            String pplOrder = entry.getKey();
            PplOrderExpiredRecordDO expiredRecordDO = entry.getValue();
            List<PplItemJoinOrderVO> joinVOs = pplMap.get(pplOrder);

            if (ListUtils.isEmpty(joinVOs)) {
                continue;
            }

            // 待处理 过期列表
            if (PplOrderExpiredOperateTypeEnum.TO_DEAL.getCode().equals(expiredRecordDO.getExpiredOperateType())) {
                toDealList.add(setExpiredDemand(expiredRecordDO, joinVOs, true));
                continue;
            }

            // 已处理 过期列表
            dealedList.add(setExpiredDemand(expiredRecordDO, joinVOs, false));
        }

        return resp;
    }

    @Override
    public List<PplRelateEnumDTO> queryExpiredPplOperateReasonEnum() {
        List<PplRelateEnumDTO> operateReasonEnums = new ArrayList<>();

        for (PplOrderExpiredOperateReasonEnum e : PplOrderExpiredOperateReasonEnum.values()) {
            PplRelateEnumDTO changeTypeEnum = new PplRelateEnumDTO();
            changeTypeEnum.setEnumCode(e.getCode());
            changeTypeEnum.setEnumName(e.getName());
            operateReasonEnums.add(changeTypeEnum);
        }

        return operateReasonEnums;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void reFreshExpiredPplStatus(RefreshExpiredReq req) {
        // 情形1： 对指定的版本 刷新 其过期需求状态
        if (req.getLastVersionId() != null && req.getCurrentVersionId() != null) {
            WhereContent query = new WhereContent();
            query.addAnd("id = ? ", req.getCurrentVersionId());
            query.addAnd("status = ?", PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
            if (StringUtils.isNotBlank(req.getProduct())) {
                query.addAnd("product like ?", "%" + req.getProduct() + "%");
            }
            PplInnerProcessVersionDO currentVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    query.getSql(), query.getParams());
            if (currentVersion == null) {
                throw new BizException("当前版本id-" + req.getCurrentVersionId() + "对应的版本不存在！");
            }

            String product = StringUtils.isNotBlank(req.getProduct()) ? req.getProduct() : currentVersion.getProduct();
            WhereContent querySql = new WhereContent();
            querySql.addAnd("status = ?", PplInnerProcessVersionStatusEnum.DONE.getCode());
            querySql.addAnd("industry_dept = ?", currentVersion.getIndustryDept());
            querySql.addAnd("product like ?", "%" + product + "%");
            querySql.orderDesc("id");
            PplInnerProcessVersionDO lastVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                    querySql.getSql(), querySql.getParams());
            if (lastVersion == null) {
                throw new BizException("当前版本id-" + req.getCurrentVersionId() + "对应的上一版本不存在！");
            }
            if (!req.getLastVersionId().equals(lastVersion.getId())) {
                throw new BizException(
                        "上一版本id-" + req.getLastVersionId() + "与数据库查出的上一版本id-" + lastVersion.getId()
                                + "对应不上，请重新设置传参！");
            }

            // 获取进行中版本对应的审批节点列表
            List<PplInnerProcessVersionSlaDO> auditNodeList = demandDBHelper.getAll(
                    PplInnerProcessVersionSlaDO.class, "where version_id = ? ", currentVersion.getId());
            PplInnerProcessVersionSlaDO firstAuditNode = auditNodeList.stream()
                    .filter(node -> "ENTER".equals(node.getDeadlineType())).findFirst().orElse(null);
            if (firstAuditNode == null || firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime()
                    .after(new Date())) {
                pplInnerProcessService.processExpiredDemand(lastVersion, currentVersion);
            }
            return;
        }

        // 情形2：对所有版本 刷新 其过期需求状态
        // 获取当前的所有进行中版本
        List<PplInnerProcessVersionDO> processVersions = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where status = ? and deleted = 0 group by industry_dept, product ",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode());

        List<Long> processVersionIds = processVersions.stream().map(PplInnerProcessVersionDO::getId).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(processVersionIds)) {
            return;
        }

        // 获取进行中版本对应的审批节点列表
        Map<Long, List<PplInnerProcessVersionSlaDO>> auditNodeMap = demandDBHelper.getAll(
                        PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", processVersionIds).stream()
                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));

        // 获取所有当前版本的上一个版本
        List<PplInnerProcessVersionDO> lastVersionDOList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where id in( "
                        + " select MAX(id) from ppl_inner_process_version "
                        + "  where status = ? and deleted = 0 "
                        + "  group by industry_dept, product "
                        + ")",
                PplInnerProcessVersionStatusEnum.DONE.getCode());
        Map<String, PplInnerProcessVersionDO> lastVersionMap = lastVersionDOList.stream()
                .collect(Collectors.toMap(e -> String.join("&", e.getIndustryDept(), e.getProduct()),
                        Function.identity(), (v1, v2) -> v2));

        // 开始对符合条件的版本，重刷其过期需求的状态
        for (PplInnerProcessVersionDO currentVersion : processVersions) {
            PplInnerProcessVersionDO lastVersion = lastVersionMap.get(
                    String.join("&", currentVersion.getIndustryDept(), currentVersion.getProduct()));
            if (lastVersion == null) {
                continue;
            }

            List<PplInnerProcessVersionSlaDO> slaDOList = auditNodeMap.get(currentVersion.getId());
            PplInnerProcessVersionSlaDO firstAuditNode = slaDOList.stream()
                    .filter(node -> "ENTER".equals(node.getDeadlineType())).findFirst().orElse(null);
            if (firstAuditNode == null || firstAuditNode.getDeadlineTime() == null || firstAuditNode.getDeadlineTime()
                    .after(new Date())) {
                pplInnerProcessService.processExpiredDemand(lastVersion, currentVersion);
            }

        }


    }

    @Override
    @Transactional("demandTransactionManager")
    public void batchAdjustPplDate(BatchAdjustPplOrderReq req) {
        if (req.getIsCurrent()) {
            LocalDate beginBuyDate;
            try {
                beginBuyDate = LocalDate.parse(req.getBeginBuyDate());
            } catch (Exception e) {
                throw new BizException("日期格式不合规");
            }

            List<PplOrderDraftDO> orderDraftDOList = demandDBHelper.getAll(PplOrderDraftDO.class,
                    "where ppl_order in (?) and draft_status = ?",
                    req.getPplOrders(), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            Map<String, PplOrderDraftDO> pplOrderToDraftDO = orderDraftDOList.stream()
                    .collect(Collectors.toMap(PplOrderDraftDO::getPplOrder, v -> v));

            List<PplItemDraftDO> all = demandDBHelper.getAll(PplItemDraftDO.class,
                    "where draft_status = ? and ppl_order in (?)",
                    PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), req.getPplOrders());
//            for (PplItemDraftDO itemDraftDO : all) {
//                Integer diffMonth = (beginBuyDate.getYear() - itemDraftDO.getBeginBuyDate().getYear()) * 12 + (
//                        beginBuyDate.getMonthValue() - itemDraftDO.getBeginBuyDate().getMonthValue());
//                itemDraftDO.setBeginBuyDate(itemDraftDO.getBeginBuyDate().plusMonths(diffMonth));
//                itemDraftDO.setEndBuyDate(itemDraftDO.getEndBuyDate().plusMonths(diffMonth));
//            }

            if (CollectionUtils.isEmpty(orderDraftDOList) || CollectionUtils.isEmpty(all)) {
                throw new BizException("需求沟通中查询不到相关单据，请关注单据是否已流转至审批流");
            }
            String industry = orderDraftDOList.get(0).getIndustryDept();
            List<String> products = all.stream().map(PplItemDraftDO::getProduct).distinct().collect(
                    Collectors.toList());
            checkVersionSubmit(industry, products);

            // 记录日志
            List<PplItemChangeRecordNewDTO> updateDTOs = new ArrayList<>();
            String product = null;
            String industryDept = null;
            for (PplItemDraftDO itemDraftDO : all) {
                PplItemChangeRecordNewDTO dto = new PplItemChangeRecordNewDTO();
                PplOrderDraftDO orderDraftDO = pplOrderToDraftDO.get(itemDraftDO.getPplOrder());
                PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(orderDraftDO, beforeAllFieldDTO);
                BeanUtils.copyProperties(itemDraftDO, beforeAllFieldDTO);
                dto.setBeforeItem(beforeAllFieldDTO);

                // 记录变化后日志并更新变更后日期
                Integer diffMonth = (beginBuyDate.getYear() - itemDraftDO.getBeginBuyDate().getYear()) * 12 + (
                        beginBuyDate.getMonthValue() - itemDraftDO.getBeginBuyDate().getMonthValue());
                itemDraftDO.setBeginBuyDate(itemDraftDO.getBeginBuyDate().plusMonths(diffMonth));
                itemDraftDO.setEndBuyDate(itemDraftDO.getEndBuyDate().plusMonths(diffMonth));
                PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(orderDraftDO, afterAllFieldDTO);
                BeanUtils.copyProperties(itemDraftDO, afterAllFieldDTO);
                dto.setAfterItem(afterAllFieldDTO);
                updateDTOs.add(dto);

                if (product == null) {
                    product = orderDraftDO.getProduct();
                }
                if (industryDept == null) {
                    industryDept = orderDraftDO.getIndustryDept();
                }
            }

            demandDBHelper.update(all);
            demandDBHelper.executeRaw(
                    "update ppl_order_draft set source = ? where deleted = 0 and "
                            + "draft_status = ? and ppl_order in (?) and source != ?",
                    OperateTypeEnum.UPDATE.getCode(), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                    req.getPplOrders(), OperateTypeEnum.INSERT.getCode());

            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD",
                            PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                            PplRecordChangeEventEnum.PPL_IMPORT_EDIT.getCode(), LoginUtils.getUserNameWithSystem(),
                            updateDTOs));

            // 修改PPL触发共识状态变化
            effectConsensus(all, industryDept, LoginUtils.getUserNameWithSystem());

        } else {
            // 查找单据信息
            List<PplOrderDO> orderDOList = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)",
                    req.getPplOrders());
            List<PplItemDO> pplItemDOList = demandDBHelper.getAll(PplItemDO.class, "where ppl_order in (?)",
                    req.getPplOrders());

            Map<String, List<PplItemDO>> pplOrderToItemList = pplItemDOList.stream()
                    .collect(Collectors.groupingBy(PplItemDO::getPplOrder));

            if (ObjectUtils.isEmpty(orderDOList) || CollectionUtils.isEmpty(pplItemDOList)) {
                throw new BizException("查询不到相关 pplOrder或pplItem");
            }

            String industryDept = orderDOList.get(0).getIndustryDept();
            List<String> products = pplItemDOList.stream().map(PplItemDO::getProduct).distinct().collect(
                    Collectors.toList());

            // 1、历史周期需求 - 忽略处理
            if (StringUtils.isNotBlank(req.getOperateType()) && PplOrderExpiredOperateTypeEnum.DEAL_IGNORE.getCode()
                    .equals(req.getOperateType())) {
                // 设置过期需求处理记录
                batchUpdateExpiredRecord(industryDept, products, req.getPplOrders(),
                        PplOrderExpiredOperateTypeEnum.DEAL_IGNORE.getCode(), Collections.emptyMap());
                return;
            }

            // 2、历史周期需求 - 更改年月(复制新增) / 延期处理(复制新增)
            // 检查日期格式
            LocalDate beginBuyDate;
            try {
                beginBuyDate = LocalDate.parse(req.getBeginBuyDate());
            } catch (Exception e) {
                throw new BizException("日期格式不合规");
            }
            // 检查版本提交截止时间
            checkVersionSubmit(industryDept, products);
            // 如果是延期操作，检查传递的操作类型
            if (StringUtils.isNotBlank(req.getOperateType()) && !PplOrderExpiredOperateTypeEnum.DEAL_DELAY.getCode()
                    .equals(req.getOperateType())) {
                throw new BizException("处理方式不合规，请联系samuelssu查看");
            }

            // 操作日志，针对新单据生成具体的操作备注
            Map<String, String> noteByOrderMap = new HashMap<>();
            // 过期需求的原始日期，记录在延期后的新单据上
            Map<String, LocalDate> dateByOrderMap = new HashMap<>();
            // 延期记录，针对原始单据生成具体的延期记录
            Map<String, PplOrderExpiredRecordDO> recordByOrderMap = new HashMap<>();

            // 因为本质上是复制，非原来的ppl 各种参数应该要清空
            List<SavePplDraftReq> list = new ArrayList<>();
            for (PplOrderDO orderDO : orderDOList) {
                String oldPplOrder = orderDO.getPplOrder();
                List<PplItemDO> itemDOList = pplOrderToItemList.get(orderDO.getPplOrder());

                String newPplOrder = pplCommonService.generatePplOrderId("N");
                orderDO.setPplOrder(newPplOrder);

                if (!CollectionUtils.isEmpty(itemDOList)) {
                    String operateReasonName = PplOrderExpiredOperateReasonEnum.getNameByCode(req.getOperateReason());
                    String changeNote = String.format("源于%d年%d月需求延期。原因：%s。复制ID：%s",
                            itemDOList.get(0).getBeginBuyDate().getYear(),
                            itemDOList.get(0).getBeginBuyDate().getMonthValue(),
                            StringUtils.isNotBlank(operateReasonName) ? operateReasonName : "-",
                            oldPplOrder);
                    noteByOrderMap.put(newPplOrder, changeNote);
                    dateByOrderMap.put(newPplOrder, itemDOList.get(0).getBeginBuyDate());
                }

                PplOrderExpiredRecordDO recordDO = new PplOrderExpiredRecordDO();
                recordDO.setExpiredPplOrder(newPplOrder);
                recordDO.setExpiredOperateReason(req.getOperateReason());
                recordDO.setExpiredYear(beginBuyDate.getYear());
                recordDO.setExpiredMonth(beginBuyDate.getMonthValue());
                recordByOrderMap.put(oldPplOrder, recordDO);

                for (PplItemDO pplItemDO : itemDOList) {
                    Integer diffMonth = (beginBuyDate.getYear() - pplItemDO.getBeginBuyDate().getYear()) * 12 + (
                            beginBuyDate.getMonthValue() - pplItemDO.getBeginBuyDate().getMonthValue());
                    pplItemDO.setBeginBuyDate(pplItemDO.getBeginBuyDate().plusMonths(diffMonth));
                    pplItemDO.setEndBuyDate(pplItemDO.getEndBuyDate().plusMonths(diffMonth));
                    pplItemDO.setPplOrder(newPplOrder);
                    pplItemDO.setPplId(null);
                    pplItemDO.setParentPplId(null);
                    pplItemDO.setStatus(null);
                    pplItemDO.setYunxiaoOrderId("");
                    pplItemDO.setYunxiaoOrderStatus("");
                    pplItemDO.setYunxiaoDetailId(0L);

                    pplItemDO.setInstanceNumApplyBefore(pplItemDO.getInstanceNumApplyAfter());
                    pplItemDO.setInstanceNumApplyAfter(0);
                    pplItemDO.setTotalCoreApplyBefore(pplItemDO.getTotalCoreApplyAfter());
                    pplItemDO.setTotalCoreApplyAfter(0);
                    pplItemDO.setTotalGpuNumApplyBefore(pplItemDO.getTotalGpuNumApplyAfter());
                    pplItemDO.setTotalGpuNumApplyAfter(BigDecimal.ZERO);
                }
                SavePplDraftReq savePplDraftReq = SavePplDraftReq.pplItemTransToDraftReq(orderDO, itemDOList);
                savePplDraftReq.setType(OperateTypeEnum.INSERT.getCode());
                savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
                list.add(savePplDraftReq);
            }

            String changeEvent = null;
            Map<String, String> noteMap = null;
            Map<String, LocalDate> dateMap = null;
            if (StringUtils.isNotBlank(req.getOperateType())) {
                // 设置过期需求处理记录
                batchUpdateExpiredRecord(industryDept, products, req.getPplOrders(),
                        PplOrderExpiredOperateTypeEnum.DEAL_DELAY.getCode(), recordByOrderMap);

                changeEvent = PplRecordChangeEventEnum.PPL_EXPIRED_COPY_ADD.getCode();
                noteMap = noteByOrderMap;
                dateMap = dateByOrderMap;
            }

            batchSaveDraft(list, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), Boolean.TRUE,
                    changeEvent, noteMap, dateMap);

        }
    }

    @Override
    public Date getDeadline(String industryDept, String product) {
        PplInnerProcessVersionDO currentVersion = innerVersionService.queryProcessingVersionByDeptAndProduct(
                industryDept,
                product);

        PplInnerProcessVersionSlaDO slaDO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = 'ENTER'", currentVersion.getId());
        if (slaDO == null || slaDO.getDeadlineTime() == null) {
            throw new BizException("行业流程未做sla初始化");
        }
        return slaDO.getDeadlineTime();
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(keyScript = "args[0]",waitLockMillisecond = 100,
            throwExceptionIfNotGetLock = false,customExceptionMessage = "导入数据正在处理中，请等待")
    public void importDataToDraft(String userName,ImportDataToDraftReq req) {

        PplInnerProcessVersionDO versionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(req.getIndustryDept(),
                req.getProduct());
        LocalDate beginBuyDate = versionDO.getInCountryDemandYearMonthFirstDay();
        if (req.getIndustryDept().equals(IndustryDeptEnum.STRATEGY.getName())) {
            // 软删除所有草稿和沟通中的对应产品的draft
            WhereSQL draftSql = new WhereSQL();
            draftSql.and("(t1.draft_status = ? OR t1.draft_status = ?)",
                    PplOrderDraftStatusEnum.DRAFT.getCode(), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            draftSql.and("(t2.draft_status = ? OR t2.draft_status = ?)",
                    PplOrderDraftStatusEnum.DRAFT.getCode(), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
            draftSql.and("t2.industry_dept = ?", req.getIndustryDept());
//            draftSql.and("t2.submit_user = ?", userName);
            if (!CollectionUtils.isEmpty(req.getCustomer())) {
                draftSql.and("t2.customer_short_name in (?)", req.getCustomer());
            }
            draftSql.and("t1.product = ?", req.getProduct());
            draftSql.and("t1.begin_buy_date >= ?", beginBuyDate);
            List<PplDraftItemJoinDraftOrderVO> all = demandDBHelper.getAll(PplDraftItemJoinDraftOrderVO.class,
                    draftSql.getSQL(), draftSql.getParams());
            List<String> pplOrders = ListUtils.transform(all, v -> v.getOrderDraftDO().getPplOrder());
            removeDraft(pplOrders, false);
            removePreSubmitDraft(pplOrders, false);
        }

        // 查询出目前生效的pplItem
        List<PplItemJoinOrderVO> validItem = getCurrentVersionValidPplItem(
                req.getIndustryDept(), req.getProduct(),  req.getCustomer());
        List<SavePplDraftReq> savePplDraftReqList = new ArrayList<>();

        if (req.getIndustryDept().equals(IndustryDeptEnum.STRATEGY.getName())) {
            boolean isGpu = Objects.equals(req.getProduct(), Ppl13weekProductTypeEnum.GPU.getName());

            // 战略客户部通过bizId关联 覆盖
            Map<String, GroupItemDTO> bizIdToItem = req.getGroupItemDTOList().stream()
                    .collect(Collectors.toMap(GroupItemDTO::getBizId, v -> v));
            ListUtils.transform(req.getGroupItemDTOList(), GroupItemDTO::getBizId);

            // 根据bizId处理已生效数据 （更新、删除）
            Map<String, List<PplItemJoinOrderVO>> validItemMap = validItem.stream()
                    .collect(Collectors.groupingBy(e -> e.getItemDO().getBizId()));
            // <bizId, bizId对应的pplId明细列表>（原本是bizId - pplId 为一对一关系，后面预约有拆单逻辑后，存在bizId - pplId 的一对多关系）
            for (Map.Entry<String, List<PplItemJoinOrderVO>> map : validItemMap.entrySet()) {
                String bizId = map.getKey();
                List<PplItemJoinOrderVO> joinVOs = map.getValue();
                GroupItemDTO importGroupItemDTO = bizIdToItem.get(bizId);

                List<PplItemJoinOrderVO> appliedJoinVOs = joinVOs.stream()
                        .filter(e -> PplItemStatusEnum.APPLIED.getCode().equals(e.getItemDO().getStatus()))
                        .collect(Collectors.toList());
                List<PplItemJoinOrderVO> noAppliedJoinVOs = joinVOs.stream()
                        .filter(e -> !PplItemStatusEnum.APPLIED.getCode().equals(e.getItemDO().getStatus()))
                        .collect(Collectors.toList());

                // 1、预约明细，维持生效数据不变
                appliedJoinVOs.forEach(appliedVO -> {
                    SavePplDraftReq saveAppliedPplDraftReq = new SavePplDraftReq();
                    // 变更类型：无未预约明细时 - 【original】；有未预约明细时 - 【update】
                    saveAppliedPplDraftReq.setType(
                            ListUtils.isEmpty(noAppliedJoinVOs) && importGroupItemDTO == null ? "original" : "update");
                    transToAppliedDraftReq(appliedVO, saveAppliedPplDraftReq);
                    saveAppliedPplDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(saveAppliedPplDraftReq);
                });

                // 2、非预约明细，根据导入的信息及资源量进行调整
                if (importGroupItemDTO == null) {
                    // 2.1、导入无记录
                    noAppliedJoinVOs.forEach(joinVO -> {
                        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
                        // 变更类型：无已预约明细时 - 【delete】；有已预约明细时 - 【update】调整为0
                        savePplDraftReq.setType(ListUtils.isEmpty(appliedJoinVOs) ? "delete" : "update");
                        transToDraftReq(joinVO, savePplDraftReq);
                        savePplDraftReq.setSubmitUser(userName);
                        savePplDraftReqList.add(savePplDraftReq);
                    });
                    continue;
                }

                // 2.2、导入有记录
                Integer totalCoreAppliedSum = appliedJoinVOs.stream().mapToInt(o -> o.getItemDO().getTotalCore())
                        .filter(Objects::nonNull).sum();
                BigDecimal totalGpuNumAppliedSum = appliedJoinVOs.stream().map(o -> o.getItemDO().getTotalGpuNum())
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                int splitInstanceNum;
                if (isGpu) {
                    BigDecimal splitTotalGpuNum = importGroupItemDTO.getTotalGpuNum().subtract(totalGpuNumAppliedSum);
                    splitInstanceNum = splitTotalGpuNum.divide(importGroupItemDTO.getGpuNum(), RoundingMode.CEILING)
                            .intValue();
                } else if (Ppl13weekProductTypeEnum.COS.getName().equals(req.getProduct())
                        || Ppl13weekProductTypeEnum.DATABASE.getName().equals(req.getProduct())) {
                    // 数据库、COS产品目前没有预约功能
                    splitInstanceNum = importGroupItemDTO.getInstanceNum();
                } else {
                    int splitTotalCore = importGroupItemDTO.getTotalCoreNum() - totalCoreAppliedSum;
                    splitInstanceNum = (int) Math.ceil(
                            splitTotalCore * 1.0 / importGroupItemDTO.getInstanceModelCoreNum());
                }

                // 2.2.1、导入的资源量 <= 已预约明细总量
                if (splitInstanceNum <= 0) {
                    // 更新（其他未预约子单资源量置0）
                    noAppliedJoinVOs.stream().forEach(joinVO -> {
                        SavePplDraftReq draftReq = new SavePplDraftReq();
                        draftReq.setType(ListUtils.isEmpty(appliedJoinVOs) ? "delete" : "update");
                        transToDraftReq(joinVO, draftReq);
                        draftReq.setSubmitUser(userName);
                        savePplDraftReqList.add(draftReq);
                    });
                    // 删除原有元素
                    bizIdToItem.remove(bizId);
                    continue;
                }
                // 2.2.2、导入的资源量 > 已预约明细总量
                if (ListUtils.isNotEmpty(noAppliedJoinVOs)) {
                    // a、更新（如果有未预约明细，将导入的多余资源量， 更新到biz_id对应的所有子单中第一个未预约子单里）
                    PplItemJoinOrderVO noAppliedVO = noAppliedJoinVOs.get(0);
                    importGroupItemDTO.setPplOrder(noAppliedVO.getPplOrderDO().getPplOrder());
                    importGroupItemDTO.setPplId(noAppliedVO.getItemDO().getPplId());
                    importGroupItemDTO.setParentPplId(noAppliedVO.getItemDO().getParentPplId());
                    importGroupItemDTO.setIndustryDept(noAppliedVO.getIndustryDept());
                    completeGroupItem(importGroupItemDTO, splitInstanceNum);

                    SavePplDraftReq updateDraftReq = new SavePplDraftReq();
                    updateDraftReq.setType("update");
                    transToDraftReq(importGroupItemDTO, updateDraftReq);
                    updateDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(updateDraftReq);

                    // 更新（其他未预约子单资源量置0）
                    noAppliedJoinVOs.stream().skip(1).forEach(joinVO -> {
                        SavePplDraftReq draftReq = new SavePplDraftReq();
                        draftReq.setType("update");
                        transToDraftReq(joinVO, draftReq);
                        draftReq.setSubmitUser(userName);
                        savePplDraftReqList.add(draftReq);
                    });
                } else {
                    // b、新增（如果无未预约明细，则导入的多余资源量新增一个子单）
                    importGroupItemDTO.setPplOrder(joinVOs.get(0).getPplOrderDO().getPplOrder());
                    importGroupItemDTO.setPplId(null);
                    importGroupItemDTO.setIndustryDept(joinVOs.get(0).getPplOrderDO().getIndustryDept());
                    completeGroupItem(importGroupItemDTO, splitInstanceNum);

                    SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
                    savePplDraftReq.setType("insert");
                    transToDraftReq(importGroupItemDTO, savePplDraftReq);
                    savePplDraftReq.getResources()
                            .forEach(o -> o.setParentPplId(joinVOs.get(0).getItemDO().getParentPplId()));
                    savePplDraftReq.setIndustryDept(req.getIndustryDept());
                    savePplDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(savePplDraftReq);
                }

                // 删除原有元素
                bizIdToItem.remove(bizId);
            }

            // 3、处理剩余新增的数据
            if (bizIdToItem.size() != 0) {
                // 新增
                bizIdToItem.forEach((k, v) -> {
                    SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
                    savePplDraftReq.setType("insert");
                    transToDraftReq(v, savePplDraftReq);
                    savePplDraftReq.setIndustryDept(req.getIndustryDept());
                    savePplDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(savePplDraftReq);
                });
            }

        } else {
            // 其他行业 通过ppl-id关联
            // 查询草稿Item
            Map<String, PplItemDO> pplIdToItem = new HashMap<>();
            if (!CollectionUtils.isEmpty(validItem)) {
                List<PplItemDO> validItemList = validItem.stream().map(PplItemJoinOrderVO::getItemDO)
                        .collect(Collectors.toList());
                pplIdToItem = validItemList.stream().collect(Collectors.toMap(PplItemDO::getPplId, v -> v));

            }
            // 拿到 已生效的还有草稿箱中存在的 ppl-id
            List<PplDraftItemJoinDraftOrderVO> draftItem =
                    getCurrentDraftPplItem(req.getIndustryDept(), req.getProduct(), beginBuyDate.toString(), null, userName);
            List<PplItemDraftDO> itemDraftDOList = draftItem.stream().map(PplDraftItemJoinDraftOrderVO::getItemDraftDO)
                    .collect(Collectors.toList());
            List<PplOrderDraftDO> orderDraftDOListList = draftItem.stream()
                    .map(PplDraftItemJoinDraftOrderVO::getOrderDraftDO).collect(Collectors.toList());
            Map<String, PplItemDraftDO> pplIdToDraftItem = itemDraftDOList.stream()
                    .collect(Collectors.toMap(PplItemDraftDO::getPplId, v -> v));
            Map<String, PplOrderDraftDO> pplOrderToDraftOrder = orderDraftDOListList.stream()
                    .collect(Collectors.toMap(PplOrderDraftDO::getPplOrder, v -> v, (v1, v2) -> v1));

            // 其他行业部门 根据 PPL-ID 更新 有则更新，无则增加
            for (GroupItemDTO groupItemDTO : req.getGroupItemDTOList()) {
                SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
                if (Strings.isNotBlank(groupItemDTO.getPplId())) {
                    if ((pplIdToDraftItem.get(groupItemDTO.getPplId()) == null) &&
                            (pplIdToItem.get(groupItemDTO.getPplId()) == null)) {
                        throw new BizException("该PPL-Id:" + groupItemDTO.getPplId() + "不存在");
                    }
                    // 更新
                    if (pplIdToItem.get(groupItemDTO.getPplId()) != null) {
                        // PPLItem有这个数据说明是历史数据 因此设置为update
                        savePplDraftReq.setType(DraftStatusEnum.UPDATE.getCode());
                        groupItemDTO.setPplOrder(pplIdToItem.get(groupItemDTO.getPplId()).getPplOrder());
                    } else {
                        savePplDraftReq.setType(
                                pplOrderToDraftOrder.get(pplIdToDraftItem.get(groupItemDTO.getPplId()).getPplOrder())
                                        .getSource());
                        groupItemDTO.setPplOrder(pplIdToDraftItem.get(groupItemDTO.getPplId()).getPplOrder());
                    }
                    groupItemDTO.setPplId(groupItemDTO.getPplId());
                    groupItemDTO.setIndustryDept(req.getIndustryDept());
                    transToDraftReq(groupItemDTO, savePplDraftReq);
                    savePplDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(savePplDraftReq);
                } else {
                    // 新增
                    savePplDraftReq.setType("insert");
                    transToDraftReq(groupItemDTO, savePplDraftReq);
                    savePplDraftReq.setIndustryDept(req.getIndustryDept());
                    savePplDraftReq.setSubmitUser(userName);
                    savePplDraftReqList.add(savePplDraftReq);
                }
            }

        }

        // 将同一pplOrder下的pplItem整合，再保存草稿
        Map<String, List<SavePplDraftReq>> savePplDraftReqMap = savePplDraftReqList.stream()
                .collect(Collectors.groupingBy(SavePplDraftReq::getPplOrder));
        List<SavePplDraftReq> savePplDraftReqByOrderList = new ArrayList<>();
        savePplDraftReqMap.forEach((k, v) -> {
            SavePplDraftReq draftReq = new SavePplDraftReq();
            BeanUtils.copyProperties(v.get(0), draftReq);
            v.stream().skip(1).forEach(e -> {
                draftReq.getResources().addAll(e.getResources());
            });
            savePplDraftReqByOrderList.add(draftReq);
        });

        PplDraftService draftService = SpringUtil.getBean(PplDraftService.class);
//
//        for (SavePplDraftReq savePplDraftReq : savePplDraftReqByOrderList) {
//            savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
//            draftService.saveDraft(savePplDraftReq);
//        }

        draftService.batchSaveDraft(savePplDraftReqByOrderList, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), Boolean.TRUE,
                null, null, null);
//
//        if (req.getIndustryDept().equals(IndustryDeptEnum.STRATEGY.getName())) {
//            List<Callable<SavePplDraftRsp>> tasks = new ArrayList<>();
//            for (SavePplDraftReq savePplDraftReq : savePplDraftReqByOrderList) {
//                savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
//                tasks.add(() -> draftService.saveDraft(savePplDraftReq));
//            }
//
//            // 提交所有任务并等待完成
//            try {
//                // 这里多线程执行，无法保证整个导入的事务
//                // 尽管这里多线程提交草稿，存在部分成功部分不成功的情况；
//                // 但是战略有bizId来确定唯一需求，因此能避免导入重复
//                executor.invokeAll(tasks);
//            } catch (InterruptedException e) {
//                // 异常处理（根据业务场景决定是否抛出或记录日志）
//                Thread.currentThread().interrupt(); // 重置中断状态
//                throw new RuntimeException("导入单据失败,请联系oliverychen", e);
//            }
//        } else {
//            for (SavePplDraftReq savePplDraftReq : savePplDraftReqByOrderList) {
//                savePplDraftReq.setDraftStatus(PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
//                draftService.saveDraft(savePplDraftReq);
//            }
//        }



    }

    @Override
    public List<PplItemJoinOrderVO> getCurrentVersionValidPplItem(String industryDept, String product,
            List<String> customer) {

        PplInnerProcessVersionDO versionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(industryDept,
                product);
        WhereSQL whereSQL = new WhereSQL();
        if (industryDept.equals(IndustryDeptEnum.STRATEGY.getName())) {
            whereSQL.and("t1.biz_id is not null");
            if (!CollectionUtils.isEmpty(customer)) {
                whereSQL.and("t2.customer_short_name in (?)", customer);
            }
        }
        whereSQL.and("t1.instance_num > 0");
        whereSQL.and("t1.product = ?", product);
        whereSQL.and("t1.begin_buy_date >= ?", versionDO.getInCountryDemandYearMonthFirstDay());
        whereSQL.and("t2.industry_dept = ?", industryDept);

        List<PplItemJoinOrderVO> validItem = demandDBHelper.getAll(PplItemJoinOrderVO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        validItem = validItem.stream().filter(v -> v.getPplOrderDO() != null).collect(Collectors.toList());

        // 如果行业录入的 海外需求不满足海外年月 需要过滤掉。
        // 海外地域
        List<String> overSeaRegions = pplDictService.queryAllRegionName(true);
        validItem.removeIf( o -> {
            return o.getPplOrderDO().getSource().equals(PplOrderSourceTypeEnum.IMPORT.getCode())
                    && !o.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                    && overSeaRegions.contains(o.getRegionName()) && !versionDO.isSatisfyOverseasYearMonth(
                    o.getItemDO().getBeginBuyDate());
        });
        return validItem;
    }

    @Override
    public List<PplDraftItemJoinDraftOrderVO> getCurrentDraftPplItem(String industryDept, String product,
            String beginBuyDate, List<String> customer, String username) {
        WhereSQL draftSql = new WhereSQL();
        draftSql.and("t2.draft_status = ?",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        draftSql.and("t2.industry_dept = ?", industryDept);

        // 只能查自己权限范围内需求沟通中的ppl
        PplInnerProcessVersionDO innerProcessVersionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(
                industryDept, product);
        WhereContent authContent = pplInnerProcessService.checkPermission(username, industryDept, "t2.",
                innerProcessVersionDO.getId());
        if (ObjectUtils.isEmpty(authContent)) {
            authContent = new WhereContent();
            // 代表无任何权限
            authContent.addAnd("t2.submit_user = ?", username);
        }

        draftSql.and(authContent.getSql().replace("WHERE", ""), authContent.getParams());
        if (!CollectionUtils.isEmpty(customer)) {
            draftSql.and("t2.customer_short_name in (?)", customer);
        }
        draftSql.and("t1.draft_status = ?",
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode());
        draftSql.and("t1.product = ?", product);
        draftSql.and("t1.begin_buy_date >= ?", beginBuyDate);
        return demandDBHelper.getAll(PplDraftItemJoinDraftOrderVO.class, draftSql.getSQL(), draftSql.getParams());
    }

    @Override
    public void deletePplDraftForPreSubmit(List<String> pplOrders,String modifyReason) {
        List<String> existPpl = demandDBHelper.getRaw(String.class,
                "select ppl_order from ppl_item where deleted = 0 and ppl_order in (?)", pplOrders);
        if (ListUtils.isNotEmpty(existPpl)) {
            String updateSql = "update ppl_item_draft set instance_num = 0,total_core = 0,total_disk = 0,total_gpu_num = 0,"
                    + "total_memory = 0,total_database_storage = 0"
                    + " where deleted = 0 and draft_status = ? and ppl_order in (?)";
            demandDBHelper.executeRaw(updateSql, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), existPpl);

            demandDBHelper.executeRaw("update ppl_order_draft set source = ? "
                    + " where deleted = 0 and draft_status = ? and ppl_order in (?)",DraftStatusEnum.DELETED.getCode(),
                     PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), existPpl);
        }
        List<String> deletedPplOrder = pplOrders.stream().filter(pplOrder -> !existPpl.contains(pplOrder))
                .collect(Collectors.toList());

        removePreSubmitDraft(deletedPplOrder);


        List<PplOrderWithPplItemVO> pplOrderDOList = demandDBHelper.getAll(PplOrderWithPplItemVO.class,
                "where ppl_order in(?) ", pplOrders);

        // 记录日志
        List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
        pplOrderDOList.forEach(orderDO -> orderDO.getPplItems().forEach(itemDO -> {
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, beforeAllFieldDTO);
            BeanUtils.copyProperties(itemDO, beforeAllFieldDTO);
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(orderDO, afterAllFieldDTO);
            BeanUtils.copyProperties(itemDO, afterAllFieldDTO);
            afterAllFieldDTO.setInstanceNum(0);
            afterAllFieldDTO.setTotalCore(0);
            afterAllFieldDTO.setTotalGpuNum(BigDecimal.ZERO);
            afterAllFieldDTO.setTotalDisk(0);
            afterAllFieldDTO.setTotalDatabaseStorage(BigDecimal.ZERO);
            afterAllFieldDTO.setTotalMemory(0);
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            recordNewDTO.setOperateNote(modifyReason);
            newDTOs.add(recordNewDTO);
        }));
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.APPROVAL_CHANGE.getCode(),
                        PplRecordChangeEventEnum.PPL_IMPORT_CANCEL.getCode(), LoginUtils.getUserNameWithSystem(),
                        newDTOs));
    }

    // 新增/更新用
    // （新增时这里不用生成pplId，在后面的saveDraft中会生成pplId）
    public void transToDraftReq(GroupItemDTO groupItemDTO, SavePplDraftReq savePplDraftReq) {
        // copy PplOrder
        BeanUtils.copyProperties(groupItemDTO, savePplDraftReq);
        savePplDraftReq.setPplOrder(Strings.isNotBlank(groupItemDTO.getPplOrder()) ?
                groupItemDTO.getPplOrder() : pplCommonService.generatePplOrderId("N"));
        savePplDraftReq.setBeginBuyDate(groupItemDTO.getBeginBuyDate().toString());
        if (groupItemDTO.getEndBuyDate() != null) {
            // COS、数据库产品没有结束购买日期
            savePplDraftReq.setEndBuyDate(groupItemDTO.getEndBuyDate().toString());
        }

        // copy PplItem
        List<DraftItemDTO> resources = new ArrayList<>();
        DraftItemDTO draftItemDTO = new DraftItemDTO();
        BeanUtils.copyProperties(groupItemDTO, draftItemDTO);
        draftItemDTO.setBeginBuyDate(groupItemDTO.getBeginBuyDate().toString());
        if (groupItemDTO.getEndBuyDate() != null) {
            // COS、数据库产品没有结束购买日期
            draftItemDTO.setEndBuyDate(groupItemDTO.getEndBuyDate().toString());
        }
        if (groupItemDTO.getBeginElasticDate() != null) {
            draftItemDTO.setBeginElasticDate(groupItemDTO.getBeginElasticDate().toString());
        }
        if (groupItemDTO.getEndElasticDate() != null) {
            draftItemDTO.setEndElasticDate(groupItemDTO.getEndElasticDate().toString());
        }
        draftItemDTO.setPplId(groupItemDTO.getPplId());
        draftItemDTO.setParentPplId(groupItemDTO.getParentPplId());
        draftItemDTO.setType(savePplDraftReq.getType());
        draftItemDTO.setBizId(groupItemDTO.getBizId());
        // 导入的均调整为未共识
        draftItemDTO.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
        resources.add(draftItemDTO);
        savePplDraftReq.setResources(resources);
    }

    // 删除用
    public void transToDraftReq(PplItemJoinOrderVO pplItemJoinOrderVO, SavePplDraftReq savePplDraftReq) {
        BeanUtils.copyProperties(pplItemJoinOrderVO.getPplOrderDO(), savePplDraftReq);
        List<DraftItemDTO> resources = new ArrayList<>();
        DraftItemDTO draftItemDTO = new DraftItemDTO();
        PplItemDO itemDO = pplItemJoinOrderVO.getItemDO();
        BeanUtils.copyProperties(itemDO, draftItemDTO);
        savePplDraftReq.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(itemDO.getEndBuyDate().toString());
        savePplDraftReq.setProduct(itemDO.getProduct());
        draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
        draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
        if (itemDO.getBeginElasticDate() != null) {
            draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
        }
        if (itemDO.getEndElasticDate() != null) {
            draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
        }
        draftItemDTO.setPplId(itemDO.getPplId());
        draftItemDTO.setParentPplId(itemDO.getParentPplId());
        draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
        draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
        draftItemDTO.setType(savePplDraftReq.getType());
        draftItemDTO.setBizId(itemDO.getBizId());
        draftItemDTO.setInstanceNum(0);
        draftItemDTO.setTotalDiskNum(0);
        draftItemDTO.setTotalCoreNum(0);
        draftItemDTO.setTotalCosStorage(BigDecimal.ZERO);
        draftItemDTO.setTotalDatabaseStorage(BigDecimal.ZERO);
        draftItemDTO.setTotalGpuNum(BigDecimal.ZERO);
        draftItemDTO.setTotalMemory(0);
        resources.add(draftItemDTO);
        savePplDraftReq.setResources(resources);
    }

    // 预约明细更新用
    private void transToAppliedDraftReq(PplItemJoinOrderVO pplItemJoinOrderVO, SavePplDraftReq savePplDraftReq) {
        PplItemDO itemDO = pplItemJoinOrderVO.getItemDO();

        BeanUtils.copyProperties(pplItemJoinOrderVO.getPplOrderDO(), savePplDraftReq);
        savePplDraftReq.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
        savePplDraftReq.setEndBuyDate(itemDO.getEndBuyDate().toString());
        savePplDraftReq.setProduct(itemDO.getProduct());

        List<DraftItemDTO> resources = new ArrayList<>();
        DraftItemDTO draftItemDTO = new DraftItemDTO();
        BeanUtils.copyProperties(itemDO, draftItemDTO);
        draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
        draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
        if (itemDO.getBeginElasticDate() != null) {
            draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
        }
        if (itemDO.getEndElasticDate() != null) {
            draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
        }

        draftItemDTO.setType(savePplDraftReq.getType());
        draftItemDTO.setBizId(itemDO.getBizId());
        draftItemDTO.setPplId(itemDO.getPplId());
        draftItemDTO.setParentPplId(itemDO.getParentPplId());

        draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
        draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
        draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
        draftItemDTO.setTotalGpuNum(itemDO.getTotalGpuNum());

        resources.add(draftItemDTO);
        savePplDraftReq.setResources(resources);
    }

    private void completeGroupItem(GroupItemDTO importGroupItemDTO, Integer splitInstanceNum) {

        final int oneDiskSize = importGroupItemDTO.getTotalDiskNum() / importGroupItemDTO.getInstanceNum();
        final int instanceCore = importGroupItemDTO.getInstanceModelCoreNum();
        final BigDecimal gpuNum =
                importGroupItemDTO.getGpuNum() == null ? BigDecimal.ZERO : importGroupItemDTO.getGpuNum();

        importGroupItemDTO.setInstanceNum(splitInstanceNum);
        importGroupItemDTO.setTotalCoreNum(splitInstanceNum * instanceCore);
        importGroupItemDTO.setTotalGpuNum(BigDecimal.valueOf(splitInstanceNum).multiply(gpuNum));
        importGroupItemDTO.setTotalDiskNum(splitInstanceNum * oneDiskSize);
    }

    List<PplOrderAuditRecordItemDO> recordOriPplItem(Long recordId, String pplOrder) {
        val items = demandDBHelper.getAll(PplItemDO.class, "where ppl_order =?", pplOrder).stream().map(resource -> {
            PplOrderAuditRecordItemDO rc = new PplOrderAuditRecordItemDO();
            rc.setAuditRecordId(recordId);
            rc.setPplOrder(resource.getPplOrder());
            rc.setProduct(resource.getProduct());
            rc.setPplId(resource.getPplId());
            rc.setParentPplId(resource.getParentPplId());
            rc.setExpiredBeginBuyDate(resource.getExpiredBeginBuyDate());

            rc.setStatus(resource.getStatus());
            rc.setYunxiaoOrderId(resource.getYunxiaoOrderId());
            rc.setYunxiaoDetailId(resource.getYunxiaoDetailId());
            rc.setOrderNumberId(resource.getOrderNumberId());
            rc.setYunxiaoOrderStatus(resource.getYunxiaoOrderStatus());
            rc.setInstanceNumApplyBefore(resource.getInstanceNumApplyBefore());
            rc.setInstanceNumApplyAfter(resource.getInstanceNumApplyAfter());
            rc.setTotalCoreApplyBefore(resource.getTotalCoreApplyBefore());
            rc.setTotalCoreApplyAfter(resource.getTotalCoreApplyAfter());
            rc.setTotalGpuNumApplyBefore(resource.getTotalGpuNumApplyBefore());
            rc.setTotalGpuNumApplyAfter(resource.getTotalGpuNumApplyAfter());

            rc.setDemandType(resource.getDemandType());
            rc.setDemandScene(resource.getDemandScene());
            rc.setProjectName(resource.getProjectName());
            rc.setBillType(resource.getBillType());
            rc.setWinRate(resource.getWinRate());
            rc.setImportantDemand(resource.getImportantDemand());
            rc.setBeginBuyDate(resource.getBeginBuyDate());
            rc.setEndBuyDate(resource.getEndBuyDate());
            rc.setBeginElasticDate(resource.getBeginElasticDate());
            rc.setEndElasticDate(resource.getEndElasticDate());
            rc.setNote(resource.getNote());
            rc.setRegionName(resource.getRegionName());
            rc.setZoneName(resource.getZoneName());
            rc.setIsStrongDesignateZone(resource.getIsStrongDesignateZone());
            rc.setInstanceType(resource.getInstanceType());
            rc.setInstanceModel(resource.getInstanceModel());
            rc.setInstanceNum(resource.getInstanceNum());
            rc.setAlternativeInstanceType(resource.getAlternativeInstanceType());
            rc.setAlternativeZoneName(resource.getAlternativeZoneName());
            rc.setAffinityType(resource.getAffinityType());
            rc.setAffinityValue(resource.getAffinityValue());
            rc.setSystemDiskType(resource.getSystemDiskType());
            rc.setSystemDiskStorage(resource.getSystemDiskStorage());
            rc.setSystemDiskNum(resource.getSystemDiskNum());
            rc.setDataDiskType(resource.getDataDiskType());
            rc.setDataDiskStorage(resource.getDataDiskStorage());
            rc.setDataDiskNum(resource.getDataDiskNum());
            rc.setTotalCore(resource.getTotalCore());
            rc.setTotalDisk(resource.getTotalCore());
            rc.setBizId(resource.getBizId());

            rc.setAppRole(resource.getAppRole());
            // gpu相关字段
            rc.setGpuProductType(resource.getGpuProductType());
            rc.setGpuType(resource.getGpuType());
            rc.setGpuNum(resource.getGpuNum());
            rc.setIsAcceptAdjust(resource.getIsAcceptAdjust());
            rc.setAcceptGpu(resource.getAcceptGpu());
            rc.setTotalGpuNum(resource.getTotalGpuNum());
            rc.setBizScene(resource.getBizScene());
            rc.setBizDetail(resource.getBizDetail());
            rc.setServiceTime(resource.getServiceTime());
            rc.setSaleDurationYear(resource.getSaleDurationYear());
            rc.setBusinessCpq(resource.getBusinessCpq());
            rc.setApplyDiscount(resource.getApplyDiscount());
            rc.setPlacementGroup(resource.getPlacementGroup());

            rc.setDatabaseName(resource.getDatabaseName());
            rc.setMoreThanOneAZ(resource.getMoreThanOneAZ());
            rc.setDatabaseStorageType(resource.getDatabaseStorageType());
            rc.setDeployType(resource.getDeployType());
            rc.setFrameworkType(resource.getFrameworkType());
            rc.setSliceNum(resource.getSliceNum());
            rc.setReplicaNum(resource.getReplicaNum());
            rc.setReadOnlyNum(resource.getReadOnlyNum());
            rc.setDatabaseSpecs(resource.getDatabaseSpecs());
            rc.setDatabaseStorage(resource.getDatabaseStorage());
            rc.setTotalDatabaseStorage(resource.getTotalDatabaseStorage());

            rc.setCosStorageType(resource.getCosStorageType());
            rc.setCosAZ(resource.getCosAZ());
            rc.setCosStorage(resource.getCosStorage());
            rc.setTotalCosStorage(resource.getTotalCosStorage());
            rc.setBandwidth(resource.getBandwidth());
            rc.setQps(resource.getQps());

            rc.setInstanceModelCoreNum(resource.getInstanceModelCoreNum());
            rc.setInstanceModelRamNum(resource.getInstanceModelRamNum());
            rc.setTotalMemory(resource.getTotalMemory());

            return rc;
        }).collect(Collectors.toList());

        // 以后新增也会走这里，所以这里要做判断
        if (ListUtils.isNotEmpty(items)){
            demandDBHelper.insert(items);
            return new ArrayList<>();
        }
        return items;
    }

    void recordDraftPplItem(Long recordId, String pplOrder, List<PplOrderAuditRecordItemDO> oriData) {
        val items = demandDBHelper.getAll(PplItemDraftDO.class, "where ppl_order = ? and draft_status = ?",
                        pplOrder, PplOrderDraftStatusEnum.PRE_SUBMIT.getCode()).stream()
                .map(resource -> {
                    PplOrderAuditRecordItemDO rc = new PplOrderAuditRecordItemDO();
                    rc.setAuditRecordId(recordId);
                    rc.setPplOrder(resource.getPplOrder());
                    rc.setProduct(resource.getProduct());
                    rc.setPplId(resource.getPplId());
                    rc.setParentPplId(resource.getParentPplId());
                    rc.setStatus(resource.getStatus());
                    rc.setExpiredBeginBuyDate(resource.getExpiredBeginBuyDate());

                    rc.setYunxiaoOrderId(resource.getYunxiaoOrderId());
                    rc.setYunxiaoDetailId(resource.getYunxiaoDetailId());
                    rc.setOrderNumberId(resource.getOrderNumberId());
                    rc.setYunxiaoOrderStatus(resource.getYunxiaoOrderStatus());
                    rc.setInstanceNumApplyBefore(resource.getInstanceNumApplyBefore());
                    rc.setInstanceNumApplyAfter(resource.getInstanceNumApplyAfter());
                    rc.setTotalCoreApplyBefore(resource.getTotalCoreApplyBefore());
                    rc.setTotalCoreApplyAfter(resource.getTotalCoreApplyAfter());
                    rc.setTotalGpuNumApplyBefore(resource.getTotalGpuNumApplyBefore());
                    rc.setTotalGpuNumApplyAfter(resource.getTotalGpuNumApplyAfter());

                    rc.setDemandType(resource.getDemandType());
                    rc.setDemandScene(resource.getDemandScene());
                    rc.setProjectName(resource.getProjectName());
                    rc.setBillType(resource.getBillType());
                    rc.setWinRate(resource.getWinRate());
                    rc.setImportantDemand(resource.getImportantDemand());
                    rc.setBeginBuyDate(resource.getBeginBuyDate());
                    rc.setEndBuyDate(resource.getEndBuyDate());
                    rc.setBeginElasticDate(resource.getBeginElasticDate());
                    rc.setEndElasticDate(resource.getEndElasticDate());
                    rc.setNote(resource.getNote());
                    rc.setRegionName(resource.getRegionName());
                    rc.setZoneName(resource.getZoneName());
                    rc.setIsStrongDesignateZone(resource.getIsStrongDesignateZone());
                    rc.setInstanceType(resource.getInstanceType());
                    rc.setInstanceModel(resource.getInstanceModel());
                    rc.setInstanceNum(resource.getInstanceNum());
                    rc.setAlternativeInstanceType(resource.getAlternativeInstanceType());
                    rc.setAlternativeZoneName(resource.getAlternativeZoneName());
                    rc.setAffinityType(resource.getAffinityType());
                    rc.setAffinityValue(resource.getAffinityValue());
                    rc.setSystemDiskType(resource.getSystemDiskType());
                    rc.setSystemDiskStorage(resource.getSystemDiskStorage());
                    rc.setSystemDiskNum(resource.getSystemDiskNum());
                    rc.setDataDiskType(resource.getDataDiskType());
                    rc.setDataDiskStorage(resource.getDataDiskStorage());
                    rc.setDataDiskNum(resource.getDataDiskNum());
                    rc.setCbsIo(resource.getCbsIo());
                    rc.setTotalCore(resource.getTotalCore());
                    rc.setTotalDisk(resource.getTotalCore());
                    rc.setBizId(resource.getBizId());

                    rc.setAppRole(resource.getAppRole());
                    // gpu相关字段
                    rc.setGpuProductType(resource.getGpuProductType());
                    rc.setGpuType(resource.getGpuType());
                    rc.setGpuNum(resource.getGpuNum());
                    rc.setIsAcceptAdjust(resource.getIsAcceptAdjust());
                    rc.setAcceptGpu(resource.getAcceptGpu());
                    rc.setTotalGpuNum(resource.getTotalGpuNum());
                    rc.setBizScene(resource.getBizScene());
                    rc.setBizDetail(resource.getBizDetail());
                    rc.setServiceTime(resource.getServiceTime());
                    rc.setSaleDurationYear(resource.getSaleDurationYear());
                    rc.setBusinessCpq(resource.getBusinessCpq());
                    rc.setApplyDiscount(resource.getApplyDiscount());
                    rc.setPlacementGroup(resource.getPlacementGroup());

                    rc.setDatabaseName(resource.getDatabaseName());
                    rc.setMoreThanOneAZ(resource.getMoreThanOneAZ());
                    rc.setDatabaseStorageType(resource.getDatabaseStorageType());
                    rc.setDeployType(resource.getDeployType());
                    rc.setFrameworkType(resource.getFrameworkType());
                    rc.setSliceNum(resource.getSliceNum());
                    rc.setReplicaNum(resource.getReplicaNum());
                    rc.setReadOnlyNum(resource.getReadOnlyNum());
                    rc.setDatabaseSpecs(resource.getDatabaseSpecs());
                    rc.setDatabaseStorage(resource.getDatabaseStorage());
                    rc.setTotalDatabaseStorage(resource.getTotalDatabaseStorage());

                    rc.setCosStorageType(resource.getCosStorageType());
                    rc.setCosAZ(resource.getCosAZ());
                    rc.setCosStorage(resource.getCosStorage());
                    rc.setTotalCosStorage(resource.getTotalCosStorage());
                    rc.setBandwidth(resource.getBandwidth());
                    rc.setQps(resource.getQps());

                    rc.setInstanceModelCoreNum(resource.getInstanceModelCoreNum());
                    rc.setInstanceModelRamNum(resource.getInstanceModelRamNum());
                    rc.setTotalMemory(resource.getTotalMemory());

                    return rc;
                }).collect(Collectors.toList());
        demandDBHelper.insert(items);
        if (!oriData.isEmpty()) {
            val insertPPl = items.stream().map(PplOrderAuditRecordItemDO::getPplId).collect(Collectors.toList());
            val miss = oriData.stream().filter(i -> !insertPPl.contains(i.getPplId()))
                    .map(i -> {
                        i.setId(null);
                        i.setAuditRecordId(recordId);
                        i.setSystemDiskNum(0);
                        i.setDataDiskNum(0);
                        i.setInstanceNum(0);
                        i.setTotalDisk(0);
                        i.setTotalCore(0);
                        i.setTotalGpuNum(BigDecimal.ZERO);
                        i.setTotalCosStorage(BigDecimal.ZERO);
                        i.setTotalDatabaseStorage(BigDecimal.ZERO);
                        i.setTotalMemory(0);
                        return i;
                    }).collect(Collectors.toList());
            if (!miss.isEmpty()) {
                demandDBHelper.insert(miss);
            }
        }
    }

    /**
     * 填充客户信息
     * 客户名称，简称，行业，行业部门，战区，中心
     *
     * @param req
     */
    void fillCustomerInfoByUin(SavePplDraftReq req) {
        if (Strings.isBlank(req.getCustomerUin())) {
            throw new WrongWebParameterException("客户uin为空");
        }
        AccountUtil.AccountInfoDTO accountInfoDTO = AccountUtil.queryUinAccountInfo(req.getCustomerUin(),
                CommonUtil.defaultSecret(),
                Lists.newArrayList("register_industry", "organization_info", "customer_info"));
        if (accountInfoDTO == null || accountInfoDTO.getDecodeDataMap() == null || accountInfoDTO.getDecodeDataMap()
                .isEmpty()) {
            throw new WrongWebParameterException("UIN 查不到信息");
        }
        req.setCustomerName(accountInfoDTO.getField("customer_info.name", String.class));

        if (Strings.isBlank(req.getCustomerName())) {
            throw new WrongWebParameterException("UIN 查不到信息客户名称");
        }

        req.setCustomerShortName(accountInfoDTO.getField("customer_info.sname", String.class));
        //简称可能不存在 使用客户名称当作简称
        if (Strings.isBlank(req.getCustomerShortName())) {
            req.setCustomerShortName(req.getCustomerName());
        }
        req.setIndustry(accountInfoDTO.getField("register_industry.industry_name", String.class));
//        req.setIndustryDept(accountInfoDTO.getField("organization_info.income_organization_name", String.class));

//        23-5-17 保存草稿接口战区必填
//        WarZoneDTO warZoneDTO = AccountUtil.queryUinWarZone(req.getCustomerUin(), CommonUtil.defaultSecret());
//        if (warZoneDTO == null) {
//            throw new WrongWebParameterException("UIN 查不到战区信息");
//        }
//        String warZone = warZoneDTO == null ? "" : warZoneDTO.getLevelOneWarZoneName();
//        req.setWarZone(warZone);

        IndustryDemandIndustryWarZoneDictDO raw = demandDBHelper.getOne(IndustryDemandIndustryWarZoneDictDO.class,
                "where war_zone_name =?", req.getWarZone());
        if (raw == null) {
            throw new WrongWebParameterException("本地未配置战区中心");
        }
    }

    Map<String, PplInnerProcessNodeDO> getNodeMap(String dept) {
        List<PplInnerProcessNodeDO> nodes = demandDBHelper.getRaw(PplInnerProcessNodeDO.class,
                "select n.* from ppl_inner_process_node n \n"
                        + "left join ppl_inner_process   p on n.process_id =p.id \n"
                        + "where p.industry_dept =? ", dept);
        Map<String, PplInnerProcessNodeDO> nodeMap = new HashMap<>();
        nodes.forEach(n -> nodeMap.put(n.getNodeCode(), n));
        return nodeMap;
    }

//    PplInnerProcessNodeDO getFirstNode(String dept) {
//        PplInnerProcessNodeDO node = demandDBHelper.getRawOne(PplInnerProcessNodeDO.class,
//                "select n.* from ppl_inner_process_node n \n"
//                        + "left join ppl_inner_process   p on n.process_id =p.id \n"
//                        + "where p.industry_dept =? and n.is_begin_node =1", dept);
//        if (node == null) {
//            throw new BizException("初始节点获取失败");
//        }
//        return node;
//    }

    PplInnerProcessVersionDO getCurrentVersion(String industry, String product) {
        PplInnerProcessVersionDO versionDO = innerVersionService.queryProcessingVersionByDeptAndProduct(industry,
                product);
        if (ObjectUtils.isEmpty(versionDO)) {
            throw new WrongWebParameterException("当前录入计划未配置");
        }
        return versionDO;
    }

    /**
     * 设置过期需求信息
     *
     * @param expiredRecordDO 过期记录
     * @param joinVOs 单据信息
     * @param isToDeal true - 待处理过期需求，false - 已处理过期需求
     * @return 过期需求信息
     */
    private PplOrderRequiredDTO setExpiredDemand(PplOrderExpiredRecordDO expiredRecordDO,
            List<PplItemJoinOrderVO> joinVOs, boolean isToDeal) {
        PplItemJoinOrderVO joinVO = joinVOs.get(0);

        PplOrderRequiredDTO expiredDemand = new PplOrderRequiredDTO();
        expiredDemand.setVersionId(expiredRecordDO.getVersionId());
        expiredDemand.setPplOrder(expiredRecordDO.getPplOrder());
        expiredDemand.setIndustryDept(joinVO.getIndustryDept());
        expiredDemand.setProduct(joinVO.getProduct());
        expiredDemand.setDemandYearMonth(String.format("%d年%02d月", joinVO.getYear(), joinVO.getMonth()));
        expiredDemand.setCustomerShortName(joinVO.getPplOrderDO().getCustomerShortName());
        expiredDemand.setDemandType(PplDemandTypeEnum.getNameByCode(joinVO.getDemandType()));
        expiredDemand.setAllInstanceType(
                joinVOs.stream().map(PplItemJoinOrderVO::getInstanceType).distinct().collect(Collectors.joining(",")));
        expiredDemand.setAllZoneName(
                joinVOs.stream().map(PplItemJoinOrderVO::getZoneName).distinct().collect(Collectors.joining(",")));
        expiredDemand.setValidTotalCore(joinVOs.stream().mapToInt(PplItemJoinOrderVO::getTotalCore).sum());
        expiredDemand.setValidTotalGpuNum(
                joinVOs.stream().map(e -> e.getItemDO().getTotalGpuNum()).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

        if (!isToDeal) {
            // 已处理 过期需求
            expiredDemand.setExpiredPplOrder(expiredRecordDO.getExpiredPplOrder());
            expiredDemand.setOperateUser(expiredRecordDO.getExpiredOperateUser());
            expiredDemand.setOperateTime(expiredRecordDO.getUpdateTime());
            expiredDemand.setOperateType(
                    PplOrderExpiredOperateTypeEnum.getNameByCode(expiredRecordDO.getExpiredOperateType()));
            expiredDemand.setOperateReason(
                    PplOrderExpiredOperateReasonEnum.getNameByCode(expiredRecordDO.getExpiredOperateReason()));
            expiredDemand.setExpiredYear(expiredRecordDO.getExpiredYear());
            expiredDemand.setExpiredMonth(expiredRecordDO.getExpiredMonth());
        }
        return expiredDemand;
    }

    /**
     * 设置过期需求处理记录
     */
    private void batchUpdateExpiredRecord(String industryDept, List<String> products, List<String> pplOrders,
            String operateType, Map<String, PplOrderExpiredRecordDO> recordByOrderMap) {
        // 查找单据过期记录
        //  目前这个接口传递的单据，都是在同一部门下的，所以下面可以直接从第一条单据中获取部门信息
        Map<String, Long> productOfVersionMap = innerVersionService.queryProcessingVersionMap(industryDept);
        List<Long> operateVersionIds = new ArrayList<>();
        productOfVersionMap.forEach((key, value) -> {
            if (products.contains(key)) {
                operateVersionIds.add(value);
            }
        });
        List<PplOrderExpiredRecordDO> expiredRecordDOs = demandDBHelper.getAll(PplOrderExpiredRecordDO.class,
                "where version_id in(?) and ppl_order in(?)", operateVersionIds, pplOrders);

        if (ListUtils.isEmpty(expiredRecordDOs)) {
            return;
        }

        // 开始更新过期需求的操作记录
        String userName = LoginUtils.getUserNameWithSystem();
        List<PplOrderExpiredRecordDO> updateRecordDOs = new ArrayList<>();
        for (PplOrderExpiredRecordDO recordDO : expiredRecordDOs) {
            PplOrderExpiredRecordDO updateDO = new PplOrderExpiredRecordDO();
            updateDO.setId(recordDO.getId());
            updateDO.setExpiredOperateType(operateType);
            updateDO.setExpiredOperateUser(userName);

            PplOrderExpiredRecordDO tmpRecord = recordByOrderMap.get(recordDO.getPplOrder());
            if (PplOrderExpiredOperateTypeEnum.DEAL_DELAY.getCode().equals(operateType) && tmpRecord != null) {
                updateDO.setExpiredPplOrder(tmpRecord.getExpiredPplOrder());
                updateDO.setExpiredOperateReason(tmpRecord.getExpiredOperateReason());
                updateDO.setExpiredYear(tmpRecord.getExpiredYear());
                updateDO.setExpiredMonth(tmpRecord.getExpiredMonth());
            }

            updateRecordDOs.add(updateDO);
        }
        if (ListUtils.isNotEmpty(updateRecordDOs)) {
            demandDBHelper.update(updateRecordDOs);
        }
    }

    /**
     * 根据用户权限修正用户请求的范围
     *
     * @param req
     */
    void wrapAuth(QueryPplDraftReq req) {
        String userName = LoginUtils.getUserName();
        log.info("wrap ppl auth");
        ArchitectPermission auth = pplCommonService.getArchitectPermission(userName);
        // 如果有权限且 权限不是管理员 则进行权限过滤
        if (StringUtils.isBlank(auth.getRole()) ||
                (auth.getIsAllIndustry() != null && !auth.getIsAllIndustry())) {
            //战区权限  > uin 权限
            if (!CollectionUtils.isEmpty(auth.getWarZone())) {
                log.info("战区权限{} -> {}", userName, auth.getWarZone());
                if (!CollectionUtils.isEmpty(req.getWarZone())) {
                    auth.getWarZone().retainAll(req.getWarZone());
                }
                req.setWarZone(auth.getWarZone());
            } else if (!CollectionUtils.isEmpty(auth.getAuthUin())) {
                log.info("uin权限{} -> {}", userName, auth.getAuthUin());
                if (!CollectionUtils.isEmpty(req.getCustomerUin())) {
                    auth.getAuthUin().retainAll(req.getCustomerUin());
                }
                if (CollectionUtils.isEmpty(auth.getAuthUin())) {
                    // 如果他为空 则取交集失败
                    //设置个查不到的条件。不报错，查不到数据即可
                    req.setCustomerUin(Lists.newArrayList("00000000"));
                } else {
                    req.setCustomerUin(auth.getAuthUin());
                }
            } else {
                //没有权限 设置个查不到的条件。不报错，查不到数据即可
                req.setCustomerUin(Lists.newArrayList("00000000"));
            }
        } else {
            log.info("admin role. skip fix req");
        }
    }

    /**
     * 校验版本提交日期
     */
    private void checkVersionSubmit(String industryDept, List<String> products) {
        // 获取行业各产品当前进行中版本
        Map<String, Long> productOfVersionMap = innerVersionService.queryProcessingVersionMap(industryDept);
        List<Long> queryVersionIds = new ArrayList<>();
        productOfVersionMap.forEach((key, value) -> {
            if (products.contains(key)) {
                queryVersionIds.add(value);
            }
        });
        if (ListUtils.isEmpty(queryVersionIds)) {
            return;
        }

        // 获取各版本的具体审批节点信息
        Map<Long, List<PplInnerProcessVersionSlaDO>> auditNodeMap = demandDBHelper.getAll(
                        PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", queryVersionIds).stream()
                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));

        // 开始校验各版本截止提交日期
        for (Long versionId : queryVersionIds) {
            List<PplInnerProcessVersionSlaDO> slaDOList = auditNodeMap.get(versionId);
            if (ListUtils.isEmpty(slaDOList)) {
                continue;
            }
            PplInnerProcessVersionSlaDO firstAuditNode = slaDOList.stream()
                    .filter(node -> "ENTER".equals(node.getDeadlineType())).findFirst().orElse(null);
            // isPreSubmit: true - 处于需求沟通阶段；false - 处于审批阶段（不再允许录入需求）
            boolean isPreSubmit = firstAuditNode == null || firstAuditNode.getDeadlineTime() == null
                    || firstAuditNode.getDeadlineTime()
                    .after(new Date());
            if (!isPreSubmit) {
                throw new BizException("已到达版本截止录入时间，无法修改数据到需求沟通中");
            }
        }

    }

    @Data
    @ToString
    public static class OrderPreSumVO {

        @Column("ppl_order")
        private String pplOrder;
        @Column("total_instance_num")
        private Integer totalInstanceNum;
        @Column("total_core")
        private Integer totalCore = 0;
        @Column("total_gpu_num")
        private Integer totalGpuNum = 0;
    }
}
