package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * 版本分组概览信息
 */
@Data
public class VersionGroupInfoResp {

    private String versionCode;
    private String industryDept;
    private String product;
    private String versionStatus; // 版本状态
    private Boolean isAllowEdit; // 版本当前状态是否允许编辑
    private Integer demandBeginYear;
    private Integer demandBeginMonth;
    private Integer demandEndYear;
    private Integer demandEndMonth;
    private Date deadline;

    public static VersionGroupInfoResp from(PplVersionGroupInfoVO vo) {
        VersionGroupInfoResp resp = new VersionGroupInfoResp();
        resp.setVersionCode(vo.getVersionCode());
        resp.setIndustryDept(vo.getIndustryDept());
        resp.setProduct(vo.getProduct());
        resp.setVersionStatus(vo.getPplVersionDO() == null ? "" : vo.getPplVersionDO().getStatus());
        resp.setIsAllowEdit(Objects.equals(resp.getVersionStatus(), PplVersionStatusEnum.PROCESS.getCode()));
        resp.setDemandBeginYear(vo.getPplVersionDO() == null ? null : vo.getPplVersionDO().getDemandBeginYear());
        resp.setDemandBeginMonth(vo.getPplVersionDO() == null ? null : vo.getPplVersionDO().getDemandBeginMonth());
        resp.setDemandEndYear(vo.getPplVersionDO() == null ? null : vo.getPplVersionDO().getDemandEndYear());
        resp.setDemandEndMonth(vo.getPplVersionDO() == null ? null : vo.getPplVersionDO().getDemandEndMonth());
        resp.setDeadline(vo.getPplVersionDO() == null ? null : vo.getPplVersionDO().getDeadline());
        return resp;
    }

}
