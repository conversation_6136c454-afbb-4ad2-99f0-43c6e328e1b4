package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.BaseDataWithPplDraftVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.QueryBaseDataReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data.SupplementPplReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplIndustryPackageBaseDataDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import java.util.List;

public interface PplModelForecastForPackageService {

    /**
     * 初始化模型预测生成的包基准数据
     * @param versionId 版本
     * @param product 产品
     * @return
     */
    List<PplIndustryPackageBaseDataDO> initForecastDataForPackage(Long versionId,String product);

    /**
     * 根据包基准数据补充ppl
     * @param req
     */
    void supplementPpl(SupplementPplReq req);


    /**
     *  * 查询包基准数据 for 需求沟通
     * @param req
     * @return
     */
    List<BaseDataWithPplDraftVO> queryPackageBaseDataForDraft(QueryBaseDataReq req);


    /**
     *  * 根据版本id 刷新模型预测生成的包基准数据
     * @param versionId
     */
    void RefreshModelPackageOnStartVersion(Long versionId);


    /**
     *  * 根据版本id 补充当前ppl数据中缺失的包基准
     * @param versionId
     */
    void fillZeroBaseData(Long versionId);


}
