package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

/**
 * 行业包基准数据表
 */
@Data
@ToString
@Table("ppl_industry_package_base_data")
public class PplIndustryPackageBaseDataDO extends BaseDO {

    /** 需求类型，NEW、RETURN<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求类型名称，新增、退回<br/>Column: [demand_type_name] */
    @Column(value = "demand_type_name")
    private String demandTypeName;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 地域编码<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 境内、境外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 通用实例类型<br/>Column: [common_instance_type] */
    @Column(value = "common_instance_type")
    private String commonInstanceType;

    /** 包基准年月, yyyy-MM<br/>Column: [demand_year_month] */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /** 创建人<br/>Column: [create_user] */
    @Column(value = "create_user")
    private String createUser;

    /** 包基准导入版本id<br/>Column: [import_version_id] */
    @Column(value = "import_version_id")
    private Long importVersionId;

    /** 是否为行业审批版本内最新的基准数据<br/>Column: [latest] */
    @Column(value = "latest")
    private Boolean latest;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** 行业审批版本id<br/>Column: [inner_version_id] */
    @Column(value = "inner_version_id")
    private Long innerVersionId;

    /** 包基准核心数 */
    @Column(value = "base_core")
    private BigDecimal baseCore;

    /**
     * 模型预测的原始包基准核心数
     *  未来还会应用于内存数、卡数等
     * */
    @Column(value = "original_base_core")
    private BigDecimal originalBaseCore;

    /** 系统生成可能的实例规格，按照“64C256G”规格进行补充，如果没有该规格，则取最接近64C的规格 */
    @Column(value = "possible_instance_model")
    private String possibleInstanceModel;

}