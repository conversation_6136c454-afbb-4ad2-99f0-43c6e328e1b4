package cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data;

import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.constant.CustomerShortNameConstant;
import cloud.demand.app.modules.p2p.ppl13week.constant.PplIndustryPackageBaseDataConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.PplListVo;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Product2ModelForecastConfigEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp.Detail;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import yunti.boot.exception.BizException;

@Data
public class BaseDataWithPplDraftVO {

    // 包基准数据id
    @Column(value = "id")
    private Long id;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "region")
    private String region;

    @Column(value = "common_instance_type")
    private String commonInstanceType;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "demand_type_name")
    private String demandTypeName;

    @Column(value = "demand_year_month")
    private String demandYearMonth;

    // 包基准核心数（A）
    @Column(value = "base_core")
    private BigDecimal baseCore = BigDecimal.ZERO;

    @Column(value = "possible_instance_model")
    private String possibleInstanceModel;

    // 单实例补充规格数
    private Integer instanceModelTarget;

    // PPL报备核心数（B）
    private BigDecimal pplCore = BigDecimal.ZERO;

    // 理论总核心数（C=max（A，B））
    private BigDecimal theoryTotalCore = BigDecimal.ZERO;

    // 当前核心数 （E）
    private BigDecimal currentCore = BigDecimal.ZERO;

    // 应调整核心数 （F=C-E）
    private BigDecimal shouldAdjustCore = BigDecimal.ZERO;

    private Map<String, List<DraftItemDTO>> pplMap = new HashMap<>();

    private Map<String, PplListVo> orderMap = new HashMap<>();

    private Boolean isAllowSupplement = true;

    private String notAllowRemark;

    // PPL报备指标数（B）
    private BigDecimal pplTarget = BigDecimal.ZERO;

    // 理论总指标数（C=max（A，B））
    private BigDecimal theoryTotalTarget = BigDecimal.ZERO;

    // 当前指标数 （E）
    private BigDecimal currentTarget = BigDecimal.ZERO;

    // 应调整指标数 （F=C-E）
    private BigDecimal shouldAdjustTarget = BigDecimal.ZERO;

    public static void baseDataAddPpl(List<PplListVo> pplOrderList,
            Map<String, String> instanceType2CommonInstanceTypeMap,
            List<BaseDataWithPplDraftVO> baseDataList,
            Map<String, String> regionName2CodeMap,
            Map<String, String> regionName2CustomhouseTitleMap,
            QueryBaseDataReq req)  {
        if (instanceType2CommonInstanceTypeMap == null) {
            instanceType2CommonInstanceTypeMap = new HashMap<>();
        }
        if (regionName2CodeMap == null) {
            regionName2CodeMap = new HashMap<>();
        }
        if (regionName2CustomhouseTitleMap == null) {
            regionName2CustomhouseTitleMap = new HashMap<>();
        }
        if (baseDataList == null) {
            baseDataList = new ArrayList<>();
        }
        if (pplOrderList == null) {
            pplOrderList = new ArrayList<>();
        }

        Function<Object, Integer> targetFunc = Product2ModelForecastConfigEnum.getTargetValeByProduct(
                req.getProduct());

        Map<String, List<DraftItemDTO>> pplMap = new HashMap<>();
        Map<String, PplListVo> orderMap = new HashMap<>();
        for (PplListVo pplListVo : pplOrderList) {
            for (DraftItemDTO pplItem : pplListVo.getPplItems()) {
                YearMonth pp = findYearMonth(pplItem);
                pplItem.setDemandYearMonth(pp);
                pplItem.setCustomhouseTitle(regionName2CustomhouseTitleMap.get(pplItem.getRegionName()));
                pplItem.setRegion(regionName2CodeMap.get(pplItem.getRegionName()));

                // 设置包管理key by不同产品
                pplItem.setPackageGroupKey(fillPackageGroupKey(pplItem,req.getProduct(),instanceType2CommonInstanceTypeMap));
                boolean match = req.matchCondition(pplItem);
                if (!match) {
                    continue;
                }
                String key = dimKey(pplItem);
                pplMap.computeIfAbsent(key, k -> new ArrayList<>()).add(pplItem);
                orderMap.put(pplListVo.getPplOrder(), pplListVo);
            }
        }
        for (BaseDataWithPplDraftVO item : baseDataList) {
            String key = dimKey(item);
            List<DraftItemDTO> pplItemList = pplMap.remove(key);
            if (ListUtils.isNotEmpty(pplItemList)) {
                for (DraftItemDTO pplItem : pplItemList) {
                    PplListVo orderVo = orderMap.get(pplItem.getPplId().split("-")[0]);
                    item.addPpl(pplItem, orderVo,targetFunc);
                }
            }
        }

        if (ListUtils.isEmpty(req.getBaseDataIds())) {
            // 没有基准，但是有PPL的那部分，进行补充
            supplementFromPpl(pplMap, baseDataList, orderMap,targetFunc);
        }

        for (BaseDataWithPplDraftVO item : baseDataList) {
            item.coreHandler(req.getProduct());
        }

        // 按维度统一排序
        ListUtils.sortAscNullLast(baseDataList, BaseDataWithPplDraftVO::dimKey);
    }

    private static void supplementFromPpl(Map<String, List<DraftItemDTO>> pplMap,
            List<BaseDataWithPplDraftVO> baseDataList,
            Map<String, PplListVo> orderMap,Function<Object, Integer> targetFunc) {
        if (ListUtils.isEmpty(pplMap)) {
            return;
        }
        for (List<DraftItemDTO> pplList : pplMap.values()) {
            if (ListUtils.isEmpty(pplList)) {
                continue;
            }
            DraftItemDTO first = pplList.get(0);
            BaseDataWithPplDraftVO item = new BaseDataWithPplDraftVO();
            item.setRegion(first.getRegion());
            item.setRegionName(first.getRegionName());
            item.setCustomhouseTitle(first.getCustomhouseTitle());
            PplDemandTypeEnum demandTypeEnum = findDemandType(first);
            item.setDemandType(first.getDemandType());
            if (demandTypeEnum != null) {
                item.setDemandType(demandTypeEnum.getCode());
                item.setDemandTypeName(demandTypeEnum.getName2());
            }
            item.setCommonInstanceType(first.getPackageGroupKey());
            item.setDemandYearMonth(first.getDemandYearMonth() == null ? null : first.getDemandYearMonth().toString());
            for (DraftItemDTO pplItem : pplList) {
                PplListVo listVo = orderMap.get(pplItem.getPplId().split("-")[0]);
                item.addPpl(pplItem, listVo,targetFunc);
            }
            baseDataList.add(item);
        }
    }

    private void addPpl(DraftItemDTO itemVO, PplListVo orderVo,Function<Object, Integer> targetFunc) {

        Integer target = targetFunc.apply(itemVO);

        if (target == null) {
            return;
        }
        if (IndustryDeptEnum.INNER_DEPT.getName().equals(orderVo.getIndustryDept())) {
            // 1.项目名称 不等于 模型预测-EMR / 模型预测EKS 或
            // 2.已经转单了的单据
            if ((!PplIndustryPackageBaseDataConstant.MODEL_FORECAST_PROJECT_LIST.contains(
                    Optional.ofNullable(orderVo.getProjectName()).orElse("")))
                    || PplItemStatusEnum.APPLIED.getCode().equals(itemVO.getStatus())){
                // 如果项目名称不等于 模型预测-EMR / 模型预测EKS 或者 已经转单了的单据， 不进行处理。
                this.pplTarget = (NumberUtils.sum(ListUtils.newArrayList(target, this.pplTarget)));
            }
        }else {
            // 1.客户名称 不等于 行业包预测 或
            // 2.已经转单了的单据
            if (!Objects.equals(PplIndustryPackageBaseDataConstant.SUPPLEMENT_CUSTOMER, orderVo.getCustomerShortName())
                    || PplItemStatusEnum.APPLIED.getCode().equals(itemVO.getStatus())) {
                this.pplTarget = (NumberUtils.sum(ListUtils.newArrayList(target, this.pplTarget)));
            }
        }

        this.currentTarget = NumberUtils.sum(ListUtils.newArrayList(target, this.currentTarget));
        this.orderMap.put(orderVo.getPplOrder(), orderVo);
        this.pplMap.computeIfAbsent(orderVo.getPplOrder(), k -> new ArrayList<>()).add(itemVO);
    }

    private void coreHandler(String product) {
        this.theoryTotalTarget = this.pplTarget.max(this.baseCore);
        this.shouldAdjustTarget = this.theoryTotalTarget.subtract(this.currentTarget);

        if (StringUtils.isNotBlank(this.possibleInstanceModel)){
            this.instanceModelTarget = getInstanceTarget(product);
            if (shouldAdjustTarget.abs().compareTo(BigDecimal.valueOf(this.instanceModelTarget)) <0) {
                // 如果需要调整的指标数 比补充规格指标数还小， 那么不允许补充。
                this.isAllowSupplement = false;
                String msg = StrUtil.format("补充规格最小指标数【{}】大于需调整指标数【{}】，无法补充。",
                        instanceModelTarget, shouldAdjustTarget.abs());
                this.notAllowRemark = msg;
            }
        }

        if (this.shouldAdjustTarget.compareTo(BigDecimal.ZERO) == 0) {
            this.isAllowSupplement = false;
            this.notAllowRemark = "当前需调整核心数为0，不允许补充。";
        }

        this.pplCore = this.pplTarget;
        this.theoryTotalCore = this.theoryTotalTarget;
        this.currentCore = this.currentTarget;
        this.shouldAdjustCore = this.shouldAdjustTarget;
    }

    private Integer getInstanceTarget(String product) {
        switch (Product2ModelForecastConfigEnum.getByName(product)) {
            case CVM:
            case EMR:
            case EKS:
                Tuple2<Integer, Integer> tuple = P2PInstanceModelParse.parseInstanceModel(this.possibleInstanceModel);
                return tuple._1;

            case CDB:
                return parseMemoryByDBInstanceType(this.commonInstanceType);
            default:
                throw new BizException("不支持的产品类型: " + product);
        }
    }

    private static String dimKey(DraftItemDTO pplItem) {
        PplDemandTypeEnum demandTypeEnum = findDemandType(pplItem);
        String demandType = demandTypeEnum == null ? null : demandTypeEnum.getCode();
        return StrUtil.format("{}_{}_{}_{}", pplItem.getRegionName(), demandType,
                pplItem.getPackageGroupKey(), pplItem.getDemandYearMonth());
    }

    private static String fillPackageGroupKey(DraftItemDTO ppl,String product,Map<String, String> instanceType2CommonInstanceTypeMap) {
        if (ppl == null) {
            return null;
        }
        if(Product2ModelForecastConfigEnum.CDB.getName().equals(product)){
            return findDbSpecs(ppl);
        }else {
            return findCommInstanceType(ppl, instanceType2CommonInstanceTypeMap);
        }

    }

    private static String findDbSpecs(DraftItemDTO ppl) {
        if(StringUtils.isBlank(ppl.getDatabaseSpecs())){
            return null;
        }
        int memory = Product2ModelForecastConfigEnum.parseDbSpecsMemory(ppl.getDatabaseSpecs());
        if (memory > 96){
            return PplIndustryPackageBaseDataConstant.CDB_BIG_INSTANCE;
        }else {
            return PplIndustryPackageBaseDataConstant.CDB_SMALL_INSTANCE;
        }

    }


    /**
     *
     * @param instanceType
     * PplIndustryPackageBaseDataConstant.CDB_BIG_INSTANCE / PplIndustryPackageBaseDataConstant.CDB_SMALL_INSTANCE
     * @return
     */
    private static Integer parseMemoryByDBInstanceType(String instanceType){
        switch (instanceType) {
            case PplIndustryPackageBaseDataConstant.CDB_BIG_INSTANCE:
                return 128;
            case PplIndustryPackageBaseDataConstant.CDB_SMALL_INSTANCE:
                return 64;
            default:
                throw new BizException("不支持的数据库实例类型：" + instanceType);
        }
    }

    private static String findCommInstanceType(DraftItemDTO ppl,
            Map<String, String> instanceType2CommonInstanceTypeMap) {
        if (ppl == null) {
            return null;
        }
        String commInstanceType = instanceType2CommonInstanceTypeMap.get(ppl.getInstanceType());
        if (StringUtils.isBlank(commInstanceType)) {
            commInstanceType = ppl.getInstanceType();
        }
        return commInstanceType;
    }

    private static YearMonth findYearMonth(DraftItemDTO pplItem) {
        if (pplItem == null) {
            return null;
        }
        YearMonth pp = null;
        if (pplItem.getBeginBuyDate() != null) {
            pp = YearMonth.from(LocalDate.parse(pplItem.getBeginBuyDate()));
        }
        return pp;
    }

    /**
     *  新增、弹性 -> 新增 <br/>
     *  退回 -> 退回 <br/>
     */
    public static PplDemandTypeEnum findDemandType(DraftItemDTO pplItem) {
        if (pplItem == null) {
            return null;
        }
        if (PplDemandTypeEnum.NEW.getCode().equals(pplItem.getDemandType())
                || PplDemandTypeEnum.ELASTIC.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.NEW;
        } else if (PplDemandTypeEnum.RETURN.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.RETURN;
        }
        return null;
    }

    private static String dimKey(BaseDataWithPplDraftVO item) {
        return StrUtil.format("{}_{}_{}_{}",
                item.getRegionName(), item.getDemandType(), item.getCommonInstanceType(), item.getDemandYearMonth());
    }

}
