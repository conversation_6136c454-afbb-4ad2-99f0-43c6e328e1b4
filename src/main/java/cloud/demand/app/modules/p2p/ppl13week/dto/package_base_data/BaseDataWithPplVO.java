package cloud.demand.app.modules.p2p.ppl13week.dto.package_base_data;

import cloud.demand.app.modules.p2p.ppl13week.constant.PplIndustryPackageBaseDataConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderVo;
import cn.hutool.core.util.StrUtil;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class BaseDataWithPplVO {

    // 包基准数据id
    @Column(value = "id")
    private Long id;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "region")
    private String region;

    @Column(value = "common_instance_type")
    private String commonInstanceType;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "demand_type_name")
    private String demandTypeName;

    @Column(value = "demand_year_month")
    private String demandYearMonth;

    // 包基准核心数（A）
    @Column(value = "base_core")
    private BigDecimal baseCore = BigDecimal.ZERO;

    @Column(value = "possible_instance_model")
    private String possibleInstanceModel;

    // PPL报备核心数（B）
    private BigDecimal pplCore = BigDecimal.ZERO;

    // 理论总核心数（C=max（A，B））
    private BigDecimal theoryTotalCore = BigDecimal.ZERO;

    // 当前核心数 （E）
    private BigDecimal currentCore = BigDecimal.ZERO;

    // 应调整核心数 （F=C-E）
    private BigDecimal shouldAdjustCore = BigDecimal.ZERO;

    private Map<String, List<PplOrderAuditRecordItemVO>> pplMap = new HashMap<>();

    private Map<String, PplOrderVo> orderMap = new HashMap<>();

    public static void baseDataAddPpl(List<PplOrderVo> pplOrderList,
            Map<String, String> instanceType2CommonInstanceTypeMap,
            List<BaseDataWithPplVO> baseDataList,
            Map<String, String> regionName2CodeMap,
            Map<String, String> regionName2CustomhouseTitleMap,
            QueryBaseDataReq req)  {
        if (instanceType2CommonInstanceTypeMap == null) {
            instanceType2CommonInstanceTypeMap = new HashMap<>();
        }
        if (regionName2CodeMap == null) {
            regionName2CodeMap = new HashMap<>();
        }
        if (regionName2CustomhouseTitleMap == null) {
            regionName2CustomhouseTitleMap = new HashMap<>();
        }
        if (baseDataList == null) {
            baseDataList = new ArrayList<>();
        }
        if (pplOrderList == null) {
            pplOrderList = new ArrayList<>();
        }

        Map<String, List<PplOrderAuditRecordItemVO>> pplMap = new HashMap<>();
        Map<String, PplOrderVo> orderMap = new HashMap<>();
        for (PplOrderVo pplOrderVo : pplOrderList) {
            for (PplOrderAuditRecordItemVO pplItem : pplOrderVo.getItemVOList()) {
                YearMonth pp = findYearMonth(pplItem);
                pplItem.setDemandYearMonth(pp);
                String commInstanceType = findCommInstanceType(pplItem, instanceType2CommonInstanceTypeMap);
                pplItem.setCustomhouseTitle(regionName2CustomhouseTitleMap.get(pplItem.getRegionName()));
                pplItem.setRegion(regionName2CodeMap.get(pplItem.getRegionName()));
                pplItem.setCommonInstanceType(commInstanceType);
                boolean match = req.matchCondition(pplItem);
                if (!match) {
                    continue;
                }
                String key = dimKey(pplItem);
                pplMap.computeIfAbsent(key, k -> new ArrayList<>()).add(pplItem);
                orderMap.put(pplOrderVo.getPplOrder(), pplOrderVo);
            }
        }
        for (BaseDataWithPplVO item : baseDataList) {
            String key = dimKey(item);
            item.coreHandler();
            List<PplOrderAuditRecordItemVO> pplItemList = pplMap.remove(key);
            if (ListUtils.isNotEmpty(pplItemList)) {
                for (PplOrderAuditRecordItemVO pplItem : pplItemList) {
                    PplOrderVo orderVo = orderMap.get(pplItem.getPplOrder());
                    item.addPpl(pplItem, orderVo);
                }
            }
        }

        if (ListUtils.isEmpty(req.getBaseDataIds())) {
            // 没有基准，但是有PPL的那部分，进行补充
            supplementFromPpl(pplMap, baseDataList, orderMap);
        }

        // 按维度统一排序
        ListUtils.sortAscNullLast(baseDataList, BaseDataWithPplVO::dimKey);
    }

    private static void supplementFromPpl(Map<String, List<PplOrderAuditRecordItemVO>> pplMap,
            List<BaseDataWithPplVO> baseDataList,
            Map<String, PplOrderVo> orderMap) {
        if (ListUtils.isEmpty(pplMap)) {
            return;
        }
        for (List<PplOrderAuditRecordItemVO> pplList : pplMap.values()) {
            if (ListUtils.isEmpty(pplList)) {
                continue;
            }
            PplOrderAuditRecordItemVO first = pplList.get(0);
            BaseDataWithPplVO item = new BaseDataWithPplVO();
            item.setRegion(first.getRegion());
            item.setRegionName(first.getRegionName());
            item.setCustomhouseTitle(first.getCustomhouseTitle());
            PplDemandTypeEnum demandTypeEnum = findDemandType(first);
            item.setDemandType(first.getDemandType());
            if (demandTypeEnum != null) {
                item.setDemandType(demandTypeEnum.getCode());
                item.setDemandTypeName(demandTypeEnum.getName2());
            }
            item.setCommonInstanceType(first.getCommonInstanceType());
            item.setDemandYearMonth(first.getDemandYearMonth() == null ? null : first.getDemandYearMonth().toString());
            for (PplOrderAuditRecordItemVO pplItem : pplList) {
                PplOrderVo orderVo = orderMap.get(pplItem.getPplOrder());
                item.addPpl(pplItem, orderVo);
            }
            baseDataList.add(item);
        }
    }

    private void addPpl(PplOrderAuditRecordItemVO itemVO, PplOrderVo orderVo) {
        if (itemVO.getTotalCore() == null) {
            return;
        }
        if (!Objects.equals(PplIndustryPackageBaseDataConstant.SUPPLEMENT_CUSTOMER, orderVo.getCustomerShortName())) {
            this.pplCore = (NumberUtils.sum(ListUtils.newArrayList(itemVO.getTotalCore(), this.pplCore)));
        }
        this.currentCore = NumberUtils.sum(ListUtils.newArrayList(itemVO.getTotalCore(), this.currentCore));
        coreHandler();
        this.orderMap.put(orderVo.getPplOrder(), orderVo);
        this.pplMap.computeIfAbsent(itemVO.getPplOrder(), k -> new ArrayList<>()).add(itemVO);
    }

    private void coreHandler() {
        this.theoryTotalCore = this.pplCore.max(this.baseCore);
        this.shouldAdjustCore = this.theoryTotalCore.subtract(this.currentCore);
    }

    private static String dimKey(PplOrderAuditRecordItemVO pplItem) {
        PplDemandTypeEnum demandTypeEnum = findDemandType(pplItem);
        String demandType = demandTypeEnum == null ? null : demandTypeEnum.getCode();
        return StrUtil.format("{}_{}_{}_{}", pplItem.getRegionName(), demandType,
                pplItem.getCommonInstanceType(), pplItem.getDemandYearMonth());
    }

    private static String findCommInstanceType(PplItemBaseDO ppl,
            Map<String, String> instanceType2CommonInstanceTypeMap) {
        if (ppl == null) {
            return null;
        }
        String commInstanceType = instanceType2CommonInstanceTypeMap.get(ppl.getInstanceType());
        if (StringUtils.isBlank(commInstanceType)) {
            commInstanceType = ppl.getInstanceType();
        }
        return commInstanceType;
    }

    private static YearMonth findYearMonth(PplItemBaseDO pplItem) {
        if (pplItem == null) {
            return null;
        }
        YearMonth pp = null;
        if (pplItem.getBeginBuyDate() != null) {
            pp = YearMonth.from(pplItem.getBeginBuyDate());
        }
        return pp;
    }

    /**
     *  新增、弹性 -> 新增 <br/>
     *  退回 -> 退回 <br/>
     */
    public static PplDemandTypeEnum findDemandType(PplItemBaseDO pplItem) {
        if (pplItem == null) {
            return null;
        }
        if (PplDemandTypeEnum.NEW.getCode().equals(pplItem.getDemandType())
                || PplDemandTypeEnum.ELASTIC.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.NEW;
        } else if (PplDemandTypeEnum.RETURN.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.RETURN;
        }
        return null;
    }

    /**
     *  新增、弹性 -> 新增 <br/>
     *  退回 -> 退回 <br/>
     */
    public static PplDemandTypeEnum findDemandType(DraftItemDTO pplItem) {
        if (pplItem == null) {
            return null;
        }
        if (PplDemandTypeEnum.NEW.getCode().equals(pplItem.getDemandType())
                || PplDemandTypeEnum.ELASTIC.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.NEW;
        } else if (PplDemandTypeEnum.RETURN.getCode().equals(pplItem.getDemandType())) {
            return PplDemandTypeEnum.RETURN;
        }
        return null;
    }

    private static String dimKey(BaseDataWithPplVO item) {
        return StrUtil.format("{}_{}_{}_{}",
                item.getRegionName(), item.getDemandType(), item.getCommonInstanceType(), item.getDemandYearMonth());
    }

}
