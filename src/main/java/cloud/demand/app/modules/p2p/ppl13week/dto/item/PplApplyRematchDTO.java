package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import lombok.Data;

@Data
public class PplApplyRematchDTO {

    // 重匹配的 PN明细单号
    private String reMatchPnItemPpl;

    // 删除的 PE单号
    private String deletedPeOrderPpl;

    // 删除的 PE明细单号
    private String deletedPeItemPpl;

    // 删除的 PE草稿单据id
    Long deletedPeOrderId;

    // 删除的 PE草稿明细id
    Long deletedPeItemId;

    // 更新的 草稿明细
    PplItemDraftDO updateDraftItem;

    // 更新的 生效明细
    PplItemDO updateItem;

    // 更新的 预约明细
    PplItemAppliedDO updateAppliedItem;

}
