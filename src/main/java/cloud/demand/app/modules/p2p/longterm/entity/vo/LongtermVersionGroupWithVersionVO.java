package cloud.demand.app.modules.p2p.longterm.entity.vo;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import lombok.Data;

@Data
public class LongtermVersionGroupWithVersionVO extends LongtermVersionGroupDO {

    @RelatedColumn(localColumn = "version_code", remoteColumn = "version_code")
    private LongtermVersionDO versionDO;

}
