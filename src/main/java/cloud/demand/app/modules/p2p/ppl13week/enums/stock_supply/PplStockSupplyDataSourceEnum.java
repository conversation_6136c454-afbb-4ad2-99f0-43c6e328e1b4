package cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply;

import java.util.Objects;
import lombok.Getter;

/**
 * ppl对冲数据来源
 */
@Getter
public enum PplStockSupplyDataSourceEnum {

    STOCK_SUPPLY("STOCK_SUPPLY", "下发库存对冲"),


    ORDER_BUY("ORDER_BUY", "订单已锁定采购"),

    ;

    final private String code;
    final private String name;

    PplStockSupplyDataSourceEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplStockSupplyDataSourceEnum getByCode(String code) {
        for (PplStockSupplyDataSourceEnum e : PplStockSupplyDataSourceEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplStockSupplyDataSourceEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}