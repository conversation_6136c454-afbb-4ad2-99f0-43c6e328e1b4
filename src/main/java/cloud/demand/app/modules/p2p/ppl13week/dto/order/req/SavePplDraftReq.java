package cloud.demand.app.modules.p2p.ppl13week.dto.order.req;

import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.SupplyPlanDetailVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplOrderItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderAuditStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.val;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

@Data
public class SavePplDraftReq {

    /**
     * 变更类型 insert｜update @see OperateTypeEnum
     */
    @NotNull
    private String type;
    /**
     * PPL单号
     */
    private String pplOrder;
    /**
     * 产品
     */
    @NotNull
    private String product;
    /**
     * 客户类型,存量客户
     */
    @NotNull
    private String customerType;
    /**
     * 客户uin
     */
    private String customerUin;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 客户简称
     */
    @NotNull
    private String customerShortName;
    /**
     * 客户来源
     */
    private String customerSource;
    /**
     * 开始购买时间
     */
    @NotNull
    private String beginBuyDate;
    /**
     * 结束购买时间
     */
    @NotNull
    private String endBuyDate;
    /**
     * 战区
     */
    @NotNull
    private String warZone;
    /**
     * 中心
     */
    private String center;
    /**
     * 行业
     */
    private String industry;
    /**
     * 行业部门
     */
    private String industryDept;
    /**
     * 需求类型
     */
    private String demandType;
    /**
     * 需求场景
     */
    private String demandScene;
    /**
     * 项目
     */
    private String projectName;

    private String submitUser;

    @NotEmpty
    List<DraftItemDTO> resources;

    /**
     * 仅审批过程中新增PPL单才会用上 nodeCode versionId
     */
    private String nodeCode;

    private Long versionId;

    /**
     * 状态 DRAFT / PRE_SUBMIT
     */
    private String draftStatus;

    /**
     * 是否需要记录ppl变化追踪
     */
    private Boolean isRecord = Boolean.TRUE;

    private String modifyReason;

    public void check() {
        if (ListUtils.isNotEmpty(resources)) {
            BigDecimal importantDemandRate = new BigDecimal(100);
            // EKS、ES、云数仓才能使用 大数据云盘、高IO型云硬盘
            List<String> productsForDataDiskCheck = ListUtils.newArrayList(Ppl13weekProductTypeEnum.EKS.getName(),
                    Ppl13weekProductTypeEnum.ES.getName(), Ppl13weekProductTypeEnum.CDW.getName());
            boolean isDatabase = Ppl13weekProductTypeEnum.DATABASE.getName().equals(product);
            List<String> errors = new ArrayList<>();
            Set<String> databaseSet = new HashSet<>();
            for (DraftItemDTO resource : resources) {
                if (resource == null) {
                    continue;
                }
                if (resource.getImportantDemand() != null
                        && Ppl13weekProductTypeEnum.GPU.getName().equals(product)
                        && resource.getImportantDemand()) {
                    if (resource.getWinRate() == null || importantDemandRate.compareTo(resource.getWinRate()) != 0) {
                        throw new BizException("GPU重保需求的赢率必须为100%");
                    }
                }
                // EMR、ES、云数仓才能使用 大数据云盘、高IO型云硬盘
                Tuple2<Boolean, String> res = PplDiskTypeEnum.checkDataDiskForPpl(resource.getDataDiskType(), product);
                if (!res._1) {
                    throw new BizException(res._2);
                }
                if (isDatabase) {
                    resource.paramsCheckDataBase(errors);
                    databaseSet.add(resource.getDatabaseName());
                }
            }
            if (databaseSet.size() > 1) {
                errors.add("单个pplOrder仅支持录入单个数据库产品");
            }
            if (errors.size() > 0) {
                throw new BizException(String.join("; ", errors));
            }
        }

    }


    /**
     * 转成 入库 模型
     *
     * @return
     */
    public PplOrderDraftDO transTo() {
        PplOrderDraftDO orderDraftDO = new PplOrderDraftDO();
        val date = LocalDate.parse(this.getBeginBuyDate());
        orderDraftDO.setPplOrder(this.getPplOrder());
        orderDraftDO.setCustomerName(customerName);
        orderDraftDO.setCustomerShortName(customerShortName);
        orderDraftDO.setCustomerUin(customerUin);
        orderDraftDO.setCustomerType(customerType);
        orderDraftDO.setCustomerSource(customerSource);
        orderDraftDO.setWarZone(warZone);
        orderDraftDO.setCenter(center);
        orderDraftDO.setIndustry(industry);
        orderDraftDO.setIndustryDept(industryDept);
        orderDraftDO.setBeginDate(getBeginBuyDate());
        orderDraftDO.setEndDate(getEndBuyDate());

        int core = resources.stream()
                .mapToInt(dto -> dto.getTotalCoreNum() != null ? dto.getTotalCoreNum() : 0)
                .sum();

        int disk = resources.stream()
                .mapToInt(dto -> dto.getTotalDiskNum() != null ? dto.getTotalDiskNum() : 0)
                .sum();

        orderDraftDO.setProduct(product);

//        orderDraftDO.setDemandType(demandType);
//        orderDraftDO.setDemandScene(demandScene);
//        orderDraftDO.setProjectName(projectName);
        //todo 目前创建接口不传递 只能从明细里面获取
        orderDraftDO.setDemandScene(resources.get(0).getDemandScene());
        orderDraftDO.setDemandType(resources.get(0).getDemandType());
        orderDraftDO.setProjectName(resources.get(0).getProjectName());

        orderDraftDO.setAllCore(core);
        orderDraftDO.setAllDisk(disk);
        orderDraftDO.setSource(type);
        orderDraftDO.setModifyReason(modifyReason);
        return orderDraftDO;
    }


    public PplOrderDO transToPplOrder() {
        PplOrderDO pplOrderDO = new PplOrderDO();
        pplOrderDO.setPplOrder(getPplOrder());
        pplOrderDO.setSource(PplOrderSourceTypeEnum.IMPORT.getCode());
        pplOrderDO.setIndustry(getIndustry());
        pplOrderDO.setIndustryDept(getIndustryDept());

        pplOrderDO.setCustomerType(getCustomerType());
        pplOrderDO.setCustomerName(getCustomerName());
        pplOrderDO.setCustomerShortName(getCustomerShortName());
        pplOrderDO.setCustomerUin(getCustomerUin());

        // 如果customer short name没有值，那么用CustomerName，其次用CustomerUin
        if (StringTools.isBlank(pplOrderDO.getCustomerShortName())) {
            if (StringTools.isNotBlank(pplOrderDO.getCustomerName())) {
                pplOrderDO.setCustomerShortName(pplOrderDO.getCustomerName());
            } else if (StringTools.isNotBlank(pplOrderDO.getCustomerUin())) {
                pplOrderDO.setCustomerShortName(pplOrderDO.getCustomerUin());
            }
        }
        // 如果customerName没有值，那么用customerUin
        if (StringTools.isBlank(pplOrderDO.getCustomerName())) {
            if (StringTools.isNotBlank(pplOrderDO.getCustomerUin())) {
                pplOrderDO.setCustomerName(pplOrderDO.getCustomerUin());
            }
        }
        pplOrderDO.setStatus(PplOrderStatusEnum.VALID.getCode());
        pplOrderDO.setAuditStatus(PplOrderAuditStatusEnum.WAIT.getCode());
        pplOrderDO.setSubmitUser(getSubmitUser());
        pplOrderDO.setWarZone(getWarZone());
        pplOrderDO.setCenter(getCenter());
        return pplOrderDO;
    }


    @EqualsAndHashCode(callSuper = true)
    @Data
    public static class DraftItemDTO extends PplOrderItemDTO {

        private YearMonth demandYearMonth;

        /**
         * 包管理Key，
         * CVM类产品是 通用实例类型 SA5
         * 数据库产品是 规格   16C64G
         */
        private String packageGroupKey;

        private String region;

        /**
         * 修改类型 'insert' | 'update'
         *
         * @see OperateTypeEnum
         */
        String type;

        /**
         * 是否是计划外的ppl单
         */
        private Boolean isUnexpectedPpl;
        /**
         * 是否是过期的ppl单
         */
        private Boolean isExpiredPpl;

        /**
         * 地域类型，境内外
         */
        private String customhouseTitle;


        /**
         * 共识需求新追加
         */
        private String consensusStatus;

        private String consensusZoneName;

        /**
         * 共识实例规格
         */
        private String consensusInstanceModel;

        /**
         * 共识实例类型
         */
        private String consensusInstanceType;

        /**
         * 共识需求日期 yyyy-MM-dd
         */
        private String consensusDemandDate;

        /**
         * 共识的满足方式,多个满足方式用英文分号隔开
         *
         * @see cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum
         */
        private String consensusMatchType;

        /**
         * 共识的满足方式名称,多个满足方式用英文分号隔开
         */
        private String consensusMatchTypeName;

        private String consensusStatusName;

        private List<SupplyPlanDetailVO> supplyPlans = new ArrayList<>();

        private Boolean isStrongDesignateZone = false;

    }

    public static SavePplDraftReq pplItemTransToDraftReq(PplOrderDO pplOrderDO, List<PplItemDO> pplItemDOS) {
        SavePplDraftReq savePplDraftReq = new SavePplDraftReq();
        BeanUtils.copyProperties(pplOrderDO, savePplDraftReq);
        savePplDraftReq.setType(OperateTypeEnum.ORIGINAL.getCode());
        if (!CollectionUtils.isEmpty(pplItemDOS)) {
            savePplDraftReq.setBeginBuyDate(pplItemDOS.get(0).getBeginBuyDate().toString());
            savePplDraftReq.setEndBuyDate(pplItemDOS.get(0).getEndBuyDate().toString());
            savePplDraftReq.setProduct(pplItemDOS.get(0).getProduct());
        }
        savePplDraftReq.setSubmitUser(pplOrderDO.getSubmitUser());
        List<SavePplDraftReq.DraftItemDTO> resources = new ArrayList<>();
        for (PplItemDO itemDO : pplItemDOS) {
            SavePplDraftReq.DraftItemDTO draftItemDTO = new SavePplDraftReq.DraftItemDTO();
            BeanUtils.copyProperties(itemDO, draftItemDTO);
            draftItemDTO.setBeginBuyDate(itemDO.getBeginBuyDate().toString());
            if (itemDO.getEndBuyDate() != null) {
                draftItemDTO.setEndBuyDate(itemDO.getEndBuyDate().toString());
            }
            if (itemDO.getBeginElasticDate() != null) {
                draftItemDTO.setBeginElasticDate(itemDO.getBeginElasticDate().toString());
            }
            if (itemDO.getEndElasticDate() != null) {
                draftItemDTO.setEndElasticDate(itemDO.getEndElasticDate().toString());
            }
            draftItemDTO.setPplId(itemDO.getPplId());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            draftItemDTO.setTotalDiskNum(itemDO.getTotalDisk());
            draftItemDTO.setType(savePplDraftReq.getType());
            draftItemDTO.setBizId(itemDO.getBizId());
            draftItemDTO.setInstanceNum(itemDO.getInstanceNum());
            draftItemDTO.setTotalCoreNum(itemDO.getTotalCore());
            if (Strings.isBlank(draftItemDTO.getConsensusStatus())) {
                draftItemDTO.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
                draftItemDTO.setConsensusZoneName(itemDO.getZoneName());
                draftItemDTO.setConsensusInstanceModel(itemDO.getInstanceModel());
            }
            resources.add(draftItemDTO);
        }
        savePplDraftReq.setResources(resources);
        return savePplDraftReq;
    }

}
