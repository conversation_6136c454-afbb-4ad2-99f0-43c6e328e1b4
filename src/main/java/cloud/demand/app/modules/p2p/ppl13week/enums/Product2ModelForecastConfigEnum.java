package cloud.demand.app.modules.p2p.ppl13week.enums;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.constant.CustomerShortNameConstant;
import cloud.demand.app.modules.p2p.ppl13week.constant.PplIndustryPackageBaseDataConstant;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp.Detail;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import yunti.boot.exception.BizException;

/**
 * 八大产品
 * 产品名称转换为模型预测客户简称
 */
@Getter
public enum Product2ModelForecastConfigEnum {

    // for 行业用户使用 仅开放CVM
    CVM(Ppl13weekProductTypeEnum.CVM.getCode(),
            Ppl13weekProductTypeEnum.CVM.getName(),
            "",
            PplIndustryPackageBaseDataConstant.SUPPLEMENT_WAR_ZONE,
            PplIndustryPackageBaseDataConstant.SUPPLEMENT_WAR_ZONE,
            PplIndustryPackageBaseDataConstant.SUPPLEMENT_WAR_ZONE),



    EMR(Ppl13weekProductTypeEnum.EMR.getCode(),
            Ppl13weekProductTypeEnum.EMR.getName(),
            "100008965662",
            PplIndustryPackageBaseDataConstant.MODEL_FORECAST_EMR_PROJECT,
            CustomerShortNameConstant.INNER_CUSTOMER_NAME,
            "弹性MapReduce（内部需求）"),


    EKS(Ppl13weekProductTypeEnum.EKS.getCode(),
            Ppl13weekProductTypeEnum.EKS.getName(),
            "3321337994",
            PplIndustryPackageBaseDataConstant.MODEL_FORECAST_EKS_PROJECT
            , CustomerShortNameConstant.INNER_CUSTOMER_NAME,
            "EKS官网（内部需求）"),

    CDB(ProductTypeEnum.CDB
            .getCode(),
            ProductTypeEnum.CDB.getName(),
            "",
            PplIndustryPackageBaseDataConstant.MODEL_FORECAST_CDB_PROJECT
            , CustomerShortNameConstant.INNER_CUSTOMER_NAME,
            "数据库（内部需求）"),


    ;
    final private String code;
    final private String name;
    final private String uin;
    final private String projectName;
    final private String customerShortName;
    final private String warZone;


    Product2ModelForecastConfigEnum(String code,String name, String uin, String projectName, String customerShortName,String warZone)  {
        this.code = code;
        this.name = name;
        this.uin = uin;
        this.projectName = projectName;
        this.customerShortName = customerShortName;
        this.warZone = warZone;
    }

    public static Product2ModelForecastConfigEnum getByCode(String code) {
        for (Product2ModelForecastConfigEnum e : Product2ModelForecastConfigEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static Product2ModelForecastConfigEnum getByName(String name) {
        for (Product2ModelForecastConfigEnum e : Product2ModelForecastConfigEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }


    // ------------------------------       不同产品工具方法    --------------------------------------------
    public static <T> Function<T, String> getGroupingKeyFunction(Function<T, String>... fieldGetters) {
        return item -> {
            StringBuilder keyBuilder = new StringBuilder();
            for (Function<T, String> getter : fieldGetters) {
                keyBuilder.append(getter.apply(item)).append("@");
            }
            // 去掉最后一个"@"
            return keyBuilder.length() > 0 ? keyBuilder.substring(0, keyBuilder.length() - 1) : "";
        };
    }

    /**
     * 根据产品类型返回不同的分组函数
     * @param product
     * @param instanceTypeToCommonInstanceType
     * @return
     * @param <T>
     */
    public static <T> Function<T, String> getGroupKeyByProduct(String product,
            Map<String, String> instanceTypeToCommonInstanceType) {
        // 根据产品类型返回不同的分组函数
        switch (Product2ModelForecastConfigEnum.getByName(product)) {
            case CVM:
            case EMR:
            case EKS:
                // 根据 需求类型 + 地域 + 通用实例类型 + 年月 进行分组
                return getGroupingKeyFunction(
                        item -> ((Detail) item).getType(),
                        item -> ((Detail) item).getRegionName(),
                        item -> instanceTypeToCommonInstanceType.getOrDefault(((Detail) item).getInstanceType(),
                                ((Detail) item).getInstanceType()),
                        item -> ((Detail) item).getYear().toString(),
                        item -> ((Detail) item).getMonth().toString()
                );
            case CDB:
                // 根据 需求类型 + 地域 + 实例类型（大小内存） + 年月 进行分组
                return getGroupingKeyFunction(
                        item -> ((Detail) item).getType(),
                        item -> ((Detail) item).getRegionName(),
                        item -> ((Detail) item).getInstanceType(), // 区分大小内存
                        item -> ((Detail) item).getYear().toString(),
                        item -> ((Detail) item).getMonth().toString()
                );
            default:
                throw new BizException("不支持的产品类型: " + product);
        }
    }


    public static <T> Function<T, Integer> getTargetValeByProduct(String product){
        switch (Product2ModelForecastConfigEnum.getByName(product)) {
            case CVM:
            case EMR:
            case EKS:
                return item -> ((DraftItemDTO) item).getTotalCoreNum();

            case CDB:
                return item -> ((DraftItemDTO) item).getTotalMemory();
            default:
                throw new BizException("不支持的产品类型: " + product);
        }
    }


    /**
     *
     * @param dbSpecs 格式 XXCXXG
     * @return
     */
    public static Integer parseDbSpecsMemory(String dbSpecs){
        try {
            String[] split = dbSpecs.split("C");
            if (split.length < 2) {
                return 0;
            }
            String[] split1 = split[1].split("G");
            if (split1.length < 1) {
                return 0;
            }
            return Integer.parseInt(split1[0]);
        } catch (Exception e) {
            throw BizException.makeThrow("dbSpecs解析失败: " + dbSpecs);
        }

    }

    /**
     *
     * @param dbSpecs 格式 XXCXXG
     * @return
     */
    public static Integer parseDbSpecsCore(String dbSpecs){
        try {
            String[] split = dbSpecs.split("C");
            if (split.length < 2) {
                return 0;
            }
            return Integer.parseInt(split[0]);
        } catch (Exception e) {
            throw BizException.makeThrow("dbSpecs解析失败: " + dbSpecs);
        }
    }

}
