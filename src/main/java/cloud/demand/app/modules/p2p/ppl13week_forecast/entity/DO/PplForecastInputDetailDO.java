package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
@Table("ppl_forecast_input_detail")
@JsonIgnoreProperties(ignoreUnknown = true)
public class PplForecastInputDetailDO implements ForecastKey{

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "input_id")
    private Long inputId;

    @Column(value = "task_id")
    private Long taskId;

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    /** 海关<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title", insertValueScript = "''")
    private String customhouseTitle;

    /** 机型<br/>Column: [gins_family] */
    @Column(value = "gins_family", insertValueScript = "''")
    private String ginsFamily;

    @Column(value = "region", insertValueScript = "''")
    private String region;

    @Column(value = "region_name", insertValueScript = "''")
    private String regionName;

    @Column(value = "core_num")
    private BigDecimal coreNum;

    @Column(value = "type", insertValueScript = "''")
    private String type;

    @Column(value = "diff_core_num")
    private BigDecimal diffCoreNum;


    @Override
    public String getForecastSeqType() {
        return this.getType();
    }
}
