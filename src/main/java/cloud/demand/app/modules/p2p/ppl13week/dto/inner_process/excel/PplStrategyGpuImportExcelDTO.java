package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel;

import cloud.demand.app.common.excel.convert.WhetherOrNotConverter;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp.ErrorMessage;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CpqTypeEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.lang.DateUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import lombok.Data;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.util.StringUtils;

@Data
public class PplStrategyGpuImportExcelDTO {

    public static PplImportExcelDTO copyToNewDTO(PplStrategyGpuImportExcelDTO source) {
        PplImportExcelDTO pplItemImportExcelDTO = new PplImportExcelDTO();
        pplItemImportExcelDTO.setCustomerTypeName(source.getCustomerTypeName());
        pplItemImportExcelDTO.setCustomerUin(source.getCustomerUin());
        pplItemImportExcelDTO.setCustomerShortName(source.getCustomerShortName());
        pplItemImportExcelDTO.setDemandTypeName(source.getDemandTypeName());
        pplItemImportExcelDTO.setDemandScene(source.getDemandScene());
        pplItemImportExcelDTO.setBillType(source.getBillType());
        pplItemImportExcelDTO.setWinRate(source.getWinRate());
        pplItemImportExcelDTO.setImportantDemand(source.getImportantDemand());

        // 兼容2025/1/6这种格式
        if (source.getBeginBuyDate().contains("/")) {
            pplItemImportExcelDTO.setBeginBuyDate(source.getBeginBuyDate()
                    .replaceAll("(\\d+)/(\\d{1,2})/(\\d{1,2})", "$1-$2-$3")
                    .replaceAll("-(\\d)(?!\\d)", "-0$1"));
        } else {
            pplItemImportExcelDTO.setBeginBuyDate(source.getBeginBuyDate());
        }
        if (source.getEndBuyDate().contains("/")) {
            pplItemImportExcelDTO.setEndBuyDate(source.getEndBuyDate()
                    .replaceAll("(\\d+)/(\\d{1,2})/(\\d{1,2})", "$1-$2-$3")
                    .replaceAll("-(\\d)(?!\\d)", "-0$1"));
        } else {
            pplItemImportExcelDTO.setEndBuyDate(source.getEndBuyDate());
        }
        pplItemImportExcelDTO.setBeginElasticDate(source.getBeginElasticDate());
        pplItemImportExcelDTO.setEndElasticDate(source.getEndElasticDate());
        pplItemImportExcelDTO.setNote(source.getNote());
        pplItemImportExcelDTO.setRegionName(source.getRegionName());
        pplItemImportExcelDTO.setIsStrongDesignateZone(source.getIsStrongDesignateZone());
        pplItemImportExcelDTO.setZoneName(source.getZoneName());
        pplItemImportExcelDTO.setInstanceType(source.getInstanceType());
        pplItemImportExcelDTO.setInstanceModel(source.getInstanceModel());
        pplItemImportExcelDTO.setInstanceModelCpuCore(source.getInstanceModelCpuCore().toString());
        pplItemImportExcelDTO.setInstanceModelRam(source.getInstanceModelRam().toString());
        pplItemImportExcelDTO.setInstanceNum(source.getInstanceNum().toString());

        //gpu独有字段
        pplItemImportExcelDTO.setBizScene(source.getBizScene());
        pplItemImportExcelDTO.setBizDetail(source.getBizDetail());
        pplItemImportExcelDTO.setServiceTime(source.getServiceTime());
        pplItemImportExcelDTO.setGpuNum(source.getGpuNum());
        pplItemImportExcelDTO.setIsAcceptAdjust(source.getIsAcceptAdjust());
        pplItemImportExcelDTO.setAcceptGpu(source.getAcceptGpu());
        pplItemImportExcelDTO.setSaleDurationYear(source.getSaleDurationYear());
        pplItemImportExcelDTO.setBusinessCpqName(source.getBusinessCpqName());
        pplItemImportExcelDTO.setApplyDiscount(source.getApplyDiscount());

        pplItemImportExcelDTO.setWarZone(source.getWarZone());
        pplItemImportExcelDTO.setProjectName(source.getProjectName());
        pplItemImportExcelDTO.setBizId(source.getBizId());

        pplItemImportExcelDTO.setCbsIo(source.getCbsIo());

        pplItemImportExcelDTO.setAlternativeZoneName(source.getAlternativeZoneName());

        return pplItemImportExcelDTO;
    }

    public static List<PplStrategyGpuImportExcelDTO> transFrom(List<GroupItemDTO> data) {
        List<PplStrategyGpuImportExcelDTO> gpuImportExcelDTOList = Lists.newArrayList();
        for (GroupItemDTO groupItemDTO : data) {
            gpuImportExcelDTOList.add(convertFromGroupItemDTO(groupItemDTO));
        }
        return gpuImportExcelDTOList;
    }

    private static PplStrategyGpuImportExcelDTO convertFromGroupItemDTO(GroupItemDTO groupItemDTO) {
        PplStrategyGpuImportExcelDTO gpuImportExcelDTO = new PplStrategyGpuImportExcelDTO();
        gpuImportExcelDTO.setCustomerTypeName(groupItemDTO.getCustomerTypeName());
        gpuImportExcelDTO.setCustomerUin(groupItemDTO.getCustomerUin());
        gpuImportExcelDTO.setCustomerShortName(groupItemDTO.getCustomerShortName());
        gpuImportExcelDTO.setDemandTypeName(groupItemDTO.getDemandTypeName());
        gpuImportExcelDTO.setDemandScene(groupItemDTO.getDemandScene());
        gpuImportExcelDTO.setBillType(groupItemDTO.getBillType());

        BigDecimal winRate = groupItemDTO.getWinRate();
        String winRateStr = winRate == null ? "" : "" + winRate.setScale(0, RoundingMode.HALF_UP).intValue() + "%";
        gpuImportExcelDTO.setWinRate(winRateStr);
        gpuImportExcelDTO.setImportantDemand(groupItemDTO.getImportantDemand());

        gpuImportExcelDTO.setBeginBuyDate(null2empty(groupItemDTO.getBeginBuyDate()));
        gpuImportExcelDTO.setEndBuyDate(null2empty(groupItemDTO.getEndBuyDate()));
        gpuImportExcelDTO.setBeginElasticDate(null2empty(groupItemDTO.getBeginElasticDate()));
        gpuImportExcelDTO.setEndElasticDate(null2empty(groupItemDTO.getEndElasticDate()));
        gpuImportExcelDTO.setNote(groupItemDTO.getNote());
        gpuImportExcelDTO.setRegionName(groupItemDTO.getRegionName());
        gpuImportExcelDTO.setIsStrongDesignateZone(groupItemDTO.getIsStrongDesignateZone() ? "是" : "否");
        gpuImportExcelDTO.setZoneName(groupItemDTO.getZoneName());
        gpuImportExcelDTO.setInstanceType(groupItemDTO.getInstanceType());
        gpuImportExcelDTO.setInstanceModel(groupItemDTO.getInstanceModel());
        gpuImportExcelDTO.setInstanceModelCpuCore(groupItemDTO.getInstanceModelCoreNum());
        gpuImportExcelDTO.setInstanceModelRam(groupItemDTO.getInstanceModelRamNum());
        gpuImportExcelDTO.setInstanceNum(groupItemDTO.getInstanceNum());
        gpuImportExcelDTO.setTotalCoreNum(groupItemDTO.getTotalCoreNum());

        gpuImportExcelDTO.setGpuNum(groupItemDTO.getGpuNum());
        gpuImportExcelDTO.setTotalGpuNum(groupItemDTO.getTotalGpuNum());
        gpuImportExcelDTO.setServiceTime(groupItemDTO.getServiceTime());
        gpuImportExcelDTO.setBizScene(groupItemDTO.getBizScene());
        gpuImportExcelDTO.setBizDetail(groupItemDTO.getBizDetail());
        gpuImportExcelDTO.setAcceptGpu(groupItemDTO.getAcceptGpu());
        gpuImportExcelDTO.setIsAcceptAdjust(
                groupItemDTO.getIsAcceptAdjust() != null && groupItemDTO.getIsAcceptAdjust() ? "是" : "否");
        if (Lang.isNotEmpty(groupItemDTO.getAlternativeZoneName())) {
            gpuImportExcelDTO.setAlternativeZoneName(
                    String.join(";", groupItemDTO.getAlternativeZoneName()));
        }
        gpuImportExcelDTO.setGpuType(groupItemDTO.getGpuType());
        gpuImportExcelDTO.setGpuProductType(groupItemDTO.getGpuProductType());

        gpuImportExcelDTO.setSaleDurationYear(groupItemDTO.getSaleDurationYear());
        gpuImportExcelDTO.setApplyDiscount(groupItemDTO.getApplyDiscount());
        gpuImportExcelDTO.setBusinessCpqName(CpqTypeEnum.getNameByCode(groupItemDTO.getBusinessCpq()));

        gpuImportExcelDTO.setBizId(groupItemDTO.getBizId());
        gpuImportExcelDTO.setCbsIo(null2empty(groupItemDTO.getCbsIo()));
//        gpuImportExcelDTO.setIndustryDept(groupItemDTO.getIndustryDept());
//        gpuImportExcelDTO.setProduct(groupItemDTO.getProduct());
//        gpuImportExcelDTO.setGroupStatus(
//                PplVersionGroupStatusEnum.getNameByCode(groupItemDTO.getGroupStatus()));
        return gpuImportExcelDTO;
    }

    private static String null2empty(BigDecimal a) {
        return a == null ? "" : a.setScale(3, RoundingMode.HALF_UP).toString();
    }

    private static String null2empty(LocalTime a) {
        return a == null ? "" : DateUtils.format(a, "HH:mm");
    }

    private static String null2empty(LocalDate a) {
        return a == null ? "" : DateUtils.format(a, "yyyy-MM-dd");
    }

    private static String null2empty(Integer a) {
        return a == null ? "" : a.toString();
    }

    private <K> ExcelProperty getEP(IGetter<K> fn) {
        return ORMUtils.getAnnotationByGetter(ExcelProperty.class, fn);
    }

    private <K> String getColName(IGetter<K> fn) {
        return getEP(fn).value()[0];
    }

    private <K> int getColIndex(IGetter<K> fn) {
        return getEP(fn).index() + 1;
    }

    private <K> ErrorMessage makeError(int raw, IGetter<K> fn, String message) {
        return new ErrorMessage(raw, getColIndex(fn), getColName(fn), message);
    }

    /**
     * 只支持 String 的方法
     */
    public <K> ErrorMessage makeErrorIfBlank(int raw, IGetter<K> fn) {

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        String colName = getColName(fn);
        if (value == null) {
            return makeError(raw, fn, colName + "为空");
        }
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (Strings.isBlank((String) value)) {
                return makeError(raw, fn, colName + "为空");
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> ErrorMessage makeErrorIfNotContain(int raw, IGetter<K> fn, List<String> all) {

        ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (value instanceof String) {
            value = StringUtils.trimWhitespace((String) value);
            if (!all.contains((String) value)) {
                String colName = getColName(fn);
                String msg = colName + "出错:  [" + value + "] 不在下面范围 " + all + " 中";
                if (Lang.isEmpty(all)) {
                    msg = colName + "出错:  不可以填 [" + value + "] ";
                }
                return makeError(raw, fn, msg);
            }
        } else {
            throw Lang.makeThrow("不支持类型");
        }
        return null;
    }

    /**
     * 首先会判断空值, 如果有空值直接返回空的 error
     *
     * 值包不含在 all 中， 返回 error
     *
     * 包含返回 null
     */
    public <K> ErrorMessage makeErrorIfNotContain(int raw, IGetter<K> fn, List<String> all, String rangeMsg) {

        ErrorMessage errorMessage = makeErrorIfBlank(raw, fn);
        if (errorMessage != null) {
            return errorMessage;
        }

        Field declaredField = ORMUtils.getFiledByGetter(fn);
        Object value = DOInfoReader.getValue(declaredField, this);
        if (!all.contains(value)) {
            String colName = getColName(fn);
            String msg = colName + "出错:  [" + value + "], " + rangeMsg;
            if (Lang.isEmpty(all)) {
                msg = colName + "出错:  不可以填 [" + value + "] ";
            }
            return makeError(raw, PplStrategyGpuImportExcelDTO::getDemandTypeName, msg);
        }
        return null;
    }

    @ExcelProperty(index = 0, value = "客户类型")
    private String customerTypeName;
    @ExcelProperty(index = 1, value = "客户Uin")
    private String customerUin;
    @ExcelProperty(index = 2, value = "客户简称")
    private String customerShortName;

    @ExcelProperty(index = 3, value = "战区")
    private String warZone;

    @ExcelProperty(index = 4, value = "项目名称")
    private String projectName;

    /**
     * 需求类型
     */
    @ExcelProperty(index = 5, value = "需求类型")
    private String demandTypeName;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @ExcelProperty(index = 6, value = "需求场景")
    private String demandScene;

    /**
     * 业务场景<br/>Column: [biz_scene]
     */
    @ExcelProperty(index = 7, value = "业务场景")
    private String bizScene;

    /**
     * 业务详情<br/>Column: [biz_detail]
     */
    @ExcelProperty(index = 8, value = "业务详情")
    private String bizDetail;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    @ExcelProperty(index = 9, value = "计费模式")
    private String billType;

    /**
     * 使用时长<br/>Column: [service_time]
     */
    @ExcelProperty(index = 10, value = "使用时长")
    private String serviceTime;

    /**
     * 是否重保需求，当前仅GPU产品需求这个字段，重保需求盈率固定为100%。备货策略：重保需求按100%备货，非重保需求按量级*赢率进行备货
     */
    @ExcelProperty(index = 11, value = "是否重保需求", converter = WhetherOrNotConverter.class)
    private Boolean importantDemand;


    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    @ExcelProperty(index = 12, value = "赢率，百分比")
    private String winRate;


    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    @ExcelProperty(index = 13, value = "开始购买日期")
    private String beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    @ExcelProperty(index = 14, value = "结束购买日期")
    private String endBuyDate;


    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    @ExcelProperty(index = 15, value = "弹性开始日期")
    private String beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    @ExcelProperty(index = 16, value = "弹性结束日期")
    private String endElasticDate;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    @ExcelProperty(index = 17, value = "地域")
    private String regionName;


    /**
     * 是否强制指定可用区<br/>Column: [is_strong_designate_zone]
     */
    @ExcelProperty(index = 18, value = "是否强指定可用区")
    private String isStrongDesignateZone;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    @ExcelProperty(index = 19, value = "可用区")
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @ExcelProperty(index = 20, value = "实例类型")
    private String instanceType;

    /**
     * 单台卡数<br/>Column: [gpu_gum]
     */
    @ExcelProperty(index = 21, value = "单台卡数")
    private BigDecimal gpuNum;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 22, value = "实例规格核心数")
    private Integer instanceModelCpuCore;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 23, value = "实例规格内存数")
    private Integer instanceModelRam;


    /**
     * 实例数量<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 24, value = "实例数量")
    private Integer instanceNum;

    /**
     * 是否接受其他卡型<br/>Column: [isAcceptAdjust]
     */
    @ExcelProperty(index = 25, value = "是否接受其他卡型")
    private String isAcceptAdjust;

    /**
     * 接受GPU卡型 用;分割<br/>Column: [acceptGpu]
     */
    @ExcelProperty(index = 26, value = "接受卡型")
    private String acceptGpu;


    /**
     * 特殊备注说明<br/>Column: [note]
     */
    @ExcelProperty(index = 27, value = "特殊备注说明")
    private String note;

    @ExcelProperty(index = 28, value = "唯一标识Id")
    private String bizId;

    /**
     * 产品形态<br/>Column: [gpuProductType]
     */
    @ExcelProperty(index = 29, value = "产品形态")
    private String gpuProductType;

    /**
     * 卡型<br/>Column: [gpuType]
     */
    @ExcelProperty(index = 30, value = "卡型")
    private String gpuType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    @ExcelProperty(index = 31, value = "实例规格")
    private String instanceModel;

    /**
     * 总卡数<br/>Column: [instance_num]
     */
    @ExcelProperty(index = 32, value = "总卡数")
    private BigDecimal totalGpuNum;

    /**
     * 总核心数<br/>Column: [totalCoreNum]
     */
    @ExcelProperty(index = 33, value = "总核心数")
    private Integer totalCoreNum;

    /**
     * 包销时长(年)（允许一位小数点，excel导入时最小填写单位为0.5）
     */
    @ExcelProperty(index = 34, value = "包销时长(年)")
    private BigDecimal saleDurationYear;

    /**
     * 申请折扣(折)（填写范围为0.1~10）
     */
    @ExcelProperty(index = 35, value = "申请折扣(折)")
    private BigDecimal applyDiscount;

    /**
     * 商务进展（枚举值-CpqTypeEnum）
     */
    @ExcelProperty(index = 36, value = "商务进展")
    private String businessCpqName;

    /**
     * CBS单实例IO(MB/s)
     */
    @ExcelProperty(index = 37, value = "单实例IO(MB/s)")
    private String cbsIo;

    @ExcelProperty(index = 38, value = "备选可用区")
    private String alternativeZoneName;

}
