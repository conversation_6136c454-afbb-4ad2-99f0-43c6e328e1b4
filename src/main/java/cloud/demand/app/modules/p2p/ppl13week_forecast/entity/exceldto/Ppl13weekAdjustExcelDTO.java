package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.exceldto;


import cloud.demand.app.common.utils.ObjUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.pugwoo.wooutils.json.JSON;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class Ppl13weekAdjustExcelDTO {

    @ExcelProperty(index = 0, value = "id")
    private Long resultSplitId;

    @ExcelProperty(index = 1, value = "需求来源")
    private String sourceTypeName;

    @ExcelProperty(index = 2, value = "类型")
    private String typeName;

    @ExcelProperty(index = 3, value = "预测算法")
    private String predictAlgorithmName;

    @ExcelProperty(index = 4, value = "预测年月")
    private String yearMonth;

    @ExcelProperty(index = 5, value = "提前期")
    private Integer predictIndex;

    @ExcelProperty(index = 6, value = "机型")
    private String ginsFamily;

    @ExcelProperty(index = 7, value = "实例规格")
    private String instanceModel;

    @ExcelProperty(index = 8, value = "核心类型")
    private String coreTypeName;

    @ExcelProperty(index = 9, value = "地域")
    private String regionName;

    @ExcelProperty(index = 10, value = "可用区")
    private String zoneName;

    @ExcelProperty(index = 11, value = "core预测值(原始)")
    private BigDecimal originNum;

    @ExcelProperty(index = 12, value = "core预测值(干预)")
    private BigDecimal adjustNum;

    @ExcelProperty(index = 13, value = "干预原因")
    private String desc;

    public static  List<Ppl13weekAdjustExcelDTO> decode(MultipartFile file) throws IOException {

        List<Ppl13weekAdjustExcelDTO> ret = Lang.list();
        EasyExcel.read(
                file.getInputStream(), Ppl13weekAdjustExcelDTO.class,
                new AnalysisEventListener<Ppl13weekAdjustExcelDTO>() {
                    @Override
                    public void invoke(Ppl13weekAdjustExcelDTO o, AnalysisContext analysisContext) {
                        if (ObjUtils.allFieldIsNull(o)) {
                            log.info("读到第一个空行，结束");
                            throw new ExcelAnalysisStopException();
                        }
                        ret.add(JSON.clone(o));
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        log.info("decode over");
                    }
                }
        ).sheet(0).headRowNumber(2).doRead();
        return ret;
    }


}
