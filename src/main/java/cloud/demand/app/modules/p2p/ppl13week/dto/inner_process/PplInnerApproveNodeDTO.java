package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionSlaDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplInnerProcessNodeDeadlineAutoPassEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessAttributeEnum;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionNodeDetail;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalTime;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import lombok.NoArgsConstructor;

@Data
public class PplInnerApproveNodeDTO {

    /**  ppl_inner_process_node / ppl_inner_process_version_sla 主键*/
    private Long nodeSlaId;

    private Long nextNodeSlaId;

    private String nodeCode;

    private Boolean isBeginNode;

    private String nodeName;

    /** 角色编码 */
    private String approveRole;

    private String approveRoleName;

    /** 分组编码 */
    private String roleAttribute;

    private String roleAttributeName;

    /** ENTER / EXIT*/
    private String deadlineType;

    private Date deadlineTime;

    /** 到期自动过单：CLOSE-关闭，UNCHANGED_PASS-无变化时自动过单，DIRECT_PASS-全部自动过单 */
    private String deadlineAutoPass;

    private String deadlineAutoPassName;

    /**  是否开启高亮审批意见 */
    private Boolean isHighlight;

    /**
     * 下几周<br/>Column: [next_week]
     */
    private Integer nextWeek;

    /**
     * 周几<br/>Column: [day_of_week]
     */
    private Integer dayOfWeek;

    /**
     * 时间<br/>Column: [time]
     */
    private LocalTime time;

    public static PplInnerApproveNodeDTO convertPplInnerApproveNodeDTO(PplInnerProcessVersionSlaDO o) {
        PplInnerApproveNodeDTO pplInnerApproveNodeDTO = new PplInnerApproveNodeDTO();
        pplInnerApproveNodeDTO.setNodeSlaId(o.getId());
        pplInnerApproveNodeDTO.setNextNodeSlaId(o.getNextSlaId());
        pplInnerApproveNodeDTO.setNodeCode(o.getNodeCode());
        pplInnerApproveNodeDTO.setIsBeginNode(o.getIsBeginNode());
        pplInnerApproveNodeDTO.setNodeName(o.getNodeName());
        pplInnerApproveNodeDTO.setApproveRole(o.getApproveRole());
        pplInnerApproveNodeDTO.setApproveRoleName(IndustryDemandAuthRoleEnum.getNameByCode(o.getApproveRole()));
        pplInnerApproveNodeDTO.setRoleAttribute(o.getRoleAttribute());
        pplInnerApproveNodeDTO.setRoleAttributeName(PplInnerProcessAttributeEnum.getNameByCode(o.getRoleAttribute()));
        pplInnerApproveNodeDTO.setDeadlineType(o.getDeadlineType());
        pplInnerApproveNodeDTO.setDeadlineTime(o.getDeadlineTime());
        pplInnerApproveNodeDTO.setDeadlineAutoPass(o.getDeadlineAutoPass());
        pplInnerApproveNodeDTO.setDeadlineAutoPassName(PplInnerProcessNodeDeadlineAutoPassEnum.getNameByCode(o.getDeadlineAutoPass()));
        pplInnerApproveNodeDTO.setIsHighlight(o.getIsHighlight());
        return pplInnerApproveNodeDTO;
    }

    public static PplInnerApproveNodeDTO convertPplInnerApproveNodeDTO(PplInnerVersionNodeDetail o) {
        PplInnerApproveNodeDTO pplInnerApproveNodeDTO = new PplInnerApproveNodeDTO();
        pplInnerApproveNodeDTO.setNodeSlaId(o.getId());
        pplInnerApproveNodeDTO.setNextNodeSlaId(o.getNextId());
        pplInnerApproveNodeDTO.setNodeCode(o.getNodeCode());
        pplInnerApproveNodeDTO.setIsBeginNode(o.getIsBeginNode());
        pplInnerApproveNodeDTO.setNodeName(o.getNodeName());
        pplInnerApproveNodeDTO.setApproveRole(o.getApproveRole());
        pplInnerApproveNodeDTO.setApproveRoleName(IndustryDemandAuthRoleEnum.getNameByCode(o.getApproveRole()));
        pplInnerApproveNodeDTO.setRoleAttribute(o.getRoleAttribute());
        pplInnerApproveNodeDTO.setRoleAttributeName(PplInnerProcessAttributeEnum.getNameByCode(o.getRoleAttribute()));
        pplInnerApproveNodeDTO.setDeadlineType(o.getDeadlineType());
        pplInnerApproveNodeDTO.setDeadlineTime(o.getDeadlineTime());
        pplInnerApproveNodeDTO.setDeadlineAutoPass(o.getDeadlineAutoPass());
        pplInnerApproveNodeDTO.setDeadlineAutoPassName(PplInnerProcessNodeDeadlineAutoPassEnum.getNameByCode(o.getDeadlineAutoPass()));
        pplInnerApproveNodeDTO.setIsHighlight(o.getIsHighlight());
        pplInnerApproveNodeDTO.setNextWeek(o.getNextWeek());
        pplInnerApproveNodeDTO.setDayOfWeek(o.getDayOfWeek());
        pplInnerApproveNodeDTO.setTime(o.getTime());
        return pplInnerApproveNodeDTO;
    }

    // 后台 -> 前台

    public Date getDeadlineTime() {
        return deadlineTime;
    }

}