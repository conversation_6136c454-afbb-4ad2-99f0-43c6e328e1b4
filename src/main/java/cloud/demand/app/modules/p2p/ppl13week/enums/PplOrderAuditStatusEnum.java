package cloud.demand.app.modules.p2p.ppl13week.enums;

import com.pugwoo.wooutils.string.StringTools;
import java.util.Objects;
import lombok.Getter;

@Getter
public enum PplOrderAuditStatusEnum {

    WAIT("WAIT", "待审批"),

    /**
     * 已经审批通过，但是由于等待同级别其它节点完成，所以没有到PASS状态
     */
    AUDITED("AUDITED", "已审批"),

    // 暂时弃用
    //  REJECT("REJECT", "驳回"),

    REJECT_MODIFY("REJECT_MODIFY", "驳回待处理"),

    REFUSE("REFUSE", "拒绝"),

    PASS("PASS", "已通过"),
    ;

    final private String code;
    final private String name;

    PplOrderAuditStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 判断审批状态是否还在进行中，这里指的还需要人工审批，不含已经审批但是处于等待状态的AUDITED
     */
    public static boolean isProcessing(String code) {
        return StringTools.isIn(code, WAIT.getCode(), REJECT_MODIFY.getCode());
    }

    public static PplOrderAuditStatusEnum getByCode(String code) {
        for (PplOrderAuditStatusEnum e : PplOrderAuditStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplOrderAuditStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}