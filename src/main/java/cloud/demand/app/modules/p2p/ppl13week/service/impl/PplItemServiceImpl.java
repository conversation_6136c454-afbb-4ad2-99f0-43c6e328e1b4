package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.ChangeItemEventDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplApplyDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeAllFieldDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplItemChangeRecordNewDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemEventTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.event.PplChangeEvent;
import cloud.demand.app.modules.p2p.ppl13week.service.PplItemService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemAppliedVO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class PplItemServiceImpl implements PplItemService {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    @Transactional("demandTransactionManager")
    public void matchPplAndApplied(boolean isPpl1, PplItemAppliedVO itemAppliedVO, PplApplyDetailDTO applyDetailDTO) {
        List<PplItemDO> allApplyPplItem = ListUtils.newList();

        // 进行预约
        List<PplItemDO> updatePplItemDOs = applyDetailDTO.getUpdatePplItemDOs();
        for (PplItemDO updatePplItem : updatePplItemDOs) {
            updatePplItem.setYunxiaoOrderId(itemAppliedVO.getYunxiaoOrderId());
            updatePplItem.setYunxiaoDetailId(itemAppliedVO.getYunxiaoDetailId());
            updatePplItem.setYunxiaoOrderStatus(itemAppliedVO.getYunxiaoOrderStatus());
            updatePplItem.setAppRole(itemAppliedVO.getAppRole());
            updatePplItem.setOrderCategory(itemAppliedVO.getOrderCategory());

            updatePplItem.setStatus(PplItemStatusEnum.APPLIED.getCode());

            updatePplItem.setInstanceNumApplyBefore(updatePplItem.getInstanceNumApplyBefore());
            updatePplItem.setTotalCoreApplyBefore(updatePplItem.getTotalCoreApplyBefore());
            updatePplItem.setTotalGpuNumApplyBefore(updatePplItem.getTotalGpuNumApplyBefore());
        }
        if (ListUtils.isNotEmpty(updatePplItemDOs)) {
            // 先查出历史pplItem并存到beforeMap中
            Map<String, PplItemDO> beforeMap = demandDBHelper.getAll(PplItemDO.class, "where id in (?)",
                    updatePplItemDOs.stream().map(PplItemDO::getPplId).collect(
                            Collectors.toList())).stream().collect(
                    Collectors.toMap(PplItemDO::getPplId, Function.identity(), (v1, v2) -> v2));

            // 更新pplItem
            demandDBHelper.update(updatePplItemDOs);
            allApplyPplItem.addAll(updatePplItemDOs);

            // 记录日志
            List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
            for (PplItemDO pplItemDO : updatePplItemDOs) {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                PplItemDO before = beforeMap.get(pplItemDO.getPplId());
                if (before != null) {
                    PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
                    BeanUtils.copyProperties(before, beforeAllFieldDTO);
                    recordNewDTO.setBeforeItem(beforeAllFieldDTO);
                }
                PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(pplItemDO, afterAllFieldDTO);
                recordNewDTO.setAfterItem(afterAllFieldDTO);
                newDTOs.add(recordNewDTO);
            }

            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.VALID_CHANGE.getCode(),
                            PplRecordChangeEventEnum.YUN_SUBMITTED.getCode(), "system",
                            newDTOs));
        }

        // 对有多余预测核数的ppl id 拆单
        List<PplItemDO> splitPplItemDOs = applyDetailDTO.getSplitPplItemDOs();
        for (PplItemDO splitPplItem : splitPplItemDOs) {
            // 如果拆单后被其他明细预约了，则设置预约信息
            if (PplItemStatusEnum.APPLIED.getCode().equals(splitPplItem.getStatus())) {
                splitPplItem.setYunxiaoOrderId(itemAppliedVO.getYunxiaoOrderId());
                splitPplItem.setYunxiaoDetailId(itemAppliedVO.getYunxiaoDetailId());
                splitPplItem.setYunxiaoOrderStatus(itemAppliedVO.getYunxiaoOrderStatus());
                splitPplItem.setAppRole(itemAppliedVO.getAppRole());
                splitPplItem.setOrderCategory(itemAppliedVO.getOrderCategory());
                allApplyPplItem.add(splitPplItem);
            }
        }
        if (ListUtils.isNotEmpty(splitPplItemDOs)) {
            demandDBHelper.insertBatchWithoutReturnId(splitPplItemDOs);

            // 记录日志
            List<PplItemChangeRecordNewDTO> newDTOs = new ArrayList<>();
            splitPplItemDOs.forEach(splitItem -> {
                PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
                PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
                BeanUtils.copyProperties(splitItem, afterAllFieldDTO);
                recordNewDTO.setAfterItem(afterAllFieldDTO);
                newDTOs.add(recordNewDTO);
            });
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.VALID_CHANGE.getCode(),
                            PplRecordChangeEventEnum.YUN_SUBMITTED_ADD.getCode(), "system",
                            newDTOs));
        }

        // 3. 自动补ppl id
        PplItemDO autoPpltem = isPpl1 ? itemAppliedVO.getPplItem1() : itemAppliedVO.getPplItem2();
        if (applyDetailDTO.getNeedAutoFillCore() > 0
                || applyDetailDTO.getNeedAutoFillGpu().compareTo(BigDecimal.ZERO) > 0) {
            // 提前保存历史pplItem
            PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
            PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(autoPpltem, beforeAllFieldDTO);
            recordNewDTO.setBeforeItem(beforeAllFieldDTO);

            int cpu = itemAppliedVO.getTotalCore() / itemAppliedVO.getInstanceNum();
            autoPpltem.setInstanceNum((int) Math.ceil(
                    applyDetailDTO.getNeedAutoFillCore().intValue() * 1.0 / cpu));
            autoPpltem.setTotalCore(applyDetailDTO.getNeedAutoFillCore().intValue());
            autoPpltem.setTotalGpuNum(applyDetailDTO.getNeedAutoFillGpu());

            autoPpltem.setInstanceNumApplyAfter(autoPpltem.getInstanceNum());
            autoPpltem.setTotalCoreApplyAfter(autoPpltem.getTotalCore());
            autoPpltem.setTotalGpuNumApplyAfter(autoPpltem.getTotalGpuNumApplyAfter());

            demandDBHelper.update(autoPpltem);
            allApplyPplItem.add(autoPpltem);

            // 记录日志
            PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
            BeanUtils.copyProperties(autoPpltem, afterAllFieldDTO);
            recordNewDTO.setAfterItem(afterAllFieldDTO);
            SpringUtil.getApplicationContext().publishEvent(
                    new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.VALID_CHANGE.getCode(),
                            PplRecordChangeEventEnum.YUN_SUBMITTED.getCode(), "system",
                            Collections.singletonList(recordNewDTO)));
        } else {
            demandDBHelper.executeRaw("update ppl_item set deleted = 1 where id = ? ", autoPpltem.getId());
        }

        // 4. 处理预约单
        if (ListUtils.isNotEmpty(allApplyPplItem)) {
            PplItemAppliedDO updateAppliedDO = new PplItemAppliedDO();
            updateAppliedDO.setId(itemAppliedVO.getId());

            List<PplOrderDO> pplOrders = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)",
                    ListUtils.transform(allApplyPplItem, o -> o.getPplOrder()));
            Map<String, PplOrderDO> pplOrderMap = ListUtils.toMap(pplOrders, o -> o.getPplOrder(), o -> o);

            // 这里应该只有2个，如果有多个，也放到第二个并用;处理起来
            updateAppliedDO.setPplId(allApplyPplItem.get(0).getPplId());
            updateAppliedDO.setPplOrder(allApplyPplItem.get(0).getPplOrder());
            PplOrderDO pplOrder = pplOrderMap.get(allApplyPplItem.get(0).getPplOrder());
            if (pplOrder != null) {
                updateAppliedDO.setPplOrderSource(pplOrder.getSource());
            }
            updateAppliedDO.setPplIdTotalCore(allApplyPplItem.get(0).getTotalCore());

            if (allApplyPplItem.size() > 1) {
                List<PplItemDO> tmp = allApplyPplItem.subList(1, allApplyPplItem.size());
                updateAppliedDO.setPplId2(String.join(";", ListUtils.transform(tmp, p -> p.getPplId())));
                List<String> noRepeatPplOrders = tmp.stream().map(PplItemDO::getPplOrder).distinct()
                        .collect(Collectors.toList());
                updateAppliedDO.setPplOrder2(String.join(";", noRepeatPplOrders));
                PplOrderDO pplOrder2 = pplOrderMap.get(updateAppliedDO.getPplOrder2());
                if (pplOrder2 != null) {
                    updateAppliedDO.setPplOrder2Source(pplOrder2.getSource());
                }
                updateAppliedDO.setPplId2TotalCore(
                        allApplyPplItem.stream().skip(1).mapToInt(PplItemDO::getTotalCore).sum());
            }

            List<PplItemDO> validPplItemDOs = new ArrayList<>();
            for (PplItemDO pplItemDO : allApplyPplItem) {
                PplOrderDO orderDO = pplOrderMap.get(pplItemDO.getPplOrder());
                if (orderDO != null && PplOrderSourceTypeEnum.IMPORT.getCode().equals(orderDO.getSource())) {
                    validPplItemDOs.add(pplItemDO);
                }
            }
            updateAppliedDO.setInstanceNumApplyAfter(
                    validPplItemDOs.stream().filter(e -> PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus())
                                    && e.getInstanceNumApplyAfter() != null)
                            .mapToInt(PplItemDO::getInstanceNumApplyAfter).sum());
            updateAppliedDO.setTotalCoreApplyAfter(
                    validPplItemDOs.stream().filter(e -> PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus())
                                    && e.getTotalCoreApplyAfter() != null)
                            .mapToInt(PplItemDO::getTotalCoreApplyAfter).sum());
            updateAppliedDO.setTotalGpuNumApplyAfter(validPplItemDOs.stream()
                    .filter(e -> PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus())
                            && e.getTotalGpuNumApplyAfter() != null).map(PplItemDO::getTotalGpuNumApplyAfter)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            demandDBHelper.update(updateAppliedDO);

        }

    }

    @Override
    public PplItemDO getByPplId(String pplId) {
        if (Strings.isBlank(pplId)) {
            return null;
        }
        return demandDBHelper.getOne(PplItemDO.class, "where ppl_id=?", pplId);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void deletePplId(String pplId, ChangeItemEventDTO eventDTO) {
        if (StringTools.isBlank(pplId)) {
            log.error("pplId is blank, fail to delete");
            return;
        }

        PplItemDO one = demandDBHelper.getOne(PplItemDO.class, "where ppl_id=?", pplId);
        if (one == null) {
            log.error("pplId:{} is not exist, fail to delete", pplId);
            return;
        }

        // 除了来自云霄的反向同步订单
        if (Objects.equals(eventDTO.getEventType(), PplItemEventTypeEnum.PPL_REVERSE_SYNC.getCode())) {
            // 云霄的反向同步订单，不检查是否已APPLIED
        } else {
            if (Objects.equals(one.getStatus(), PplItemStatusEnum.APPLIED.getCode())) {
                log.warn("pplId:{} already applied, cannot delete, just ignore", pplId);
                return;
            }
        }

        PplItemRecordDO recordDO = new PplItemRecordDO();
        recordDO.setPplId(pplId);
        recordDO.setBeforeJson(JSON.toJson(one));

        // 删除相当于把数量设置为0
        one.setInstanceNum(0);
        one.setTotalCore(0);
        one.setTotalDisk(0);
        recordDO.setAfterJson(JSON.toJson(one));

        if (eventDTO != null) {
            eventDTO.fill(recordDO);
        }

        // 如果是云霄的反向同步，则真删除，否则是设置为0
        if (Objects.equals(eventDTO.getEventType(), PplItemEventTypeEnum.PPL_REVERSE_SYNC.getCode())) {
            demandDBHelper.delete(one);
        } else {
            demandDBHelper.update(one);
        }

        demandDBHelper.insert(recordDO);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void updatePplId(PplItemDO pplItemDO, ChangeItemEventDTO eventDTO) {
        if (pplItemDO == null) {
            throw new BizException("参数pplItemDO为null，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        if (pplItemDO.getId() == null && StringTools.isBlank(pplItemDO.getPplId())) {
            throw new BizException("参数pplItemDO主键不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }

        PplItemDO before;
        if (pplItemDO.getId() != null) {
            before = demandDBHelper.getOne(PplItemDO.class, "where id=?", pplItemDO.getId());
            if (before == null) {
                throw new BizException(
                        "ppl id:" + pplItemDO.getId() + "不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
            }
        } else {
            before = demandDBHelper.getOne(PplItemDO.class, "where ppl_id=?", pplItemDO.getPplId());
            if (before == null) {
                throw new BizException(
                        "pplId:" + pplItemDO.getPplId() + "不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
            }
        }

        // 已申领就不再允许变更了，除了来自云霄的反向同步订单
        if (Objects.equals(eventDTO.getEventType(), PplItemEventTypeEnum.PPL_REVERSE_SYNC.getCode())) {
            // 云霄的反向同步订单，不检查是否已APPLIED
        } else {
            if (Objects.equals(before.getStatus(), PplItemStatusEnum.APPLIED.getCode())) {
                log.warn("pplId:{} already applied, cannot update, just ignore", before.getPplId());
                return;
            }
        }

        if (pplItemDO.getId() == null) {
            pplItemDO.setId(before.getId());
        }
        updateTotalAmount(pplItemDO);
        int row = demandDBHelper.update(pplItemDO);
        if (row == 0) {
            throw new BizException(
                    "ppl_id:" + pplItemDO.getPplId() + "更新失败，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        PplItemDO after = demandDBHelper.getOne(PplItemDO.class, "where id=?", pplItemDO.getId());

        // 写入recordDO
        PplItemRecordDO recordDO = new PplItemRecordDO();
        recordDO.setPplId(pplItemDO.getPplId());
        recordDO.setBeforeJson(JSON.toJson(before));
        recordDO.setAfterJson(JSON.toJson(after));
        if (eventDTO != null) {
            eventDTO.fill(recordDO);
        }
        demandDBHelper.insert(recordDO);
    }


    private void updateTotalAmount(PplItemDO pplItemDO) {
        Tuple2<Integer, Integer> cpuRamNum = P2PInstanceModelParse.parseInstanceModel(
                pplItemDO.getInstanceModel());

        Integer instanceNum = pplItemDO.getInstanceNum();
        instanceNum = instanceNum == null ? 0 : instanceNum;

        pplItemDO.setTotalCore(cpuRamNum._1 * instanceNum);
        if (PplItemStatusEnum.APPLIED.getCode().equals(pplItemDO.getStatus())) {
            pplItemDO.setTotalCoreApplyAfter(pplItemDO.getTotalCore());
            pplItemDO.setTotalGpuNumApplyAfter(pplItemDO.getTotalGpuNumApplyAfter() != null
                    && pplItemDO.getTotalGpuNumApplyAfter().compareTo(BigDecimal.ZERO) > 0
                    ? pplItemDO.getTotalGpuNumApplyAfter() : pplItemDO.getTotalGpuNum());
            pplItemDO.setInstanceNumApplyAfter(
                    pplItemDO.getInstanceNumApplyAfter() != null && pplItemDO.getInstanceNumApplyAfter() > 0
                            ? pplItemDO.getInstanceNumApplyAfter() : pplItemDO.getInstanceNum());
        }

        int totalDiskNum = 0;
        if (pplItemDO.getSystemDiskNum() == null) {
            pplItemDO.setSystemDiskNum(1);
        }
        if (pplItemDO.getSystemDiskStorage() != null) {
            totalDiskNum += pplItemDO.getSystemDiskStorage() * pplItemDO.getSystemDiskNum();
        }
        if (pplItemDO.getDataDiskStorage() != null && pplItemDO.getDataDiskNum() != null) {
            totalDiskNum += pplItemDO.getDataDiskStorage() * pplItemDO.getDataDiskNum();
        }
        pplItemDO.setTotalDisk(totalDiskNum * instanceNum);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void insert(PplItemDO pplItemDO, ChangeItemEventDTO eventDTO) {
        if (pplItemDO == null) {
            throw new BizException("参数pplItemDO为null，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        if (StringTools.isBlank(pplItemDO.getPplId())) {
            throw new BizException("参数pplItemDO主键不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }

        pplItemDO.setId(null);
        pplItemDO.setCreateTime(null);
        pplItemDO.setUpdateTime(null);
        pplItemDO.setDeleted(null);

        updateTotalAmount(pplItemDO);
        int row = demandDBHelper.insert(pplItemDO);
        if (row == 0) {
            throw new BizException(
                    "ppl_id:" + pplItemDO.getPplId() + "更新失败，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        PplItemDO after = demandDBHelper.getOne(PplItemDO.class, "where id=?", pplItemDO.getId());

        // 写入recordDO
        PplItemRecordDO recordDO = new PplItemRecordDO();
        recordDO.setPplId(pplItemDO.getPplId());
        recordDO.setBeforeJson(null);
        recordDO.setAfterJson(JSON.toJson(after));
        if (eventDTO != null) {
            eventDTO.fill(recordDO);
        }
        demandDBHelper.insert(recordDO);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void apply(PplItemDO pplItemDO, ChangeItemEventDTO eventDTO) {
        if (pplItemDO == null) {
            throw new BizException("参数pplItemDO为null，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        if (pplItemDO.getId() == null) {
            throw new BizException("参数pplItemDO主键不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }

        PplItemDO before = demandDBHelper.getOne(PplItemDO.class, "where id=?", pplItemDO.getId());

        if (before == null) {
            throw new BizException(
                    "ppl id:" + pplItemDO.getId() + "不存在，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        if (!PplItemStatusEnum.VALID.getCode().equals(before.getStatus())) {
            throw new BizException("ppl id:" + pplItemDO.getId() + "状态不是VALID，不允许预约");
        }
        pplItemDO.setStatus(PplItemStatusEnum.APPLIED.getCode());

        // 记录预约前 预测的预约资源量
        pplItemDO.setInstanceNumApplyBefore(before.getInstanceNumApplyBefore());
        pplItemDO.setTotalCoreApplyBefore(before.getTotalCoreApplyBefore());
        pplItemDO.setTotalGpuNumApplyBefore(before.getTotalGpuNumApplyBefore());

        int row = demandDBHelper.update(pplItemDO);
        if (row == 0) {
            throw new BizException(
                    "ppl_id:" + pplItemDO.getPplId() + "更新失败，属于系统异常，为保证数据完整性，请先联系开发人员排查");
        }
        PplItemDO after = demandDBHelper.getOne(PplItemDO.class, "where id=?", pplItemDO.getId());

        // 写入recordDO
        PplItemRecordDO recordDO = new PplItemRecordDO();
        recordDO.setPplId(pplItemDO.getPplId());
        recordDO.setBeforeJson(JSON.toJson(before));
        recordDO.setAfterJson(JSON.toJson(after));
        if (eventDTO != null) {
            eventDTO.fill(recordDO);
        }
        demandDBHelper.insert(recordDO);

        // 记录日志
        PplItemChangeRecordNewDTO recordNewDTO = new PplItemChangeRecordNewDTO();
        PplItemChangeAllFieldDTO beforeAllFieldDTO = new PplItemChangeAllFieldDTO();
        BeanUtils.copyProperties(before, beforeAllFieldDTO);
        PplItemChangeAllFieldDTO afterAllFieldDTO = new PplItemChangeAllFieldDTO();
        BeanUtils.copyProperties(pplItemDO, afterAllFieldDTO);
        recordNewDTO.setBeforeItem(beforeAllFieldDTO);
        recordNewDTO.setAfterItem(afterAllFieldDTO);
        SpringUtil.getApplicationContext().publishEvent(
                new PplChangeEvent(new Object(), "CHANGE_RECORD", PplRecordChangeTypeEnum.VALID_CHANGE.getCode(),
                        PplRecordChangeEventEnum.YUN_SUBMITTED.getCode(), LoginUtils.getUserNameWithSystem(),
                        Collections.singletonList(recordNewDTO)));

    }
}
