package cloud.demand.app.modules.p2p.longterm.service;

import cloud.demand.app.modules.p2p.longterm.dto.LongTermUnifiedVersionDTO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;

public interface LongTermVersionService {

    /**
     * 创建中长期行业版本
     * @param req
     */
    void createLongTermVersion(LongTermUnifiedVersionDTO req);

    /**
     * 创建中长期行业版本
     * @param req
     */
    void updateLongTermVersion(LongTermUnifiedVersionDTO req);


    /**
     * 开启版本
     * @param versionCode
     */
    void startLongTermVersion(String versionCode);

    /**
     * 发送版本未提交消息
     */
    void sendNotSubmitMsg();

    /**
     * 发送版本预测有错误的信息
     */
    void sendItemErrorMsg();

    /**
     * 关闭版本
     * @param versionCode
     */
    void closeLongTermVersion(String versionCode);


    /**
     * 查询给定的版本编码的上一期的版本编码
     * @param versionCode 版本编码
     * @return 不存在上一期则返回null
     */
    LongtermVersionDO getPreviousDoneVersion(String versionCode);

}

