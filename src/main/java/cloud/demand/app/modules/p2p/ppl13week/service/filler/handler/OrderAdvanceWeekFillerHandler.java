package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.modules.order.dto.OrderTagResultItemDto;
import cloud.demand.app.modules.order.entity.PplOrderTagLogDO;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderAdvanceWeekFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class OrderAdvanceWeekFillerHandler implements FillerHandler<OrderAdvanceWeekFiller> {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public void fill(List<OrderAdvanceWeekFiller> obj) {
        BatchUtil.syncBatchExec(obj, 8000, this::handler);
    }

    private void handler(List<OrderAdvanceWeekFiller> obj) {
        Set<String> orderNumberIds = ListUtils.toSet(obj, OrderAdvanceWeekFiller::provideOrderNumberId);
        List<PplOrderTagLogDO> tags = demandDBHelper.getAll(PplOrderTagLogDO.class,
                "where order_number_id in (?)", orderNumberIds);
        Map<String, PplOrderTagLogDO> map = ListUtils.toMap(tags, PplOrderTagLogDO::getOrderNumberId, Function.identity());
        for (OrderAdvanceWeekFiller filler : obj) {
            PplOrderTagLogDO tag = map.get(filler.provideOrderNumberId());
            if (tag == null) {
                continue;
            }
            filler.fillAdvanceWeek(tag.getAdvanceWeek());
            List<OrderTagResultItemDto> realTagResult = tag.getRealTagResult();
            int core1To4 = 0;
            int core5To8 = 0;
            int core9To12 = 0;
            int core13 = 0;
            for (OrderTagResultItemDto item : realTagResult) {
                if (item == null || item.getAdvanceWeek() == null) {
                    continue;
                }
                int week = item.getAdvanceWeek();
                if (week >= 0 && week <= 4) {
                    core1To4 += item.getCore();
                } else if (week >= 5 && week <= 8) {
                    core5To8 += item.getCore();
                } else if (week >= 9 && week <= 12) {
                    core9To12 += item.getCore();
                } else if (week >= 13) {
                    core13 += item.getCore();
                }
            }
            filler.fillAdvanceWeekCore1to4(core1To4);
            filler.fillAdvanceWeekCore5to8(core5To8);
            filler.fillAdvanceWeekCore9to12(core9To12);
            filler.fillAdvanceWeekCore13(core13);

            int totalGpu = filler.provideTotalGpu();
            if (totalGpu >= 0) {
                BigDecimal rate = BigDecimal.ZERO;
                if (totalGpu > 0 && tag.getOrderCore() != null && tag.getOrderCore() != 0) {
                    rate = new BigDecimal(totalGpu)
                            .divide(new BigDecimal(tag.getOrderCore()), 8, RoundingMode.HALF_UP);
                }
                int gpu1To4 = rate.multiply(new BigDecimal(core1To4))
                        .setScale(0, RoundingMode.HALF_UP).intValue();
                int gpu5To8 = rate.multiply(new BigDecimal(core5To8))
                        .setScale(0, RoundingMode.HALF_UP).intValue();
                int gpu9To12 = rate.multiply(new BigDecimal(core9To12))
                        .setScale(0, RoundingMode.HALF_UP).intValue();
                int gpu13 = rate.multiply(new BigDecimal(core13))
                        .setScale(0, RoundingMode.HALF_UP).intValue();
                filler.fillAdvanceWeekGpu1to4(gpu1To4);
                filler.fillAdvanceWeekGpu5to8(gpu5To8);
                filler.fillAdvanceWeekGpu9to12(gpu9To12);
                filler.fillAdvanceWeekGpu13(gpu13);
            }

        }
    }

}
