package cloud.demand.app.modules.p2p.product_demand.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("product_demand_approve_record")
public class ProductDemandApproveRecordDO {

    /**
     * 自增id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 需求组Id<br/>Column: [demand_group_id]
     */
    @Column(value = "demand_group_id")
    private Long demandGroupId;

    /**
     * 审批节点<br/>Column: [status]
     */
    @Column(value = "status")
    private String status;

    /**
     * 审批人<br/>Column: [approver]
     */
    @Column(value = "approver")
    private String approver;

    /**
     * 审批意见<br/>Column: [approve_msg]
     */
    @Column(value = "approve_msg")
    private String approveMsg;

    /**
     * 审批结果<br/>Column: [action]
     */
    @Column(value = "action")
    private String action;

    /**
     * 扩展字段-可存单据当前状态<br/>Column: [ext_json]
     */
    @Column(value = "ext_json")
    private String extJson;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

}
