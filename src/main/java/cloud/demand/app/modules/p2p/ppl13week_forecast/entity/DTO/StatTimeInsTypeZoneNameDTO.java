package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO;


import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StatTimeInsTypeZoneNameDTO {

    @Column(value = "stat_time")
    private LocalDate statTime;

    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "country")
    private String country;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "value")
    private BigDecimal value;

}
