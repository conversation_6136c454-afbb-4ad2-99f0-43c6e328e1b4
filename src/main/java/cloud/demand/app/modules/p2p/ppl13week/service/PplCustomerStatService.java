package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryApplyDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDistributedReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerDistributedResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerGlobalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stat.QueryCustomerGlobalResp;

public interface PplCustomerStatService {

    /**
     * 查询客户构成概览
     */
    QueryCustomerGlobalResp queryCustomerGlobal(QueryCustomerGlobalReq req);

    /**
     * 查询客户分布
     */
    QueryCustomerDistributedResp queryCustomerDistributed(QueryCustomerDistributedReq req);

    /**
     * 客户构成 - 明细
     */
    QueryCustomerDetailResp queryCustomerDistributedDetail(QueryCustomerDistributedReq req);

    /**
     * 预约执行 - 明细
     */
    QueryApplyDetailResp queryApplyDetail(QueryCustomerDistributedReq req);
}
