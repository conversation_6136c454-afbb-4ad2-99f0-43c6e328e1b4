package cloud.demand.app.modules.p2p.ppl13week.vo;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.ForecastKey;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.pugwoo.dbhelper.annotation.RelatedColumn;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.nutz.lang.Lang;


@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PplVersionGroupRecordItemWithOrderVO extends PplVersionGroupRecordItemDO implements ForecastKey {

    @RelatedColumn(localColumn = "ppl_order", remoteColumn = "ppl_order")
    private PplOrderDO pplOrderDO;

    @RelatedColumn(localColumn = "ppl_id", remoteColumn = "ppl_id", conditional = "t.status != 'VERSION_IMPORT'")
    private PplItemDO itemDO;


    /**
     * 要当前版本上一个月的 退回数据
     */
    private Boolean fromItem = false;

    private String yearMonth;


    public String getIndustry() {
        return pplOrderDO == null ? "" : pplOrderDO.getIndustry();
    }

    public String getIndustryDept() {
        return pplOrderDO == null ? "" : pplOrderDO.getIndustryDept();
    }

    public String getCustomerType() {
        return pplOrderDO == null ? "" : pplOrderDO.getCustomerType();
    }

    public String getCustomerUin() {
        return pplOrderDO == null ? "" : pplOrderDO.getCustomerUin();
    }

    public String getCustomerShortName() {
        return pplOrderDO == null ? "" : pplOrderDO.getCustomerShortName();
    }

    public String getCustomerSource() {
        return pplOrderDO == null ? "" : pplOrderDO.getCustomerSource();
    }

    public String getWarZone() {
        return pplOrderDO == null ? "" : pplOrderDO.getWarZone();
    }

    public String getYearMonth() {
        if (itemDO == null) {
            return "";
        }
        LocalDate beginBuyDate = itemDO.getBeginBuyDate();
        return beginBuyDate.getYear() + "-" + (beginBuyDate.getMonthValue() >= 10 ? beginBuyDate.getMonthValue()
                : "0" + beginBuyDate.getMonthValue());
    }

    /**
     * 获取分组K
     */
    public String getGroupK() {
        return String.join("-",
                this.getYearMonth(), this.getCustomerShortName(), this.getCustomerUin(), this.getDemandScene(),
                this.getBillType(), this.getRegionName(), this.getDemandType(), this.getInstanceType(),
                this.getInstanceModel());
    }

    public static PplVersionGroupRecordItemWithOrderVO copyOne(PplVersionGroupRecordItemWithOrderVO one) {
        PplOrderDO pplOrderDO = one.getPplOrderDO();
        PplVersionGroupRecordItemWithOrderVO copy = new PplVersionGroupRecordItemWithOrderVO();
        copy.setDemandType(one.getDemandType());
        copy.setDemandScene(one.getDemandScene());
        copy.setBillType(one.getBillType());
        copy.setRegionName(one.getRegionName());
        copy.setInstanceType(one.getInstanceType());
        copy.setInstanceModel(one.getInstanceModel());
        copy.setInstanceNum(0);
        copy.setTotalCore(0);
        copy.setTotalDisk(0);
        if (pplOrderDO != null) {
            PplOrderDO copyOrder = new PplOrderDO();
            copyOrder.setCustomerShortName(pplOrderDO.getCustomerShortName());
            copyOrder.setCustomerUin(pplOrderDO.getCustomerUin());
            copy.setPplOrderDO(copyOrder);
        }
        return copy;
    }

    @Override
    public String getGinsFamily() {
        return this.getInstanceType();
    }

    @Override
    public String getForecastSeqType() {
        return PplDemandTypeEnum.getForecastType(this.getDemandType());
    }

    @Override
    public Integer getMonth() {
        return this.getBeginBuyDate().getMonthValue();
    }

    @Override
    public Integer getYear() {
        return this.getBeginBuyDate().getYear();
    }
}
