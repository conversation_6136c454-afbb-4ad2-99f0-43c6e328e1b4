package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.Getter;

@Getter
public enum PplJoinOrderVersionItemDataSource {


    ORDER("ORDER", "订单"),

    VERSION("VERSION", "版本"),

    EXTEND("EXTEND", "继承"),

    ;
    final private String code;
    final private String name;

    PplJoinOrderVersionItemDataSource(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplJoinOrderVersionItemDataSource getByCode(String code) {
        for (PplJoinOrderVersionItemDataSource e : PplJoinOrderVersionItemDataSource.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplJoinOrderVersionItemDataSource e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}
