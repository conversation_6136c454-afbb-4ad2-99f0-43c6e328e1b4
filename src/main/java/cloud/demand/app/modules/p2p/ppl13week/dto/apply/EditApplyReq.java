package cloud.demand.app.modules.p2p.ppl13week.dto.apply;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.EditOrderReq;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.string.StringTools;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 编辑预约单
 */
@Data
public class EditApplyReq {

    private String yunxiaoOrderId; // 预约单号
    private String beginBuyDate; // 开始购买日期
    private String endBuyDate; // 结束购买日期
    private String beginElasticTime; // 开始弹性时间
    private String endElasticTime; // 结束弹性时间
    private String region; // 地域，中文

    private List<EditResourceDetailDTO> resourceDetails; // 资源列表

    @Data
    public static class EditResourceDetailDTO {
        private String type; // 编辑：update delete
        private Long id;
        private String payMode;
        private String zoneName; // 可用区，中文
        private String instanceModel;
        private String systemDiskType;
        private Integer systemDiskSize;
        private String dataDiskType;
        private Integer dataDiskSize;
        private Integer dataDiskCount;
        private Integer applyCount;
    }

    public void fillToEditReq(EditOrderReq editReq, Map<String, String> regionNameToCode,
                              Map<String, String> zoneNameToCode) {
        if (StringTools.isNotBlank(beginBuyDate)) {
            editReq.setExpectTime(beginBuyDate);
        }
        if (StringTools.isNotBlank(endBuyDate)) {
            editReq.setLatestExpectTime(endBuyDate);
        }
        if (StringTools.isNotBlank(beginElasticTime)) {
            editReq.setAsStartTime(beginElasticTime);
        }
        if (StringTools.isNotBlank(endElasticTime)) {
            editReq.setAsEndTime(endElasticTime);
        }
        if (StringTools.isNotBlank(region)) {
            String regionCode = regionNameToCode.get(region);
            if (StringTools.isBlank(regionCode)) {
                throw new WrongWebParameterException("未知地域：" + region + ", 编辑失败");
            }
        }

        ListUtils.forEach(resourceDetails, o -> {
            if ("update".equals(o.getType())) {
                if (o.getId() == null) {
                    throw new WrongWebParameterException("编辑的资源没有传id");
                }
                List<EditOrderReq.OrderDetails> matched = ListUtils.filter(
                        editReq.getOrderDetails(), e -> Objects.equals(o.getId(), e.getId()));
                if (matched.isEmpty()) {
                    throw new WrongWebParameterException("编辑的资源id不存在，请刷新页面重试");
                }
                EditOrderReq.OrderDetails orderDetails = matched.get(0);
                if (StringTools.isNotBlank(o.getPayMode())) {
                    orderDetails.setPayMode(o.getPayMode());
                }
                if (StringTools.isNotBlank(o.getZoneName())) {
                    String zoneCode = zoneNameToCode.get(o.getZoneName());
                    if (StringTools.isBlank(zoneCode)) {
                        throw new WrongWebParameterException("可用区" + o.getZoneName() + "不存在");
                    }
                    orderDetails.setZone(zoneCode);
                }
                if (StringTools.isNotBlank(o.getInstanceModel())) {
                    orderDetails.setInstanceType(o.getInstanceModel());
                }
                if (StringTools.isNotBlank(o.getSystemDiskType())) {
                    orderDetails.setSysDiskType(o.getSystemDiskType());
                }
                if (o.getSystemDiskSize() != null) {
                    orderDetails.setSysDiskSize(o.getSystemDiskSize());
                }
                if (StringTools.isNotBlank(o.getDataDiskType())) {
                    orderDetails.setDataDiskType(o.getDataDiskType());
                }
                if (o.getDataDiskSize() != null) {
                    orderDetails.setDataDiskSize(o.getDataDiskSize());
                }
                if (o.getDataDiskCount() != null) {
                    orderDetails.setDataDiskCount(o.getDataDiskCount());
                }
                if (o.getApplyCount() != null) {
                    orderDetails.setApplyCount(o.getApplyCount());
                }
            } else if ("delete".equals(o.getType())) {
                if (o.getId() == null) {
                    throw new WrongWebParameterException("删除的资源没有传id");
                }
                if (editReq.getOrderDetails() != null) {
                    editReq.getOrderDetails().removeIf(next -> Objects.equals(o.getId(), next.getId()));
                }
            } else {
                throw new WrongWebParameterException("未知的操作类型：" + o.getType());
            }
        });
    }

}
