package cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply;

import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;

/**
 * 库存对冲满足方式
 */
@Getter
public enum PplStockSupplyMatchTypeEnum {

    /**
     * SATISFY  现网满足
     * 行业的资源需求，可以通过产品现网库存满足
     */
    SATISFY("SATISFY", "库存满足"),

    /**
     * BUY 采购满足
     * 行业的资源需求，产品需要通过采购物理机满足
     */
    BUY("BUY", "采购满足"),

    /**
     * MOVE  搬迁满足
     * 行业的资源需求，产品需要通过搬迁异地的库存满足
     */
    MOVE("MOVE", "搬迁满足"),

    /**
     * FAIL 无法满足
     * 行业的资源需求，不符合产品供应策略，且产品无法提供引导，需求无法满足；
     */
    FAIL("FAIL", "无法满足"),

    /*
     * 2023-06-27 delete
     */
//    REJECT("REJECT", "对冲驳回"),

    /**
     * SUGGEST 库存引导
     * 行业的原始需求产品无法满足，但产品可以使用不同的机型或者地域来满足；
     */
    SUGGEST("SUGGEST", "库存引导"),

    /**
     * <a href="https://iwiki.woa.com/p/4009356318">2023-12-27 新增采购引导</a>
     * SUGGEST_BUY 采购引导
     * 行业的原始需求产品无法满足，但产品可以采购不同需求时间、不同可用区、不同机型来满足；
     */
    SUGGEST_BUY("SUGGEST_BUY", "采购引导"),
    ;

    final private String code;
    final private String name;

    PplStockSupplyMatchTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplStockSupplyMatchTypeEnum getByCode(String code) {
        for (PplStockSupplyMatchTypeEnum e : PplStockSupplyMatchTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getCodeByName(String name) {
        for (PplStockSupplyMatchTypeEnum e : PplStockSupplyMatchTypeEnum.values()) {
            if (Strings.equals(name, e.getName())) {
                return e.getCode();
            }
        }
        return "";
    }


    public static String getNameByCode(String code) {
        PplStockSupplyMatchTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

    /**
     *   根据传入的满足方式编码按分隔符进行分割后获取满足方式名称拼接的字符串
     * @param code 用指定分隔符拼接而成的满足方式编码
     * @param delimiter 分隔符
     * @return 用分隔符拼接而成的满足方式名称
     */
    public static String getNameByCodeAndDelimiter(String code, String delimiter) {
        List<String> list = getNameListByCodeAndDelimiter(code, delimiter);
        if (ListUtils.isEmpty(list)) {
            return "";
        }
        StringJoiner joiner = new StringJoiner(delimiter);
        list.forEach(joiner::add);
        return joiner.toString();
    }

    /**
     *   根据传入的满足方式编码按分隔符进行分割后获取满足方式名称集合
     * @param code 用指定分隔符拼接而成的满足方式编码
     * @param delimiter 分隔符
     * @return 满足方式名称集合
     */
    public static List<String> getNameListByCodeAndDelimiter(String code, String delimiter) {
        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(code)) {
            return result;
        }
        String[] array;
        if (StringUtils.isBlank(delimiter)) {
            array = new String[]{code};
        } else {
            array = code.split(delimiter);
        }
        for (String s : array) {
            PplStockSupplyMatchTypeEnum e = getByCode(s);
            if (e != null) {
                result.add(e.getName());
            }
        }
        return result;
    }

    public static PplStockSupplyMatchTypeEnum getCbs(String matchType) {

        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchType)) {
            return BUY;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.MOVE.getCode(), matchType)) {
            return MOVE;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.SATISFY.getCode(), matchType)) {
            return SATISFY;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.FAIL.getCode(), matchType)) {
            return FAIL;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.SUGGEST.getCode(), matchType)) {
            return SUGGEST;
        }
        return null;
    }

    public static PplStockSupplyMatchTypeEnum getCvm(String matchType) {

        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchType)) {
            return BUY;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.MOVE.getCode(), matchType)) {
            return MOVE;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.SATISFY.getCode(), matchType)) {
            return SATISFY;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.SUGGEST.getCode(), matchType)) {
            return SUGGEST;
        }
        if (Strings.equalsIgnoreCase(PplStockSupplyMatchTypeEnum.FAIL.getCode() , matchType)) {
            return FAIL;
        }
        return null;
    }

    public static String getNameOrDefault(String matchTypeCode, String s) {
        /**
         *  这里如果相同名字合并
         */
        if (Lang.list(PplStockSupplyMatchTypeEnum.MOVE.getCode(),
                PplStockSupplyMatchTypeEnum.SATISFY.getCode()).contains(matchTypeCode)) {
            return PplStockSupplyMatchTypeEnum.SATISFY.getName();
        }
        String nameByCode = getNameByCode(matchTypeCode);
        if (Strings.isBlank(nameByCode)) {
            return s;
        }
        return nameByCode;
    }

    public static String getCvmColumnName(String matchTypeCode) {
        ImmutableMap<String, String> columnNameMap = ImmutableMap.of(PplStockSupplyMatchTypeEnum.BUY.getCode(), "采购机型",
                PplStockSupplyMatchTypeEnum.MOVE.getCode(), "满足实例类型",
                PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                "满足实例类型", PplStockSupplyMatchTypeEnum.FAIL.getCode(), "驳回实例类型");
        PplStockSupplyMatchTypeEnum byCode = getByCode(matchTypeCode);
        if (byCode == null) {
            return "未分类";
        }
        return columnNameMap.get(byCode.getCode());
    }
}