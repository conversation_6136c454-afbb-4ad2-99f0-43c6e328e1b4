package cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.cvm;

import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.ImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.PplStrategyCvmImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplExportWriteHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.AbstractInnerPplExcelParseService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionVO;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

/**
 * 战略客户部CVM导入行业内excel的流程
 */
@Service
@Slf4j
public class PplExcelImportForStrategyCvmService extends AbstractInnerPplExcelParseService {

    @Resource
    private PplDictService pplDictService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplInnerVersionService innerVersionService;

    @Override
    public boolean support(String productType, String industryDept) {
        return (productType.equals(Ppl13weekProductTypeEnum.CVM.getName())
                || productType.equals(Ppl13weekProductTypeEnum.BM.getName())
                || productType.equals(Ppl13weekProductTypeEnum.EMR.getName())
                || productType.equals(Ppl13weekProductTypeEnum.EKS.getName())
                || productType.equals(Ppl13weekProductTypeEnum.ES.getName())
                || productType.equals(Ppl13weekProductTypeEnum.CDW.getName())
                || productType.equals(Ppl13weekProductTypeEnum.PAAS.getName()))
                && industryDept.equals(
                IndustryDeptEnum.STRATEGY.getName());
    }

    @Override
    public List<PplImportExcelDTO> decodeExcel(MultipartFile file, String product) {
        List<PplImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplStrategyCvmImportExcelDTO.class,
                    new AnalysisEventListener<PplStrategyCvmImportExcelDTO>() {
                        @Override
                        public void invoke(PplStrategyCvmImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplImportExcelDTO i = PplStrategyCvmImportExcelDTO.copyToNewDTO(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }

    @Override
    public PplItemImportRsp execute(MultipartFile file, ImportExcelDTO importExcelDTO) {
        String product = importExcelDTO.getProduct();
        YearMonth startYearMonth = DateUtils.parse(importExcelDTO.getStartYearMonth());
        YearMonth endYearMonth = DateUtils.parse(importExcelDTO.getEndYearMonth());
        List<String> customer = importExcelDTO.getCustomer();
        String industryDept = importExcelDTO.getIndustryDept();
        String userName = LoginUtils.getUserName();
        checkImportPermission(industryDept, product, userName);
        //解析excel
        List<PplImportExcelDTO> uploadData = decodeExcel(file, importExcelDTO.getProduct());

        //获取uin
        List<String> uins = uploadData.stream()
                .map(PplImportExcelDTO::getCustomerUin)
                .filter(Strings::isNotBlank)
                .distinct().collect(Collectors.toList());
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = queryInfoByUinRspHashMap(uins);

        PplInnerVersionVO pplInnerVersionVO = innerVersionService.queryVersionVO(industryDept, product);
        List<String> overseas = pplDictService.queryAllRegionName(true);

        Map<String, String> customerNameToWarZoneName = industryDemandDictService.customerName2WarZoneNameMap();

        CheckYunxiaoReq checkYunxiaoReq = buildCheckYunxiaoReq();

        if (CollectionUtils.isEmpty(customer)) {
            customer = queryCustomerList(industryDept);
        }

        // 获取已预约的item信息
        List<String> bizIds = uploadData.stream().map(PplImportExcelDTO::getBizId).filter(Strings::isNotBlank).collect(
                Collectors.toList());
        Map<String, PplOrderDO> appliedPplOrderMap = new HashMap<>();
        Map<String, List<PplItemDO>> appliedPplItemMap = new HashMap<>();
        if (ListUtils.isNotEmpty(bizIds)) {
            List<PplItemJoinOrderVO> appliedItems = demandDBHelper.getAll(PplItemJoinOrderVO.class,
                    "where t1.biz_id in(?) and t1.status = ? ",
                    bizIds, PplItemStatusEnum.APPLIED.getCode());
            appliedPplItemMap = appliedItems.stream().map(PplItemJoinOrderVO::getItemDO)
                    .collect(Collectors.groupingBy(PplItemDO::getBizId, Collectors.toList()));
            appliedPplOrderMap = appliedItems.stream().map(PplItemJoinOrderVO::getPplOrderDO)
                    .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v, (v1, v2) -> v1));
        }

        List<GroupItemDTO> retData = Lang.list();
        List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();

        for (int i = 0; i < uploadData.size(); i++) {
            PplImportExcelDTO oneData = uploadData.get(i);
            int row = i + 3;
            int beginErrorSize = errors.size();
            PplItemImportRsp.ErrorMessage error = null;

            String warZoneName = "";
            // 校验是否在导入的客户范围内
            if (!CollectionUtils.isEmpty(customer)) {
                String customerShortName = "";
                if (Strings.isNotBlank(oneData.getCustomerUin())) {
                    if (uinMapInfo.get(oneData.getCustomerUin()) != null) {
                        customerShortName = uinMapInfo.get(oneData.getCustomerUin()).getCustomerShortName();
                    }
                }
                // uin 的优先级要高于用户填写的 客户简称
                if (Strings.isNotBlank(customerShortName)) {
                    oneData.setCustomerShortName(customerShortName);
                }
                error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getCustomerShortName,
                        customer);
                errors.add(error);
                if (error == null) {
                    warZoneName = customerNameToWarZoneName.get(oneData.getCustomerShortName());
                    if (warZoneName == null) {
                        errors.add(makeError(row, PplImportExcelDTO::getCustomerShortName,
                                "当前客户: " + customerShortName + " 未关联上战区"));
                    }
                    oneData.setWarZone(warZoneName);
                }
            }

            // 校验bizId
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBizId);
            errors.add(error);
//            if(oneData.getBizId() != null){
//                PplItemDO pplItemDO = demandDBHelper.getOne(PplItemDO.class, "where biz_id = ?", oneData.getBizId());
//                if (pplItemDO != null){
//                    LocalDate start = LocalDate.of(startYearMonth.getYear(), startYearMonth.getMonth(), 1);
//                    LocalDate end = LocalDate.of(endYearMonth.getYear(), endYearMonth.getMonth()+1, 1);
//                    if (pplItemDO.getBeginBuyDate().isBefore(start) || pplItemDO.getBeginBuyDate().isAfter(end)){
//                        error = makeError(row,PplImportExcelDTO::getBizId,"该业务标识ID: " + oneData.getBizId() + "已存在于系统中，且需求购买时间不在当前年月筛选范围内");
//                        errors.add(error);
//                    }
//                }
//            }

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getCustomerTypeName,
                    CustomerTypeEnum.names());
            errors.add(error);

            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getProjectName);
            errors.add(error);

            // 校验已预约明细主key信息和资源量
            List<PplItemDO> appliedPplItems = appliedPplItemMap.get(oneData.getBizId());
            PplOrderDO appliedPplOrder =
                    ListUtils.isNotEmpty(appliedPplItems) ? appliedPplOrderMap.get(appliedPplItems.get(0).getPplOrder())
                            : null;
            checkAppliedItemForStrategy(errors, oneData, appliedPplOrder, appliedPplItems, row);

            //校验uin
            QueryInfoByUinRsp queryInfoByUinRsp = checkUin(errors, oneData, row, uinMapInfo);

            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());

            //校验需求
            checkDemandAndBillType(errors, oneData, row);

            //校验赢率
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getWinRate);
            errors.add(error);
            BigDecimal winRate = checkWinRate(errors, oneData, row);

            //各时间参数校验
            DateDTO dateDTO = checkInnerTime(errors, oneData, row, startYearMonth, endYearMonth);

            // 云霄方CVM相关校验 地域/可用区/实例类型/实例配置/核心数/内存数
            List<String> alternativeInstances = new ArrayList<>();
            checkYunXiaoRelevant(errors, oneData, row, alternativeInstances, product, false, checkYunxiaoReq);
            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            Integer num = NumberUtils.parseInt(oneData.getInstanceNum());

            // CBS方相关校验  磁盘类型/数量
            checkCBSRelevant(errors, oneData, row, product);

            // 海外跨月需求校验
            checkOverseasCrossMonth(errors,oneData,row,pplInnerVersionVO,overseas);

            // 是否强可用区校验
            strongDesignateZoneCheck(errors,oneData,row);

            // 校验强指定可用区需求
            Integer systemStorage = NumberUtils.parseInt(oneData.getSystemDiskStorage());
            Integer diskNum = NumberUtils.parseInt(oneData.getDataDiskNum());
            Integer diskStorage = NumberUtils.parseInt(oneData.getDataDiskStorage());

            int oneDiskSize = 0;
            if (systemStorage != null) {
                oneDiskSize += systemStorage;
            }
            if (diskNum != null && diskStorage != null) {
                oneDiskSize += diskNum * diskStorage;
            }

            // 如果该条ppl没有产生error 则直接转换为 DTO
            if (beginErrorSize == errors.size()) {

                GroupItemDTO tmp = GroupItemDTO.trans(oneData);
                retData.add(tmp);
                if (customerTypeEnum != null) {
                    tmp.setCustomerType(customerTypeEnum.getCode());
                }
                tmp.setYear(dateDTO.getYear());
                tmp.setMonth(dateDTO.getMonth());
                tmp.setYearMonth(dateDTO.getYearMonth());
                tmp.setCustomerShortName(oneData.getCustomerShortName());
                if (queryInfoByUinRsp != null) {
                    if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                        tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
                    }
                    if (Strings.isBlank(tmp.getCustomerShortName())) {
                        tmp.setCustomerTypeName(tmp.getCustomerName());
                    }
                    tmp.setIndustry(queryInfoByUinRsp.getIndustry());
                    tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
                    tmp.setWarZone(warZoneName);
                    tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
                }
                tmp.setWinRate(winRate);
                tmp.setBeginBuyDate(dateDTO.getBeginBuyDateRet());
                tmp.setEndBuyDate(dateDTO.getEndBuyDateRet());
                tmp.setBeginElasticDate(dateDTO.getBeginElasticDateRet());
                tmp.setEndElasticDate(dateDTO.getEndElasticDateRet());
                tmp.setTotalCoreNum(coreNum * (num == null ? 0 : num));
                tmp.setTotalDiskNum(oneDiskSize * (num == null ? 0 : num));
                tmp.setInstanceModelCoreNum(coreNum);
                tmp.setInstanceModelRamNum(ramNum);
                tmp.setStatus(PplItemStatusEnum.VALID.getCode());
                tmp.setStatusName(PplItemStatusEnum.VALID.getName());
                tmp.setAffinityValue(NumberUtils.parseBigDecimal(oneData.getAffinityValue()));
                if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
                    tmp.setAlternativeInstanceType(alternativeInstances);
                }
                tmp.setAlternativeInstanceType(alternativeInstances);
                tmp.setProduct(product);

                // 2022-12-13 增加推荐机型
                List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(tmp.getZoneName());

                boolean isMainInstanceType = mainInstanceTypes.contains(tmp.getInstanceType());
                tmp.setIsRecommendedInstanceType(isMainInstanceType);
                tmp.setBizId(oneData.getBizId());
            }
        }

        return new PplItemImportRsp(errors.size() == 0, errors, retData);
    }

    @Override
    public DownloadBean export(String industryDept, String product, List<GroupItemDTO> groupItemDTOList) {
        InputStream templateIn = IOUtils.readClasspathResourceInputStream(
                "excel/inner_process/strategy_cvm_import.xlsx");
        String fileName = industryDept + "-" + product + "-PPL数据导出";

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        HashMap<String, Integer> config = new HashMap<>();

        List data = new ArrayList<>();
        List dictData = new ArrayList();

        data = PplStrategyCvmImportExcelDTO.transFrom(groupItemDTOList);
        dictData.addAll(getStrategyCvmDictData(industryDept, product, config));

        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new PplExportWriteHandler(config))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        WriteSheet dictSheet = EasyExcel.writerSheet("字典").build();

        excelWriter.
                fill(new FillWrapper("item", data), writeSheet)
                .write(dictData, dictSheet)
                .finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                fileName + com.pugwoo.wooutils.lang.DateUtils.format(new Date(), "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }


    ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 战略部特殊字典
     *
     * @param config
     * @return
     */
    @SneakyThrows
    private List<PplStrategyCvmImportExcelDTO> getStrategyCvmDictData(String industryDept, String product,
            HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return pplDictService.queryAllCityName(null);
        });
        Future<List<IndustryDemandRegionZoneInstanceTypeDictDO>> zonesFuture = executorService.submit(() -> {
            return pplDictService.queryAllZoneName();
        });
        Future<List<String>> instanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceType("");
        });
        Future<List<String>> instanceModelFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceModel(null, null);
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });

        Boolean isBM = product.equals(Ppl13weekProductTypeEnum.BM.getName());
        List<String> warZoneList = getIndustryWarZone(industryDept);
        List<IndustryDemandRegionZoneInstanceTypeDictDO> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> instanceType = instanceTypeFuture.get();
        List<String> instanceModel = instanceModelFuture.get();
        instanceType = isBM ? instanceType.stream().filter(v -> v.startsWith("BM")).collect(Collectors.toList()) :
                instanceType.stream().filter(v -> !v.startsWith("BM")).collect(Collectors.toList());
        instanceModel = isBM ? instanceModel.stream().filter(v -> v.startsWith("BM")).collect(Collectors.toList()) :
                instanceModel.stream().filter(v -> !v.startsWith("BM")).collect(Collectors.toList());
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();

        List<String> AffType = Lang.list("母机", "交换机", "机柜");
        List<String> systemDiskTypes = Lang.list("SSD云硬盘", "高性能云硬盘", "通用型SSD云硬盘");
        List<String> dateDiskTypes = Lang.list("SSD云硬盘", "高性能云硬盘", "通用型SSD云硬盘", "增强型SSD云硬盘");
        List<String> billType = Lang.list("包年包月", "按量计费", "竞价实例");

        List<String> winRateRange = Lang.list();

        config.put("D", warZoneList.size());
        config.put("E", demandType.size());
        config.put("F", demandScene.size());
        config.put("H", billType.size());
        config.put("I", 101);
        config.put("O", cityNames.size());
        config.put("P", zones.size());
        config.put("Q", instanceType.size());
        config.put("R", instanceModel.size());
        config.put("S", parseCpu.size());
        config.put("T", parseRam.size());
        config.put("X", 2);
        config.put("Z", AffType.size());
        config.put("AB", systemDiskTypes.size());
        config.put("AD", dateDiskTypes.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }

        List<PplStrategyCvmImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplStrategyCvmImportExcelDTO one = new PplStrategyCvmImportExcelDTO();

            if (i < warZoneList.size()) {
                one.setWarZone(warZoneList.get(i));
            }
            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(parseCpu.get(i));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(parseRam.get(i));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setIsAcceptAlternative("是");
                one.setZoneName("随机可用区");
            }
            if (i == 1) {
                one.setIsAcceptAlternative("否");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i < instanceType.size()) {
                one.setInstanceType(instanceType.get(i));
            }
            if (i < instanceModel.size()) {
                one.setInstanceModel(instanceModel.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i).getZoneName());
            }
            if (i < AffType.size()) {
                one.setAffinityType(AffType.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < systemDiskTypes.size()) {
                one.setSystemDiskType(systemDiskTypes.get(i));
            }
            if (i < dateDiskTypes.size()) {
                one.setDataDiskType(dateDiskTypes.get(i));
            }
            ret.add(one);
        }
        return ret;
    }

}
