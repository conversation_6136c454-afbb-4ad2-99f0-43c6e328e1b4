package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupApproveResultEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 版本分组概览详情
 */
@Data
public class VersionGroupApproveResp {

    /**
     * 当前状态
     */
    private String curStatus;
    /**
     * 当前处理人
     */
    private String handler;

    /**
     * 是否可撤回
     */
    private Boolean revocable = Boolean.FALSE;

    /**
     * 完整的流程节点flow
     */
    private List<FlowStatusDTO> flow;

    /**
     * 审批记录
     */
    private List<StatusLogDTO> logs;

    @Data
    public static class FlowStatusDTO {

        private String status;
        private String statusName;

        public static FlowStatusDTO from(PplVersionGroupStatusEnum e) {
            FlowStatusDTO flowStatusDTO = new FlowStatusDTO();
            flowStatusDTO.setStatus(e.getCode());
            flowStatusDTO.setStatusName(e.getName());
            return flowStatusDTO;
        }
    }

    @Data
    public static class StatusLogDTO {

        private String status;
        private String statusName;
        /**
         * 操作人
         */
        private String approver;
        /**
         * 操作时间
         */
        private Date createTime;
        /**
         * 操作
         */
        private String action;
        /**
         * 操作内容
         */
        private String approveMsg;

        public static StatusLogDTO from(PplVersionGroupRecordDO recordDO) {
            StatusLogDTO statusLogDTO = new StatusLogDTO();
            statusLogDTO.setStatus(recordDO.getStatus());
            statusLogDTO.setStatusName(PplVersionGroupStatusEnum.getNameByCode(recordDO.getStatus()));
            statusLogDTO.setApprover(recordDO.getOperateUser());
            statusLogDTO.setCreateTime(recordDO.getApproveTime());
            statusLogDTO.setAction(PplVersionGroupApproveResultEnum.getNameByCode(recordDO.getApproveResult()));
            statusLogDTO.setApproveMsg(recordDO.getApproveNote());
            return statusLogDTO;
        }
    }

}
