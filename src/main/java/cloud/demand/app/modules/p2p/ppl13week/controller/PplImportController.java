package cloud.demand.app.modules.p2p.ppl13week.controller;


import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.QueryPplItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SubmitPplReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.PplOrderImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryPplOrderDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryPplRecordRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.service.PplImportService;
import cloud.demand.app.web.model.common.DownloadBean;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/ppl13week")
@Slf4j
public class PplImportController {


    @Resource
    PplImportService pplImportService;
    @Resource
    DictService dictService;


    /**
     * 查询所有客户简称
     *
     * @return list
     */
    @RequestMapping
    List<String> queryAllCustomShortName() {
        return pplImportService.queryAllCustomShortName();
    }

    /**
     * 查询所有战区
     *
     * @return list
     */
    @RequestMapping
    List<String> queryAllWarZoneName() {
        return pplImportService.queryAllWarZoneName();
    }


    /**
     * item 中的
     */
    @RequestMapping
    List<QueryPplOrderDetailRsp> queryPplItem(@CurrentUser TofUser tofUser, @JsonrpcParam QueryPplItemReq req) {
        List<PplItemDO> ret = pplImportService.queryPplItem(req);
        List<QueryPplOrderDetailRsp> tmp = ListUtils.transform(ret, QueryPplOrderDetailRsp::transFrom);
        tmp.forEach((o) -> o.setCoreNum(dictService.getInstanceCoreNum(o.getInstanceModel())));
        return tmp;
    }

    @RequestMapping
    QueryPplRecordRsp queryPplRecord(@JsonrpcParam QueryPplItemReq req) {

        return pplImportService.queryPplRecord(req.getPplId());
    }


    @RequestMapping
    public PplOrderImportRsp uploadPplOrderDetailExcel(@RequestParam("file") MultipartFile file,
            @RequestParam Map<String, String> params) {

        if (params == null) {
            throw new BizException("参数为空");
        }

        String customerType = params.get("customerType");
        String yearMonth = params.get("yearMonth");

        if (Strings.isBlank(customerType)) {
            throw new BizException("customerType为空");
        }
        if (Strings.isBlank(yearMonth)) {
            throw new BizException("yearMonth为空");

        }

        String realUsername = LoginUtils.getUserName();
        return pplImportService.uploadPplOrderDetailExcel(file, customerType, yearMonth);
    }

    @RequestMapping
    public DownloadBean exportPplOrderFormalExcel() {
        FileNameAndBytesDTO fileNameAndBytesDTO = pplImportService.exportPplOrderFormalExcel();
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }


    @RequestMapping
    public DownloadBean exportPplOrderDetailExcel(@CurrentUser TofUser user, @JsonrpcParam SubmitPplReq req) {

        FileNameAndBytesDTO fileNameAndBytesDTO = pplImportService.exportPplOrderDetailExcel(req);
        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

}
