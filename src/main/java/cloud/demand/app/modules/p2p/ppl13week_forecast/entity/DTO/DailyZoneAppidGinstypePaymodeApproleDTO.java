package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ToString
public class DailyZoneAppidGinstypePaymodeApproleDTO {

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "first_day")
    private LocalDate firstDay;

    @Column(value = "last_day")
    private LocalDate lastDay;

    @Column(value = "last_day_num")
    private BigDecimal lastDayNum;

    @Column(value = "new_diff")
    private BigDecimal newDiff;

    @Column(value = "ret_diff")
    private BigDecimal retDiff;

    @Column(value = "gins_family")
    private String ginsFamily;

    @Column(value = "region")
    private String region;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "customhouse_title")
    private String customhouseTitle;

}
