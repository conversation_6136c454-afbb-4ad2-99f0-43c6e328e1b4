package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import com.pugwoo.wooutils.string.StringTools;
import lombok.Getter;

import java.util.Objects;

/**
 * 云霄订单状态枚举值
 */
@Getter
public enum YunxiaoOrderStatusEnum {

    CREATED("CREATED", "草稿"),

    WAIT_CVM_APPROVAL("WAIT_CVM_APPROVAL", "待CVM审批"),

    WAIT_CBS_APPROVAL("WAIT_CBS_APPROVAL", "待CBS审批"),

    RESOURCES_PREPARE("RESOURCES_PREPARE", "资源准备中"),

    ON_SALE("ON_SALE", "待用户购买"),

    FINISHED("FINISHED", "结束"),

    WAIT_HOST_FLOW("WAIT_HOST_FLOW", "等待搬迁/采购完成"),

    CANCELED("CANCELED", "已取消"),

    BAD_CANCELED("BAD_CANCELED", "已强制取消"),

    REJECTED("REJECTED", "驳回")

    ;

    /**
     * 是否是取消(驳回)状态
     */
    public static boolean isCancelStatus(String status) {
        return StringTools.isIn(status, CANCELED.getCode(), BAD_CANCELED.getCode(), REJECTED.getCode());
    }

    final private String code;
    final private String name;

    YunxiaoOrderStatusEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static YunxiaoOrderStatusEnum getByCode(String code) {
        for (YunxiaoOrderStatusEnum e : YunxiaoOrderStatusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoOrderStatusEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}