package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class UpdateVersionReq {

    @NotNull(message = "版本id必须提供")
    private Long id;

    /**版本编号*/
    private String versionCode;
    /**版本名称*/
    private String versionName;

    private Integer demandBeginYear;
    private Integer demandBeginMonth;

    private Integer demandEndYear;
    private Integer demandEndMonth;

    private Date deadline;

    /**版本类型*/
    private String versionType;
    /**描述*/
    private String note;

    public void updateDO(PplVersionDO d) {
        d.setVersionCode(versionCode);
        d.setVersionName(versionName);
        fillDate(d);
        d.setDeadline(deadline);
        d.setVersionType(versionType);
        d.setNote(note);
    }

    private void fillDate(PplVersionDO d) {
        if (demandBeginYear != null && demandBeginMonth != null && demandEndYear != null && demandEndMonth != null) {
            d.setDemandBeginYear(demandBeginYear);
            d.setDemandBeginMonth(demandBeginMonth);

            d.setDemandEndYear(demandEndYear);
            d.setDemandEndMonth(demandEndMonth);

            if (cloud.demand.app.common.utils.DateUtils.isBefore(d.getDemandEndYear(), d.getDemandEndMonth(),
                    d.getDemandBeginYear(), d.getDemandBeginMonth())) {
                throw new WrongWebParameterException("结束年月不应该早于开始年月");
            }
        }
    }

}
