package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PplItemImportRsp {

    private Boolean isSuccess;

    private List<PplItemImportRsp.ErrorMessage> errorMsg;

    List<GroupItemDTO> rsp;

    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class ErrorMessage {

        private Integer row;
        private Integer col;
        private String colName;
        private String message;

        public static List<cloud.demand.app.common.excel.core.ErrorMessage> covert(List<ErrorMessage> errors) {
            List<cloud.demand.app.common.excel.core.ErrorMessage> result = new ArrayList<>();
            if (ListUtils.isNotEmpty(errors)) {
                errors.forEach(error -> {
                    cloud.demand.app.common.excel.core.ErrorMessage item = convert(error);
                    if (item != null) {
                        result.add(item);
                    }
                });
            }
            return result;
        }

        public static cloud.demand.app.common.excel.core.ErrorMessage convert(ErrorMessage error) {
            if (error == null) {
                return null;
            }
            return new cloud.demand.app.common.excel.core.ErrorMessage(error.row, error.col, error.colName, error.message);
        }

        public static List<ErrorMessage> from(List<cloud.demand.app.common.excel.core.ErrorMessage> errors) {
            List<ErrorMessage> result = new ArrayList<>();
            if (ListUtils.isNotEmpty(errors)) {
                errors.forEach(error -> {
                    ErrorMessage item = from(error);
                    if (item != null) {
                        result.add(item);
                    }
                });
            }
            return result;
        }

        public static ErrorMessage from(cloud.demand.app.common.excel.core.ErrorMessage error) {
            if (error == null) {
                return null;
            }
            return new ErrorMessage(error.getRow(), error.getCol(), error.getColName(), error.getMessage());
        }

    }


}
