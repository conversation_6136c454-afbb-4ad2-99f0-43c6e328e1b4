package cloud.demand.app.modules.p2p.product_demand.dto;

import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionDO;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandVersionStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class InsertVersionDTO {

    @NotEmpty(message = "版本名称不能为空")
    private String name;
    @NotEmpty(message = "版本编码不能为空")
    private String demandVersion;
    private String desc;
    @NotNull(message = "开放开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportOpenDate;
    @NotNull(message = "开放结束时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportCloseDate;
    @NotNull(message = "年度规划范围开始年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastFrom;
    @NotNull(message = "年度规划范围结束年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date forecastTo;
    @NotNull(message = "13周范围开始年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date w13ForecastFrom;
    @NotNull(message = "13周范围结束年月不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM", timezone = "GMT+8")
    private Date w13ForecastTo;
    @NotEmpty(message = "引用13周版本不能为空")
    private String w13DemandVersion;

    public static ProductDemandVersionDO toDO(InsertVersionDTO dto) {
        ProductDemandVersionDO productDemandVersionDO = new ProductDemandVersionDO();
        productDemandVersionDO.setStatus(ProductDemandVersionStatusEnum.DISABLED.getCode());
        productDemandVersionDO.setDemandVersion(dto.getDemandVersion());
        productDemandVersionDO.setName(dto.getName());
        productDemandVersionDO.setDesc(dto.getDesc() == null ? "" : dto.getDesc());
        productDemandVersionDO.setDemandImportOpenDate(DateUtils.toLocalDate(dto.getDemandImportOpenDate()));
        productDemandVersionDO.setDemandImportCloseDate(DateUtils.toLocalDate(dto.getDemandImportCloseDate()));
        productDemandVersionDO.setForecastFromYear(DateUtils.getYear(dto.getForecastFrom()));
        productDemandVersionDO.setForecastFromMonth(DateUtils.getMonth(dto.getForecastFrom()));
        productDemandVersionDO.setForecastToYear(DateUtils.getYear(dto.getForecastTo()));
        productDemandVersionDO.setForecastToMonth(DateUtils.getMonth(dto.getForecastTo()));
        productDemandVersionDO.setW13ForecastFromYear(DateUtils.getYear(dto.getW13ForecastFrom()));
        productDemandVersionDO.setW13ForecastFromMonth(DateUtils.getMonth(dto.getW13ForecastFrom()));
        productDemandVersionDO.setW13ForecastToYear(DateUtils.getYear(dto.getW13ForecastTo()));
        productDemandVersionDO.setW13ForecastToMonth(DateUtils.getMonth(dto.getW13ForecastTo()));
        productDemandVersionDO.setW13DemandVersion(dto.getW13DemandVersion());
        return productDemandVersionDO;
    }
}
