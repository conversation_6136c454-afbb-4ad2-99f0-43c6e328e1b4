package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Table("w13_data")
@Data
@ToString
public class W13DataDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column("year_month_1")
    private String yearMonth;

    @Column("week")

    private Integer week;

    @Column("industry_dept")
    private String industryDept;

    @Column("common_instance_type")
    private String commonInstanceType;

    @Column("country")
    private String conutry;

    @Column("version_code")
    private String versionCode;

    @Column("w13_predict_total_core")
    private Integer W13PredictTotalCore = 0;

    @Column("w6_predict_total_core")
    private Integer W6PredictTotalCore = 0;

    @Column("w5_predict_total_core")
    private Integer W5PredictTotalCore = 0;

    @Column("applied_total_core")
    private Integer appliedTotalCore = 0;

    @Column("rate")
    private BigDecimal rate;

    @Column("flag")
    private String flag;
}
