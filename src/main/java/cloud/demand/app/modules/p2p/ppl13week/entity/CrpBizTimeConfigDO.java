package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalTime;
import lombok.Data;
import lombok.ToString;

/**
 * crp业务时间配置表
 */
@Data
@ToString
@Table("crp_biz_time_config")
public class CrpBizTimeConfigDO extends BaseDO {

    /**
     * 业务分组<br/>Column: [group]
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.BizTimeGroupEnum
     */
    @Column(value = "biz_group")
    private String bizGroup;


    @Column(value = "code")
    private String code;

    /**
     * 配置名称<br/>Column: [name]
     */
    @Column(value = "name")
    private String name;

    /**
     * 下几周<br/>Column: [next_week]
     */
    @Column(value = "next_week")
    private Integer nextWeek;

    /**
     * 周几<br/>Column: [day_of_week]
     */
    @Column(value = "day_of_week")
    private Integer dayOfWeek;

    /**
     * 几号<br/>Column: [day_of_month]
     */
    @Column(value = "day_of_month")
    private Integer dayOfMonth;

    /**
     * 时间<br/>Column: [time]
     */
    @Column(value = "time")
    private LocalTime time;


    /**
     * 排序<br/>Column: [sort]
     */
    @Column(value = "sort")
    private Integer sort;
}
