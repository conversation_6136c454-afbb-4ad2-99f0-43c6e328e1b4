package cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao;

import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import lombok.Getter;

import java.util.Objects;

/**
 * 云盘类型
 */
@Getter
public enum YunxiaoDiskTypeEnum {

    CLOUD_BASIC("CLOUD_BASIC", "普通云硬盘"),
    CLOUD_BSSD("CLOUD_BSSD", "通用型SSD云硬盘"),
    CLOUD_PREMIUM("CLOUD_PREMIUM", "高性能云硬盘"),
    CLOUD_SSD("CLOUD_SSD", "SSD云硬盘"),
    CLOUD_HSSD("CLOUD_HSSD", "增强型SSD云硬盘"),
    CLOUD_TSSD("CLOUD_TSSD", "极速型SSD云硬盘"),
    LOCAL_BASIC("LOCAL_BASIC", "本地盘"),
    LOCAL_SSD("LOCAL_SSD", "本地SSD硬盘"),

    ;

    final private String code;
    final private String name;

    YunxiaoDiskTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String from(String diskType) {
        if (PplDiskTypeEnum.CLOUD_SSD.getName().equals(diskType)) {
            return CLOUD_SSD.getCode();
        }
        if (PplDiskTypeEnum.CLOUD_PREMIUM.getName().equals(diskType)) {
            return CLOUD_PREMIUM.getCode();
        }
        return "";
    }

    public static YunxiaoDiskTypeEnum getByCode(String code) {
        for (YunxiaoDiskTypeEnum e : YunxiaoDiskTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        YunxiaoDiskTypeEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}