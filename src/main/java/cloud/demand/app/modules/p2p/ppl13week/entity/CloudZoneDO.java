package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDateTime;
import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Table;

/**
 * 云可用区策略表
 */
@Data
@ToString
@Table("cloud_zone_info")
public class CloudZoneDO extends BaseDO {

    /** 境内外<br/>Column: [qcloud_area_domes_foreig] */
    @Column(value = "qcloud_area_domes_foreig")
    private String qcloudAreaDomesForeig;

    /** 区域ID<br/>Column: [qcloud_area_id] */
    @Column(value = "qcloud_area_id")
    private Integer qcloudAreaId;

    /** 区域名称（国内）<br/>Column: [qcloud_area_name] */
    @Column(value = "qcloud_area_name")
    private String qcloudAreaName;

    /** 地域ID<br/>Column: [qcloud_region_id] */
    @Column(value = "qcloud_region_id")
    private Integer qcloudRegionId;

    /** 地域名称<br/>Column: [qcloud_region_name] */
    @Column(value = "qcloud_region_name")
    private String qcloudRegionName;

    /** 地域缩写<br/>Column: [qcloud_region_ab] */
    @Column(value = "qcloud_region_ab")
    private String qcloudRegionAb;

    /** 地域英文编码<br/>Column: [qcloud_region_en] */
    @Column(value = "qcloud_region_en")
    private String qcloudRegionEn;

    /** 地域英文名称<br/>Column: [qcloud_region_name_en] */
    @Column(value = "qcloud_region_name_en")
    private String qcloudRegionNameEn;

    /**
     * 大区名称 + 地域名称 组装而成 如： 华南地区(广州)
     * <br/>Column: [area_region_name]
     */
    @Column("area_region_name")
    private String areaRegionName;

    /** 可用区英文名称<br/>Column: [qcloud_zone_en] */
    @Column(value = "qcloud_zone_en")
    private String qcloudZoneEn;

    /** 可用区ID<br/>Column: [qcloud_zone_id] */
    @Column(value = "qcloud_zone_id")
    private Integer qcloudZoneId;

    /** 可用区名称<br/>Column: [qcloud_zone_name] */
    @Column(value = "qcloud_zone_name")
    private String qcloudZoneName;

}