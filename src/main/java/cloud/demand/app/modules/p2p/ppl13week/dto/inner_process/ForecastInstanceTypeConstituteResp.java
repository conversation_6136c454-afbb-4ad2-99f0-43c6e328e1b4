package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.ZoneInfoFiller;
import cn.hutool.core.collection.CollUtil;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import yunti.boot.exception.BizException;

@Data
public class ForecastInstanceTypeConstituteResp {

    private List<ForecastInstanceTypeConstituteItem> summary;

    private LinkedHashMap<YearMonth, List<ForecastInstanceTypeConstituteItem>> yearMonthData;

    // 13周外数据汇总，只向后取，不向前取
    private List<ForecastInstanceTypeConstituteItem> summaryOutside13Week;
    // 当前周期是否有13周外的需求
    private Boolean hasOutside13Week = false;

    public static ForecastInstanceTypeConstituteResp result(List<ForecastInstanceTypeDetail> current,
            List<ForecastInstanceTypeDetail> last, PplInnerProcessVersionDO version) {
        ForecastInstanceTypeConstituteResp result = new ForecastInstanceTypeConstituteResp();
        if (version == null) {
            throw BizException.makeThrow("审批版本为空");
        }
        if (version.getDemandBeginYear() == null || version.getDemandBeginMonth() == null
                || version.getDemandEndYear() == null || version.getDemandEndMonth() == null) {
            throw BizException.makeThrow("审批版本需求年月为空, version: %s", JSON.toJson(version));
        }

        YearMonth begin = YearMonth.of(version.getDemandBeginYear(), version.getDemandBeginMonth());
        YearMonth end = YearMonth.of(version.getDemandEndYear(), version.getDemandEndMonth());

        calcAndSetSummary(result, current, last);
        calcAndSetYearMonthData(result, current, last, begin, end);
        calcAndSetSummaryOutside13Week(result, current, last, end);
        return result;
    }

    private static void calcAndSetYearMonthData(ForecastInstanceTypeConstituteResp result,
            List<ForecastInstanceTypeDetail> current, List<ForecastInstanceTypeDetail> last,
            YearMonth begin, YearMonth end) {
        List<ForecastInstanceTypeConstituteDetail> currentVersion = mergeByYearMonth(current);
        List<ForecastInstanceTypeConstituteDetail> lastVersion = mergeByYearMonth(last);

        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentRegionMap =
                calcCompareToLastVersion(currentVersion, lastVersion);

        Map<YearMonth, List<ForecastInstanceTypeConstituteItem>> customhouseTitleMap =
                calcCustomhouseTitleMap(currentRegionMap);

        List<YearMonth> yearMonths = new ArrayList<>();
        if (ListUtils.isNotEmpty(customhouseTitleMap.keySet())) {
            yearMonths.addAll(customhouseTitleMap.keySet());
        }
        YearMonth start = begin;
        while (!start.isAfter(end)) {
            if (!yearMonths.contains(start)) {
                // 补齐数据为空的版本内需求年月
                yearMonths.add(start);
            }
            start = start.plusMonths(1);
        }
        // 按年月从小到大排序
        ListUtils.sortAscNullLast(yearMonths, item -> item);
        result.yearMonthData = new LinkedHashMap<>();
        for (YearMonth month : yearMonths) {
            List<ForecastInstanceTypeConstituteItem> monthItems = customhouseTitleMap.get(month);
            if (monthItems == null) {
                monthItems = new ArrayList<>();
            }
            monthItems.forEach(ForecastInstanceTypeConstituteItem::sortRegionList);
            // 判断在版本需求年月范围内yearMonth ∈ [begin, end]
            if (!month.isBefore(begin) && !month.isAfter(end)) {
                result.yearMonthData.put(month, monthItems);
            }
        }
    }

    private static List<ForecastInstanceTypeConstituteDetail> mergeByYearMonth(List<ForecastInstanceTypeDetail> datas) {
        List<ForecastInstanceTypeConstituteDetail> res = new ArrayList<>();
        if (ListUtils.isEmpty(datas)) {
            return res;
        }
        Map<String, List<ForecastInstanceTypeDetail>> map = ListUtils
                .groupBy(datas,
                        item -> item.customhouseTitle  + item.regionName + item.getYearMonth());
        for (List<ForecastInstanceTypeDetail> values : map.values()) {
            if (ListUtils.isEmpty(values)) {
                continue;
            }
            ForecastInstanceTypeDetail first = values.get(0);
            if (first == null) {
                continue;
            }
            ForecastInstanceTypeConstituteDetail item = new ForecastInstanceTypeConstituteDetail();
            item.customhouseTitle = first.customhouseTitle;
            item.regionName = first.regionName;
            item.yearMonth = first.getYearMonth();
            List<String> list = ListUtils.transform(values, ForecastInstanceTypeDetail::getInstanceType);
            item.instanceTypeSet.addAll(list);
            res.add(item);
        }
        return res;
    }

    private static List<ForecastInstanceTypeConstituteDetail> mergeAll(List<ForecastInstanceTypeDetail> datas) {
        List<ForecastInstanceTypeConstituteDetail> res = new ArrayList<>();
        if (ListUtils.isEmpty(datas)) {
            return res;
        }
        Map<String, List<ForecastInstanceTypeDetail>> map = ListUtils
                .groupBy(datas, item -> item.customhouseTitle + item.regionName);
        for (List<ForecastInstanceTypeDetail> values : map.values()) {
            if (ListUtils.isEmpty(values)) {
                continue;
            }
            ForecastInstanceTypeDetail first = values.get(0);
            if (first == null) {
                continue;
            }
            ForecastInstanceTypeConstituteDetail item = new ForecastInstanceTypeConstituteDetail();
            item.customhouseTitle = first.customhouseTitle;
            item.regionName = first.regionName;
            item.yearMonth = null;
            List<String> list = ListUtils.transform(values, ForecastInstanceTypeDetail::getInstanceType);
            item.instanceTypeSet.addAll(list);
            res.add(item);
        }
        return res;
    }

    private static void calcAndSetSummary(ForecastInstanceTypeConstituteResp result,
            List<ForecastInstanceTypeDetail> current, List<ForecastInstanceTypeDetail> last) {
        List<ForecastInstanceTypeConstituteDetail> currentVersion = mergeAll(current);
        List<ForecastInstanceTypeConstituteDetail> lastVersion = mergeAll(last);

        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentRegionMap =
                calcCompareToLastVersion(currentVersion, lastVersion);
        result.summary = calcSummary(currentRegionMap);
        result.summary.forEach(ForecastInstanceTypeConstituteItem::sortRegionList);
    }

    private static List<ForecastInstanceTypeConstituteItem> calcSummary(
            Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentRegionMap) {
        List<ForecastInstanceTypeConstituteRegionName> list = new ArrayList<>();
        currentRegionMap.values().forEach(list::addAll);
        return calcCustomhouseTitleSummary(list);
    }

    private static void calcAndSetSummaryOutside13Week(ForecastInstanceTypeConstituteResp result,
            List<ForecastInstanceTypeDetail> current, List<ForecastInstanceTypeDetail> last, YearMonth end) {

        List<ForecastInstanceTypeDetail> currentOutside13Week = getOutside13Week(current, end);
        List<ForecastInstanceTypeDetail> lastOutside13Week = getOutside13Week(last, end);

        List<ForecastInstanceTypeConstituteDetail> currentVersion = mergeAll(currentOutside13Week);
        List<ForecastInstanceTypeConstituteDetail> lastVersion = mergeAll(lastOutside13Week);

        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentRegionMap =
                calcCompareToLastVersion(currentVersion, lastVersion);

        result.summaryOutside13Week = calcSummary(currentRegionMap);
        result.summaryOutside13Week.forEach(ForecastInstanceTypeConstituteItem::sortRegionList);
        result.hasOutside13Week = ListUtils.isNotEmpty(result.summaryOutside13Week);
    }

    private static List<ForecastInstanceTypeDetail> getOutside13Week(List<ForecastInstanceTypeDetail> input,
            YearMonth end) {
        List<ForecastInstanceTypeDetail> res = new ArrayList<>();
        if (ListUtils.isEmpty(input)) {
            return res;
        }
        for (ForecastInstanceTypeDetail detail : input) {
            if (detail == null || detail.getYearMonth() == null) {
                continue;
            }
            if (detail.getYearMonth().isAfter(end)) {
                // 13周外的数据，只向后取，不向前取
                res.add(detail);
            }
        }
        return res;
    }

    private static List<ForecastInstanceTypeConstituteItem> calcCustomhouseTitleSummary(
            List<ForecastInstanceTypeConstituteRegionName> list) {
        List<ForecastInstanceTypeConstituteItem> summary = new ArrayList<>();
        if (ListUtils.isEmpty(list)) {
            return summary;
        }
        Map<String, List<ForecastInstanceTypeConstituteRegionName>> datas =
                ListUtils.groupBy(list, ForecastInstanceTypeConstituteRegionName::getCustomhouseTitle);
        for (List<ForecastInstanceTypeConstituteRegionName> datum : datas.values()) {
            if (ListUtils.isEmpty(datum)) {
                continue;
            }
            ForecastInstanceTypeConstituteRegionName first = datum.get(0);
            if (first == null) {
                continue;
            }
            ForecastInstanceTypeConstituteItem item = new ForecastInstanceTypeConstituteItem();
            item.customhouseTitle = first.customhouseTitle;
            for (ForecastInstanceTypeConstituteRegionName region : datum) {
                if (region == null) {
                    continue;
                }
                item.instanceSet.addAll(region.instanceSet);
                item.lastInstanceSet.addAll(region.lastInstanceSet);
            }
            item.countHandler();
            item.regionList = mergeRegion(datum);
            summary.add(item);
        }
        return summary;
    }

    private static List<ForecastInstanceTypeConstituteRegionName> mergeRegion(
            List<ForecastInstanceTypeConstituteRegionName> datas) {
        Map<String, List<ForecastInstanceTypeConstituteRegionName>> regionMap = ListUtils
                .groupBy(datas, item -> item.customhouseTitle + item.regionName);
        List<ForecastInstanceTypeConstituteRegionName> res = new ArrayList<>();
        for (List<ForecastInstanceTypeConstituteRegionName> values : regionMap.values()) {
            if (ListUtils.isEmpty(values)) {
                continue;
            }
            ForecastInstanceTypeConstituteRegionName first = values.get(0);
            if (first == null) {
                continue;
            }
            ForecastInstanceTypeConstituteRegionName item = new ForecastInstanceTypeConstituteRegionName();
            item.customhouseTitle = first.customhouseTitle;
            item.regionName = first.regionName;
            for (ForecastInstanceTypeConstituteRegionName value : values) {
                item.instanceSet.addAll(value.instanceSet);
                item.lastInstanceSet.addAll(value.lastInstanceSet);
            }
            item.countHandler();
            res.add(item);
        }
        return res;
    }


    private static Map<YearMonth, List<ForecastInstanceTypeConstituteItem>> calcCustomhouseTitleMap(
            Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentRegionMap) {
        Map<YearMonth, List<ForecastInstanceTypeConstituteItem>> res = new HashMap<>();
        if (ListUtils.isEmpty(currentRegionMap)) {
            return res;
        }
        Map<YearMonth, List<ForecastInstanceTypeConstituteRegionName>> map = new HashMap<>();
        for (Entry<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> entry
                : currentRegionMap.entrySet()) {
            List<ForecastInstanceTypeConstituteRegionName> regionData = entry.getValue();
            if (ListUtils.isEmpty(regionData)) {
                continue;
            }
            YearMonth month = entry.getKey() == null ? null : entry.getKey().getLeft();
            List<ForecastInstanceTypeConstituteRegionName> monthData = map.computeIfAbsent(month, k -> new ArrayList<>());

            monthData.addAll(regionData);
        }
        map.forEach((key, value) -> {
            List<ForecastInstanceTypeConstituteItem> summary = calcCustomhouseTitleSummary(value);
            res.put(key, summary);
        });
        return res;
    }

    private static Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>>
    calcCompareToLastVersion(
            List<ForecastInstanceTypeConstituteDetail> currentVersion,
            List<ForecastInstanceTypeConstituteDetail> lastVersion) {
        if (currentVersion == null) {
            currentVersion = new ArrayList<>();
        }
        if (lastVersion == null) {
            lastVersion = new ArrayList<>();
        }

        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> currentMap = ListUtils
                .toMapList(currentVersion, ForecastInstanceTypeConstituteDetail::key,
                        ForecastInstanceTypeConstituteDetail::trans);
        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> lastMap = ListUtils
                .toMapList(lastVersion, ForecastInstanceTypeConstituteDetail::key,
                        ForecastInstanceTypeConstituteDetail::trans);

        Map<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> res = new HashMap<>();
        List<Pair<YearMonth, String>> keys = new ArrayList<>();
        for (Entry<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> entry
                : currentMap.entrySet()) {
            Pair<YearMonth, String> key = entry.getKey();
            keys.add(key);
            List<ForecastInstanceTypeConstituteRegionName> value = entry.getValue();
            Map<String, ForecastInstanceTypeConstituteRegionName> current = calcRegionNameSummary(value);
            List<ForecastInstanceTypeConstituteRegionName> lastData = lastMap.get(key);
            if (lastData == null) {
                lastData = new ArrayList<>();
            }
            Map<String, ForecastInstanceTypeConstituteRegionName> last = calcRegionNameSummary(lastData);
            current.forEach((region, data) -> {
                if (data == null) {
                    return;
                }
                ForecastInstanceTypeConstituteRegionName lastRegionData = last.get(region);
                if (lastRegionData ==  null) {
                    return;
                }
                data.lastInstanceSet.addAll(lastRegionData.instanceSet);
                data.countHandler();
            });
            if (ListUtils.isNotEmpty(current.values())) {
                res.put(key, new ArrayList<>(current.values()));
            }
        }

        // 针对本周期没有上周期有的数据进行计算
        for (Entry<Pair<YearMonth, String>, List<ForecastInstanceTypeConstituteRegionName>> entry
                : lastMap.entrySet()) {
            Pair<YearMonth, String> key = entry.getKey();
            if (keys.contains(key)) {
                // 本周期已有，跳过，上面已经处理过了
                continue;
            }
            List<ForecastInstanceTypeConstituteRegionName> value = entry.getValue();
            Map<String, ForecastInstanceTypeConstituteRegionName> lastData = calcRegionNameSummary(value);
            List<ForecastInstanceTypeConstituteRegionName> currentData = currentMap.get(key);
            if (currentData == null) {
                currentData = new ArrayList<>();
            }
            Map<String, ForecastInstanceTypeConstituteRegionName> current = calcRegionNameSummary(currentData);
            lastData.forEach((region, data) -> {
                if (data == null) {
                    return;
                }
                ForecastInstanceTypeConstituteRegionName currentRegionData = current.get(region);
                if (currentRegionData ==  null) {
                    currentRegionData = new ForecastInstanceTypeConstituteRegionName();
                    currentRegionData.regionName = data.regionName;
                    currentRegionData.customhouseTitle = data.customhouseTitle;
                }
                currentRegionData.lastInstanceSet.addAll(data.instanceSet);
                currentRegionData.countHandler();
                current.put(region, currentRegionData);
            });
            if (ListUtils.isNotEmpty(current.values())) {
                res.put(key, new ArrayList<>(current.values()));
            }
        }

        return res;
    }

    private static Map<String, ForecastInstanceTypeConstituteRegionName> calcRegionNameSummary(
            List<ForecastInstanceTypeConstituteRegionName> list) {
        Map<String, ForecastInstanceTypeConstituteRegionName> summary = new HashMap<>();
        if (ListUtils.isEmpty(list)) {
            return summary;
        }
        Map<String, List<ForecastInstanceTypeConstituteRegionName>> datas =
                ListUtils.groupBy(list, ForecastInstanceTypeConstituteRegionName::getRegionName);
        for (Entry<String, List<ForecastInstanceTypeConstituteRegionName>> entry : datas.entrySet()) {
            List<ForecastInstanceTypeConstituteRegionName> datum = entry.getValue();
            if (ListUtils.isEmpty(datum)) {
                continue;
            }
            ForecastInstanceTypeConstituteRegionName first = datum.get(0);
            if (first == null) {
                continue;
            }
            ForecastInstanceTypeConstituteRegionName item = new ForecastInstanceTypeConstituteRegionName();
            item.regionName = first.regionName;
            for (ForecastInstanceTypeConstituteRegionName region : datum) {
                if (region == null) {
                    continue;
                }
                item.instanceSet.addAll(region.instanceSet);
                item.lastInstanceSet.addAll(region.lastInstanceSet);
            }
            item.customhouseTitle = first.customhouseTitle;
            item.countHandler();
            summary.put(entry.getKey(), item);
        }
        return summary;
    }


    @Data
    public static class ForecastInstanceTypeConstituteRegionName {

        private String regionName;

        private String customhouseTitle;

        private Integer instanceTypeCount;

        private Integer compareToLastVersion;

        private final Set<String> instanceSet = new HashSet<>();

        private final Set<String> lastInstanceSet = new HashSet<>();

        private void countHandler() {
            instanceTypeCount = CollUtil.size(instanceSet);
            compareToLastVersion = instanceTypeCount - CollUtil.size(lastInstanceSet);
        }

    }

    @Data
    public static class ForecastInstanceTypeConstituteItem {

        private String customhouseTitle;

        private Integer instanceTypeCount;

        private Integer compareToLastVersion;

        private final Set<String> instanceSet = new HashSet<>();

        private  final Set<String> lastInstanceSet  = new HashSet<>();

        List<ForecastInstanceTypeConstituteRegionName> regionList;

        private void sortRegionList() {
            if (ListUtils.isEmpty(regionList)) {
                return;
            }
            ListUtils.sortDescNullLast(regionList, ForecastInstanceTypeConstituteRegionName::getInstanceTypeCount);
        }

        private void countHandler() {
            instanceTypeCount = CollUtil.size(instanceSet);
            compareToLastVersion = instanceTypeCount - CollUtil.size(lastInstanceSet);
        }

    }

    @Data
    public static class ForecastInstanceTypeConstituteDetail {
        private final Set<String> instanceTypeSet = new HashSet<>();
        private String regionName;
        private String customhouseTitle;
        private YearMonth yearMonth;

        private ForecastInstanceTypeConstituteRegionName trans() {
            ForecastInstanceTypeConstituteRegionName res = new ForecastInstanceTypeConstituteRegionName();
            res.regionName = regionName;
            res.customhouseTitle = customhouseTitle;
            res.instanceSet.addAll(instanceTypeSet);
            res.compareToLastVersion = 0;
            return res;
        }

        private Pair<YearMonth, String> key() {
            return Pair.of(getYearMonth(), regionName);
        }
    }

    @Data
    public static class ForecastInstanceTypeDetail implements ZoneInfoFiller {

        @Column("instance_type")
        private String instanceType;

        @Column("region_name")
        private String regionName;

        @Column("demand_year_month")
        private String demandYearMonth;

        private String customhouseTitle;

        private YearMonth yearMonth;

        public YearMonth getYearMonth() {
            if (yearMonth == null && StringUtils.isNotBlank(demandYearMonth)) {
                yearMonth = YearMonth.parse(demandYearMonth, DateTimeFormatter.ofPattern("yyyy-MM"));
            }
            return yearMonth;
        }

        @Override
        public String provideZoneName() {
            return null;
        }

        @Override
        public String provideRegionName() {
            return this.regionName;
        }

        @Override
        public void fillZone(String zone) {
            // non
        }

        @Override
        public void fillAreaName(String areaName) {
            // non
        }

        @Override
        public void fillCustomhouseTitle(String customhouseTitle) {
            this.customhouseTitle = customhouseTitle;
        }
    }

}
