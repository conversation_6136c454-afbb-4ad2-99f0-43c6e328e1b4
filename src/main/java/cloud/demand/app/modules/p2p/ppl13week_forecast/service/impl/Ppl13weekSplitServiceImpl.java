package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_FOR_ZIYAN;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekPredictServiceImpl.gerReplaceZoneName;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.mrpv2.entity.Mrpv2CommonInstanceTypeConfigDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigPubRegionConfigDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigZiyanInstanceTypeSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultForZiyanSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitMiddleDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.PplSplitForMiddleDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.ZiyanDemandProjectCustomBgDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastCoreTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastResourcePoolEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekInputService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekPredictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekPredictServiceImpl.InstanceTypeInfo;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Stack;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.stat.regression.SimpleRegression;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import yunti.boot.exception.BizException;

/**
 * <AUTHOR>
 */
@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class Ppl13weekSplitServiceImpl implements
        Ppl13weekSplitService {

    @Resource
    private Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    private Ppl13weekInputService ppl13weekInputService;
    @Resource
    private Ppl13weekPredictService ppl13weekPredictService;

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Data
    @AllArgsConstructor
    public static class KeyProjectCustomBg {

        String projectName;
        String customBgName;
    }

    @Data
    @AllArgsConstructor
    public static class KeyDeviceGroupRegion {

        String deviceGroup;
        String regionName;
    }

    @Override
    public PplForecastPredictTaskOutputVersionDO createOutputVersion(Long taskId, String name,
            Ppl13weekForecastOutputVersionTypeEnum type, String desc) {
        PplForecastPredictTaskOutputVersionDO one = new PplForecastPredictTaskOutputVersionDO();
        one.setCreateUser(LoginUtils.getUserNameWithSystem());
        one.setTaskId(taskId);
        one.setOutputVersionName(name);
        one.setOutputVersionType(type.getCode());
        one.setStatus("NEW");
        one.setDesc(desc);

        demandDBHelper.insert(one);
        return one;
    }

    @Override
    public void updateStatus(Long outputVersionId, String status,String appendDesc) {
        if (outputVersionId == null) {
            return;
        }
        PplForecastPredictTaskOutputVersionDO one
                = demandDBHelper.getByKey(PplForecastPredictTaskOutputVersionDO.class, outputVersionId);
        if (one != null) {
            one.setStatus(status);
            if (Strings.isNotBlank(appendDesc)) {
                String desc = Strings.isNotBlank(one.getDesc()) ? "" : one.getDesc();
                one.setDesc(desc + "\n" + appendDesc);
            }
            demandDBHelper.update(one);
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    @Synchronized(throwExceptionIfNotGetLock = true)
    public void splitProjectCustomBgDeviceGroup(Long taskId, String name, String note) {

        // 创建记录然后去拆分
        PplForecastPredictTaskOutputVersionDO outputVersionDO = new PplForecastPredictTaskOutputVersionDO();
        outputVersionDO.setCreateUser(LoginUtils.getUserNameWithSystem());
        outputVersionDO.setTaskId(taskId);
        outputVersionDO.setOutputVersionName(name);
        outputVersionDO.setOutputVersionType(SPLIT_FOR_ZIYAN.getCode());
        outputVersionDO.setStatus("SPLIT");
        outputVersionDO.setDesc(note);
        outputVersionDO.setTaskId(taskId);
        DBList.demandDBHelper.insert(outputVersionDO);

        splitProjectCustomBgDeviceGroup(outputVersionDO.getId());
    }


    @Override
    @Transactional(value = "demandTransactionManager")
    @Synchronized(throwExceptionIfNotGetLock = true)
    public void splitProjectCustomBgDeviceGroup(Long outputVersionId) {

        PplForecastPredictTaskOutputVersionDO outputVersion = DBList.demandDBHelper.getOne(
                PplForecastPredictTaskOutputVersionDO.class, "where id=?", outputVersionId);
        Long taskId = outputVersion.getTaskId();

        // 1、取到需要拆分的明细数据
        PplForecastPredictTaskDO task = DBList.demandDBHelper.getOne(
                PplForecastPredictTaskDO.class, "where id=?", taskId);
        List<PplForecastPredictResultDO> predict = DBList.demandDBHelper.getAll(PplForecastPredictResultDO.class,
                "where task_id=?", taskId);

        if (task == null ||
                !task.getResourcePool().equals(Ppl13weekForecastResourcePoolEnum.ZIYAN.getCode())) {
            throw Lang.makeThrow("task null 或 不是自研的");
        }

        String regionDict = "select distinct region_name,customhouse_title\n"
                + "from yunti_demand.dwd_yunti_cvm_demand_forecast_item_df where region_name != ''";
        Map<String, String> regionName2country = ORMUtils.db(DBList.prodReadOnlyCkStdCrpDBHelper).getKVMap(regionDict);

        List<PplForecastPredictResultForZiyanSplitDO> predictTrans = Lang.list();
        for (PplForecastPredictResultDO o : predict) {
            PplForecastPredictResultForZiyanSplitDO i = new PplForecastPredictResultForZiyanSplitDO();
            BeanUtils.copyProperties(o, i);
            // ⚠️⚠️⚠️ 注意这里是复用，上游没有做
            i.setDeviceGroup(o.getGinsFamily());
            // 这3个是需要拆分的字段
            i.setGinsFamily("");
            i.setProjectName("");
            i.setCustomBgName("");
            i.setOutputVersionId(outputVersionId);

            i.setResultId(o.getId());
            i.setNote(String.format(">原始预测量:#%.3f#\n", o.getCoreNum().floatValue()));
            // 特别说明，这里的region_name 是实际中来的，实际有，预测一定有，除非预测值补了不存在的城市
            i.setCustomhouseTitle(regionName2country.getOrDefault(i.getRegionName(), i.getRegionName()));
            predictTrans.add(i);
        }

        /*
         * 2、 根据历史 ⚠️1年执行量⚠️ 拆分数据
         *  全量->自定义BG/部门，按近1年执行量比例拆分
         *  全量->项目类型，按近1年执行量比例拆分
         */
        predictTrans = splitProjectCustomBg(task, predictTrans);

        /*
         * 3、 根据配置表拆分数据
         *   机型族->机型大类，按人工配置比例拆
         */
        predictTrans = splitGinsfamily(predictTrans);

        predictTrans.forEach((o) -> {
            o.setId(null);
            o.setCreateTime(null);
        });
        if (predictTrans.isEmpty()) {
            throw BizException.makeThrow("拆分数据为空，属于异常情况。");
        }

        DBList.demandDBHelper.insertBatchWithoutReturnId(predictTrans);
    }

    @NotNull
    private List<PplForecastPredictResultForZiyanSplitDO> splitGinsfamily(
            List<PplForecastPredictResultForZiyanSplitDO> predictTrans) {
        List<PplForecastPredictResultForZiyanSplitDO> predictTrans1 = Lang.list();
        List<PplForecastConfigZiyanInstanceTypeSplitDO> allConfig = DBList.demandDBHelper.getAll(
                PplForecastConfigZiyanInstanceTypeSplitDO.class);

        // config1  优先级高一点
        Table<String, String, PplForecastConfigZiyanInstanceTypeSplitDO> config1 = HashBasedTable.create();
        Table<String, String, PplForecastConfigZiyanInstanceTypeSplitDO> config2 = HashBasedTable.create();

        allConfig.stream().filter((o) -> Strings.isNotBlank(o.getCustomhouseTitle()))
                .forEach((o) -> config1.put(o.getDeviceGroup() + o.getCustomhouseTitle(), o.getGinsFamily(), o));

        allConfig.stream().filter((o) -> Strings.isBlank(o.getCustomhouseTitle()))
                .forEach((o) -> config2.put(o.getDeviceGroup(), o.getGinsFamily(), o));

        Map<String, List<PplForecastPredictResultForZiyanSplitDO>> groupByDeviceGroup = predictTrans.stream()
                .collect(Collectors.groupingBy(PplForecastPredictResultForZiyanSplitDO::getDeviceGroup));

        for (String deviceGroup : groupByDeviceGroup.keySet()) {

            List<PplForecastPredictResultForZiyanSplitDO> detailForSplit = groupByDeviceGroup.get(deviceGroup);

            for (PplForecastPredictResultForZiyanSplitDO i : detailForSplit) {
                if (config1.containsRow(i.getDeviceGroup() + i.getCustomhouseTitle())) {
                    Map<String, PplForecastConfigZiyanInstanceTypeSplitDO> row = config1.row(
                            i.getDeviceGroup() + i.getCustomhouseTitle());
                    split(predictTrans1, i, row, 1);
                    continue;
                }
                if (config2.containsRow(i.getDeviceGroup())) {
                    Map<String, PplForecastConfigZiyanInstanceTypeSplitDO> row = config2.row(
                            i.getDeviceGroup());
                    split(predictTrans1, i, row, 2);
                    continue;
                }
                // 兜底不拆分
                predictTrans1.add(i);
            }
        }
        return predictTrans1;
    }

    private void split(List<PplForecastPredictResultForZiyanSplitDO> predictTrans1,
            PplForecastPredictResultForZiyanSplitDO i, Map<String, PplForecastConfigZiyanInstanceTypeSplitDO> row,
            int i2) {
        for (String ginsfamily : row.keySet()) {
            PplForecastPredictResultForZiyanSplitDO clone = JSON.clone(i);
            clone.setGinsFamily(ginsfamily);
            String note = clone.getNote();
            note += String.format(">实例规格拆分%d:拆分条数为#%d#,拆分前为#%.1f#,比例为:#%.3f#\n",
                    i2, row.keySet().size(), i.getCoreNum(), row.get(ginsfamily).getRate());
            clone.setCoreNum(clone.getCoreNum().multiply(row.get(ginsfamily).getRate()));
            clone.setNote(note);
            predictTrans1.add(clone);
        }
    }

    private List<PplForecastPredictResultForZiyanSplitDO> splitProjectCustomBg(
            PplForecastPredictTaskDO task,
            List<PplForecastPredictResultForZiyanSplitDO> predictTrans) {

        LocalDate predictMonth = task.getInputDateEnd().plusDays(1L); // task.getInputDO().getPredictMonth();
        // region_name 其实可以不用的
        List<ZiyanDemandProjectCustomBgDTO> monthDataForZiyan = getMonthDataForZiyan(predictMonth, task);

        Map<String, String> ziyanGinsfamily2DeviceGroup = ppl13weekCommonDataAccess.getZiyanGinsfamily2DeviceGroup();

        Table<KeyDeviceGroupRegion, KeyProjectCustomBg, List<ZiyanDemandProjectCustomBgDTO>> detail =
                HashBasedTable.create();

        // key1 是要拆分的数据 和 历史数据都有的维度
        // key2 是要拆分的明细数据
        for (ZiyanDemandProjectCustomBgDTO o : monthDataForZiyan) {
            String deviceGroup = ziyanGinsfamily2DeviceGroup.getOrDefault(o.getGinsFamily(), o.getGinsFamily());
            KeyDeviceGroupRegion key1 = new KeyDeviceGroupRegion(deviceGroup, o.getRegionName());
            KeyProjectCustomBg key2 = new KeyProjectCustomBg(o.getProjectName(), o.getCustomBgName());
            if (!detail.contains(key1, key2)) {
                detail.put(key1, key2, new ArrayList<>());
            }
            detail.get(key1, key2).add(o);
        }

        if (detail.isEmpty()) {
            throw BizException.makeThrow("拆分依赖数据为空，请重新尝试");
        }
        List<PplForecastPredictResultForZiyanSplitDO> predictTrans1 = Lang.list();
        for (PplForecastPredictResultForZiyanSplitDO o : predictTrans) {

            Map<KeyProjectCustomBg, List<ZiyanDemandProjectCustomBgDTO>> row =
                    detail.row(new KeyDeviceGroupRegion(o.getDeviceGroup(), o.getRegionName()));
            BigDecimal deviceGroupCustomBgTotal = NumberUtils.sum(row.values(),
                    (i) -> NumberUtils.sum(i, ZiyanDemandProjectCustomBgDTO::getNewDiff));

            log.info("处理机型族:{},城市{}, 拆分数量:{}", o.getDeviceGroup(), o.getRegionName(), row.size());

            for (KeyProjectCustomBg keyProjectCustomBg : row.keySet()) {
                PplForecastPredictResultForZiyanSplitDO clone = JSON.clone(o);
                clone.setProjectName(keyProjectCustomBg.getProjectName());
                clone.setCustomBgName(keyProjectCustomBg.getCustomBgName());
                BigDecimal oneProjectCustomBgSum = NumberUtils.sum(row.get(keyProjectCustomBg),
                        ZiyanDemandProjectCustomBgDTO::getNewDiff);
                BigDecimal rate = oneProjectCustomBgSum.divide(deviceGroupCustomBgTotal, 8, RoundingMode.HALF_UP);
                clone.setCoreNum(o.getCoreNum().multiply(rate));
                String note = clone.getNote();
                note +=
                        String.format(
                                ">项目+自定义事业群拆分:拆分条数为#%d#,拆分前为#%.1f#,近1年数量为#%.1f#,比例为:#%f.3#\n",
                                row.keySet().size(), deviceGroupCustomBgTotal, oneProjectCustomBgSum, rate);
                clone.setNote(note);
                predictTrans1.add(clone);
            }
        }
        return predictTrans1;
    }


    private List<ZiyanDemandProjectCustomBgDTO> getMonthDataForZiyan(LocalDate predictDate,
            PplForecastPredictTaskDO task) {

//      String conditionSql = ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan_new_condition.sql");
        String conditionSql = task.getInputSqlConditionNew();
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan_by_project_custombg_rate.sql");
        sql = sql.replace("${CONDITION}", conditionSql);
        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> params = new HashMap<>(1000);
        params.put("predictDate", predictDate);
        return DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(ZiyanDemandProjectCustomBgDTO.class, sql, params);
    }


    public static class Logger {

        private static final ThreadLocal<List<String>> THREAD_LOGS = ThreadLocal.withInitial(ArrayList::new);
        private static final ThreadLocal<Stack<Instant>> START_TIMES = ThreadLocal.withInitial(Stack::new);

        public static List<String> getLogs() {
            return THREAD_LOGS.get();
        }

        public static void start(String message, Object... args) {
            Instant startTime = Instant.now();
            START_TIMES.get().push(startTime);
            log("Start time: " + FORMATTER.format(startTime.atZone(ZoneId.systemDefault())) + "\t" + message, args);
        }

        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");

        public static void stop() {
            if (!START_TIMES.get().isEmpty()) {
                Instant stopTime = Instant.now();
                Instant startTime = START_TIMES.get().pop();
                log("Stop time: " + FORMATTER.format(stopTime.atZone(ZoneId.systemDefault())));
                Duration duration = Duration.between(startTime, stopTime);
                log("Duration: " + duration.toMillis() + " ms\n");
            } else {
                log("Error: stop called without a corresponding start");
            }
        }

        public static void log(String message, Object... args) {
            THREAD_LOGS.get().add(String.format("- " + message, args));
        }

        public static void log1(String message, Object... args) {
            THREAD_LOGS.get().add(String.format("\t- " + message, args));
        }

        public static void log2(String message, Object... args) {
            if (true) {
                return;
            }
            THREAD_LOGS.get().add(String.format("\t\t- " + message, args));
        }

        public static void release() {
            THREAD_LOGS.remove();
        }
    }

    @SneakyThrows
    @Override
    public String splitCloudMiddle(Long taskId, Long outputVersionId) {

        Logger.release();
        Logger.log("taskId = %s, 拆分的数据会插入到 outputVersionId = %s", taskId, outputVersionId);
        //  1、获取到taskId对应的条件sql，然后查询出机型、地域下各行业部门的新增和退回量
        PplForecastPredictTaskDO taskDO = demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new WrongWebParameterException("taskId不存在");
        }
        Logger.log("category = %s", taskDO.getCategory());

        // 目前拆分规则：拿预测日期最近3个月的新增、退回量数据作为拆分比例
        // 获取到历史明细数据，最细粒度的, 没有做任何处理,存量值不可用

        // 替换可用区功能
        CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture =
                CompletableFuture.supplyAsync(() -> {
                    List<PplForecastConfigPubRegionConfigDO> publicRegionConfig =
                            ppl13weekInputService.getValuePublicRegionConfig();
                    return ListUtils.toMap(publicRegionConfig, PplForecastConfigPubRegionConfigDO::getZoneName, o -> o);
                });

        // 是否保留客户维度的数据
        List<PplForecastPredictResultSplitMiddleDO> notSplitResults = Lang.list();
        boolean isNoUin = taskDO.getCategory().contains("方案651") || taskDO.getCategory().contains("方案661");

        List<PplForecastPredictResultSplitMiddleDO> results;
        // 客户去重的数据这里独立拆分
        if (isNoUin) {
            results = splitZoneName(taskDO, pubRegionConfigMapFuture, notSplitResults);
            results = splitIndustry(taskDO, pubRegionConfigMapFuture, results, notSplitResults);
            results = splitInstanceModel(taskDO, pubRegionConfigMapFuture, results, notSplitResults);
            results.forEach((o) -> {
                LocalDate weekDate = o.getStatTime().withDayOfMonth(15);
                o.setWeekStart(weekDate);
            });
        } else {
            results = splitWithUin(taskDO, pubRegionConfigMapFuture, notSplitResults);
        }
        // 把可用区替换的
        results = replaceZoneName(pubRegionConfigMapFuture, results);

        setUniqueValueColumn(outputVersionId, notSplitResults, results);
        // 3、写入到split_middle
        demandDBHelper.insertBatchWithoutReturnId(results);
        demandDBHelper.insertBatchWithoutReturnId(notSplitResults);
        return Strings.join("\n", Logger.getLogs());
        // mark 处理成净增的目前不在这个split环节处理，因此这里不处理
    }

    @SneakyThrows
    @Override
    public String splitEmrLongTail(Long taskId, Long outputVersionId) {

        Logger.release();
        Logger.log("taskId = %s, 拆分的数据会插入到 outputVersionId = %s", taskId, outputVersionId);

        //  1、获取到taskId对应的条件sql，然后查询出机型、地域下各行业部门的新增和退回量
        PplForecastPredictTaskDO taskDO = demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new WrongWebParameterException("taskId不存在");
        }

        // 随机获取地域对应的主力可用区
        String mainZoneSql = "select region_name,zone_name\n"
                + "from inventory_health_main_zone_name_config\n"
                + "where date = (select max(`date`) from inventory_health_main_zone_name_config where deleted = 0)\n"
                + "  and deleted = 0 and type_name='主力可用区'\n"
                + "group by region_name\n"
                + "order by region_name";
        Map<String, String> regionToRandomMainZone = ORMUtils.db(demandDBHelper).getKVMap(mainZoneSql);

        Logger.log("category = %s", taskDO.getCategory());

        // 目前拆分规则：拿预测日期最近3个月的新增、退回量数据作为拆分比例
        // 获取到历史明细数据，最细粒度的, 没有做任何处理,存量值不可用

        List<PplForecastPredictResultSplitDO> notSplitResults = Lang.list();

        List<PplForecastPredictResultDO> predictData = getPredictData(taskDO);

        predictData = calculateNetIncrease(predictData);


        List<PplForecastPredictResultSplitDO> results
                = ListUtils.transform(predictData, PplForecastPredictResultSplitDO::transFrom);
        replaceNewInstanceType(results);

        Logger.start("获取历史最明细的数据，没有机型收敛");
        HistoryData historyData = getHistoryData(taskDO, null);
        Logger.stop();

        Logger.start("拆分地域到大小核心");
        results = splitRegionName2instanceModel(results, historyData);
        results = addEmptyToNotSplitResultsAndLog1(results, notSplitResults,
                PplForecastPredictResultSplitDO::getInstanceModel, "大小核心");
        Logger.stop();

        ListUtils.concat(results, notSplitResults).forEach(o -> {
            o.setOutputVersionId(outputVersionId);
            o.setTaskId(taskId);
            if (Strings.isNotBlank(o.getInstanceModel())) {
                o.setCvmNum(o.getCoreNum().divide(BigDecimal.valueOf(o.getInstanceModelCpu()), 5, RoundingMode.HALF_UP)
                        .intValue());
            }
            o.setZoneName(regionToRandomMainZone.getOrDefault(o.getRegionName(), "随机可用区"));
            o.setBillType(taskDO.getBillType());
            LocalDate statTime = o.getStatTime();
            LocalDate firstDayOfMonth = statTime.withDayOfMonth(1);
            o.setBeginBuyDate(firstDayOfMonth.plusDays(14)); // 以周中作为代表
            o.setEndBuyDate(firstDayOfMonth.plusDays(21)); // 开始和结束日期间隔1周
        });

        // 3、写入到split_middle
        demandDBHelper.insertBatchWithoutReturnId(results);
        demandDBHelper.insertBatchWithoutReturnId(notSplitResults);

        return Strings.join("\n", Logger.getLogs());
    }

    private void replaceNewInstanceType( List<PplForecastPredictResultSplitDO> predictData) {

        Map<String, String> purchaseNewTypeMap = ppl13weekCommonDataAccess.getPurchaseNewInstanceTypeMap();
        for (PplForecastPredictResultSplitDO predict : predictData) {
            if (Strings.isBlank(predict.getGinsFamily())) {
                throw new RuntimeException("instanceType 不能为空，出现了空的数据");
            }
            String oldInstanceType = predict.getGinsFamily();
            String purchaseInstanceType = purchaseNewTypeMap.get(oldInstanceType);
            if (Strings.equals(predict.getForecastSeqType(), "NEW") && Strings.isNotBlank(purchaseInstanceType)) {
                predict.setGinsFamily(purchaseInstanceType);
                predict.appendErrorMsg("转换新机型： " + oldInstanceType + "->" + purchaseInstanceType);
            }
        }
    }

    private static @NotNull List<PplForecastPredictResultDO> calculateNetIncrease(
            List<PplForecastPredictResultDO> predictData) {

        // 这里处理成净增数据，因为拆分后再去处理净增数据会导致细粒度数据无法对冲
        // 例子：
        //    北京 SA5 新增2300核心  退回1600核心， 净增为 700核心
        // 如果拆分大小核心后对冲：
        //    北京 SA5 大核心 新增2000
        //    北京 SA5 小核心 新增300
        //    北京 SA5 大核心 退回1000
        //    北京 SA5 小核心 退回600
        // 对冲结果为： 大核心净增： 1000，   小核心净增： -300, 导致SA5的新增变多了，退回也变多了
        // 有300 的量因为拆分的数据无法对冲，而EMR应该是大小核心不敏感的， 这里要弱化大小核心对于量级的影响

        predictData = new ArrayList<>(ListUtils2.groupAndApply(predictData,
                (o) -> Strings.join(o.getYear().toString(), o.getMonth(), o.getGinsFamily(), o.getRegionName()),
                (key, list) -> {
                    PplForecastPredictResultDO ret = list.get(0);
                    BigDecimal coreSum = NumberUtils.sum(list, (o -> {
                        if (Strings.equals(o.getType(), PplForecastTypeEnum.NEW.getType())) {
                            return o.getCoreNum().abs();
                        } else if (Strings.equals(o.getType(), PplForecastTypeEnum.RET.getType())) {
                            return BigDecimal.ZERO.subtract(o.getCoreNum().abs());
                        } else {
                            throw new RuntimeException("异常 TYPE: " + o.getType());
                        }
                    }));
                    ret.setCoreNum(coreSum.abs());
                    if (coreSum.compareTo(BigDecimal.ZERO) > 0) {
                        ret.setType(PplForecastTypeEnum.NEW.getType());
                    } else {
                        ret.setType(PplForecastTypeEnum.RET.getType());
                    }
                    return ret;
                }).values());
        return predictData;
    }

    @SneakyThrows
    @Override
    public String splitNewLongTail(Long taskId, Long outputVersionId) {

        Logger.release();
        Logger.log("taskId = %s, 拆分的数据会插入到 outputVersionId = %s", taskId, outputVersionId);
        //  1、获取到taskId对应的条件sql，然后查询出机型、地域下各行业部门的新增和退回量
        PplForecastPredictTaskDO taskDO = demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new WrongWebParameterException("taskId不存在");
        }
        Logger.log("category = %s", taskDO.getCategory());

        // 目前拆分规则：拿预测日期最近3个月的新增、退回量数据作为拆分比例
        // 获取到历史明细数据，最细粒度的, 没有做任何处理,存量值不可用
        // 替换可用区功能
        CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture =
                CompletableFuture.supplyAsync(() -> {
                    List<PplForecastConfigPubRegionConfigDO> publicRegionConfig =
                            ppl13weekInputService.getValuePublicRegionConfig();
                    return ListUtils.toMap(publicRegionConfig, PplForecastConfigPubRegionConfigDO::getZoneName, o -> o);
                });

        List<PplForecastPredictResultDO> predictData = getPredictData(taskDO);

        List<PplForecastPredictResultSplitDO> notSplitResults = Lang.list();
        List<PplForecastPredictResultSplitDO> results = ListUtils.transform(predictData,
                PplForecastPredictResultSplitDO::transFrom);

        Logger.start("获取历史最明细的数据，没有机型收敛");
        HistoryData historyData = getHistoryData(taskDO, null);
        Logger.stop();
//        Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap = pubRegionConfigMapFuture.get();

        /*
         * 这一步拆分可用区，顺序不可以乱，拆分这个动
         * 国家在可用区拆分这里带上
         */
        Logger.start("拆分可用区");
        results = splitRegionName2ZoneName(results, historyData);
        results = addEmptyToNotSplitResultsAndLog1(results, notSplitResults,
                PplForecastPredictResultSplitDO::getZoneName, "可用区");
        Logger.stop();

        /*
         * 这一步拆分大小核心，顺序不可以乱，
         */
        Logger.start("拆分大小核心");
        results = splitZoneName2instanceModel(results, historyData);
        results = addEmptyToNotSplitResultsAndLog1(results, notSplitResults,
                PplForecastPredictResultSplitDO::getInstanceModel, "大小核心");
        Logger.stop();

        // 把可用区替换的
        Map<String, PplForecastConfigPubRegionConfigDO> zoneMap = pubRegionConfigMapFuture.get();
        results = new ArrayList<>(ListUtils2.groupAndApply(results,
                (o) -> Strings.join("@", o.getYear(), o.getMonth(), o.getPredictIndex(), o.getInstanceModel(),
                        o.getGinsFamily(), o.getRegionName(), o.getForecastSeqType(), o.getBeginBuyDate(),
                        gerReplaceZoneName(zoneMap, o.getGinsFamily(), o.getZoneName())
                ),
                (k, o) -> {
                    PplForecastPredictResultSplitDO ret = o.get(0);
                    String newZoneName = gerReplaceZoneName(zoneMap, ret.getGinsFamily(), ret.getZoneName());
                    List<PplForecastPredictResultSplitDO> replacedData = ListUtils.filter(o,
                            i -> !Strings.equals(i.getZoneName(), newZoneName));
                    if (!replacedData.isEmpty()) {
                        List<String> info = replacedData.stream()
                                .map(r -> Strings.join(":", r.getZoneName(), r.getCoreNum()))
                                .collect(Collectors.toList());
                        ret.appendErrorMsg("可用区替换逻辑生效，被替换的为：%s ", Strings.join(",", info));
                    }
                    if (o.size() > 2) {
                        ret.appendErrorMsg("聚合了结果，拆分后的数据不止一条，存在错误");
                    }
                    ret.setZoneName(newZoneName);
                    BigDecimal sum = NumberUtils.sum(o, PplForecastPredictResultSplitDO::getCoreNum);
                    ret.setCoreNum(sum);
                    return ret;
                }
        ).values());

        ListUtils.concat(results, notSplitResults).forEach(o -> {
            o.setOutputVersionId(outputVersionId);
            o.setTaskId(taskId);
            if (Strings.isNotBlank(o.getInstanceModel())) {
                o.setCvmNum(o.getCoreNum().divide(BigDecimal.valueOf(o.getInstanceModelCpu()), 5, RoundingMode.HALF_UP)
                        .intValue());
            }
            o.setBillType(taskDO.getBillType());
            LocalDate statTime = o.getStatTime();
            LocalDate firstDayOfMonth = statTime.withDayOfMonth(1);
            o.setBeginBuyDate(firstDayOfMonth.plusDays(14)); // 以周中作为代表
            o.setEndBuyDate(firstDayOfMonth.plusDays(21)); // 开始和结束日期间隔1周
        });

        // 3、写入到split_middle
        demandDBHelper.insertBatchWithoutReturnId(results);
        demandDBHelper.insertBatchWithoutReturnId(notSplitResults);

        return Strings.join("\n", Logger.getLogs());
    }

    @Override
    public String splitEksLongTail(Long taskId, Long outputVersionId) {

        Logger.release();
        Logger.log("taskId = %s, 拆分的数据会插入到 outputVersionId = %s", taskId, outputVersionId);

        //  1、获取到taskId对应的条件sql，然后查询出机型、地域下各行业部门的新增和退回量
        PplForecastPredictTaskDO taskDO = demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new WrongWebParameterException("taskId不存在");
        }

        List<PplForecastPredictResultDO> predictData = getPredictData(taskDO);
        predictData = calculateNetIncrease(predictData);

        List<PplForecastPredictResultSplitDO> results
                = ListUtils.transform(predictData, PplForecastPredictResultSplitDO::transFrom);

        replaceNewInstanceType(results);

        Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
        ListUtils.concat(results).forEach(o -> {
            o.setOutputVersionId(outputVersionId);
            o.setTaskId(taskId);
            if (instanceTypeInfoMap.containsKey(o.getGinsFamily()) ) {
                InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(o.getGinsFamily());
                if (Strings.isNotBlank(instanceTypeInfo.getBigCoreInstanceModel())) {
                    o.setInstanceModel(instanceTypeInfo.getBigCoreInstanceModel());
                    o.setInstanceModelCpu(instanceTypeInfo.getBigCoreCpu());
                    o.setInstanceModelMem(instanceTypeInfo.getBigCoreMem());
                    o.setInstanceModelGpu(instanceTypeInfo.getBigGpu());
                    o.setCoreType("BIG");
                } else {
                    o.setInstanceModel(instanceTypeInfo.getSmallCoreInstanceModel());
                    o.setInstanceModelCpu(instanceTypeInfo.getSmallCoreCpu());
                    o.setInstanceModelMem(instanceTypeInfo.getSmallCoreMem());
                    o.setInstanceModelGpu(instanceTypeInfo.getSmallGpu());
                    o.setCoreType("SMALL");
                }
            }
            if (Strings.isNotBlank(o.getInstanceModel())) {
                o.setCvmNum(o.getCoreNum().divide(
                        BigDecimal.valueOf(o.getInstanceModelCpu()), 5, RoundingMode.HALF_UP) .intValue()
                );
            }
            o.setZoneName("随机可用区");
            o.setBillType(taskDO.getBillType());
            LocalDate statTime = o.getStatTime();
            LocalDate firstDayOfMonth = statTime.withDayOfMonth(1);
            o.setBeginBuyDate(firstDayOfMonth.plusDays(14)); // 以周中作为代表
            o.setEndBuyDate(firstDayOfMonth.plusDays(21)); // 开始和结束日期间隔1周
        });

        BigDecimal splitSum = NumberUtils.sum(results, PplForecastPredictResultSplitDO::getCoreNum);
        Logger.log1("当前拆分后的有效条数为: %d, 总核心数：%.1f ", results.size(), splitSum);

        demandDBHelper.insertBatchWithoutReturnId(results);

        return Strings.join("\n", Logger.getLogs());
    }

    @Override
    public String splitCdbLongTail(Long taskId, Long outputVersionId) {

        Logger.release();
        Logger.log("taskId = %s, 拆分的数据会插入到 outputVersionId = %s", taskId, outputVersionId);

        //  1、获取到taskId对应的条件sql，然后查询出机型、地域下各行业部门的新增和退回量
        PplForecastPredictTaskDO taskDO = demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        if (taskDO == null) {
            throw new WrongWebParameterException("taskId不存在");
        }

        List<PplForecastPredictResultDO> predictData = getPredictData(taskDO);
        predictData = calculateNetIncrease(predictData);

        List<PplForecastPredictResultSplitDO> results
                = ListUtils.transform(predictData, PplForecastPredictResultSplitDO::transFrom);

        ListUtils.concat(results).forEach(o -> {
            o.setOutputVersionId(outputVersionId);
            o.setTaskId(taskId);
            if (o.getGinsFamily().equals("SMALL")) {
                o.setGinsFamily("16C64G");
                o.setInstanceModel("16C64G");
                o.setCvmNum(o.getCoreNum().divide(BigDecimal.valueOf(64), 5, RoundingMode.HALF_UP).intValue());
                o.setCoreType("SMALL");
            }else{
                o.setGinsFamily("32C128G");
                o.setInstanceModel("32C128G");
                o.setCvmNum(o.getCoreNum().divide(BigDecimal.valueOf(128), 5, RoundingMode.HALF_UP).intValue());
                o.setCoreType("BIG");
            }
            o.setZoneName("随机可用区");
            o.setBillType(taskDO.getBillType());
            LocalDate statTime = o.getStatTime();
            LocalDate firstDayOfMonth = statTime.withDayOfMonth(1);
            o.setBeginBuyDate(firstDayOfMonth.plusDays(14)); // 以周中作为代表
            o.setEndBuyDate(firstDayOfMonth.plusDays(21)); // 开始和结束日期间隔1周
        });

        BigDecimal splitSum = NumberUtils.sum(results, PplForecastPredictResultSplitDO::getCoreNum);
        Logger.log1("当前拆分后的有效条数为: %d, 总核心数：%.1f ", results.size(), splitSum);

        demandDBHelper.insertBatchWithoutReturnId(results);

        return Strings.join("\n", Logger.getLogs());
    }


    @NotNull
    @SneakyThrows
    private List<PplForecastPredictResultSplitMiddleDO> replaceZoneName(
            CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture,
            List<PplForecastPredictResultSplitMiddleDO> results) {
        Map<String, PplForecastConfigPubRegionConfigDO> zoneMap = pubRegionConfigMapFuture.get();
        results = new ArrayList<>(ListUtils2.groupAndApply(results,
                (o) -> Strings.join("@",
                        o.getYear(),
                        o.getMonth(),
                        o.getPredictIndex(),
                        o.getGinsType(),
                        o.getType(),
                        o.getRegionName(),
                        o.getIndustryDept(),
                        o.getWeekStart(),
                        gerReplaceZoneName(zoneMap, o.getGinsFamily(), o.getZoneName())
                ),
                (k, o) -> {
                    PplForecastPredictResultSplitMiddleDO ret = o.get(0);
                    ret.setZoneName(gerReplaceZoneName(zoneMap, ret.getGinsFamily(), ret.getZoneName()));
                    BigDecimal sum = NumberUtils.sum(o, PplForecastPredictResultSplitMiddleDO::getCoreNum);
                    ret.setCoreNum(sum);
                    return ret;
                }
        ).values());
        return results;
    }

    @NotNull
    private List<PplForecastPredictResultSplitMiddleDO> splitWithUin(
            PplForecastPredictTaskDO taskDO,
            CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture,
            List<PplForecastPredictResultSplitMiddleDO> notSplitResults)
            throws InterruptedException, ExecutionException {
        List<PplForecastPredictResultSplitMiddleDO> results;
        Logger.start("获取历史最明细的数据，没有机型收敛");
        HistoryData historyData = getHistoryData(taskDO, null);
        Logger.stop();
        Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap = pubRegionConfigMapFuture.get();


        /*
         *  这一步拆分了行业部门
         */
        Logger.start("拆分行业部门");
        results = splitIndustryDept(taskDO, historyData);
        results = addEmptyToNotSplitResultsAndLog(results, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getIndustryDept, "行业部门");
        Logger.stop();

        /*
         * 这一步拆分可用区，顺序不可以乱，拆分这个动
         * 国家在可用区拆分这里带上
         */
        Logger.start("拆分可用区");
        results = splitIndustryDept2ZoneName(results, taskDO, historyData, pubRegionConfigMap);
        results = addEmptyToNotSplitResultsAndLog(results, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getZoneName, "可用区");
        Logger.stop();

        /*
         * 这一步拆分大小核心，顺序不可以乱，
         */
        Logger.start("拆分大小核心");
        results = splitZoneName2instanceType(results, taskDO, historyData, pubRegionConfigMap);
        results = addEmptyToNotSplitResultsAndLog(results, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getGinsType, "大小核心");
        Logger.stop();

        Logger.start("拆分周");
        results = splitWeek(results);
        results = addEmptyToNotSplitResultsAndLog(results, notSplitResults,
                (o) -> Objects.toString(o.getWeekStart()), "拆分周");
        Logger.stop();
        return results;
    }

    @SneakyThrows
    private List<PplForecastPredictResultSplitMiddleDO> splitInstanceModel(PplForecastPredictTaskDO taskDO,
            CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture,
            List<PplForecastPredictResultSplitMiddleDO> results,
            List<PplForecastPredictResultSplitMiddleDO> notSplitResults) {

        Logger.start("客户去重，拆分大小核心，获取历史数据");
        HistoryData historyData = getHistoryData(taskDO, NoUinSplitType.SPLIT_MODEL);
        Logger.stop();

        Function<PplSplitForMiddleDTO, String> reserveKey = o -> {
//            String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
            String zoneName = o.getZoneName();
            return Strings.join("@", o.getRegionName(), zoneName, o.getSmallOrBig());
        };
        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);
        Logger.log1("客户去重，拆分大小核心，历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc =
                o -> Strings.join("@", o.getGinsFamily(), o.getRegionName(), o.getZoneName());
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc,
                o -> o);

        List<PplForecastPredictResultSplitMiddleDO> resultsSplit = new ArrayList<>();

        for (PplForecastPredictResultSplitMiddleDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName(), source.getZoneName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);

            String ginsFamily = source.getGinsFamily();
            Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
            InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(ginsFamily);

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                if (splitSourceList == null) {
                    source.appendErrorMsg("机型地域可用区没有找到大小核心历史的拆分数据");
                    resultsSplit.add(source);
                    continue;
                }
            }

            BigDecimal totalWeightValue = PplForecastTypeEnum.isNew(source.getType()) ?
                    NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff) :
                    NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
            PplForecastTypeEnum curType = PplForecastTypeEnum.valueOf(source.getType());

            if (isZero(totalWeightValue)) {
                splitSourceList = splitSourceMap24.get(key);
                totalWeightValue = PplForecastTypeEnum.isNew(source.getType()) ?
                        NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff) :
                        NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
                if (isZero(totalWeightValue)) {
                    log.warn("机型地域{}, {}量为0，不拆分大小核心", key, curType.getTypeName());
                    source.appendErrorMsg("机型地域可用区 %s量为0，不拆分大小核心", curType.getTypeName());
                    resultsSplit.add(source);
                    continue;
                }
            }
            if (splitSourceList.size() == 1) {
                BigDecimal i = splitSourceList.get(0).getNewOrRetValue(source.getType());
                source.appendErrorMsg("历史数据只有一条，不拆分大小核心,历史值为" + i);
                smallBigNon(source, instanceTypeInfo);
                resultsSplit.add(source);
                continue;
            }

            Map<String, PplSplitForMiddleDTO> smallOrBig2threeMonthData =
                    ListUtils.toMap(splitSourceList, PplSplitForMiddleDTO::getSmallOrBig, Function.identity());

            PplForecastPredictResultSplitMiddleDO bigOne = null;
            PplForecastPredictResultSplitMiddleDO smallOne = null;

            for (Entry<String, PplSplitForMiddleDTO> keyValue : smallOrBig2threeMonthData.entrySet()) {
                String smallOrBig = keyValue.getKey();
                PplForecastPredictResultSplitMiddleDO clone = JSON.clone(source);
                BigDecimal historyValue = smallOrBig2threeMonthData.get(smallOrBig)
                        .getNewOrRetValue(curType.getType());
                BigDecimal splitValue = source.getCoreNum()
                        .multiply(historyValue)
                        .divide(totalWeightValue, 6, RoundingMode.HALF_UP);
                clone.setCoreNum(splitValue);
                if (instanceTypeInfo != null && smallOrBig.equals(PplForecastCoreTypeEnum.BIG.getType())) {
                    clone.setGinsType(instanceTypeInfo.getBigCoreInstanceModel());
                    clone.setCpuCoreNum(instanceTypeInfo.getBigCoreCpu());
                    bigOne = clone;
                } else if (instanceTypeInfo != null && smallOrBig.equals(PplForecastCoreTypeEnum.SMALL.getType())) {
                    clone.setGinsType(instanceTypeInfo.getSmallCoreInstanceModel());
                    clone.setCpuCoreNum(instanceTypeInfo.getSmallCoreCpu());
                    smallOne = clone;
                } else {
                    clone.setGinsType("");
                    smallOne = clone;
                }
            }
            // 当大小核心拆分完之后，如果大核心的机型不足1台，那么不拆分大小核心，都用小核心机型表示。
            Assert.notNull(instanceTypeInfo, "大小核心配置空");
            if (bigOne != null
                    && bigOne.getCoreNum().compareTo(BigDecimal.valueOf(instanceTypeInfo.getBigCoreCpu())) < 0) {
                bigOne.setCoreNum(source.getCoreNum());
                bigOne.appendErrorMsg("大核心不足一台，转为小核心");
                smallBigNon(bigOne, instanceTypeInfo);
                resultsSplit.add(bigOne);
                continue;
            }
            if (bigOne != null) {
                resultsSplit.add(bigOne);
            }
            if (smallOne != null) {
                resultsSplit.add(smallOne);
            }
        }
        return addEmptyToNotSplitResultsAndLog(resultsSplit, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getGinsType, "拆分大小核心");
    }

    private void smallBigNon(PplForecastPredictResultSplitDO source, InstanceTypeInfo instanceTypeInfo) {
        if (Strings.isNotBlank(instanceTypeInfo.getSmallCoreInstanceModel())) {
            source.setCoreType(PplForecastCoreTypeEnum.SMALL.getType());
            source.setInstanceModel(instanceTypeInfo.getSmallCoreInstanceModel());
            source.setInstanceModelCpu(instanceTypeInfo.getSmallCoreCpu());
            return;
        }
        if (Strings.isNotBlank(instanceTypeInfo.getBigCoreInstanceModel())) {
            source.setCoreType(PplForecastCoreTypeEnum.BIG.getType());
            source.setInstanceModel(instanceTypeInfo.getBigCoreInstanceModel());
            source.setInstanceModelCpu(instanceTypeInfo.getBigCoreCpu());
            source.appendErrorMsg("小核心不存在，使用用大核心");
            return;
        }
        source.appendErrorMsg("没有找到大小核心规格");
    }

    private void smallBigNon(PplForecastPredictResultSplitMiddleDO source, InstanceTypeInfo instanceTypeInfo) {
        if (Strings.isNotBlank(instanceTypeInfo.getSmallCoreInstanceModel())) {
            source.setGinsType(instanceTypeInfo.getSmallCoreInstanceModel());
            source.setCpuCoreNum(instanceTypeInfo.getSmallCoreCpu());
            return;
        }
        if (Strings.isNotBlank(instanceTypeInfo.getBigCoreInstanceModel())) {
            source.setGinsType(instanceTypeInfo.getBigCoreInstanceModel());
            source.setCpuCoreNum(instanceTypeInfo.getBigCoreCpu());
            source.appendErrorMsg("小核心不存在，使用用大核心");
            return;
        }
        source.appendErrorMsg("没有找到大小核心规格");
    }

    @NotNull
    @SneakyThrows
    private List<PplForecastPredictResultSplitMiddleDO> splitIndustry(
            PplForecastPredictTaskDO taskDO,
            CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture,
            List<PplForecastPredictResultSplitMiddleDO> results,
            List<PplForecastPredictResultSplitMiddleDO> notSplitResults) {

        Logger.start("客户去重，拆分行业部门，获取历史数据");
        HistoryData historyData = getHistoryData(taskDO, NoUinSplitType.SPLIT_INDUSTRY);
        Logger.stop();

        Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap = pubRegionConfigMapFuture.get();
        Function<PplSplitForMiddleDTO, String> reserveKey = o -> {
//            String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
            String zoneName = o.getZoneName();
            return Strings.join("@", o.getRegionName(), zoneName, o.getIndustryDept());
        };
        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);
        Logger.log1("客户去重，拆分行业部门，历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc =
                o -> Strings.join("@", o.getGinsFamily(), o.getRegionName(), o.getZoneName());
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc,
                o -> o);

        List<PplForecastPredictResultSplitMiddleDO> resultsSplit = new ArrayList<>();

        for (PplForecastPredictResultSplitMiddleDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName(), source.getZoneName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                if (splitSourceList == null) {
                    source.appendErrorMsg("机型地域可用区没有找到行业部门历史的拆分数据");
                    resultsSplit.add(source);
                    continue;
                }
            }

            BigDecimal totalWeightValue = PplForecastTypeEnum.isNew(source.getType()) ?
                    NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff) :
                    NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
            PplForecastTypeEnum curType = PplForecastTypeEnum.valueOf(source.getType());

            if (isZero(totalWeightValue)) {
                splitSourceList = splitSourceMap24.get(key);
                totalWeightValue = PplForecastTypeEnum.isNew(source.getType()) ?
                        NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff) :
                        NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
                if (isZero(totalWeightValue)) {
                    log.warn("机型地域{}, {}量为0，不拆分行业部门", key, curType.getTypeName());
                    source.appendErrorMsg("机型地域可用区 %s量为0，不拆分行业部门", curType.getTypeName());
                    resultsSplit.add(source);
                    continue;
                }
            }

            if (!PplForecastTypeEnum.isNew(source.getType()) && !PplForecastTypeEnum.isRet(source.getType())) {
                log.warn("机型地域可用区{}预测结果类型不是新增或退回，不拆分行业部门", key);
                source.appendErrorMsg("机型地域可用区预测结果类型不是新增或退回，不拆分行业部门");
                resultsSplit.add(source);
                continue;
            }

            Map<String, PplSplitForMiddleDTO> industryDept2threeMonthData =
                    ListUtils.toMap(splitSourceList, PplSplitForMiddleDTO::getIndustryDept, Function.identity());

            if (splitSourceList.size() == 1) {
                PplSplitForMiddleDTO pplSplitForMiddleDTO = splitSourceList.get(0);
                BigDecimal i = pplSplitForMiddleDTO.getNewOrRetValue(source.getType());
                source.appendErrorMsg("历史数据只有一条，不拆分行业部门,历史值为" + i);
                source.setIndustryDept(pplSplitForMiddleDTO.getIndustryDept());
                resultsSplit.add(source);
                continue;
            }

            List<PplForecastPredictResultSplitMiddleDO> curSplit = Lang.list();
            for (Entry<String, PplSplitForMiddleDTO> keyValue : industryDept2threeMonthData.entrySet()) {
                String industryDept = keyValue.getKey();
                PplForecastPredictResultSplitMiddleDO clone = JSON.clone(source);
                BigDecimal historyValue = industryDept2threeMonthData.get(industryDept)
                        .getNewOrRetValue(curType.getType());
                BigDecimal splitValue = source.getCoreNum()
                        .multiply(historyValue)
                        .divide(totalWeightValue, 6, RoundingMode.HALF_UP);
                clone.setIndustryDept(industryDept);
                clone.setCoreNum(splitValue);
                curSplit.add(clone);
            }

            resultsSplit.addAll(curSplit);
            BigDecimal diff = NumberUtils.sum(curSplit, PplForecastPredictResultSplitMiddleDO::getCoreNum)
                    .subtract(source.getCoreNum());
            if (diff.abs().compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                System.out.println("stop:" + diff);
            }

        }

        return addEmptyToNotSplitResultsAndLog(resultsSplit, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getIndustryDept, "拆分行业部门");
    }

    @SneakyThrows
    @NotNull
    private List<PplForecastPredictResultSplitMiddleDO> splitZoneName(
            PplForecastPredictTaskDO taskDO,
            CompletableFuture<Map<String, PplForecastConfigPubRegionConfigDO>> pubRegionConfigMapFuture,
            List<PplForecastPredictResultSplitMiddleDO> notSplitResults) {

        Logger.start("客户去重，拆分可用区，获取历史数据");
        HistoryData historyData = getHistoryData(taskDO, NoUinSplitType.SPLIT_ZONE);
        Logger.stop();

        Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap = pubRegionConfigMapFuture.get();
        Function<PplSplitForMiddleDTO, String> reserveKey = o -> {
//            String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
            String zoneName = o.getZoneName();
            return Strings.join("@", o.getRegionName(), zoneName);
        };
        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);
        Logger.log1("客户去重，拆分可用区，历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc =
                o -> Strings.join("@", o.getGinsFamily(), o.getRegionName());
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc,
                o -> o);

        Map<String, String> zoneName2customhouseTitle = getZone2countryMap(splitSource, splitSource24);

        List<PplForecastPredictResultSplitMiddleDO> resultsSplit = new ArrayList<>();

        List<PplForecastPredictResultDO> results = getPredictData(taskDO);
        for (PplForecastPredictResultDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                if (splitSourceList == null) {
                    resultsSplit.add(toMiddleDO(source, "", "机型地域没有找到可用区历史的拆分数据"));
                    continue;
                }
            }
            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            if (PplForecastTypeEnum.isNew(source.getForecastSeqType()) && isZero(newDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                if (isZero(newDiffSum)) {
                    log.warn("机型地域{}新增量为0，不拆分可用区", key);
                    resultsSplit.add(toMiddleDO(source, "", "机型地域新增量为0，不拆分可用区"));
                    continue;
                }
            }

            if (PplForecastTypeEnum.isRet(source.getForecastSeqType()) && isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
                if (isZero(retDiffSum)) {
                    log.warn("机型地域{}退回量为0，不拆分可用区", key);
                    resultsSplit.add(toMiddleDO(source, "", "机型地域退回量为0，不拆分可用区"));
                    continue;
                }
            }

            BigDecimal totalWeightValue = PplForecastTypeEnum.isNew(source.getForecastSeqType()) ?
                    newDiffSum : retDiffSum;

            if (!PplForecastTypeEnum.isNew(source.getForecastSeqType()) && !PplForecastTypeEnum.isRet(
                    source.getForecastSeqType())) {
                log.warn("机型地域{}预测结果类型不是新增或退回，不拆分可用区", key);
                resultsSplit.add(toMiddleDO(source, "",
                        "机型地域{}预测结果类型不是新增或退回，不拆分可用区"));
                continue;
            }

            Map<String, PplSplitForMiddleDTO> zoneName2threeMonthData =
                    ListUtils.toMap(splitSourceList, PplSplitForMiddleDTO::getZoneName, Function.identity());

            if (splitSourceList.size() == 1) {
                BigDecimal i = splitSourceList.get(0).getNewOrRetValue(source.getForecastSeqType());
                PplForecastPredictResultSplitMiddleDO target =
                        toMiddleDO(source, "", "历史数据只有一条，不拆分可用区,历史值为" + i);
                // 这里先设置正确
                target.setZoneName(splitSourceList.get(0).getZoneName());
                resultsSplit.add(target);
                continue;
            }

            List<PplForecastPredictResultSplitMiddleDO> curSplit = Lang.list();
            for (Entry<String, PplSplitForMiddleDTO> keyValue : zoneName2threeMonthData.entrySet()) {
                String zoneName = keyValue.getKey();
                PplSplitForMiddleDTO value = keyValue.getValue();
                PplForecastPredictResultSplitMiddleDO clone = toMiddleDO(source, "", "");
                BigDecimal historyValue = value.getNewOrRetValue(source.getForecastSeqType());
                BigDecimal splitValue = source.getCoreNum()
                        .multiply(historyValue)
                        .divide(totalWeightValue, 6, RoundingMode.HALF_UP);
                clone.setZoneName(zoneName);
                // 可用区这里顺带把 国家带上，不过上面有空的可用区可能设置不上
                clone.setCustomhouseTitle(zoneName2customhouseTitle.getOrDefault(zoneName, "EMPTY"));
                clone.setCoreNum(splitValue);
                curSplit.add(clone);
            }
            resultsSplit.addAll(curSplit);
            BigDecimal diff = NumberUtils.sum(curSplit, PplForecastPredictResultSplitMiddleDO::getCoreNum)
                    .subtract(source.getCoreNum());
            if (diff.abs().compareTo(BigDecimal.valueOf(0.01)) >= 0) {
                System.out.println("stop:" + diff);
            }
        }

        return addEmptyToNotSplitResultsAndLog(resultsSplit, notSplitResults,
                PplForecastPredictResultSplitMiddleDO::getZoneName, "拆分可用区");
    }

    private boolean isZero(BigDecimal newDiffSum) {
        return newDiffSum.compareTo(BigDecimal.ONE) < 0;
    }

    @NotNull
    private Map<String, String> getZone2countryMap(List<PplSplitForMiddleDTO> splitSource,
            List<PplSplitForMiddleDTO> splitSource24) {
        Map<String, String> zoneName2customhouseTitle = new HashMap<>();
        splitSource.forEach((o) -> zoneName2customhouseTitle.put(o.getZoneName(), o.getCustomhouseTitle()));
        splitSource24.forEach((o) -> zoneName2customhouseTitle.put(o.getZoneName(), o.getCustomhouseTitle()));
        return zoneName2customhouseTitle;
    }

    /**
     * 设置全部相同的字段
     */
    private void setUniqueValueColumn(Long outputVersionId, List<PplForecastPredictResultSplitMiddleDO> notSplitResults,
            List<PplForecastPredictResultSplitMiddleDO> results) {
        ListUtils.forEach(results, o -> {
            o.setOutputVersionId(outputVersionId);
            if (Strings.isNotBlank(o.getGinsType())) {
                o.setInstanceNum(o.getCoreNum()
                        .divide(BigDecimal.valueOf(o.getCpuCoreNum()), 5, RoundingMode.HALF_UP));
            }
        });
        ListUtils.forEach(notSplitResults, o -> {
            o.setOutputVersionId(outputVersionId);
            if (Strings.isNotBlank(o.getGinsType())) {
                o.setInstanceNum(o.getCoreNum()
                        .divide(BigDecimal.valueOf(o.getCpuCoreNum()), 5, RoundingMode.HALF_UP));
            }
        });
    }

    private List<PplForecastPredictResultSplitDO> addEmptyToNotSplitResultsAndLog1(
            List<PplForecastPredictResultSplitDO> results,
            List<PplForecastPredictResultSplitDO> notSplitResults,
            Function<PplForecastPredictResultSplitDO, String> getOneFieldValue,
            String msg) {

        List<PplForecastPredictResultSplitDO> curNotSplitResults = Lang.list();

        BigDecimal totalSum = NumberUtils.sum(results, PplForecastPredictResultSplitDO::getCoreNum);
        Logger.log1("当前拆分后的总条数为: %d, 总核心数：%.1f ", results.size(), totalSum);

        results = results.stream().filter((o) -> {
            boolean blank = Strings.isBlank(getOneFieldValue.apply(o)) || "null".equals(getOneFieldValue.apply(o));
            if (blank) {
                curNotSplitResults.add(o);
                o.appendErrorMsg("拆分 %s 后出现空数据，此条是没有拆分的数据", msg);
            }
            return !blank;
        }).collect(Collectors.toList());
        BigDecimal notSplitSum = NumberUtils.sum(curNotSplitResults, PplForecastPredictResultSplitDO::getCoreNum);
        BigDecimal splitSum = NumberUtils.sum(results, PplForecastPredictResultSplitDO::getCoreNum);

        @AllArgsConstructor
        class NumInfo {

            public BigDecimal totalCoreNum;
            public Integer size;
        }
        Map<String, NumInfo> detailMap = ListUtils2.groupAndApply(results,
                getOneFieldValue,
                (key, list) -> new NumInfo(
                        NumberUtils.sum(list, PplForecastPredictResultSplitDO::getCoreNum),
                        list.size()
                )
        );

        Logger.log1("当前拆分后的有效条数为: %d, 总核心数：%.1f ", results.size(), splitSum);
        detailMap.forEach((k, v) ->
                Logger.log1("\t - 拆分后 %10s 的条数为: %d,  总核心数：%.1f ", k, v.size, v.totalCoreNum));
        Logger.log1("当前未拆分(无效)条数为: %d, 总核心数：%.1f ", curNotSplitResults.size(), notSplitSum);

        notSplitResults.addAll(curNotSplitResults);
        return results;
    }

    private List<PplForecastPredictResultSplitMiddleDO> addEmptyToNotSplitResultsAndLog(
            List<PplForecastPredictResultSplitMiddleDO> results,
            List<PplForecastPredictResultSplitMiddleDO> notSplitResults,
            Function<PplForecastPredictResultSplitMiddleDO, String> getOneFieldValue,
            String msg) {

        List<PplForecastPredictResultSplitMiddleDO> curNotSplitResults = Lang.list();

        BigDecimal totalSum = NumberUtils.sum(results, PplForecastPredictResultSplitMiddleDO::getCoreNum);
        Logger.log1("当前拆分后的总条数为: %d, 总核心数：%.1f ", results.size(), totalSum);

        results = results.stream().filter((o) -> {
            boolean blank = Strings.isBlank(getOneFieldValue.apply(o)) || "null".equals(getOneFieldValue.apply(o));
            if (blank) {
                curNotSplitResults.add(o);
                o.appendErrorMsg("拆分 %s 后出现空数据，此条是没有拆分的数据", msg);
            }
            return !blank;
        }).collect(Collectors.toList());
        BigDecimal notSplitSum = NumberUtils.sum(curNotSplitResults, PplForecastPredictResultSplitMiddleDO::getCoreNum);
        BigDecimal splitSum = NumberUtils.sum(results, PplForecastPredictResultSplitMiddleDO::getCoreNum);

        @AllArgsConstructor
        class NumInfo {

            public BigDecimal totalCoreNum;
            public Integer size;
        }
        Map<String, NumInfo> detailMap = ListUtils2.groupAndApply(results,
                getOneFieldValue,
                (key, list) -> new NumInfo(
                        NumberUtils.sum(list, PplForecastPredictResultSplitMiddleDO::getCoreNum),
                        list.size()
                )
        );

        Logger.log1("当前拆分后的有效条数为: %d, 总核心数：%.1f ", results.size(), splitSum);
        detailMap.forEach((k, v) ->
                Logger.log1("\t - 拆分后 %10s 的条数为: %d,  总核心数：%.1f ", k, v.size, v.totalCoreNum));
        Logger.log1("当前未拆分(无效)条数为: %d, 总核心数：%.1f ", curNotSplitResults.size(), notSplitSum);

        notSplitResults.addAll(curNotSplitResults);
        return results;
    }

    @NotNull
    private List<PplForecastPredictResultSplitMiddleDO> splitWeek(List<PplForecastPredictResultSplitMiddleDO> results) {

        results.forEach((o) -> {
            LocalDate weekDate = o.getStatTime().withDayOfMonth(15);
            o.setWeekStart(weekDate);
        });
        // 先不拆周了， 按照月中的时间来设置week
        if (true) {
            return results;
        }

        @AllArgsConstructor
        @NoArgsConstructor
        @Data
        class DataWithKey extends PplForecastPredictResultSplitMiddleDO {

            String key;
        }
        // 内部类不暴露
        List<DataWithKey> splitByDate = new ArrayList<>();
        // 拆成最细粒度的数据，然后重新聚合就可以了
        for (PplForecastPredictResultSplitMiddleDO result : results) {
            YearMonth ym = YearMonth.of(result.getYear(), result.getMonth());
            int splitNum = ym.lengthOfMonth();
            BigDecimal totalMonthCore = result.getCoreNum();
            BigDecimal oneDayCore = totalMonthCore.divide(BigDecimal.valueOf(splitNum), 8, RoundingMode.HALF_UP);
            // 对于每一天
            for (int i = 1; i <= splitNum; i++) {
                LocalDate date = ym.atDay(i);
                // 自然周的开始日期
                // predictIndex 不可以使用了
                LocalDate weekStart = date.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                String key = Strings.join("@", result.getIndustryDept(), result.getRegionName(),
                        result.getGinsFamily(), result.getGinsType(), result.getZoneName(), result.getType()
                );
                DataWithKey dataWithKey = new DataWithKey(key);
                BeanUtils.copyProperties(result, dataWithKey);
                dataWithKey.setWeekStart(weekStart);
                // 每天的比例是一样的
                dataWithKey.setCoreNum(oneDayCore);
                splitByDate.add(dataWithKey);
            }
        }

        Map<String, List<DataWithKey>> groupByWeek = ListUtils.groupBy(splitByDate,
                (o) -> Strings.join("@", o.getWeekStart(), o.getKey()));

        // 1 2 3 4(开始) 5 6 7 8 9 10 11(开始) 12 13；   如果大于3乘以系数
        List<PplForecastPredictResultSplitMiddleDO> list = Lang.list();

        BigDecimal totalAdd = BigDecimal.ZERO;

        for (String key : groupByWeek.keySet()) {
            List<DataWithKey> value = groupByWeek.get(key);
            BigDecimal sum = NumberUtils.sum(value, PplForecastPredictResultSplitMiddleDO::getCoreNum);
            PplForecastPredictResultSplitMiddleDO ret = value.get(0);
            if (value.size() > 7) {
                throw BizException.makeThrow("周天数大于7，不应该出现这个错误");
            }
            if (value.size() >= 4 && value.size() < 7) {
                BigDecimal multiplyNum = BigDecimal.valueOf(7).divide(
                        BigDecimal.valueOf(value.size()), 8, RoundingMode.HALF_UP);
                BigDecimal multiply = sum.multiply(multiplyNum);
                totalAdd = totalAdd.add(multiply).subtract(sum);
                ret.appendErrorMsg("周天数 %d 天, 少于7天，增加 %.1f 倍，原来 sum:%.1f , 增加比例后：%.1f",
                        value.size(), multiplyNum, sum, multiply);
                sum = multiply;
            }
            ret.setCoreNum(sum);
            if (value.size() < 4) {
                ret.setWeekStart(null);
                ret.appendErrorMsg("周天数不足4条了，不拆周， 丢弃");
            }

            // dbhelper 不支持不同对象
            PplForecastPredictResultSplitMiddleDO copy = new PplForecastPredictResultSplitMiddleDO();
            BeanUtils.copyProperties(ret, copy);
            list.add(copy);
        }
        Logger.log1("最后一周天数不够，总共增加了: %.1f 核", totalAdd);

        return list;
    }


    @AllArgsConstructor
    @Getter
    enum NoUinSplitType {
        // 按顺序拆，不可以乱
        // 拆分可用区
        SPLIT_ZONE("SPLIT_ZONE",
                "/sql/ppl13week_forecast/middle/split_history/monthly_data_for_split_nouin_zone.sql"),
        // 拆分行业部门
        SPLIT_INDUSTRY("SPLIT_ZONE",
                "/sql/ppl13week_forecast/middle/split_history/monthly_data_for_split_nouin_zone_industry.sql"),
        // 拆分大小核心机型
        SPLIT_MODEL("SPLIT_MODEL",
                "/sql/ppl13week_forecast/middle/split_history/monthly_data_for_split_nouin_zone_instancemodel.sql");
        String name;
        String sqlFile;
    }

    @SneakyThrows
    @NotNull
    private HistoryData getHistoryData(PplForecastPredictTaskDO taskDO, NoUinSplitType noUinSplitType) {

        String sql;
        if (noUinSplitType != null) {
            sql = ORMUtils.getSql(noUinSplitType.getSqlFile());
        } else {
            sql = ORMUtils.getSql("/sql/ppl13week_forecast/middle/split_history/monthly_data_for_split_uin.sql");
        }

        LocalDate inputDateEnd = taskDO.getInputDateEnd().withDayOfMonth(taskDO.getInputDateEnd().lengthOfMonth());
        // 默认3个月
        LocalDate inputDateStart = inputDateEnd.minusMonths(2).withDayOfMonth(1);

        if (Strings.isBlank(taskDO.getInputSqlConditionNew())) {
            throw new WrongWebParameterException("新增量拆分条件为空，请检查");
        }
        sql = sql.replace("${CONDITION}", taskDO.getInputSqlConditionNew());

        Map<String, Object> params = new HashMap<>();
        params.put("inputDateEnd", inputDateEnd);
        params.put("inputDateStart", inputDateStart);

        sql = sql.replace("/*${WEB_CONDITION}*/", " and stat_time in (:predictDateWeb) ");
        LocalDate start = inputDateStart;
        List<LocalDate> monthEndDates = new ArrayList<>();
        while (start.isBefore(inputDateEnd)) {
            monthEndDates.add(start.withDayOfMonth(start.lengthOfMonth()));
            start = start.plusMonths(1);
        }
        params.put("predictDateWeb", monthEndDates);

        String finalSql = sql;
        HashMap<String, Object> params1 = new HashMap<>(params);
        CompletableFuture<List<PplSplitForMiddleDTO>> future1 = CompletableFuture.supplyAsync(
                () -> ckcldStdCrpDBHelper.getRaw(PplSplitForMiddleDTO.class, finalSql, params1));

        // 准备一个最近12个月的数据，用于当最近3个月没有执行数据时的兜底
        inputDateStart = inputDateEnd.minusMonths(12);
        start = inputDateStart;
        monthEndDates = new ArrayList<>();
        while (start.isBefore(inputDateEnd)) {
            monthEndDates.add(start.withDayOfMonth(start.lengthOfMonth()));
            start = start.plusMonths(1);
        }
        params.put("predictDateWeb", monthEndDates);

        params.put("inputDateStart", inputDateStart);
        CompletableFuture<List<PplSplitForMiddleDTO>> future2 = CompletableFuture.supplyAsync(
                () -> ckcldStdCrpDBHelper.getRaw(PplSplitForMiddleDTO.class, finalSql, params));

        return new HistoryData(future1.get(), future2.get());
    }

    private List<PplForecastPredictResultSplitDO> splitRegionName2instanceModel(
            List<PplForecastPredictResultSplitDO> results, HistoryData historyData) {

        // 这里把 年月也带上，需要保留一个趋势，实际上是拆分的时候也做一个预测
        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> Strings.join("@", o.getYear(), o.getMonth(), o.getRegionName(), o.getSmallOrBig());

        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

        Logger.log1("城市拆大小核心历史数据获取到 %d 条", splitSource.size());

        // zoneName 上面已经替换了，这里不用替换了
        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName());

        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

        List<PplForecastPredictResultSplitDO> resultsSplit = new ArrayList<>();
        for (PplForecastPredictResultSplitDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            boolean has3month = true;
            String ginsFamily = source.getGinsFamily();
            Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
            InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(ginsFamily);

            if (Strings.isBlank(instanceTypeInfo.getBigCoreInstanceModel())) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setSmallCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg("默认大核心为空，直接用小核心");
                continue;
            }

            if (Strings.isBlank(instanceTypeInfo.getSmallCoreInstanceModel())) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setBigCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg("默认小核心为空，直接用大核心");
                continue;
            }
            // 如果核心数小于等于该机型的 小核心默认机型的核心数+大核心默认机型的核心数， 那就是小核心了, 因为拆了也是会变回小核心
            int needSplitCoreNum = instanceTypeInfo.getSmallCoreCpu() + instanceTypeInfo.getBigCoreCpu();
            if (source.getCoreNum().compareTo(BigDecimal.valueOf(needSplitCoreNum)) < 0) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setSmallCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg(
                        "核心数小于等于该机型的 小核心默认机型的核心数+大核心默认机型的核心数,直接用小核心");
                continue;
            }

            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            // 只要一个为0，用近一年的数据
            if (splitSourceList == null || isZero(newDiffSum) || isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
                if (splitSourceList == null || isZero(newDiffSum) || isZero(retDiffSum)) {
                    BigDecimal half = source.getCoreNum().multiply(new BigDecimal("0.5"));
                    if (instanceTypeInfo.getBigCoreCpu() > half.intValue()) {
                        resultsSplit.add(source);
                        source.setSmallCoreTypeModel(instanceTypeInfo);
                        source.appendErrorMsg("机型地域可用区没有找到对应的拆分数据且不够大核心，直接用小核心");
                        continue;
                    }
                    PplForecastPredictResultSplitDO big = JSON.clone(source);
                    PplForecastPredictResultSplitDO small = JSON.clone(source);
                    resultsSplit.add(big);
                    resultsSplit.add(small);
                    big.setBigCoreTypeModel(instanceTypeInfo);
                    small.setSmallCoreTypeModel(instanceTypeInfo);
                    String info = String.format("机型地域可用区没有找到对应的拆分数据: %s,大小平分", key);
                    big.appendErrorMsg(info);
                    small.appendErrorMsg(info);
                    big.setCoreNum(half);
                    small.setCoreNum(half);
                    continue;
                }
            }

            if (checkHistoryData(resultsSplit, source, key, splitSourceList)) {
                continue;
            }

            // 按可用区,大小核心,历史3个月的平均值作为默认的权重，这里一定有数据，不然上面的 总量为0就过滤掉了
            // 正常情况下， value.size() == 3
            Map<String, List<PplSplitForMiddleDTO>> smallOrBig2threeMonthData = ListUtils.groupBy(splitSourceList,
                    PplSplitForMiddleDTO::getSmallOrBig);

            Function<PplSplitForMiddleDTO, BigDecimal> getNewOrRetValue =
                    PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType()) ?
                            PplSplitForMiddleDTO::getNewDiff : PplSplitForMiddleDTO::getRetDiff;

            // 默认是用 3个月的平均值作为权重
            // 考虑了特殊情况 当第一个月没有值， 23个月有值的时候，用  2+3 的值除以2， 而不是除以 3
            Map<String, BigDecimal> smallOrBig2weightValue = smallOrBig2threeMonthData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<PplSplitForMiddleDTO> list = entry.getValue();
                                BigDecimal sum = list.stream().map(getNewOrRetValue)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                return sum.divide(BigDecimal.valueOf(list.size()), RoundingMode.HALF_UP);
                            }
                    ));
            Map<String, String> smallOrBig2Info = new HashMap<>();
            if (has3month) {
                for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {
                    // 3个月的时候去更新权重值, 上面的 has3month 是数据源，这里进来还要判断
                    List<PplSplitForMiddleDTO> value = keyValue.getValue().stream().sorted(
                                    Comparator.comparing(PplSplitForMiddleDTO::getYear)
                                            .thenComparing(PplSplitForMiddleDTO::getMonth))
                            .collect(Collectors.toList());
                    if (value.size() == 3) {
                        // 启用最小二乘法预测 3个月的趋势
                        BigDecimal predict4value = fitting3value(getNewOrRetValue.apply(value.get(0)),
                                getNewOrRetValue.apply(value.get(1)), getNewOrRetValue.apply(value.get(2)));
                        // 看看预测 predict4value 出来的权重是不是偏离，最后一个月的 50%，如果是排除, 兜底方法
                        BigDecimal lastMonth = getNewOrRetValue.apply(value.get(2));
                        BigDecimal halfLastMonth = lastMonth.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                        BigDecimal addHalfLastMonth = lastMonth.add(halfLastMonth);
                        if (predict4value.compareTo(halfLastMonth) >= 0
                                && predict4value.compareTo(addHalfLastMonth) <= 0) {
                            // 用预测出来的权重去更新
                            String info = String.format("最小2乘法预测大小核心比例: %.1f, %.1f, %.1f  ->  %.1f",
                                    getNewOrRetValue.apply(value.get(0)), getNewOrRetValue.apply(value.get(1)),
                                    getNewOrRetValue.apply(value.get(2)), predict4value);
                            smallOrBig2weightValue.put(keyValue.getKey(), predict4value);
                            smallOrBig2Info.put(keyValue.getKey(), info);
                        }
                    }
                }

            }
            // 新的权重
            BigDecimal totalWeightValue = NumberUtils.sum(smallOrBig2weightValue.values(), Function.identity());

            PplForecastPredictResultSplitDO bigOne = null;
            PplForecastPredictResultSplitDO smallOne = null;
            for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {

                String smallOrBig = keyValue.getKey();
                PplForecastPredictResultSplitDO clone = JSON.clone(source);

                BigDecimal newDiff;
                if (smallOrBig2weightValue.size() == 1) {
                    clone.appendErrorMsg("历史数据只有一条，不拆分实例规格, 直接用原来的数据");
                    newDiff = source.getCoreNum();
                } else if (totalWeightValue.compareTo(BigDecimal.ONE) < 0) {
                    newDiff = source.getCoreNum().multiply(BigDecimal.valueOf(1))
                            .divide(BigDecimal.valueOf(smallOrBig2threeMonthData.size()), 6, RoundingMode.HALF_UP);
                    clone.appendErrorMsg("拆机型总的权重为0，平均分每一个key，正常不会出现, key:%s, value: %s",
                            Strings.join(",", smallOrBig2weightValue.keySet()),
                            Strings.join(",", smallOrBig2weightValue.values())
                    );
                } else {
                    newDiff = source.getCoreNum().multiply(smallOrBig2weightValue.get(smallOrBig)
                            .divide(totalWeightValue, 6, RoundingMode.HALF_UP));
                }

                if (smallOrBig2Info.containsKey(smallOrBig)) {
                    Logger.log2(smallOrBig2Info.get(smallOrBig));
                    clone.appendErrorMsg(smallOrBig2Info.get(smallOrBig));
                }
                if (smallOrBig.equals(PplForecastCoreTypeEnum.BIG.getType())) {
                    clone.setBigCoreTypeModel(instanceTypeInfo);
                    bigOne = clone;
                } else if (smallOrBig.equals(PplForecastCoreTypeEnum.SMALL.getType())) {
                    clone.setSmallCoreTypeModel(instanceTypeInfo);
                    smallOne = clone;
                }

                clone.setCoreNum(newDiff);
            }

            // 当大小核心拆分完之后，如果大核心的机型不足1台，那么不拆分大小核心，都用小核心机型表示。
            Assert.notNull(instanceTypeInfo, "大小核心配置空");
            if (bigOne != null
                    && bigOne.getCoreNum().compareTo(BigDecimal.valueOf(instanceTypeInfo.getBigCoreCpu())) < 0) {
                smallBigNon(bigOne, instanceTypeInfo);
                bigOne.setCoreNum(source.getCoreNum());
                resultsSplit.add(bigOne);
                continue;
            }
            if (bigOne != null) {
                resultsSplit.add(bigOne);
            }
            if (smallOne != null) {
                resultsSplit.add(smallOne);
            }
        }

        return resultsSplit;
    }

    private List<PplForecastPredictResultSplitDO> splitZoneName2instanceModel(
            List<PplForecastPredictResultSplitDO> results, HistoryData historyData) {

        // 这里把 年月也带上，需要保留一个趋势，实际上是拆分的时候也做一个预测
        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> {
//                    String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
                    return Strings.join("@", o.getYear(), o.getMonth(),
                            o.getRegionName(), o.getZoneName(), o.getSmallOrBig());
                };

        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

        Logger.log1("可用区拆大小核心历史数据获取到 %d 条", splitSource.size());

        // zoneName 上面已经替换了，这里不用替换了
        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getZoneName());

        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

        List<PplForecastPredictResultSplitDO> resultsSplit = new ArrayList<>();
        for (PplForecastPredictResultSplitDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName(), source.getZoneName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            boolean has3month = true;
            String ginsFamily = source.getGinsFamily();
            Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
            InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(ginsFamily);

            if (Strings.isBlank(instanceTypeInfo.getBigCoreInstanceModel())) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setSmallCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg("默认大核心为空，直接用小核心");
                continue;
            }

            if (Strings.isBlank(instanceTypeInfo.getSmallCoreInstanceModel())) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setBigCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg("默认小核心为空，直接用大核心");
                continue;
            }
            // 如果核心数小于等于该机型的 小核心默认机型的核心数+大核心默认机型的核心数， 那就是小核心了, 因为拆了也是会变回小核心
            int needSplitCoreNum = instanceTypeInfo.getSmallCoreCpu() + instanceTypeInfo.getBigCoreCpu();
            if (source.getCoreNum().compareTo(BigDecimal.valueOf(needSplitCoreNum)) < 0) {
                PplForecastPredictResultSplitDO clone = JSON.clone(source);
                resultsSplit.add(clone);
                clone.setSmallCoreTypeModel(instanceTypeInfo);
                clone.appendErrorMsg(
                        "核心数小于等于该机型的 小核心默认机型的核心数+大核心默认机型的核心数,直接用小核心");
                continue;
            }

            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            // 只要一个为0，用近一年的数据
            if (splitSourceList == null || isZero(newDiffSum) || isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
                if (splitSourceList == null || isZero(newDiffSum) || isZero(retDiffSum)) {
                    BigDecimal half = source.getCoreNum().multiply(new BigDecimal("0.5"));
                    if (instanceTypeInfo.getBigCoreCpu() > half.intValue()) {
                        resultsSplit.add(source);
                        source.setSmallCoreTypeModel(instanceTypeInfo);
                        source.appendErrorMsg("机型地域可用区没有找到对应的拆分数据且不够大核心，直接用小核心");
                        continue;
                    }
                    PplForecastPredictResultSplitDO big = JSON.clone(source);
                    PplForecastPredictResultSplitDO small = JSON.clone(source);
                    resultsSplit.add(big);
                    resultsSplit.add(small);
                    big.setBigCoreTypeModel(instanceTypeInfo);
                    small.setSmallCoreTypeModel(instanceTypeInfo);
                    String info = String.format("机型地域可用区没有找到对应的拆分数据: %s,大小平分", key);
                    big.appendErrorMsg(info);
                    small.appendErrorMsg(info);
                    big.setCoreNum(half);
                    small.setCoreNum(half);
                    continue;
                }
            }

            if (checkHistoryData(resultsSplit, source, key, splitSourceList)) {
                continue;
            }

            // 按可用区,大小核心,历史3个月的平均值作为默认的权重，这里一定有数据，不然上面的 总量为0就过滤掉了
            // 正常情况下， value.size() == 3
            Map<String, List<PplSplitForMiddleDTO>> smallOrBig2threeMonthData = ListUtils.groupBy(splitSourceList,
                    PplSplitForMiddleDTO::getSmallOrBig);

            Function<PplSplitForMiddleDTO, BigDecimal> getNewOrRetValue =
                    PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType()) ?
                            PplSplitForMiddleDTO::getNewDiff : PplSplitForMiddleDTO::getRetDiff;

            // 默认是用 3个月的平均值作为权重
            // 考虑了特殊情况 当第一个月没有值， 23个月有值的时候，用  2+3 的值除以2， 而不是除以 3
            Map<String, BigDecimal> smallOrBig2weightValue = smallOrBig2threeMonthData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<PplSplitForMiddleDTO> list = entry.getValue();
                                BigDecimal sum = list.stream().map(getNewOrRetValue)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                return sum.divide(BigDecimal.valueOf(list.size()), RoundingMode.HALF_UP);
                            }
                    ));
            Map<String, String> smallOrBig2Info = new HashMap<>();
            if (has3month) {
                for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {
                    // 3个月的时候去更新权重值, 上面的 has3month 是数据源，这里进来还要判断
                    List<PplSplitForMiddleDTO> value = keyValue.getValue().stream().sorted(
                                    Comparator.comparing(PplSplitForMiddleDTO::getYear)
                                            .thenComparing(PplSplitForMiddleDTO::getMonth))
                            .collect(Collectors.toList());
                    if (value.size() == 3) {
                        // 启用最小二乘法预测 3个月的趋势
                        BigDecimal predict4value = fitting3value(getNewOrRetValue.apply(value.get(0)),
                                getNewOrRetValue.apply(value.get(1)), getNewOrRetValue.apply(value.get(2)));
                        // 看看预测 predict4value 出来的权重是不是偏离，最后一个月的 50%，如果是排除, 兜底方法
                        BigDecimal lastMonth = getNewOrRetValue.apply(value.get(2));
                        BigDecimal halfLastMonth = lastMonth.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                        BigDecimal addHalfLastMonth = lastMonth.add(halfLastMonth);
                        if (predict4value.compareTo(halfLastMonth) >= 0
                                && predict4value.compareTo(addHalfLastMonth) <= 0) {
                            // 用预测出来的权重去更新
                            String info = String.format("最小2乘法预测大小核心比例: %.1f, %.1f, %.1f  ->  %.1f",
                                    getNewOrRetValue.apply(value.get(0)), getNewOrRetValue.apply(value.get(1)),
                                    getNewOrRetValue.apply(value.get(2)), predict4value);
                            smallOrBig2weightValue.put(keyValue.getKey(), predict4value);
                            smallOrBig2Info.put(keyValue.getKey(), info);
                        }
                    }
                }

            }
            // 新的权重
            BigDecimal totalWeightValue = NumberUtils.sum(smallOrBig2weightValue.values(), Function.identity());

            PplForecastPredictResultSplitDO bigOne = null;
            PplForecastPredictResultSplitDO smallOne = null;
            for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {

                String smallOrBig = keyValue.getKey();
                PplForecastPredictResultSplitDO clone = JSON.clone(source);

                BigDecimal newDiff;
                if (smallOrBig2weightValue.size() == 1) {
                    clone.appendErrorMsg("历史数据只有一条，不拆分实例规格, 直接用原来的数据");
                    newDiff = source.getCoreNum();
                } else if (totalWeightValue.compareTo(BigDecimal.ONE) < 0) {
                    newDiff = source.getCoreNum().multiply(BigDecimal.valueOf(1))
                            .divide(BigDecimal.valueOf(smallOrBig2threeMonthData.size()), 6, RoundingMode.HALF_UP);
                    clone.appendErrorMsg("拆机型总的权重为0，平均分每一个key，正常不会出现, key:%s, value: %s",
                            Strings.join(",", smallOrBig2weightValue.keySet()),
                            Strings.join(",", smallOrBig2weightValue.values())
                    );
                } else {
                    newDiff = source.getCoreNum().multiply(smallOrBig2weightValue.get(smallOrBig)
                            .divide(totalWeightValue, 6, RoundingMode.HALF_UP));
                }

                if (smallOrBig2Info.containsKey(smallOrBig)) {
                    Logger.log2(smallOrBig2Info.get(smallOrBig));
                    clone.appendErrorMsg(smallOrBig2Info.get(smallOrBig));
                }
                if (smallOrBig.equals(PplForecastCoreTypeEnum.BIG.getType())) {
                    clone.setBigCoreTypeModel(instanceTypeInfo);
                    bigOne = clone;
                } else if (smallOrBig.equals(PplForecastCoreTypeEnum.SMALL.getType())) {
                    clone.setSmallCoreTypeModel(instanceTypeInfo);
                    smallOne = clone;
                }

                clone.setCoreNum(newDiff);
            }

            // 当大小核心拆分完之后，如果大核心的机型不足1台，那么不拆分大小核心，都用小核心机型表示。
            Assert.notNull(instanceTypeInfo, "大小核心配置空");
            if (bigOne != null
                    && bigOne.getCoreNum().compareTo(BigDecimal.valueOf(instanceTypeInfo.getBigCoreCpu())) < 0) {
                smallBigNon(bigOne, instanceTypeInfo);
                bigOne.setCoreNum(source.getCoreNum());
                resultsSplit.add(bigOne);
                continue;
            }
            if (bigOne != null) {
                resultsSplit.add(bigOne);
            }
            if (smallOne != null) {
                resultsSplit.add(smallOne);
            }
        }

        return resultsSplit;
    }


    private List<PplForecastPredictResultSplitMiddleDO> splitZoneName2instanceType(
            List<PplForecastPredictResultSplitMiddleDO> results,
            PplForecastPredictTaskDO taskDO,
            HistoryData historyData,
            Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap) {

        // 这里把 年月也带上，需要保留一个趋势，实际上是拆分的时候也做一个预测
        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> {
//                    String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
                    return Strings.join("@", o.getYear(), o.getMonth(),
                            o.getRegionName(), o.getIndustryDept(), o.getZoneName(), o.getSmallOrBig());
                };

        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

        Logger.log1("可用区拆大小核心历史数据获取到 %d 条", splitSource.size());

        // zoneName 上面已经替换了，这里不用替换了
        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getIndustryDept(), o.getZoneName());

        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

        List<PplForecastPredictResultSplitMiddleDO> resultsSplit = new ArrayList<>();
        for (PplForecastPredictResultSplitMiddleDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName(),
                    source.getIndustryDept(), source.getZoneName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            boolean has3month = true;
            String ginsFamily = source.getGinsFamily();
            Map<String, InstanceTypeInfo> instanceTypeInfoMap = ppl13weekPredictService.getInstanceTypeInfo();
            InstanceTypeInfo instanceTypeInfo = instanceTypeInfoMap.get(ginsFamily);

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                if (splitSourceList == null) {
                    String info = String.format("机型地域行业可用区没有找到对应的拆分数据: %s", key);
                    appendErrorMsgAndLog(source, info);
                    resultsSplit.add(source);
                    continue;
                }
            }

            if (checkHistoryData(resultsSplit, source, key, splitSourceList)) {
                continue;
            }

            // 按可用区,大小核心,历史3个月的平均值作为默认的权重，这里一定有数据，不然上面的 总量为0就过滤掉了
            // 正常情况下， value.size() == 3
            Map<String, List<PplSplitForMiddleDTO>> smallOrBig2threeMonthData = ListUtils.groupBy(splitSourceList,
                    PplSplitForMiddleDTO::getSmallOrBig);

            Function<PplSplitForMiddleDTO, BigDecimal> getNewOrRetValue =
                    PplForecastTypeEnum.NEW.getType().equals(source.getType()) ?
                            PplSplitForMiddleDTO::getNewDiff : PplSplitForMiddleDTO::getRetDiff;

            // 默认是用 3个月的平均值作为权重
            // 考虑了特殊情况 当第一个月没有值， 23个月有值的时候，用  2+3 的值除以2， 而不是除以 3
            Map<String, BigDecimal> smallOrBig2weightValue = smallOrBig2threeMonthData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<PplSplitForMiddleDTO> list = entry.getValue();
                                BigDecimal sum = list.stream().map(getNewOrRetValue)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                return sum.divide(BigDecimal.valueOf(list.size()), RoundingMode.HALF_UP);
                            }
                    ));
            Map<String, String> smallOrBig2Info = new HashMap<>();
            if (has3month) {
                for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {
                    // 3个月的时候去更新权重值, 上面的 has3month 是数据源，这里进来还要判断
                    List<PplSplitForMiddleDTO> value = keyValue.getValue().stream().sorted(
                                    Comparator.comparing(PplSplitForMiddleDTO::getYear)
                                            .thenComparing(PplSplitForMiddleDTO::getMonth))
                            .collect(Collectors.toList());
                    if (value.size() == 3) {
                        // 启用最小二乘法预测 3个月的趋势
                        BigDecimal predict4value = fitting3value(getNewOrRetValue.apply(value.get(0)),
                                getNewOrRetValue.apply(value.get(1)), getNewOrRetValue.apply(value.get(2)));
                        // 看看预测 predict4value 出来的权重是不是偏离，最后一个月的 50%，如果是排除, 兜底方法
                        BigDecimal lastMonth = getNewOrRetValue.apply(value.get(2));
                        BigDecimal halfLastMonth = lastMonth.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                        BigDecimal addHalfLastMonth = lastMonth.add(halfLastMonth);
                        if (predict4value.compareTo(halfLastMonth) >= 0
                                && predict4value.compareTo(addHalfLastMonth) <= 0) {
                            // 用预测出来的权重去更新
                            String info = String.format("最小2乘法预测大小核心比例: %.1f, %.1f, %.1f  ->  %.1f",
                                    getNewOrRetValue.apply(value.get(0)), getNewOrRetValue.apply(value.get(1)),
                                    getNewOrRetValue.apply(value.get(2)), predict4value);
                            smallOrBig2weightValue.put(keyValue.getKey(), predict4value);
                            smallOrBig2Info.put(keyValue.getKey(), info);
                        }
                    }
                }

            }
            // 新的权重
            BigDecimal totalWeightValue = NumberUtils.sum(smallOrBig2weightValue.values(), Function.identity());

            PplForecastPredictResultSplitMiddleDO bigOne = null;
            PplForecastPredictResultSplitMiddleDO smallOne = null;
            for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : smallOrBig2threeMonthData.entrySet()) {

                String smallOrBig = keyValue.getKey();
                PplForecastPredictResultSplitMiddleDO clone = JSON.clone(source);

                BigDecimal newDiff;
                if (smallOrBig2weightValue.size() == 1) {
                    clone.appendErrorMsg("历史数据只有一条，不拆分实例规格, 直接用原来的数据");
                    newDiff = source.getCoreNum();
                } else if (totalWeightValue.compareTo(BigDecimal.ONE) < 0) {
                    newDiff = source.getCoreNum().multiply(BigDecimal.valueOf(1))
                            .divide(BigDecimal.valueOf(smallOrBig2threeMonthData.size()), 6, RoundingMode.HALF_UP);
                    clone.appendErrorMsg("拆机型总的权重为0，平均分每一个key，正常不会出现, key:%s, value: %s",
                            Strings.join(",", smallOrBig2weightValue.keySet()),
                            Strings.join(",", smallOrBig2weightValue.values())
                    );
                } else {
                    newDiff = source.getCoreNum().multiply(smallOrBig2weightValue.get(smallOrBig)
                            .divide(totalWeightValue, 6, RoundingMode.HALF_UP));
                }

                if (smallOrBig2Info.containsKey(smallOrBig)) {
                    Logger.log2(smallOrBig2Info.get(smallOrBig));
                    clone.appendErrorMsg(smallOrBig2Info.get(smallOrBig));
                }
                if (instanceTypeInfo != null && smallOrBig.equals(PplForecastCoreTypeEnum.BIG.getType())) {
                    clone.setGinsType(instanceTypeInfo.getBigCoreInstanceModel());
                    clone.setCpuCoreNum(instanceTypeInfo.getBigCoreCpu());
                    bigOne = clone;
                } else if (instanceTypeInfo != null && smallOrBig.equals(PplForecastCoreTypeEnum.SMALL.getType())) {
                    clone.setGinsType(instanceTypeInfo.getSmallCoreInstanceModel());
                    clone.setCpuCoreNum(instanceTypeInfo.getSmallCoreCpu());
                    smallOne = clone;
                } else {
                    clone.setGinsType("");
                    smallOne = clone;
                }
                clone.setCoreNum(newDiff);
            }

            // 当大小核心拆分完之后，如果大核心的机型不足1台，那么不拆分大小核心，都用小核心机型表示。
            Assert.notNull(instanceTypeInfo, "大小核心配置空");
            if (bigOne != null
                    && bigOne.getCoreNum().compareTo(BigDecimal.valueOf(instanceTypeInfo.getBigCoreCpu())) < 0) {
                smallBigNon(bigOne, instanceTypeInfo);
                bigOne.setCoreNum(source.getCoreNum());
                resultsSplit.add(bigOne);
                continue;
            }
            if (bigOne != null) {
                resultsSplit.add(bigOne);
            }
            if (smallOne != null) {
                resultsSplit.add(smallOne);
            }
        }

        return resultsSplit;
    }

    private boolean checkHistoryData(List<PplForecastPredictResultSplitDO> resultsSplit,
            PplForecastPredictResultSplitDO source, String key, List<PplSplitForMiddleDTO> splitSourceList) {

        BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
        BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

        // 可用区和行业，只要一个小于0 就用历史 24 个月的数据
        // 这里不拦截了，已经很少数据了

        if (PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType())
                && isZero(newDiffSum)) {
            String info = String.format("机型地域可用区的新增量为0，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        if (PplForecastTypeEnum.RET.getType().equals(source.getForecastSeqType())
                && isZero(retDiffSum)) {
            String info = String.format("机型地域可用区的退回量为0，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        if (!PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType()) &&
                !PplForecastTypeEnum.RET.getType().equals(source.getForecastSeqType())) {
            String info = String.format("机型地域可用区预测结果类型不是新增或退回，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        return false;
    }


    private boolean checkHistoryData(List<PplForecastPredictResultSplitMiddleDO> resultsSplit,
            PplForecastPredictResultSplitMiddleDO source, String key, List<PplSplitForMiddleDTO> splitSourceList) {

        BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
        BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

        // 可用区和行业，只要一个小于0 就用历史 24 个月的数据
        // 这里不拦截了，已经很少数据了

        if (PplForecastTypeEnum.NEW.getType().equals(source.getType())
                && isZero(newDiffSum)) {
            String info = String.format("机型地域行业可用区的新增量为0，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        if (PplForecastTypeEnum.RET.getType().equals(source.getType())
                && isZero(retDiffSum)) {
            String info = String.format("机型地域行业可用区的退回量为0，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        if (!PplForecastTypeEnum.NEW.getType().equals(source.getType()) &&
                !PplForecastTypeEnum.RET.getType().equals(source.getType())) {
            String info = String.format("机型地域行业可用区预测结果类型不是新增或退回，不拆分: %s", key);
            appendErrorMsgAndLog(source, info);
            resultsSplit.add(source);
            return true;
        }
        return false;
    }

    private void appendErrorMsgAndLog(PplForecastPredictResultSplitMiddleDO source, String info) {
        log.warn(info);
        Logger.log2(info);
        source.appendErrorMsg(info);
    }

    private void appendErrorMsgAndLog(PplForecastPredictResultSplitDO source, String info) {
        log.warn(info);
        Logger.log2(info);
        source.appendErrorMsg(info);
    }

    //  预测 3 个数， 用最最简单 y = a + kx 线性预，最小二乘法拟合，3个点效率很快
    // 一些测试值
    //  1 2 3           -> 4
    //  3 4 6           -> 7
    //  1 20 100        -> 139.33333333333334
    //  10  5 10      -> 8.333333333333334

    public BigDecimal fitting3value(BigDecimal a, BigDecimal b, BigDecimal c) {
        SimpleRegression regression = new SimpleRegression();
        regression.addData(1, a.doubleValue());
        regression.addData(2, b.doubleValue());
        regression.addData(3, c.doubleValue());

        double slope = regression.getSlope();
        double intercept = regression.getIntercept();

        double predictedY4 = slope * 4 + intercept;
        return BigDecimal.valueOf(predictedY4);
    }


    // 改成 main 可以测试
    public static void test() {
        double[][] data = {{1, 10}, {2, 5}, {3, 10}};

        SimpleRegression regression = new SimpleRegression();
        for (double[] point : data) {
            regression.addData(point[0], point[1]);
        }

        double slope = regression.getSlope();
        double intercept = regression.getIntercept();

        double predictedY4 = slope * 4 + intercept;
        System.out.println("The predicted value at x = 4 is: " + predictedY4);
    }

    private List<PplForecastPredictResultSplitDO>
    splitRegionName2ZoneName(List<PplForecastPredictResultSplitDO> results,
            HistoryData historyData) {

//        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/middle/monthly_data_for_industry_dept_zone_name_split.sql");

        // 这里把 年月也带上，需要保留一个趋势，实际上是拆分的时候也做一个预测
        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> Strings.join("@", o.getYear(), o.getMonth(), o.getRegionName(), o.getZoneName());
//        里层会保留机型
        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

        Logger.log1("历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName());

        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

//        Map<String, String> zoneName2customhouseTitle = getZone2countryMap(splitSource, splitSource24);

        List<PplForecastPredictResultSplitDO> resultsSplit = new ArrayList<>();
        for (PplForecastPredictResultSplitDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            boolean has3month = true;

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                if (splitSourceList == null) {
                    log.warn("机型地域{}没有找到对应的拆分数据", key);
                    source.appendErrorMsg("机型地域没有找到对应的拆分数据");
                    resultsSplit.add(source);
                    continue;
                }
            }

            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            if (isZero(newDiffSum) || isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
            }

            if (PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType())
                    && isZero(newDiffSum)) {
                log.warn("机型地域{}新增量为0，不拆分", key);
                source.appendErrorMsg("机型地域新增量为0，不拆分");
                resultsSplit.add(source);
                continue;
            }
            if (PplForecastTypeEnum.RET.getType().equals(source.getForecastSeqType())
                    && isZero(retDiffSum)) {
                log.warn("机型地域{}退回量为0，不拆分", key);
                source.appendErrorMsg("机型地域退回量为0，不拆分");
                resultsSplit.add(source);
                continue;
            }
            if (!PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType()) &&
                    !PplForecastTypeEnum.RET.getType().equals(source.getForecastSeqType())) {
                log.warn("机型地域{}预测结果类型不是新增或退回，不拆分", key);
                source.appendErrorMsg("机型地域预测结果类型不是新增或退回，不拆分");
                resultsSplit.add(source);
                continue;
            }

            // 按可用区,历史3个月的平均值作为默认的权重，这里一定有数据，不然上面的 总量为0就过滤掉了
            // 正常情况下， value.size() == 3
            Map<String, List<PplSplitForMiddleDTO>> zoneName2threeMonthData = ListUtils.groupBy(splitSourceList,
                    PplSplitForMiddleDTO::getZoneName);

            Function<PplSplitForMiddleDTO, BigDecimal> getNewOrRetValue =
                    PplForecastTypeEnum.NEW.getType().equals(source.getForecastSeqType()) ?
                            PplSplitForMiddleDTO::getNewDiff : PplSplitForMiddleDTO::getRetDiff;

            // 默认是用 3个月的平均值作为权重
            // 考虑了特殊情况 当第一个月没有值， 23个月有值的时候，用  2+3 的值除以2， 而不是除以 3
            Map<String, BigDecimal> zoneName2weightValue = zoneName2threeMonthData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<PplSplitForMiddleDTO> list = entry.getValue();
                                BigDecimal sum = list.stream().map(getNewOrRetValue)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                return sum.divide(BigDecimal.valueOf(list.size()), RoundingMode.HALF_UP);
                            }
                    ));
            Map<String, String> zoneName2Info = new HashMap<>();
            if (has3month) {
                for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : zoneName2threeMonthData.entrySet()) {
                    // 3个月的时候去更新权重值, 上面的 has3month 是数据源，这里进来还要判断
                    List<PplSplitForMiddleDTO> value = keyValue.getValue().stream().sorted(
                                    Comparator.comparing(PplSplitForMiddleDTO::getYear)
                                            .thenComparing(PplSplitForMiddleDTO::getMonth))
                            .collect(Collectors.toList());
                    if (value.size() == 3) {
                        // 启用最小二乘法预测 3个月的趋势
                        BigDecimal predict4value = fitting3value(getNewOrRetValue.apply(value.get(0)),
                                getNewOrRetValue.apply(value.get(1)), getNewOrRetValue.apply(value.get(2)));
                        // 看看预测 predict4value 出来的权重是不是偏离，最后一个月的 50%，如果是排除, 兜底方法
                        BigDecimal lastMonth = getNewOrRetValue.apply(value.get(2));
                        BigDecimal halfLastMonth = lastMonth.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                        BigDecimal addHalfLastMonth = lastMonth.add(halfLastMonth);
                        if (predict4value.compareTo(halfLastMonth) >= 0
                                && predict4value.compareTo(addHalfLastMonth) <= 0) {
                            // 用预测出来的权重去更新
                            String info = String.format("最小2乘法预测可用区历史比例: %.1f, %.1f, %.1f  ->  %.1f",
                                    getNewOrRetValue.apply(value.get(0)), getNewOrRetValue.apply(value.get(1)),
                                    getNewOrRetValue.apply(value.get(2)), predict4value);
                            zoneName2weightValue.put(keyValue.getKey(), predict4value);
                            zoneName2Info.put(keyValue.getKey(), info);
                        }
                    }
                }

            }
            // 新的权重
            BigDecimal totalWeightValue = NumberUtils.sum(zoneName2weightValue.values(), Function.identity());

            for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : zoneName2threeMonthData.entrySet()) {

                String zoneName = keyValue.getKey();
                PplForecastPredictResultSplitDO clone = JSON.clone(source);

                BigDecimal newDiff;
                if (zoneName2threeMonthData.size() == 1) {
                    newDiff = source.getCoreNum();
                    clone.appendErrorMsg("历史数据只有一条，不拆分可用区");
                } else if (totalWeightValue.compareTo(BigDecimal.ONE) < 0) {
                    newDiff = source.getCoreNum().multiply(BigDecimal.valueOf(1))
                            .divide(BigDecimal.valueOf(zoneName2weightValue.size()), 6, RoundingMode.HALF_UP);
                    clone.appendErrorMsg("拆可用区的总的权重为0，平均分每一个key，正常不会出现, key:%s, value: %s",
                            Strings.join(",", zoneName2weightValue.keySet()),
                            Strings.join(",", zoneName2weightValue.values())
                    );
                } else {
                    newDiff = source.getCoreNum().multiply(zoneName2weightValue.get(zoneName)
                            .divide(totalWeightValue, 6, RoundingMode.HALF_UP));
                }
                clone.setZoneName(zoneName);
                // 可用区这里顺带把 国家带上，不过上面有空的可用区可能设置不上
                // todo 这里原来没有国家这个字段，这里也展示不用这个字段
//                clone.setCustomhouseTitle(zoneName2customhouseTitle.getOrDefault(zoneName, "EMPTY"));
                clone.setCoreNum(newDiff);
                if (zoneName2Info.containsKey(zoneName)) {
                    Logger.log2(zoneName2Info.get(zoneName));
                    clone.appendErrorMsg(zoneName2Info.get(zoneName));
                }
                resultsSplit.add(clone);
            }
        }

        return resultsSplit;
    }

    private List<PplForecastPredictResultSplitMiddleDO> splitIndustryDept2ZoneName(
            List<PplForecastPredictResultSplitMiddleDO> results,
            PplForecastPredictTaskDO taskDO,
            HistoryData historyData,
            Map<String, PplForecastConfigPubRegionConfigDO> pubRegionConfigMap) {

//        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/middle/monthly_data_for_industry_dept_zone_name_split.sql");

        // 这里把 年月也带上，需要保留一个趋势，实际上是拆分的时候也做一个预测
        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> {
//                    String zoneName = gerReplaceZoneName(pubRegionConfigMap, o.getGinsFamily(), o.getZoneName());
                    return Strings.join("@", o.getYear(), o.getMonth(),
                            o.getRegionName(), o.getIndustryDept(), o.getZoneName());
                };
//        HistoryData historyData = getHistoryDataAndMergeGinsFamily(taskDO, sql,reserveKey);

        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

        Logger.log1("历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName(), o.getIndustryDept());

        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

        Map<String, String> zoneName2customhouseTitle = getZone2countryMap(splitSource, splitSource24);

        List<PplForecastPredictResultSplitMiddleDO> resultsSplit = new ArrayList<>();
        for (PplForecastPredictResultSplitMiddleDO source : results) {
            String key = StringTools.join("@", source.getGinsFamily(), source.getRegionName(),
                    source.getIndustryDept());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            boolean has3month = true;

            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                if (splitSourceList == null) {
                    log.warn("机型地域{}没有找到对应的拆分数据", key);
                    source.appendErrorMsg("机型地域行业没有找到对应的拆分数据");
                    resultsSplit.add(source);
                    continue;
                }
            }

            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            if (isZero(newDiffSum) || isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                has3month = false;
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
            }

            if (PplForecastTypeEnum.NEW.getType().equals(source.getType())
                    && isZero(newDiffSum)) {
                log.warn("机型地域{}新增量为0，不拆分", key);
                source.appendErrorMsg("机型地域新增量为0，不拆分");
                resultsSplit.add(source);
                continue;
            }
            if (PplForecastTypeEnum.RET.getType().equals(source.getType())
                    && isZero(retDiffSum)) {
                log.warn("机型地域{}退回量为0，不拆分", key);
                source.appendErrorMsg("机型地域退回量为0，不拆分");
                resultsSplit.add(source);
                continue;
            }
            if (!PplForecastTypeEnum.NEW.getType().equals(source.getType()) &&
                    !PplForecastTypeEnum.RET.getType().equals(source.getType())) {
                log.warn("机型地域{}预测结果类型不是新增或退回，不拆分", key);
                source.appendErrorMsg("机型地域预测结果类型不是新增或退回，不拆分");
                resultsSplit.add(source);
                continue;
            }

            // 按可用区,历史3个月的平均值作为默认的权重，这里一定有数据，不然上面的 总量为0就过滤掉了
            // 正常情况下， value.size() == 3
            Map<String, List<PplSplitForMiddleDTO>> zoneName2threeMonthData = ListUtils.groupBy(splitSourceList,
                    PplSplitForMiddleDTO::getZoneName);

            Function<PplSplitForMiddleDTO, BigDecimal> getNewOrRetValue =
                    PplForecastTypeEnum.NEW.getType().equals(source.getType()) ?
                            PplSplitForMiddleDTO::getNewDiff : PplSplitForMiddleDTO::getRetDiff;

            // 默认是用 3个月的平均值作为权重
            // 考虑了特殊情况 当第一个月没有值， 23个月有值的时候，用  2+3 的值除以2， 而不是除以 3
            Map<String, BigDecimal> zoneName2weightValue = zoneName2threeMonthData.entrySet().stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> {
                                List<PplSplitForMiddleDTO> list = entry.getValue();
                                BigDecimal sum = list.stream().map(getNewOrRetValue)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                return sum.divide(BigDecimal.valueOf(list.size()), RoundingMode.HALF_UP);
                            }
                    ));
            Map<String, String> zoneName2Info = new HashMap<>();
            if (has3month) {
                for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : zoneName2threeMonthData.entrySet()) {
                    // 3个月的时候去更新权重值, 上面的 has3month 是数据源，这里进来还要判断
                    List<PplSplitForMiddleDTO> value = keyValue.getValue().stream().sorted(
                                    Comparator.comparing(PplSplitForMiddleDTO::getYear)
                                            .thenComparing(PplSplitForMiddleDTO::getMonth))
                            .collect(Collectors.toList());
                    if (value.size() == 3) {
                        // 启用最小二乘法预测 3个月的趋势
                        BigDecimal predict4value = fitting3value(getNewOrRetValue.apply(value.get(0)),
                                getNewOrRetValue.apply(value.get(1)), getNewOrRetValue.apply(value.get(2)));
                        // 看看预测 predict4value 出来的权重是不是偏离，最后一个月的 50%，如果是排除, 兜底方法
                        BigDecimal lastMonth = getNewOrRetValue.apply(value.get(2));
                        BigDecimal halfLastMonth = lastMonth.divide(BigDecimal.valueOf(2), RoundingMode.HALF_UP);
                        BigDecimal addHalfLastMonth = lastMonth.add(halfLastMonth);
                        if (predict4value.compareTo(halfLastMonth) >= 0
                                && predict4value.compareTo(addHalfLastMonth) <= 0) {
                            // 用预测出来的权重去更新
                            String info = String.format("最小2乘法预测可用区历史比例: %.1f, %.1f, %.1f  ->  %.1f",
                                    getNewOrRetValue.apply(value.get(0)), getNewOrRetValue.apply(value.get(1)),
                                    getNewOrRetValue.apply(value.get(2)), predict4value);
                            zoneName2weightValue.put(keyValue.getKey(), predict4value);
                            zoneName2Info.put(keyValue.getKey(), info);
                        }
                    }
                }

            }
            // 新的权重
            BigDecimal totalWeightValue = NumberUtils.sum(zoneName2weightValue.values(), Function.identity());

            for (Entry<String, List<PplSplitForMiddleDTO>> keyValue : zoneName2threeMonthData.entrySet()) {

                String zoneName = keyValue.getKey();
                PplForecastPredictResultSplitMiddleDO clone = JSON.clone(source);

                BigDecimal newDiff;
                if (zoneName2threeMonthData.size() == 1) {
                    newDiff = source.getCoreNum();
                    clone.appendErrorMsg("历史数据只有一条，不拆分可用区");
                } else if (totalWeightValue.compareTo(BigDecimal.ONE) < 0) {
                    newDiff = source.getCoreNum().multiply(BigDecimal.valueOf(1))
                            .divide(BigDecimal.valueOf(zoneName2weightValue.size()), 6, RoundingMode.HALF_UP);
                    clone.appendErrorMsg("拆可用区的总的权重为0，平均分每一个key，正常不会出现, key:%s, value: %s",
                            Strings.join(",", zoneName2weightValue.keySet()),
                            Strings.join(",", zoneName2weightValue.values())
                    );
                } else {
                    newDiff = source.getCoreNum().multiply(zoneName2weightValue.get(zoneName)
                            .divide(totalWeightValue, 6, RoundingMode.HALF_UP));
                }
                clone.setZoneName(zoneName);
                // 可用区这里顺带把 国家带上，不过上面有空的可用区可能设置不上
                clone.setCustomhouseTitle(zoneName2customhouseTitle.getOrDefault(zoneName, "EMPTY"));
                clone.setCoreNum(newDiff);
                if (zoneName2Info.containsKey(zoneName)) {
                    Logger.log2(zoneName2Info.get(zoneName));
                    clone.appendErrorMsg(zoneName2Info.get(zoneName));
                }
                resultsSplit.add(clone);
            }
        }

        return resultsSplit;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class HistoryData {

        // 最近 3个月的数据
        List<PplSplitForMiddleDTO> splitSource;
        // 最近 12 个月
        List<PplSplitForMiddleDTO> splitSource24;
    }

    @NotNull
    private List<PplForecastPredictResultSplitMiddleDO> splitIndustryDept(PplForecastPredictTaskDO taskDO,
            HistoryData historyData) {

//        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/middle/monthly_data_for_industry_dept_split.sql");

        Function<PplSplitForMiddleDTO, String> reserveKey =
                o -> Strings.join("@", o.getRegionName(), o.getIndustryDept());
        // 保留region 和 industry 维度的数， 额外收揽机型

        List<PplSplitForMiddleDTO> splitSource = mergeInstanceType(historyData.getSplitSource(), reserveKey);
        List<PplSplitForMiddleDTO> splitSource24 = mergeInstanceType(historyData.getSplitSource24(), reserveKey);

//        HistoryData historyDataOld = getHistoryDataAndMergeGinsFamily(taskDO, sql, reserveKey);
//        List<PplSplitForMiddleDTO> splitSource1 = historyDataOld.getSplitSource();

        Logger.log1("历史数据获取到 %d 条", splitSource.size());

        Function<PplSplitForMiddleDTO, String> mapFunc = o ->
                StringTools.join("@", o.getGinsFamily(), o.getRegionName());
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap = ListUtils.toMapList(splitSource, mapFunc, o -> o);
        Map<String, List<PplSplitForMiddleDTO>> splitSourceMap24 = ListUtils.toMapList(splitSource24, mapFunc, o -> o);

        List<PplForecastPredictResultDO> predictResult = getPredictData(taskDO);

        List<PplForecastPredictResultSplitMiddleDO> results = new ArrayList<>();
        // 2、对于每一条predict_result，按上面的比例拆分
        for (PplForecastPredictResultDO pr : predictResult) {
            String key = StringTools.join("@", pr.getGinsFamily(), pr.getRegionName());
            List<PplSplitForMiddleDTO> splitSourceList = splitSourceMap.get(key);
            if (splitSourceList == null) {
                splitSourceList = splitSourceMap24.get(key);
                if (splitSourceList == null) {
                    log.warn("机型地域{}没有找到对应的拆分数据", key);
                    results.add(toMiddleDO(pr, "", "机型地域没有找到对应的行业部门拆分比例"));
                    continue;
                }
            }

            BigDecimal newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);

            if (isZero(newDiffSum) || isZero(retDiffSum)) {
                splitSourceList = splitSourceMap24.get(key);
                newDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getNewDiff);
                retDiffSum = NumberUtils.sum(splitSourceList, PplSplitForMiddleDTO::getRetDiff);
            }

            if (PplForecastTypeEnum.NEW.getType().equals(pr.getForecastSeqType()) && isZero(newDiffSum)) {
                log.warn("机型地域{}新增量为0，不拆分", key);
                results.add(toMiddleDO(pr, "", "机型地域新增量为0，不拆分"));
                continue;
            }
            if (PplForecastTypeEnum.RET.getType().equals(pr.getForecastSeqType()) && isZero(retDiffSum)) {
                log.warn("机型地域{}退回量为0，不拆分", key);
                results.add(toMiddleDO(pr, "", "机型地域退回量为0，不拆分"));
                continue;
            }
            if (!PplForecastTypeEnum.NEW.getType().equals(pr.getForecastSeqType()) &&
                    !PplForecastTypeEnum.RET.getType().equals(pr.getForecastSeqType())) {
                log.warn("机型地域{}预测结果类型不是新增或退回，不拆分", key);
                results.add(toMiddleDO(pr, "", "机型地域预测结果类型不是新增或退回，不拆分"));
                continue;
            }

            for (PplSplitForMiddleDTO ss : splitSourceList) {
                if (PplForecastTypeEnum.NEW.getType().equals(pr.getForecastSeqType())
                        && newDiffSum.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal newDiff = pr.getCoreNum().multiply(ss.getNewDiff())
                            .divide(newDiffSum, 6, RoundingMode.HALF_UP);
                    PplForecastPredictResultSplitMiddleDO middleDO = toMiddleDO(pr, ss.getIndustryDept(), "");
                    middleDO.setCoreNum(newDiff);
                    results.add(middleDO);
                } else if (PplForecastTypeEnum.RET.getType().equals(pr.getForecastSeqType())
                        && retDiffSum.compareTo(BigDecimal.ZERO) > 0) {
                    BigDecimal retDiff = pr.getCoreNum().multiply(ss.getRetDiff())
                            .divide(retDiffSum, 6, RoundingMode.HALF_UP);
                    PplForecastPredictResultSplitMiddleDO middleDO = toMiddleDO(pr, ss.getIndustryDept(), "");
                    middleDO.setCoreNum(retDiff);
                    results.add(middleDO);
                }
            }
        }

        return results;
    }

    @NotNull
    private List<PplForecastPredictResultDO> getPredictData(PplForecastPredictTaskDO taskDO) {
        Long taskId = taskDO.getId();
        List<PplForecastPredictResultDO> predictResult = demandDBHelper.getAll(PplForecastPredictResultDO.class,
                "where task_id=?", taskId);
        Logger.log1("***taskId %d 的预测条数是:%d, 预测原始总核心: %f***", taskId, predictResult.size(),
                predictResult.stream().mapToDouble((o) -> o.getCoreNum().doubleValue()).sum());
        return predictResult;
    }

    private List<PplSplitForMiddleDTO> mergeInstanceType(List<PplSplitForMiddleDTO> list,
            Function<PplSplitForMiddleDTO, String> reserveKey) {
        Map<String, String> instanceTypeToGroup = new HashMap<>();
        List<Mrpv2CommonInstanceTypeConfigDO> configDOS = demandDBHelper.getAll(Mrpv2CommonInstanceTypeConfigDO.class,
                "where use_forecast=1");
        for (Mrpv2CommonInstanceTypeConfigDO configDO : configDOS) {
            try {
                String[] instanceTypes = configDO.getInstanceTypes().split("[,|，]");
                if (instanceTypes.length == 0) {
                    continue;
                }
                for (String instanceType : instanceTypes) {
                    if (StringTools.isBlank(instanceType)) {
                        continue;
                    }
                    instanceTypeToGroup.put(instanceType, instanceTypes[0]);
                }
            } catch (Exception e) {
                log.warn("getCommonStaticFieldFromList ex:", e);
            }
        }

        Function<PplSplitForMiddleDTO, String> groupByKeyWithMergedInstanceType = o -> StringTools.join("@",
                reserveKey.apply(o), instanceTypeToGroup.getOrDefault(o.getGinsFamily(), o.getGinsFamily()));
        Map<String, List<PplSplitForMiddleDTO>> mapList =
                ListUtils.toMapList(list, groupByKeyWithMergedInstanceType, o -> o);

        List<PplSplitForMiddleDTO> result = new ArrayList<>();
        for (List<PplSplitForMiddleDTO> vlist : mapList.values()) {
            PplSplitForMiddleDTO first = JSON.clone(vlist.get(0));
            BigDecimal newDiff = NumberUtils.sum(vlist, PplSplitForMiddleDTO::getNewDiff);
            BigDecimal retDiff = NumberUtils.sum(vlist, PplSplitForMiddleDTO::getRetDiff);
            BigDecimal lastDayNum = NumberUtils.sum(vlist, PplSplitForMiddleDTO::getLastDayNum);
            first.setLastDayNum(lastDayNum);
            first.setNewDiff(newDiff);
            first.setRetDiff(retDiff);
            first.setGinsFamily(instanceTypeToGroup.getOrDefault(first.getGinsFamily(), first.getGinsFamily()));
            result.add(first);
        }

        return result;
    }

    private static PplForecastPredictResultSplitMiddleDO toMiddleDO(PplForecastPredictResultDO resultDO,
            String industryDept,
            String errorMsg) {
        PplForecastPredictResultSplitMiddleDO middleDO = new PplForecastPredictResultSplitMiddleDO();
        middleDO.setCreateTime(resultDO.getCreateTime());
        middleDO.setTaskId(resultDO.getTaskId());
        middleDO.setResultId(resultDO.getId());
        middleDO.setStatTime(resultDO.getStatTime());
        middleDO.setPredictIndex(resultDO.getPredictIndex());
        middleDO.setYear(resultDO.getYear());
        middleDO.setMonth(resultDO.getMonth());
        middleDO.setGinsFamily(resultDO.getGinsFamily());
        middleDO.setRegionName(resultDO.getRegionName());
        middleDO.setIndustryDept(industryDept);
        middleDO.setType(resultDO.getForecastSeqType());
        middleDO.setCoreNum(resultDO.getCoreNum());
        middleDO.setErrorMsg(errorMsg);
        return middleDO;

    }

}
