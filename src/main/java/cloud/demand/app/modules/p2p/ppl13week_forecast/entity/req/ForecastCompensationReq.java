package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class ForecastCompensationReq {

    /**
     * 要补偿的方案前缀
     */
    @NotBlank(message = "方案前缀不能为空: categoryPrefix")
    private String categoryPrefix;

    /**本批次调整的备注*/
    private String note;

    /**
     * 补偿的明细
     */
    private List<CompensationDetailDTO> details;

    /**
     * 补偿的明细，按机型、年月、其他属性来
     */
    @Data
    public static class CompensationDetailDTO {
        /**机型*/
        @NotBlank(message = "补偿机型不能为空: instanceType")
        private String instanceType;
        /**补偿时参考的机型，可选，当为空时，取instanceType*/
        private String refInstanceType;

        /**补偿时参考的历史N个月的增量占比*/
        @NotNull(message = "补偿时参考的N个月不能为空: refMonths")
        private Integer refMonths;

        /**补偿的年*/
        private Integer year;
        /**补偿的月*/
        private Integer month;

        /**补偿的核心数，正数加，负数减*/
        @NotNull(message = "补偿的核心数不能为空: compensationCore")
        private Integer compensationCore;

        /**境内外属性，可选，为空表示不分，取值：境内、境外*/
        private String customhouseTitle;
    }

}
