package cloud.demand.app.modules.p2p.ppl13week.dto.dict;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class InstanceModelAndTypeNameDTO {

    @Column("instance_model")
    private String instanceModel;

    @Column("instance_type_name")
    private String instanceTypeName;

    @Column("parse_core")
    private Integer parseCore;

    @Column("parse_ram")
    private Integer parseRam;

}
