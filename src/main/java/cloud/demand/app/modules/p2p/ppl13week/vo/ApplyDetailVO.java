package cloud.demand.app.modules.p2p.ppl13week.vo;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

@Data
public class ApplyDetailVO {

    @Column(value = "yunxiao_order_id")
    private String yunxiaoOrderId;

    @Column(value = "yunxiao_detail_id")
    private Long yunxiaoDetailId;

    @Column(value = "yunxiao_order_status")
    private String yunxiaoOrderStatus;

    @Column(value = "product")
    private String product;

    @Column(value = "demand_type")
    private String demandType;

    @Column(value = "demand_scene")
    private String demandScene;

    @Column(value = "bill_type")
    private String billType;

    @Column(value = "begin_buy_date")
    private String beginBuyDate;

    @Column(value = "end_buy_date")
    private String endBuyDate;

    @Column(value = "begin_elastic_date")
    private String beginElasticDate;

    @Column(value = "end_elastic_date")
    private String endElasticDate;

    @Column(value = "note")
    private String note;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    @Column(value = "total_core")
    private Integer totalCore;

    @Column(value = "total_disk")
    private Integer totalDisk;

    @Column(value = "system_disk_type")
    private String systemDiskType;

    @Column(value = "system_disk_storage")
    private Integer systemDiskStorage;

    @Column(value = "system_disk_num")
    private Integer systemDiskNum;

    @Column(value = "data_disk_type")
    private String dataDiskType;

    @Column(value = "data_disk_storage")
    private Integer dataDiskStorage;

    @Column(value = "data_disk_num")
    private Integer dataDiskNum;

    @Column(value = "instance_num")
    private Integer instanceNum;

    @Column(value = "year")
    private Integer year;

    @Column(value = "month")
    private Integer month;

    @Column(value = "industry")
    private String industry;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "customer_short_name")
    private String customerShortName;

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "submit_user")
    private String submitUser;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "war_zone")
    private String warZone;

    @Column(value = "customer_source")
    private String customerSource;

    @Column(value = "category")
    private String category;

    @Column(value = "app_role")
    private String appRole;

}
