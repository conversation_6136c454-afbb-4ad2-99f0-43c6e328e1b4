package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_item")
public class PplItemSumByOrderDO {

    /**
     * PPL单号(考虑单号可能有英文，目前可以与id保持一致)<br/>Column: [ppl_order]
     */
    @Column(value = "ppl_order")
    private String pplOrder;

    @Column(value = "all_core_sum", computed = "sum(if(demand_type='RETURN',-total_core,total_core))")
    private Integer allCoreSum;
    @Column(value = "all_disk_sum", computed = "sum(if(demand_type='RETURN',-total_disk,total_disk))")
    private Integer allDiskSum;


}