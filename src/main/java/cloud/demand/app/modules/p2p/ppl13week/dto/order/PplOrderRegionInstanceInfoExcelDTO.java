package cloud.demand.app.modules.p2p.ppl13week.dto.order;

import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.google.common.collect.Lists;
import java.util.List;
import lombok.Data;

@Data
public class PplOrderRegionInstanceInfoExcelDTO {

    /**
     * 城市<br/>Column: [region_short_ch_name]
     */
    @ExcelProperty(index = 0, value = "城市")
    private String regionShortChName;

    /**
     * 可用区名<br/>Column: [zone_name]
     */
    @ExcelProperty(index = 1, value = "可用区")
    private String zoneName;
    /**
     * 实例类型<br/>Column: [instance_type]
     */
    @ExcelProperty(index = 2, value = "实例类型")
    private String instanceType;

    /**
     * 实例类型<br/>Column: [instance_type_name]
     */
    @ExcelProperty(index = 3, value = "实例规格")
    private String instanceModel;


    public static PplOrderRegionInstanceInfoExcelDTO transFrom(IndustryDemandRegionZoneInstanceTypeDictDO source) {
        PplOrderRegionInstanceInfoExcelDTO pplOrderRegionInstanceInfoExcelDTO = new PplOrderRegionInstanceInfoExcelDTO();
        pplOrderRegionInstanceInfoExcelDTO.setZoneName(source.getZoneName());
        pplOrderRegionInstanceInfoExcelDTO.setInstanceType(source.getInstanceType());
        pplOrderRegionInstanceInfoExcelDTO.setInstanceModel(source.getInstanceModel());
        pplOrderRegionInstanceInfoExcelDTO.setRegionShortChName(source.getRegionShortChName());
        return pplOrderRegionInstanceInfoExcelDTO;
    }

    public static List<PplOrderRegionInstanceInfoExcelDTO> transFrom(
            List<IndustryDemandRegionZoneInstanceTypeDictDO> source) {
        List<PplOrderRegionInstanceInfoExcelDTO> ret = Lists.newArrayList();
        for (IndustryDemandRegionZoneInstanceTypeDictDO industryDemandRegionZoneInstanceTypeDictDO : source) {
            ret.add(transFrom(industryDemandRegionZoneInstanceTypeDictDO));
        }
        return ret;
    }

}
