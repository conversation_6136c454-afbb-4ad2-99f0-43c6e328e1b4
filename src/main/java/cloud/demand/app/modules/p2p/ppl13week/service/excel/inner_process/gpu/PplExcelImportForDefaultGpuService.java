package cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.gpu;

import cloud.demand.app.common.excel.convert.String2StringOrNumberCellConverterForExport;
import cloud.demand.app.common.excel.convert.WhetherOrNotConverter;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplDraftItemJoinDraftOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.ImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.PplDefaultGpuImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplExportWriteHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGpuRegionZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CpqTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process.AbstractInnerPplExcelParseService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionVO;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

/**
 * 战略客户部CVM导入行业内excel流程
 */
@Service
@Slf4j
public class PplExcelImportForDefaultGpuService extends AbstractInnerPplExcelParseService {

    @Resource
    private PplDictService pplDictService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private DictService dictService;
    @Resource
    private PplDraftService pplDraftService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PplInnerVersionService innerVersionService;

    @Override
    public boolean support(String productType, String industryDept) {
        return productType.equals(Ppl13weekProductTypeEnum.GPU.getName()) &&
                (!industryDept.equals(IndustryDeptEnum.STRATEGY.getName()));
    }

    @Override
    public List<PplImportExcelDTO> decodeExcel(MultipartFile file, String product) {
        List<PplImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplDefaultGpuImportExcelDTO.class,
                    new AnalysisEventListener<PplDefaultGpuImportExcelDTO>() {
                        @Override
                        public void invoke(PplDefaultGpuImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplImportExcelDTO i = PplDefaultGpuImportExcelDTO.copyToNewDTO(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }

    @Override
    public PplItemImportRsp execute(MultipartFile file, ImportExcelDTO importExcelDTO) {
        String product = importExcelDTO.getProduct();
        YearMonth startYearMonth = DateUtils.parse(importExcelDTO.getStartYearMonth());
        YearMonth endYearMonth = DateUtils.parse(importExcelDTO.getEndYearMonth());
        String beginBuyDate =
                startYearMonth.getYear().toString() + "-" + startYearMonth.getMonth().toString() + "-" + "1";
        String industryDept = importExcelDTO.getIndustryDept();
        String userName = LoginUtils.getUserName();
        checkImportPermission(industryDept, product, userName);

        PplInnerVersionVO pplInnerVersionVO = innerVersionService.queryVersionVO(industryDept, product);
        List<String> overseas = pplDictService.queryAllRegionName(true);

        //解析excel
        List<PplImportExcelDTO> uploadData = decodeExcel(file, product);

        //获取uin
        List<String> uins = uploadData.stream()
                .map(PplImportExcelDTO::getCustomerUin)
                .filter(Strings::isNotBlank)
                .distinct().collect(Collectors.toList());
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = queryInfoByUinRspHashMap(uins);
        List<GroupItemDTO> retData = Lang.list();
        List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();
        List<PplGpuRegionZoneDO> pplGpuRegionZoneDOS = pplDictService.queryGpuInstanceList();

        // 腾讯云的region和zone map
        Map<String, String> regionNameMap = dictService.getRegionNameMap();
        Map<String, String> zoneNameMap = dictService.getZoneNameMap();

        // 拿到 已生效的还有草稿箱中存在的 ppl-id
        List<PplDraftItemJoinDraftOrderVO> preSubmitItem = pplDraftService.getCurrentDraftPplItem(industryDept, product,
                beginBuyDate, null, userName);
        Map<String, PplItemDraftDO> pplIdToDraftItem = new HashMap<>();
        Map<String, PplOrderDraftDO> pplOrderToDraftOrder = new HashMap<>();
        if (!CollectionUtils.isEmpty(preSubmitItem)) {
            List<PplItemDraftDO> preSubmitItemList = preSubmitItem.stream()
                    .map(PplDraftItemJoinDraftOrderVO::getItemDraftDO)
                    .collect(Collectors.toList());
            List<PplOrderDraftDO> preSubmitOrderDOList = preSubmitItem.stream()
                    .map(PplDraftItemJoinDraftOrderVO::getOrderDraftDO).collect(Collectors.toList());
            pplIdToDraftItem = preSubmitItemList.stream().collect(Collectors.toMap(PplItemDraftDO::getPplId, v -> v));
            pplOrderToDraftOrder = preSubmitOrderDOList.stream()
                    .collect(Collectors.toMap(PplOrderDraftDO::getPplOrder, v -> v, (v1, v2) -> v1));
        }

        // 获取已生效的pplId
        List<PplItemJoinOrderVO> currentVersionValidPplItem
                = pplDraftService.getCurrentVersionValidPplItem(industryDept, product,
                null);
        Map<String, PplItemDO> pplIdToItem = new HashMap<>();
        Map<String, PplOrderDO> pplOrderToOrder = new HashMap<>();
        if (!CollectionUtils.isEmpty(currentVersionValidPplItem)) {
            List<PplItemDO> validItemList = currentVersionValidPplItem.stream().map(PplItemJoinOrderVO::getItemDO)
                    .collect(Collectors.toList());
            List<PplOrderDO> orderDOList = currentVersionValidPplItem.stream().map(PplItemJoinOrderVO::getPplOrderDO)
                    .collect(Collectors.toList());
            pplIdToItem = validItemList.stream().collect(Collectors.toMap(PplItemDO::getPplId, v -> v));
            pplOrderToOrder = orderDOList.stream()
                    .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v, (v1, v2) -> v1));

        }

        // 获取已预约的item信息
        List<String> pplIds = uploadData.stream().map(PplImportExcelDTO::getPplId).filter(Strings::isNotBlank).collect(
                Collectors.toList());
        Map<String, PplOrderDO> appliedPplOrderMap = new HashMap<>();
        Map<String, PplItemDO> appliedPplItemMap = new HashMap<>();
        if (ListUtils.isNotEmpty(pplIds)) {
            List<PplItemJoinOrderVO> appliedItems = demandDBHelper.getAll(PplItemJoinOrderVO.class,
                    "where t1.ppl_id in(?) and t1.status = ? ",
                    pplIds, PplItemStatusEnum.APPLIED.getCode());
            appliedPplOrderMap = appliedItems.stream().map(PplItemJoinOrderVO::getPplOrderDO)
                    .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v, (v1, v2) -> v1));
            appliedPplItemMap = appliedItems.stream().map(PplItemJoinOrderVO::getItemDO)
                    .collect(Collectors.toMap(PplItemDO::getPplId, v -> v, (v1, v2) -> v1));
        }

        IndustryDemandAuthDO demandAuth = getDemandAuth(userName);
        Boolean isAdmin = permissionService.checkIsAdmin(userName);
        BigDecimal importantDemandRate = new BigDecimal(100);

        for (int i = 0; i < uploadData.size(); i++) {
            PplImportExcelDTO oneData = uploadData.get(i);
            int row = i + 3;
            int beginErrorSize = errors.size();
            PplItemImportRsp.ErrorMessage error = null;

            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getCustomerTypeName,
                    CustomerTypeEnum.names());
            errors.add(error);

            // 校验战区
            checkWarZone(errors, oneData, row, industryDept);

            //校验uin
            QueryInfoByUinRsp queryInfoByUinRsp = checkUin(errors, oneData, row, uinMapInfo);
            if (queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)
                    && industryDept.equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())) {
                oneData.setWarZone(queryInfoByUinRsp.getWarZone());
            }

            if (Strings.isNotBlank(oneData.getPplId())) {
                if (pplIdToItem.get(oneData.getPplId()) == null && pplIdToDraftItem.get(oneData.getPplId()) == null) {
                    errors.add(makeError(row, PplImportExcelDTO::getPplId, "PPL-ID不存在 或 PPL-ID不在用户权限范围内"));
                } else {
                    String submitUser = "";
                    if (pplIdToItem.get(oneData.getPplId()) != null
                            && pplOrderToOrder.get(pplIdToItem.get(oneData.getPplId())) != null) {
                        submitUser = pplOrderToOrder.get(pplIdToItem.get(oneData.getPplId())).getSubmitUser();
                        // 获取已生效的提单人
                    } else if (pplIdToDraftItem.get(oneData.getPplId()) != null
                            && pplOrderToDraftOrder.get(pplIdToDraftItem.get(oneData.getPplId())) != null) {
                        submitUser = pplOrderToDraftOrder.get(pplIdToDraftItem.get(oneData.getPplId())).getSubmitUser();
                        // 获取已生效的提单人
                    }
                    // 校验用户权限
//                    if (!isAdmin) {
//                        if (industryDept.equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())) {
//                            // 一部     uin / 13周预测提交人 / 提单人   可以提单
//                            if (queryInfoByUinRsp.getIsExist()
//                                    && !queryInfoByUinRsp.getSystemArchitect().contains(userName)) {
//                                // 满足 uin 权限
//                            } else if (demandAuth.getIndustry().contains(industryDept)) {
//                                // 满足 13周预测提交人 权限
//                            } else if (submitUser.equals(userName)) {
//                                // 满足 提单人权限
//                            } else {
//                                errors.add(makeError(row, PplImportExcelDTO::getPplId,
//                                        "PPL-ID不存在 或 PPL-ID不在用户权限范围内"));
//                            }
//                        } else {
//                            // 其他行业 13周预测提交人 / 提单人 可以提单
//                            if (demandAuth.getIndustry().contains(industryDept)) {
//                                // 满足 13周预测提交人 权限
//                            } else if (submitUser.equals(userName)) {
//                                // 满足 提单人权限
//                            } else {
//                                errors.add(makeError(row, PplImportExcelDTO::getPplId,
//                                        "PPL-ID不存在 或 PPL-ID不在用户权限范围内"));
//                            }
//                        }
//                    }
                    // 校验已预约明细主key信息和资源量
                    PplItemDO appliedPplItem = appliedPplItemMap.get(oneData.getPplId());
                    PplOrderDO appliedPplOrder =
                            appliedPplItem != null ? appliedPplOrderMap.get(appliedPplItem.getPplOrder()) : null;
                    checkAppliedItem(errors, oneData, appliedPplOrder, appliedPplItem, row);
                }
            }

            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());

            //校验需求
            checkDemandAndBillType(errors, oneData, row);

            //校验业务场景和详情
            checkBizSceneAndDetail(errors, oneData, row);

            //校验使用时长
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getServiceTime);
            errors.add(error);

            //校验赢率 GPU赢率必填
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getWinRate);
            errors.add(error);
            BigDecimal winRate = null;
            if (error == null) {
                winRate = checkWinRate(errors, oneData, row);
            }
            if (oneData.getImportantDemand() != null && oneData.getImportantDemand()) {
                if (winRate == null || importantDemandRate.compareTo(winRate) != 0) {
                    error = oneData.makeErrorIfTure(row, PplImportExcelDTO::getWinRate, true,
                            "GPU重保需求的赢率必须为100%");
                    errors.add(error);
                }
            }

            //各时间参数校验
            DateDTO dateDTO = checkTime(errors, oneData, row, startYearMonth, endYearMonth);

            // 海外跨月需求校验
            checkOverseasCrossMonth(errors,oneData,row,pplInnerVersionVO,overseas);

            // 是否强可用区校验
            strongDesignateZoneCheck(errors,oneData,row);

            // GPU相关校验 地域/可用区/实例类型/核心数/内存数
            GpuDTO gpuDTO = checkGpuRelevant(errors, oneData, row, regionNameMap, zoneNameMap,
                    pplGpuRegionZoneDOS);

            oneData.setGpuType(gpuDTO.getGpuType());
            gpuCheck(errors, oneData, row);

            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            BigDecimal gpuNum = NumberUtils.parseBigDecimal(oneData.getGpuNum());

            // 如果该条ppl没有产生error 则直接转换为 DTO
            if (beginErrorSize == errors.size()) {

                GroupItemDTO tmp = GroupItemDTO.trans(oneData);
                retData.add(tmp);
                if (customerTypeEnum != null) {
                    tmp.setCustomerType(customerTypeEnum.getCode());
                }
                tmp.setYear(dateDTO.getYear());
                tmp.setMonth(dateDTO.getMonth());
                tmp.setYearMonth(dateDTO.getYearMonth());
                tmp.setCustomerShortName(oneData.getCustomerShortName());
                if (queryInfoByUinRsp != null && queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)) {
                    if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                        tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
                    }
                    if (Strings.isBlank(tmp.getCustomerShortName())) {
                        tmp.setCustomerTypeName(tmp.getCustomerName());
                    }
                    tmp.setIndustry(queryInfoByUinRsp.getIndustry());
                    tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
                    tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
                    if (industryDept.equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())) {
                        tmp.setWarZone(queryInfoByUinRsp.getWarZone());
                    }
                }
                tmp.setWinRate(winRate);
                tmp.setBeginBuyDate(dateDTO.getBeginBuyDateRet());
                tmp.setEndBuyDate(dateDTO.getEndBuyDateRet());
                tmp.setBeginElasticDate(dateDTO.getBeginElasticDateRet());
                tmp.setEndElasticDate(dateDTO.getEndElasticDateRet());

                tmp.setInstanceModelCoreNum(coreNum);
                tmp.setInstanceModelRamNum(ramNum);
                tmp.setGpuNum(gpuNum);
                tmp.setStatus(PplItemStatusEnum.VALID.getCode());
                tmp.setStatusName(PplItemStatusEnum.VALID.getName());

                tmp.setGpuType(gpuDTO.getGpuType());
                tmp.setGpuProductType(gpuDTO.getGpuProductType());
                tmp.setInstanceModel(gpuDTO.getInstanceModel());
                tmp.setTotalCoreNum(gpuDTO.getTotalCore());
                tmp.setTotalDiskNum(gpuDTO.getTotalDisk());
                tmp.setTotalGpuNum(gpuDTO.getTotalGpu());

                tmp.setProduct(product);

            }
        }

        return new PplItemImportRsp(errors.size() == 0, errors, retData);
    }

    @Override
    public DownloadBean export(String industryDept, String product, List<GroupItemDTO> groupItemDTOList) {
        InputStream templateIn = IOUtils.readClasspathResourceInputStream(
                "excel/inner_process/default_gpu_import.xlsx");
        String fileName = industryDept + "-" + product + "-PPL数据导出";

        ByteArrayOutputStream out = new ByteArrayOutputStream();

        HashMap<String, Integer> config = new HashMap<>();

        List data = new ArrayList<>();
        List dictData = new ArrayList();

        data = PplDefaultGpuImportExcelDTO.transFrom(groupItemDTOList);
        dictData.addAll(getDefaultGpuDictData(industryDept, config));

        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new PplExportWriteHandler(config))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet()
                .registerConverter(new String2StringOrNumberCellConverterForExport())
                .registerConverter(new WhetherOrNotConverter()).build();
        WriteSheet dictSheet = EasyExcel.writerSheet("字典").build();

        excelWriter.
                fill(new FillWrapper("item", data), writeSheet)
                .write(dictData, dictSheet)
                .finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                fileName + com.pugwoo.wooutils.lang.DateUtils.format(new Date(), "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }


    ExecutorService executorService = Executors.newFixedThreadPool(5);

    /**
     * 默认行业GPU的EXCEL导出字典
     *
     * @param config
     * @return
     */
    @SneakyThrows
    private List<PplDefaultGpuImportExcelDTO> getDefaultGpuDictData(String industryDept,
            HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";

        // 此处region和zone区别于cvm的  数据来源为腾讯云
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return dictService.queryRegionNameList();
        });
        Future<List<String>> zonesFuture = executorService.submit(() -> {
            return dictService.queryZoneNameList();
        });
        Future<List<String>> gpuInstanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryGpuInstanceTypeListNeedShow();
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });
        String gpuNumSpl = "select distinct gpu_num\n"
                + "from ppl_gpu_region_zone where deleted=0 and gpu_num > 0 order by gpu_num";
        Future<List<String>> parseGpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, gpuNumSpl);
        });

        List<String> warZoneList = getIndustryWarZone(industryDept);
        List<String> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> billType = Lang.list("包年包月", "按量计费", "竞价实例");
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();
        List<String> parseGpu = parseGpuFuture.get();
        List<String> gpuInstanceTypeList = gpuInstanceTypeFuture.get();

        List<String> bizSceneList = Lang.list("视觉计算", "AI计算场景", "科学计算场景");
        List<String> bizDetailList = Lang.list("云游戏", "大型规模训练", "工业仿真", "图像渲染", "渲染农场", "图像分类",
                "自动驾驶", "推荐系统");
        List<String> businessCpqList = Arrays.asList(CpqTypeEnum.values()).stream().map(CpqTypeEnum::getName)
                .collect(Collectors.toList());

        List<String> winRateRange = Lang.list();

        config.put("E", warZoneList.size());
        config.put("G", demandType.size());
        config.put("H", demandScene.size());
        config.put("I", bizSceneList.size());
        config.put("J", bizDetailList.size());
        config.put("K", billType.size());
        config.put("M", 101);
        config.put("R", cityNames.size());
        config.put("S", zones.size());
        config.put("T", gpuInstanceTypeList.size());

        config.put("U", parseGpu.size());
        config.put("V", parseCpu.size());
        config.put("W", parseRam.size());

        config.put("AJ", businessCpqList.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }

        List<PplDefaultGpuImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplDefaultGpuImportExcelDTO one = new PplDefaultGpuImportExcelDTO();

            if (i < warZoneList.size()) {
                one.setWarZone(warZoneList.get(i));
            }

            if (i < parseGpu.size()) {
                one.setGpuNum(new BigDecimal(parseGpu.get(i)));
            }

            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(Integer.parseInt(parseCpu.get(i)));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(Integer.parseInt(parseRam.get(i)));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setZoneName("随机可用区");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < bizSceneList.size()) {
                one.setBizScene(bizSceneList.get(i));
            }
            if (i < bizDetailList.size()) {
                one.setBizDetail(bizDetailList.get(i));
            }
            if (i < businessCpqList.size()) {
                one.setBusinessCpqName(businessCpqList.get(i));
            }
            if (i < gpuInstanceTypeList.size()) {
                one.setInstanceType(gpuInstanceTypeList.get(i));
            }
            ret.add(one);
        }

        return ret;
    }

}
