package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.ListUtils2;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.constant.PplInnerProcessVersionStartConsensusEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplVersionSlaDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.consensus.PplSupplyConsensusReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.ConfigProcessResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.EditPplInnerApproveVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.IndustryVersionRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.InnerVersionDateReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.IssueConsensusVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplIndustryVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplIndustryVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveNodeDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveTemplateDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveTemplateDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveTemplateReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerApproveVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerProcessVersionSlaDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerVersionDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.PplInnerVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryConfigProcessReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryIndustryApproveReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryInnerVersionDetailReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryInnerVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.QueryProcessingVersionResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.SaveProcessConfigReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.UpdateVersionDetailReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.SlaDeadlineDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpCommonHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessNodeDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessTemplateDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionSlaDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplSupplyConsensusDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplInnerProcessNodeDeadlineAutoPassEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplInnerProcessNodeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessAttributeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryPackageBaseDataService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplOrderAuditRecordService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplIndustryVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerApproveVersionItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerDeptVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerProcessVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionNodeDetail;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplInnerVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplOrderAuditRecordVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplProcessTemplateVO;
import cloud.demand.app.modules.report_proxy.anno.TaskRunSql;
import com.alibaba.excel.util.CollectionUtils;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAccessor;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplInnerVersionServiceImpl implements PplInnerVersionService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplInnerProcessService pplInnerProcessService;
    @Resource
    private PplOrderAuditRecordService auditRecordService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PplCommonService pplCommonService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Resource
    private PplConsensusService pplConsensusService;

    @Resource
    private DictService dictService;

    @Resource
    private PplIndustryPackageBaseDataService pplIndustryPackageBaseDataService;

    @Resource
    Alert alert;

    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    static ExecutorService executor = Executors.newFixedThreadPool(10);

    @Override
    public ConfigProcessResp queryConfigProcess(QueryConfigProcessReq req) {
        PplInnerProcessDO configProcessDO = demandDBHelper.getOne(PplInnerProcessDO.class,
                "where industry_dept = ? and product like ? ", req.getIndustryDept(), "%" + req.getProduct() + "%");
        ConfigProcessResp resp = new ConfigProcessResp();
        resp.setIsConfig(configProcessDO != null);
        return resp;
    }


    @Override
    public PplInnerVersionResp queryProcessingVersion(QueryInnerVersionReq req) {
        PplInnerVersionResp resp = new PplInnerVersionResp();
        List<PplInnerVersionVO> all = demandDBHelper.getAll(PplInnerVersionVO.class,
                "where status = ? group by process_id", PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        String userName = LoginUtils.getUserNameWithSystem();
        if (req.getIsSla() && !permissionService.checkIsAdmin(userName)) {
            // 如果不是管理员，则根据13周预测确认人的行业过滤
            IndustryDemandAuthDO userByRole = permissionService.getUserByRole(
                    IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode(), userName);
            if (userByRole == null || Strings.isBlank(userByRole.getIndustry())) {
                return resp;
            } else {
                List<String> industryDeptList = Arrays.asList(userByRole.getIndustry().split(";"));
                req.setIndustryDept(industryDeptList);
            }
        }

        List<PplInnerProcessDO> processDOList = demandDBHelper.getAll(PplInnerProcessDO.class,
                "group by industry_dept, product");

        // 查询出供应方案
        List<PplSupplyConsensusDO> pplSupplyConsensusDOList = demandDBHelper.getAll(PplSupplyConsensusDO.class,
                "where id in (SELECT max(id) FROM ppl_supply_consensus where deleted = 0 GROUP BY industry_dept)");
        Map<String, String> deptToVersionCode = pplSupplyConsensusDOList.stream()
                .collect(Collectors.toMap(PplSupplyConsensusDO::getIndustryDept, PplSupplyConsensusDO::getVersionCode));

        // 筛选出有权限查看的行业列表
        if (!CollectionUtils.isEmpty(req.getIndustryDept())) {
            processDOList = processDOList.stream().filter(v -> req.getIndustryDept().contains(v.getIndustryDept()))
                    .collect(Collectors.toList());
            all = all.stream().filter(v -> req.getIndustryDept().contains(v.getIndustryDept()))
                    .collect(Collectors.toList());
        }

        Map<Long, PplInnerProcessDO> processDOMap = processDOList.stream()
                .collect(Collectors.toMap(PplInnerProcessDO::getId, Function.identity()));

        for (PplInnerVersionVO pplInnerVersionVO : all) {
            PplInnerProcessDO pplInnerProcessDO = processDOMap.get(pplInnerVersionVO.getProcessId());
            pplInnerVersionVO.setAutoApprovalForSameApprove(pplInnerProcessDO.getSameApproveAutoPass());
            pplInnerVersionVO.setSort(pplInnerProcessDO.getSort());
            pplInnerVersionVO.setConsensusRole(pplInnerProcessDO.getConsensusRole());
            pplInnerVersionVO.setIsAutoCreateVersion(pplInnerProcessDO.getIsAutoCreateVersion());
        }

        List<Long> versionIds = all.stream().map(PplInnerVersionVO::getId).collect(Collectors.toList());
        // 这里加判断是为了减轻前端别的页面调当前方法时的速度
        if (req.getIsSla()) {
            // 只有查审批计划的时候才需要查当前节点
            List<PplOrderAuditRecordDO> recordList = demandDBHelper.getAll(PplOrderAuditRecordDO.class,
                    "where version_id in (?) and node_code is not null "
                            + "and audit_status = 'WAIT'",
                    versionIds);
            Map<Long, List<PplOrderAuditRecordDO>> map = recordList.stream()
                    .collect(Collectors.groupingBy(PplOrderAuditRecordDO::getVersionId));
            for (PplInnerVersionVO pplInnerVersionVO : all) {
                newSetCurrentNodeForVersion(pplInnerVersionVO, map.get(pplInnerVersionVO.getId()));
            }
            resp.setInnerVersionVOS(all);

            // 判断当前供应方案共识状态 及最新供应方案
            for (PplInnerVersionVO pplInnerVersionVO : all) {
                PplInnerProcessVersionStartConsensusEnum consensusEnum = PplInnerProcessVersionStartConsensusEnum
                        .getByCode(pplInnerVersionVO.getIsStartConsensus());
                if (consensusEnum == null) {
                    consensusEnum = PplInnerProcessVersionStartConsensusEnum.NOT_CONSENSUS;
                }
                if (pplInnerVersionVO.getProduct().contains(Ppl13weekProductTypeEnum.CVM.getName())) {
                    pplInnerVersionVO.setLatestSupplyVersion(
                            deptToVersionCode.get(pplInnerVersionVO.getIndustryDept()));
                }
                pplInnerVersionVO.setConsensusStatus(consensusEnum.getName());
            }
        }

        // 补充 还没有配置进行中的审批版本 的 行业（行业+适用产品维度）
        List<Long> processIdList = processDOList.stream().map(PplInnerProcessDO::getId).collect(Collectors.toList());
        List<Long> processingIdList = all.stream().map(PplInnerProcessVersionDO::getProcessId)
                .collect(Collectors.toList());
        processIdList.removeAll(processingIdList);
        if (!CollectionUtils.isEmpty(processIdList)) {
            for (Long processId : processIdList) {
                PplInnerProcessDO pplInnerProcessDO = processDOMap.get(processId);
                PplInnerVersionVO versionVO = new PplInnerVersionVO();
                versionVO.setProcessId(processId);
                versionVO.setIndustryDept(pplInnerProcessDO.getIndustryDept());
                versionVO.setProduct(pplInnerProcessDO.getProduct());
                versionVO.setAutoApprovalForSameApprove(pplInnerProcessDO.getSameApproveAutoPass());
                versionVO.setSort(pplInnerProcessDO.getSort());
                all.add(versionVO);
            }
        }

        resp.setInnerVersionVOS(
                all.stream().sorted(Comparator.comparing(PplInnerVersionVO::getSort)).collect(Collectors.toList()));
        return resp;
    }

    @Override
    public PplIndustryVersionResp queryUserIndustryVersion() {
        PplIndustryVersionResp pplIndustryVersionResp = new PplIndustryVersionResp();
        String userName = LoginUtils.getUserName();
        pplIndustryVersionResp.setIsAllIndustryAuth(permissionService.isAllIndustryAuth(userName));
        List<String> industryDeptPermission = permissionService.queryUserIndustryDeptPermission(userName);
        List<PplIndustryVersionVO> all = demandDBHelper.getAll(PplIndustryVersionVO.class,
                "where status = ? and industry_dept in (?)",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode(), industryDeptPermission);
        if (CollectionUtils.isEmpty(all)) {
            return pplIndustryVersionResp;
        }
        List<Long> versionId = all.stream().map(PplInnerProcessVersionDO::getId).collect(Collectors.toList());
        List<PplInnerProcessVersionSlaDO> firstSlaList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?) and deadline_type = 'ENTER'", versionId);
        Map<Long, PplInnerProcessVersionSlaDO> versionIdToEnterSla = firstSlaList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getVersionId, v -> v, (v1, v2) -> v1));
        for (PplIndustryVersionVO pplIndustryVersionVO : all) {
            PplInnerProcessVersionSlaDO slaDO = versionIdToEnterSla.get(pplIndustryVersionVO.getId());
            if (slaDO != null) {
                pplIndustryVersionVO.setAuditStartTime(DateUtils.format(slaDO.getDeadlineTime()));
            }
        }
        pplIndustryVersionResp.setIndustryVersionVOS(all);
        return pplIndustryVersionResp;
    }

    @Override
    public PplInnerVersionDetailResp getVersionDetail(QueryInnerVersionDetailReq req) {

        if (IndustryDeptEnum.INNER_DEPT.getName().equals(req.getIndustryDept()) &&
        Ppl13weekProductTypeEnum.CVM.getName().equals(req.getProduct())){
            // 如果查内部业务部的CVM 因为当前不会有CVM流程 直接去查GPU
            req.setProduct(Ppl13weekProductTypeEnum.GPU.getName());
        }

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("industry_dept = ? ", req.getIndustryDept());
        if (req.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            whereSQL.and("product like '裸金属' or product like '%;裸金属%' or product like '%裸金属;%'");
        } else {
            whereSQL.and("product like ?", "%" + req.getProduct() + "%");
        }
        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class, whereSQL.getSQL(),
                whereSQL.getParams());

        if (processDO == null) {
            throw new BizException("当前行业: " + req.getIndustryDept() + " （适用产品-" + req.getProduct()
                    + "）未配置初始流程，请联系 kaijiazhang 查看");
        }
        List<PplInnerVersionNodeDetail> result = new ArrayList<>();

        PplInnerProcessVersionDO currentVersion = queryProcessingVersionByDeptAndProduct(req.getIndustryDept(),
                req.getProduct());

        PplInnerVersionDetailResp pplInnerVersionDetailResp = new PplInnerVersionDetailResp();
        pplInnerVersionDetailResp.setNodeDetails(result);
        pplInnerVersionDetailResp.setNotConfig(Boolean.TRUE);

        if (currentVersion == null) {
            // 如果还没开始进行中的版本 直接返回
            return pplInnerVersionDetailResp;
        }

        List<PplInnerProcessVersionSlaDO> versionSlaList = getVersionSlaList(currentVersion.getId());
        for (int i = 0; i < versionSlaList.size(); i++) {
            PplInnerProcessVersionSlaDO pplInnerProcessNodeVo = versionSlaList.get(0);
            if (i == 0 && !pplInnerProcessNodeVo.getNodeCode().equals(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode())){
                // 如果第一个节点不是预录入节点 则添加预录入节点 后面考了拆除
                PplInnerVersionNodeDetail firstNodeDetail = new PplInnerVersionNodeDetail();
                firstNodeDetail.setNodeCode(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
                firstNodeDetail.setNodeName(PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
                firstNodeDetail.setDeadlineType("ENTER");
                result.add(firstNodeDetail);
            }
            PplInnerVersionNodeDetail pplInnerVersionNodeDetail = new PplInnerVersionNodeDetail();
            BeanUtils.copyProperties(pplInnerProcessNodeVo, pplInnerVersionNodeDetail);
            result.add(pplInnerVersionNodeDetail);
        }

        for (PplInnerVersionNodeDetail pplInnerVersionNodeDetail : result) {
            pplInnerVersionNodeDetail.setApproveRole(
                    IndustryDemandAuthRoleEnum.getNameByCode(pplInnerVersionNodeDetail.getApproveRole()));
            pplInnerVersionNodeDetail.setRoleAttribute(
                    PplInnerProcessAttributeEnum.getNameByCode(pplInnerVersionNodeDetail.getRoleAttribute()));
            pplInnerVersionNodeDetail.setWaitAttribute(
                    PplInnerProcessAttributeEnum.getNameByCode(pplInnerVersionNodeDetail.getWaitAttribute()));
        }

        PplInnerProcessVersionDO validVersion = null;
        List<PplInnerProcessVersionSlaDO> slaDOS = new ArrayList<>();
        if (req.getIsCurrent()) {
            slaDOS = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class
                    , "where version_id = ?", currentVersion.getId());
            validVersion = currentVersion;
        } else {
            PplInnerVersionVO nextVersion = demandDBHelper.getOne(PplInnerVersionVO.class,
                    "where process_id =  ? and id > ?", processDO.getId(), currentVersion.getId());
            if (nextVersion == null) {
                return pplInnerVersionDetailResp;
            }
            validVersion = nextVersion;
            slaDOS = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class
                    , "where version_id = ?", nextVersion.getId());
        }
        PplInnerProcessVersionSlaDO enterNodeSla = slaDOS.get(0);
        Map<String, PplInnerProcessVersionSlaDO> exit = slaDOS.stream().filter(v -> v.getDeadlineType().equals("EXIT"))
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));
        for (PplInnerVersionNodeDetail pplInnerVersionNodeDetail : result) {
            PplInnerProcessVersionSlaDO pplInnerProcessVersionSlaDO = exit.get(pplInnerVersionNodeDetail.getNodeCode());
            if (pplInnerProcessVersionSlaDO != null) {
                pplInnerVersionNodeDetail.setDeadlineTime(pplInnerProcessVersionSlaDO.getDeadlineTime());
                pplInnerVersionNodeDetail.setDeadlineType(pplInnerProcessVersionSlaDO.getDeadlineType());
            } else if (pplInnerVersionNodeDetail.getDeadlineType().equals("ENTER")) {
                pplInnerVersionNodeDetail.setDeadlineTime(enterNodeSla.getDeadlineTime());
                pplInnerVersionNodeDetail.setDeadlineType(enterNodeSla.getDeadlineType());
            }
        }
        pplInnerVersionDetailResp.setNotConfig(Boolean.FALSE);
        pplInnerVersionDetailResp.setNodeDetails(result);
        pplInnerVersionDetailResp.setDemandBeginYear(validVersion.getDemandBeginYear());
        pplInnerVersionDetailResp.setDemandBeginMonth(validVersion.getDemandBeginMonth());
        pplInnerVersionDetailResp.setDemandEndYear(validVersion.getDemandEndYear());
        pplInnerVersionDetailResp.setDemandEndMonth(validVersion.getDemandEndMonth());
        pplInnerVersionDetailResp.setOverseasDemandBeginYear(validVersion.getOverseasDemandBeginYear());
        pplInnerVersionDetailResp.setOverseasDemandBeginMonth(validVersion.getOverseasDemandBeginMonth());
        pplInnerVersionDetailResp.setVersionStartTime(validVersion.getCreateTime());

        // 获取 当前阶段
        PplInnerVersionVO pplInnerVersionVO = demandDBHelper.getOne(PplInnerVersionVO.class,
                "where id = ?", validVersion.getId());
        setCurrentNodeForVersion(pplInnerVersionVO);
        pplInnerVersionDetailResp.setCurrentNode(pplInnerVersionVO.getCurrentNode());
        pplInnerVersionDetailResp.setCurrentNodeCode(pplInnerVersionVO.getCurrentNodeCode());
        return pplInnerVersionDetailResp;
    }

    @Override
    public IndustryVersionRsp getVersionDetailById(Long versionId) {
        IndustryVersionRsp rsp = demandDBHelper.getByKey(IndustryVersionRsp.class, versionId);
        List<PplInnerProcessVersionSlaDO> versionSlaList = getVersionSlaList(versionId);
        List<String> roleList = versionSlaList.stream().map(PplInnerProcessVersionSlaDO::getApproveRole)
                .filter(Strings::isNotBlank).collect(Collectors.toList());
        Map<String, IndustryDemandAuthDO> roleToMap = new HashMap<>();
        String userName = LoginUtils.getUserName();
        Boolean isAuth = false;
        if (!permissionService.checkIsAdmin(userName)) {
            List<IndustryDemandAuthDO> industryDemandAuthDOList = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                    "where industry = ? and user_name = ? and role in (?)",
                    rsp.getIndustryDept(), userName, roleList);
            roleToMap = industryDemandAuthDOList.stream()
                    .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));
        } else {
            isAuth = true;
        }

        List<PplVersionSlaDTO> list = new ArrayList<>();
        for (int i = versionSlaList.size() - 1; i >= 0; i--) {
            PplInnerProcessVersionSlaDO slaDO = versionSlaList.get(i);
            PplVersionSlaDTO dto = new PplVersionSlaDTO();
            BeanUtils.copyProperties(slaDO, dto);
            if (roleToMap.get(slaDO.getApproveRole()) != null) {
                isAuth = true;
            }
            dto.setIsAuth(isAuth);
            list.add(dto);
        }
        Collections.reverse(list);
        rsp.setSlaDOS(list);
        return rsp;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void updateVersionDetail(UpdateVersionDetailReq req) {
        PplInnerProcessVersionDO currentVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where industry_dept = ? and status = ?", req.getIndustryDept(),
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        Long versionId = currentVersion.getId();
        if (!req.getIsCurrent()) {
            // 如果不是当前版本则查下一个版本
            PplInnerVersionVO nextVersion = demandDBHelper.getOne(PplInnerVersionVO.class,
                    "where industry_dept = ? and id > ?", req.getIndustryDept(), currentVersion.getId());
            if (nextVersion == null) {
                req.setProcessId(currentVersion.getProcessId());
                req.setTemplateId(currentVersion.getTemplateId());
                req.setProduct(currentVersion.getProduct());
                req.setDemandBeginYear(currentVersion.getDemandBeginYear());
                req.setDemandBeginMonth(currentVersion.getDemandBeginMonth());
                req.setDemandEndYear(currentVersion.getDemandEndYear());
                req.setDemandEndMonth(currentVersion.getDemandEndMonth());
                req.setOverseasDemandBeginYear(currentVersion.getOverseasDemandBeginYear());
                req.setOverseasDemandBeginMonth(currentVersion.getOverseasDemandBeginMonth());
                versionId = insertNewVersion(req);
            } else {
                versionId = nextVersion.getId();
            }
        } else {
            currentVersion.setDemandBeginYear(req.getDemandBeginYear());
            currentVersion.setDemandBeginMonth(req.getDemandBeginMonth());
            currentVersion.setDemandEndYear(req.getDemandEndYear());
            currentVersion.setDemandEndMonth(req.getDemandEndMonth());
            currentVersion.setOverseasDemandBeginYear(req.getOverseasDemandBeginYear());
            currentVersion.setOverseasDemandBeginMonth(req.getOverseasDemandBeginMonth());
            demandDBHelper.update(currentVersion);
        }

        List<PplInnerProcessVersionSlaDO> all = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id = ?", versionId);
        Map<String, Date> codeToDate = req.getNodeDetails().stream().collect(
                Collectors.toMap(PplInnerVersionNodeDetail::getNodeCode, PplInnerVersionNodeDetail::getDeadlineTime));
        for (PplInnerProcessVersionSlaDO slaDO : all) {
            slaDO.setDeadlineTime(codeToDate.get(slaDO.getNodeCode()));
            demandDBHelper.update(slaDO);
        }
    }

    /**
     * 自动新增版本 时适用，延用上一版配置的模版
     *
     * @param req 配置
     * @return 新增版本id
     */
    @Override
    @Transactional(value = "demandTransactionManager")
    public Long insertNewVersion(UpdateVersionDetailReq req) {
        PplInnerProcessVersionDO versionDO = new PplInnerProcessVersionDO();
        versionDO.setProduct(req.getProduct());
        versionDO.setProcessId(req.getProcessId());
        versionDO.setTemplateId(req.getTemplateId());
        versionDO.setVersionCode("V_待配置");

        versionDO.setDemandBeginYear(req.getDemandBeginYear());
        versionDO.setDemandBeginMonth(req.getDemandBeginMonth());
        versionDO.setDemandEndYear(req.getDemandEndYear());
        versionDO.setDemandEndMonth(req.getDemandEndMonth());
        versionDO.setOverseasDemandBeginYear(req.getOverseasDemandBeginYear());
        versionDO.setOverseasDemandBeginMonth(req.getOverseasDemandBeginMonth());

        versionDO.setStatus(PplInnerProcessVersionStatusEnum.NEW.getCode());
        versionDO.setIndustryDept(req.getIndustryDept());

        versionDO.setVersionCreator("system");
        versionDO.setVersionUpdater("system");

        versionDO.setConsumeStatus(0);

        demandDBHelper.insert(versionDO);

        // 拉取模版最新数据生成 版本 对应的 审批节点流快照
        batchProduceNodeSlasAuto(req.getTemplateId(), versionDO.getId());

        return versionDO.getId();
    }

    @Override
    public Map<String, String> getNodeCodeMap(String industryDept, String product, Long versionId) {
        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class,
                "where industry_dept = ? and product = ? ", industryDept, product);
        if (processDO == null) {
            throw new BizException("当前行业: " + industryDept + " 未配置初始审批流程，请联系 oliverychen 查看！");
        }
//        List<PplInnerProcessNodeDO> all = demandDBHelper.getAll(PplInnerProcessNodeDO.class, "where process_id = ?",
//                processDO.getId());
//        Map<String, String> map = all.stream()
//                .collect(Collectors.toMap(PplInnerProcessNodeDO::getNodeCode, PplInnerProcessNodeDO::getNodeName,
//                        (v1, v2) -> v1));

        Map<String, String> map = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class, "where version_id = ? ",
                        versionId).stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode,
                        PplInnerProcessVersionSlaDO::getNodeName));
        // 补充sla表的快照节点编码，防止节点被删除后 与 ppl单存储的当前节点编码对应不上问题
        // （前置了解：版本生效后，选择的模版删除了部分节点，模版最新信息同步到选择了此模版的待启动版本快照中，已生效版本快照不受模版更改影响）
        // （问题出现场景：模版删除节点后，node表的节点deleted = 1，但生效中版本中，此节点对应的快照节点并未受影响，即节点删除但快照节点并未被删除，而审批流程中ppl单保存的当前审批节点编码来自快照节点，如果只查询node表，那么返回的map会缺失快照节点的编码信息，造成前台展示当前节点名称缺失）
        map.put(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode(), PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
        return map;
    }

    @Override
    public Long autoInitNewVersion(String industryDept, Long versionId) {
        // 完成旧版本
        PplInnerProcessVersionDO one = demandDBHelper.getOne(PplInnerProcessVersionDO.class, "where id = ?", versionId);
        one.setStatus(PplInnerProcessVersionStatusEnum.DONE.getCode());
        one.setEndDate(LocalDate.now());
        demandDBHelper.update(one);

        // 查询新版本
        PplInnerVersionVO nextVersion = demandDBHelper.getOne(PplInnerVersionVO.class,
                "where process_id = ?  and id > ? ", one.getProcessId(), versionId);
        if (nextVersion != null) {
            // 开启此版本（版本开启时自动生成版本号）
            PplInnerProcessVersionSlaDO enterNodeDTO = nextVersion.getSlaDOList().stream()
                    .filter(e -> PplInnerProcessNodeEnum.PRE_SUBMIT.getCode().equals(e.getNodeCode())).collect(
                            Collectors.toList()).get(0);
            Date deadline = enterNodeDTO.getDeadlineTime();
            if (deadline != null) {
                String versionCode =
                        "V_" + DateUtils.getYear(deadline) + String.format("%tm", deadline) + String.format("%td",
                                deadline);
                nextVersion.setVersionCode(versionCode);
            }

            nextVersion.setBeginDate(LocalDate.now());
            nextVersion.setStatus(PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
            nextVersion.setVersionUpdater("system");
            demandDBHelper.update(nextVersion);
            pplIndustryPackageBaseDataService.inheritOrRefreshBaseData(nextVersion.getId(), versionId);
            return nextVersion.getId();
        }

        UpdateVersionDetailReq req = new UpdateVersionDetailReq();
        req.setProcessId(one.getProcessId());
        req.setTemplateId(one.getTemplateId());
        req.setProduct(one.getProduct());
        req.setIndustryDept(one.getIndustryDept());
        req.setDemandBeginYear(one.getDemandBeginYear());
        req.setDemandBeginMonth(one.getDemandBeginMonth());
        req.setDemandEndYear(one.getDemandEndYear());
        req.setDemandEndMonth(one.getDemandEndMonth());
        req.setOverseasDemandBeginYear(one.getOverseasDemandBeginYear());
        req.setOverseasDemandBeginMonth(one.getOverseasDemandBeginMonth());


        // 业务没有配置下一版本时，系统自动生成下一版本
        Long nextVersionId = insertNewVersion(req);
        PplInnerProcessVersionDO newNextVersion = new PplInnerProcessVersionDO();
        newNextVersion.setId(nextVersionId);
        newNextVersion.setBeginDate(LocalDate.now());
        newNextVersion.setStatus(PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        demandDBHelper.update(newNextVersion);

        // 给"13周预测确认人"角色，发送邮件告警
        boolean isTest = !Strings.isEmpty(testEnv.get());
        String prefix = isTest ? "exp-" : "";
        String acceptUser = pplCommonService.getApproveUser(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);
        String[] product = req.getProduct().split(";");
        if (isTest) {
            acceptUser = "kaijiazhang;oliverychen;laurenpeng;erickssu";
        }
        alert.sendMail(acceptUser, "行业PPL审批计划通知",
                "【" + req.getIndustryDept() + "】-适用产品" + Arrays.toString(product) + "：<br/><br/>"
                        + "因业务没有配置下一审批版本，下一审批版本已由系统自动生成。<br/><br/>"
                        + "请注意！<br/>"
                        + "<span style = \"color:red\">目前缺失各审批节点的结束时间配置，审批流程无法推进，请尽快补全配置！</span><br/><br/>"
                        + "详细信息请前往CRP查看。<a href='https://"
                        + prefix + "crp.woa.com/13ppl/approval-process/sla-config'>点击前往</a>。<br />");
        pplIndustryPackageBaseDataService.inheritOrRefreshBaseData(nextVersionId, versionId);
        return nextVersionId;

        // 废弃自动开启的下一版本 各节点结束时间顺延一个月的设置
       /* nextVersion = demandDBHelper.getOne(PplInnerVersionVO.class,
                "where id = ?", nextVersionId);
        // 拿到当前版本的sla时间
        List<PplInnerProcessVersionSlaDO> currentVersionSla = demandDBHelper.getAll(
                PplInnerProcessVersionSlaDO.class, "where version_id = ?", versionId);
        Map<String, Date> codeToDate = currentVersionSla.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode,
                        PplInnerProcessVersionSlaDO::getDeadlineTime));

        // 查出下版本的sla时间，并每个时间都加上30天
        List<PplInnerProcessVersionSlaDO> nextVersionSla = demandDBHelper.getAll
                (PplInnerProcessVersionSlaDO.class, "where version_id = ?", nextVersion.getId());
        for (PplInnerProcessVersionSlaDO slaDO : nextVersionSla) {
            slaDO.setDeadlineTime(addDays(codeToDate.get(slaDO.getNodeCode()), 30L));
            demandDBHelper.update(slaDO);
        }*/

    }


    @Override
    public List<PplInnerVersionDetailResp> queryVersionDate(InnerVersionDateReq req) {
        List<PplInnerProcessVersionDO> versionDOList =
                demandDBHelper.getAll(PplInnerProcessVersionDO.class, "where industry_dept = ? ",
                        req.getIndustryDept());
        List<Long> processId = versionDOList.stream().map(PplInnerProcessVersionDO::getProcessId).distinct()
                .collect(Collectors.toList());

        if (StringUtils.isNotBlank(req.getProduct()) && processId.size() > 1) {
            // 查询条件产品过滤
            versionDOList.removeIf(item -> {
                List<String> productList = PplInnerProcessVersionVO.splitProductFieldToProductList(item);
                return ListUtils.isEmpty(productList) || !productList.contains(req.getProduct());
            });
        }

        List<PplInnerVersionDetailResp> respList = new ArrayList<>();
        for (PplInnerProcessVersionDO versionDO : versionDOList) {
            List<PplInnerVersionNodeDetail> result = new ArrayList<>();

            List<PplInnerProcessVersionSlaDO> nodes = pplInnerProcessService.getProcessByVersionId(versionDO.getId());
            if (ListUtils.isEmpty(nodes)) {
                continue;
            }

            for (PplInnerProcessVersionSlaDO processNode : nodes) {
                PplInnerVersionNodeDetail nodeDetail = PplInnerVersionNodeDetail.convertFrom(processNode);
                if (nodeDetail != null) {
                    result.add(nodeDetail);
                }
            }
            PplInnerVersionDetailResp pplInnerVersionDetailResp = new PplInnerVersionDetailResp();
            pplInnerVersionDetailResp.setNodeDetails(result);
            pplInnerVersionDetailResp.setDemandBeginYear(versionDO.getDemandBeginYear());
            pplInnerVersionDetailResp.setDemandBeginMonth(versionDO.getDemandBeginMonth());
            pplInnerVersionDetailResp.setDemandEndYear(versionDO.getDemandEndYear());
            pplInnerVersionDetailResp.setDemandEndMonth(versionDO.getDemandEndMonth());
            pplInnerVersionDetailResp.setBeginDate(versionDO.getBeginDate());
            pplInnerVersionDetailResp.setEndDate(versionDO.getEndDate());

            if (versionDO.getStatus().equals(PplInnerProcessVersionStatusEnum.PROCESSING.getCode())) {
                // 获取 当前阶段
                PplInnerVersionVO pplInnerVersionVO = demandDBHelper.getOne(PplInnerVersionVO.class,
                        "where id = ?", versionDO.getId());
                setCurrentNodeForVersion(pplInnerVersionVO);
                pplInnerVersionDetailResp.setCurrentNode(pplInnerVersionVO.getCurrentNode());
                pplInnerVersionDetailResp.setCurrentNodeCode(pplInnerVersionVO.getCurrentNodeCode());
                pplInnerVersionDetailResp.setIsCurrent(Boolean.TRUE);
            }

            respList.add(pplInnerVersionDetailResp);

        }
        return respList;
    }

    /**
     * 根据 行业  查询 全产品 进行中审批版本配置的 需求沟通节点信息
     */
    @Override
    public QueryProcessingVersionResp queryProcessingVersionSlaDeadline(String industryDept) {
        QueryProcessingVersionResp resp = new QueryProcessingVersionResp();
        resp.setIndustryDept(industryDept);

        Map<String, Long> processVersionMap = queryProcessingVersionMap(industryDept);

        List<Long> processVersionIds = processVersionMap.values().stream().filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processVersionIds)) {
            resp.setSlaDeadlineDetails(Collections.emptyList());
            return resp;
        }

        WhereContent whereContent = new WhereContent();
        whereContent.andIn(PplInnerProcessVersionSlaDO::getVersionId, processVersionIds);
        whereContent.andEqual(PplInnerProcessVersionSlaDO::getDeadlineType, "ENTER");
        Map<Long, PplInnerProcessVersionSlaDO> enterSlaDOMap = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                        whereContent.getSql(), whereContent.getParams())
                .stream().collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getVersionId, Function.identity()));

        List<SlaDeadlineDetailDTO> slaDeadlineDetailList = new ArrayList<>();
        processVersionMap.forEach((k, v) -> {
            PplInnerProcessVersionSlaDO enterSlaDO = enterSlaDOMap.get(v);
            if (enterSlaDO != null) {
                SlaDeadlineDetailDTO slaDetailDTO = new SlaDeadlineDetailDTO();
                slaDetailDTO.setProduct(k);
                slaDetailDTO.setVersionId(v);
                slaDetailDTO.setNodeCode(enterSlaDO.getNodeCode());
                slaDetailDTO.setNodeName(enterSlaDO.getNodeName());
                slaDetailDTO.setDeadlineType(enterSlaDO.getDeadlineType());
                slaDetailDTO.setDeadlineTime(enterSlaDO.getDeadlineTime());
                slaDeadlineDetailList.add(slaDetailDTO);
            }
        });

        resp.setSlaDeadlineDetails(slaDeadlineDetailList);
        return resp;
    }

    /**
     * 通过行业查询出，各产品对应的正常进行中的versionId，没有则存null，如
     * {
     * 'CVM&CBS' : 21,
     * 'GPU(裸金属&CVM)' : 22,
     * '裸金属' ： null
     * }
     *
     * @param industryDept 行业
     * @return Map(适用产品 - > 当前审批版本id)
     */
    @Override
    public Map<String, Long> queryProcessingVersionMap(String industryDept) {
        Map<String, Long> productProcessVersionMap = new HashMap<>(16);
        List<PplInnerProcessVersionDO> versionDOList = demandDBHelper.getRaw(PplInnerProcessVersionDO.class,
                "select id, product from ppl_inner_process_version where deleted = 0 and industry_dept = ? and status = ?  group by product ",
                industryDept, PplInnerProcessVersionStatusEnum.PROCESSING.getCode());

        for (PplInnerProcessVersionDO versionDO : versionDOList) {
            Stream.of(versionDO.getProduct().split(";")).collect(Collectors.toList()).stream()
                    .forEach(e -> productProcessVersionMap.put(e, versionDO.getId()));
        }

        return productProcessVersionMap;
    }

    /**
     * 查询具体 行业+产品 对应的 进行中审批版本信息
     *
     * @param industryDept 行业
     * @param product 产品
     * @return 当前审批版本信息
     */
    @Override
    public PplInnerProcessVersionDO queryProcessingVersionByDeptAndProduct(String industryDept, String product) {
        WhereContent where = new WhereContent();
        where.andEqual(PplInnerProcessVersionDO::getIndustryDept, industryDept);
        where.andEqual(PplInnerProcessVersionDO::getStatus, PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        where.orderDesc("id");
        List<PplInnerProcessVersionDO> versions = demandDBHelper
                .getAll(PplInnerProcessVersionDO.class, where.getSql(), where.getParams());
        PplInnerProcessVersionDO latestVersion = null;
        A:
        for (PplInnerProcessVersionDO version : versions) {
            if (version == null || Strings.isBlank(version.getProduct())) {
                continue;
            }
            if (version.getProduct().equals(product)) {
                latestVersion = version;
                break A;
            }
            String[] productArray = version.getProduct().split(";");
            for (String s : productArray) {
                if (Objects.equals(product, s)) {
                    latestVersion = version;
                    break A;
                }
            }
        }
        return latestVersion;
    }

    @Override
    public PplInnerVersionVO queryVersionVO(String industryDept, String product) {
        WhereContent where = new WhereContent();
        where.andEqual(PplInnerProcessVersionDO::getIndustryDept, industryDept);
        where.andEqual(PplInnerProcessVersionDO::getStatus, PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        where.orderDesc("id");
        List<PplInnerVersionVO> versions = demandDBHelper
                .getAll(PplInnerVersionVO.class, where.getSql(), where.getParams());
        PplInnerVersionVO latestVersion = null;
        A:
        for (PplInnerVersionVO version : versions) {
            if (version == null || Strings.isBlank(version.getProduct())) {
                continue;
            }
            if (version.getProduct().equals(product)) {
                latestVersion = version;
                break A;
            }
            String[] productArray = version.getProduct().split(";");
            for (String s : productArray) {
                if (Objects.equals(product, s)) {
                    latestVersion = version;
                    break A;
                }
            }
        }
        return latestVersion;
    }

    @Override
    public PplInnerProcessVersionDO queryInnerVersionByDeptAndProductAndConsensusVersion(String industryDept,
            String product, String consensusVersion) {
        WhereContent where = new WhereContent();
        where.andEqual(PplInnerProcessVersionDO::getIndustryDept, industryDept);
        where.andEqual(PplInnerProcessVersionDO::getConsensusVersion, consensusVersion);
        where.orderDesc("id");
        List<PplInnerProcessVersionDO> versions = demandDBHelper
                .getAll(PplInnerProcessVersionDO.class, where.getSql(), where.getParams());
        PplInnerProcessVersionDO latestVersion = null;
        A:
        for (PplInnerProcessVersionDO version : versions) {
            if (version == null || Strings.isBlank(version.getProduct())) {
                continue;
            }
            String[] productArray = version.getProduct().split(";");
            for (String s : productArray) {
                if (Objects.equals(product, s)) {
                    latestVersion = version;
                    break A;
                }
            }
        }
        return latestVersion;
    }

    /**
     * 全量查询 所有 行业-适用产品 对应的 进行中审批版本信息列表
     *
     * @param queryBySingleProduct 是否按具体产品查询（false-直接返回version表中所有 进行中审批版本列表（其中product为多产品字段，; 分隔）；
     *         true-将version表中所有 进行中审批版本列表 加工处理，转换为 单个适用产品-审批版本信息 列表）
     * @return 进行中审批版本信息列表
     */
    public List<PplInnerProcessVersionVO> queryAllProcessingVersion(Boolean queryBySingleProduct) {
        List<PplInnerProcessVersionVO> versionDOList = demandDBHelper.getAll(PplInnerProcessVersionVO.class,
                "where status = ? and deleted = 0 group by industry_dept, product ",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        if (queryBySingleProduct == null || !queryBySingleProduct) {
            return versionDOList;
        }

        List<PplInnerProcessVersionVO> transVersionVOList = new ArrayList<>();
        for (PplInnerProcessVersionVO versionDO : versionDOList) {
            List<String> products = Arrays.asList(versionDO.getProduct().split(";"));
            products.forEach(product -> {
                PplInnerProcessVersionVO transVersionVO = new PplInnerProcessVersionVO();
                BeanUtils.copyProperties(versionDO, transVersionVO);
                transVersionVO.setSingleProduct(product);
                transVersionVOList.add(transVersionVO);
            });
        }
        return transVersionVOList;
    }

    @Override
    public PplInnerProcessVersionSlaDTO getCurrentNode(Long versionId, String currentNode) {
        return demandDBHelper.getOne(PplInnerProcessVersionSlaDTO.class,
                "where version_id = ? and node_code = ?", versionId, currentNode);
    }

    @Override
    public PplInnerProcessVersionSlaDTO getFirstNode(Long versionId) {
        return demandDBHelper.getOne(PplInnerProcessVersionSlaDTO.class,
                "where version_id = ? and is_begin_node = 1", versionId);
    }

    @Override
    public PplInnerProcessVersionSlaDTO getEnterNode(Long versionId) {
        return demandDBHelper.getOne(PplInnerProcessVersionSlaDTO.class,
                "where version_id = ? and deadline_type = 'ENTER'", versionId);
    }

    @Override
    public List<PplInnerProcessVersionSlaDO> getVersionSlaList(Long versionId) {
        List<PplInnerProcessVersionSlaDO> nodeSlaDOList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? ", versionId);
        if (CollectionUtils.isEmpty(nodeSlaDOList)) {
            return Collections.emptyList();
        }

        List<PplInnerProcessVersionSlaDO> nodeDetailList = new ArrayList<>();
        PplInnerProcessVersionSlaDO firstNodeDetail = nodeSlaDOList.stream()
                .filter(e -> "ENTER".equals(e.getDeadlineType())).collect(Collectors.toList()).get(0);
        nodeDetailList.add(firstNodeDetail);

        Map<Long, PplInnerProcessVersionSlaDO> nodeSlaDOMap = nodeSlaDOList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getId, v -> v));
        Long nextSlaId = firstNodeDetail.getNextSlaId();
        while (nodeSlaDOMap.get(nextSlaId) != null) {
            PplInnerProcessVersionSlaDO slaDO = nodeSlaDOMap.get(nextSlaId);
            nextSlaId = slaDO.getNextSlaId();
            nodeDetailList.add(slaDO);
        }
        return nodeDetailList;
    }

    @Override
    public List<PplInnerProcessVersionSlaDO> getVersionSlaListWithoutPreSubmit(Long versionId) {
        List<PplInnerProcessVersionSlaDO> versionSlaList = getVersionSlaList(versionId);
        return versionSlaList.stream().filter(v -> v.getDeadlineType().equals("EXIT")).collect(Collectors.toList());
    }

    /**
     * 查询部门审批流相关审批角色,根据审批顺序从后往前排序
     *
     * @param versionId
     * @return
     */
    @Override
    public List<String> queryVersionRoles(Long versionId) {
        List<PplInnerProcessVersionSlaDO> versionSlaList = getVersionSlaList(versionId);
        List<String> roles = new ArrayList<>();
        for (int i = versionSlaList.size() - 1; i >= 0; i--) {
            roles.add(versionSlaList.get(i).getApproveRole());
        }
        return roles;
    }

    @Override
    public Map<Long, List<String>> queryVersionsRoles(List<Long> versionIds) {
        if (ListUtils.isEmpty(versionIds)) {
            return new HashMap<>();
        }
        List<PplInnerProcessVersionSlaDO> versionSlaList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?)", versionIds);
        if (ListUtils.isEmpty(versionSlaList)) {
            return new HashMap<>();
        }
        return ListUtils.toMapList(versionSlaList, PplInnerProcessVersionSlaDO::getVersionId,
                PplInnerProcessVersionSlaDO::getApproveRole);
    }

    @Override
    public List<PplInnerProcessVersionVO> queryIndustryProcessingVersion(String industryDept) {
        List<PplInnerProcessVersionVO> versionVOS = demandDBHelper.getAll(PplInnerProcessVersionVO.class,
                "where status = ? and industry_dept = ?",
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode(), industryDept);
        List<Long> versionIds = ListUtils.transform(versionVOS, PplInnerProcessVersionVO::getId);
        List<PplInnerProcessVersionSlaDO> all = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?) and deadline_type = 'ENTER'", versionIds);
        Map<Long, PplInnerProcessVersionSlaDO> slaMap = all.stream()
                .collect(Collectors.toMap(v -> v.getVersionId(), v -> v));
        for (PplInnerProcessVersionVO versionVO : versionVOS) {
            PplInnerProcessVersionSlaDO slaDO = slaMap.get(versionVO.getId());
            if (slaDO.getDeadlineTime() != null && slaDO.getDeadlineTime().before(new Date())) {
                versionVO.setIsAudit(Boolean.TRUE);
            }
        }
        return versionVOS;
    }

    @Override
    public Boolean checkVersionIsAudit(String industryDept, String product) {
        PplInnerProcessVersionDO innerProcessVersionDO = queryProcessingVersionByDeptAndProduct(industryDept, product);
        PplInnerProcessVersionSlaDO one = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and deadline_type = ?", innerProcessVersionDO.getId(), "ENTER");
        if (one == null || one.getDeadlineTime() == null || one.getDeadlineTime().after(new Date())) {
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean checkNextVersionIsSameYearMonth(Long versionId) {
        PplInnerProcessVersionDO currentVersion = demandDBHelper.getByKey(PplInnerProcessVersionDO.class, versionId);
        if (currentVersion.getDemandBeginYear() == null || currentVersion.getDemandBeginMonth() == null) {
            return Boolean.FALSE;
        }
        PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and id > ? order by id asc limit 1 ",
                currentVersion.getProcessId(), currentVersion.getId());
        if (nextVersion == null || nextVersion.getDemandBeginYear() == null
                || nextVersion.getDemandBeginMonth() == null) {
            return Boolean.FALSE;
        }

        if (currentVersion.getInCountryDemandYearMonth().equals(nextVersion.getInCountryDemandYearMonth())
                && currentVersion.getOverseasDemandYearMonth().equals(nextVersion.getOverseasDemandYearMonth())) {
            return Boolean.TRUE;
        }

        return Boolean.FALSE;
    }

    @Override
    public PplInnerProcessVersionDO nextVersion(PplInnerProcessVersionDO currentVersion) {
        if (currentVersion == null) {
            return null;
        }
        PplInnerProcessVersionDO nextVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and id > ? order by id asc limit 1 ",
                currentVersion.getProcessId(), currentVersion.getId());
        return nextVersion;
    }

    @Override
    public PplInnerProcessVersionDO nextVersion(String industryDept, String product) {
        PplInnerProcessVersionDO currentVersion = queryProcessingVersionByDeptAndProduct(industryDept, product);
        PplInnerProcessVersionDO res = nextVersion(currentVersion);
        if (res == null) {
            res = new PplInnerProcessVersionDO();
            res.setDemandBeginYear(currentVersion.getDemandBeginYear());
            res.setDemandBeginMonth(currentVersion.getDemandBeginMonth());
            res.setBeginDate(currentVersion.getBeginDate());
            res.setEndDate(currentVersion.getEndDate());
            res.setDemandEndYear(currentVersion.getDemandEndYear());
            res.setDemandEndMonth(currentVersion.getDemandEndMonth());
            res.setOverseasDemandBeginYear(currentVersion.getOverseasDemandBeginYear());
            res.setOverseasDemandBeginMonth(currentVersion.getOverseasDemandBeginMonth());
        }
        return res;
    }

    @Override
    public PplInnerProcessVersionDO getByVersionId(Long versionId) {
        return demandDBHelper.getByKey(PplInnerProcessVersionDO.class, versionId);
    }

    @Override
    public void saveProcessConfig(SaveProcessConfigReq req) {
        PplInnerProcessDO pplInnerProcessDO = new PplInnerProcessDO();
        pplInnerProcessDO.setId(req.getProcessId());
        pplInnerProcessDO.setSameApproveAutoPass(req.getAutoApprovalForSameApprove());
        pplInnerProcessDO.setConsensusRole(req.getConsensusRole());
        demandDBHelper.update(pplInnerProcessDO);
    }

    @Override
    public List<PplInnerApproveVersionDTO> queryIndustryVersionList(QueryIndustryApproveReq req) {
        Long processId = req.getProcessId();

        List<PplInnerApproveVersionItemVO> versionItemVOList = demandDBHelper.getAll(PplInnerApproveVersionItemVO.class,
                "where process_id = ? ", processId);
        if (CollectionUtils.isEmpty(versionItemVOList)) {
            return Collections.emptyList();
        }

        List<PplInnerApproveVersionDTO> versionDTOList = ListUtils.transform(versionItemVOList,
                o -> new PplInnerApproveVersionDTO(o.getId(), o.getVersionCode(), o.getStatus(),
                        PplInnerProcessVersionStatusEnum.getNameByCode(o.getStatus()),
                        o.getDemandBeginYear(), o.getDemandBeginMonth(), o.getDemandEndYear(), o.getDemandEndMonth(),
                        o.getOverseasDemandBeginYear(),o.getOverseasDemandBeginMonth(),
                        CollectionUtils.isEmpty(o.getSlaDOList()) ? null : o.getSlaDOList().get(0).getDeadlineTime(),
                        CollectionUtils.isEmpty(o.getSlaDOList()) ? null
                                : o.getSlaDOList().get(o.getSlaDOList().size() - 1).getDeadlineTime(),
                        o.getVersionCreator(),
                        o.getTemplateId(),
                        o.getTemplateDO() != null ? o.getTemplateDO().getTemplate() : "（原模版已被删除）",
                        PplInnerProcessVersionStatusEnum.NEW.getCode().equals(o.getStatus()),
                        PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(o.getStatus())
                                || PplInnerProcessVersionStatusEnum.NEW.getCode().equals(o.getStatus()),
                        !CollectionUtils.isEmpty(o.getSlaDOList().stream().filter(e -> e.getDeadlineTime() == null)
                                .collect(Collectors.toList()))));

        // 排序处理
        List<PplInnerApproveVersionDTO> processVersionDTOs = versionDTOList.stream()
                .filter(o -> PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(o.getStatus()))
                .collect(Collectors.toList());
        List<PplInnerApproveVersionDTO> newVersionDTOs = versionDTOList.stream()
                .filter(o -> PplInnerProcessVersionStatusEnum.NEW.getCode().equals(o.getStatus()))
                .sorted(Comparator.comparing(PplInnerApproveVersionDTO::getVersionId))
                .collect(Collectors.toList());
        List<PplInnerApproveVersionDTO> doneVersionDTOs = versionDTOList.stream()
                .filter(o -> PplInnerProcessVersionStatusEnum.DONE.getCode().equals(o.getStatus()))
                .sorted(Comparator.comparing(PplInnerApproveVersionDTO::getVersionId))
                .collect(Collectors.toList());
        processVersionDTOs.addAll(newVersionDTOs);
        processVersionDTOs.addAll(doneVersionDTOs);
        return processVersionDTOs;
    }

    @Override
    public String processApprovalVersion(Long versionId) {
        String message = "ok";

        PplInnerProcessVersionDO newVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where id = ? and status = ? ", versionId,
                PplInnerProcessVersionStatusEnum.NEW.getCode());
        if (newVersion == null) {
            throw new BizException("此版本不存在！请核查！");
        }

        PplInnerProcessVersionDO processVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and status = ? ", newVersion.getProcessId(),
                PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        if (processVersion != null) {
            throw new BizException("此行业已存在生效中版本，versionId-" + processVersion.getId());
        }

        PplInnerProcessVersionDO lastVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and status = ? ", newVersion.getProcessId(),
                PplInnerProcessVersionStatusEnum.DONE.getCode());
        if (lastVersion != null) {
            message = "【" + lastVersion.getIndustryDept() + "】-【" + lastVersion.getProduct()
                    + "】已存在启动过的审批版本，系统在版本结束后会自动开启下一版本，现已手动开启，请注意手动开启是否有影响！";
        }

        // 开启此版本（版本开启时自动生成版本号）
        PplInnerProcessVersionSlaDO enterNodeDTO = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? and node_code = ? ", newVersion.getId(), PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
        Date deadline = enterNodeDTO.getDeadlineTime();
        if (deadline != null) {
            String versionCode =
                    "V_" + DateUtils.getYear(deadline) + String.format("%tm", deadline) + String.format("%td",
                            deadline);
            newVersion.setVersionCode(versionCode);
        }

        newVersion.setStatus(PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
        newVersion.setBeginDate(LocalDate.now());
        newVersion.setVersionUpdater("system");

        demandDBHelper.update(newVersion);

        // 将新版本需求年月范围已生效数据移至新版本的需求沟通中
        pplInnerProcessService.syncNewVersionPpl(newVersion.getId());

        return message;
    }

    @Override
    public PplInnerApproveVersionResp queryIndustryApprovalVersion(PplInnerApproveVersionReq req) {
        Long versionId = req.getVersionId();
        PplInnerApproveVersionItemVO versionItemVO = demandDBHelper.getOne(PplInnerApproveVersionItemVO.class,
                "where deleted = 0 and id = ? ", versionId);
        if (versionItemVO == null) {
            throw new BizException("找不到此审批版本！请联系lauren检查");
        }

        PplInnerApproveVersionResp resp = new PplInnerApproveVersionResp();
        resp.setNodeDetails(getNodesByVersion(versionId));
        resp.setConsensusVersion(versionItemVO.getConsensusVersion());
        return resp;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void deleteIndustryApprovalVersion(PplInnerApproveVersionReq req) {
        PplInnerProcessVersionDO versionDO = demandDBHelper.getOne(PplInnerProcessVersionDO.class, "where id = ? ",
                req.getVersionId());
        if (versionDO == null) {
            throw new BizException("找不到此审批版本，请联系lauren核查！");
        }
        if (!PplInnerProcessVersionStatusEnum.NEW.getCode().equals(versionDO.getStatus())) {
            throw new BizException("只允许删除【待启动】版本！");
        }
        demandDBHelper.executeRaw("update ppl_inner_process_version set deleted = 1 where id = ? ", req.getVersionId());
        demandDBHelper.delete(PplInnerProcessVersionSlaDO.class, "where version_id = ? ", req.getVersionId());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void createIndustryApprovalVersion(EditPplInnerApproveVersionReq req) {
        if (req.getProcessId() == null) {
            throw new BizException("行业id为空，请传递行业id！");
        }

        checkNodeEditParam(req.getNodeDetails());

        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class, "where id = ? ",
                req.getProcessId());
        if (processDO == null) {
            throw new BizException("找不到行业信息，请联系lauren核查！");
        }

        // 1、生成版本信息
        PplInnerProcessVersionDO versionDO = new PplInnerProcessVersionDO();
        versionDO.setDemandBeginYear(req.getDemandBeginYear());
        versionDO.setDemandBeginMonth(req.getDemandBeginMonth());
        versionDO.setDemandEndYear(req.getDemandEndYear());
        versionDO.setDemandEndMonth(req.getDemandEndMonth());
        versionDO.setOverseasDemandBeginYear(req.getOverseasDemandBeginYear());
        versionDO.setOverseasDemandBeginMonth(req.getOverseasDemandBeginMonth());

        versionDO.setStatus(PplInnerProcessVersionStatusEnum.NEW.getCode());
        versionDO.setIndustryDept(processDO.getIndustryDept());
        versionDO.setProduct(processDO.getProduct());
        versionDO.setProcessId(req.getProcessId());
        versionDO.setTemplateId(req.getTemplateId());
        String currentUser = LoginUtils.getUserNameWithSystem();
        versionDO.setVersionCreator(currentUser);
        versionDO.setVersionUpdater(currentUser);
        versionDO.setConsumeStatus(0);
        demandDBHelper.insert(versionDO);

        // 2、拉取模版最新快照（批量生成）
        batchProduceNodeSlas(Collections.singletonMap(versionDO.getId(), req.getNodeDetails()),
                Collections.singletonList(versionDO.getId()), req.getTemplateId(), false);
    }

    @Override
    @Transactional("demandTransactionManager")
    public void updateIndustryApprovalVersion(EditPplInnerApproveVersionReq req) {
        // 传参校验
        if (req.getVersionId() == null) {
            throw new BizException("审批版本id为空，请传递审批版本id！");
        }
        checkNodeEditParam(req.getNodeDetails());

        // 条件限制
        PplInnerProcessVersionDO versionDO = demandDBHelper.getOne(PplInnerProcessVersionDO.class, "where id = ? ",
                req.getVersionId());
        if (versionDO == null) {
            throw new BizException("找不到此审批版本，请联系lauren核查！");
        }
        if (PplInnerProcessVersionStatusEnum.DONE.getCode().equals(versionDO.getStatus())) {
            throw new BizException("不允许编辑【已完成】版本！");
        }

        boolean isTemplateUpdate = !versionDO.getTemplateId().equals(req.getTemplateId());
        if (PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(versionDO.getStatus()) && isTemplateUpdate) {
            throw new BizException("【生效中】版本不允许重新选择模版！");
        }

        // 1、更新版本信息 （需求录入范围）
        PplInnerProcessVersionDO updateVersionDO = new PplInnerProcessVersionDO();
        updateVersionDO.setId(versionDO.getId());
        updateVersionDO.setVersionUpdater(LoginUtils.getUserNameWithSystem());
        if (!PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(versionDO.getStatus())) {
            updateVersionDO.setDemandBeginYear(req.getDemandBeginYear());
            updateVersionDO.setDemandBeginMonth(req.getDemandBeginMonth());
            updateVersionDO.setOverseasDemandBeginYear(req.getOverseasDemandBeginYear());
            updateVersionDO.setOverseasDemandBeginMonth(req.getOverseasDemandBeginMonth());
        }
        updateVersionDO.setDemandEndYear(req.getDemandEndYear());
        updateVersionDO.setDemandEndMonth(req.getDemandEndMonth());
        if (PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(versionDO.getStatus()) && (StringUtils.isEmpty(
                versionDO.getVersionCode()) || "V_待配置".equals(versionDO.getVersionCode()))) {
            PplInnerApproveNodeDTO enterNodeDTO = req.getNodeDetails().stream()
                    .filter(e -> PplInnerProcessNodeEnum.PRE_SUBMIT.getCode().equals(e.getNodeCode())).collect(
                            Collectors.toList()).get(0);
            Date deadline = enterNodeDTO.getDeadlineTime();
            String versionCode =
                    "V_" + DateUtils.getYear(deadline) + String.format("%tm", deadline) + String.format("%td",
                            deadline);
            updateVersionDO.setVersionCode(versionCode);
        }

        // 版本无变动，则更新version表其他配置，sla表deadlineTime（版本无变动时，前端传递的node信息为创建此版本时生成的快照node信息-queryIndustryApprovalVersion接口返回）
        if (!isTemplateUpdate) {
            if (!CollectionUtils.isEmpty(req.getNodeDetails().stream().filter(e -> e.getNodeSlaId() == null).collect(
                    Collectors.toList()))) {
                throw new BizException("节点id缺失，请传递！");
            }
            // 1.1、更新版本信息
            demandDBHelper.update(updateVersionDO);
            // 1.2、更新版本下的节点快照信息

            List<PplInnerApproveNodeDTO> nodeDTOs = req.getNodeDetails();
            // 节点编辑限制：生效中-当前节点及之后的节点 可编辑，待启动-全部节点 可编辑
            if (PplInnerProcessVersionStatusEnum.PROCESSING.getCode().equals(versionDO.getStatus())) {
                PplInnerVersionVO pplInnerVersionVO = demandDBHelper.getOne(PplInnerVersionVO.class,
                        "where id = ?", versionDO.getId());
                setCurrentNodeForVersion(pplInnerVersionVO);

                int skipNum = 0;
                int index = 0;
                for (PplInnerApproveNodeDTO nodeDTO : nodeDTOs) {
                    if (nodeDTO.getNodeCode().equals(pplInnerVersionVO.getCurrentNodeCode())) {
                        skipNum = index;
                        break;
                    }
                    index++;
                }
                nodeDTOs = nodeDTOs.stream().skip(skipNum).collect(Collectors.toList());
            }

            List<PplInnerProcessVersionSlaDO> updateSlaDOs = new ArrayList<>();
            nodeDTOs.forEach(node -> {
                PplInnerProcessVersionSlaDO slaDO = new PplInnerProcessVersionSlaDO();
                slaDO.setId(node.getNodeSlaId());
                slaDO.setDeadlineTime(node.getDeadlineTime());
                updateSlaDOs.add(slaDO);
            });
            demandDBHelper.update(updateSlaDOs);

            return;
        }

        // 2、如果选择的审批模版有变动，则version表更新审批模版，sla表重新拉取模版最新快照
        // （版本有变动时，前端传递的node信息为选择的模版对应的最新数据-queryIndustryApprovalTemplate接口返回）
        updateVersionDO.setTemplateId(req.getTemplateId());
        demandDBHelper.update(updateVersionDO);

        demandDBHelper.delete(PplInnerProcessVersionSlaDO.class, "where version_id = ? ", versionDO.getId());

        batchProduceNodeSlas(Collections.singletonMap(versionDO.getId(), req.getNodeDetails()),
                Collections.singletonList(versionDO.getId()), req.getTemplateId(), false);
    }

    @Override
    public List<PplInnerApproveTemplateDTO> queryIndustryApprovalTemplateList(QueryIndustryApproveReq req) {
        Long processId = req.getProcessId();

        List<PplInnerProcessTemplateDO> templateDOList = demandDBHelper.getAll(PplInnerProcessTemplateDO.class,
                "where process_id = ? ", processId);
        if (CollectionUtils.isEmpty(templateDOList)) {
            return Collections.emptyList();
        }

        List<Long> templateIds = templateDOList.stream().map(PplInnerProcessTemplateDO::getId)
                .collect(Collectors.toList());
        List<PplInnerProcessNodeDO> nodeDOList = demandDBHelper.getRaw(PplInnerProcessNodeDO.class,
                "select id, template_id from ppl_inner_process_node where deleted = 0 and template_id in(?) ",
                templateIds);
        Map<Long, List<Long>> nodeOfTemplateMap = nodeDOList.stream().collect(
                Collectors.groupingBy(PplInnerProcessNodeDO::getTemplateId,
                        Collectors.mapping(PplInnerProcessNodeDO::getId, Collectors.toList())));

        return ListUtils.transform(templateDOList, o -> new PplInnerApproveTemplateDTO(o.getId(), o.getTemplate(),
                o.getTemplateType(), nodeOfTemplateMap.getOrDefault(o.getId(),
                Collections.emptyList()).size() + 1, o.getTemplateCreator(),
                o.getUpdateTime()));
    }

    @Override
    public PplInnerApproveTemplateDetailDTO queryIndustryApprovalTemplate(PplInnerApproveTemplateReq req) {
        Long templateId = req.getTemplateId();
        PplInnerProcessTemplateDO templateDO = demandDBHelper.getOne(PplInnerProcessTemplateDO.class, "where id = ? ",
                templateId);
        if (templateDO == null) {
            throw new BizException("找不到此审批模版！请联系lauren检查");
        }

        PplInnerApproveTemplateDetailDTO templateDetailDTO = new PplInnerApproveTemplateDetailDTO();
        templateDetailDTO.setTemplateId(templateId);
        templateDetailDTO.setTemplateName(templateDO.getTemplate());
        templateDetailDTO.setTemplateType(templateDO.getTemplateType());

        List<PplInnerApproveNodeDTO> nodeDTOList = getNodesByTemplate(templateId);
        if (CollectionUtils.isEmpty(nodeDTOList)) {
            PplInnerApproveNodeDTO firstNodeDTO = new PplInnerApproveNodeDTO();
            firstNodeDTO.setNodeCode(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
            firstNodeDTO.setNodeName(PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
            firstNodeDTO.setDeadlineType("ENTER");
            nodeDTOList.add(firstNodeDTO);
        }
        templateDetailDTO.setNodeDetails(nodeDTOList);

        return templateDetailDTO;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void deleteIndustryApprovalTemplate(PplInnerApproveTemplateReq req) {
        PplInnerProcessTemplateDO templateDO = demandDBHelper.getOne(PplInnerProcessTemplateDO.class, "where id = ? ",
                req.getTemplateId());
        if (templateDO == null) {
            throw new BizException("找不到此审批模版，请联系lauren核查！");
        }

        List<PplInnerProcessVersionDO> versionDOList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where template_id = ? and  status in( ? ) ",
                req.getTemplateId(),
                Arrays.asList(PplInnerProcessVersionStatusEnum.NEW.getCode(),
                        PplInnerProcessVersionStatusEnum.PROCESSING.getCode()));

        if (!CollectionUtils.isEmpty(versionDOList)) {
            throw new BizException("此审批模版正被" + versionDOList.size() + "个【"
                    + PplInnerProcessVersionStatusEnum.PROCESSING.getName() + "/"
                    + PplInnerProcessVersionStatusEnum.NEW.getName() + "】版本引用，请勿误删！");
        }

        demandDBHelper.executeRaw("update ppl_inner_process_template set deleted = 1 where id = ? ",
                req.getTemplateId());
        demandDBHelper.executeRaw("update ppl_inner_process_node set deleted = 1 where template_id = ? ",
                req.getTemplateId());
    }

    @Override
    @Transactional("demandTransactionManager")
    public void createIndustryApprovalTemplate(PplInnerApproveTemplateDetailDTO req) {
        checkTemplateEditParam(req);

        PplInnerProcessTemplateDO templateDOByName = demandDBHelper.getOne(PplInnerProcessTemplateDO.class,
                "where process_id = ? and template = ? ", req.getProcessId(), req.getTemplateName());
        if (templateDOByName != null) {
            throw new BizException("此模版名称已存在，请重新填写！");
        }

        List<PplInnerProcessTemplateDO> templateList = demandDBHelper.getAll(PplInnerProcessTemplateDO.class,
                " where process_id = ? and template_type = ?", req.getProcessId(),req.getTemplateType());
        if (ListUtils.isNotEmpty(templateList)){
            throw new BizException("该行业审批模版至多只能创建1个周审批模版、1个月审批模版！");
        }

        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class, "where id = ? ",
                req.getProcessId());
        if (processDO == null) {
            throw new BizException("找不到此行业信息，请联系kaijiazhang ");
        }

        if (processDO.getIsAutoCreateVersion()){
            List<PplInnerApproveNodeDTO> notSetTimeList = req.getNodeDetails().stream()
                    .filter(node -> node.getNextWeek() == null
                            || node.getDayOfWeek() == null
                            || node.getTime() == null).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(notSetTimeList)) {
                throw new BizException("该行业设置了自动开启版本,请设置具体节点时间！");
            }
        }

        // 1、生成模版
        PplInnerProcessTemplateDO newTemplateDO = new PplInnerProcessTemplateDO();
        newTemplateDO.setTemplate(req.getTemplateName());
        newTemplateDO.setTemplateType(req.getTemplateType());
        newTemplateDO.setProcessId(req.getProcessId());
        String currentUser = LoginUtils.getUserNameWithSystem();
        newTemplateDO.setTemplateCreator(currentUser);
        newTemplateDO.setTemplateUpdater(currentUser);
        demandDBHelper.insert(newTemplateDO);

        // 2、生成节点流
        batchProduceNodes(getNodeCodeByNameMap(processDO.getId()), req.getNodeDetails(), newTemplateDO.getId(),
                req.getProcessId());
    }

    @Override
    @Transactional("demandTransactionManager")
    public List<String> updateIndustryApprovalTemplate(PplInnerApproveTemplateDetailDTO req) {
        if (req.getTemplateId() == null) {
            throw new BizException("模版id为空，请传递要编辑的模版id！");
        }
        checkTemplateEditParam(req);
        PplInnerProcessTemplateDO templateDOByName = demandDBHelper.getOne(PplInnerProcessTemplateDO.class,
                "where process_id = ? and template = ? and id != ? ", req.getProcessId(), req.getTemplateName(),
                req.getTemplateId());
        if (templateDOByName != null) {
            throw new BizException("此模版名称已存在，请重新填写！");
        }

        PplInnerProcessTemplateDO templateDO = demandDBHelper.getOne(PplInnerProcessTemplateDO.class, "where id = ? ",
                req.getTemplateId());
        if (templateDO == null) {
            throw new BizException("找不到此审批模版，请联系lauren核查！");
        }

        List<PplInnerProcessTemplateDO> templateList = demandDBHelper.getAll(PplInnerProcessTemplateDO.class,
                " where process_id = ? and template_type = ? and id != ?", req.getProcessId(),req.getTemplateType(),req.getTemplateId());
        if (ListUtils.isNotEmpty(templateList)){
            throw new BizException("该行业审批模版至多只能创建1个周审批模版、1个月审批模版！");
        }


        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class, "where id = ? ",
                req.getProcessId());
        if (processDO.getIsAutoCreateVersion()){
            List<PplInnerApproveNodeDTO> notSetTimeList = req.getNodeDetails().stream()
                    .filter(node -> node.getNextWeek() == null
                            || node.getDayOfWeek() == null
                            || node.getTime() == null).collect(Collectors.toList());
            if (ListUtils.isNotEmpty(notSetTimeList)) {
                throw new BizException("该行业设置了自动开启版本,请设置具体节点时间！");
            }
        }

        // 1、更新模版
        PplInnerProcessTemplateDO updateTemplateDO = new PplInnerProcessTemplateDO();
        updateTemplateDO.setId(templateDO.getId());
        updateTemplateDO.setTemplate(req.getTemplateName());
        updateTemplateDO.setTemplateUpdater(LoginUtils.getUserNameWithSystem());
        updateTemplateDO.setTemplateType(req.getTemplateType());
        demandDBHelper.update(updateTemplateDO);

        // 先保存原节点流的节点code备份，供生成节点code时重复使用
        // （否则每次更改模版，根据节点名找不到对应code（因为原节点删除后未保存备份），会出现问题：（1）导致生成新的节点code，最终与审批版本中的快照节点code对不上，造成模版中未更改节点在版本中无法保留结束时间配置；（2）导致ppl单存储的node_code对不上，造成当前节点展示错误的问题）
        Map<String, String> nodeNameByCodeMap = getNodeCodeByNameMap(req.getProcessId());

        // 2、更新节点流
        demandDBHelper.executeRaw("update ppl_inner_process_node set deleted = 1 where template_id = ? ",
                templateDO.getId());
        batchProduceNodes(nodeNameByCodeMap, req.getNodeDetails(), templateDO.getId(), req.getProcessId());

        // 3、更新 所有 选择了此模版的 【待启动】版本的快照
//        List<PplInnerProcessVersionDO> relatedVersionDOList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
//                " where process_id = ? and template_id = ? and status = ?  ",
//                req.getProcessId(), templateDO.getId(), PplInnerProcessVersionStatusEnum.NEW.getCode());
//        if (CollectionUtils.isEmpty(relatedVersionDOList)) {
//            return Collections.emptyList();
//        }
//
//        List<Long> relatedVersionIds = relatedVersionDOList.stream().map(PplInnerProcessVersionDO::getId)
//                .collect(Collectors.toList());
//
//        Map<Long, List<PplInnerApproveNodeDTO>> oldNodeDTOByVersionMap = new HashMap<>();
//        Map<Long, List<PplInnerProcessVersionSlaDO>> oldSlaDOByVersionMap = demandDBHelper.getAll(
//                        PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", relatedVersionIds).stream()
//                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));
//        for (Map.Entry<Long, List<PplInnerProcessVersionSlaDO>> entry : oldSlaDOByVersionMap.entrySet()) {
//            Long oldVersionId = entry.getKey();
//            List<PplInnerProcessVersionSlaDO> oldVersionSlaDOs = entry.getValue();
//
//            List<PplInnerApproveNodeDTO> oldNodeDTOs = new ArrayList<>();
//            for (PplInnerProcessVersionSlaDO slaDO : oldVersionSlaDOs) {
//                {
//                    PplInnerApproveNodeDTO oldNodeDTO = new PplInnerApproveNodeDTO();
//                    BeanUtils.copyProperties(slaDO, oldNodeDTO);
//                    oldNodeDTOs.add(oldNodeDTO);
//                }
//            }
//            oldNodeDTOByVersionMap.put(oldVersionId, oldNodeDTOs);
//        }
//
//        demandDBHelper.delete(PplInnerProcessVersionSlaDO.class, "where version_id in(?) ", relatedVersionIds);
//        boolean isOrderChange = batchProduceNodeSlas(oldNodeDTOByVersionMap, relatedVersionIds, req.getTemplateId(),
//                true);
//
//        List<String> oldNodeCodeList = new ArrayList<>(oldSlaDOByVersionMap.values()).get(0).stream()
//                .map(PplInnerProcessVersionSlaDO::getNodeCode).collect(
//                        Collectors.toList());
//        List<String> updateNodeCodeList = req.getNodeDetails().stream().map(PplInnerApproveNodeDTO::getNodeCode)
//                .collect(
//                        Collectors.toList());
//        updateNodeCodeList.removeAll(oldNodeCodeList);
//        if (CollectionUtils.isEmpty(updateNodeCodeList) && !isOrderChange) {
//            // 编辑模版后，审批节点流没有新增节点 / 节点顺序未变动
//            return Collections.emptyList();
//        }
//
//        // 审批节点流有新增节点 / 节点顺序变动
//        // 给选择了此模版的所有版本的维护人，发送邮件告警
//        boolean isTest = !Strings.isEmpty(testEnv.get());
//        String prefix = isTest ? "exp-" : "";
//        List<String> relatedUsers = relatedVersionDOList.stream().map(PplInnerProcessVersionDO::getVersionCreator)
//                .collect(
//                        Collectors.toList());
//        relatedUsers.addAll(relatedVersionDOList.stream().map(PplInnerProcessVersionDO::getVersionUpdater).collect(
//                Collectors.toList()));
//        String acceptUser = relatedUsers.stream().filter(StringUtils::isNotEmpty).filter(e -> !"system".equals(e))
//                .distinct()
//                .collect(Collectors.joining(";"));
//        String message = isOrderChange ? "修改后的模版调整了节点顺序。<br/><br/>" : "修改后的模版有新增节点。<br/><br/>";
//        String typeMessage = isOrderChange ? "调整顺序节点" : "新增节点";
//        String[] product = relatedVersionDOList.get(0).getProduct().split(";");
//        alert.sendMail(acceptUser, "行业PPL审批计划通知",
//                "【" + relatedVersionDOList.get(0).getIndustryDept() + "】-适用产品" + Arrays.toString(product)
//                        + "：<br/><br/>"
//                        + LoginUtils.getUserName() + "修改了模版【" + req.getTemplateName() + "】!<br/><br/>"
//                        + message
//                        + "修改后的模版配置，系统已同步调整到选择了此模版的待启动版本中。<br/><br/>"
//                        + "请注意！<br/>"
//                        + "<span style = \"color:red\">目前 待启动版本 缺失 " + typeMessage + " 的 结束时间配置，"
//                        + "审批流程及需求日历会发生问题，请尽快查看对应审批版本，补全相关节点的结束时间配置！</span><br/><br/>"
//                        + "详细信息请前往CRP查看。<a href='https://"
//                        + prefix + "crp.woa.com/13ppl/approval-process/sla-config'>点击前往</a>。<br />");
//        return relatedVersionDOList.stream().map(PplInnerProcessVersionDO::getVersionCode).collect(Collectors.toList());
        return new ArrayList<>();
    }


    @Override
    @Transactional("demandTransactionManager")
    public void issueConsensusVersion(IssueConsensusVersionReq req) {
        PplInnerProcessVersionDO currentVersion = getByVersionId(req.getVersionId());

        currentVersion.setIsStartConsensus(PplInnerProcessVersionStartConsensusEnum.IN_CONSENSUS.getCode());
        currentVersion.setConsensusVersion(req.getVersionCode());
        demandDBHelper.update(currentVersion);

        String userName = LoginUtils.getUserName();
        // 没有待共识数据，直接结束共识
        handleWithoutWaitConsensus(req, currentVersion, userName);

        executor.execute(() -> {
            try {
                Set<String> users = pplConsensusService.queryAllowConsensusUser(currentVersion.getIndustryDept(),
                        Ppl13weekProductTypeEnum.CVM.getName());
                PplSupplyConsensusReq consensusReq = new PplSupplyConsensusReq();
                consensusReq.setIndustryDept(currentVersion.getIndustryDept());
                consensusReq.setVersionCode(req.getVersionCode());
                consensusReq.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
                consensusReq.setMatchType(ListUtils.newArrayList(PplStockSupplyMatchTypeEnum.SUGGEST.getCode(),
                        PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getCode()));
                for (String user : users) {
                    pplConsensusService.startConsensusSendMail(consensusReq, user, null);
                }
            } catch (Exception e) {
                AlarmRobotUtil.doAlarm("行业PPL: issueConsensusVersion", "发送开始共识邮件失败，e: " + e.getMessage(),
                        null, false);
            }
        });
    }

    private void handleWithoutWaitConsensus(IssueConsensusVersionReq req,
            PplInnerProcessVersionDO innerVersion, String userName) {
        WhereContent where = new WhereContent();
        where.andEqual(PplSupplyConsensusDO::getConsensusStatus, PplConsensusStatusEnum.WAIT_CONSENSUS.getCode())
                .andEqual(PplSupplyConsensusDO::getVersionCode, innerVersion.getConsensusVersion())
                .andEqual(PplSupplyConsensusDO::getIndustryDept, innerVersion.getIndustryDept());
        long count = demandDBHelper.getCount(PplSupplyConsensusDO.class, where.getSql(), where.getParams());
        if (count < 1) {
            // 没有待共识数据，直接结束共识
            finishInnerVersionConsensus(req, innerVersion, userName);
        }
    }

    private void finishInnerVersionConsensus(IssueConsensusVersionReq req,
            PplInnerProcessVersionDO innerVersion, String userName) {
        if (req == null || req.getVersionId() == null) {
            return;
        }
        if (innerVersion == null) {
            innerVersion = getByVersionId(req.getVersionId());
        }
        if (!Objects.equals(req.getVersionCode(), innerVersion.getConsensusVersion())) {
            throw BizException
                    .makeThrow(
                            "结束共识失败，入参中共识版本【%s】与当前行业审批流版本的共识版本【%s】不相同，请刷新页面后重试",
                            req.getVersionCode(), innerVersion.getConsensusVersion());
        }
        PplInnerProcessVersionStartConsensusEnum status = PplInnerProcessVersionStartConsensusEnum
                .getByCode(innerVersion.getIsStartConsensus());
        if (status != PplInnerProcessVersionStartConsensusEnum.IN_CONSENSUS) {
            throw BizException
                    .makeThrow("仅共识中状态可以进行结束共识操作,当前状态【%s】",
                            status == null ? null : status.getName());
        }
        PplInnerProcessVersionDO updateVersion = new PplInnerProcessVersionDO();
        updateVersion.setId(innerVersion.getId());
        updateVersion.setIsStartConsensus(PplInnerProcessVersionStartConsensusEnum.OVER_CONSENSUS.getCode());
        innerVersion.setIsStartConsensus(PplInnerProcessVersionStartConsensusEnum.OVER_CONSENSUS.getCode());
        demandDBHelper.update(updateVersion);

        // 1、推送结束共识信号,需要在提交草稿前做这一步，保证需求沟通中的数据都没有待引导的共识
        if (Strings.isNotBlank(innerVersion.getConsensusVersion())) {
            String[] products = innerVersion.getProduct().split(";");
            for (String product : products) {
                //推送结束共识信号
                pplConsensusService.finishConsensus(innerVersion.getConsensusVersion(),
                        innerVersion.getIndustryDept(), product, userName);
                // 发送共识结束邮件
                finishInnerVersionConsensusSendMail(innerVersion);
            }
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void finishInnerVersionConsensus(IssueConsensusVersionReq req) {
        if (req == null || req.getVersionId() == null) {
            return;
        }
        PplInnerProcessVersionDO currentVersion = getByVersionId(req.getVersionId());
        String userName = LoginUtils.getUserName();
        finishInnerVersionConsensus(req, currentVersion, userName);
    }

    private void finishInnerVersionConsensusSendMail(PplInnerProcessVersionDO currentVersion) {
        Set<String> allowUser = pplConsensusService.queryAllowConsensusUser(currentVersion.getIndustryDept(),
                currentVersion.getProduct());
        if (!org.springframework.util.CollectionUtils.isEmpty(allowUser)) {
            Boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
            String acceptUser = org.apache.logging.log4j.util.Strings.join(allowUser, ';');
            //  测试环境关掉
            if (isTest) {
                if (acceptUser.contains("kaijiazhang") || acceptUser.contains("oliverychen")
                        || acceptUser.contains("dotyou")) {
                    // 开发产品能收到测试环境邮件
                    acceptUser = "kaijiazhang;oliverychen;dotyou";
                } else {
                    acceptUser = "";
                }
            }
            String prefix = isTest ? "exp-" : "";
            alert.sendMail(acceptUser, "行业PPL共识结束通知",
                    "本周期需求共识已结束。如果您需要查看完整的供应方案及共识结果，"
                            + " <a href='https://" + prefix
                            + "crp.woa.com/13ppl/requirement-consensus?tabKey=todo'>点击前往</a>。");
        }
    }

    @Override
    public Date checkVersionIsPreSubmit(Long versionId) {
        List<PplInnerProcessVersionSlaDO> versionSlaList = getVersionSlaList(versionId);
        if (CollectionUtils.isEmpty(versionSlaList) || versionSlaList.get(0).getDeadlineTime() == null) {
            return null;
        }
        if (versionSlaList.get(0).getDeadlineTime().after(new Date())) {
            return versionSlaList.get(0).getDeadlineTime();
        }
        return null;
    }

    @Override
    public PplInnerProcessVersionDO getLastVersion(Long currentVersionId) {
        PplInnerProcessVersionDO currentVersion = demandDBHelper.getByKey(PplInnerProcessVersionDO.class,
                currentVersionId);

        PplInnerProcessVersionDO one = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and status = ? and id < ? order by id desc", currentVersion.getProcessId(),
                PplInnerProcessVersionStatusEnum.DONE.getCode(), currentVersionId);
        return one;
    }

    @Override
    public List<PplIndustryVersionDTO> queryIndustryVersion(String industryDept) {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("industry_dept = ?", industryDept);
        whereSQL.and("status in (?)", Arrays.asList(PplInnerProcessVersionStatusEnum.PROCESSING.getCode(),
                PplInnerProcessVersionStatusEnum.DONE.getCode()));
        List<PplInnerDeptVersionVO> all = demandDBHelper.getAll(PplInnerDeptVersionVO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        if (CollectionUtils.isEmpty(all)) {
            return new ArrayList<>();
        }
        Map<LocalDate, List<PplInnerDeptVersionVO>> beginDateMap = all.stream()
                .collect(Collectors.groupingBy(PplInnerDeptVersionVO::getBeginDate));
        List<PplIndustryVersionDTO> result = new ArrayList<>();
        beginDateMap.forEach((k, v) -> {
            Map<String, List<PplInnerDeptVersionVO>> productToListMap = v.stream()
                    .collect(Collectors.groupingBy(PplInnerDeptVersionVO::getProduct));
            String keyWithMaxSize = null;
            int maxSize = 0;
            for (Map.Entry<String, List<PplInnerDeptVersionVO>> entry : productToListMap.entrySet()) {
                List<PplInnerDeptVersionVO> list = entry.getValue();
                int size = list.size();
                if (size > maxSize) {
                    maxSize = size;
                    keyWithMaxSize = entry.getKey();
                }
            }
            for (int i = 0; i < maxSize; i++) {
                PplIndustryVersionDTO dto = new PplIndustryVersionDTO();
                List<PplInnerDeptVersionVO> relevantVersion = new ArrayList<>();
                dto.setBeginDate(k.toString() + (i == 0 ? "" : "_" + i));
                for (String s : productToListMap.keySet()) {
                    if (productToListMap.get(s).size() > i) {
                        relevantVersion.add(productToListMap.get(s).get(i));
                    }
                }
                dto.setRelevantVersion(relevantVersion);
                result.add(dto);
            }
        });
        List<PplIndustryVersionDTO> collect = result.stream()
                .sorted(Comparator.comparing(PplIndustryVersionDTO::getBeginDate).reversed())
                .collect(Collectors.toList());

        // 补充审批版本 关联13周产品版本的信息
        Map<String, List<PplInnerDeptVersionVO>> relevantVersionMap = collect.stream()
                .map(PplIndustryVersionDTO::getRelevantVersion)
                .flatMap(List::stream).collect(Collectors.groupingBy(PplInnerDeptVersionVO::getProduct));
        for (Map.Entry<String, List<PplInnerDeptVersionVO>> entry : relevantVersionMap.entrySet()) {
            List<String> products = Lang.list(entry.getKey().split(";"));
            pplInnerProcessService.completeInnerDeptVersions(entry.getValue(), products);
        }

        return collect;
    }

    /**
     * 设置指定版本的当前节点信息
     *
     * @param pplInnerVersionVO 指定版本
     */
    public void setCurrentNodeForVersion(PplInnerVersionVO pplInnerVersionVO) {
        List<PplOrderAuditRecordVO> pplOrderAuditRecordVOS = auditRecordService.queryVersionPplOrder(
                pplInnerVersionVO.getId(), pplInnerVersionVO.getIndustryDept(), null, null,
                null, null);
        Set<String> nodeCode = pplOrderAuditRecordVOS.stream()
                .filter(v -> Strings.isNotBlank(v.getPplOrderDO().getNodeCode()))
                .map(v -> v.getPplOrderDO().getNodeCode()).collect(Collectors.toSet());
        PplInnerVersionVO.preHandle(pplInnerVersionVO, nodeCode);
    }

    /**
     * 设置指定版本的当前节点信息
     *
     * @param pplInnerVersionVO 指定版本
     */
    public void newSetCurrentNodeForVersion(PplInnerVersionVO pplInnerVersionVO,
            List<PplOrderAuditRecordDO> recordList) {
        Set<String> nodeCode = ListUtils.isEmpty(recordList) ? new HashSet<>() : recordList.stream()
                .map(PplOrderAuditRecordDO::getNodeCode).collect(Collectors.toSet());
        PplInnerVersionVO.preHandle(pplInnerVersionVO, nodeCode);
    }

    @Override
    public List<PplInnerProcessVersionDO> queryProcessingVersionByDept(String industryDept) {
        List<PplInnerProcessVersionDO> result = new ArrayList<>();
        if (Strings.isBlank(industryDept)) {
            return result;
        }
        WhereContent where = new WhereContent()
                .andEqual(PplInnerProcessVersionDO::getStatus, PplInnerProcessVersionStatusEnum.PROCESSING.getCode())
                .andEqual(PplInnerProcessVersionDO::getIndustryDept, industryDept);
        result = demandDBHelper.getAll(PplInnerProcessVersionDO.class, where.getSql(), where.getParams());
        return result;
    }

    @Override
    public List<PplInnerProcessVersionDO> queryVersionInfoByIdList(String industryDept, List<Long> versionIdList) {
        List<PplInnerProcessVersionDO> result = new ArrayList<>();
        if (Strings.isBlank(industryDept) || ListUtils.isEmpty(versionIdList)) {
            return result;
        }
        WhereContent where = new WhereContent()
                .andIn(PplInnerProcessVersionDO::getId, versionIdList)
                .andEqual(PplInnerProcessVersionDO::getIndustryDept, industryDept);
        result = demandDBHelper.getAll(PplInnerProcessVersionDO.class, where.getSql(), where.getParams());
        return result;
    }

    /**
     * 拼接指定版本下的审批节点流（快照数据-ppl_inner_process_version_sla）
     *
     * @param versionId 审批版本 ppl_inner_process_version
     * @return 审批节点流
     */
    public List<PplInnerApproveNodeDTO> getNodesByVersion(Long versionId) {
        List<PplInnerProcessVersionSlaDO> nodeSlaList = getVersionSlaList(versionId);
        return ListUtils.transform(nodeSlaList,
                PplInnerApproveNodeDTO::convertPplInnerApproveNodeDTO);
    }

    @Override
    public List<PplInnerApproveNodeDTO> getNodesByVersionWithoutPreSubmit(Long versionId) {
        List<PplInnerApproveNodeDTO> nodesByVersion = getNodesByVersion(versionId);
        // 删除预提交节点
        nodesByVersion.remove(0);
        return nodesByVersion;
    }

    /**
     * 拼接指定模版下的审批节点流（最新数据-ppl_inner_process_node）
     *
     * @param templateId 审批模版 ppl_inner_process_template
     * @return 审批节点流
     */
    public List<PplInnerApproveNodeDTO> getNodesByTemplate(Long templateId) {
        if (templateId == null) {
            return new ArrayList<>();
        }

        List<PplInnerVersionNodeDetail> nodeDOList = demandDBHelper.getAll(PplInnerVersionNodeDetail.class,
                "where template_id = ? ", templateId);

        if (CollectionUtils.isEmpty(nodeDOList)) {
            return new ArrayList<>();
        }

        List<PplInnerVersionNodeDetail> nodeDetailList = new ArrayList<>();


        Map<Long, PplInnerVersionNodeDetail> nodeDOMap = nodeDOList.stream()
                .collect(Collectors.toMap(PplInnerVersionNodeDetail::getId, v -> v));
        PplInnerVersionNodeDetail firstNode = nodeDOList.stream()
                .filter(e -> Boolean.TRUE.equals(e.getIsBeginNode()))
                .collect(Collectors.toList()).get(0);

        List<PplInnerVersionNodeDetail> preSubmit = nodeDOList.stream()
                .filter(e -> e.getNodeCode().equals(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode()))
                .collect(Collectors.toList());

        if (ListUtils.isEmpty(preSubmit)){
            // 如果没有预提交节点，则需要添加预提交节点  历史数据 后面考虑拆除
            PplInnerVersionNodeDetail firstNodeDetail = new PplInnerVersionNodeDetail();
            firstNodeDetail.setNodeCode(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
            firstNodeDetail.setNodeName(PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
            firstNodeDetail.setDeadlineType("ENTER");
            firstNodeDetail.setDeadlineAutoPass(PplInnerProcessNodeDeadlineAutoPassEnum.CLOSE.getCode());
            firstNodeDetail.setIsHighlight(false);
            nodeDetailList.add(firstNodeDetail);
        }else {
            PplInnerVersionNodeDetail nodeDO = preSubmit.get(0);
            nodeDO.setDeadlineType("ENTER");
            nodeDetailList.add(nodeDO);
        }

        Long nextId = firstNode.getId();
        while (nodeDOMap.get(nextId) != null) {
            PplInnerVersionNodeDetail nodeDO = nodeDOMap.get(nextId);
            nodeDO.setDeadlineType("EXIT");
            nodeDetailList.add(nodeDO);

            nextId = nodeDO.getNextId();
        }

        return ListUtils.transform(nodeDetailList,
                PplInnerApproveNodeDTO::convertPplInnerApproveNodeDTO);
    }

    /**
     * 拉取模版最新快照（批量生成）-- 适用 系统自动生成下一版本 时的快照拉取
     *
     * @param templateId 节点对应的模版id
     * @param versionId 快照节点对应的版本
     */
    public void batchProduceNodeSlasAuto(Long templateId, Long versionId) {
        List<PplInnerApproveNodeDTO> nodeTemplateDTOs = getNodesByTemplate(templateId);
        if (CollectionUtils.isEmpty(nodeTemplateDTOs)) {
            PplInnerApproveNodeDTO firstNodeDTO = new PplInnerApproveNodeDTO();
            firstNodeDTO.setNodeCode(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode());
            firstNodeDTO.setNodeName(PplInnerProcessNodeEnum.PRE_SUBMIT.getName());
            firstNodeDTO.setDeadlineType("ENTER");
            nodeTemplateDTOs.add(firstNodeDTO);
        }

        // 1、生成快照
        // 此处slaId实际为node表id
        List<PplInnerProcessVersionSlaDO> nodeSlaDOList = new ArrayList<>();
        for (PplInnerApproveNodeDTO nodeDTO : nodeTemplateDTOs) {
            PplInnerProcessVersionSlaDO slaDO = new PplInnerProcessVersionSlaDO();
            BeanUtils.copyProperties(nodeDTO, slaDO);
            slaDO.setVersionId(versionId);
            nodeSlaDOList.add(slaDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(nodeSlaDOList);

        if (nodeSlaDOList.size() == 1) {
            return;
        }

        // 2、更新模版快照的顺序，即next_sla_id字段, 此处实际为node表id
        List<PplInnerProcessVersionSlaDO> newSlaDOList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id = ? ", versionId);
        Map<String, PplInnerProcessVersionSlaDO> newSlaDOMap = newSlaDOList.stream()
                .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));

        PplInnerProcessVersionSlaDO updateFirstSlaDO = new PplInnerProcessVersionSlaDO();
        updateFirstSlaDO.setId(newSlaDOMap.get(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode()).getId());
        updateFirstSlaDO.setNextSlaId(
                newSlaDOList.stream().filter(e -> Boolean.TRUE.equals(e.getIsBeginNode())).collect(Collectors.toList())
                        .get(0).getId());

        List<PplInnerProcessVersionSlaDO> updateSlaDOs = new ArrayList<>();
        updateSlaDOs.add(updateFirstSlaDO);

        Map<Long, PplInnerApproveNodeDTO> nodeTemplateDTOMap = nodeTemplateDTOs.stream()
                .filter(e -> e.getNodeSlaId() != null)
                .collect(Collectors.toMap(PplInnerApproveNodeDTO::getNodeSlaId, v -> v));
        for (Map.Entry<Long, PplInnerApproveNodeDTO> entry : nodeTemplateDTOMap.entrySet()) {
            PplInnerApproveNodeDTO nextNodeDTO = nodeTemplateDTOMap.get(entry.getValue().getNextNodeSlaId());
            if (nextNodeDTO == null) {
                continue;
            }
            PplInnerProcessVersionSlaDO currentSlaDO = newSlaDOMap.get(entry.getValue().getNodeCode());
            PplInnerProcessVersionSlaDO nextSlaDO = newSlaDOMap.get(nextNodeDTO.getNodeCode());
            if (currentSlaDO != null && nextSlaDO != null) {
                PplInnerProcessVersionSlaDO updateSlaDO = new PplInnerProcessVersionSlaDO();
                updateSlaDO.setNextSlaId(nextSlaDO.getId());
                updateSlaDO.setId(currentSlaDO.getId());
                updateSlaDOs.add(updateSlaDO);
            }
        }
        demandDBHelper.update(updateSlaDOs);

    }

    /**
     * 拉取模版最新快照（批量生成，支持多版本拉取）
     *
     * @param nodeDetailsMap Map<选择了此模版的版本 -> 节点列表>（多版本 -> 每个版本要修改的节点信息）
     * @param versionIdList 快照节点对应的多版本
     * @param templateId 节点对应的模版id
     * @param isSystemCreate 是否系统自动创建
     * @return true-模版节点改变了顺序，false-未改变
     */
    public boolean batchProduceNodeSlas(Map<Long, List<PplInnerApproveNodeDTO>> nodeDetailsMap,
            List<Long> versionIdList, Long templateId, boolean isSystemCreate) {
        if (CollectionUtils.isEmpty(versionIdList)) {
            return false;
        }
        List<PplInnerApproveNodeDTO> nodeTemplateDTOs = getNodesByTemplate(templateId);
        if (CollectionUtils.isEmpty(nodeTemplateDTOs)) {
            throw new BizException("选择的模版未初始化流程配置，请检查模版！" + templateId);
        }
        nodeDetailsMap.values().forEach(nodeDetails -> {
            if (!nodeDetails.stream().map(PplInnerApproveNodeDTO::getNodeCode).distinct().findAny().isPresent()) {
                throw new BizException("节点编码都为空，请传递！");
            }
        });

        boolean isOrderChange = false;

        // 1、生成快照
        List<PplInnerProcessVersionSlaDO> nodeSlaDOList = new ArrayList<>();
        for (Map.Entry<Long, List<PplInnerApproveNodeDTO>> entry : nodeDetailsMap.entrySet()) {
            List<PplInnerApproveNodeDTO> nodeDTOList = entry.getValue();

            int index = 0;
            for (PplInnerApproveNodeDTO nodeTemplateDTO : nodeTemplateDTOs) {
                PplInnerProcessVersionSlaDO slaDO = new PplInnerProcessVersionSlaDO();
                BeanUtils.copyProperties(nodeTemplateDTO, slaDO);
                slaDO.setVersionId(entry.getKey());

                // 请求更改的节点流中，与模版顺序相同的节点，才可初始化快照节点的结束时间，否则需要业务在【审批版本】页面重新配置
                // 场景示例：业务创建模版1，版本1、2、3，版本都选择了模版1，且版本都为待启动状态；
                //                  当业务编辑模版后，版本1、2、3 都会舍弃原节点快照，拉取模版最新数据重新生成节点快照；重新生成之前，会保留原节点快照对应的结束时间 作为 新节点快照的结束时间，目的：节省业务重复配置结束时间的操作
                //                 但如果模版中更改了节点顺序，或者增加了新节点，那么新节点 / 调整顺序的节点及之后的节点 快照的结束时间系统不进行配置，需业务重新在版本编辑页配置
                PplInnerApproveNodeDTO approveNodeDTO = index < nodeDTOList.size() ? nodeDTOList.get(index) : null;
                if (approveNodeDTO != null && nodeTemplateDTO.getNodeCode().equals(approveNodeDTO.getNodeCode())) {
                    slaDO.setDeadlineTime(approveNodeDTO.getDeadlineTime());
                } else {
                    isOrderChange = true;
                    if (!isSystemCreate) {
                        throw new BizException(
                                "您复制的版本关联的模版信息已有更改，请重新选择模版，获取模版最新配置后，再进行创建版本操作！");
                    }
                }

                nodeSlaDOList.add(slaDO);
                index++;
            }
        }
        demandDBHelper.insertBatchWithoutReturnId(nodeSlaDOList);

        // 2、更新模版快照的顺序，即next_sla_id字段, 此处实际为node表id
        Map<Long, PplInnerApproveNodeDTO> nodeTemplateDTOMap = nodeTemplateDTOs.stream()
                .filter(e -> e.getNodeSlaId() != null)
                .collect(Collectors.toMap(PplInnerApproveNodeDTO::getNodeSlaId, v -> v));

        List<PplInnerProcessVersionSlaDO> newSlaDOList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id in (?) ", versionIdList);
        Map<Long, List<PplInnerProcessVersionSlaDO>> newSlaDOByVersionMap = newSlaDOList.stream()
                .collect(Collectors.groupingBy(PplInnerProcessVersionSlaDO::getVersionId));

        List<PplInnerProcessVersionSlaDO> updateSlaDOs = new ArrayList<>();
        for (Map.Entry<Long, List<PplInnerProcessVersionSlaDO>> slaByVersionEntry : newSlaDOByVersionMap.entrySet()) {
            List<PplInnerProcessVersionSlaDO> newSlaDOs = slaByVersionEntry.getValue();
            if (newSlaDOList.size() == 1) {
                continue;
            }
            Map<String, PplInnerProcessVersionSlaDO> newSlaDOMap = newSlaDOs.stream()
                    .collect(Collectors.toMap(PplInnerProcessVersionSlaDO::getNodeCode, v -> v));

            PplInnerProcessVersionSlaDO updateFirstSlaDO = new PplInnerProcessVersionSlaDO();
            updateFirstSlaDO.setId(newSlaDOMap.get(PplInnerProcessNodeEnum.PRE_SUBMIT.getCode()).getId());
            updateFirstSlaDO.setNextSlaId(
                    slaByVersionEntry.getValue().stream().filter(e -> Boolean.TRUE.equals(e.getIsBeginNode())).collect(
                            Collectors.toList()).get(0).getId());
            updateSlaDOs.add(updateFirstSlaDO);

            for (Map.Entry<Long, PplInnerApproveNodeDTO> entry : nodeTemplateDTOMap.entrySet()) {
                PplInnerApproveNodeDTO currentNodeDTO = entry.getValue();
                PplInnerApproveNodeDTO nextNodeDTO = nodeTemplateDTOMap.get(currentNodeDTO.getNextNodeSlaId());
                if (nextNodeDTO == null) {
                    continue;
                }

                PplInnerProcessVersionSlaDO currentSlaDO = newSlaDOMap.get(currentNodeDTO.getNodeCode());
                PplInnerProcessVersionSlaDO nextSlaDO = newSlaDOMap.get(nextNodeDTO.getNodeCode());
                if (currentSlaDO == null || nextSlaDO == null) {
                    continue;
                }

                PplInnerProcessVersionSlaDO updateSlaDO = new PplInnerProcessVersionSlaDO();
                updateSlaDO.setId(currentSlaDO.getId());
                updateSlaDO.setNextSlaId(nextSlaDO.getId());
                updateSlaDOs.add(updateSlaDO);
            }
        }

        demandDBHelper.update(updateSlaDOs);

        return isOrderChange;
    }

    /**
     * 生成模版配置的节点流（批量生成）
     *
     * @param nodeDetails 节点流
     * @param templateId 对应模版
     * @param processId 对应行业+适用产品
     */
    public void batchProduceNodes(Map<String, String> nodeNameByCodeMap, List<PplInnerApproveNodeDTO> nodeDetails,
            Long templateId, Long processId) {
        List<PplInnerProcessNodeDO> newNodeDOList = new ArrayList<>();
//        List<PplInnerApproveNodeDTO> exitNodeDetailList = nodeDetails.stream()
//                .filter(e -> !"ENTER".equals(e.getDeadlineType())).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(exitNodeDetailList)) {
//            throw new BizException("请传入EXIT类型的审批节点配置！");
//        }

        int index = 0;
        for (PplInnerApproveNodeDTO nodeDTO : nodeDetails) {
            PplInnerProcessNodeDO newNodeDO = new PplInnerProcessNodeDO();
            newNodeDO.setProcessId(processId);
            newNodeDO.setTemplateId(templateId);
            newNodeDO.setNodeName(nodeDTO.getNodeName());
            newNodeDO.setApproveRole(nodeDTO.getApproveRole());
            newNodeDO.setRoleAttribute(nodeDTO.getRoleAttribute());
            newNodeDO.setDeadlineAutoPass(nodeDTO.getDeadlineAutoPass());
            newNodeDO.setIsHighlight(nodeDTO.getIsHighlight());
            newNodeDO.setNextWeek(nodeDTO.getNextWeek());
            newNodeDO.setDayOfWeek(nodeDTO.getDayOfWeek());
            newNodeDO.setTime(nodeDTO.getTime());

            newNodeDO.setIsBeginNode(index == 1);
            index++;

            newNodeDO.setNodeCode(nodeNameByCodeMap.getOrDefault(nodeDTO.getNodeName(),
                    UUID.randomUUID().toString().toUpperCase().replaceAll("-", "") + "_AUDIT"));
            newNodeDOList.add(newNodeDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(newNodeDOList);

        // 补充next_id、wait_attribute字段
        // wait_attribute: 存储着下一个节点的role_attribute，若无下一个节点，则存储当前节点的role_attribute
        List<PplInnerProcessNodeDO> updateNodeDOList = new ArrayList<>();
        List<PplInnerProcessNodeDO> newNodeDOs = demandDBHelper.getAll(PplInnerProcessNodeDO.class,
                "where template_id = ? ", templateId);
        for (int i = 0; i < newNodeDOs.size() - 1; i++) {
            PplInnerProcessNodeDO updateNodeDO = new PplInnerProcessNodeDO();
            updateNodeDO.setId(newNodeDOs.get(i).getId());
            updateNodeDO.setNextId(newNodeDOs.get(i + 1).getId());
            updateNodeDO.setWaitAttribute(newNodeDOs.get(i + 1).getRoleAttribute());
            updateNodeDOList.add(updateNodeDO);
        }

        PplInnerProcessNodeDO updateNodeDO = new PplInnerProcessNodeDO();
        updateNodeDO.setId(newNodeDOs.get(newNodeDOs.size() - 1).getId());
        updateNodeDO.setWaitAttribute(newNodeDOs.get(newNodeDOs.size() - 1).getRoleAttribute());
        updateNodeDOList.add(updateNodeDO);

        demandDBHelper.update(updateNodeDOList);
    }


    /**
     * 根据行业id，获取有效的node code & node name (此方法目前主要供生成node_code时，复用node_code使用)
     *
     * @param processId 行业id
     * @return Map<NodeName, NodeCode>
     */
    Map<String, String> getNodeCodeByNameMap(Long processId) {
        Map<String, String> nodeNameByCodeMap = demandDBHelper.getRaw(PplInnerProcessNodeDO.class,
                        "select distinct node_code, node_name from ppl_inner_process_node where process_id = ? and deleted = 0 ",
                        processId)
                .stream().collect(
                        Collectors.toMap(PplInnerProcessNodeDO::getNodeName, PplInnerProcessNodeDO::getNodeCode,
                                (v1, v2) -> v1));
        nodeNameByCodeMap.putAll(demandDBHelper.getRaw(Map.class,
                "select distinct node_name, node_code from ppl_inner_process_version a \n"
                        + "inner join ppl_inner_process_version_sla b on a.id = b.version_id \n"
                        + "where a.deleted = 0 and a.process_id = ? ", processId).stream().collect(
                Collectors.toMap(e -> (String) e.get("node_name"), e -> (String) e.get("node_code"), (e1, e2) -> e1)));
        return nodeNameByCodeMap;
    }

    /**
     * 检查修改审批版本对应节点信息相关传参
     *
     * @param nodeDetails 节点信息
     */
    private void checkNodeEditParam(List<PplInnerApproveNodeDTO> nodeDetails) {
        PplInnerApproveNodeDTO enterNodeDTO = nodeDetails.stream().filter(e -> PplInnerProcessNodeEnum.PRE_SUBMIT.getCode().equals(e.getNodeCode()))
                .collect(Collectors.toList()).get(0);
        if (enterNodeDTO == null) {
            throw new BizException("需求沟通节点缺失，请传递！");
        }

        for (int i = 0; i < nodeDetails.size() - 1; i++) {
            PplInnerApproveNodeDTO currentNode = nodeDetails.get(i);
            PplInnerApproveNodeDTO nextNode = nodeDetails.get(i + 1);
            Date currentNodeDeadline = currentNode.getDeadlineTime();
            Date nextNodeDeadline = nextNode.getDeadlineTime();

            if (currentNodeDeadline == null || nextNodeDeadline == null) {
                throw new BizException("存在缺失【结束时间】的节点，请补充配置！");
            }

            if (currentNodeDeadline.after(nextNodeDeadline)) {
                throw new BizException("上一个节点的结束时间需小于下一个节点的结束时间！");
            }
        }
    }

    /**
     * 检查修改审批版本相关传参
     *
     * @param req 传参
     */
    private void checkTemplateEditParam(PplInnerApproveTemplateDetailDTO req) {
        if (req.getProcessId() == null) {
            throw new BizException("行业id为空，请传递行业id！");
        }
        if (StringUtils.isEmpty(req.getTemplateName())) {
            throw new BizException("模版名称为空，请填写！");
        }
        List<PplInnerApproveNodeDTO> nodeDTOs = req.getNodeDetails();
        if (CollectionUtils.isEmpty(nodeDTOs)) {
            throw new BizException("节点信息为空，请填写节点相关信息！");
        }

        if (nodeDTOs.size() != nodeDTOs.stream().map(PplInnerApproveNodeDTO::getNodeName).distinct().count()) {
            throw new BizException("此模版存在重复节点名称 /  部分节点缺失名称 ，请修改！");
        }

        Map<String, Integer> compareMap = new HashMap<>();
        compareMap.put(PplInnerProcessAttributeEnum.CUSTOMER.getCode(), 1);
        compareMap.put(PplInnerProcessAttributeEnum.WAR_ZONE.getCode(), 2);
        compareMap.put(PplInnerProcessAttributeEnum.DEPT.getCode(), 3);

        List<PplInnerApproveNodeDTO> exitNodeDetailList = req.getNodeDetails().stream()
                .filter(e -> !"ENTER".equals(e.getDeadlineType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(exitNodeDetailList)) {
            throw new BizException("请传入EXIT类型的审批节点配置！");
        }
        for (int i = 0; i < exitNodeDetailList.size() - 1; i++) {
            PplInnerApproveNodeDTO nodeDTO = exitNodeDetailList.get(i);
            PplInnerApproveNodeDTO nextNodeDTO = exitNodeDetailList.get(i + 1);
            String roleAttribute = nodeDTO.getRoleAttribute();
            String nextRoleAttribute = nextNodeDTO.getRoleAttribute();
            if (StringUtils.isEmpty(roleAttribute) || StringUtils.isEmpty(nextRoleAttribute)) {
                throw new BizException("存在缺失【审批分组】的节点，请补充配置！");
            }
            if (compareMap.get(roleAttribute) == null || compareMap.get(nextRoleAttribute) == null) {
                throw new BizException("请传入正确的分组！联系erickssu检查");
            }
            if (compareMap.get(roleAttribute) > compareMap.get(nextRoleAttribute)) {
                throw new BizException("请按【客户 -> 战区 -> 部门】从小到大的顺序配置节点的审批分组");
            }
            if (StringUtils.isEmpty(nodeDTO.getApproveRole()) || StringUtils.isEmpty(nextNodeDTO.getApproveRole())) {
                throw new BizException("存在缺失【审批角色】的节点，请补充配置！");
            }

            if (StringUtils.isEmpty(nodeDTO.getDeadlineAutoPass())) {
                nodeDTO.setDeadlineAutoPass(PplInnerProcessNodeDeadlineAutoPassEnum.CLOSE.getCode());
            }
            if (nodeDTO.getIsHighlight() == null) {
                nodeDTO.setIsHighlight(Boolean.FALSE);
            }

            if (StringUtils.isEmpty(nextNodeDTO.getDeadlineAutoPass())) {
                nextNodeDTO.setDeadlineAutoPass(PplInnerProcessNodeDeadlineAutoPassEnum.CLOSE.getCode());
            }
            if (nextNodeDTO.getIsHighlight() == null) {
                nextNodeDTO.setIsHighlight(Boolean.FALSE);
            }
        }

        if (!PplInnerProcessAttributeEnum.DEPT.getCode()
                .equals(exitNodeDetailList.get(exitNodeDetailList.size() - 1).getRoleAttribute())) {
            throw new BizException("最后一个节点的分组必须配置为【行业】，请重新选择！");
        }

    }

    public static Date addDays(Date date, Long days) {
        Long addTime = days * 24 * 60 * 60 * 1000;
        date.setTime(date.getTime() + addTime);
        return date;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void addProductVersion(long processId, String product) {
        // 1，获取 ppl_inner_process 表数据，复制生成 指定产品的 ppl_inner_process 表数据
        PplInnerProcessDO normalProcess = demandDBHelper.getOne(PplInnerProcessDO.class,
                "where id = ?", processId);
        if (normalProcess == null) {
            throw new BizException("未找到对应的ppl_inner_process数据，请检查！id：" + processId);
        }
        PplInnerProcessDO newProcess = new PplInnerProcessDO();
        BeanUtils.copyProperties(normalProcess, newProcess);
        newProcess.setId(null);
        newProcess.setUpdateTime(null);
        newProcess.setCreateTime(null);
        newProcess.setProduct(product);
        demandDBHelper.insert(newProcess);

        // 2，获取 ppl_inner_process_template 表数据，复制生成 指定产品的 ppl_inner_process_template 表数据
        PplInnerProcessTemplateDO normalTemplate = demandDBHelper.getOne(PplInnerProcessTemplateDO.class,
                "where process_id = ?", processId);
        if (normalTemplate == null) {
            throw new BizException("未找到对应的ppl_inner_process_template数据，请检查！process_id：" + processId);
        }
        PplInnerProcessTemplateDO newTemplate = new PplInnerProcessTemplateDO();
        newTemplate.setTemplateCreator("system");
        newTemplate.setProcessId(newProcess.getId());
        newTemplate.setTemplateType(normalTemplate.getTemplateType());
        newTemplate.setTemplate(normalTemplate.getTemplate() + product);
        demandDBHelper.insert(newTemplate);

        // 3，获取 ppl_inner_process_node 表数据，复制生成 指定产品的 ppl_inner_process_node 表数据
        List<PplInnerProcessNodeDO> normalNodeList = demandDBHelper.getAll(PplInnerProcessNodeDO.class,
                "where template_id = ?", normalTemplate.getId());
        if (CollectionUtils.isEmpty(normalNodeList)) {
            throw new BizException("未找到对应的ppl_inner_process_node数据，请检查！template_id：" + normalTemplate.getId());
        }
        LinkedList<PplInnerProcessNodeDO> newNodeList = new LinkedList<>();
        // 节点链
        LinkedList<PplInnerProcessNodeDO> normalNodeChain = ListUtils2.sortChain(normalNodeList,
                PplInnerProcessNodeDO::getIsBeginNode,
                PplInnerProcessNodeDO::getNextId, BaseDO::getId);
        // 反向操作节点链，进行数据写入，方便写入 nextId
        for (int index = normalNodeChain.size() - 1; index >= 0; index--) {
            PplInnerProcessNodeDO normalNode = normalNodeChain.get(index);
            PplInnerProcessNodeDO newNode = new PplInnerProcessNodeDO();
            BeanUtils.copyProperties(normalNode, newNode);
            newNode.setId(null);
            newNode.setUpdateTime(null);
            newNode.setCreateTime(null);
            newNode.setProcessId(newProcess.getId());
            newNode.setTemplateId(newTemplate.getId());
            if (index < normalNodeChain.size() - 1) {
                newNode.setNextId(newNodeList.getFirst().getId());
            } else {
                newNode.setNextId(null);
            }
            demandDBHelper.insert(newNode);
            newNodeList.addFirst(newNode);
        }
        // 4，获取 ppl_inner_process_version 表数据，复制生成 指定产品的 ppl_inner_process_version 表数据
        PplInnerProcessVersionDO normalVersion = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "where process_id = ? and status = 'processing'", processId);
        if (normalVersion == null) {
            throw new BizException("未找到对应进行中的ppl_inner_process_version数据，请检查！process_id：" + processId);
        }
        PplInnerProcessVersionDO newVersion = new PplInnerProcessVersionDO();
        BeanUtils.copyProperties(normalVersion, newVersion);
        newVersion.setId(null);
        newVersion.setUpdateTime(null);
        newVersion.setCreateTime(null);
        newVersion.setTemplateId(newTemplate.getId());
        newVersion.setIsStartConsensus(PplInnerProcessVersionStartConsensusEnum.NOT_CONSENSUS.getCode());
        newVersion.setProcessId(newProcess.getId());
        newVersion.setConsumeTime(null);
        newVersion.setConsumeStatus(0);
        newVersion.setProduct(product);
        demandDBHelper.insert(newVersion);

        // 5，获取 ppl_inner_process_version_sla 表数据，复制生成 指定产品的 ppl_inner_process_version_sla 表数据
        List<PplInnerProcessVersionSlaDO> normalSlaList = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                "where version_id = ?", normalVersion.getId());
        if (CollectionUtils.isEmpty(normalSlaList)) {
            throw new BizException("未找到对应的ppl_inner_process_version_sla数据，请检查！version_id：" + normalVersion.getId());
        }
        // 节点链
        LinkedList<PplInnerProcessVersionSlaDO> normalSlaNodeChain = ListUtils2.sortChain(normalSlaList,
                PplInnerProcessVersionSlaDO::getIsBeginNode,
                PplInnerProcessVersionSlaDO::getNextSlaId, PplInnerProcessVersionSlaDO::getId);
        LinkedList<PplInnerProcessVersionSlaDO> newSlaNodeChain = new LinkedList<>();
        // 反向操作节点链，进行数据写入，方便写入 nextId
        for (int index = normalSlaNodeChain.size() - 1; index >= 0; index--) {
            PplInnerProcessVersionSlaDO normalSla = normalSlaNodeChain.get(index);
            PplInnerProcessVersionSlaDO newSla = new PplInnerProcessVersionSlaDO();
            BeanUtils.copyProperties(normalSla, newSla);
            newSla.setId(null);
            newSla.setVersionId(newVersion.getId());
            if (index < normalSlaNodeChain.size() - 1) {
                newSla.setNextSlaId(newSlaNodeChain.getFirst().getId());
            } else {
                newSla.setNextSlaId(null);
            }
            demandDBHelper.insert(newSla);
            newSlaNodeChain.addFirst(newSla);
        }

    }

    /**
     * 配置了自动创建版本的行业产品，自动开启版本。
     */
    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-autoCreateVersion",keyScript = "args[0]",waitLockMillisecond = 0, throwExceptionIfNotGetLock = false)
    public void autoCreateVersion(String versionCode) {

        UnificatedVersionDTO unificatedVersionDTO = demandDBHelper.getOne(UnificatedVersionDTO.class, "where version_code = ?",
                versionCode);
        unificatedVersionDTO.parseJsonDemand();
        List<PplInnerProcessDO> processDOList = demandDBHelper.getAll(PplInnerProcessDO.class,
                "where is_auto_create_version = 1");
        if (ListUtils.isEmpty(processDOList)){
            // 如果没有自动创建的流程，直接返回
            return;
        }
        List<PplInnerProcessVersionDO> existVersionList = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where version_code = ?", unificatedVersionDTO.getVersionCode());
        List<Long> existVersionProcessIdList = ListUtils.isEmpty(existVersionList) ?
                new ArrayList<>() : existVersionList.stream().map(PplInnerProcessVersionDO::getProcessId).collect(Collectors.toList());

        List<PplInnerProcessDO> filter = processDOList.stream()
                .filter(v -> !existVersionProcessIdList.contains(v.getId())).collect(Collectors.toList());

        if (ListUtils.isEmpty(filter)){
            // 如果没有需要生成的直接返回
            return;
        }

        UnificatedVersionEventDO event = unificatedVersionDTO.getEventByCode(
                CrpEventEnum.VERSION_START_TIME.getCode());

        CrpCommonHolidayWeekDO currentWeek = queryCurrentWeek(
                cloud.demand.app.common.utils.DateUtils.dateToLocalDate(event.getDeadline()));
        String templateType = unificatedVersionDTO.getIsLastMonthWeek() ? "month" : "week";
        List<PplProcessTemplateVO> templateList = demandDBHelper.getAll(PplProcessTemplateVO.class,
                "where process_id in (?)", filter.stream().map(PplInnerProcessDO::getId).collect(Collectors.toList()));

        List<PplInnerProcessVersionDO> newVersionList = new ArrayList<>();
        List<PplInnerProcessVersionSlaDO> slaList = new ArrayList<>();
        for (PplInnerProcessDO processDO : filter) {

            PplProcessTemplateVO templateVO = getTemplate(templateList, processDO.getId(), templateType);
            // 构建version
            PplInnerProcessVersionDO versionDO = initVersionByTemplate(processDO, unificatedVersionDTO);
            versionDO.setTemplateId(templateVO.getId());
            demandDBHelper.insert(versionDO);
            newVersionList.add(versionDO);
            // 构建sla
            for (PplInnerProcessNodeDO nodeDO : templateVO.getNodeDOList()) {
                PplInnerProcessVersionSlaDO slaDO = new PplInnerProcessVersionSlaDO();
                BeanUtils.copyProperties(nodeDO,slaDO);
                slaDO.setId(null);
                slaDO.setVersionId(versionDO.getId());
                slaDO.setDeadlineTime(dictService.getLocalDateByTimeConfig(currentWeek,nodeDO.getNextWeek(),nodeDO.getDayOfWeek(),nodeDO.getTime()));
                slaList.add(slaDO);
            }
        }
        demandDBHelper.insertBatchWithoutReturnId(slaList);

        List<PplInnerProcessVersionSlaDO> updateList = new ArrayList<>();
        for (PplInnerProcessVersionDO pplInnerProcessVersionDO : newVersionList) {
            List<PplInnerProcessVersionSlaDO> all = demandDBHelper.getAll(PplInnerProcessVersionSlaDO.class,
                    "where version_id = ?", pplInnerProcessVersionDO.getId());
            for (int i = 0; i < all.size(); i++) {
                PplInnerProcessVersionSlaDO slaDO = all.get(i);
                slaDO.setDeadlineType(i == 0 ? "ENTER" : "EXIT");
                if (i < all.size()-1){
                    // 最后一个节点不需要设置nextId
                    slaDO.setNextSlaId(all.get(i+1).getId());
                }
            }
            updateList.addAll(all);
        }
        demandDBHelper.update(updateList);

    }

    @Override
    @TaskRunSql(namespace = "PPL", nameScript = "'autoCreateVersionTask'")
    @TaskLog(taskName = "autoCreateVersionTask")
    public void autoCreateVersionTask() {

        List<UnificatedVersionDO> all = demandDBHelper.getAll(UnificatedVersionDO.class, "where status = ? order by id asc",
                UnificatedVersionStatusEnum.NEW.getCode());

        List<Long> idList = demandDBHelper.getRaw(Long.class,
                "select id from ppl_inner_process where deleted = 0 and is_auto_create_version = ?", 1);
        List<String> existVersionCode = demandDBHelper.getRaw(String.class,
                "select distinct version_code"
                        + " from ppl_inner_process_version where deleted = 0 and process_id in (?)", idList);

        PplInnerVersionService innerVersionService = SpringUtil.getBean(PplInnerVersionService.class);

        for (int i = 0; i < all.size(); i++) {
            if (i == 3){
                // 最多只创建3个行业版本
                break;
            }
            if (existVersionCode.contains(all.get(i).getVersionCode())) {
                continue;
            }else {
                innerVersionService.autoCreateVersion(all.get(i).getVersionCode());
            }
        }
    }

    public PplInnerProcessVersionDO initVersionByTemplate(PplInnerProcessDO processDO,
            UnificatedVersionDTO unificatedVersionDTO){

        PplInnerProcessVersionDO versionDO = new PplInnerProcessVersionDO();
        versionDO.setDemandBeginYear(unificatedVersionDTO.getPplDemand().getBeginYear());
        versionDO.setDemandBeginMonth(unificatedVersionDTO.getPplDemand().getBeginMonth());
        versionDO.setDemandEndYear(unificatedVersionDTO.getPplDemand().getEndYear());
        versionDO.setDemandEndMonth(unificatedVersionDTO.getPplDemand().getEndMonth());
        versionDO.setOverseasDemandBeginYear(unificatedVersionDTO.getPplDemand().getOverseasBeginYear());
        versionDO.setOverseasDemandBeginMonth(unificatedVersionDTO.getPplDemand().getOverseasBeginMonth());

        if (IndustryDeptEnum.INNER_DEPT.getName().equals(processDO.getIndustryDept())){
            // 如果是内部业务部 结束年月 往后推一个月
            if (versionDO.getDemandEndMonth() == 12){
                versionDO.setDemandEndYear(versionDO.getDemandEndYear() + 1);
                versionDO.setDemandEndMonth(1);
            }else {
                versionDO.setDemandEndMonth(versionDO.getDemandEndMonth() + 1);
            }
        }



        versionDO.setStatus(PplInnerProcessVersionStatusEnum.NEW.getCode());
        versionDO.setIndustryDept(processDO.getIndustryDept());
        versionDO.setProduct(processDO.getProduct());
        versionDO.setProcessId(processDO.getId());
        versionDO.setVersionCreator("system");
        versionDO.setVersionUpdater("system");
        versionDO.setVersionCode(unificatedVersionDTO.getVersionCode());
        versionDO.setConsumeStatus(0);


        return versionDO;

    }

    public PplProcessTemplateVO getTemplate(List<PplProcessTemplateVO> templateList,Long processId,String templateType){
        List<PplProcessTemplateVO> templateVOList = templateList.stream()
                .filter(v -> v.getProcessId().equals(processId))
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(templateVOList)){
            throw new BizException("该流程缺失模版配置, processId: " + processId);
        }

        List<PplProcessTemplateVO> templateTypeList = templateVOList.stream()
                .filter(v -> v.getTemplateType().equals(templateType)).collect(Collectors.toList());
        if (ListUtils.isNotEmpty(templateTypeList)){
            return templateTypeList.get(0);
        }else {
            return templateVOList.get(0);
        }


    }

    public CrpCommonHolidayWeekDO queryCurrentWeek(LocalDate localDate) {
        CrpCommonHolidayWeekDO one = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class,
                "where start <= ? and end >= ? ", localDate.toString(), localDate.toString());
        return one;
    }

}
