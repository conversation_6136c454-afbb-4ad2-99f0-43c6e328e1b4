package cloud.demand.app.modules.p2p.ppl13week.entity;


import cloud.demand.app.common.utils.ObjUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.json.JSON;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class Ppl13weekGpuSupplyImportExcelDTO {

    @ExcelProperty(index = 0, value = "ppl-id")
    private String pplId;

    @ExcelProperty(index = 1, value = "满足方式")
    private String matchTypeName;

    @ExcelProperty(index = 2, value = "满足可用区")
    private String zoneName;

    @ExcelProperty(index = 3, value = "实例规格")
    private String instanceModel;

    @ExcelProperty(index = 4, value = "gpu 卡数")
    private BigDecimal gpuNum;

    @ExcelProperty(index = 5, value = "满足母机类型")
    private String hostType;

    @ExcelProperty(index = 6, value = "满足母机台数")
    private BigDecimal hostNum;

    @ExcelProperty(index = 7, value = "原因")
    private String remark;

    // 满足实例数
    private Integer instanceNum;

    // 满足核心数
    private Integer cpuNum;

    public static  List<Ppl13weekGpuSupplyImportExcelDTO> decode(MultipartFile file) throws IOException {

        List<Ppl13weekGpuSupplyImportExcelDTO> ret = Lang.list();
        EasyExcel.read(
                file.getInputStream(), Ppl13weekGpuSupplyImportExcelDTO.class,
                new AnalysisEventListener<Ppl13weekGpuSupplyImportExcelDTO>() {
                    @Override
                    public void invoke(Ppl13weekGpuSupplyImportExcelDTO o, AnalysisContext analysisContext) {
                        if (ObjUtils.allFieldIsNull(o)) {
                            log.info("读到第一个空行，结束");
                            throw new ExcelAnalysisStopException();
                        }
                        ret.add(JSON.clone(o));
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        log.info("decode over");
                    }
                }
        ).sheet(0).headRowNumber(1).doRead();
        return ret;
    }


}
