package cloud.demand.app.modules.p2p.ppl13week.controller;


import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.job.Ppl13WeekRelevantTask;
import cloud.demand.app.modules.p2p.ppl13week.service.UnificatedVersionService;
import cloud.demand.app.modules.rrp_new_feature.service.W13ProductAcceptIndustryService;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.service.VersionService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.BizException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;

@JsonrpcController("/ppl13week")
@Slf4j
public class UnificatedVersionController {

    @Resource
    private UnificatedVersionService unificatedVersionService;

    @Resource
    private Ppl13WeekRelevantTask ppl13WeekRelevantTask;

    @Resource
    private W13ProductAcceptIndustryService w13ProductAcceptIndustryService;

    @Resource
    private VersionService versionService;

    @Resource
    private DBHelper demandDBHelper;

    @RequestMapping
    public Object createUnificatedVersion() {
        unificatedVersionService.createUnificatedVersion();
        return ImmutableMap.of("rs", "ok");
    }

    @RequestMapping
    public Object deleteUnificatedVersion(@JsonrpcParam UnificatedVersionDTO req) {
        unificatedVersionService.deleteUnificatedVersion(req.getId());
        return ImmutableMap.of("rs", "ok");
    }

    @RequestMapping
    public Object queryUnificatedVersionList(@JsonrpcParam UnificatedVersionReq req) {
        return unificatedVersionService.queryUnificatedVersionList(req);
    }

    @RequestMapping
    public Object saveUnificatedVersion(@JsonrpcParam UnificatedVersionDTO req) {
        unificatedVersionService.saveUnificatedVersion(req);
        return ImmutableMap.of("rs", "ok");

    }

    @RequestMapping
    public Object unificatedVersionScheduled() throws InterruptedException {
        ppl13WeekRelevantTask.unificatedVersionScheduled();
        return ImmutableMap.of("rs", "ok");

    }

    @RequestMapping
    public Object firstInitUnificatedVersion() {
        unificatedVersionService.firstInit();
        return ImmutableMap.of("rs", "ok");

    }

    @RequestMapping
    public Object queryUnificatedVersionProgress(@JsonrpcParam UnificatedVersionReq req) {
        if (req.getId() == null) {
            throw new BizException("确实版本id");
        }
        return unificatedVersionService.queryUnificatedVersionProgress(req.getId());
    }

    private Integer longToInteger(long value) {
        if (value >= Integer.MIN_VALUE && value <= Integer.MAX_VALUE) {
            return (int) value;
        } else {
            throw new BizException("The long value is out of range for an Integer.");
        }
    }

    @RequestMapping
    public Object syncIndustryDemand(@JsonrpcParam IDDto id) {
        if (id == null) {
            throw new BizException("统一大版本id不能为空");
        }
        UnificatedVersionDTO unificatedVersionDTO = unificatedVersionService.getUnificatedVersionDTOById(id.getId());
        if (unificatedVersionDTO == null) {
            throw new BizException(String.format("找不到id为【%s】的统一大版本", id));
        }
        if (StringUtils.isBlank(unificatedVersionDTO.getVersionCode())) {
            throw new BizException(String.format("id为【%s】的统一大版本的版本编码为空", id));
        }
        if (UnificatedVersionStatusEnum.DONE.getCode().equals(unificatedVersionDTO.getStatus())) {
            throw new BizException("版本已经关闭，不允许操作");
        }
        RrpConfigDO rrpConfig = versionService.getRrpConfig(unificatedVersionDTO.getVersionCode());
        if (rrpConfig == null) {
            throw new BizException(
                    String.format("找不到版本号为【%s】的产品13周版本", unificatedVersionDTO.getVersionCode()));
        }
        for (UnificatedVersionEventDO eventDO : unificatedVersionDTO.getEventList()) {
            if (CrpEventEnum.STOCK_SUPPLY_DONE.getCode().equals(eventDO.getEventCode()) && !eventDO.getIsDone()) {
                throw new BizException("行业13周数据的尚未对冲完成");
            }
        }
        w13ProductAcceptIndustryService.receivePythonModifyIndustryVersionSignal(longToInteger(rrpConfig.getId()),
                rrpConfig.getIndustryVersion());
        return ImmutableMap.of("rs", "ok");
    }

    @RequestMapping
    public Object forceCloseUnificatedVersion(@JsonrpcParam UnificatedVersionReq req) throws InterruptedException {
        if (req.getId() == null) {
            throw new BizException("缺失版本id");
        }
        unificatedVersionService.forceCloseUnificatedVersion(req.getId());
        return ImmutableMap.of("rs", "ok");
    }

    @RequestMapping
    public Object versionStartStockSupply() throws InterruptedException {
        UnificatedVersionDTO unificatedVersionDTO = demandDBHelper.getOne(UnificatedVersionDTO.class,
                "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
        if (unificatedVersionDTO == null) {
            return ImmutableMap.of("rs", "fail");
        }
        unificatedVersionService.startStockSupply(
                unificatedVersionDTO,unificatedVersionDTO.getEventByCode(CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode()));
        return ImmutableMap.of("rs", "ok");
    }

    @Data
    static class IDDto{

        private Long id;

    }

}
