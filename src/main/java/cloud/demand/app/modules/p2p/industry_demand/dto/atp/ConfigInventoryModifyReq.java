package cloud.demand.app.modules.p2p.industry_demand.dto.atp;

import java.time.YearMonth;
import lombok.Data;

@Data
public class ConfigInventoryModifyReq {

    private Long id;

    private String region;

    private String instanceType;

    private YearMonth dataYearMonth;

    /** 结余库存 */
    private Integer surplusInventoryCore;

    /** 配置库存 */
    private Integer configInventoryCore;

    private String configRemark;

    /**
     * true：使用配置库存 <br/>
     * false：使用结余库存
     */
    private boolean useConfigInventory;

}
