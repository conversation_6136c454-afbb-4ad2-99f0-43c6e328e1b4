package cloud.demand.app.modules.p2p.ppl13week.service.excel;


import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.ppl13week.controller.PplVersionGroupController.YearMonthDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import com.pugwoo.wooutils.json.JSON;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class PplExcelParseServiceAdapter {

    private List<PplExcelParseService> handlers = new ArrayList<>();

    public void registry(PplExcelParseService service) {
        handlers.add(service);
    }

    public PplItemImportRsp execute(MultipartFile file, Map<String, String> params) {
        if (params == null || params.get("params") == null) {
            throw new BizException("参数为空");
        }

        YearMonthDTO params1 = JSON.parse(params.get("params"), YearMonthDTO.class);

        String startYearMonth = params1.getStartYearMonth();
        String endYearMonth = params1.getEndYearMonth();
        String product = params1.getProduct();

        if (Strings.isBlank(startYearMonth)) {
            throw new BizException("startYearMonth 为空");
        }
        if (Strings.isBlank(endYearMonth)) {
            throw new BizException("endYearMonth为空");
        }
        if (Strings.isBlank(product)) {
            throw BizException.makeThrow("product 为空");
        }

        YearMonth start = DateUtils.parse(startYearMonth);
        YearMonth end = DateUtils.parse(endYearMonth);
        if (start == null || end == null) {
            throw BizException.makeThrow("版本日期范围解析出错");
        }

        PplExcelParseService parseService = handlers.stream().filter(v -> v.support(product)).findFirst().orElse(null);
        if (parseService == null) {
            throw BizException.makeThrow("未找到该产品类型的执行器");
        }
        return parseService.execute(file, start, end, product);
    }


}
