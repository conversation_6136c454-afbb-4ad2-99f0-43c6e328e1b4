package cloud.demand.app.modules.p2p.industry_demand.controller;

import cloud.demand.app.modules.p2p.industry_demand.dto.atp.ConfigInventoryModifyReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.CountryThresholdReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.IndustryQueryReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.IndustryQueryResp;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.QueryListReq;
import cloud.demand.app.modules.p2p.industry_demand.dto.atp.ReasonReq;
import cloud.demand.app.modules.p2p.industry_demand.entity.AtpIndustryDataDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.AtpIndustrySyncVersionDO;
import cloud.demand.app.modules.p2p.industry_demand.service.AtpIndustryDataService;
import com.pugwoo.wooutils.collect.MapUtils;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

@JsonrpcController("/industry/atp")
public class AtpIndustryDataController {

    @Resource
    private AtpIndustryDataService atpIndustryDataService;

    /**
     * 修改配置库存
     * @return 修改后的数据
     */
    @RequestMapping
    public AtpIndustryDataDO modifyConfigInventory(@JsonrpcParam ConfigInventoryModifyReq req) {
        return atpIndustryDataService.modifyConfigInventory(req);
    }

    /**
     *  是否有ATP配置权限
     */
    @RequestMapping
    public boolean hasConfigAuth() {
        return atpIndustryDataService.hasConfigAuth();
    }

    /**
     *  “行业ATP咨询人”，用于在查询页面引导用户咨询。
     */
    @RequestMapping
    public List<String> queryIndustryAtpConsultant(@JsonrpcParam IndustryQueryReq req) {
        return atpIndustryDataService.queryIndustryAtpConsultant(req.getIndustryDept());
    }

    /**
     * 查询ATP数据
     */
    @RequestMapping
    public List<AtpIndustryDataDO> queryList(@JsonrpcParam QueryListReq req) {
        return atpIndustryDataService.queryList(req);
    }

    /**
     * 查询ATP数据同步版本信息
     */
    @RequestMapping
    public AtpIndustrySyncVersionDO getCurrentSyncVersion() {
        return atpIndustryDataService.getCurrentSyncVersion();
    }

    /**
     * 行业供应能力查询
     */
    @RequestMapping
    public IndustryQueryResp queryForIndustry(@JsonrpcParam IndustryQueryReq req) {
        return atpIndustryDataService.queryForIndustry(req);
    }

    /**
     * 查询原因列表
     */
    @RequestMapping
    public List<String> queryReasonList() {
        return atpIndustryDataService.queryReasonList();
    }

    /**
     * 添加查询原因
     */
    @RequestMapping
    public Object addQueryReasons(@JsonrpcParam ReasonReq req) {
        atpIndustryDataService.addQueryReasons(req.getReasonList());
        return MapUtils.of("result", "ok");
    }


    /**
     * 设置查询原因
     */
    @RequestMapping
    public Object setQueryReasonList(@JsonrpcParam ReasonReq req) {
        atpIndustryDataService.setQueryReasonList(req.getReasonList());
        return MapUtils.of("result", "ok");
    }

    /**
     * 国家需求阈值设置
     */
    @RequestMapping
    public Object modifyInDemandThreshold(@JsonrpcParam CountryThresholdReq req) {
        atpIndustryDataService.modifyInDemandThreshold(req);
        return MapUtils.of("result", "ok");
    }


}
