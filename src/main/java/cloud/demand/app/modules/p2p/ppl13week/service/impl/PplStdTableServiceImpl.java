package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.order.dto.OrderConsensusDemandDetailJoinOrderVO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.OrderActualBuyDTO;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.p2p.industry_demand.dto.dict.RegionDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderExportJsonDTO.OrderItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchSummaryDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.StdYunXiaoMatchSummaryDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpConsensusOrderCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplJoinOrderVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplOrderItemAndInfoCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplYunxiaoApplyItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdYunxiaoApplyOrderDetailCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersion532CfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionBaseCfDo;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplJoinOrderVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplJoinOrderVersionNewestCfDO.YearMonthVersionCode;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.OrderItemAndInfoVO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.SystemSyncTableLogDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplJoinOrderVersionItemDataSource;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderCategoryEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStdTableService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerService;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwsCrpPplItemVersion532CfVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwsCrpPplItemVersionBaseCfVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.PplItemApplyStdTableVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.PplItemStdTableVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.PplItemVersionStdTableVO;
import cloud.demand.app.modules.report_proxy.anno.TaskRunSql;
import cn.hutool.core.date.DatePattern;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.redis.Synchronized;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class PplStdTableServiceImpl implements PplStdTableService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private FillerService fillerService;
    @Resource
    private DBHelper ckcldStdCrpSwapDBHelper;
    @Resource
    private YunxiaoAPIService yunxiaoAPIService;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DictService dictService;
    @Resource
    private PplDictService pplDictService;

    @SneakyThrows
    @Override
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncPplItemLatestToCkStdTable", timeout = 60 * 2)
    public void syncPplItemLatestToCkStdTable() {
        String sql = IOUtils.readClasspathResourceAsString("/sql/ppl13week/std_table/ppl_latest.sql");
        List<PplItemStdTableVO> all = demandDBHelper.getRaw(PplItemStdTableVO.class, sql);

        List<RegionDTO> regionDTOS = industryDemandDictService.listAllRegion();
        Map<String, String> regionToLanMap = ListUtils.toMap(regionDTOS, RegionDTO::getRegionShortChName, RegionDTO::getLand);

        List<DwdCrpPplItemCfDO> result = ListUtils.transform(all, vo -> PplItemStdTableVO.transTo(vo, regionToLanMap));
        if (ListUtils.isEmpty(result)) {
            log.error("syncPplItemLatestToCkStdTable result is empty, ignore sync to ck");
            return;
        }

        fillerService.fill(result);
        // 删除备份库数据
        ckcldStdCrpSwapDBHelper.executeRaw(
                "TRUNCATE TABLE std_crp_swap.dwd_crp_ppl_item_cf_local ON CLUSTER default_cluster");
        // 备份库进行新数据插入
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);

        String countSql = " select count(1) from std_crp_swap.dwd_crp_ppl_item_cf";
        // 每隔3秒钟检查一次，如果10次都没达到预期，则放弃
        waitWrite(10, 3, countSql, ckcldStdCrpSwapDBHelper, result.size());
        // 重命名表，实现将备份库表数据和主库表数据对调
        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dwd_crp_ppl_item_cf_local to std_crp_swap.dwd_crp_ppl_item_cf_local_tmp,\n"
                + "std_crp_swap.dwd_crp_ppl_item_cf_local to std_crp.dwd_crp_ppl_item_cf_local,\n"
                + "std_crp_swap.dwd_crp_ppl_item_cf_local_tmp to std_crp_swap.dwd_crp_ppl_item_cf_local\n"
                + "on cluster default_cluster");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_crp_ppl_item_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncPplItemVersionToCkStdTable", timeout = 60 * 5)
    @TaskRunSql(namespace = "PPL", nameScript = "'syncPplItemVersionToCkStdTable'",
            keyScript = "java.time.LocalDate.now()", ignoreTable = "std_crp.system_sync_table_log")
    public void syncPplItemVersionToCkStdTable() {
        String sql = IOUtils.readClasspathResourceAsString("/sql/ppl13week/std_table/ppl_version_latest.sql");
        List<PplItemVersionStdTableVO> all = demandDBHelper.getRaw(PplItemVersionStdTableVO.class, sql);

        List<RegionDTO> regionDTOS = industryDemandDictService.listAllRegion();
        Map<String, String> regionToLanMap = ListUtils
                .toMap(regionDTOS, RegionDTO::getRegionShortChName, RegionDTO::getLand);

        List<DwdCrpPplItemVersionCfDO> result = ListUtils.transform(
                all, vo -> PplItemVersionStdTableVO.transTo(vo, regionToLanMap));
        if (ListUtils.isEmpty(result)) {
            log.error("syncPplItemVersionToCkStdTable result is empty, ignore sync to ck");
            return;
        }

        fillerService.fill(result);
        ckcldStdCrpSwapDBHelper.executeRaw(
                "TRUNCATE TABLE std_crp_swap.dwd_crp_ppl_item_version_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);

        String countSql = " select count(1) from std_crp_swap.dwd_crp_ppl_item_version_cf";
        // 每隔2秒钟检查一次，如果15次都没达到预期，则放弃
        waitWrite(15, 2, countSql, ckcldStdCrpSwapDBHelper, result.size());
        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dwd_crp_ppl_item_version_cf_local to std_crp_swap.dwd_crp_ppl_item_version_cf_local_tmp,\n"
                + "std_crp_swap.dwd_crp_ppl_item_version_cf_local to std_crp.dwd_crp_ppl_item_version_cf_local,\n"
                + "std_crp_swap.dwd_crp_ppl_item_version_cf_local_tmp to std_crp_swap.dwd_crp_ppl_item_version_cf_local\n"
                + "on cluster default_cluster");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_crp_ppl_item_version_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    private void waitWrite(int times, int sleepSeconds, String countSql, DBHelper dbHelper, int count)
            throws InterruptedException {
        boolean isOk = false;
        int trueCount = 0;
        int allSleepSeconds = times * sleepSeconds;
        while (times > 0) {
            Thread.sleep(sleepSeconds * 1000L);
            trueCount = dbHelper.getRawOne(Integer.class, countSql);
            if (trueCount == count) {
                isOk = true;
                break;
            }
            times--;
        }
        if (!isOk) {
            throw BizException.makeThrow("检查数据写入量失败，等待时长【%s】秒，应写入量【%s】，实际写入量【%s】，实际写入量检查sql【%s】",
                    allSleepSeconds, count, trueCount, countSql);
        }
    }

    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncOrderItemAndInfoToCkStdTable", timeout = 60 * 2)
    @Override
    public void syncOrderItemAndInfoToCkStdTable() {
        String sql = ORMUtils.getSql("/sql/ppl13week/std_table/ppl_order_latest.sql");
        List<OrderItemAndInfoVO> all = demandDBHelper.getRaw(OrderItemAndInfoVO.class, sql);
        Map<String, StaticZoneDO> zones = dictService.getAllPlanZoneInfosGroupByName();
        List<DwdCrpPplOrderItemAndInfoCfDO> result = ListUtils.transform(all,
                o -> OrderItemAndInfoVO.transTo(o, zones));
        fillerService.fill(result);
        Map<String, BigDecimal> orderTotalCoreMap = new HashMap<>();
        for (DwdCrpPplOrderItemAndInfoCfDO item : result) {
            BigDecimal totalCore = orderTotalCoreMap.get(item.getOrderNumber());
            if (totalCore == null) {
                totalCore = BigDecimal.ZERO;
            }
            if (item.getTotalCore() != null) {
                totalCore = totalCore.add(new BigDecimal(item.getTotalCore()));
            }
            orderTotalCoreMap.put(item.getOrderNumber(), totalCore);
        }
        // 填充订单满足计算需要的分摊到订单明细的大盘满足的履约数据、预扣数据
        BatchUtil.syncBatchExec(result, 10000, batch -> {
            orderItemFillForSatisfySupply(batch, orderTotalCoreMap);
        });
        ckcldStdCrpSwapDBHelper.executeRaw("TRUNCATE TABLE std_crp_swap.dwd_crp_order_item_and_info_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);

        String countSql = " select count(1) from std_crp_swap.dwd_crp_order_item_and_info_cf";
        // 每隔3秒钟检查一次，如果10次都没达到预期，则放弃
        waitWrite(10, 3, countSql, ckcldStdCrpSwapDBHelper, result.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dwd_crp_order_item_and_info_cf_local to std_crp_swap.dwd_crp_order_item_and_info_cf_local_tmp,\n"
                + "std_crp_swap.dwd_crp_order_item_and_info_cf_local to std_crp.dwd_crp_order_item_and_info_cf_local,\n"
                + "std_crp_swap.dwd_crp_order_item_and_info_cf_local_tmp to std_crp_swap.dwd_crp_order_item_and_info_cf_local\n"
                + "on cluster default_cluster");
        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_crp_order_item_and_info_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    private void orderItemFillForSatisfySupply(List<DwdCrpPplOrderItemAndInfoCfDO> result,
            Map<String, BigDecimal> orderTotalCoreMap) {
        Set<String> orderNumbers = ListUtils.toSet(result, DwdCrpPplOrderItemAndInfoCfDO::getOrderNumber);
        String sql = ORMUtils.getSql("/sql/order/supply/query_order_actual_buy.sql");
        List<OrderActualBuyDTO> orderBuyList = ckcldStdCrpDBHelper.getRaw(OrderActualBuyDTO.class, sql, orderNumbers);
        Map<String, OrderActualBuyDTO> map = ListUtils.toMap(orderBuyList, OrderActualBuyDTO::getOrderNumber,
                Function.identity());
        for (DwdCrpPplOrderItemAndInfoCfDO item : result) {
            if (item.getTotalCore() == null || item.getTotalCore() <= 0) {
                continue;
            }
            BigDecimal orderTotalCore = orderTotalCoreMap.get(item.getOrderNumber());
            BigDecimal itemTotalCore = new BigDecimal(item.getTotalCore());
            OrderActualBuyDTO orderActualBuyDTO = map.get(item.getOrderNumber());
            BigDecimal actualBuyCore = BigDecimal.ZERO;
            if (orderActualBuyDTO != null) {
                // 计算出订单明细需求核心数占订单需求核心数的比例
                BigDecimal pre = itemTotalCore.divide(orderTotalCore, 8, RoundingMode.HALF_UP);
                // 按比例分摊出订单明细中的履约核心数
                actualBuyCore = orderActualBuyDTO.getActualBuyCore().multiply(pre);
                item.setActualBuyCore(actualBuyCore);
            }
            if (item.getSatisfySupplyCore() != null && item.getSatisfySupplyCore() > 0) {
                // 计算出订单明细中大盘满足占订单明细需求核心数的比例
                BigDecimal pre2 = new BigDecimal(item.getSatisfySupplyCore())
                        .divide(itemTotalCore, 8, RoundingMode.HALF_UP);
                // 按比例分摊出订单明细中大盘满足的履约量
                BigDecimal actualBuyCoreForSatisfySupply = actualBuyCore.multiply(pre2);
                item.setActualBuyCoreForSatisfySupply(actualBuyCoreForSatisfySupply);
                if (item.getActualPreDeductCore() != null && item.getActualPreDeductCore() > 0) {
                    // 按比例分摊出订单明细中大盘满足的预扣核心数
                    BigDecimal actualPreDeductCoreForSatisfySupply = new BigDecimal(item.getActualPreDeductCore())
                            .multiply(pre2);
                    item.setActualPreDeductCoreForSatisfySupply(actualPreDeductCoreForSatisfySupply);
                }
            }

        }
    }


    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncPplYunxiaoApplyToCkStdTable", timeout = 60 * 2)
    @Deprecated
    public void syncPplYunxiaoApplyToCkStdTable() {
        String sql = IOUtils.readClasspathResourceAsString("/sql/ppl13week/std_table/ppl_yunxiao_apply_latest.sql");
        List<PplItemApplyStdTableVO> all = demandDBHelper.getRaw(PplItemApplyStdTableVO.class, sql);

        List<RegionDTO> regionDTOS = industryDemandDictService.listAllRegion();
        Map<String, String> regionToLanMap = ListUtils.toMap(regionDTOS, RegionDTO::getRegionShortChName, RegionDTO::getLand);

        fillerService.fill(all);
        List<DwdCrpPplYunxiaoApplyItemCfDO> result = ListUtils.transform(
                all, vo -> PplItemApplyStdTableVO.transTo(vo, regionToLanMap));
        if (ListUtils.isEmpty(result)) {
            log.error("syncPplYunxiaoApplyToCkStdTable result is empty, ignore sync to ck");
            return;
        }

        ckcldStdCrpSwapDBHelper.executeRaw(
                "TRUNCATE TABLE std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);
        Thread.sleep(3000);

        String countSql = " select count(1) from std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf";
        // 每隔2秒钟检查一次，如果15次都没达到预期，则放弃
        waitWrite(15, 2, countSql, ckcldStdCrpSwapDBHelper, result.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dwd_crp_ppl_yunxiao_apply_item_cf_local to std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf_local_tmp,\n"
                + "std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf_local to std_crp.dwd_crp_ppl_yunxiao_apply_item_cf_local,\n"
                + "std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf_local_tmp to std_crp_swap.dwd_crp_ppl_yunxiao_apply_item_cf_local\n"
                + "on cluster default_cluster");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_crp_ppl_yunxiao_apply_item_cf");
        ckcldStdCrpDBHelper.insert(logDO);

    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncLatestPplItemVersionBaseToCkStdTable", timeout = 60)
    public void syncLatestPplItemVersionBaseToCkStdTable() {
        // 基准版：：版本月为预测需求月N-3第一个版本（需求预测月6月，对应取版本月为3月第一版，需求预测月5月，对应取版本月为2月第一版）
        // 先取出 版本月为预测需求月N-3 的数据
        String sql = IOUtils.readClasspathResourceAsString("/sql/ppl13week/std_table/ppl_version_base.sql");
        List<DwsCrpPplItemVersionBaseCfVO> all = ckcldStdCrpDBHelper.getRaw(DwsCrpPplItemVersionBaseCfVO.class, sql);
        if (ListUtils.isEmpty(all)) {
            log.error("syncLatestPplItemVersionBaseToCkStdTable full version data is empty, ignore sync to ck");
            return;
        }
        Map<String, List<DwsCrpPplItemVersionBaseCfVO>> yearMonthMap = ListUtils
                .toMapList(all, vo->vo.getYear()+"-"+vo.getMonth(), item->item);
        List<DwsCrpPplItemVersionBaseCfDo> result = new ArrayList<>();
        Optional<DwsCrpPplItemVersionBaseCfVO> data;
        String baseVersionCode;
        for (List<DwsCrpPplItemVersionBaseCfVO> values : yearMonthMap.values()) {
            // 再取出 每个版本月为预测需求月N-3 的 第一版数据
            data = values.stream().min(Comparator.comparing(DwsCrpPplItemVersionBaseCfVO::getVersionStartAuditTime));
            if (data.isPresent()) {
                // 基准版的版本号
                baseVersionCode = data.get().getVersionCode();
                final String code = baseVersionCode;
                values.forEach(vo -> {
                    if (code.equals(vo.getVersionCode())) {
                        result.add(DwsCrpPplItemVersionBaseCfVO.tranTo(vo));
                    }
                });
            }
        }
        if (ListUtils.isEmpty(result)) {
            log.error("syncLatestPplItemVersionBaseToCkStdTable result is empty, ignore sync to ck");
            return;
        }

        fillerService.fill(result);
        ckcldStdCrpSwapDBHelper.executeRaw(
                "TRUNCATE TABLE std_crp_swap.dws_crp_ppl_item_version_base_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);
        Thread.sleep(3000);

        String countSql = " select count(1) from std_crp_swap.dws_crp_ppl_item_version_base_cf";
        // 每隔2秒钟检查一次，如果15次都没达到预期，则放弃
        waitWrite(15, 2, countSql, ckcldStdCrpSwapDBHelper, result.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dws_crp_ppl_item_version_base_cf_local to std_crp_swap.dws_crp_ppl_item_version_base_cf_local_tmp,\n"
                + "std_crp_swap.dws_crp_ppl_item_version_base_cf_local to std_crp.dws_crp_ppl_item_version_base_cf_local,\n"
                + "std_crp_swap.dws_crp_ppl_item_version_base_cf_local_tmp to std_crp_swap.dws_crp_ppl_item_version_base_cf_local\n"
                + "on cluster default_cluster");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dws_crp_ppl_item_version_base_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncLatestPplItemVersion532ToCkStdTable", timeout = 60)
    public void syncLatestPplItemVersion532ToCkStdTable() {
        // 对于需求月M，532版中只存在 M-1、M-2、M-3 月的数据
        String sql = IOUtils.readClasspathResourceAsString("/sql/ppl13week/std_table/ppl_version_532.sql");
        List<DwsCrpPplItemVersion532CfVO> all = ckcldStdCrpDBHelper.getRaw(DwsCrpPplItemVersion532CfVO.class, sql);
        if (ListUtils.isEmpty(all)) {
            log.error("syncLatestPplItemVersion532ToCkStdTable full version data is empty, ignore sync to ck");
            return;
        }

        List<DwsCrpPplItemVersion532CfDO> result = new ArrayList<>();
        all.forEach(vo->result.add(DwsCrpPplItemVersion532CfVO.tranTo(vo)));
        if (ListUtils.isEmpty(result)) {
            log.error("syncLatestPplItemVersion532ToCkStdTable result is empty, ignore sync to ck");
            return;
        }

        fillVersion532(result);
        ckcldStdCrpSwapDBHelper.executeRaw(
                "TRUNCATE TABLE std_crp_swap.dws_crp_ppl_item_version_532_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);
        Thread.sleep(3000);

        String countSql = " select count(1) from std_crp_swap.dws_crp_ppl_item_version_532_cf";
        // 每隔2秒钟检查一次，如果15次都没达到预期，则放弃
        waitWrite(15, 2, countSql, ckcldStdCrpSwapDBHelper, result.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dws_crp_ppl_item_version_532_cf_local to std_crp_swap.dws_crp_ppl_item_version_532_cf_local_tmp,\n"
                + "std_crp_swap.dws_crp_ppl_item_version_532_cf_local to std_crp.dws_crp_ppl_item_version_532_cf_local,\n"
                + "std_crp_swap.dws_crp_ppl_item_version_532_cf_local_tmp to std_crp_swap.dws_crp_ppl_item_version_532_cf_local\n"
                + "on cluster default_cluster");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dws_crp_ppl_item_version_532_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncYunxiaoApplyOrderDetailToToCkStdTable", timeout = 60 * 5)
    public void syncYunxiaoApplyOrderDetailToToCkStdTable(YearMonth end, int months, boolean forceRefresh) {
        if (months < 1) {
            return;
        }
        if (end == null) {
            end = YearMonth.now();
        }
        YearMonth yearMonth = end;
        for (int i = 0; i < months; i++) {
            syncYunxiaoApplyOrderDetailToToCkStdTable(yearMonth, forceRefresh);
            yearMonth = yearMonth.minusMonths(1);
        }
    }

    @SneakyThrows
    private void syncYunxiaoApplyOrderDetailToToCkStdTable(YearMonth yearMonth, boolean forceRefresh) {
        List<OrderItem> datas = yunxiaoAPIService.orderExportToJson(yearMonth);
        Integer partition = Integer.parseInt(yearMonth.format(DatePattern.SIMPLE_MONTH_FORMATTER));
        if (ListUtils.isEmpty(datas)) {
            log.error("请求云霄预约单（预扣纬度）接口数据为空, 提单年月：{}", partition);
        }
        fillerService.fill(datas);
        List<DwdYunxiaoApplyOrderDetailCfDO> result = new ArrayList<>(datas.size());
        // 获取当前宽表中预约单的资源准备进度, 强制刷新时不从当前宽表读取数据
        Map<String, StdYunXiaoMatchSummaryDTO> orderMatchMap = forceRefresh
                ? new HashMap<>() : matchSummaryMap(partition);
        List<String> orderOverList = ListUtils.newArrayList(YunxiaoOrderStatusEnum.FINISHED.getName(),
                YunxiaoOrderStatusEnum.CANCELED.getName());
        Map<String, OrderMatchSummaryDTO> cache = new HashMap<>();
        for (OrderItem datum : datas) {
            if (datum == null) {
                continue;
            }
            DwdYunxiaoApplyOrderDetailCfDO item = OrderItem.convert(datum);
            StdYunXiaoMatchSummaryDTO dbData = orderMatchMap.get(datum.getOrderId());
            if (dbData == null || !orderOverList.contains(dbData.getStatus())) {
                // 非结束或者取消的订单需要从云霄接口同步最新的 资源准备进度 信息
                OrderMatchSummaryDTO matchSummaryDTO = cache.get(datum.getOrderId());
                if (matchSummaryDTO == null) {
                    matchSummaryDTO = getMatchSummaryByYunxiaoApi(datum.getOrderId(), 0);
                    cache.put(datum.getOrderId(), matchSummaryDTO);
                }
                item.matchInfoSet(matchSummaryDTO);
            } else {
                item.matchInfoSet(dbData);
            }
            item.setSubmitYearMonth(partition);
            result.add(item);
        }
        ckcldStdCrpSwapDBHelper.executeRaw("alter table `std_crp_swap`.`dwd_yunxiao_apply_order_detail_cf_local` "
                + " on cluster default_cluster drop partition " + partition);
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);
        Thread.sleep(2 * 1000);
        // 最后将备份库的最新同步完成的分区数据复制到主库
        ckcldStdCrpDBHelper.executeRaw("ALTER TABLE `std_crp`.`dwd_yunxiao_apply_order_detail_cf_local` "
                + " on cluster default_cluster \n"
                + " REPLACE PARTITION " + partition
                + " FROM `std_crp_swap`.`dwd_yunxiao_apply_order_detail_cf_local`");

        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_yunxiao_apply_order_detail_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncPplItemVersionNewestFromStdSwapToCkStdTable", timeout = 60)
    public void syncPplItemVersionNewestFromStdSwapToCkStdTable() {
        // 备份表的同步时间
        Long swapImpDate = ckcldStdCrpSwapDBHelper.getRawOne(Long.class,
                "select max(imp_date) from std_crp_swap.dws_crp_ppl_item_version_newest_cf");
        if (swapImpDate == null) {
            log.error("syncPplItemVersionNewestFromStdSwapToCkStdTable std_crp_swap data is empty, "
                    + "ignore sync to std_crp");
            return;
        }
        // 主表的同步时间
        Long mainImpDate = ckcldStdCrpDBHelper.getRawOne(Long.class,
                "select max(imp_date) from std_crp.dws_crp_ppl_item_version_newest_cf");
        if (mainImpDate == null || swapImpDate > mainImpDate) {
            // 备份表的同步时间大于主表的同步时间，则表示备份表是欧拉生成的最新的数据，需要同步到主表中
            ckcldStdCrpDBHelper.executeRaw("rename table\n"
                    + "std_crp.dws_crp_ppl_item_version_newest_cf_local "
                    + " to std_crp_swap.dws_crp_ppl_item_version_newest_cf_local_tmp, \n"
                    + "std_crp_swap.dws_crp_ppl_item_version_newest_cf_local "
                    + " to std_crp.dws_crp_ppl_item_version_newest_cf_local,\n"
                    + "std_crp_swap.dws_crp_ppl_item_version_newest_cf_local_tmp "
                    + " to std_crp_swap.dws_crp_ppl_item_version_newest_cf_local\n"
                    + "on cluster default_cluster");
            SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
            logDO.setTableName("dws_crp_ppl_item_version_newest_cf");
            ckcldStdCrpDBHelper.insert(logDO);
        }
    }

    private OrderMatchSummaryDTO getMatchSummaryByYunxiaoApi(String orderId, int loop) {
        OrderMatchSummaryDTO res = yunxiaoAPIService.getMatchSummary(orderId);
        String resultStr = JSON.toJson(res);
        if (resultStr != null && (resultStr.contains("Throttling") || resultStr.contains("触发流控"))) {
            try {
                Thread.sleep(10_000); // 因为限频，所以超频时等待10秒
            } catch (InterruptedException ignore) {
                // non
            }
            if (loop > 10) {
                taskLogService.genRunLog("syncYunxiaoApplyOrderDetailToToCkStdTable",
                        "getMatchSummaryByYunxiaoApi",
                        "order:【" + orderId + "】,云霄接口超时超过10次，退出");
                return res;
            } else {
                return getMatchSummaryByYunxiaoApi(orderId, loop + 1);
            }
        }
        return res;
    }

    private Map<String, StdYunXiaoMatchSummaryDTO> matchSummaryMap(Integer partition) {
        WhereContent where = new WhereContent()
                .andEqual(DwdYunxiaoApplyOrderDetailCfDO::getSubmitYearMonth, partition);
        List<StdYunXiaoMatchSummaryDTO> data = ckcldStdCrpDBHelper
                .getAll(StdYunXiaoMatchSummaryDTO.class, where.getSql(), where.getParams());
        return ListUtils.toMap(data, StdYunXiaoMatchSummaryDTO::getOrderId, item -> item);
    }

    // 532计算逻辑放这里
    public void fillVersion532(List<DwsCrpPplItemVersion532CfDO> list) {
        if (ListUtils.isEmpty(list)) {
            return;
        }
        fillVersion532MonthWeight(list);
        fillVersion532MonthVersionCountWeight(list);
        fillVersion532PredictiveValueWithWeight(list);
    }

    /**
     *  填充月度权值
     */
    private void fillVersion532MonthWeight(List<DwsCrpPplItemVersion532CfDO> list) {
        int differenceMonth;
        for (DwsCrpPplItemVersion532CfDO filler : list) {
            if (filler == null || filler.getVersionYear() == null || filler.getVersionMonth() == null
                    || filler.getYear() == null || filler.getMonth() == null) {
                continue;
            }
            differenceMonth = filler.getYear() * 12 + filler.getMonth()
                    - (filler.getVersionYear() * 12 + filler.getVersionMonth());
            switch (differenceMonth) {
                case 3:
                    filler.setMonthWeight(Constant.b05);
                    break;
                case 2:
                    filler.setMonthWeight(Constant.b03);
                    break;
                case 1:
                    filler.setMonthWeight(Constant.b02);
                    break;
                default:
            }
        }
    }

    /**
     *  填充月度内版本次数权值
     */
    private void fillVersion532MonthVersionCountWeight(List<DwsCrpPplItemVersion532CfDO> list) {
        String split = "-";
        // 先按版本年月分组
        Map<String, List<DwsCrpPplItemVersion532CfDO>> mapList = ListUtils.toMapList(
                list.stream()
                        .filter(filler -> filler != null && filler.getVersionYear() != null
                                && filler.getVersionMonth() != null)
                        .collect(Collectors.toList()),
                filler -> filler.getVersionYear() + split + filler.getVersionMonth(),
                filler -> filler);
        mapList.forEach((versionYearMonth, version532s) -> {
            String[] array = versionYearMonth.split(split);
            int versionYear = Integer.parseInt(array[0]);
            int versionMonth = Integer.parseInt(array[1]);
            // 再按按需求年月分组
            Map<String, List<DwsCrpPplItemVersion532CfDO>> mapList2 = ListUtils.toMapList(
                    version532s.stream()
                            .filter(filler -> filler != null && filler.getYear() != null && filler.getMonth() != null)
                            .collect(Collectors.toList()),
                    filler -> filler.getYear() + split + filler.getMonth(),
                    filler -> filler);
            mapList2.forEach((yearMonth, itemList) -> {
                String[] yearMonthArray = yearMonth.split(split);
                int year = Integer.parseInt(yearMonthArray[0]);
                int month = Integer.parseInt(yearMonthArray[1]);
                fillVersion532MonthVersionCountWeight(versionYear, versionMonth, year, month, itemList);
            });
        });
    }

    /**
     *   为532数据设置指定版本年月、需求年月的月度版本次数权值
     * @param versionYear  版本年
     * @param versionMonth  版本月
     * @param year 需求年
     * @param month 需求月
     * @param list 入参中指定的版本年月、需求年月的532数据
     */
    private void fillVersion532MonthVersionCountWeight(int versionYear, int versionMonth,
            int year, int month,
            List<DwsCrpPplItemVersion532CfDO> list) {
        if (ListUtils.isEmpty(list)) {
            return;
        }

        int monthCount = year * 12 + month;
        // 获取版本范围包含了入参中需求年月的版本信息
        String sql = "select * from ppl_version where deleted = 0 "
                + "and ? BETWEEN (demand_begin_year*12+demand_begin_month) and (demand_end_year*12+demand_end_month)";
        List<PplVersionDO> pplVersionDOList = demandDBHelper.getRaw(PplVersionDO.class, sql, monthCount);
        if (ListUtils.isNotEmpty(pplVersionDOList)) {
            // 只保留入参中指定版本年月的版本信息
            pplVersionDOList.removeIf(item -> {
                YearMonth yearMonth =  PplItemVersionStdTableVO.parseYearMonth(item.getVersionCode(),
                        item.getStartAuditTime(), item.getCreateTime());
                return yearMonth == null || yearMonth.getYear() != versionYear
                        || yearMonth.getMonthValue() != versionMonth;
            });
        }

        BigDecimal weight = BigDecimal.ZERO;
        if (ListUtils.isNotEmpty(pplVersionDOList)) {
            // 月度内版本次数权值
            weight = BigDecimal.ONE.divide(new BigDecimal(pplVersionDOList.size() + ""), 6, RoundingMode.HALF_UP);
        }
        for (DwsCrpPplItemVersion532CfDO version532CfDO : list) {
            // 对于在相同版本月、需求月内的pplId，MonthVersionCountWeight 值都是一致的
            version532CfDO.setMonthVersionCountWeight(weight);
        }
    }

    /**
     *  填充加权之后的各项预测值
     */
    private void fillVersion532PredictiveValueWithWeight(List<DwsCrpPplItemVersion532CfDO> list) {
        BigDecimal originalTotalCore;
        BigDecimal originalInstanceNum;
        for (DwsCrpPplItemVersion532CfDO filler : list) {
            if (filler == null || filler.getMonthWeight() == null || BigDecimal.ZERO.equals(filler.getMonthWeight())
                    || filler.getMonthVersionCountWeight() == null
                    || BigDecimal.ZERO.equals(filler.getMonthVersionCountWeight())) {
                continue;
            }

            if (filler.getOriginalTotalCore() != null && filler.getOriginalTotalCore() != 0) {
                originalTotalCore = new BigDecimal(filler.getOriginalTotalCore().toString());
                filler.setTotalCore(
                        originalTotalCore.multiply(filler.getMonthWeight())
                                .multiply(filler.getMonthVersionCountWeight())
                );
            }

            if (filler.getOriginalInstanceNum() != null && filler.getOriginalInstanceNum() != 0) {
                originalInstanceNum = new BigDecimal(filler.getOriginalInstanceNum().toString());
                filler.setInstanceNum(
                        originalInstanceNum.multiply(filler.getMonthWeight())
                                .multiply(filler.getMonthVersionCountWeight())
                );
            }

            if (filler.getOriginalTotalGpuNum() != null && !BigDecimal.ZERO.equals(filler.getOriginalTotalGpuNum())) {
                filler.setTotalGpuNum(
                        filler.getOriginalTotalGpuNum().multiply(filler.getMonthWeight())
                                .multiply(filler.getMonthVersionCountWeight())
                );
            }
        }
    }


    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false,keyScript = "args[0]")
    @TaskLog(taskName = "syncPplItemVersionJoinOrderToCkStdTable", timeout = 60)
    public void syncPplItemVersionJoinOrderToCkStdTable(String versionCode) {

        PplVersionDO version = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?", versionCode);
        if (version == null){
            throw new BizException("版本不存在,请检查版本号");
        }
        // 版本允许录入PPL开始年月
        String currentVersionStartYearMonth = DateUtils.getYearMonthStr(version.getDemandBeginYear(),version.getDemandBeginMonth());
        UnificatedVersionDTO unificatedVersionDTO =
                demandDBHelper.getOne(UnificatedVersionDTO.class, "where version_code = ?", versionCode);
        UnificatedVersionEventDO event = unificatedVersionDTO.getEventByCode(
                CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode());
        // 版本所属年月
        String versionYearMonth = DateUtils.chineseYearMonthToEnglishYearMonth(unificatedVersionDTO.getVersionYearMonth());

        // PPL版本数据
        // 境内6周后 境外8周后 新增/弹性/退回
        List<DwdCrpPplItemVersionCfDO> versionOriginalData = ckcldStdCrpDBHelper.getAll(DwdCrpPplItemVersionCfDO.class,
                "where version_code = ?", versionCode);

        // 订单共识数据
        List<OrderConsensusDemandDetailJoinOrderVO> orderOriginalData = getOriginalOrderData(versionYearMonth, version);

        // 继承数据
        // 取版本所属年月 到 当前版本允许录入PPL年月 之间的继承数据 （退回数据/模型预测数据）
        List<DwdCrpPplItemVersionCfDO> extendOriginalData = getOriginalExtendData(versionYearMonth,
                currentVersionStartYearMonth);


        List<DwdCrpPplJoinOrderVersionCfDO> result = new ArrayList<>();
        // 境内6周内 海外8周内 非退回 取订单数据
        // 取共识需求模块数据
        List<DwdCrpPplJoinOrderVersionCfDO> versionData = ListUtils.transform(versionOriginalData,
                v -> DwdCrpPplJoinOrderVersionCfDO.trans(version, PplJoinOrderVersionItemDataSource.VERSION.getCode(), v));

        List<DwdCrpPplJoinOrderVersionCfDO> extendData = ListUtils.transform(extendOriginalData,
                v -> DwdCrpPplJoinOrderVersionCfDO.trans(version, PplJoinOrderVersionItemDataSource.EXTEND.getCode(), v));

        List<DwdCrpPplJoinOrderVersionCfDO> orderData = ListUtils.transform(orderOriginalData,
                v -> DwdCrpPplJoinOrderVersionCfDO.trans(version, v));

        fillerService.fill(orderData);

        result.addAll(versionData);
        result.addAll(extendData);
        result.addAll(orderData);

        ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.dwd_crp_ppl_join_order_version_cf_local ON CLUSTER default_cluster DROP PARTITION ?",
                versionCode);
        ckcldStdCrpDBHelper.insertBatchWithoutReturnId(result);

    }

    @Override
    public void syncPplJoinOrderToCkStdTableAllVersion() {
        List<PplVersionDO> versionList = demandDBHelper.getAll(PplVersionDO.class,
                "where status != ? and version_code >= ? order by version_code asc",
                PplVersionStatusEnum.CREATED.getCode(), "V_20240625");
        PplStdTableService pplStdTableService = SpringUtil.getBean(PplStdTableService.class);

        for (PplVersionDO pplVersionDO : versionList) {
            pplStdTableService.syncPplItemVersionJoinOrderToCkStdTable(pplVersionDO.getVersionCode());
        }

    }

    @Override
    @TaskLog(taskName = "syncPplJoinOrderTaskByVersionEvent", timeout = 60)
    public void syncPplJoinOrderTaskByVersionEvent(String versionCode) throws InterruptedException {
        Thread.sleep(10000);
        PplStdTableService pplStdTableService = SpringUtil.getBean(PplStdTableService.class);

        // 先同步 版本数据
        pplStdTableService.syncPplItemVersionToCkStdTable();

        // 触发拼接宽表同步任务
        pplStdTableService.syncPplItemVersionJoinOrderToCkStdTable(versionCode);
    }

    @Override
    @TaskLog(taskName = "syncPplJoinOrderTaskByVersionEvent", timeout = 60)
    public void syncJoinTableForNewVersion() {
        UnificatedVersionDTO one = demandDBHelper.getOne(UnificatedVersionDTO.class, " where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
        if (one == null){
            return;
        }
        UnificatedVersionEventDO event = one.getEventByCode(CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode());
        if (event == null || event.getDeadline() == null || event.getIsDone()){
            // 如果已经下发了对冲 那么不再同步。
            return;
        }
        PplStdTableService pplStdTableService = SpringUtil.getBean(PplStdTableService.class);
        pplStdTableService.syncPplItemVersionJoinOrderToCkStdTable(one.getVersionCode());
    }

    /**
     * 同步最新版 版本拼接宽表
     */
    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncPplItemVersionJoinOrderLatestToCkStdTable", timeout = 60 * 2)
    public void syncPplItemVersionJoinOrderLatestToCkStdTable() {
        List<YearMonthVersionCode> historyVersion = DBList.ckcldStdCrpDBHelper.getRaw(YearMonthVersionCode.class,
                "SELECT year,month,max(version_code)as version_code from std_crp.dwd_crp_ppl_join_order_version_cf"
                        + " where data_source = 'ORDER' "
                        + " GROUP BY year,month order by year,month ");

        WhereSQL or = new WhereSQL();
        for (int i = 0; i < historyVersion.size(); i++) {
            YearMonthVersionCode yearMonthVersionCode = historyVersion.get(i);

            if (i == historyVersion.size()-1){
                // 最新的一个版本 直接获取所有数据
                or.or("version_code = ?",yearMonthVersionCode.getVersionCode());

            } else {
                or.or("(version_code = ? and year = ? and month = ?)",
                        yearMonthVersionCode.getVersionCode(), yearMonthVersionCode.getYear(), yearMonthVersionCode.getMonth());
            }
        }

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("total_core > 0 ");
        whereSQL.and(or);
        List<DwdCrpPplJoinOrderVersionCfDO> sourceData = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplJoinOrderVersionCfDO.class,
                or.getSQL(), or.getParams());

        List<DwsCrpPplJoinOrderVersionNewestCfDO> targetData = ListUtils.transform(sourceData,
                DwsCrpPplJoinOrderVersionNewestCfDO::transform);

        // 刷新swap库表
        ckcldStdCrpSwapDBHelper.executeRaw("TRUNCATE TABLE std_crp_swap.dws_crp_ppl_join_order_version_newest_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(targetData);

        String countSql = " select count(1) from std_crp_swap.dws_crp_ppl_join_order_version_newest_cf";
        // 每隔3秒钟检查一次，如果10次都没达到预期，则放弃
        waitWrite(10, 3, countSql, ckcldStdCrpSwapDBHelper, targetData.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dws_crp_ppl_join_order_version_newest_cf_local to std_crp_swap.dws_crp_ppl_join_order_version_newest_cf_local_tmp,\n"
                + "std_crp_swap.dws_crp_ppl_join_order_version_newest_cf_local to std_crp.dws_crp_ppl_join_order_version_newest_cf_local,\n"
                + "std_crp_swap.dws_crp_ppl_join_order_version_newest_cf_local_tmp to std_crp_swap.dws_crp_ppl_join_order_version_newest_cf_local\n"
                + "on cluster default_cluster");
        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dws_crp_ppl_join_order_version_newest_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @SneakyThrows
    @Synchronized(waitLockMillisecond = 2000, throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncConsensusOrderToCkStdTable", timeout = 60 * 2)
    public void syncConsensusOrderToCkStdTable() {
        WhereSQL whereSQL = new WhereSQL();

        whereSQL.and("t1.available_status = 'available'");
        whereSQL.and("t2.available_status = 'available'");
        whereSQL.and("t2.order_category in (?)", YunxiaoOrderCategoryEnum.getCvmCategory());
        whereSQL.and("t2.order_status not in (?)",
                Arrays.asList(OrderStatusEnum.DRAFT.getCode(),OrderStatusEnum.CANCELED.getCode()));

        List<OrderConsensusDemandDetailJoinOrderVO> consensusOrder = demandDBHelper.getAll(
                OrderConsensusDemandDetailJoinOrderVO.class, whereSQL.getSQL(), whereSQL.getParams());

        Map<String, StaticZoneDO> zoneNameMap = dictService.queryZoneName2StaticZone();
        Map<String, String> instanceTypeToCommonInstanceTypeMap = pplDictService.getInstanceTypeToCommonInstanceTypeMap();
        Map<String, IndustryDemandIndustryWarZoneDictDO> customerMap = dictService.queryCustomerMap();
        List<DwdCrpConsensusOrderCfDO> result = ListUtils.transform(consensusOrder,
                v -> DwdCrpConsensusOrderCfDO.convert(v,zoneNameMap,instanceTypeToCommonInstanceTypeMap,customerMap));

        // 刷新swap库表
        ckcldStdCrpSwapDBHelper.executeRaw("TRUNCATE TABLE std_crp_swap.dwd_crp_consensus_order_cf_local ON CLUSTER default_cluster");
        ckcldStdCrpSwapDBHelper.insertBatchWithoutReturnId(result);

        String countSql = " select count(1) from std_crp_swap.dwd_crp_consensus_order_cf";
        // 每隔3秒钟检查一次，如果10次都没达到预期，则放弃
        waitWrite(10, 3, countSql, ckcldStdCrpSwapDBHelper, result.size());

        ckcldStdCrpDBHelper.executeRaw("rename table\n"
                + "std_crp.dwd_crp_consensus_order_cf_local to std_crp_swap.dwd_crp_consensus_order_cf_local_tmp,\n"
                + "std_crp_swap.dwd_crp_consensus_order_cf_local to std_crp.dwd_crp_consensus_order_cf_local,\n"
                + "std_crp_swap.dwd_crp_consensus_order_cf_local_tmp to std_crp_swap.dwd_crp_consensus_order_cf_local\n"
                + "on cluster default_cluster");
        SystemSyncTableLogDO logDO = new SystemSyncTableLogDO();
        logDO.setTableName("dwd_crp_consensus_order_cf");
        ckcldStdCrpDBHelper.insert(logDO);
    }

    @Override
    @TaskLog(taskName = "refreshHistoryJoinTable")
    public void refreshHistoryJoinTable() {
        LocalDate now = LocalDate.now();
        // 刷新历史六个月的实时订单数据。
        List<String> versionList = demandDBHelper.getRaw(String.class, "select max(version_code) from unificated_version where deleted = 0 and status = 'DONE'\n"
                + " and version_year_month != (select version_year_month from unificated_version where deleted = 0 and status = 'PROCESS')\n"
                + " group by version_year_month order by version_code desc limit ?",6);

        PplStdTableService pplStdTableService = SpringUtil.getBean(PplStdTableService.class);
        for (String versionCode : versionList) {
            pplStdTableService.syncPplItemVersionJoinOrderToCkStdTable(versionCode);
        }

    }

    /**
     * 查询6周内订单数据
     * @param versionYearMonth
     * @param version
     * @return
     */
    public List<OrderConsensusDemandDetailJoinOrderVO> getOriginalOrderData(String versionYearMonth,PplVersionDO version) {
        String currentVersionStartYearMonth = version.getDemandBeginYear() + "-" + version.getDemandBeginMonth();
        String currentVersionOverseasStartYearMonth = version.getOverseasDemandBeginYear() + "-" + version.getOverseasDemandBeginMonth();
        WhereSQL whereSQL = new WhereSQL();

        whereSQL.and("t1.available_status = 'available'");
        whereSQL.and("t2.available_status = 'available'");

        whereSQL.and("t2.order_category in (?)", YunxiaoOrderCategoryEnum.getCvmCategory());

        // 当前版本年月的最后一天
        LocalDate lastDayOfMonthLocalDate = DateUtils.getLastDayOfMonthLocalDate(versionYearMonth);

        // 订单提单时间 小于 当前版本年月最后一天的时间
        whereSQL.and("t2.submit_time <= ?", LocalDateTime.of(lastDayOfMonthLocalDate,LocalTime.MAX));
        whereSQL.and("t2.order_status not in (?)",
                Arrays.asList(OrderStatusEnum.DRAFT.getCode(),OrderStatusEnum.CANCELED.getCode()));

        WhereSQL orSql = new WhereSQL();
        orSql.or("(t1.demand_customhouse_title = ? and t1.consensus_begin_buy_date >= ? and t1.consensus_begin_buy_date < ?)",
                "境内",DateUtils.getFirstDayOfMonth(versionYearMonth),
                DateUtils.getFirstDayOfMonth(currentVersionStartYearMonth));
        orSql.or("(t1.demand_customhouse_title = ? and t1.consensus_begin_buy_date >= ? and t1.consensus_begin_buy_date < ?)",
                "境外",DateUtils.getFirstDayOfMonth(versionYearMonth),
                DateUtils.getFirstDayOfMonth(currentVersionOverseasStartYearMonth));
        whereSQL.and(orSql);
        List<OrderConsensusDemandDetailJoinOrderVO> all = demandDBHelper.getAll(
                OrderConsensusDemandDetailJoinOrderVO.class, whereSQL.getSQL(), whereSQL.getParams());
        return all;
    }

    /**
     * 查询需要继承的数据
     * @param versionYearMonth
     * @param currentVersionStartYearMonth
     * @return
     */
    public List<DwdCrpPplItemVersionCfDO> getOriginalExtendData(String versionYearMonth,String currentVersionStartYearMonth){
        List<DwdCrpPplItemVersionCfDO> extendOriginalData = new ArrayList<>();
        // 取版本所属年月 到 当前版本允许录入PPL年月 之间的继承数据
        while(!versionYearMonth.equals(currentVersionStartYearMonth)){
            PplVersionDO monthVersion = getLastVersionVersionCodeByYearMonth(versionYearMonth);

            // 6周内的退回数据 取最后一次滚动版本数据 继承数据
            List<DwdCrpPplItemVersionCfDO> returnData = ckcldStdCrpDBHelper.getAll(DwdCrpPplItemVersionCfDO.class,
                    "where version_code = ? and source != ? and demand_type = ? and year = ? and month = ? ",
                    monthVersion.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(),PplDemandTypeEnum.RETURN.getCode(),
                    monthVersion.getDemandBeginYear(),monthVersion.getDemandBeginMonth());
            extendOriginalData.addAll(returnData);

            // 6周内的模型预测数据 取最后一次滚动版本数据 继承数据
            List<DwdCrpPplItemVersionCfDO> forecastData = ckcldStdCrpDBHelper.getAll(DwdCrpPplItemVersionCfDO.class,
                    "where version_code = ? and source = ? and industry_dept = ? and year = ? and month = ? ",
                    monthVersion.getVersionCode(), PplOrderSourceTypeEnum.FORECAST.getCode(), IndustryDeptEnum.LONG_TAIL.getName(),
                    monthVersion.getDemandBeginYear(),monthVersion.getDemandBeginMonth());
            extendOriginalData.addAll(forecastData);

            versionYearMonth = DateUtils.yearMonthAddMonth(versionYearMonth,1);
        }
        return extendOriginalData;
    }

    public PplVersionDO getLastVersionVersionCodeByYearMonth(String versionYearMonth) {
        String[] split = versionYearMonth.split("-");
        Integer year = Integer.parseInt(split[0]);
        Integer month = Integer.parseInt(split[1]);
        PplVersionDO lastVersion = demandDBHelper.getOne(PplVersionDO.class,
                "where demand_begin_year = ? and demand_begin_month = ? order by id desc limit 1",year,month);
        if (lastVersion == null){
            throw new BizException("缺失对应版本年月的版本" + versionYearMonth);
        }
        return lastVersion;
    }


}
