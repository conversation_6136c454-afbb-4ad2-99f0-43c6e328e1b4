package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CreateOrderReq {

    private String creator; // 提单人
    private String architect; // 架构师，也就是PPL的提单人
    private String uin; // 客户uin
    private String appRole; // 应用角色
    private String region; // 地域，例如ap-beijing，如果是随机区域就不传了，这里需要把中文转成代号了
    /**
     * 原因分类
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoReasonTypeEnum
     */
    private String reasonType;
    private String reason; // 原因，相当于于预约单备注
    private String expectTime; // 期望开始使用时间
    private String latestExpectTime; // 期望结束使用时间
    /**
     * 订单类型
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderTypeEnum
     */
    private String orderType;
    /**
     * 订单分类
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderCategoryEnum
     */
    private String orderCategory;
    private String asStartTime; // 弹性开始时间，如果订单类型是弹性，那么这个有值，HH:mm:ss
    private String asEndTime; // 弹性结束时间，如果订单类型是弹性，那么这个有值，HH:mm:ss
    private List<OrderDetails> orderDetails; // 订单明细
    private String flowId; // 不填

    private String projectName; // 项目名称，预约单不需要，仅ppl有

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OrderDetails {
        private String pplId;
        private String pplOrder;
        private String projectName; // 项目名称，预约单不需要，仅ppl有

        private Long id; // 不填
        private String creator; // 不填，页面没有，也可以由order单查出
        private String appId; // 不填，页面没有，也可以由order单查出
        private String uin; // 不填，页面没有，也可以由order单查出
        private String zone; // 可用区
        private String zoneName; // 可用区的中文名
        private String region;
        private String regionName;
        private List<String> optionalZones; //
        private String instanceType; // 机型，例如S5.8XLARGE64
        private String instanceFamily; // 机型族，这个在云霄的请求接口参数上没有，但是页面请求有，所以这里加上
        private String reservedInstanceType; // 实际预扣实例类型
        private List<String> optionalInstanceTypes; //
        private List<String> optionalInstanceFamily; // 可替代机型族，这个在云霄的请求接口参数上没有，但是页面请求有，所以这里加上
        /**
         * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoDiskTypeEnum
         */
        private String sysDiskType; // 系统盘类型
        private Integer sysDiskSize;
        private String dataDiskType;
        private Integer dataDiskSize;
        private Integer dataDiskCount;
        private Integer applyCount;

        private Integer applyCore; // 申领核心数，这个是额外给的
        private BigDecimal applyGpu; // 申领的gpu卡数，这个是额外给的

        /**
         * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum
         */
        private String payMode; // 计费模式
        private String reason;
        private List<String> hostIpList;
        private List<String> disasterRecoverGroupIdList;
        private Integer familyCpuLimitPerNode;
        private String startTime;
        private String destroyTime;
        private Boolean keepReserved;
        /**
         * 预扣单初始状态，创建时不填
         */
        private String status;
        private List<String> filterHostTypes;
        private Boolean forceAcrossNodeFlag;
        private String affinityType;
        private Integer affinity;
        /**逻辑区*/
        private Integer appMask;
        private String appRole;
    }
}
