package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_config_stat_industry_dept_class")
public class PplConfigStatIndustryDeptClassDO extends BaseDO {

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 分类<br/>Column: [category] */
    @Column(value = "category")
    private String category;

}