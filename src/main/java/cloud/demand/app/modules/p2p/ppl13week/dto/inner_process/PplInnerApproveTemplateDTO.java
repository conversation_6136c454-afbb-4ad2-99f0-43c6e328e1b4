package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.h2.api.DatabaseEventListener;

import java.util.Date;

@Data
@AllArgsConstructor
public class PplInnerApproveTemplateDTO {

    /** ppl_inner_process_template 主键*/
    private Long templateId;

    /** 模版名*/
    private String templateName;

    private String templateType;

    /** 模版节点数量 */
    private Integer nodeNum;

    /**  模版维护人 */
    private String creator;

    private Date updateTime;

}
