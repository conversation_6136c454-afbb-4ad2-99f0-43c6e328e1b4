package cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp;


import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryResourceInfoRsp {


    List<Data> data;

    @lombok.Data
    public static class Data {

        // 资源类型
        String sourceTypeName;

        String instanceTotalDemand;
        String instanceCoreTotalDemand;
        String storageTotalDemand;

        String instanceWaitMeddle;
        String instanceCoreWaitMeddle;
        String storageWaitMeddle;

        String instanceOrdered; // 已预约
        String instanceCoreOrdered; // 已预约 核心数
        String instanceUnordered; // 未预约
        String instanceCoreUnordered; // 未预约
        String storageOrdered; // 已预约
        String storageUnordered; // 未预约

    }


}
