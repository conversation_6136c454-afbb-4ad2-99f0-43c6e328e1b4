package cloud.demand.app.modules.p2p.industry_demand.dto;

import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandVersionDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.wooutils.lang.DateUtils;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
public class UpdateVersionDTO {

    @NotNull(message = "id不能为空")
    private Long id;
    private String desc;
    @NotNull(message = "开放开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportOpenDate;
    @NotNull(message = "开放结束时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date demandImportCloseDate;

    public static IndustryDemandVersionDO toDO(UpdateVersionDTO dto) {
        IndustryDemandVersionDO industryDemandVersionDO = new IndustryDemandVersionDO();
        industryDemandVersionDO.setDesc(dto.getDesc() == null ? "" : dto.getDesc());
        industryDemandVersionDO.setDemandImportOpenDate(DateUtils.toLocalDate(dto.getDemandImportOpenDate()));
        industryDemandVersionDO.setDemandImportCloseDate(DateUtils.toLocalDate(dto.getDemandImportCloseDate()));
        industryDemandVersionDO.setId(dto.getId());
        return industryDemandVersionDO;
    }
}
