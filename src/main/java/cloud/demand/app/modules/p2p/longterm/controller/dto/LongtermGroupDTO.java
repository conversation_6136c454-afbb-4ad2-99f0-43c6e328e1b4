package cloud.demand.app.modules.p2p.longterm.controller.dto;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionDO;
import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupDO;
import cloud.demand.app.modules.p2p.longterm.entity.vo.LongtermVersionGroupWithVersionVO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupStatusEnum;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
public class LongtermGroupDTO {

    /**分组id*/
    private Long groupId;

    /**是否使用35年配置*/
    private Boolean if35 = false;

    /**版本编码*/
    private String versionCode;

    /**行业部门*/
    private String industryDept;

    /**业务分组*/
    private String bizGroup;

    /**版本范围：起始年份*/
    private Integer demandBeginYear;
    /**版本范围：结束年份*/
    private Integer demandEndYear;

    /**版本范围：35年起始年份*/
    private Integer extraBeginYear;
    /**版本范围：35年结束年份*/
    private Integer extraEndYear;

    /**状态code*/
    private String status;
    /**状态名称*/
    private String statusName;

    /**处理人*/
    private String currentProcessor;

    /**
     * 云运管13周干预情况
     */
    private Integer comdIntervened13week;

    private String comdIntervenedTime;

    public static LongtermGroupDTO trans(LongtermVersionGroupWithVersionVO group) {
        LongtermGroupDTO longtermGroupDTO = new LongtermGroupDTO();
        longtermGroupDTO.setGroupId(group.getId());
        longtermGroupDTO.setVersionCode(group.getVersionCode());
        longtermGroupDTO.setIndustryDept(group.getIndustryDept());
        longtermGroupDTO.setBizGroup(group.getBizGroup());
        LongtermVersionDO versionDO = group.getVersionDO();
        longtermGroupDTO.setDemandBeginYear(versionDO == null ? null : versionDO.getDemandBeginYear());
        longtermGroupDTO.setDemandEndYear(versionDO == null ? null : versionDO.getDemandEndYear());
        if (versionDO != null && versionDO.getExtraIndustries().contains(longtermGroupDTO.getIndustryDept())) {
            longtermGroupDTO.setIf35(true);
            longtermGroupDTO.setExtraBeginYear(versionDO.getExtraBeginYear());
            longtermGroupDTO.setExtraEndYear(versionDO.getExtraEndYear());
        }
        longtermGroupDTO.setStatus(group.getStatus());
        longtermGroupDTO.setStatusName(LongtermVersionGroupStatusEnum.getNameByCode(group.getStatus()));
        longtermGroupDTO.setCurrentProcessor(group.getCurrentProcessor());
        longtermGroupDTO.setComdIntervened13week(group.getComdIntervened13week() == null ? 0 : group.getComdIntervened13week());
        longtermGroupDTO.setComdIntervenedTime(DateUtils.format(group.getComdIntervenedTime()));
        return longtermGroupDTO;
    }


    public static LongtermGroupDTO trans(LongtermVersionGroupDO group, LongtermVersionDO versionDO,String currentProcessor) {
        LongtermGroupDTO longtermGroupDTO = new LongtermGroupDTO();
        longtermGroupDTO.setGroupId(group.getId());
        longtermGroupDTO.setVersionCode(group.getVersionCode());
        longtermGroupDTO.setIndustryDept(group.getIndustryDept());
        longtermGroupDTO.setBizGroup(group.getBizGroup());
        longtermGroupDTO.setDemandBeginYear(versionDO == null ? null : versionDO.getDemandBeginYear());
        longtermGroupDTO.setDemandEndYear(versionDO == null ? null : versionDO.getDemandEndYear());
        if (versionDO != null && versionDO.getExtraIndustries().contains(longtermGroupDTO.getIndustryDept())) {
            longtermGroupDTO.setIf35(true);
            longtermGroupDTO.setExtraBeginYear(versionDO.getExtraBeginYear());
            longtermGroupDTO.setExtraEndYear(versionDO.getExtraEndYear());
        }
        longtermGroupDTO.setStatus(group.getStatus());
        longtermGroupDTO.setStatusName(LongtermVersionGroupStatusEnum.getNameByCode(group.getStatus()));
        longtermGroupDTO.setCurrentProcessor(mergeProcessor(group.getCurrentProcessor(),currentProcessor));
        longtermGroupDTO.setComdIntervened13week(group.getComdIntervened13week() == null ? 0 : group.getComdIntervened13week());
        longtermGroupDTO.setComdIntervenedTime(DateUtils.format(group.getComdIntervenedTime()));
        return longtermGroupDTO;
    }

    public static String mergeProcessor(String groupProcessor, String currentProcessor) {
        Set<String> processorSet = new HashSet<>();
        if (StringUtils.isNotBlank(groupProcessor)) {
            List<String> list = Arrays.asList(groupProcessor.split(";"));
            processorSet.addAll(list);
        }

        if (StringUtils.isNotBlank(currentProcessor)) {
            List<String> list = Arrays.asList(currentProcessor.split(";"));
            processorSet.addAll(list);
        }
        if (processorSet.isEmpty()){
            return "";
        }
        return Strings.join(";",processorSet);
    }

}
