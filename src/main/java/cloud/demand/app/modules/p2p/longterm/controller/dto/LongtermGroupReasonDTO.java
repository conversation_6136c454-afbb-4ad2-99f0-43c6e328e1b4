package cloud.demand.app.modules.p2p.longterm.controller.dto;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionGroupReasonDO;
import cloud.demand.app.modules.p2p.longterm.key_accessor.v1_key_getter.LongtermReasonDimGK;
import lombok.Data;

@Data
public class LongtermGroupReasonDTO implements LongtermReasonDimGK {

    /** CVM/GPU/裸金属/PAAS */
    private String product;

    /** 年 */
    private Integer reasonYear;

    /** 推导逻辑 */
    private String reason;

    public static LongtermGroupReasonDTO from(LongtermVersionGroupReasonDO reasonDO) {
        LongtermGroupReasonDTO reasonDTO = new LongtermGroupReasonDTO();
        reasonDTO.setProduct(reasonDO.getProduct());
        reasonDTO.setReasonYear(reasonDO.getReasonYear());
        reasonDTO.setReason(reasonDO.getReason());
        return reasonDTO;
    }

    public static LongtermVersionGroupReasonDO toReasonDO(LongtermGroupReasonDTO dto) {
        LongtermVersionGroupReasonDO longtermVersionGroupReasonDO = new LongtermVersionGroupReasonDO();
        longtermVersionGroupReasonDO.setProduct(dto.getProduct());
        longtermVersionGroupReasonDO.setReasonYear(dto.getReasonYear());
        longtermVersionGroupReasonDO.setReason(dto.getReason());
        return longtermVersionGroupReasonDO;
    }
}
