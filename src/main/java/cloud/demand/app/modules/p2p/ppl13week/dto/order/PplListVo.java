package cloud.demand.app.modules.p2p.ppl13week.dto.order;

import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.req.SavePplDraftReq.DraftItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.DraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceEnum;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import io.vavr.Tuple2;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.nutz.lang.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Data
//@JsonInclude(Include.NON_NULL)
public class PplListVo {

    /**
     * ppl来源（PPL填报 / 系统自动补充）
     *
     * @see PplOrderSourceEnum
     */
    String pplSource;

    String changeType;
    String type;
    String pplOrder;
    String source;
    String demandType;// 需求类型
    String warZone;        // 战区
    String customerUin;      //客户UIN
    String customerShortName; // 客户简称
    String customerType;  // 客户类型
    String customerTypeName; // 客户类型名称
    String industryDept; // 行业部门
    String projectName;   // 项目名称
    String beginBuyDate;  // 开始日期
    String endBuyDate;    // 结束日期
    Boolean isOverdue = false;     //是否即将过期，作用于未转订单

    String approvalNode;          //流程节点
    String approvalStatus;          //流程节点
    Integer withdrawable = 0;     //是否可以撤回
    String approver;       //审批人

    Integer totalCore;    // 总核心数，单位“核”
    Integer totalGpu;    // 总卡数，单位“卡”
    Integer totalInstanceNum;    // 总台数，单位“台”

    Integer totalMemory;    // 总内存，单位“GB”
    BigDecimal totalCosStorage; // COS产品总容量，单位为 PB

    BigDecimal totalDatabaseStorage; // 数据库产品总容量，单位为 GB

    Integer totalCbsStorage; // CBS总容量，单位“GB”

    Integer preTotalCore = 0;// 更改之前的总核心数

    Integer preTotalGpu = 0;// 更改之前的总卡数 GPU

    Integer preTotalInstanceNum = 0;// 更改之前的总台数  裸金属

    Integer preTotalMemory;    // 更改之前的总内存

    BigDecimal preTotalCosStorage; // 更改之前的COS产品总容量

    BigDecimal preTotalDatabaseStorage; // 更改之前的数据库产品总容量

    Integer preTotalCbsStorage;    // 更改之前的CBS总容量

    Boolean isCanRevert; //是否可撤回
    String submitUser; //提单人
    String modifyReason = ""; // 调整原因
    Integer changeCore; //滚动刷新核数
    Integer changeGpu; //滚动刷新卡数
    Integer changeInstanceNum; //滚动刷新台数
    Integer changeMemory; // 滚动刷新内存
    BigDecimal changeCosStorage; // 滚动刷新COS产品总容量
    BigDecimal changeDatabaseStorage; // 滚动刷新数据库产品总容量
    Integer changeCbsStorage; // 滚动刷新CBS总容量
    BigDecimal winRate; //赢率

    String product; // 产品 产品
    List<DraftItemDTO> pplItems;

    Integer applyInstanceNum;
    Integer applyTotalCore;
    Integer applyTotalGpuNum;

    Integer notApplyInstanceNum;
    Integer notApplyTotalCore;
    Integer notApplyTotalGpuNum;

    private String orderConsensusStatus;

    private String orderConsensusSupplyPlan;

    private Boolean isCurrentVersion;

    private Date updateTime;

    private String appliedStatus;

    private String yunxiaoOrderId;

    private String currentOrderStatus;

    /**
     *  为 true 时，表示当前PPL是下期生效的数据。
     *  否则为当前版本
     */
    private boolean nextVersionValid = false;

    /**
     * 下期生效数据（审批流程中的PPL才有这个字段）。<br/>
     * 当{@link #nextVersionValid}为true时， {@link #nextVersionValidData} 不为 null 时，
     *              表示当前PPL是 存在已修改下期生效的数据，其中{@link #nextVersionValidData} 就是已修改下期生效的数据。<br/>
     * 当{@link #nextVersionValid}为true时，{@link #nextVersionValidData} 为 null 时，表示当前PPL是 新增下期生效的数据。<br/>
     */
    private PplListVo nextVersionValidData;

    public static List<VersionGroupItemResp.GroupItemDTO> transToGroupItemDTO(List<PplListVo> source) {
        List<VersionGroupItemResp.GroupItemDTO> target = new ArrayList<>();
        for (PplListVo pplListVo : source) {
            for (DraftItemDTO pplItem : pplListVo.getPplItems()) {
                VersionGroupItemResp.GroupItemDTO groupItemDTO = new VersionGroupItemResp.GroupItemDTO();
                BeanUtil.copyProperties(pplItem, groupItemDTO);
                BeanUtil.copyProperties(pplListVo, groupItemDTO);

                groupItemDTO.setBeginBuyDate(LocalDateTimeUtil.parseDate(pplItem.getBeginBuyDate()));
                if (Strings.isNotBlank(pplItem.getEndBuyDate())) {
                    groupItemDTO.setEndBuyDate(LocalDateTimeUtil.parseDate(pplItem.getEndBuyDate()));
                }
                if (Strings.isNotBlank(pplItem.getBeginElasticDate())) {
                    groupItemDTO.setBeginElasticDate(LocalTime.parse(pplItem.getBeginElasticDate()));
                }
                if (Strings.isNotBlank(pplItem.getEndElasticDate())) {
                    groupItemDTO.setEndElasticDate(LocalTime.parse(pplItem.getEndElasticDate()));
                }
                groupItemDTO.setWinRate(pplListVo.getWinRate());
                groupItemDTO.setWinRateStr(groupItemDTO.getWinRate() == null ? "" : groupItemDTO.getWinRate().setScale(0, RoundingMode.HALF_UP).intValue() + "%");
                groupItemDTO.setDemandTypeName(pplListVo.getDemandType());
                groupItemDTO.setCustomerTypeName(CustomerTypeEnum.getNameByCode(pplListVo.getCustomerType()));
                Tuple2<Integer, Integer> modelInfos = P2PInstanceModelParse.parseInstanceModel(
                        groupItemDTO.getInstanceModel());
                groupItemDTO.setInstanceModelCoreNum(modelInfos._1);
                groupItemDTO.setCoreNum(modelInfos._1);
                groupItemDTO.setInstanceModelRamNum(modelInfos._2);
                groupItemDTO.setAlternativeInstanceType(pplItem.getAlternativeInstanceType());
                groupItemDTO.setAlternativeZoneName(pplItem.getAlternativeZoneName());
                if (ListUtils.isNotEmpty(pplItem.getAlternativeZoneName())) {
                    groupItemDTO.setAlternativeZoneNamesJoin(String.join(";", pplItem.getAlternativeZoneName()));
                }
                groupItemDTO.setWinRate(pplItem.getWinRate());
                groupItemDTO.setWinRateStr(pplItem.getWinRate() == null ? "" : pplItem.getWinRate().setScale(0, RoundingMode.HALF_UP).intValue() + "%");
                groupItemDTO.setBizId(pplItem.getBizId());
                if (pplItem.getAfterNewApplyTotalCore() != null) {
                    groupItemDTO.setApplyTotalCore(pplItem.getAfterNewApplyTotalCore());
                }
                if (pplItem.getAfterNewApplyTotalGpuNum() != null) {
                    groupItemDTO.setApplyTotalGpuNum(pplItem.getAfterNewApplyTotalGpuNum().intValue());
                }
                target.add(groupItemDTO);
            }
        }

        return target;
    }

    /**
     * 计算一些pplOrder级别的总指标数据
     * @param oldAuditItems 原ppl明细数据，用于对比
     * @param calcPreData 是否需要计算前一版本的数据，false不计算。如果方法调用处已经有别的逻辑计算了，则传false，不在此方法内计算
     */
    public void calcAndSetOrderLevelIndex(List<? extends PplItemBaseDO> oldAuditItems, boolean calcPreData) {
        int totalCore = 0;
        int totalGpu = 0;
        int totalInstanceNum = 0;
        int totalMemory = 0;
        BigDecimal totalCosStorage = BigDecimal.ZERO;
        BigDecimal totalDatabaseStorage = BigDecimal.ZERO;
        int totalCbsStorage = 0;
        for (DraftItemDTO item : getPplItems()) {
            totalCore = totalCore + item.getTotalCoreNum();
            totalGpu = totalGpu + (ObjectUtils.isEmpty(item.getTotalGpuNum()) ? 0
                    : item.getTotalGpuNum().intValue());
            totalInstanceNum = totalInstanceNum + item.getInstanceNum();
            totalMemory += item.getTotalMemory() == null ? 0 : item.getTotalMemory();
            totalCosStorage = totalCosStorage.add(item.getTotalCosStorage() == null
                    ? BigDecimal.ZERO : item.getTotalCosStorage());
            totalDatabaseStorage = totalDatabaseStorage.add(item.getTotalDatabaseStorage() == null
                    ? BigDecimal.ZERO : item.getTotalDatabaseStorage());
            totalCbsStorage += item.getTotalDiskNum();
        }
        this.setTotalCore(totalCore);
        this.setTotalGpu(totalGpu);
        this.setTotalInstanceNum(totalInstanceNum);
        this.setTotalMemory(totalMemory);
        this.setTotalCosStorage(totalCosStorage);
        this.setTotalDatabaseStorage(totalDatabaseStorage);
        this.setTotalCbsStorage(totalCbsStorage);

        if (calcPreData) {
            this.setPreTotalCore(CollectionUtils.isEmpty(oldAuditItems) ? 0
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalCore).intValue());
            this.setPreTotalGpu(CollectionUtils.isEmpty(oldAuditItems) ? 0
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalGpuNum).intValue());
            this.setPreTotalInstanceNum(CollectionUtils.isEmpty(oldAuditItems) ? 0
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getInstanceNum).intValue());
            this.setPreTotalMemory(CollectionUtils.isEmpty(oldAuditItems) ? 0
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalMemory).intValue());
            this.setPreTotalCosStorage(CollectionUtils.isEmpty(oldAuditItems) ? BigDecimal.ZERO
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalCosStorage));
            this.setPreTotalDatabaseStorage(CollectionUtils.isEmpty(oldAuditItems) ? BigDecimal.ZERO
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalDatabaseStorage));
            this.setPreTotalCbsStorage(CollectionUtils.isEmpty(oldAuditItems) ? 0
                    : NumberUtils.sum(oldAuditItems, PplItemBaseDO::getTotalDisk).intValue());
        }

        this.setChangeCore(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType())) ? this.getTotalCore()
                        - (this.getPreTotalCore() == null ? 0 : this.getPreTotalCore())
                        : this.getTotalCore());
        this.setChangeGpu(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType())) ? this.getTotalGpu()
                        - (this.getPreTotalGpu() == null ? 0 : this.getPreTotalGpu())
                        : this.getTotalGpu());
        this.setChangeInstanceNum(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType())) ? this.getTotalInstanceNum()
                        - (this.getPreTotalInstanceNum() == null ? 0 : this.getPreTotalInstanceNum())
                        : this.getTotalInstanceNum());
        this.setChangeMemory(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType()))
                        ? this.getTotalMemory() - (this.getPreTotalMemory() == null ? 0 : this.getPreTotalMemory())
                        : this.getTotalMemory());
        this.setChangeCosStorage(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType()))
                        ? this.getTotalCosStorage().subtract(
                                this.getPreTotalCosStorage() == null ? BigDecimal.ZERO : this.getPreTotalCosStorage())
                        : this.getTotalCosStorage());
        this.setChangeDatabaseStorage(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType()))
                        ? this.getTotalDatabaseStorage().subtract(
                                this.getPreTotalDatabaseStorage() == null ? BigDecimal.ZERO : this.getPreTotalDatabaseStorage())
                        : this.getTotalDatabaseStorage());
        this.setChangeCbsStorage(
                (!DraftStatusEnum.INSERT.getCode().equals(this.getType()))
                        ? this.getTotalCbsStorage() - (this.getPreTotalCbsStorage() == null ? 0 : this.getPreTotalCbsStorage())
                        : this.getTotalCbsStorage());
    }


}
