package cloud.demand.app.modules.p2p.ppl13week.vo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import lombok.Data;
import lombok.ToString;

/**
 * 视图层返回的行业PPLItem
 */
@Data
@ToString
public class IndustryPplItemVO {

    /**
     * ppl需求id<br/>Column: [ppl_id]
     */
    private String pplId;

    /**
     * 需求状态<br/>Column: [status]
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum
     */
    private String status;

    /**
     * ppl单号<br/>Column: [ppl_order]
     */
    private String pplOrder;

    /**
     * 云霄预约单号<br/>Column: [yunxiao_order_id]
     */
    private String yunxiaoOrderId;

    /**
     * 云霄明细的主键id<br/>Column: [yunxiao_detail_id]
     */
    private Long yunxiaoDetailId;

    /**
     * 云霄预约单状态<br/>Column: [yunxiao_order_status]
     */
    private String yunxiaoOrderStatus;

    /**
     * 产品<br/>Column: [product]
     */
    private String product;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    private String demandType;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    private String demandScene;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    private String projectName;

    /**
     * 计费模式<br/>Column: [bill_type]
     */
    private String billType;

    /**
     * 赢率，百分比<br/>Column: [win_rate]
     */
    private BigDecimal winRate;

    /**
     * 开始购买日期<br/>Column: [begin_buy_date]
     */
    private LocalDate beginBuyDate;

    /**
     * 结束购买日期<br/>Column: [end_buy_date]
     */
    private LocalDate endBuyDate;

    /**
     * 弹性开始日期<br/>Column: [begin_elastic_date]
     */
    private LocalTime beginElasticDate;

    /**
     * 弹性结束日期<br/>Column: [end_elastic_date]
     */
    private LocalTime endElasticDate;

    /**
     * 特殊备注说明<br/>Column: [note]
     */
    private String note;

    /**
     * 地域，看看要不要存id<br/>Column: [region_name]
     */
    private String regionName;

    /**
     * 可用区，看看要不要存id<br/>Column: [zone_name]
     */
    private String zoneName;

    /**
     * 实例类型<br/>Column: [instance_type]
     */
    private String instanceType;

    /**
     * 实例规格<br/>Column: [instance_model]
     */
    private String instanceModel;

    /**
     * 实例数量<br/>Column: [instance_num]
     */
    private Integer instanceNum;

    /**
     * 可接受的其它实例类型，分号隔开<br/>Column: [alternative_instance_type]
     */
    private String alternativeInstanceType;

    /**
     * 亲和度类型<br/>Column: [affinity_type]
     */
    private String affinityType;

    /**
     * 亲和度值<br/>Column: [affinity_value]
     */
    private BigDecimal affinityValue;

    /**
     * 磁盘类型<br/>Column: [system_disk_type]
     */
    private String systemDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [system_disk_storage]
     */
    private Integer systemDiskStorage;

    /**
     * 磁盘块数<br/>Column: [system_disk_num]
     */
    private Integer systemDiskNum;

    /**
     * 磁盘类型<br/>Column: [data_disk_type]
     */
    private String dataDiskType;

    /**
     * 磁盘容量，单位G<br/>Column: [data_disk_storage]
     */
    private Integer dataDiskStorage;

    /**
     * 磁盘块数<br/>Column: [data_disk_num]
     */
    private Integer dataDiskNum;

    private Integer totalCore;

    private Integer totalDisk;

    /**
     * 应用角色<br/>Column: [app_role]
     */
    private String appRole;

    /**
     * gpu产品形态 cvm/裸金属 <br/>Column: [gpuProductType]
     */
    private String gpuProductType;

    /**
     * gpu型号 <br/>Column: [gpuType]
     */
    private String gpuType;

    /**
     * gpu卡数 <br/>Column: [gpuNum]
     */
    private BigDecimal gpuNum;

    /**
     * gpu总卡数 <br/>Column: [totalGpuNum]
     */
    private BigDecimal totalGpuNum;

    /**
     * 是否接受其他卡型<br/>Column: [isAcceptAdjust]
     */
    private Boolean isAcceptAdjust;

    /**
     * 接受GPU卡型 用;分割<br/>Column: [acceptGpu]
     */
    private String acceptGpu;

    /**
     * 业务场景<br/>Column: [bizScene]
     */
    private String bizScene;

    /**
     * 业务详情<br/>Column: [bizDetail]
     */
    private String bizDetail;

    /**
     * 使用时长<br/>Column: [serviceTime]
     */
    private String serviceTime;

    /**
     * 云霄预约单订单分类<br/>Column: [order_category]
     */
    private String orderCategory;

    /**
     * 唯一标识ID 战略客户部独有
     */
    private String bizId;

    /**
     * 包销时长（月）- 现废弃使用
     */
    private Integer saleDuration;

    /**
     * 是否CPQ（GPU相关）- 现废弃使用
     */
    private Boolean cpq;

    /**
     * 包销时长(年)（允许一位小数点，excel导入时最小填写单位为0.5）
     */
    private BigDecimal saleDurationYear;

    /**
     * 申请折扣(折)（填写范围为0.1~10）
     */
    private BigDecimal applyDiscount;

    /**
     * 商务进展（枚举值-CpqTypeEnum）
     */
    private String businessCpq;

    private String consensusStatus;

    /**
     * 共识的满足方式,多个满足方式用英文分号隔开
     * 分隔符：{@link cloud.demand.app.modules.p2p.ppl13week.constant.ConsensusConstant#CONSENSUS_DELIMITER}
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum
     */
    private String consensusMatchType;

    /**
     * ppl草稿单状态<br/>Column: [status]
     *
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum
     */
    private String draftStatus;

    private Long auditRecordId;
}
