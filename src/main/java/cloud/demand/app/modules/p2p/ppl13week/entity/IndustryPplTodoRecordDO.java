package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("Industry_ppl_todo_record")
public class IndustryPplTodoRecordDO extends BaseDO {

    /**
     * 行业版本id PplInnerProcessVersion的Id
     */
    @Column(value = "version_id")
    private Long versionId;

    /**
     * 节点Code
     */
    @Column(value = "node_code")
    private String nodeCode;

    /**
     * 产品
     */
    @Column(value = "product")
    private String product;

    /**
     * 行业
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 战区
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 客户
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * todo的 taskOrderId
     */
    @Column(value = "biz_key")
    private String bizKey;

    /**
     * 接收用户
     */

    @Column(value = "accept_user")
    private String acceptUser;

    /**
     * 接收信息
     */

    @Column(value = "detail")
    private String detail;

    /**
     * 0-待推送 1-待审批 2-已完成
     */

    @Column(value = "push_status")
    private Integer pushStatus;

    @Column(value = "error_message")
    private String errorMessage;


}