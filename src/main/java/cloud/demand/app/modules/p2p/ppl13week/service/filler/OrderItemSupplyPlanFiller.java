package cloud.demand.app.modules.p2p.ppl13week.service.filler;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 *  订单明细数据，填充供应方案满足核心数、实例数，以及订单满足度的相关满足量
 */
public interface OrderItemSupplyPlanFiller {

    /**
     * 提供：订单明细号
     */
    String provideOrderNumberId();

    /**
     * 提供：订单明细需求核心数，用于计算订单满足度 <br/>
     * 为 null 时不会计算订单满足度
     */
    default Integer provideOrderItemTotalCore() {
        return null;
    }

    /**
     *  填充字段：供应方案-大盘满足核心数
     */
    void fillSatisfySupplyCore(Integer satisfySupplyCore);

    /**
     *  填充字段：供应方案-采购满足核心数
     */
    void fillBuySupplyCore(Integer buySupplyCore);

    /**
     *  填充字段：供应方案-搬迁满足核心数
     */
    void fillMoveSupplyCore(Integer moveSupplyCore);

    /**
     *  填充字段：供应方案-大盘满足实例数
     */
    void fillSatisfySupplyInstanceNum(Integer satisfySupplyInstanceNum);

    /**
     *  填充字段：供应方案-采购满足实例数
     */
    void fillBuySupplyInstanceNum(Integer buySupplyInstanceNum);

    /**
     *  填充字段：供应方案-搬迁满足实例数
     */
    void fillMoveSupplyInstanceNum(Integer moveSupplyInstanceNum);

    /**
     *  填充字段：最早实际交付日期
     */
    default void fillEarliestSupplyDate(LocalDate earliestSupplyDate) {
        // non
    }

    /**
     *  填充字段：最晚实际交付日期
     */
    default void fillLatestSupplyDate(LocalDate latestSupplyDate) {
        // non
    }

    /**
     *  填充字段：订单满足-大盘满足核心数
     */
    default void fillOrderItemSatisfyMatchedCore(BigDecimal satisfyMatchedCore) {
        // non
    }

    /**
     *  填充字段：订单满足-采购满足核心数
     */
    default void fillOrderItemBuyMatchedCore(BigDecimal buyMatchedCore) {
        // non
    }

    /**
     *  填充字段：订单满足-搬迁满足核心数
     */
    default void fillOrderItemMoveMatchedCore(BigDecimal moveMatchedCore) {
        // non
    }

    /**
     *  填充字段：订单满足-订单满足总核心数
     */
    default void fillOrderItemTotalMatchedCore(BigDecimal totalMatchedCore) {
        // non
    }

    /**
     *  填充字段：订单满足率
     */
    default void fillOrderItemTotalMatchedRate(BigDecimal totalMatchedRate) {
        // non
    }

}
