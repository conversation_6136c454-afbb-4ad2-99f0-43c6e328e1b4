package cloud.demand.app.modules.p2p.product_demand.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Set;
import java.util.function.Supplier;
import yunti.boot.config.DynamicProperty;

public class DynamicProperties {

    private static final ObjectMapper object_mapper = new ObjectMapper();

    private static final Supplier<String> tcres_url =
            DynamicProperty.create("product-annual-demand.tcres-url", "");

    private static final Supplier<Set<String>> obs_project_type_enum_set =
            DynamicProperty.create("product-annual-demand.obs_project_type-enum-set",
                    "[\"常规项目\", \"机房裁撤\"]",
                    e -> object_mapper.readValue(e, new TypeReference<Set<String>>() {
                    }));


    public static Set<String> obsProjectTypeEnumSet() {
        return obs_project_type_enum_set.get();
    }

    public static String tcresUrl() {
        return tcres_url.get();
    }
}
