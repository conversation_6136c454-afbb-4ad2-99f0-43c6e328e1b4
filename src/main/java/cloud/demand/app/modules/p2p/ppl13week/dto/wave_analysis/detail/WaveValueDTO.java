package cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WaveValueDTO {

    private String dataColumn;
    private String value;
    private Info info;

    // hover提示信息
    private String hoverInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Info {

        private String lastVersion;
        private String changeNum;
        private Double percentage;
        private List<Detail> details;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Detail {

        private String name;
        private String changeNum;
        private Double percentage;
    }
}
