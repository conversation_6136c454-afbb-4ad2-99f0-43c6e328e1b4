package cloud.demand.app.modules.p2p.product_demand.service.impl;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.modules.p2p.product_demand.dto.ApproveFlowDto;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandApproveRecordDO;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionGroupDO;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandAuthRoleStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.enums.ProductDemandGroupStatusEnum;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandAuthService;
import cloud.demand.app.modules.p2p.product_demand.service.ProductTodoService;
import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import yunti.boot.client.JsonrpcClient;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;

@Slf4j
@Service
public class ProductTodoServiceImpl implements ProductTodoService {

    @Resource
    ProductDemandAuthService productDemandAuthService;
    @Resource
    JsonrpcClient jsonrpcClient;
    @Resource
    Alert alert;

    Supplier<String> todoUrlSupplier = DynamicProperty.create("app.url.todo-url", "");
    Supplier<String> domainSupplier = DynamicProperty.create("app.product.domain", "");

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public ApproveFlowDto productDemandApproveFlow(Long groupId) {
        ProductDemandVersionGroupDO demandGroup = demandDBHelper.getOne(ProductDemandVersionGroupDO.class,
                "where id=?", groupId);
        if (demandGroup == null) {
            throw new BizException("单据状态已发生变化，请刷新页面后重试.");
        }

        ApproveFlowDto dto = new ApproveFlowDto();
        ProductDemandGroupStatusEnum groupStatusEnum = ProductDemandGroupStatusEnum.getByCode(demandGroup.getStatus());
        dto.setCurStatus(demandGroup.getStatus());
        dto.setCurStatusName(groupStatusEnum.getName());
        String handler = getApproveByStatus(groupStatusEnum,
                demandGroup.getPlanProduct(),
                demandGroup.getCreator());
        dto.setHandler(handler);

        List ls = new ArrayList();
        for (ProductDemandGroupStatusEnum em : ProductDemandGroupStatusEnum.APPROVE_STEP_ENUM_ARRAY) {
            ls.add(ImmutableMap.of("status", em.getCode(), "statusName", em.getName()));
        }
        dto.setFlow(ls);
        dto.setLogs(listApproveLogs(groupId));
        return dto;
    }

    @Override
    public List<ProductDemandApproveRecordDO> listApproveLogs(Long groupId, boolean withAll) {
        val list = demandDBHelper.getAll(ProductDemandApproveRecordDO.class, "where demand_group_id = ?", groupId);
        if (withAll) {
            return list;
        }
        for (int i = list.size() - 1; i >= 0; i--) {
            if (ProductDemandGroupStatusEnum.INPUT.getCode().equals(list.get(i).getStatus())) {
                return list.subList(i, list.size());
            }
        }
        return Lists.newArrayList();
    }

    @Override
    public boolean checkOrderStepCanApprove(String orderTaskId, String operator) {
        val arr = orderTaskId.split("-");
        ProductDemandVersionGroupDO demandGroup = demandDBHelper.getOne(ProductDemandVersionGroupDO.class,
                "where id=?", arr[1]);
        if (demandGroup == null || !demandGroup.getStatus().equals(arr[2])) {
            throw new BizException("单据状态已发生变化，请刷新页面后重试.");
        }

        String curHandler = getApproveByStatus(ProductDemandGroupStatusEnum.getByCode(demandGroup.getStatus()),
                demandGroup.getPlanProduct(), operator);

        if (!Splitter.on(";").omitEmptyStrings().splitToList(curHandler).contains(operator)) {
            throw new BizException("没有当前节点审批权限.");
        }
        return true;
    }

    @Override
    public void startProductDemandApproveFlow(long groupId, String userName) {
        ProductDemandVersionGroupDO demandGroup = demandDBHelper.getOne(ProductDemandVersionGroupDO.class,
                "where id=?", groupId);
        if (demandGroup == null) {
            throw new BizException("需求组不存在");
        }
        if (demandGroup.getStatus().equals(ProductDemandGroupStatusEnum.INPUT.getCode()) || demandGroup.getStatus()
                .equals(ProductDemandGroupStatusEnum.REJECT.getCode())) {
            demandGroup.setStatus(ProductDemandGroupStatusEnum.INPUT.getCode());
            demandGroup.setExecCount(demandGroup.getExecCount() + 1);
            demandDBHelper.update(demandGroup);

            ApprovalMessageBody msg = new ApprovalMessageBody();
            String orderTaskId =
                    "ProductDemand_" + demandGroup.getExecCount() + "-" + demandGroup.getId() + "-"
                            + demandGroup.getStatus();
            msg.setApproveMemo("发起审批");
            msg.setApprover(userName);
            msg.setApproverAppOrder(demandGroup.getDemandVersion() + "-" + demandGroup.getId());
            msg.setApproverOrder(orderTaskId);
            msg.setApproveResult(0);
            doProductDemandApprove(msg);
        } else {
            throw new BizException("当前节点没法发起审批.期望状态【需求提交】【审批驳回】");
        }
    }

    @Override
    public void doProductDemandApprove(ApprovalMessageBody msg) {
        val ls = Splitter.on("-").splitToList(msg.getApproverOrder());
        if (ls.size() != 3) {
            //tip
            return;
        }
        val demandGroup = demandDBHelper.getOne(ProductDemandVersionGroupDO.class, "where id=?", ls.get(1));

        String curStatus = ls.get(2);
        if (!demandGroup.getStatus().equals(curStatus)) {
            log.warn("单据当前状态 和审批状态不一致,忽略");
            return;
        }

        try {
            if (demandGroup.getStatus().equals(curStatus)) {
                boolean pass = (msg.getApproveResult() == 0);
                saveApproveLog(msg);
                ProductDemandGroupStatusEnum next;
                if (!pass) {
                    next = ProductDemandGroupStatusEnum.REJECT;
                    alert.sendRtx("jackycjchen;kaijiazhang", "物理机需求全年版本流程驳回通知",
                            String.format("物理机需求全年版本【%s】，规划产品【%s】的流程驳回，请关注", demandGroup.getDemandVersion(), demandGroup.getPlanProduct()));
                } else {
                    if (ProductDemandGroupStatusEnum.INPUT.getCode().equals(curStatus)
                            || ProductDemandGroupStatusEnum.REJECT.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.PRODUCT_MANAGER_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.PRODUCT_MANAGER_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.PRODUCT_DIRECTOR_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.PRODUCT_DIRECTOR_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.PRODUCT_GM_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.PRODUCT_GM_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.COMD_MANAGER_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.COMD_MANAGER_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.COMD_DIRECTOR_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.COMD_DIRECTOR_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.COMD_GM_APPROVE;
                    } else if (ProductDemandGroupStatusEnum.COMD_GM_APPROVE.getCode().equals(curStatus)) {
                        next = ProductDemandGroupStatusEnum.COMPLETED;
                    } else if (ProductDemandGroupStatusEnum.COMPLETED.getCode()
                            .equals(curStatus)) { // 避免流转unknown,多次审批都应该是幂等的
                        next = ProductDemandGroupStatusEnum.COMPLETED;
                    } else {
                        next = ProductDemandGroupStatusEnum.UNKNOWN;
                    }
                }
                // 更新单据状态
                demandGroup.setStatus(next.getCode());
                String currentHandler = getApproveByStatus(next, demandGroup.getPlanProduct(),
                        demandGroup.getCreator());
                demandGroup.setCurrentHandler(currentHandler);
                demandDBHelper.update(demandGroup);

                if (pass) {
                    //下发下一步审批的待办
                    makeProductDemandApprove(demandGroup);
                }
            } else {
                log.warn("当前状态和单据状态不一致，忽略 .. 单：{}->批：{}", demandGroup.getStatus(), curStatus);
            }
        } catch (Exception e) {
            log.error("执行审批失败", e);
        } finally {
            //关闭当前节点的任务
            callTodoSubmitTaskResult(msg);
        }
    }

    @Override
    public String getApproveByStatus(ProductDemandGroupStatusEnum status, String planProduct,
            String creator) {
        String handler = "";
        switch (status) {
            case INPUT:
            case REJECT:
                handler = creator;
                break;
            case PRODUCT_MANAGER_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.PRODUCT_MANAGER,
                        planProduct);
                break;
            case PRODUCT_DIRECTOR_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.PRODUCT_DIRECTOR,
                        planProduct);
                break;
            case PRODUCT_GM_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.PRODUCT_GM,
                        planProduct);
                break;
            case COMD_MANAGER_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.COMD_MANAGER,
                        planProduct);
                break;
            case COMD_DIRECTOR_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.COMD_DIRECTOR,
                        planProduct);
                break;
            case COMD_GM_APPROVE:
                handler = productDemandAuthService.getUserByRole(ProductDemandAuthRoleStatusEnum.COMD_GM, planProduct);
                break;
            default:
                break;
        }
        return handler;
    }

    /**
     * 发送单据当前节点的待办任务
     *
     * @param groupDO
     */
    public void makeProductDemandApprove(ProductDemandVersionGroupDO groupDO) {
        val approvalData = new ApprovalData();
        val status = ProductDemandGroupStatusEnum.getByCode(groupDO.getStatus());

        String orderId = "ProductDemand_" + groupDO.getExecCount() + "-" + groupDO.getId();
        String orderTaskId =
                orderId + "-" + status.getCode();
        String formUrl = domainSupplier.get() + "/product/approval/" + groupDO.getId();

        approvalData.setOrderId(groupDO.getDemandVersion() + "-" + groupDO.getId());
        approvalData.setTaskId(orderTaskId);
        approvalData.setSystem("YUNTI");
        approvalData.setSourceApp("产品全年需求");
        approvalData.setSourceEvent(status.getName());
        approvalData.setSourceAppCnName("云需求管理");
        approvalData.setActivity(status.getName());
        approvalData.setHandler(groupDO.getCurrentHandler());
        approvalData.setFormUrl(formUrl);
        approvalData.setIsCallBackApi(0);

        List<ListView> listViewList = Arrays.asList(
                new ListView("当前版本",
                        groupDO.getDemandVersion()),
                new ListView("规划产品",
                        groupDO.getPlanProduct()),
                new ListView("提单人",
                        groupDO.getSubmitUser()));
        approvalData.setListView(listViewList);
        callTodo(approvalData);
    }

    /**
     * 审批日志入库
     *
     * @param msg
     */
    void saveApproveLog(ApprovalMessageBody msg) {
        val ls = Splitter.on("-").splitToList(msg.getApproverOrder());
        if (ls.size() >= 3) {
            Long gid = Long.parseLong(ls.get(1));
            ProductDemandApproveRecordDO rdo = new ProductDemandApproveRecordDO();
            rdo.setApprover(msg.getApprover());
            rdo.setApproveMsg(msg.getApproveMemo());
            rdo.setStatus(ls.get(2));
            rdo.setAction(msg.getApproveResult() == 0 ? "通过" : "驳回");
            rdo.setDemandGroupId(gid);
            //todo 如果需要存储当前节点单据的快照， 可以在这里查询下单的所有信息存储起来
            //rdo.setExtJson();
            demandDBHelper.insert(rdo);
        }
    }


    /**
     * 创建审批单据
     *
     * @param request
     */
    public void callTodo(ApprovalData request) {
        request.setCallBackUrl(domainSupplier.get() + "/cloud-demand-app/api/todo");
        String resp = jsonrpcClient.jsonrpc("createTask").uri(todoUrlSupplier.get()).id(UUID.randomUUID().toString())
                .params(request).noInjectServletRequest().postForObject(String.class);
        log.info("callTodo result: " + resp);
    }

    /**
     * 提交审批动作完成给taskcenter
     *
     * @param messageBody
     */
    public void callTodoSubmitTaskResult(
            ApprovalMessageBody messageBody) {
        Map params = ImmutableMap.of("taskId", messageBody.getApproverOrder(), "processResult", 0, "approver",
                messageBody.getApprover(), "approveResult", messageBody.getApproveResult(), "msg",
                messageBody.getApproveMemo());
        log.info("submitTaskResult params = {}", params);
        String resp = jsonrpcClient.jsonrpc("submitTaskResult").uri(todoUrlSupplier.get())
                .id(UUID.randomUUID().toString()).params(params).noInjectServletRequest().postForObject(String.class);
        log.info("submitTaskResult result = {}", resp);

    }
}
