package cloud.demand.app.modules.p2p.ppl13week.enums;

public enum CrpNoticeContentTemplateEnum {

    /**
     *  使用简单的占位符 {}
     */
    simple("simple"),

    /**
     *  使用springEL表达式
     */
    springEl("springEl"),

    /**
     *  使用thymeleaf模版引擎
     */
    thymeleaf("thymeleaf"),

    /**
     * 使用freemarker模版引擎
     * */
    freemarker("freemarker"),
    ;

    private final String code;

    CrpNoticeContentTemplateEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static CrpNoticeContentTemplateEnum getByCode(String code) {
        for (CrpNoticeContentTemplateEnum e : CrpNoticeContentTemplateEnum.values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
