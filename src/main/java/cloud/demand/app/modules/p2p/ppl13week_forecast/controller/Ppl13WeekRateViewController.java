package cloud.demand.app.modules.p2p.ppl13week_forecast.controller;


import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotCommonReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySplitDetailForPplReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13WeekRateViewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;


/**
 * <AUTHOR>
 */
@JsonrpcController("/ppl13week-forecast")
@Slf4j
public class Ppl13WeekRateViewController {

    @Resource
    Ppl13WeekRateViewService ppl13WeekRateViewService;

    @RequestMapping
    public QueryRateViewFittingPlotRsp queryRateViewFittingPlot(
            @Valid @JsonrpcParam QueryRateViewFittingPlotReq req,
            @RequestParam(value = "useSwap", defaultValue = "false") Boolean useSwap) {
        req.setUseSwap(useSwap);
        return ppl13WeekRateViewService.queryRateViewFittingPlot(req);
    }

    @RequestMapping
    public QueryRateViewFittingPlotRsp queryRateViewFittingPlotForMrp(
            @Valid @JsonrpcParam QueryRateViewFittingPlotCommonReq req) {
        return ppl13WeekRateViewService.queryRateViewFittingPlotForMrp(req);
    }

    @RequestMapping
    public QuerySplitDetailForPplResp querySplitDetailForPpl(
            @Valid @JsonrpcParam QuerySplitDetailForPplReq req) {
        return ppl13WeekRateViewService.querySplitDetailForPpl(req);
    }

    @RequestMapping
    public QueryRateViewDetailRsp queryRateViewDetail(
            @Valid @JsonrpcParam QueryRateViewDetailReq req,
            @RequestParam(value = "useSwap", defaultValue = "false") Boolean useSwap) {
        req.setUseSwap(useSwap);
        return ppl13WeekRateViewService.queryRateViewDetail(req);
    }


    

}
