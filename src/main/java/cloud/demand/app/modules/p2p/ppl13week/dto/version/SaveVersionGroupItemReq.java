package cloud.demand.app.modules.p2p.ppl13week.dto.version;

import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import com.pugwoo.wooutils.string.StringTools;
import java.time.LocalTime;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class SaveVersionGroupItemReq {

    /**分组id，必填*/
    @NotNull(message = "分组id必须提供")
    private Long groupId;

    /**
     * 不支持修改单据数据
     */
    private List<Item> editItems;

    /**
     *  新增一行, excel 导入
     *
     *  包含创建单据信息
     *
     */
    @NotNull(message = "行业部门必须提供")
    private String industryDept;

    List<GroupItemDTO> insertData;

    /**
     * 修改明细
     */
    @Data
    public static class Item {
        /**重要，编辑类型，这里不支持插入insert
         * @see cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum
         * */
        private String editType;

        /**
         * 导入数据的方式
         */
        private String importType;

        private String pplId; // ppl需求id

        private String product;
        private String demandType; // 需求类型

        private String demandScene; // 需求场景
        private String projectName; // 项目名称
        private String billType; // 计费模式
        private BigDecimal winRate; // 赢率

        private LocalDate beginBuyDate;
        private LocalDate endBuyDate;
        private LocalTime beginElasticDate;
        private LocalTime endElasticDate;

        private String note;

        private String regionName; // 地域
        private String zoneName; // 可用区

        private String instanceType; // 实例类型
        private String instanceModel; // 实例规格
        private Integer instanceNum; // 实例数量
        private List<String> alternativeInstanceType; // 兼容

        private String affinityType;
        private BigDecimal affinityValue;

        private String systemDiskType; // 系统盘类型
        private Integer systemDiskStorage; // 系统盘容量，G
        private Integer systemDiskNum; // 系统盘块数
        private String dataDiskType; // 数据盘类型
        private Integer dataDiskStorage;
        private Integer dataDiskNum;

        public void fill(PplVersionGroupRecordItemDO item) {
            item.setProduct(getProduct());
            item.setDemandType(getDemandType());
            item.setDemandScene(getDemandScene());
            item.setProjectName(getProjectName());
            item.setBillType(getBillType());
            item.setWinRate(getWinRate());
            item.setBeginBuyDate(getBeginBuyDate());
            item.setEndBuyDate(getEndBuyDate());
            item.setBeginElasticDate(getBeginElasticDate());
            item.setEndElasticDate(getEndElasticDate());
            item.setNote(getNote());
            item.setRegionName(getRegionName());
            item.setZoneName(getZoneName());
            item.setInstanceType(getInstanceType());
            item.setInstanceModel(getInstanceModel());
            item.setInstanceNum(getInstanceNum());
            item.setAlternativeInstanceType(StringTools.join(getAlternativeInstanceType(), ";"));
            item.setAffinityType(getAffinityType());
            item.setAffinityValue(getAffinityValue());
            item.setSystemDiskType(getSystemDiskType());
            item.setSystemDiskStorage(getSystemDiskStorage());
            item.setSystemDiskNum(getSystemDiskNum());
            item.setDataDiskType(getDataDiskType());
            item.setDataDiskStorage(getDataDiskStorage());
            item.setDataDiskNum(getDataDiskNum());
        }
    }

}
