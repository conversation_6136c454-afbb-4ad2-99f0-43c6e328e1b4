package cloud.demand.app.modules.p2p.ppl13week.service.excel.inner_process;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelResultDataAfterConvertChecker;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.PermissionService;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.excel.ImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplImportExcelDTO.QueryInfoByUinRspHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.excel.AbstractGeneralPplExcelParseService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public abstract class AbstractInnerPplExcelParseService extends AbstractGeneralPplExcelParseService implements
        PplInnerExcelParseService {

    @Resource
    private PplInnerExcelParseServiceAdapter adapter;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PermissionService permissionService;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplDictService pplDictService;

    @PostConstruct
    public void init() {
        adapter.registry(this);
    }


    protected void checkImportPermission(String industryDept, String product, String username) {
        if (permissionService.checkIsAdmin(username)) {
            return;
        }
        // 校验13周预测人权限
        String userByRole = permissionService.getUserByRole(
                IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE.getCode(), industryDept, product);
        if (!userByRole.contains(username)) {
            throw new BizException("您没有该行业产品导入的权限，请联系管理员 kaijiazhang 添加");
        }
    }

    /**
     * 获取13周录入人权限
     *
     * @param username
     * @return
     */
    protected IndustryDemandAuthDO getDemandAuth(String username) {
        return permissionService.getUserByRole(IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE.getCode(), username);
    }

    protected List<String> getIndustryWarZone(String industryDept) {
        Map<String, Set<String>> industryWarZoneMap = industryDemandDictService.getIndustryWarZoneMap();
        Set<String> warZone = industryWarZoneMap.get(industryDept);
        if (CollectionUtils.isEmpty(warZone)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(warZone);
    }

    protected List<String> queryCustomerList(String industryDept) {
        List<IndustryDemandIndustryWarZoneDictDO> all = demandDBHelper.
                getAll(IndustryDemandIndustryWarZoneDictDO.class, "where industry = ?", industryDept);
        if (CollectionUtils.isEmpty(all)) {
            throw new BizException("当前部门未配置客户关联关系,无法导入");
        }
        List<String> customerList = all.stream().filter(v -> Strings.isNotBlank(v.getCustomerName()))
                .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerList)) {
            throw new BizException("当前部门未配置客户关联关系，无法导入");
        }
        return customerList;
    }

    protected void checkWarZone(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, String industryDept) {
        PplItemImportRsp.ErrorMessage error = null;
        if (!industryDept.equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())) {
            error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getWarZone);
            errors.add(error);

            // check 各行业的战区
            List<String> industryWarZone = industryDemandDictService.getIndustryWarZone(industryDept);
            error = oneData.makeErrorIfNotContain(row, PplImportExcelDTO::getWarZone, industryWarZone);
            errors.add(error);
        }
    }

    // 各时间参数校验
    protected DateDTO checkInnerTime(List<PplItemImportRsp.ErrorMessage> errors,
            PplImportExcelDTO oneData, Integer row, DateUtils.YearMonth startYearMonth,
            DateUtils.YearMonth endYearMonth) {
        boolean beginSuccess = false;
        PplItemImportRsp.ErrorMessage error = null;
        int year = 0, month = 0;
        String yearMonth = null;
        LocalDate beginBuyDateRet = null;
        LocalDate endBuyDateRet = null;
        LocalTime beginElasticDateRet = null;
        LocalTime endElasticDateRet = null;
        PplDemandTypeEnum demandTypeEnum = PplDemandTypeEnum.getByName(oneData.getDemandTypeName());
        DateDTO dateDTO = new DateDTO();
        String beginBuyDate = oneData.getBeginBuyDate();
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBeginBuyDate);
        errors.add(error);
        if (error == null) {
            LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
            if (localDate == null) {
                error = makeError(row, PplImportExcelDTO::getBeginBuyDate, "开始日期解析出错");
                errors.add(error);
            } else {
                beginBuyDateRet = localDate;
                year = localDate.getYear();
                month = localDate.getMonth().getValue();
                yearMonth = com.pugwoo.wooutils.lang.DateUtils.format(localDate, "yyyy-MM");
                // 23/3/6 GPU录入和CVM录入的冲突点， 临时处理，逻辑确定后需马上修正。 isGpu ? month + 1 : month
                // 23/5/5 去除 ⬆️ 允许录入前一个月的逻辑
                if (startYearMonth.getYear() > year ||
                        (startYearMonth.getYear() == year && startYearMonth.getMonth() > month)) {
                    String info = "开始日期:" + yearMonth + "  <  导入选择需求开始年月:" + startYearMonth.toDateStr();
                    error = makeError(row, PplImportExcelDTO::getBeginBuyDate, info);
                    errors.add(error);
                }
                beginSuccess = true;
            }
        }

        String endBuyDate = oneData.getEndBuyDate();
        error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getEndBuyDate);
        errors.add(error);
        if (error == null) {
            LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(endBuyDate);
            endBuyDateRet = localDate;
            if (localDate == null) {
                error = makeError(row, PplImportExcelDTO::getEndBuyDate, "结束日期解析出错");
                errors.add(error);
            } else {
                if (beginSuccess) {
                    LocalDate beginLocalDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
                    if (beginLocalDate.compareTo(localDate) > 0) {
                        error = makeError(row, PplImportExcelDTO::getEndBuyDate, "结束日期比开始日期小");
                        errors.add(error);
                    }

                    if (Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.NEW.getName())
                            || Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.RETURN.getName())) {
//                            https://zhiyan.woa.com/requirement/6756/story/#/cloudrm-1186?tab=info&story_tab=info

                        Period between = Period.between(localDate.withDayOfMonth(1),
                                beginLocalDate.withDayOfMonth(1));
                        if (Math.abs(between.getMonths()) > 1) {
                            error = makeError(row, PplImportExcelDTO::getEndBuyDate,
                                    "新增需求/退回需求的月份差值大于1： 结束购买日期年月 - 开始购买日期年月 > 1");
                            errors.add(error);
                        }
                    }
                }
            }
        }

        if (demandTypeEnum != null) {
            if (demandTypeEnum == PplDemandTypeEnum.ELASTIC) {
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getBeginElasticDate);
                errors.add(error);
                error = oneData.makeErrorIfBlank(row, PplImportExcelDTO::getEndElasticDate);
                errors.add(error);
                String beginElasticDate = oneData.getBeginElasticDate();
                LocalTime localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(beginElasticDate);
                beginElasticDateRet = localTime;
                if (localTime == null) {
                    error = makeError(row, PplImportExcelDTO::getBeginElasticDate, "弹性开始日期解析出错");
                    errors.add(error);
                }
                String endElasticDate = oneData.getEndElasticDate();
                localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(endElasticDate);
                endElasticDateRet = localTime;
                if (localTime == null) {
                    error = makeError(row, PplImportExcelDTO::getEndElasticDate, "弹性结束日期解析出错");
                    errors.add(error);
                }
            }
        }

        // 内部流程新增
        LocalDate beginDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(oneData.getBeginBuyDate());
        LocalDate endDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(oneData.getEndBuyDate());
//
//        LocalDate nextMonth = LocalDate.now().plusMonths(1);
//        LocalDate nextMonthDay1 = LocalDate.of(nextMonth.getYear(), nextMonth.getMonthValue(), 1);
//        if (beginDate != null && endDate != null && beginDate.isBefore(nextMonthDay1)) {
//            error = makeError(row, PplImportExcelDTO::getBeginBuyDate, "开始购买时间需为当前月份的下个月");
//            errors.add(error);
//        }

        if (beginDate != null && endDate != null && demandTypeEnum.getCode().equals(PplDemandTypeEnum.NEW.getCode())) {
            LocalDate limitDate = beginDate.plusDays(28);
            if (limitDate.isBefore(endDate) || limitDate.toString().equals(endDate.toString())) {
                error = makeError(row, PplImportExcelDTO::getEndBuyDate,
                        "新增需求，结束购买时间与开始购买时间间隔需小于28天");
                errors.add(error);
            }
        }
//        else if (beginDate != null && endDate != null && demandTypeEnum.getCode()
//                .equals(PplDemandTypeEnum.ELASTIC.getCode())) {
//            LocalDate limitDate = beginDate.plusDays(90);
//            if (limitDate.isBefore(endDate) || limitDate.toString().equals(endDate.toString())) {
//                error = makeError(row, PplImportExcelDTO::getEndBuyDate,
//                        "弹性需求，结束购买时间与开始购买时间间隔需小于90天");
//                errors.add(error);
//            }
//        }

        dateDTO.setYear(year);
        dateDTO.setMonth(month);
        dateDTO.setBeginBuyDateRet(beginBuyDateRet);
        dateDTO.setEndBuyDateRet(endBuyDateRet);
        dateDTO.setBeginElasticDateRet(beginElasticDateRet);
        dateDTO.setEndElasticDateRet(endElasticDateRet);
        dateDTO.setYearMonth(yearMonth);
        return dateDTO;
    }

    @Override
    protected GroupItemDTO convert(PplImportExcelDTO oneData, String industryDept, String product,
            DateDTO dateDTO, QueryInfoByUinRsp queryInfoByUinRsp) {
        GroupItemDTO tmp = super.convert(oneData, industryDept, product, dateDTO, queryInfoByUinRsp);
        if (queryInfoByUinRsp != null && queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)) {
            if (industryDept.equals(IndustryDeptEnum.SMART_INDUSTRY_ONE.getName())) {
                tmp.setWarZone(queryInfoByUinRsp.getWarZone());
            }
        }
        tmp.setStatus(PplItemStatusEnum.VALID.getCode());
        tmp.setStatusName(PplItemStatusEnum.VALID.getName());
        return tmp;
    }

    /**
     * 行业录入流程中，uin、客户类型、客户简称混合校验，设置QueryInfoByUinRsp、以及智慧行业一部战区  <br/>
     * 需要配合 {@link QueryInfoByUinRspHandler} 使用
     */
    public class PplExcelResultHandler implements ExcelResultDataAfterConvertChecker<PplImportExcelDTO> {

        protected final String industryDept;

        protected final YearMonth startYearMonth;

        protected final YearMonth endYearMonth;

        protected final String product;

        protected final ImportExcelDTO importExcelDTO;

        protected final List<GroupItemDTO> result = new ArrayList<>();

        protected Set<String> pplIdList;

        public PplExcelResultHandler(ImportExcelDTO importExcelDTO, Set<String> pplIdSet) {
            if (importExcelDTO == null) {
                throw BizException.makeThrow("EXCEL导入PPL请求参数不能为空");
            }
            this.startYearMonth = DateUtils.parse(importExcelDTO.getStartYearMonth());
            this.endYearMonth = DateUtils.parse(importExcelDTO.getEndYearMonth());
            this.industryDept = importExcelDTO.getIndustryDept();
            this.product = importExcelDTO.getProduct();
            this.importExcelDTO = importExcelDTO;
            if (pplIdSet != null) {
                pplIdList = pplIdSet;
            }
        }

        @Override
        @SneakyThrows
        public <R extends PplImportExcelDTO> void checkResultDataAfterConvert(List<R> resultData,
                List<cloud.demand.app.common.excel.core.ErrorMessage> contextErrors, ParseContext<R> context) {

            Map<String, PplOrderDO> appliedPplOrderMap = new HashMap<>();
            Map<String, PplItemDO> appliedPplItemMap = new HashMap<>();
            if (ListUtils.isNotEmpty(pplIdList)) {
                List<PplItemJoinOrderVO> appliedItems = demandDBHelper.getAll(PplItemJoinOrderVO.class,
                        "where t1.ppl_id in(?) and t1.status = ? ",
                        pplIdList, PplItemStatusEnum.APPLIED.getCode());
                appliedPplOrderMap = appliedItems.stream().map(PplItemJoinOrderVO::getPplOrderDO)
                        .collect(Collectors.toMap(PplOrderDO::getPplOrder, v -> v, (v1, v2) -> v1));
                appliedPplItemMap = appliedItems.stream().map(PplItemJoinOrderVO::getItemDO)
                        .collect(Collectors.toMap(PplItemDO::getPplId, v -> v, (v1, v2) -> v1));
            }
            // 获取完整 uin 信息
            Map<String, QueryInfoByUinRsp> uinMapInfo = QueryInfoByUinRspHandler.getUinInfoMapCheckTask(context);

            for (int i = 0; i < resultData.size(); i++) {
                List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();
                R oneData = resultData.get(i);
                int rowIndex = context.getHeadRowNumber() + i + 1;
                //校验uin
                QueryInfoByUinRsp queryInfoByUinRsp = checkUin(contextErrors, context, oneData, rowIndex, uinMapInfo);
                if (queryInfoByUinRsp != null && queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)
                        && IndustryDeptEnum.SMART_INDUSTRY_ONE.getName().equals(industryDept)) {
                    // 设置智慧行业一部战区
                    oneData.setWarZone(queryInfoByUinRsp.getWarZone());
                }
                if (StringUtils.isNotBlank(oneData.getPplId())) {
                    // 校验已预约明细主key信息和资源量
                    PplItemDO appliedPplItem = appliedPplItemMap.get(oneData.getPplId());
                    PplOrderDO appliedPplOrder =
                            appliedPplItem != null ? appliedPplOrderMap.get(appliedPplItem.getPplOrder()) : null;
                    checkAppliedItem(errors, oneData, appliedPplOrder, appliedPplItem, rowIndex);
                }
                //各时间参数校验
                DateDTO dateDTO = checkInnerTime(errors, oneData, rowIndex, startYearMonth, endYearMonth);
                contextErrors.addAll(PplItemImportRsp.ErrorMessage.covert(errors));
                GroupItemDTO item = convert(oneData, industryDept, product, dateDTO, queryInfoByUinRsp);
                result.add(item);
            }
        }

        public List<GroupItemDTO> getResult() {
            return result;
        }

    }

    /**
     *  校验uin、客户简称。<br/>
     *  开始购买日期（需求日期）在录入范围内。<br/>
     *  {@link #getResult()} 中部分字段默认值设置。<br/>
     */
    public class PplResultHandlerForDatabaseAndCos
            implements ExcelResultDataAfterConvertChecker<PplImportExcelDTO> {

        private final String industryDept;

        private final YearMonth startYearMonth;

        private final String product;

        private final List<GroupItemDTO> result = new ArrayList<>();

        public PplResultHandlerForDatabaseAndCos(ImportExcelDTO req) {
            this.industryDept = req.getIndustryDept();
            this.startYearMonth = DateUtils.parse(req.getStartYearMonth());
            this.product = req.getProduct();
        }

        @Override
        public <R extends PplImportExcelDTO> void checkResultDataAfterConvert(List<R> resultData,
                List<ErrorMessage> contextErrors, ParseContext<R> context) {
            // 获取完整 uin 信息
            Map<String, QueryInfoByUinRsp> uinMapInfo = QueryInfoByUinRspHandler.getUinInfoMapCheckTask(context);
            for (int i = 0; i < resultData.size(); i++) {
                R oneData = resultData.get(i);
                int rowIndex = context.getHeadRowNumber() + i + 1;
                // 校验uin、客户简称
                QueryInfoByUinRsp queryInfoByUinRsp = checkUin(contextErrors, context, oneData, rowIndex, uinMapInfo);
                if (queryInfoByUinRsp != null && queryInfoByUinRsp.getIsExist().equals(Boolean.TRUE)
                        && IndustryDeptEnum.SMART_INDUSTRY_ONE.getName().equals(industryDept)) {
                    // 设置智慧行业一部战区
                    oneData.setWarZone(queryInfoByUinRsp.getWarZone());
                }

                // 开始购买日期（需求日期）
                DateDTO dateDTO = checkDate(contextErrors, oneData, rowIndex, startYearMonth, context);
                GroupItemDTO item = convert(oneData, industryDept, product, dateDTO, queryInfoByUinRsp);
                if (Ppl13weekProductTypeEnum.DATABASE.getName().equals(product)) {
                    // 数据库产品的部分字段默认值设置
                    item.databaseDefaultValueSet();
                } else if (Ppl13weekProductTypeEnum.COS.getName().equals(product)) {
                    // COS产品部分字段的默认值设置
                    item.cosDefaultValueSet();
                }
                result.add(item);
            }
        }

        public List<GroupItemDTO> getResult() {
            return result;
        }

        private DateDTO checkDate(List<ErrorMessage> errors,
                PplImportExcelDTO oneData, Integer row, YearMonth startYearMonth,
                ParseContext<? extends PplImportExcelDTO> context) {
            LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(oneData.getBeginBuyDate());
            String yearMonth = null;
            int year = 0;
            int month = 0;
            if (localDate == null) {
                ErrorMessage error = new ErrorMessage(row, context, oneData::getBeginBuyDate, "日期解析出错");
                errors.add(error);
            } else {
                year = localDate.getYear();
                month = localDate.getMonth().getValue();
                yearMonth = com.pugwoo.wooutils.lang.DateUtils.format(localDate, "yyyy-MM");
                if (startYearMonth.getYear() > year ||
                        (startYearMonth.getYear() == year && startYearMonth.getMonth() > month)) {
                    String info = "开始日期:" + yearMonth + "  <  导入选择需求开始年月:" + startYearMonth.toDateStr();
                    ErrorMessage error = new ErrorMessage(row, context, oneData::getBeginBuyDate, info);
                    errors.add(error);
                }
            }
            DateDTO res = new DateDTO();
            res.setYear(year);
            res.setMonth(month);
            res.setYearMonth(yearMonth);
            res.setBeginBuyDateRet(localDate);
            return res;
        }
    }


}
