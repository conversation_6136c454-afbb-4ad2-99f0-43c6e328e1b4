package cloud.demand.app.modules.p2p.ppl13week.dto.stat;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 客户分布返回
 */
@Data
public class QueryCustomerDistributedResp {

    /**行业预测*/
    private Distributed pplDemand;
    /**预约单执行*/
    private Distributed apply;

    @Data
    public static class Distributed {
        /**数量分布*/
        private List<Item> resourceCount;
        /**地域分布*/
        private Item region;
        /**客户分布*/
        private Item customer;
        /**战区分布*/
        private Item warZone;
        /**实例类型分布，top15*/
        private Item instanceType;
        /**实例规格分布，top15*/
        private Item instanceModel;
    }

    @Data
    public static class Item {
        /**名称*/
        private String name;
        /**数据*/
        private List<KeyValue> data;
    }

    @Data
    public static class KeyValue {
        /**key*/
        private String key;
        /**值*/
        private BigDecimal value;
        /**单位*/
        private String unit;
        /**第二个值*/
        private BigDecimal value2;
        /**第二个单位*/
        private String unit2;
        /** 第三个值 */
        private BigDecimal value3;
        /** 第三个单位 */
        private String unit3;
    }

}
