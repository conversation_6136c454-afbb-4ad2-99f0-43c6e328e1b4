package cloud.demand.app.modules.p2p.ppl13week.enums.inner_process;

import java.util.Objects;
import lombok.Getter;

/**
 * 流程中定义的属性枚举
 */
@Getter
public enum PplDeadlineAutoPassEnum {

    CLOSE("CLOSE", "关闭"),
    UNCHANGED_PASS("UNCHANGED_PASS", "无变化时到期自动过单"),

    DIRECT_PASS("DIRECT_PASS", "默认到期自动过单"),

    ;

    final private String code;
    final private String name;

    PplDeadlineAutoPassEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PplDeadlineAutoPassEnum getByCode(String code) {
        for (PplDeadlineAutoPassEnum e : PplDeadlineAutoPassEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getNameByCode(String code) {
        PplDeadlineAutoPassEnum e = getByCode(code);
        return e == null ? "" : e.getName();
    }

}