package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import cloud.demand.app.modules.p2p.ppl13week.service.filler.ZoneInfoFiller;
import cn.hutool.core.util.NumberUtil;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class ForecastConstituteResp {

    private String groupCondition;

    private BigDecimal total = BigDecimal.ZERO;

    List<ForecastConstituteGroupItem> items;

    @Data
    public static class ForecastConstituteGroupItem {

        private String name;

        private BigDecimal value;

        private BigDecimal percentage;

        private String percentageString;

        public ForecastConstituteGroupItem() {

        }

        public ForecastConstituteGroupItem(String name, BigDecimal value) {
            this.name = name;
            this.value = value == null ? BigDecimal.ZERO : value;
        }

        public ForecastConstituteGroupItem(String name, BigDecimal value, BigDecimal total) {
            this.name = name;
            this.value = value == null ? BigDecimal.ZERO : value;
            if (total == null || total.compareTo(BigDecimal.ZERO) == 0) {
                this.percentage = BigDecimal.ZERO;
            } else {
                this.percentage = NumberUtil.div(this.value, total, 4);
            }
            this.percentageString = NumberUtil.formatPercent(this.percentage.doubleValue(), 2);
        }
    }


    @Data
    public static class ForecastConstituteDetail implements ZoneInfoFiller {

        @Column("total")
        private BigDecimal total;

        @Column("gpu_type")
        private String gpuType;

        @Column("instance_type")
        private String instanceType;

        @Column("region_name")
        private String regionName;

        @Column("zone_name")
        private String zoneName;

        @Column("war_zone")
        private String warZone;

        @Column("customer_short_name")
        private String customerShortName;

        @Column("demand_year_month")
        private String demandYearMonth;

        @Column("database_name")
        private String databaseName;

        @Column("cos_storage_type")
        private String cosStorageType;

        private String customhouseTitle;

        @Override
        public String provideZoneName() {
            return this.zoneName;
        }

        @Override
        public String provideRegionName() {
            return this.regionName;
        }

        @Override
        public void fillZone(String zone) {
            // non
        }

        @Override
        public void fillAreaName(String areaName) {
            // non
        }

        @Override
        public void fillCustomhouseTitle(String customhouseTitle) {
            this.customhouseTitle = customhouseTitle;
        }
    }

}
