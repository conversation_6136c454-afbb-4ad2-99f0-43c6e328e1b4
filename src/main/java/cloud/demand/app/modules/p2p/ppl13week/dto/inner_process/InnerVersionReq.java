package cloud.demand.app.modules.p2p.ppl13week.dto.inner_process;

import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Data;
import yunti.boot.exception.BizException;

@Data
public class InnerVersionReq {

    private List<String> industryDeptList;

    private List<String> versionStatusList;

    public List<String> checkAndGetIndustryDeptListByAuthDeptList(List<String> authDeptList) {
        if (ListUtils.isNotEmpty(this.getIndustryDeptList())) {
            Set<String> deptList = new HashSet<>();
            Set<String> noAuthDeptList = new HashSet<>();
            for (String s : this.getIndustryDeptList()) {
                if (authDeptList.contains(s)) {
                    deptList.add(s);
                } else {
                    noAuthDeptList.add(s);
                }
            }
            if (ListUtils.isNotEmpty(noAuthDeptList)) {
                throw BizException.makeThrow("暂无部门【%s】的查询权限", JSON.toJson(noAuthDeptList));
            }
            return new ArrayList<>(deptList);
        } else {
            return authDeptList;
        }
    }

}
