package cloud.demand.app.modules.p2p.ppl13week.controller;

import cloud.demand.app.modules.mrpv3.dto.req.ext.MrpV3ReqForIndustry;
import cloud.demand.app.modules.mrpv3.dto.resp.MrpV3ReportResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplIndustryVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplOrderWithAuditRecordItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.inner_process.InnerVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplSupplyConsensusDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplApiService;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RateLimit;
import com.pugwoo.wooutils.redis.RedisLimitPeriodEnum;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.security.CurrentUser;
import yunti.boot.security.TofUser;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 提供给其他部门的接口，目前智慧行业一部调用，获取PPL信息
 */
@JsonrpcController("/ppl/api")
@Slf4j
public class PplApiController {

    @Resource
    private PplApiService pplApiService;

    @RequestMapping
    @RateLimit(limitPeriod = RedisLimitPeriodEnum.SECOND, limitCount = 50, customExceptionMessage = "请求过于频繁",
            keyScript = "args[1].getUsername()")
    public List<PplInnerProcessVersionDO> queryIndustryVersions(@JsonrpcParam InnerVersionReq req,
            @CurrentUser TofUser user) {
        if (ListUtils.isEmpty(req.getVersionStatusList())) {
            // 当前只获取已完成的版本
            req.setVersionStatusList(ListUtils.newArrayList(PplInnerProcessVersionStatusEnum.DONE.getCode()));
        }
        return pplApiService.queryIndustryVersion(req, user);
    }

    @RequestMapping
    @RateLimit(limitPeriod = RedisLimitPeriodEnum.SECOND, limitCount = 50, customExceptionMessage = "请求过于频繁",
            keyScript = "args[1].getUsername()")
    public List<PplOrderWithAuditRecordItemDTO> queryPplByIndustryVersion(@JsonrpcParam PplIndustryVersionReq req,
            @CurrentUser TofUser user) {
        return pplApiService.queryPplByIndustryVersion(req, user);
    }

    @RequestMapping
    @RateLimit(limitPeriod = RedisLimitPeriodEnum.SECOND, limitCount = 50, customExceptionMessage = "请求过于频繁",
            keyScript = "args[1].getUsername()")
    public List<PplSupplyConsensusDO> queryPplSupplyConsensus(@JsonrpcParam PplIndustryVersionReq req,
            @CurrentUser TofUser user) {
        return pplApiService.queryPplSupplyConsensus(req, user);
    }

    @RequestMapping
    @RateLimit(limitPeriod = RedisLimitPeriodEnum.SECOND, limitCount = 50, customExceptionMessage = "请求过于频繁",
            keyScript = "args[1].getUsername()")
    public MrpV3ReportResp getReportByIndustry(@JsonrpcParam MrpV3ReqForIndustry req,
            @CurrentUser TofUser user) {
        return pplApiService.getReportByIndustry(req, user);
    }

}
