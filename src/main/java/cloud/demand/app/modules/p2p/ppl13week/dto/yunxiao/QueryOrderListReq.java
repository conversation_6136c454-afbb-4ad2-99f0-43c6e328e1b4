package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 查询订单列表
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class QueryOrderListReq {
    /** 预约单号列表 */
    private List<String> orderIds;
    /**预约单号，等值查询*/
    private String orderId;
    /**提单人，等值查询*/
    private String creator;
    /**客户uin，等值查询*/
    private String uin;
    /**appid，等值查询*/
    private String appId;
    /**appName，客户名称*/
    private String appName;

    /**industryType，行业*/
    private String industryType;
    /**organizationName，行业部门*/
    private String organizationName;

    /**区域，多值查询*/
    private List<String> region;
    /**状态
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum
     * */
    private List<String> status;

    /**开始购买日期*/
    private String startExpectTime;
    /**结束购买日期*/
    private String endExpectTime;

    /**页数，从1开始*/
    private Integer pageNumber = 1;
    /**页数，默认20*/
    private Integer pageSize = 20;

}
