package cloud.demand.app.modules.p2p.industry_demand.dto.atp;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import lombok.Data;
import yunti.boot.exception.BizException;

@Data
public class QueryListReq {

    private YearMonth beginYearMonth;

    private YearMonth endYearMonth;

    /** 境内、境外 */
    private List<String> customhouseTitleList;

    /** 地域编码 */
    private List<String> regionList;

    private List<String> instanceTypeList;

    private List<String> countryNameList;

    public WhereSQL toWhereSQL(Long syncVersionId) {
        if (syncVersionId == null) {
            throw BizException.makeThrow("版本id不能为空");
        }
        WhereSQL whereSQL = new WhereSQL();
        if (beginYearMonth != null) {
            whereSQL.and("data_year_month >= ?", beginYearMonth.toString());
        }
        if (endYearMonth != null) {
            whereSQL.and("data_year_month <= ?", endYearMonth.toString());
        }
        if (ListUtils.isNotEmpty(customhouseTitleList)) {
            whereSQL.and("customhouse_title in (?)", customhouseTitleList);
        }
        if (ListUtils.isNotEmpty(regionList)) {
            whereSQL.and("region in (?)", regionList);
        }
        if (ListUtils.isNotEmpty(instanceTypeList)) {
            whereSQL.and("instance_type in (?)", instanceTypeList);
        }
        if (ListUtils.isNotEmpty(countryNameList)) {
            whereSQL.and("country_name in (?)", countryNameList);
        }
        whereSQL.and("sync_version_id = ?", syncVersionId);
        whereSQL.and("available = ?", true);
        return whereSQL;
    }

}
