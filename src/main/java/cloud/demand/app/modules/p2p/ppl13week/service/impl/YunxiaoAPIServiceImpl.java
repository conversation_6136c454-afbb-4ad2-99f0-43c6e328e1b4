package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateOrderResultDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreatePreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreatePreDeductOrderResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CrpQuotaCreateReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CrpQuotaCreateResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.DestroyPreDeductOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.EditOrderReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GetPreDeductOrderDetailResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GetPreDeductOrderDetailResultResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GridQueryReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GridQueryResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.GridQueryResp.Grid;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.InstanceFamilyDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.InstanceFamilyDTO.InstanceFamily;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDetailDTO.OrderDetail;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderExportJsonDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderExportJsonDTO.OrderItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchResultDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchSummaryDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PlatformProductConfigListDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductData;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.PreDeductRenewalReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryInstanceTypeReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryPreDeductOrderListReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryPreDeductOrderListResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryPreDeductOrderListResp.PreDeductDataResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryStockSupplyResultRsp.Result;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.RenewalPreDeductResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.YunXiaoInstanceTypeResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.YunXiaoInstanceTypeRespDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigProductEnumDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplYunxiaoApiLogDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderCategoryEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithOrderVO;
import cloud.demand.app.modules.tencent_cloud_utils.YunXiaoUtil;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDataDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.MainInstanceTypeDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.PageDataDTO;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class YunxiaoAPIServiceImpl implements YunxiaoAPIService {

    @Resource
    private DBHelper demandDBHelper;

    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public PageDataDTO<OrderDTO> queryOrderList(QueryOrderListReq req) {
        if (req == null) {
            req = new QueryOrderListReq();
        }
        return YunXiaoUtil.postForPageData("/rubik/order", req, OrderDTO.class);
    }

    @Override
    public OrderDetailDTO queryOrderDetail(String orderId) {
        if (StringTools.isBlank(orderId)) {
            return null;
        }
        String resp = YunXiaoUtil.getRaw("/rubik/order/" + orderId);
        return JSON.parse(resp, OrderDetailDTO.class);
    }

    @Override
    public void completeOrderDetail(OrderDetailDTO orderDetailDTO) {
        OrderDetailDTO.Data data = orderDetailDTO.getData();
        boolean isGpu = Objects.equals(YunxiaoOrderCategoryEnum.GPU.getCode(), data.getOrderCategory());

        List<PplItemWithOrderVO> existAppliedItems = demandDBHelper.getAll(PplItemWithOrderVO.class,
                "where status = ? and yunxiao_order_id= ? ", PplItemStatusEnum.APPLIED.getCode(), data.getOrderId());
        existAppliedItems = existAppliedItems.stream().filter(e -> e.getPplOrderDO() != null)
                .collect(Collectors.toList());
        Map<Long, List<PplItemWithOrderVO>> existAppliedItemMap = existAppliedItems.stream()
                .collect(Collectors.groupingBy(PplItemWithOrderVO::getYunxiaoDetailId));

        // 一部的未生效预测也可参与匹配，因此这里如果生效预测没有预约信息，则从预约表中获取
        List<PplItemAppliedDO> appliedItems = demandDBHelper.getAll(PplItemAppliedDO.class,
                "where yunxiao_order_id= ? ", data.getOrderId());
        Map<Long, PplItemAppliedDO> appliedItemMap = appliedItems.stream()
                .collect(Collectors.toMap(PplItemAppliedDO::getYunxiaoDetailId, e -> e, (v1, v2) -> v2));

        data.getOrderDetails().forEach(orderDetail -> {
            List<PplItemWithOrderVO> pplItemVOs = existAppliedItemMap.get(orderDetail.getId());
            PplItemAppliedDO appliedDO = appliedItemMap.get(orderDetail.getId());
            if (appliedDO != null) {
                appliedDO.setTotalCoreApplyAfter(
                        appliedDO.getTotalCoreApplyAfter() == null ? 0 : appliedDO.getTotalCoreApplyAfter());
                appliedDO.setTotalGpuNumApplyAfter(appliedDO.getTotalGpuNumApplyAfter() == null ? BigDecimal.ZERO
                        : appliedDO.getTotalGpuNumApplyAfter());
            }

            orderDetail.setApplyCpuCount(orderDetail.getApplyCount() * orderDetail.getCpu().intValue());
            orderDetail.setMatchedCore(
                    CollectionUtils.isEmpty(pplItemVOs) ? (appliedDO != null ? appliedDO.getTotalCoreApplyAfter() : 0)
                            : pplItemVOs.stream().filter(e -> PplOrderSourceTypeEnum.IMPORT.getCode()
                                            .equals(e.getPplOrderDO().getSource()))
                                    .mapToInt(e -> e.getTotalCoreApplyAfter() == null ? 0 : e.getTotalCoreApplyAfter())
                                    .sum());
            orderDetail.setUnexpectedCore(Math.max(orderDetail.getApplyCpuCount() - orderDetail.getMatchedCore(), 0));

            orderDetail.setApplyGpuCount(
                    orderDetail.getApplyCount() * (orderDetail.getGpu() == null ? 0 : orderDetail.getGpu().intValue()));
            if (isGpu) {
                orderDetail.setMatchedGpu(
                        CollectionUtils.isEmpty(pplItemVOs) ? (appliedDO != null ? appliedDO.getTotalGpuNumApplyAfter()
                                : BigDecimal.ZERO) : pplItemVOs.stream()
                                .filter(e -> PplOrderSourceTypeEnum.IMPORT.getCode()
                                        .equals(e.getPplOrderDO().getSource()))
                                .map(PplItemWithOrderVO::getTotalGpuNumApplyAfter).filter(Objects::nonNull).reduce(
                                        BigDecimal.ZERO, BigDecimal::add));
                orderDetail.setUnexpectedGpu(
                        BigDecimal.valueOf(orderDetail.getApplyGpuCount()).subtract(orderDetail.getMatchedGpu())
                                .max(BigDecimal.ZERO));
            }
        });

        data.setApplyTotalPplCore(data.getOrderDetails().stream().mapToInt(OrderDetail::getApplyCpuCount).sum());
        data.setUnexpectedCore(data.getOrderDetails().stream().mapToInt(OrderDetail::getUnexpectedCore).sum());
        data.setMatchedPplCore(data.getApplyTotalPplCore() - data.getUnexpectedCore());

        data.setApplyGpuNum(data.getOrderDetails().stream().mapToInt(OrderDetail::getApplyGpuCount).sum());
        if (isGpu) {
            data.setUnexpectedGpuNum(data.getOrderDetails().stream().map(OrderDetail::getUnexpectedGpu)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }

        PplItemAppliedDO existApplyItem = demandDBHelper.getOne(PplItemAppliedDO.class,
                "where yunxiao_order_id=?", data.getOrderId());
        data.setInnerVersionDate(existApplyItem == null ? null : existApplyItem.getInnerVersionDate());

    }

    @Override
    public void submit(String yunxiaoOrderId) {
        if (StringTools.isBlank(yunxiaoOrderId)) {
            throw new BizException("云霄单号为空");
        }
        String resp = YunXiaoUtil.postRaw("/rubik/order/submit?orderId=" + yunxiaoOrderId, "");
        CreateOrderResultDTO result = JSON.parse(resp, CreateOrderResultDTO.class);
        if (result == null || !result.checkSuccess()) {
            throw new BizException("提交云霄订单失败, requestId:" + (result == null ? "null" : result.getRequestId()));
        }
    }

    @Override
    public CreateOrderResultDTO createAndSubmit(CreateOrderReq req) {
        if (req == null) {
            req = new CreateOrderReq();
        }

        String resp = YunXiaoUtil.postRaw("/rubik/order/create-submit", req);
        CreateOrderResultDTO result = JSON.parse(resp, CreateOrderResultDTO.class);
        return result;
    }

    @Override
    public BaseDTO editAndSubmit(EditOrderReq req) {
        if (req == null) {
            return null;
        }
        String resp = YunXiaoUtil.postRaw("/rubik/order/modify-submit", req);
        BaseDTO baseDTO = JSON.parse(resp, BaseDTO.class); // 只要成功或失败就可以
        return baseDTO;
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void logYunxiaoApi(String url, String method, String req, String resp) {
        try {
            PplYunxiaoApiLogDO logDO = new PplYunxiaoApiLogDO();
            logDO.setUrl(url);
            logDO.setMethod(method);
            logDO.setReq(req);
            logDO.setResp(resp);

            demandDBHelper.insert(logDO);
        } catch (Throwable e) {
            log.error("logYunxiaoApi fail, url:{}, method:{}, req:{}, resp:{}", url, method, req, resp);
        }
    }

    @Override
    public String getYunxiaoAppRoleMapping(String product) {
        if (StringTools.isBlank(product)) {
            return "";
        }

        List<String> products = new ArrayList<>();
        products.add(product);
        // 特别处理下CVM
        if (Objects.equals("CVM&CBS", product)) {
            products.add("CVM");
        }
        if (Objects.equals("CVM", product)) {
            products.add("CVM&CBS");
        }
        String[] productArr = products.toArray(new String[0]);

        List<PplConfigProductEnumDO> list = SpringUtil.getBean(YunxiaoAPIServiceImpl.class).getAllConfigProductEnum();
        // 先尝试映射名称
        for (PplConfigProductEnumDO e : list) {
            if (StringTools.isInIgnoreCase(e.getProductName(), productArr)) {
                return e.getYunxiaoApprole();
            }
        }

        // 再尝试映射code
        for (PplConfigProductEnumDO e : list) {
            if (StringTools.isInIgnoreCase(e.getProductCode(), productArr)) {
                return e.getYunxiaoApprole();
            }
        }

        throw new BizException("产品:" + product + "找不到映射的云霄appRole代号，请先补全配置");
    }

    @HiSpeedCache(expireSecond = 60, continueFetchSecond = 300)
    public List<PplConfigProductEnumDO> getAllConfigProductEnum() {
        return demandDBHelper.getAll(PplConfigProductEnumDO.class);
    }

    @Override
    public CreateStockSupplyPlanRsp createStockSupplyPlan(CreateStockSupplyPlanReq req) {
        if (req == null) {
            throw new BizException("请求参数为null");
        }
        String resp = YunXiaoUtil.postRaw("/beacon/resource-match-plan/create", req);
        return JSON.parse(resp, CreateStockSupplyPlanRsp.class);
    }

    @Override
    public QueryStockSupplyResultRsp submitStockSupplyResult(String planId) {
        if (StringTools.isBlank(planId)) {
            throw new BizException("请求参数planId为空");
        }

        Map<String, String> req = new HashMap<>();
        req.put("planId", planId);

        String resp = YunXiaoUtil.postRaw("/beacon/resource-match-plan/submit", req);
        return JSON.parse(resp, QueryStockSupplyResultRsp.class);
    }

    @Override
    public QueryStockSupplyResultRsp queryStockSupplyResult(String planId) {
        if (StringTools.isBlank(planId)) {
            throw new BizException("请求参数planId为空");
        }
        String resp = YunXiaoUtil.getRaw("/beacon/resource-match-plan/detail?planId=" + planId);
        return JSON.parse(resp, QueryStockSupplyResultRsp.class);
    }

    @Override
    public String completeStockSupply(String planId, List<Result> results) {
        if (StringTools.isBlank(planId)) {
            throw new BizException("请求参数planId为空");
        }

        Map<String, Object> req = new HashMap<>();
        req.put("planId", planId);
        req.put("result", results);

        String resp = YunXiaoUtil.postRaw("/beacon/resource-match-plan/complete", req);
        return JSON.toJsonFormatted(resp);
    }


    /**
     * platform/product-config/list
     */
    @Override
    public String platformProductConfigList(PlatformProductConfigListDTO req) {
        String resp = YunXiaoUtil.postRaw(true, "/platform/product-config/list", req);
        return JSON.toJsonFormatted(resp);
    }

    @Override
    public OrderMatchSummaryDTO getMatchSummary(String orderId) {
        if (orderId == null) {
            return null;
        }
        String resp = YunXiaoUtil.getRaw("/rubik/order/match-summary?orderId=" + orderId);
        return JSON.parse(resp, OrderMatchSummaryDTO.class);
    }

    @Override
    public OrderMatchResultDTO getMatchResult(String orderId) {
        if (orderId == null) {
            return null;
        }
        Map<String, Object> req = new HashMap<>();
        req.put("orderId", orderId);
        String resp = YunXiaoUtil.postRaw("/rubik/order/match-result", req);
        return JSON.parse(resp, OrderMatchResultDTO.class);
    }

    @Override
    public List<OrderItem> orderExportToJson(YearMonth submitYearMonth) {
        if (submitYearMonth == null) {
            throw BizException.makeThrow("提单时间查询月不能为空");
        }
        LocalDateTime submitBegin = LocalDateTime.of(submitYearMonth.atDay(1), LocalTime.MIN);
        LocalDateTime submitEnd = LocalDateTime.of(submitYearMonth.atEndOfMonth(), LocalTime.MAX);
        Map<String, Object> req = new HashMap<>();
        req.put("pageNumber", 1);
        req.put("pageSize", 1000);
        req.put("startCreateTime", LocalDateTimeUtil.format(submitBegin, DatePattern.NORM_DATETIME_FORMATTER));
        req.put("endCreateTime", LocalDateTimeUtil.format(submitEnd, DatePattern.NORM_DATETIME_FORMATTER));
        String resp = YunXiaoUtil.postRaw("/rubik/order/export-to-json", req);
        OrderExportJsonDTO dto = JSON.parse(resp, OrderExportJsonDTO.class);
        if (dto == null || dto.getSuccess() == null || !dto.getSuccess()) {
            throw BizException.makeThrow("查询云霄预扣单失败，请求参数： %s \n 响应结果： %s", JSON.toJson(req), resp);
        }
        return dto.getData();
    }

    @Override
    public BaseDataDTO<MainInstanceTypeDTO> queryPrincipalInstanceFamily() {
        Map<String, Object> req = new HashMap<>();
        req.put("pageNumber", 1);
        req.put("pageSize", 1000);
        req.put("state", Arrays.asList("PRINCIPAL"));
        BaseDataDTO<MainInstanceTypeDTO> result = new BaseDataDTO<>();
        result.setSuccess(Boolean.TRUE);
        result.setData(new ArrayList<>());
        try {
            String resp = YunXiaoUtil.postRaw("/compass/instance-family", req);
            Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
            InstanceFamilyDTO data = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                    InstanceFamilyDTO.class);
            List<MainInstanceTypeDTO> list = new ArrayList<>();
            for (InstanceFamily datum : data.getData()) {
                MainInstanceTypeDTO mainInstanceTypeDTO = new MainInstanceTypeDTO();
                mainInstanceTypeDTO.setInstanceFamily(datum.getInstanceFamily());
                mainInstanceTypeDTO.setZoneName("");
                list.add(mainInstanceTypeDTO);
            }
            result.setData(list);
            return result;
        } catch (Exception e) {
            // 此处为推荐机型，若出现异常，直接返回空，不影响系统操作。
            return result;
        }
    }

    @Override
    public List<PreDeductData> queryPreDeductOrderList(QueryPreDeductOrderListReq req) {
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/list", req);
        QueryPreDeductOrderListResp result = JSON.parse(resp, QueryPreDeductOrderListResp.class);
        PreDeductDataResp respData = result.getRespData();
        return respData == null ? new ArrayList<>() : respData.getData();
    }

    @Override
    public CreatePreDeductOrderResp createPreDeductOrder(CreatePreDeductOrderReq req) {
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/create", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 如果创建预扣单失败了
            CreatePreDeductOrderResp result = new CreatePreDeductOrderResp();
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg((String) stringObjectMap.get("message"));
            return result;
        }
        CreatePreDeductOrderResp result = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                CreatePreDeductOrderResp.class);
        return result;
    }

    @Override
    public CreatePreDeductOrderResp destroyPreDeductOrder(DestroyPreDeductOrderReq req) {
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/destroy", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        CreatePreDeductOrderResp result = new CreatePreDeductOrderResp();

        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 如果创建预扣单失败了
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg((String) stringObjectMap.get("message"));
            return result;
        }

        return result;
    }

    @Override
    public GetPreDeductOrderDetailResp getPreDeductOrderDetail(Integer id) {
        String resp = YunXiaoUtil.getRaw("/rubik/reservation-form?id=" + id);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        GetPreDeductOrderDetailResp data = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                GetPreDeductOrderDetailResp.class);
        return data;
    }


    @Override
    public GetPreDeductOrderDetailResultResp getPreDeductOrderDetailResult(String id) {
        Map<String, Object> req = new HashMap<>();
        req.put("reservationFormId", Arrays.asList(id));
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/result", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        GetPreDeductOrderDetailResultResp data = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                GetPreDeductOrderDetailResultResp.class);
        return data;
    }

    @Override
    public List<YunXiaoInstanceTypeResp> queryInstanceModelInfos(QueryInstanceTypeReq req) {
        String response = YunXiaoUtil.postRaw("/data360/instance-type", req);
        YunXiaoInstanceTypeRespDTO result = JSON.parse(response, YunXiaoInstanceTypeRespDTO.class);
        if (result == null) {
            throw BizException.makeThrow("调用云霄实例类型接口返回null，接口入参【%s】", JSON.toJson(req));
        }
        if (result.getSuccess() == null || !result.getSuccess()) {
            throw BizException.makeThrow("调用云霄实例类型接口错误消息【%s】，接口入参【%s】，接口全部出参【%s】",
                    result.getMessage(), JSON.toJson(req), response);
        }
        return result.getData() == null ? null : result.getData().getData();
    }

    @Override
    public CreatePreDeductOrderResp beforePreDeductForOrderCheck(CreatePreDeductOrderReq req) {
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/create-check", req);
        BaseDTO baseDTO = JSON.parse(resp, BaseDTO.class);
        if (baseDTO == null) {
            throw BizException.makeThrow("云霄接口检查返回 null");
        }
//        if (baseDTO.getSuccess() == null || !baseDTO.getSuccess()) {
//            throw BizException.makeThrow("调用云霄创建预扣单前检查接口错误消息【%s】", baseDTO.getMessage());
//        }
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 如果模拟创建预扣单失败了
            CreatePreDeductOrderResp result = new CreatePreDeductOrderResp();
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg((String) stringObjectMap.get("message"));
            return result;
        }

        CreatePreDeductOrderResp result = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                CreatePreDeductOrderResp.class);
        return result;

    }

    @Override
    public void preDeductOrderReserve(List<Integer> ids) {
        Map<String, Object> req = new HashMap<>();
        req.put("reservationFormId", ids);
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/reserve", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        return;
    }

    @Override
    public RenewalPreDeductResp renewalPreDeduct(PreDeductRenewalReq req) {
        String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/renewal", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 续期预扣单失败
            RenewalPreDeductResp result = new RenewalPreDeductResp();
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg((String) stringObjectMap.get("message"));
            return result;
        }
        return JSON.parse(resp, RenewalPreDeductResp.class);
    }

    @Override
    public CrpQuotaCreateResp crpQuotaPush(CrpQuotaCreateReq req) {
        String resp = YunXiaoUtil.postRaw("/quota/quota-crp/monthly-suggestion/create", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 创建配额建议 失败
            CrpQuotaCreateResp result = new CrpQuotaCreateResp();
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg(stringObjectMap.get("errorDetail") != null ? (String) stringObjectMap.get("errorDetail")
                    : "调用云霄接口发生异常");
            return result;
        }
        CrpQuotaCreateResp result = JSON.parse(JSON.toJson(stringObjectMap.get("data")),
                CrpQuotaCreateResp.class);
        return result;
    }

    @Override
    public QuotaQueryResp queryQuota(QuotaQueryReq req) {
        String resp = YunXiaoUtil.postRaw("/weaver/upstream/zeus-quota/QueryQuota", req);
        Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
        if (stringObjectMap.get("code") != null && stringObjectMap.get("code").equals("ComponentApiError")) {
            QuotaQueryResp result = new QuotaQueryResp();
            result.setQuotaList(new ArrayList<>());
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg(stringObjectMap.get("errorDetail") != null ? (String) stringObjectMap.get("errorDetail")
                    : "region不适配");
            return result;
        }
        if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
            // 创建配额建议 失败
            QuotaQueryResp result = new QuotaQueryResp();
            result.setSuccess(Boolean.FALSE);
            result.setErrorMsg(stringObjectMap.get("errorDetail") != null ? (String) stringObjectMap.get("errorDetail")
                    : "调用云霄接口发生异常");
            result.setRetry(stringObjectMap.get("message").equals("触发流控。") ? Boolean.TRUE : Boolean.FALSE);
            return result;
        }

        QuotaQueryResp result = JSON.parse(
                JSON.toJson(JSON.parseToMap(JSON.toJsonFormatted(stringObjectMap.get("data"))).get("data")),
                QuotaQueryResp.class);
        return result;
    }

    @Override
    public List<Grid> queryGridList(List<Integer> reservationFormIds) {
        // 参数校验
        if (reservationFormIds == null || reservationFormIds.isEmpty()) {
            return new ArrayList<>();
        }

        GridQueryReq req = new GridQueryReq();
        req.setReservationFormId(reservationFormIds);

        List<Grid> result = new ArrayList<>();
        int maxRetries = 3;   // 流控最大重试次数
        int currentRetry = 0;

        while (true) {
            GridQueryResp queryResp = null;
            try {
                String resp = YunXiaoUtil.postRaw("/rubik/reservation-form/grid", req);

                // 处理响应（提取为独立方法）
                Map<String, Object> respMap = parseResponse(resp);

                // 处理流控重试
                if (isRateLimited(respMap)) {
                    if (++currentRetry > maxRetries) {
                        throw new BizException("查询块接口-接口流控重试超过最大次数");
                    }
                    TimeUnit.SECONDS.sleep(1); // 流控休眠
                    continue;                  // 重试当前页
                }

                // 解析数据
                queryResp = GridQueryResp.parseData(respMap);

                if (queryResp.getTotalCount() == 0) {
                    return new ArrayList<>();
                }

                result.addAll(queryResp.getData());

                // 分页终止条件：当前页是否是最后一页
                if (req.getPageNumber() * req.getPageSize() >= queryResp.getTotalCount()) {
                    break;
                }

                req.setPageNumber(req.getPageNumber() + 1);
                currentRetry = 0; // 重置重试计数器
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new BizException("查询被中断");
            } catch (Exception e) {
                throw new BizException("查询预扣块接口异常: " + e.getMessage());
            }
        }

        return result;
    }

    private Map<String, Object> parseResponse(String resp) {
        try {
            return JSON.parseToMap(resp);
        } catch (Exception e) {
            throw new BizException("响应解析失败: " + e.getMessage());
        }
    }


    private boolean isRateLimited(Map<String, Object> respMap) {
        return Boolean.FALSE.equals(respMap.get("success"))
                && "触发流控。".equals(respMap.get("message"));
    }

    @Override
    public List<String> queryUinShareList() {
        Map<String, Object> req = new HashMap<>();
        req.put("uinList", new ArrayList<>());  // 筛选uin列表。为空时，表示查询所有uin
        req.put("region", "ap-guangzhou");  // 地域编码，必填， 云霄那边一般做全地域共享uin配置的发布，这里暂时使用固定一个地域查询
        String resp = null;
        try {
            resp = YunXiaoUtil.postRaw("/rubik/reservation-form/uin-share/query", req);
            Map<String, Object> stringObjectMap = JSON.parseToMap(resp);
            if (stringObjectMap.get("success") != null && stringObjectMap.get("success").equals(Boolean.FALSE)) {
                throw BizException.makeThrow("接口响应数据 success 为 false");
            }
            Map<String, Object> data = JSON.parseToMap(JSON.toJson(stringObjectMap.get("data")));
            return JSON.parseToList(JSON.toJson(data.get("uinList")), String.class);
        } catch (Exception e) {
            String msg = StrUtil.format("获取云霄共享预扣uin列表异常，异常信息【{}】, 请求【{}】，响应【{}】",
                    ExceptionUtil.getMessage(e), JSON.toJson(req), resp);
            AlarmRobotUtil.doAlarm("queryUinShareList", msg, null, false);
            log.error(msg, e);
        }
        return new ArrayList<>();
    }
}
