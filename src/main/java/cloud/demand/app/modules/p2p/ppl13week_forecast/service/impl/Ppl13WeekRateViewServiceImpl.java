package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Product2ModelForecastConfigEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.ForecastKey.Key;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictCompensationDetailDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastTaskInputDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.PplForecastRateDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.RateViewDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastPredictIndexEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastSerialIntervalTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.DateReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotCommonReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotCommonReq.PlotType;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotRsp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QueryRateViewFittingPlotRsp.Item;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.req.QuerySplitDetailForPplReq;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp.QuerySplitDetailForPplResp;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13WeekRateViewService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import io.vavr.Tuple;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Service;
import yunti.boot.exception.BizException;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class Ppl13WeekRateViewServiceImpl implements Ppl13WeekRateViewService {

    @Resource
    Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckForecastStdCrpDBHelper;
    @Resource
    private DBHelper ckForecastStdCrpSwapDBHelper;


    DateTimeFormatter yyyyMMFormatter = DateTimeFormatter.ofPattern("yyyyMM");

    @Override
    public QueryRateViewFittingPlotRsp queryRateViewFittingPlot(QueryRateViewFittingPlotReq req) {

        Long taskId = req.getTaskIds().get(0);
        PplForecastTaskInputDO taskDO = getTaskInput(taskId);
        String category = taskDO.getCategory();

        WhereContent whereContent = new WhereContent();
        // 这里只能使用 category
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceType();
        whereContent.addAnd("category = ?", category);
        whereContent.andNotIn("gins_family", allBlackInstanceType);
        setYearMonthWhere(whereContent, req);
        whereContent.andInIfValueNotEmpty("gins_family", req.getGinsFamilies());
        whereContent.andInIfValueNotEmpty("region_name", req.getRegionNames());

        if (Objects.equals(PplForecastSerialIntervalTypeEnum.WEEK.getType(), taskDO.getSerialInterval())) {
            return getWeekData(req, taskDO, whereContent);
        }

        String predictIndex = req.getPredictIndex();
        // 532 的数据在 3 期那里才有
        if (PplForecastPredictIndexEnum.CODE532.getCode().equals(req.getPredictIndex())) {
            predictIndex = "3";
        } else if (PplForecastPredictIndexEnum.CODE55.getCode().equals(req.getPredictIndex())) {
            predictIndex = "2";
        }

        WhereContent fitWhere = new WhereContent(whereContent);
        // 拟合数据有 predict_index 的限制,单独预测没有
        fitWhere.addAnd("predict_index = ?", predictIndex);
        fitWhere.andLT("stat_time", taskDO.getPredictMonth());

        // 预测数据限定为一个 taskId, 通过时间分段，两个一次查找返回
        WhereContent predictWhere = new WhereContent(whereContent);
        predictWhere.andEqual("task_id", taskId);
        predictWhere.andGTE("stat_time", taskDO.getPredictMonth());

        CompletableFuture<List<PplForecastRateDetailDTO>> allFilDetailsFuture = CompletableFuture.supplyAsync(
                () -> getDetail(req, fitWhere, req.getPredictIndex()));
        // 预测的数据只有预测，没有实际值, 下面 predict_index 要是 1-6 的才可以了， sql 中限制了
        List<PplForecastRateDetailDTO> allPredictDetails = getDetail(req, predictWhere, predictIndex);
        List<PplForecastRateDetailDTO> allFilDetails = allFilDetailsFuture.join();

        List<Item> newDetail = new ArrayList<>();
        List<Item> retDetail = new ArrayList<>();

        sortAndTransToRsp(allFilDetails, allPredictDetails, newDetail, retDetail);

        // 把补偿的数据设置上

//        WhereContent compensationWhere = new WhereContent();
//        compensationWhere.andInNoBracketsIfNotEmpty("gins_family", req.getGinsFamilies());
//        compensationWhere.andInNoBracketsIfNotEmpty("region_name", req.getRegionNames());
//        compensationWhere.andInNoBracketsIfNotEmpty("is_enable", Lang.list(1));
//        compensationWhere.andInNoBracketsIfNotEmpty("compensation_type", Lang.list("INPUT"));
//        compensationWhere.andNotInNoBracketsIfNotEmpty("gins_family", allBlackInstanceType);

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("task_id = ?", taskId);
        whereSQL.and("compensation_type = ?", "INPUT");
        whereSQL.andIf(ListUtils.isNotEmpty(req.getGinsFamilies()), "gins_family in (?)", req.getGinsFamilies());
        whereSQL.andIf(ListUtils.isNotEmpty(req.getRegionNames()), "region_name in (?)", req.getRegionNames());
        whereSQL.andIf(ListUtils.isNotEmpty(allBlackInstanceType), "gins_family not in (?)", allBlackInstanceType);

        List<PplForecastPredictCompensationDetailDO> allCompensation = DBList.demandDBHelper.getAll(
                PplForecastPredictCompensationDetailDO.class, whereSQL.getSQL(), whereSQL.getParams());

//        List<PplForecastInputCompensationDO> allCompensation = DBList.demandDBHelper.getAll(
//                PplForecastInputCompensationDO.class,
//                "\n" + compensationWhere.getSql(), compensationWhere.getParams());
        // 这里过滤类型相同的
//        Ppl13weekForecastRangeEnumDTO rangeEnums = Ppl13weekForecastRangeEnumDTO.newFrom(taskDO);
//        allCompensation = allCompensation.stream()
//                .filter((o) -> o.filterByCategoryRangeEnums(category, rangeEnums))
//                .collect(Collectors.toList());
        Map<Key, List<PplForecastPredictCompensationDetailDO>> groupBy = allCompensation.stream()
                .collect(Collectors.groupingBy(getKey(req)));

        for (Item item : newDetail) {
            Function<Item, Key> key = getKey(PplForecastTypeEnum.NEW, req.getPlotType());
            List<PplForecastPredictCompensationDetailDO> compensation = groupBy.get(key.apply(item));
            BigDecimal sum = NumberUtils.sum(compensation, PplForecastPredictCompensationDetailDO::getCoreNum);
            item.setCompensationRealCoreNum(sum);
        }
        for (Item item : retDetail) {
            Function<Item, Key> key = getKey(PplForecastTypeEnum.RET, req.getPlotType());
            List<PplForecastPredictCompensationDetailDO> compensation = groupBy.get(key.apply(item));
            BigDecimal sum = NumberUtils.sum(compensation, PplForecastPredictCompensationDetailDO::getCoreNum);
            item.setCompensationRealCoreNum(sum);
        }
        return new QueryRateViewFittingPlotRsp(newDetail, retDetail);
    }


    @NotNull
    private QueryRateViewFittingPlotRsp getWeekData(QueryRateViewFittingPlotReq req,
            PplForecastTaskInputDO taskDO, WhereContent whereContent) {

        Long taskId = taskDO.getTaskId();

        WhereContent fitWhere = new WhereContent(whereContent);
        // 拟合数据有 predict_index 的限制,单独预测没有
        fitWhere.andLT("stat_time", taskDO.getPredictMonth());

        WhereContent predictWhere = new WhereContent(whereContent);
        predictWhere.andEqual("task_id", taskId);
        predictWhere.andGTE("stat_time", taskDO.getPredictMonth());

        final String sql = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate_week.sql");

        CompletableFuture<List<PplForecastRateDetailDTO>> allFilDetailsFuture = CompletableFuture.supplyAsync(
                () -> getWeekDetail(req, fitWhere, sql));
        List<PplForecastRateDetailDTO> allFilDetails = allFilDetailsFuture.join();

        String sql1 = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate_week_predict.sql");
        sql1 = sql1.replace("/*${WHERE}*/", predictWhere.getSql());
        sql1 = getSqlColumn(sql1, req.getPlotType());
        List<PplForecastRateDetailDTO> allPredictDetails = ckForecastStdCrpDBHelper.getRaw(
                PplForecastRateDetailDTO.class, sql1, predictWhere.getParams());

        List<Item> newDetail = new ArrayList<>();
        List<Item> retDetail = new ArrayList<>();
        sortAndTransToRsp(allFilDetails, allPredictDetails, newDetail, retDetail);
        return new QueryRateViewFittingPlotRsp(newDetail, retDetail);
    }

    private List<PplForecastRateDetailDTO> getWeekDetail(QueryRateViewFittingPlotReq req,
            WhereContent predictWhere, String sql) {
        String sql1 = sql.replace("${WHERE}", predictWhere.getSql());
        sql1 = getSqlColumn(sql1, req.getPlotType());
        return ckForecastStdCrpDBHelper.getRaw(
                PplForecastRateDetailDTO.class,
                sql1, Stream.concat(Arrays.stream(predictWhere.getParams()),
                        Stream.of(req.getPredictIndex())).toArray());
    }


    private void sortAndTransToRsp(List<PplForecastRateDetailDTO> allFilDetails,
            List<PplForecastRateDetailDTO> allPredictDetails, List<Item> newDetail, List<Item> retDetail) {
        for (PplForecastRateDetailDTO detail : allFilDetails) {
            getItem(newDetail, retDetail, detail);
        }
        for (PplForecastRateDetailDTO detail : allPredictDetails) {
            Item item = getItem(newDetail, retDetail, detail);
            item.setRealCoreNum(null);
            item.setAccuracyRate(null);
        }
        // 最后一步时间排序一下
        {
            ListUtils.sortAscNullLast(newDetail, Item::getYearMonth);
            ListUtils.sortAscNullLast(retDetail, Item::getYearMonth);
        }
    }

    private String getSqlColumn(String sql, PlotType plotType) {

        String regionNameReplacement = "''";
        String ginsFamilyReplacement = "''";
        /*
         * 这个字段是加在占比类型的数据上的，导致这里的数据不是总的权重，给行业数据看板使用
         */
        String partition = "";
        switch (plotType) {
            case REGION_RATIO:
                regionNameReplacement = "region_name";
                partition = ",region_name";
                break;
            case REGION_MODEL_RATIO:
                regionNameReplacement = "region_name";
                ginsFamilyReplacement = "gins_family";
                partition = ",region_name,gins_family";
                break;
            case MODEL_RATIO:
                ginsFamilyReplacement = "gins_family";
                partition = ",gins_family";
                break;
            default:
        }
        sql = sql.replace("${REGION_NAME}", regionNameReplacement)
                .replace("${GINS_FAMILY}", ginsFamilyReplacement)
                .replace("${PARTITION}", partition);
        return sql;
    }

    private Function<PplForecastPredictCompensationDetailDO, Key> getKey(QueryRateViewFittingPlotReq req) {
        return (o) -> {
            Key key = new Key();
            key.setSeqType(o.getSeqType());
            key.setMonth(o.getMonth());
            key.setYear(o.getYear());
            switch (req.getPlotType()) {
                case REGION_RATIO:
                    key.setRegionName(o.getRegionName());
                    break;
                case REGION_MODEL_RATIO:
                    key.setRegionName(o.getRegionName());
                    key.setGinsFamily(o.getGinsFamily());
                    break;
                case MODEL_RATIO:
                    key.setGinsFamily(o.getGinsFamily());
                    break;
                default:
            }
            return key;
        };
    }

    private Function<Item, Key> getKey(PplForecastTypeEnum pplForecastTypeEnum, PlotType plotType) {
        return (o) -> {
            Key key = new Key();
            key.setSeqType(pplForecastTypeEnum.getType());
            YearMonth yearMonth = YearMonth.parse(o.getYearMonth(), yyyyMMFormatter);
            key.setMonth(yearMonth.getMonthValue());
            key.setYear(yearMonth.getYear());

            switch (plotType) {
                case REGION_RATIO:
                    key.setRegionName(o.getRegionName());
                    break;
                case REGION_MODEL_RATIO:
                    key.setRegionName(o.getRegionName());
                    key.setGinsFamily(o.getGinsFamily());
                    break;
                case MODEL_RATIO:
                    key.setGinsFamily(o.getGinsFamily());
                    break;
                default:
            }
            return key;
        };
    }


    public static <R> R copyProperties(Object source, Class<R> targetClass) throws BeansException {
        try {
            R target = targetClass.getDeclaredConstructor().newInstance();
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            throw new BeansException("Failed to copy properties", e) {
            };
        }
    }

    @Override
    public QueryRateViewFittingPlotRsp queryRateViewFittingPlotForMrp(QueryRateViewFittingPlotCommonReq req) {
        // 选取最新的 task 作为参数就可以了，预测页面默认是最新的，这样可以直接保持一致
        String sql = "with category_detail as (select *\n"
                + "                         from ppl_forecast_task_input\n"
                + "                         where category = '方案705：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX'\n"
                + "                           and is_enable = 1)\n"
                + "select *\n"
                + "from category_detail\n"
                + "where predict_month = (select max(predict_month) from category_detail)";
        PplForecastTaskInputDO raw = demandDBHelper.getRawOne(PplForecastTaskInputDO.class, sql);
        QueryRateViewFittingPlotReq reqTmp = new QueryRateViewFittingPlotReq();
        BeanUtils.copyProperties(req, reqTmp);
        reqTmp.setTaskIds(Lang.list(raw.getTaskId()));

        // 补上当月的数据,注意会乘上一个比例作为实际值,给行业数据看板使用的数据
        QueryRateViewFittingPlotRsp rsp = queryRateViewFittingPlot(reqTmp);
        setCurMonthData1(req, rsp);
        return rsp;
    }


    @Override
    public QuerySplitDetailForPplResp querySplitDetailForPpl(QuerySplitDetailForPplReq req) {

        String product = req.getProduct();
        LocalDate lookBackDate = req.getLookBackDate();
        if (lookBackDate == null) {
            lookBackDate = LocalDate.now();
        }
        lookBackDate = lookBackDate.withDayOfMonth(1); // 月的第一天

        String category = getCategoryByProduct(product);
        String postSql = "where category = ? and is_enable = 1 and predict_month = ?";
        PplForecastTaskInputDO taskDO
                = demandDBHelper.getOne(PplForecastTaskInputDO.class, postSql, category, lookBackDate);
        if (taskDO == null) {
            throw new RuntimeException("预测任务未生成，请联系fireflychen生成");
        }

        String splitVersionPostSql = "where task_id = ? order by create_time desc";
        PplForecastPredictTaskOutputVersionDO splitVersion =
                demandDBHelper.getOne(PplForecastPredictTaskOutputVersionDO.class, splitVersionPostSql, taskDO.getTaskId());
        if (splitVersion == null) {
            throw new RuntimeException("预测任务未生成，请联系fireflychen生成");
        }

        List<PplForecastPredictResultSplitDO> details =
                demandDBHelper.getAll(PplForecastPredictResultSplitDO.class,
                "where output_version_id = ?", splitVersion.getId());

        return QuerySplitDetailForPplResp.constructResp(details);
    }

    private static @NotNull String getCategoryByProduct(String product) {
        List<String> supportProduct = Lang.list(
                Product2ModelForecastConfigEnum.EKS.getCode(),
                Product2ModelForecastConfigEnum.EMR.getCode(),
                Product2ModelForecastConfigEnum.CDB.getCode()
        );
        if (!supportProduct.contains(product)) {
            throw new RuntimeException("不支持的产品类型: " + product);
        }

        String category = "";
        if (product.equals(Ppl13weekProductTypeEnum.EKS.getCode())) {
            category = "方案520：EKS中长尾-月切保留客户维度(机型规格)-机型收敛-全部客户-月度ARIMAX";
        } else if (product.equals(Ppl13weekProductTypeEnum.EMR.getCode())) {
            category = "方案510：EMR中长尾-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX";
        } else if("CDB".equals(product)) {
            category = "方案530：CDB中长尾-月切保留客户维度-内存收敛-全部客户-月度ARIMAX";
        }
        return category;
    }


    private void setCurMonthData1(QueryRateViewFittingPlotCommonReq req, QueryRateViewFittingPlotRsp rsp) {

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceType();
        String predictIndex = req.getPredictIndex();
        // 532 的数据在 3 期那里才有
        if (PplForecastPredictIndexEnum.CODE532.getCode().equals(req.getPredictIndex())) {
            predictIndex = "3";
        } else if (PplForecastPredictIndexEnum.CODE55.getCode().equals(req.getPredictIndex())) {
            predictIndex = "2";
        }

        WhereContent whereContent = new WhereContent();
        // 这里不需要category 了， 在 ola 设置了
        whereContent.andNotIn("gins_family", allBlackInstanceType);
        whereContent.andInIfValueNotEmpty("gins_family", req.getGinsFamilies());
        whereContent.andInIfValueNotEmpty("region_name", req.getRegionNames());
        // has_predict 不给加上，当月数据本来就不准的
        whereContent.addAnd("predict_index = ?", predictIndex);

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate_cur_month1.sql");
        sql = sql.replace("${WHERE}", whereContent.getSql());

        sql = getSqlColumn(sql, req.getPlotType());

        // 按天计算比例,折算成月
        LocalDate now = LocalDate.now();
        BigDecimal rate = BigDecimal.ZERO;
        if (now.getDayOfMonth() > 1) {
            rate = BigDecimal.valueOf(now.lengthOfMonth())
                    .divide(BigDecimal.valueOf(now.getDayOfMonth() - 1), 5, RoundingMode.HALF_UP);
            rate = BigDecimal.valueOf(0.3).add(BigDecimal.valueOf(0.7).multiply(rate));
        }
        // 特别注意 predict_index
        List<PplForecastRateDetailDTO> raw = ckForecastStdCrpDBHelper.getRaw(
                PplForecastRateDetailDTO.class,
                sql, Stream.concat(Stream.of(req.getPredictIndex(), rate),
                        Arrays.stream(whereContent.getParams())).toArray());

        Function<PplForecastRateDetailDTO, Object> key;
        switch (req.getPlotType()) {
            case REGION_RATIO:
                key = (o) -> Tuple.of(o.getYearMonth(), o.getSeqType(), o.getRegionName());
                break;
            case REGION_MODEL_RATIO:
                key = (o) -> Tuple.of(o.getYearMonth(), o.getSeqType(), o.getRegionName(), o.getGinsFamily());
                break;
            case MODEL_RATIO:
                key = (o) -> Tuple.of(o.getYearMonth(), o.getSeqType(), o.getGinsFamily());
                break;
            default:
                key = (o) -> Tuple.of(o.getYearMonth(), o.getSeqType());
        }

        Map<Object, PplForecastRateDetailDTO> keyMap = raw.parallelStream()
                .collect(Collectors.toMap(key, (o) -> o, (a, b) -> a));

        PplForecastRateDetailDTO tmp = new PplForecastRateDetailDTO();
        tmp.setYearMonth(DateTimeFormatter.ofPattern("yyyyMM").format(now));
        rsp.getNewDetail().forEach((o) -> {
            setRegionNameGinsfamily(req, o);
            if (o.getYearMonth().equals(DateTimeFormatter.ofPattern("yyyyMM").format(now))) {
                tmp.setGinsFamily(o.getGinsFamily());
                tmp.setRegionName(o.getRegionName());
                tmp.setSeqType("NEW");
                PplForecastRateDetailDTO group = keyMap.get(key.apply(tmp));
                if (group != null) {
                    o.setRealCoreNum(group.getTotalRealCoreNum());
                    o.setAccuracyRate(group.getRate());
                }
            }
        });
        rsp.getRetDetail().forEach((o) -> {
            setRegionNameGinsfamily(req, o);
            if (o.getYearMonth().equals(DateTimeFormatter.ofPattern("yyyyMM").format(now))) {
                tmp.setGinsFamily(o.getGinsFamily());
                tmp.setRegionName(o.getRegionName());
                tmp.setSeqType("RET");
                PplForecastRateDetailDTO group = keyMap.get(key.apply(tmp));
                if (group != null) {
                    o.setRealCoreNum(group.getTotalRealCoreNum());
                    o.setAccuracyRate(group.getRate());
                }
            }
        });

    }

    private void setRegionNameGinsfamily(QueryRateViewFittingPlotCommonReq req, Item o) {
        if (req.getPlotType() == PlotType.REGION_RATIO || req.getPlotType() == PlotType.ACCURACY_TREND) {
            o.setGinsFamily("");
        }
        if (req.getPlotType() == PlotType.MODEL_RATIO || req.getPlotType() == PlotType.ACCURACY_TREND) {
            o.setRegionName("");
        }
    }

    @NotNull
    private Item getItem(List<Item> newDetail, List<Item> retDetail, PplForecastRateDetailDTO detail) {
        Item item = new Item();
        item.setYearMonth(detail.getYearMonth());
        item.setPredictCoreNum(detail.getTotalPredictCoreNum());
        item.setRealCoreNum(detail.getTotalRealCoreNum());
        item.setAccuracyRate(detail.getRate());
        item.setGinsFamily(detail.getGinsFamily());
        item.setRegionName(detail.getRegionName());
        if (PplForecastTypeEnum.NEW.getType().equals(detail.getSeqType())) {
            newDetail.add(item);
        } else if (PplForecastTypeEnum.RET.getType().equals(detail.getSeqType())) {
            retDetail.add(item);
        }
        return item;
    }


    private List<PplForecastRateDetailDTO> getDetail(QueryRateViewFittingPlotReq req, WhereContent fitWhere,
            String predictIndex) {
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate.sql");
        sql = sql.replace("${WHERE}", fitWhere.getSql());

        sql = getSqlColumn(sql, req.getPlotType());

        if (Objects.equals(true, req.getUseSwap())) {
            return ckForecastStdCrpSwapDBHelper.getRaw(
                    PplForecastRateDetailDTO.class,
                    sql, Stream.concat(Stream.of(predictIndex), Arrays.stream(fitWhere.getParams())).toArray());
        }
        return ckForecastStdCrpDBHelper.getRaw(
                PplForecastRateDetailDTO.class,
                sql, Stream.concat(Stream.of(predictIndex), Arrays.stream(fitWhere.getParams())).toArray());
    }


    private PplForecastTaskInputDO getTaskInput(Long taskId) {
        List<PplForecastTaskInputDO> tasks = demandDBHelper.getRaw(PplForecastTaskInputDO.class,
                "select * from ppl_forecast_task_input where task_id=?", taskId);
        if (Lang.isEmpty(tasks)) {
            throw BizException.makeThrow("未找到预测方案: taskId=%d", taskId);
        }
        // 这里默认第一个
        return tasks.get(0);
    }

    @Override
    public QueryRateViewDetailRsp queryRateViewDetail(QueryRateViewDetailReq req) {
        Long taskId = req.getTaskIds().get(0);
        PplForecastTaskInputDO taskDO = getTaskInput(taskId);

        WhereContent whereContent = new WhereContent();
        // 这里只能使用 category
        whereContent.addAnd("category = ?", taskDO.getCategory());
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceType();
        whereContent.andNotIn("gins_family", allBlackInstanceType);
        setYearMonthWhere(whereContent, req);
        whereContent.andInIfValueNotEmpty("gins_family", req.getGinsFamilies());
        whereContent.andInIfValueNotEmpty("region_name", req.getRegionNames());

        if (Objects.equals(PplForecastSerialIntervalTypeEnum.WEEK.getType(), taskDO.getSerialInterval())) {
            return getWeekDetailData(req, taskDO, whereContent);
        }

        whereContent.andInIfValueNotEmpty("predict_index", Lang.list(1, 2, 3, 4, 5, 6));

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate_detail.sql");
        sql = sql.replace("${WHERE}", whereContent.getSql());

        List<RateViewDetailDTO> allDetails;
        if (Objects.equals(true, req.getUseSwap())) {
            allDetails = ckForecastStdCrpSwapDBHelper.getRaw(RateViewDetailDTO.class, sql, whereContent.getParams());
        } else {
            allDetails = ckForecastStdCrpDBHelper.getRaw(RateViewDetailDTO.class, sql, whereContent.getParams());
        }

        return QueryRateViewDetailRsp.convert(allDetails);
    }

    private QueryRateViewDetailRsp getWeekDetailData(QueryRateViewDetailReq req, PplForecastTaskInputDO taskDO,
            WhereContent whereContent) {

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/rate_view/accuracy_rate_week_detail.sql");
        sql = sql.replace("/*${WHERE}*/", whereContent.getSql());
        List<RateViewDetailDTO> allDetails = ckForecastStdCrpDBHelper.getRaw(
                RateViewDetailDTO.class, sql, whereContent.getParams());
        return QueryRateViewDetailRsp.convert(allDetails);
    }

    private void setYearMonthWhere(WhereContent whereContent, DateReq req) {

        YearMonth startYearMonth = req.getStartYearMonth();
        YearMonth endYearMonth = req.getEndYearMonth();

        if (startYearMonth != null) {
            whereContent.addAnd(" year > ? OR (year = ? AND month >= ?)",
                    startYearMonth.getYear(), startYearMonth.getYear(), startYearMonth.getMonthValue()
            );
        }
        if (endYearMonth != null) {
            whereContent.addAnd(" year < ? OR (year = ? AND month <= ?)",
                    endYearMonth.getYear(), endYearMonth.getYear(), endYearMonth.getMonthValue()
            );
        }
        if (req.getStartStatTime() != null) {
            whereContent.andGTE("stat_time", req.getStartStatTime());
        }
        if (req.getEndStatTime() != null) {
            whereContent.andLTE("stat_time", req.getEndStatTime());
        }
    }
}
