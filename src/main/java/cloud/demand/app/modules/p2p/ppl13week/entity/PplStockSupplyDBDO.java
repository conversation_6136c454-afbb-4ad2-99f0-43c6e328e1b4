package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Table("ppl_stock_supply_db")
public class PplStockSupplyDBDO extends BaseDO {

    /** 版本group id<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;


    /** 版本group id<br/>Column: [version_code] */
    @Column(value = "status")
    private String status;

    /** 创建人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    @Column(value = "finish_time")
    private Date finishTime;

}