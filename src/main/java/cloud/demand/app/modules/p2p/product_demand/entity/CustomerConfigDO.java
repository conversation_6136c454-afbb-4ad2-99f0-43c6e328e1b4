package cloud.demand.app.modules.p2p.product_demand.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("customer_config")
public class CustomerConfigDO {

    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "industry")
    private String industry;

}