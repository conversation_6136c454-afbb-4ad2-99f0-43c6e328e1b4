package cloud.demand.app.modules.p2p.ppl13week.dto.order;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreateOrderDTO {

    /**
     * 需求年份<br/>Column: [year]
     */
    private Integer year;

    /**
     * 需求月份<br/>Column: [month]
     */
    private Integer month;

    /**
     * 行业<br/>Column: [industry]
     */
    private String industry;

    /**
     * 战区<br/>Column: [product]
     */
    private String warZone;

    /**
     * 客户类型,存量客户,winback客户<br/>Column: [customer_type]
     */
    private String customerType;

    /**
     * 客户名称<br/>Column: [customer_name]
     */
    private String customerName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    private String customerUin;

    private String customerShortName;

    private String customerSource;


    /**
     * 行业<br/>Column: [industry]
     */
    private String industryDept;


    /**
     * 提单人<br/>Column: [submit_user]
     */
    private String submitUser;

    private Integer allCore;
    private Integer allDisk;


    private PplOrderSourceTypeEnum sourceTypeEnum;


    /**
     * 用于关联预测
     */
    private Long forecastResultId;
    private String forecastSourceUuid;

    private Boolean isComd;

    private String originPplOrder;

    private String prefix;


    public static PplOrderDO transFrom(CreateOrderDTO source) {
        PplOrderDO pplOrderDO = new PplOrderDO();
//        pplOrderDO.setPplOrder();
//        pplOrderDO.setSource();
        pplOrderDO.setIndustry(source.getIndustry());
        pplOrderDO.setCustomerSource(source.getCustomerSource());
        pplOrderDO.setIndustryDept(source.getIndustryDept());
        pplOrderDO.setWarZone(source.getWarZone());
        pplOrderDO.setCustomerType(source.getCustomerType());
        pplOrderDO.setCustomerName(source.getCustomerName());
        pplOrderDO.setCustomerUin(source.getCustomerUin());
        pplOrderDO.setCustomerShortName(source.getCustomerShortName());
//        pplOrderDO.setStatus();
        pplOrderDO.setSubmitUser(source.getSubmitUser());
        pplOrderDO.setAllCore(source.getAllCore());
        pplOrderDO.setAllDisk(source.getAllDisk());
        pplOrderDO.setForecastResultId(source.getForecastResultId());
        pplOrderDO.setForecastSourceUuid(source.getForecastSourceUuid());
        return pplOrderDO;
    }

}
