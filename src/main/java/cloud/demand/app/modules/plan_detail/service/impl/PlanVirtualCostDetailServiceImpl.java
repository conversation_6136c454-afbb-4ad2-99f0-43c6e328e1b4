package cloud.demand.app.modules.plan_detail.service.impl;

import cloud.demand.app.entity.rrp.ReportPlanDetailDO;
import cloud.demand.app.modules.common.enums.ComputeTypeEnum;
import cloud.demand.app.modules.common.enums.PlanIndicatorEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.plan_detail.model.VirtualCostDTO;
import cloud.demand.app.modules.plan_detail.service.PlanDetailCommonService;
import cloud.demand.app.modules.plan_detail.service.PlanVirtualCostDetailService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class PlanVirtualCostDetailServiceImpl implements PlanVirtualCostDetailService {

    @Resource
    private DBHelper planDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    PlanDetailCommonService planDetailCommonService;

    @Override
    public void getAndSaveVirtualCostData(String statTime) {
        planDetailCommonService.saveCvmDataToDB(generateVirtualCostData(statTime));
    }

    /**
     * 生成虚拟化开销的DO集合
     */
    private List<ReportPlanDetailDO> generateVirtualCostData(String statTime){
        List<ReportPlanDetailDO> result = new ArrayList<>();
        ArrayList<ComputeTypeEnum> computeTypes = Lang.list(ComputeTypeEnum.CPU, ComputeTypeEnum.GPU);

        for (ComputeTypeEnum computeType : computeTypes) {
            List<VirtualCostDTO> list = generateVirtualCostDTOList(statTime, computeType);
            if (ListUtils.isEmpty(list)) {
                ReportPlanDetailDO planDetailDO = PlanIndicatorEnum.VIRTUAL_COST.toReportPlanDetailDO();
                planDetailDO.setStatTime(DateUtils.parse(statTime));
                planDetailDO.setProductType(ProductTypeEnum.CVM.getCode());
                planDetailDO.setCores(BigDecimal.ZERO);
                planDetailDO.setComputeType(computeType.getCode());
                result.add(planDetailDO);
                continue;
            }
            for (VirtualCostDTO dto : list) {
                ReportPlanDetailDO planDetailDO = PlanIndicatorEnum.VIRTUAL_COST.toReportPlanDetailDO();
                planDetailCommonService.fillReportPlanDetailDO(planDetailDO, ProductTypeEnum.CVM, computeType, dto, statTime);
                planDetailDO.setCores(new BigDecimal(dto.getReservedCore()));
                result.add(planDetailDO);
            }

            //  清理重复数据
            rrpDBHelper.delete(ReportPlanDetailDO.class,
                    "where stat_time = ? and indicator_code = ? and product_type = 'CVM' and compute_type = ?",
                    statTime, PlanIndicatorEnum.VIRTUAL_COST.getCode(), computeType.getCode());
        }
        return result;

    }


    /**
     * 生成虚拟化开销的DTO对象
     */
    private List<VirtualCostDTO> generateVirtualCostDTOList(String statTime, ComputeTypeEnum computeType){
        String sql = "SELECT cvmtype, zoneid, SUM(cpucore_reserved) `reserved`";
        String fromTable = " FROM daily_cvm_cpu_good_bad_and_ugly WHERE stattime= ?";
        String condCvm = " and cvmtype in(select cvmtype from static_cvmtype where gpucard = 0)";
        String condGpu = " and cvmtype in(select cvmtype from static_cvmtype where gpucard > 0)";
        String groupBy = " group by cvmtype, zoneid";

        StringBuilder builder = new StringBuilder();
        if (Objects.equals(computeType, ComputeTypeEnum.CPU)) {
            builder.append(sql).append(fromTable).append(condCvm).append(groupBy);
        }else if (Objects.equals(computeType, ComputeTypeEnum.GPU)){
            builder.append(sql).append(fromTable).append(condGpu).append(groupBy);
        }

        List<VirtualCostDTO> raw = planDBHelper.getRaw(VirtualCostDTO.class, builder.toString(), statTime);
        if (ListUtils.isEmpty(raw)){
            return Lang.list();
        }
        return raw;
    }

}
