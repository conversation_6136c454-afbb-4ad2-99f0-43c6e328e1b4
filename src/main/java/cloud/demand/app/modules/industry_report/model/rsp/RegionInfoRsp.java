package cloud.demand.app.modules.industry_report.model.rsp;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.util.List;

@Data
public class RegionInfoRsp {

    private List<RegionInfoDTO> data;

    @Data
    public static class RegionInfoDTO{

        @Column("customhouse_title")
        private String customhouseTitle;

        @Column("area_name")
        private String areaName;

        @Column("region_name")
        private String regionName;

        @Column("zone_name")
        private String zoneName;
    }
}
