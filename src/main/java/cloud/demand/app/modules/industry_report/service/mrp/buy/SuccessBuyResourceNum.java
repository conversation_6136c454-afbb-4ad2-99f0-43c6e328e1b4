package cloud.demand.app.modules.industry_report.service.mrp.buy;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class SuccessBuyResourceNum extends BaseBuyInfo{

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.SUCCESS_BUY_RESOURCE_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        SuccessBuyResourceNum task = new SuccessBuyResourceNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        List<GroupTencentCloudBuyInfo> rawData = loadData();

        List<MRPTargetVO> result = rawData.stream().map(r -> {
            MRPTargetVO vo = GroupTencentCloudBuyInfo.toVO(r, content.getProduct());
            if (content.getProduct().equals("CVM")) {
                if (!r.getBizType().equals("cvm") || r.getSuccessCoreNum().compareTo(BigDecimal.ZERO) == 0) {
                    return null;
                }
            } else {
                if (!r.getBizType().equals("gpu") || r.getSuccessGpuNum().compareTo(BigDecimal.ZERO) == 0) {
                    return null;
                }
            }
            vo.setCoreNum(r.getSuccessCoreNum());
            vo.setGpuNum(r.getSuccessGpuNum());
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        addToResult(result);
        return result;
    }
}
