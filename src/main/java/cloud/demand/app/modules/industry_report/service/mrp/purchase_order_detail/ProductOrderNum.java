package cloud.demand.app.modules.industry_report.service.mrp.purchase_order_detail;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import com.pugwoo.dbhelper.DBHelper;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class ProductOrderNum extends BaseNum {

    private DBHelper demandDBHelper;
    private DBHelper rrpDBHelper;
    private DBHelper yuntiDBHelper;
    private PplDictService pplDictService;
    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.PRODUCT_ORDER_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        ProductOrderNum task = new ProductOrderNum();
        task.init(initContext);
        return task;
    }

    @Override
    public String sqlTemplate() {
        return "select '${type}' type,\n"
                + "       ${industryOrDept} industryOrDept,\n"
                + "       txy_region_name,\n"
                + "       device_type,\n"
                + "       year(expect_delivery_date)  year,\n"
                + "       month(expect_delivery_date) month,\n"
                + "       sum(logic_core_num)         num,\n"
                + "       sum(logic_num)              gpu_num\n"
                + "from report_purchase_order_detail\n";
    }
}
