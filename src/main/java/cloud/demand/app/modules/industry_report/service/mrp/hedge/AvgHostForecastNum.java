package cloud.demand.app.modules.industry_report.service.mrp.hedge;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import cloud.demand.app.modules.industry_report.service.mrp.AvgForecastNum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Data
@Slf4j
public class AvgHostForecastNum extends ProductHedgeNum {

    @Override
    public void init(Map<String, Object> initContext) {
        super.init(initContext);
        demandDBHelper = (DBHelper) initContext.get("demandDBHelper");
    }

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.AVG_HOST_FORECAST_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        AvgHostForecastNum task = new AvgHostForecastNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        Map<String, MRPTargetVO> m3TargetVOMap = mnTargetVOMap(-3);
        Map<String, MRPTargetVO> m2TargetVOMap = mnTargetVOMap(-2);
        Map<String, MRPTargetVO> m1TargetVOMap = mnTargetVOMap(-1);
        List<MRPTargetVO> result = AvgForecastNum.avgResult(m3TargetVOMap, m2TargetVOMap, m1TargetVOMap);
        addToResult(result);
        return result;
    }

    private Map<String, MRPTargetVO> mnTargetVOMap(int addMonth) {
        Map mnMap = monthAddIncreaseMonthVersion(addMonth, content.getYearMonths());
        if (CollectionUtils.isEmpty(mnMap)) {
            return new HashMap<>();
        }
        List<MRPTargetVO> mnProductHedgeNum = productHedgeNum(mnMap, content.getVersion(), hostForecast, addMonth);
        return ListUtils.toMap(mnProductHedgeNum, MRPTargetVO::withYearMonthKey, Function.identity());
    }
}
