package cloud.demand.app.modules.industry_report.service.mrp.buy;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import com.pugwoo.wooutils.collect.ListUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import yunti.boot.exception.ITException;

@Data
@Slf4j
public class SuccessBuyRate extends BaseBuyInfo{

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.SUCCESS_BUY_RATE;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return Lang.set(IndustryMRPReportTargetEnum.SUCCESS_BUY_NUM, IndustryMRPReportTargetEnum.FAIL_BUY_NUM);
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        SuccessBuyRate task = new SuccessBuyRate();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        try {
            SuccessBuyNum successBuyNum = new SuccessBuyNum();
            successBuyNum.init(this.initContext);
            FailBuyNum failBuyNum = new FailBuyNum();
            failBuyNum.init(this.initContext);

            ForkJoinTask<Object> successBuyNumTask = successBuyNum.fork();
            ForkJoinTask<Object> failBuyNumTask = failBuyNum.fork();

            List<MRPTargetVO> successBuyNumVOS = (List<MRPTargetVO>) successBuyNumTask.get();
            List<MRPTargetVO> failBuyNumVOS = (List<MRPTargetVO>) failBuyNumTask.get();
            Map<String, MRPTargetVO> successBuyNumVOMap = ListUtils.toMap(successBuyNumVOS,
                    MRPTargetVO::withYearMonthKey, Function.identity());
            Map<String, MRPTargetVO> failBuyNumVOMap = ListUtils.toMap(failBuyNumVOS,
                    MRPTargetVO::withYearMonthKey, Function.identity());
            Set<String> keys = new HashSet<>(successBuyNumVOMap.keySet());
            keys.addAll(failBuyNumVOMap.keySet());
            List<MRPTargetVO> result = new ArrayList<>();
            for (String key : keys) {
                MRPTargetVO resultVO;
                MRPTargetVO successBuyNumVO = successBuyNumVOMap.get(key);
                MRPTargetVO failBuyNumVO = failBuyNumVOMap.get(key);
                BigDecimal totalNum = BigDecimal.ZERO;
                if (successBuyNumVO == null) {
                    // 没有成功的数据就跳过
                    continue;
                }
                else {
                    resultVO = MRPTargetVO.copy(successBuyNumVO);
                    // coreNum和gpuNum都一样
                    totalNum = totalNum.add(successBuyNumVO.getCoreNum());
                }
                if (failBuyNumVO != null) {
                    totalNum = totalNum.add(failBuyNumVO.getCoreNum());
                }
                if (totalNum.compareTo(BigDecimal.ZERO) == 0) {
                    // 0 就跳过
                    continue;
                } else {
                    BigDecimal rate = successBuyNumVO.getCoreNum().divide(totalNum, 4, RoundingMode.HALF_UP);
                    resultVO.setData(String.format("%.2f%%", BigDecimal.valueOf(100).multiply(rate)));
                }
                resultVO.setCoreNum(null);
                resultVO.setGpuNum(null);

                result.add(resultVO);
            }

            isSumRow = false;
            isSumTime = false;
            monthToQ = false;
            addToResult(result);
            return result;
        } catch (Exception e) {
            log.error("", e);
            throw new ITException(e);
        }
    }
}
