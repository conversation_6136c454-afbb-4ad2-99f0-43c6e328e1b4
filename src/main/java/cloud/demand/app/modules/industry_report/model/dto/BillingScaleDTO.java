package cloud.demand.app.modules.industry_report.model.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BillingScaleDTO {

    @Column("stat_time")
    private String statTime;

    @Column("demand_type")
    private String demandType;

    private BigDecimal total;

    @Column("total_core")
    private BigDecimal totalCore;

    @Column("total_gpu_num")
    private BigDecimal totalGpuNum;

    @Column("industry_category")
    private String industryCategory;

}
