package cloud.demand.app.modules.industry_report.service.mrp;

import cloud.demand.app.modules.industry_report.model.dto.QueryIndustryMRPReportDAGCollector;
import cloud.demand.app.modules.industry_report.model.dto.QueryIndustryMRPReportDAGContent;
import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import cloud.demand.app.modules.industry_report.service.util.MonthUtil;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import java.util.function.Function;
import lombok.Data;
import org.springframework.util.CollectionUtils;

/**
 * 数据指标，这是一个工厂不是一个任务
 */
@Data
public abstract class TargetFetcher extends RecursiveTask<Object> {

    public boolean isAddToResult = true;
    public boolean monthToQ = true;
    public boolean isSumRow = true;
    public boolean isSumTime = true;

    public Map<String, Object> initContext;

    public QueryIndustryMRPReportDAGContent content;

    public QueryIndustryMRPReportDAGCollector collector;

    public static String fullGroupKey(MRPTargetVO vo) {
        List<Function<MRPTargetVO, Object>> functions = MRPTargetVO.groupKeys.get(MRPTargetVO.bitColumns - 1);
        return groupKey(functions, vo);
    }

    public static String groupKey(List<Function<MRPTargetVO, Object>> functions, MRPTargetVO vo) {
        String[] sa = new String[functions.size()];
        for (int i = 0; i < functions.size(); i++) {
            Function<MRPTargetVO, Object> f = functions.get(i);
            Object o = f.apply(vo);
            sa[i] = o == null ? "null" : o.toString();
        }
        return String.join("@", sa);
    }

    public abstract IndustryMRPReportTargetEnum token();

    public abstract Set<IndustryMRPReportTargetEnum> sonTargets();

    public Set<IndustryMRPReportTargetEnum> sonTargets(String version) {
        return sonTargets();
    }

    public void groupName(MRPTargetVO vo, String key, int index) {
        if ((index & MRPTargetVO.industryOrDeptBit) != MRPTargetVO.industryOrDeptBit) {
            vo.setIndustryOrDept("总计");
        }
        if ((index & MRPTargetVO.regionNameBit) != MRPTargetVO.regionNameBit) {
            vo.setRegionName("总计");
        }
        if ((index & MRPTargetVO.instanceTypeBit) != MRPTargetVO.instanceTypeBit) {
            vo.setInstanceType("总计");
        }
        if ((index & MRPTargetVO.demandTypeBit) != MRPTargetVO.demandTypeBit) {
            vo.setDemandType("总计");
        }
        // 去掉年月
        String shortKey = key.substring(0, key.lastIndexOf("@"));
        shortKey = shortKey.substring(0, shortKey.lastIndexOf("@"));
        vo.setGroupName(shortKey);

        // 这个组有可能属于其他组
        Set<String> groupBelongTo = new HashSet<>();
        String[] keyParts = key.split("@");
        if (!keyParts[0].equals("总计")) {
            String k = String.join("@", "总计", keyParts[1],
                    keyParts[2], keyParts[3], keyParts[4]);
            groupBelongTo.add(k);
        }
        if (!keyParts[1].equals("总计")) {
            String k = String.join("@", keyParts[0], "总计",
                    keyParts[2], keyParts[3], keyParts[4]);
            groupBelongTo.add(k);
        }
        if (!keyParts[3].equals("总计")) {
            String k = String.join("@", keyParts[0], keyParts[1],
                    keyParts[2], "总计", keyParts[4]);
            groupBelongTo.add(k);
        }
        if (!keyParts[4].equals("总计")) {
            String k = String.join("@", keyParts[0], keyParts[1],
                    keyParts[2], keyParts[3], "总计");
            groupBelongTo.add(k);
        }
        if (!(keyParts[0].equals("总计") && keyParts[1].equals("总计") && keyParts[3].equals("总计") && keyParts[4].equals("总计"))) {
            // 一定会属于总计组
            String k = String.join("@", "总计", "总计",
                    keyParts[2], "总计", "总计");
            groupBelongTo.add(k);
        }
        if (!groupBelongTo.isEmpty()) {
            vo.setGroupBelongTo(new ArrayList<>(groupBelongTo));
        }
    }

    public void groupBelongTo(MRPTargetVO vo, String key) {
        List<String> consumer = vo.getGroupBelongTo();
        if (consumer == null) {
            consumer = new ArrayList<>();
            vo.setGroupBelongTo(consumer);
        }// 去掉年月
        String shortKey = key.substring(0, key.lastIndexOf("@"));
        shortKey = shortKey.substring(0, shortKey.lastIndexOf("@"));
        consumer.add(shortKey);
    }

    public void sumTime(List<MRPTargetVO> mrpTargetVOS, boolean clearOrigin, Function<MRPTargetVO, String> keyF,
            Function<String, String> monthF) {
        Map<String, MRPTargetVO> newVOMap = new HashMap<>();
        for (MRPTargetVO vo : mrpTargetVOS) {
            String key = keyF.apply(vo);
            MRPTargetVO existVO = newVOMap.get(key);
            if (existVO == null) {
                existVO = MRPTargetVO.copy(vo);
                existVO.setMonth(monthF.apply(existVO.getMonth()));
                newVOMap.put(key, existVO);
            } else {
                existVO.setCoreNum(existVO.getCoreNum().add(vo.getCoreNum()));
                existVO.setGpuNum(existVO.getGpuNum().add(vo.getGpuNum()));
            }
        }
        if (!CollectionUtils.isEmpty(newVOMap)) {
            if (clearOrigin) {
                mrpTargetVOS.clear();
            }
            mrpTargetVOS.addAll(newVOMap.values());
        }
    }

    public void monthToQ(List<MRPTargetVO> mrpTargetVOS) {
        sumTime(mrpTargetVOS, true, MRPTargetVO::withQKey, MonthUtil::monthToQ);
    }

    public void sumRow(List<MRPTargetVO> mrpTargetVOS) {
        // 默认的sum方法
        List<MRPTargetVO> newVOS = new ArrayList<>();
        List<List<Function<MRPTargetVO, Object>>> groupKeys = MRPTargetVO.groupKeys;
        Set<Integer> passGroupIndex = content.getPassGroupIndex();
        for (int j = 0; j < groupKeys.size(); j++) {
            if (passGroupIndex.contains(j)) {
                continue;
            }
            List<Function<MRPTargetVO, Object>> functions = groupKeys.get(j);
            Map<String, MRPTargetVO> group = new HashMap<>();
            for (MRPTargetVO vo : mrpTargetVOS) {
                String key = groupKey(functions, vo);
                MRPTargetVO existVO = group.get(key);
                if (existVO == null) {
                    existVO = MRPTargetVO.copy(vo);
                    groupName(existVO, key, j);
                    group.put(key, existVO);
                } else {
                    existVO.setCoreNum(existVO.getCoreNum().add(vo.getCoreNum()));
                    existVO.setGpuNum(existVO.getGpuNum().add(vo.getGpuNum()));
                }
                groupBelongTo(vo, key);
            }
            newVOS.addAll(group.values());
        }
        mrpTargetVOS.addAll(newVOS);
    }

    public void addToResult(List<MRPTargetVO> mrpTargetVOS) {
        if (CollectionUtils.isEmpty(mrpTargetVOS)) {
            return;
        }
        if (monthToQ && "季度".equals(content.getTimeDegree())) {
            monthToQ(mrpTargetVOS);
        }
        if (isSumTime) {
            sumTime(mrpTargetVOS, false, MRPTargetVO::key, s -> "总计");
        }
        if (isSumRow) {
            sumRow(mrpTargetVOS);
        }
        if (isAddToResult) {
            collector.getResult().put(token(), mrpTargetVOS);
        }
    }

    public void init(Map<String, Object> initContext) {
        content = (QueryIndustryMRPReportDAGContent) initContext.get("content");
        collector = (QueryIndustryMRPReportDAGCollector) initContext.get("collector");
        this.initContext = initContext;
    }

    // 这才是创建一个任务
    public abstract RecursiveTask<Object> task(Map<String, Object> initContext);

    public Map<String, Object> copyInitContext() {
        return new HashMap<>(initContext);
    }
}
