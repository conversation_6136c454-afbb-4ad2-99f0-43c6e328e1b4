package cloud.demand.app.modules.industry_report.service.impl;

import cloud.demand.app.entity.plan.StaticGinstypeDO;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.industry_report.entity.PplBillingScaleDailyDO;
import cloud.demand.app.modules.industry_report.service.IndustryMRPDataService;
import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.DailyZoneAppidGinstypePaymodeApproleDO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

@Service
@Slf4j
public class IndustryMRPDataServiceImpl implements IndustryMRPDataService {

    @Resource
    private DBHelper cdCommonDbHelper;
    @Resource
    DBHelper demandDBHelper;
    @Resource
    DBHelper ckcldDBHelper;
    @Resource
    IndustryReportDictService industryReportDictService;

    @Resource
    TaskLogService taskLogService;

    @Override
    @TaskLog(taskName = "syncAndWashTencentCloudBuyInfo")
    public void syncAndWashTencentCloudBuyInfo(Set<String> dateStrSet) {
//        String sql = ORMUtils.getSql("/sql/industry_report/mrp/tencent_cloud_buy_info.sql");
//        // 这一步sql操作会比较久，因为数据源没有建立索引，所以要提前计算
//        // 数据量并不大，不用分页查询
//        List<Object> args = new ArrayList<>();
//        if (CollectionUtils.isEmpty(dateStrSet)) {
//            sql = sql.replace("${dateFilter}", " ");
//        } else {
//            sql = sql.replace("${dateFilter}", " and date_format(log.stattime, '%Y-%m-%d') in (?) ");
//            args.add(dateStrSet);
//        }
//        List<DailyTencentCloudBuyInfoDO> tencentCloudCvmBuyInfoDOList = planDBHelper.getRaw(DailyTencentCloudBuyInfoDO.class,
//                sql, args.toArray());
//        if (!CollectionUtils.isEmpty(tencentCloudCvmBuyInfoDOList)) {
//            // 如果有数据的话，先把旧数据删除了
//            Set<Long> appidSet = null;
//            if (CollectionUtils.isEmpty(dateStrSet)) {
//                // 使用查出来数据的日期集合
//                dateStrSet = new HashSet<>();
//                appidSet = new HashSet<>();
//                for (DailyTencentCloudBuyInfoDO tencentCloudCvmBuyInfoDO : tencentCloudCvmBuyInfoDOList) {
//                    dateStrSet.add(DateUtils.formatDate(tencentCloudCvmBuyInfoDO.getStatTime()));
//                    // 尽量减少一次循环
//                    appidSet.add(tencentCloudCvmBuyInfoDO.getAppId());
//                }
//            }
//            for (String dateStr : dateStrSet) {
//                .executeRaw(
//                        "ALTER TABLE cloud_demand.daily_tencent_cloud_buy_info_local ON CLUSTER default_cluster DROP PARTITION ?",
//                        dateStr);
//            }
//            // 补出uin和行业部门属性
//            if (appidSet == null) {
//                appidSet = new HashSet<>();
//                for (DailyTencentCloudBuyInfoDO tencentCloudCvmBuyInfoDO : tencentCloudCvmBuyInfoDOList) {
//                    appidSet.add(tencentCloudCvmBuyInfoDO.getAppId());
//                }
//            }
//            List<IndustryReportAppidInfoLatestWithoutJsonDO> industryReportAppidInfoLatestDOS = demandDBHelper.getAll(
//                    IndustryReportAppidInfoLatestWithoutJsonDO.class, "where appid in (?)", appidSet);
//            Map<Long, IndustryReportAppidInfoLatestWithoutJsonDO> appid2InfoMap = ListUtils.toMap(industryReportAppidInfoLatestDOS,
//                    IndustryReportAppidInfoLatestWithoutJsonDO::getAppid, Function.identity());
//            for (DailyTencentCloudBuyInfoDO tencentCloudCvmBuyInfoDO : tencentCloudCvmBuyInfoDOList) {
//                IndustryReportAppidInfoLatestWithoutJsonDO info = appid2InfoMap.get(tencentCloudCvmBuyInfoDO.getAppId());
//                if (info == null || info.getUin() == null) {
//                    continue;
//                }
//                long uin = info.getUin();
//                String industryDept = info.getIndustryDept();
//                Integer uinType = info.getUinType();
//
//                tencentCloudCvmBuyInfoDO.setUin(uin);
//                tencentCloudCvmBuyInfoDO.setUinType(uinType);
//                // 这里的行业部门有可能为空字符串
//                tencentCloudCvmBuyInfoDO.setIndustryDept(industryDept);
//            }
//            // 插入到数据库
//            .insertBatchWithoutReturnId(tencentCloudCvmBuyInfoDOList);
//        }
    }

    private String getCategoryByDept(String industryDept, Map<String, String> map) {
        if (StringUtils.isBlank(industryDept)) {
            return "未分类";
        }
        String category = map.get(industryDept);
        if (StringUtils.isBlank(category)) {
            return "未分类";
        }
        return category;
    }

    @Override
    @Synchronized(waitLockMillisecond = 100, namespace = "syncAndWashDailyBillingScaleTable0", throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "syncAndWashDailyBillingScaleTable")
    public void syncAndWashDailyBillingScaleTable(List<String> dateStrList) {
        // 如果dateStrSet为空，结束
        if (CollectionUtils.isEmpty(dateStrList)) {
            return;
        }
        // 加载机型数据，数据量很小，直接加载全部
        Map<String, StaticGinstypeDO> ginstypeDOMap = ListUtils.toMap(
                cdCommonDbHelper.getAll(StaticGinstypeDO.class),
                StaticGinstypeDO::getGinstype,
                Function.identity()
        );
        long limit = 20_0000;

        // 加载行业分类map
        Map<String, String> map = industryReportDictService.queryCategory();

        List<PplBillingScaleDailyDO> inserters = new ArrayList<>((int) limit);

        // 避免appid2InfoMap过大，每个循环new一个，gc时清除上一个map (废弃的注释）
        // 2023-05-10 实际上，相连的日切片的appid应该是差不多的，不需要每天都重新缓存一遍
        Map<Long, IndustryReportAppidInfoLatestWithoutJsonDO> appid2InfoMap = new HashMap<>();
        Set<Long> appid2InfoMapKeySet = appid2InfoMap.keySet();
        Set<Long> needQueryAppIdSet = new HashSet<>();

        // 2023-05-10 添加月末值相关指标
        // 建议同步时，日期是有序的，升序逆序都可以，这样可以保证同步时，尽量的使用同一个日切片数据，也不会造成缓存的日切片数据量过大
        String curLastMonthDateStr = null;
        Map<String, PplBillingScaleDailyDO> curLastMonthDailyInfoMap = null;

        // 一天一天跑数据
        for (long j = 0; j < dateStrList.size(); j++) {
            WhereSQL whereSQL;
            String dateStr = dateStrList.get((int) j);

            // 先拿到上一个月月末的日期
            String[] dateArray = dateStr.split("-");
            String lastMonthDateStr = DateUtils.formatDate(
                    DateUtils.addTime(DateUtils.parse(dateArray[0] + "-" + dateArray[1] + "-01"), Calendar.DATE, -1));
            if (curLastMonthDateStr == null || !curLastMonthDateStr.equals(lastMonthDateStr)) {
                // 如果不等于缓存的上一个月月末的日期，重新计算月末map
                curLastMonthDateStr = lastMonthDateStr;
                if (curLastMonthDailyInfoMap != null) {
                    curLastMonthDailyInfoMap.clear();
                }
                whereSQL = new WhereSQL();
                whereSQL.and("stat_time = ?", lastMonthDateStr);
                List<PplBillingScaleDailyDO> lastMonthDOS = ckcldDBHelper.getAll(PplBillingScaleDailyDO.class,
                        whereSQL.getSQL(), whereSQL.getParams());
                curLastMonthDailyInfoMap = ListUtils.toMap(lastMonthDOS, PplBillingScaleDailyDO::key,
                        Function.identity());
            }
            whereSQL = new WhereSQL();
            whereSQL.and("stat_time = ?", dateStr);
            // 先select count看看有多少条数据
            long count = ckcldDBHelper.getRaw(Long.class,
                    "select count(1) from daily_zone_appid_ginstype_paymode_approle " + whereSQL.getSQL(),
                    whereSQL.getParams()).get(0);
            if (count > 0) {
                // 能查到数据就删掉旧数据
                ckcldDBHelper.executeRaw(
                        "ALTER TABLE cloud_demand.ppl_billing_scale_daily_local ON CLUSTER default_cluster DROP PARTITION ?",
                        dateStr);
            } else {
                // 无数据
                continue;
            }
            // 这张表没有主键，为了保证ck的连续读取是有序的，全字段order by（除了时间）
            whereSQL.addOrderBy("app_id, zone_id, zone_name,"
                    + " ginstype, gins_family, paymode, app_role, "
                    + "cur_billcpu, buy_billcpu, rtn_billcpu, cur_freecpu, "
                    + "buy_freecpu, rtn_freecpu, biztype, gpu, region, "
                    + "region_name, area_name, customhouse_title");

            for (long offset = 0L; offset < count; offset += limit) {
                // 手工拼offset limit
                List<DailyZoneAppidGinstypePaymodeApproleDO> receivers = ckcldDBHelper.getAll(
                        DailyZoneAppidGinstypePaymodeApproleDO.class,
                        whereSQL.getSQL() + " limit " + offset + ", " + limit, whereSQL.getParams());
                // 先看一遍有没有appid不在appid2InfoMap的key set的
                for (long i = 0; i < receivers.size(); i++) {
                    DailyZoneAppidGinstypePaymodeApproleDO receiver = receivers.get((int) i);
                    if (!appid2InfoMapKeySet.contains(receiver.getAppId())) {
                        needQueryAppIdSet.add(receiver.getAppId());
                    }
                }
                if (!CollectionUtils.isEmpty(needQueryAppIdSet)) {
                    List<IndustryReportAppidInfoLatestWithoutJsonDO> queryAppIdInfos = demandDBHelper.getAll(
                            IndustryReportAppidInfoLatestWithoutJsonDO.class,
                            "where appid in (?)", needQueryAppIdSet);
                    if (!CollectionUtils.isEmpty(queryAppIdInfos)) {
                        for (long i = 0; i < queryAppIdInfos.size(); i++) {
                            IndustryReportAppidInfoLatestWithoutJsonDO industryReportAppidInfoLatestDO = queryAppIdInfos.get(
                                    (int) i);
                            appid2InfoMap.put(industryReportAppidInfoLatestDO.getAppid(),
                                    industryReportAppidInfoLatestDO);
                        }
                    }
                    needQueryAppIdSet.clear();
                }
                // 查出来后，进行清洗
                for (long i = 0; i < receivers.size(); i++) {
                    DailyZoneAppidGinstypePaymodeApproleDO receiver = receivers.get((int) i);
                    IndustryReportAppidInfoLatestWithoutJsonDO appidInfo = appid2InfoMap.get(receiver.getAppId());
                    if (appidInfo == null) {
                        // 如果没有账号信息，跳过
                        continue;
                    }
                    PplBillingScaleDailyDO inserter = PplBillingScaleDailyDO.copy(receiver);
                    // 关联账号信息
                    inserter.setCustomerUin(String.valueOf(appidInfo.getUin()));
                    inserter.setCustomerShortName(appidInfo.getCustomerShortName());
                    inserter.setCustomerName(appidInfo.getCustomerName());
                    inserter.setIndustryDept(appidInfo.getIndustryDept());
                    inserter.setOrgLabel(appidInfo.getIndustryDept());
                    //  是否直销客户
                    if (StringTools.isNotBlank(appidInfo.getBusinessManager())) {
                        //  判断主销售的组织架构是否跟行业部门一致，如果属于则为直销客户，否则为非直销客户
                        if (Objects.equals(inserter.getOrgLabel(), appidInfo.getBusinessManagerOaDept())) {
                            inserter.setIsDirectSellingCustomer(1);
                        } else {
                            inserter.setIsDirectSellingCustomer(0);
                        }
                    } else {
                        //  主销售名business_manager为空的用户默认为非直销客户
                        inserter.setIsDirectSellingCustomer(0);
                    }
                    //  是否个人客户
                    inserter.setIsPersonalCustomer(Objects.equals(appidInfo.getCustomerType(), 0) ? 1 : 0);
                    inserter.setIndustryCategory(getCategoryByDept(inserter.getIndustryDept(), map));
                    // 清洗出GPU信息
                    StaticGinstypeDO staticGinstypeDO = ginstypeDOMap.get(receiver.getGinstype());
                    if (staticGinstypeDO != null) {
                        Long cpu = staticGinstypeDO.getCpu();
                        Long gpuRatio = staticGinstypeDO.getGpuratio();
                        Long gpu = staticGinstypeDO.getGpu();
                        if (cpu != null && cpu != 0L && gpuRatio != null && gpuRatio != 0L && gpu != null) {
                            inserter.setCurBillgpu(inserter.getCurBillcpu() / (cpu / 100) * gpu / gpuRatio);
                            inserter.setDiffBillgpu(inserter.getDiffBillcpu() / (cpu / 100) * gpu / gpuRatio);
                            inserter.setCurFreegpu(inserter.getCurFreecpu() / (cpu / 100) * gpu / gpuRatio);
                            inserter.setDiffFreegpu(inserter.getDiffFreecpu() / (cpu / 100) * gpu / gpuRatio);
                        }
                    }
                    // 获取月末数据
                    PplBillingScaleDailyDO lastMonthDO = curLastMonthDailyInfoMap.get(inserter.key(lastMonthDateStr));
                    if (lastMonthDO != null) {
                        inserter.setMonthlyEndDiffBillCoreNum(inserter.getCurBillcpu() - lastMonthDO.getCurBillcpu());
                        inserter.setMonthlyEndDiffBillGpuNum(inserter.getCurBillgpu() - lastMonthDO.getCurBillgpu());
                        inserter.setMonthlyEndDiffFreeCoreNum(inserter.getCurFreecpu() - lastMonthDO.getCurFreecpu());
                        inserter.setMonthlyEndDiffFreeGpuNum(inserter.getCurFreegpu() - lastMonthDO.getCurFreegpu());
                    }
                    // 加入到插入列表
                    inserters.add(inserter);
                }
                // 清洗后入库
                ckcldDBHelper.insertBatchWithoutReturnId(inserters);
                inserters.clear();
            }
            taskLogService.genRunWarnLog("IndustryMRPDataService",
                    "syncAndWashDailyBillingScaleTable",
                    dateStr + ": 日切片执行完毕");
        }
    }
}
