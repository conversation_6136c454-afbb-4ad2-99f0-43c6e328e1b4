package cloud.demand.app.modules.industry_report.model.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;
import org.nutz.lang.Strings;

import java.math.BigDecimal;

@Data
public class PplItemOrderDTO {

    @Column("year")
    private Integer year;

    @Column("month")
    private Integer month;

    @Column("type")
    private String demandType;

    @Column("industry_category")
    private String industryDept;

    @Column("total_core")
    private BigDecimal totalCore;

    @Column("total_gpu_num")
    private BigDecimal totalGpuNum;

    private BigDecimal logicNum;

    public String getYearMonth(){
        return Strings.join("-", year, month >= 10 ? month : "0" + month);
    }

}