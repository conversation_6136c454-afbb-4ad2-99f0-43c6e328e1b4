package cloud.demand.app.modules.industry_report.service.mrp.hedge;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import com.pugwoo.dbhelper.DBHelper;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class LatestProductStockSupplyNum extends ProductHedgeNum {

    @Override
    public void init(Map<String, Object> initContext) {
        super.init(initContext);
        demandDBHelper = (DBHelper) initContext.get("demandDBHelper");
    }

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.LATEST_PRODUCT_STOCK_SUPPLY_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        LatestProductStockSupplyNum task = new LatestProductStockSupplyNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        Map monthLatestSupplyIdMap = monthLatestSupplyIdMap();
        List<MRPTargetVO> productHedgeNum = productHedgeNum(monthLatestSupplyIdMap, content.getVersion(), stockSupply);
        addToResult(productHedgeNum);
        return productHedgeNum;
    }
}
