package cloud.demand.app.modules.industry_report.job;

import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.industry_report.service.IndustryReportGenDataService;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class FillAppIdInfoTask {

    @Autowired
    private IndustryReportGenDataService genDataService;

    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
//    @Scheduled(cron = "0 0 7 * * ?")
    public void genIndustryReportData() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        String yearMonth = DateUtils.format(yesterday, "yyyy-MM");
        genDataService.genBillingScaleData(yearMonth);
        genDataService.syncData2ClickHouse2NewCk(yearMonth + "-01");
    }
}
