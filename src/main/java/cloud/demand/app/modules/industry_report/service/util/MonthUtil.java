package cloud.demand.app.modules.industry_report.service.util;

public class MonthUtil {

    public static String monthToQ(int month) {
        int idx = (month - 1) / 3 + 1;
        return "Q" + idx;
    }

    public static String monthToQ(String monthStr) {
        int month = Integer.parseInt(monthStr);
        return monthToQ(month);
    }

    public static String yearMonthToQ(String yearMonthStr) {
        String[] s = yearMonthStr.split("-");
        return s[0] + "-" + monthToQ(s[1]);
    }
}
