package cloud.demand.app.modules.industry_report.model.req;

import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.util.List;

@Data
public class PurchaseOrderReq {

    /**
     * 需求年月
     */
    private String beginYearMonth;

    /**
     * 需求年月
     */
    private String endYearMonth;

    /**
     * 行业部门
     */
    private List<String> category;

    /**
     * 单据类型
     */
    private List<String> type;

    private List<Integer> statusCode;

    /**
     * 以下是端到端明细跳转中的补充筛选维度
     */
    private List<String> product;
    private List<String> customhouseTitle;
    private List<String> areaName;
    private List<String> regionName;
    private List<String> industryDept;
    private List<String> instanceType;

    public WhereSQL genWhereSQL() {
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("plan_month between ? and ?", beginYearMonth, endYearMonth);
        whereSQL.and("(order_type in (1, 100) or (order_type=0 and status in (0,1,3,4,5,6)))");
//        whereSQL.and("STATUS NOT IN (0,3,5,8000127)");
        //  全选 == 不选，这里的枚举只有常规、短租
        if (ListUtils.isNotEmpty(type) && type.size() == 1) {
            if (type.contains("常规")){
                whereSQL.and("proj_set_name = '常规项目'");
            } else if(type.contains("其他")){
                whereSQL.and("proj_set_name <> '常规项目'");
            }
        }
        if (ListUtils.isNotEmpty(statusCode)) {
            whereSQL.and("status in (?)", statusCode);
        }
        if (ListUtils.isNotEmpty(industryDept)) {
            whereSQL.and("industry in (?)", industryDept);
        }
        return whereSQL;
    }
}

