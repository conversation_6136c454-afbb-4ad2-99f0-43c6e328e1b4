package cloud.demand.app.modules.industry_report.service.impl;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.modules.industry_report.model.dto.QueryIndustryMRPReportDAGCollector;
import cloud.demand.app.modules.industry_report.model.dto.QueryIndustryMRPReportDAGContent;
import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.req.QueryIndustryMRPReportReq;
import cloud.demand.app.modules.industry_report.model.req.QueryIndustryMRPReportReq.TimePeriod;
import cloud.demand.app.modules.industry_report.model.rsp.QueryIndustryMRPReportRsp;
import cloud.demand.app.modules.industry_report.model.rsp.QueryIndustryMRPReportRsp.Header;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import cloud.demand.app.modules.industry_report.service.IndustryMRPReportService;
import cloud.demand.app.modules.industry_report.service.mrp.TargetFetcher;
import cloud.demand.app.modules.industry_report.service.util.MonthUtil;
import cloud.demand.app.modules.industry_report.web.IndustryReportController;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDataDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.MainInstanceTypeDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinTask;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.ITException;

@Service
@Slf4j
public class IndustryMRPReportServiceImpl implements IndustryMRPReportService {

    private final static int executor_size = 16;

    private final ForkJoinPool forkJoinPool = new ForkJoinPool(executor_size);

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper yuntiDBHelper;
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private IndustryReportController industryReportController;

    private List<IndustryMRPReportTargetEnum> tidyQueryTargets(List<String> queryTargets) {
        // 去重、排序、合法检查
        List<IndustryMRPReportTargetEnum> targetEnums
                = new ArrayList<>(IndustryMRPReportTargetEnum.allValidList);
        targetEnums.removeIf(e -> !queryTargets.contains(e.getField()));
        return targetEnums;
    }

    private List<YearMonth> tidyTimeRange(TimePeriod timePeriod) {
        if (timePeriod == null) {
            return null;
        }
        YearMonth start = timePeriod.getStart();
        YearMonth end = timePeriod.getEnd();
        if (start == null || end == null
                || start.getYear() == null || start.getMonth() == null
                || end.getYear() == null || end.getMonth() == null) {
            return null;
        }
        return cloud.demand.app.common.utils.DateUtils.listYearMonth(start.getYear(), start.getMonth(),
                end.getYear(), end.getMonth());
    }

    private void mainInstanceFamilyAndZone(Boolean mainInstanceFamilyAndZone,
            QueryIndustryMRPReportDAGContent content) {
        if (mainInstanceFamilyAndZone != null && mainInstanceFamilyAndZone) {
            BaseDataDTO<MainInstanceTypeDTO> rawResult = pplDictService.queryMainInstanceType();
            List<MainInstanceTypeDTO> container = rawResult.getData();
            // 加载主力园区和主力机型
            Set<String> zoneNames = new HashSet<>();
            Set<String> instanceFamilies = new HashSet<>();
            if (!CollectionUtils.isEmpty(container)) {
                for (MainInstanceTypeDTO dto : container) {
                    if (!StringUtils.isBlank(dto.getZoneName())) {
                        zoneNames.add(dto.getZoneName());
                    }
                    if (!StringUtils.isBlank(dto.getInstanceFamily())) {
                        instanceFamilies.add(dto.getInstanceFamily());
                    }
                }
            }
            List<String> inputZoneNames = content.getZoneNames();
            if (!CollectionUtils.isEmpty(inputZoneNames)) {
                // 取交集
                inputZoneNames.removeIf(d -> !zoneNames.contains(d));
                if (CollectionUtils.isEmpty(inputZoneNames)) {
                    inputZoneNames.add("无数据");
                }
            } else {
                content.setZoneNames(new ArrayList<>(zoneNames));
            }
            List<String> inputInstanceTypes = content.getInstanceTypes();
            if (!CollectionUtils.isEmpty(inputInstanceTypes)) {
                // 取交集
                inputInstanceTypes.removeIf(d -> !instanceFamilies.contains(d));
                if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                    inputInstanceTypes.add("无数据");
                }
            } else {
                content.setInstanceTypes(new ArrayList<>(instanceFamilies));
            }
        }
    }

    private void regionTypesFilter(List<String> regionTypes, QueryIndustryMRPReportDAGContent content) {
        if (!CollectionUtils.isEmpty(regionTypes)) {
            boolean hasInland = regionTypes.contains("境内");
            boolean hasOverSeas = regionTypes.contains("境外");
            String type;
            if (hasInland && hasOverSeas) {
                // 都筛选等于不筛选
                return;
            } else if (!hasInland && !hasOverSeas) {
                // 没筛选等于不筛选
                return;
            } else if (hasInland) {
                type = "国内";
            } else {
                type = "海外";
            }
            // 加载地域信息表
            List<Map> regionAreaList = shuttleDBHelper.getRaw(Map.class,
                    "select r.region region, a.domes_foreig domes_foreig\n"
                            + "from region r\n"
                            + "left join area a on a.id = r.area_id\n"
                            + "where a.domes_foreig is not null");
            Map<String, List<String>> areaRegionMap = ListUtils.toMapList(regionAreaList,
                    m -> (String) m.get("domes_foreig"), m -> (String) m.get("region"));
            List<String> regions = areaRegionMap.get(type);
            if (!CollectionUtils.isEmpty(regions)) {
                List<String> newRegionList = new ArrayList<>(regions);
                List<String> inputRegionNames = content.getRegionNames();
                if (!CollectionUtils.isEmpty(inputRegionNames)) {
                    inputRegionNames.removeIf(d -> !newRegionList.contains(d));
                } else {
                    content.setRegionNames(newRegionList);
                }
            } else {
                content.setRegionNames(ListUtils.newArrayList("无数据"));
                content.setZoneNames(ListUtils.newArrayList("无数据"));
            }
        }
    }

    private void renderInstanceType(Boolean isRendering, QueryIndustryMRPReportDAGContent content) {
        if (isRendering != null) {
            // 只要渲染机型或非渲染机型
            List<String> inputInstanceTypes = content.getInstanceTypes();
            List<String> allGinsFamily = industryReportController.queryAllGinsFamily();
            Set<String> targetGinsFamily;
            if (isRendering) {
                targetGinsFamily = allGinsFamily.stream()
                        .filter(e -> e.startsWith("RS"))
                        .collect(Collectors.toSet());
            } else {
                targetGinsFamily = allGinsFamily.stream()
                        .filter(e -> !e.startsWith("RS"))
                        .collect(Collectors.toSet());
            }
            if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                // 如果原来没有筛选实例机型，那直接把（非）渲染机型列表写入到实例机型
                inputInstanceTypes = new ArrayList<>(targetGinsFamily);
                content.setInstanceTypes(inputInstanceTypes);
            } else {
                // 如果原来有筛选实例机型，那就取交集
                inputInstanceTypes.removeIf(e -> !targetGinsFamily.contains(e));
                if (CollectionUtils.isEmpty(inputInstanceTypes)) {
                    // 如果取完交集后，筛选的实例机型是空的，那就放入无数据
                    inputInstanceTypes.add("无数据");
                }
            }
        }
    }

    private QueryIndustryMRPReportDAGContent context(QueryIndustryMRPReportReq req,
            List<YearMonth> yearMonths, List<IndustryMRPReportTargetEnum> targetEnums) {
        QueryIndustryMRPReportDAGContent context = QueryIndustryMRPReportDAGContent.context(req);
        context.setYearMonths(yearMonths);
        context.setOriginQueryTargetEnum(targetEnums);
        regionTypesFilter(req.getCountry(), context);
        mainInstanceFamilyAndZone(req.getMainInstanceFamilyAndZone(), context);
        renderInstanceType(req.getIsRendering(), context);
        return context;
    }

    private void quickSetRspHeaders(List<IndustryMRPReportTargetEnum> targetEnums, String timeDegree,
            List<YearMonth> yearMonths, QueryIndustryMRPReportRsp rsp) {
        List<String> times = new ArrayList<>();
        for (YearMonth yearMonth : yearMonths) {
            String time;
            if (timeDegree.equals("月度")) {
                time = yearMonth.toDateStr();
            } else {
                time = yearMonth.getYear() + "-" + MonthUtil.monthToQ(yearMonth.getMonth());
            }
            if (!times.contains(time)) {
                times.add(time);
            }
        }
        ListUtils.sortAscNullLast(times, Function.identity());
        times.add(0, "总计");
        List<Header> headers = new ArrayList<>();
        for (String time : times) {
            for (IndustryMRPReportTargetEnum targetEnum : targetEnums) {
                Header header = new Header();
                header.setParentHeader(new Header("", targetEnum.getType(),
                        new Header("", time)));
                header.setDisplay(targetEnum.getDisplay());
                header.setField(String.join("@", time, targetEnum.getField()));
                headers.add(header);
            }
        }
        rsp.setOptionalHeaders(headers);
    }

    private void addToTress(TreeNode sonNode, Map<TreeNode, TreeNode> tress, String version, TreeNode father) {
        // 这一段的作用：
        // 如果自己在森林中了，把自己的父亲信息更新一下
        // 如果不在，添加到森林中
        sonNode = tress.getOrDefault(sonNode, sonNode);
        if (father != null) {
            sonNode.fathers.add(father);
        }
        tress.put(sonNode, sonNode);

        // 把自己的儿子也加入到森林中
        Set<IndustryMRPReportTargetEnum> sons = sonNode.getValue().getTargetFetcher().sonTargets(version);
        if (sons != null) {
            for (IndustryMRPReportTargetEnum son : sons) {
                TreeNode treeNode = new TreeNode(son);
                addToTress(treeNode, tress, version, sonNode);
            }
        }
    }

    private void dag(List<IndustryMRPReportTargetEnum> targetEnums,
            String version, Map<TreeNode, TreeNode> tress) {
        for (IndustryMRPReportTargetEnum targetEnum : targetEnums) {
            // 先替换成实际指标
            targetEnum = replaceEnum(targetEnum, version);
            // 构造一个树结点，让其尝试加入到森林中
            TreeNode treeNode = new TreeNode(targetEnum);
            addToTress(treeNode, tress, version, null);
        }
    }

    private IndustryMRPReportTargetEnum replaceEnum(IndustryMRPReportTargetEnum targetEnum,
            String version) {
        // 例：预测量 -> 最新预测量
        TargetFetcher targetFetcher = targetEnum.getTargetFetcher();
        if (targetFetcher == null) {
            targetEnum = targetEnum.findRealTargetEnum(version);
        }
        // 初始化指标获取器的变量
        return targetEnum;
    }

    private List<IndustryMRPReportTargetEnum> actualQueryTargetEnums(Map<TreeNode, TreeNode> tress) {
        List<IndustryMRPReportTargetEnum> actualQueryTargetEnums = new ArrayList<>();
        for (TreeNode treeNode : tress.keySet()) {
            if (CollectionUtils.isEmpty(treeNode.getFathers())) {
                actualQueryTargetEnums.add(treeNode.getValue());
            }
        }
        return actualQueryTargetEnums;
    }

    private void authFilter(QueryIndustryMRPReportDAGCollector collector) {
        String username = LoginUtils.getUserName();
        if ("no".equalsIgnoreCase(username) || "UNKNOWN".equalsIgnoreCase(username)) {
            // 不用过滤了，能看到全部数据
            return;
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(username);
        Set<String> authIndustryDepts = new HashSet<>();
        Set<String> authProducts = new HashSet<>();
        for (IndustryDemandAuthDO authDO : auths) {
            if (IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(authDO.getRole())) {
                // 不用过滤了，能看到全部数据
                return;
            }
            if (authDO.getProduct() != null) {
                if (authDO.getProduct().contains("CVM")
                        || authDO.getProduct().contains("CVM&CBS")) {
                    // 看有哪些行业的权限
                    String industryDepts = authDO.getIndustry();
                    if (!StringUtils.isBlank(industryDepts)) {
                        String[] industryDeptArray = industryDepts.split(";");
                        for (String s : industryDeptArray) {
                            if (!StringUtils.isBlank(s)) {
                                authIndustryDepts.add(s);
                            }
                        }
                    }
                }
                if (authDO.getIndustry().equals("内部业务部")) {
                    // 看有哪些产品的权限
                    String products = authDO.getProduct();
                    if (!StringUtils.isBlank(products)) {
                        String[] productArray = products.split(";");
                        for (String s : productArray) {
                            if (!StringUtils.isBlank(s)) {
                                authProducts.add(s);
                            }
                        }
                    }
                }
            }
        }
        Collection<List<MRPTargetVO>> eachResult = collector.getResult().values();
        for (List<MRPTargetVO> resultList : eachResult) {
            if (!CollectionUtils.isEmpty(resultList)) {
                resultList.removeIf(vo -> {
                    if ("外部行业".equals(vo.getType())) {
                        // 把有权限的行业保留
                        return !authIndustryDepts.contains(vo.getIndustryOrDept());
                    } else if ("内部业务".equals(vo.getType())) {
                        // 有内部业务部的对应产品权限就能保留
                        return !authProducts.contains("内部业务部");
                    }
                    return true;
                });
            }
        }
    }


    private void makeRsp(QueryIndustryMRPReportDAGCollector collector,
            QueryIndustryMRPReportRsp rsp, String version) {
        Map<String, Map<String, Object>> rspDataMap = new HashMap<>();
        Set<String> queryFields = new HashSet<>();
        List<Header> headers = rsp.getOptionalHeaders();
        for (Header header : headers) {
            String hField = header.getField();
            // field的格式是 时间@指标代码，所以要切割字符串
            String[] fieldArray = hField.split("@");
            String field = fieldArray[1];
            if (!queryFields.contains(field)) {
                // 找到本次查询的实际指标
                IndustryMRPReportTargetEnum realTargetEnum =
                        IndustryMRPReportTargetEnum.getByField(field).findRealTargetEnum(version);
                // 在collector的结果里找对应的列表
                List<MRPTargetVO> rawData = collector.getResult().get(realTargetEnum);
                // 按横轴为key，做group by
                Map<String, List<MRPTargetVO>> mapData = ListUtils.toMapList(rawData, MRPTargetVO::key,
                        Function.identity());
                // 开始加载数据
                for (Entry<String, List<MRPTargetVO>> entry : mapData.entrySet()) {
                    String key = entry.getKey();
                    List<MRPTargetVO> values = entry.getValue();
                    for (MRPTargetVO vo : values) {
                        if (vo.getGroupName() == null) {
                            // 目前只有最细的维度下会没有groupName，那它也属于自己的这一个组
                            String groupKey = TargetFetcher.fullGroupKey(vo);
                            String shortKey = groupKey.substring(0, groupKey.lastIndexOf("@"));
                            shortKey = shortKey.substring(0, shortKey.lastIndexOf("@"));
                            vo.setGroupName(shortKey);
                        }
                        Map<String, Object> dataHolder = rspDataMap.get(key);
                        if (dataHolder == null) {
                            dataHolder = vo.toMap(field);
                            rspDataMap.put(key, dataHolder);
                        } else {
                            if (vo.getProduct().equals("CVM")) {
                                dataHolder.put(vo.time() + "@" + field,
                                        vo.getCoreNum() == null ? vo.getData() : vo.getCoreNum());
                            } else {
                                dataHolder.put(vo.time() + "@" + field,
                                        vo.getGpuNum() == null ? vo.getData() : vo.getGpuNum());
                            }
                        }
                    }
                }
                queryFields.add(field);
            }
        }
        // 前端已排序
        rsp.setData(rspDataMap.values());
    }


    @Override
    public QueryIndustryMRPReportRsp query(QueryIndustryMRPReportReq req) {
        // --- 参数检查和响应预先设置 ---
        QueryIndustryMRPReportRsp rsp = new QueryIndustryMRPReportRsp();
        List<String> queryTargets = req.getQueryTargets();
        List<YearMonth> yearMonths = tidyTimeRange(req.getTimePeriod());
        if (CollectionUtils.isEmpty(queryTargets) || CollectionUtils.isEmpty(yearMonths)) {
            return rsp;
        }
        List<IndustryMRPReportTargetEnum> targetEnums = tidyQueryTargets(queryTargets);
        quickSetRspHeaders(targetEnums, req.getTimeDegree(), yearMonths, rsp);
        log.info("after set req and rsp");
        // 分析指标的DAG关系，扩展指标列
        Map<TreeNode, TreeNode> tress = new HashMap<>();
        QueryIndustryMRPReportDAGContent content = context(req, yearMonths, targetEnums);
        QueryIndustryMRPReportDAGCollector collector = new QueryIndustryMRPReportDAGCollector();
        dag(targetEnums, content.getVersion(), tress);
        log.info("after dag");
        // 最后只需要从森林里的每个树的根结点触发计算就行
        List<IndustryMRPReportTargetEnum> actualQueryTargetEnums = actualQueryTargetEnums(tress);
        log.info("after actual query target enums");
        // 并发拉取数据
        Map<String, Object> initContext = new HashMap<>();
        initContext.put("demandDBHelper", demandDBHelper);
        initContext.put("yuntiDBHelper", yuntiDBHelper);
        initContext.put("pplDictService", pplDictService);
        initContext.put("rrpDBHelper", rrpDBHelper);
        initContext.put("content", content);
        initContext.put("collector", collector);
        log.info("after set init context");
        List<ForkJoinTask<Object>> tasks = new ArrayList<>();
        for (IndustryMRPReportTargetEnum targetEnum : actualQueryTargetEnums) {
            ForkJoinTask<Object> task = forkJoinPool.submit(targetEnum.getTargetFetcher().task(initContext));
            tasks.add(task);
        }
        log.info("after submit fork join task");
        try {
            // 等待计算结束，最多等待1分钟
            for (ForkJoinTask<Object> task : tasks) {
                task.get(1, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            log.error("", e);
            throw new ITException(e);
        }
        log.info("after get fork join task");
        // 权限过滤
        authFilter(collector);
        log.info("after authFilter");
        // 根据参数检查后的原始用户入参，来还原rsp
        makeRsp(collector, rsp, req.getVersion());
        log.info("after makeRsp");
        return rsp;
    }

    @Override
    public List<Map<String, Object>> getQueryTargetList() {
        List<IndustryMRPReportTargetEnum> rawEnum = IndustryMRPReportTargetEnum.allValidList;
        List<Map<String, Object>> result = new ArrayList<>();
        for (IndustryMRPReportTargetEnum e : rawEnum) {
            result.add(MapUtils.of("type", e.getType(),
                    "display", e.getDisplay(),
                    "field", e.getField()));
        }
        return result;
    }

    @Data
    static class TreeNode {

        private IndustryMRPReportTargetEnum value;
        private Set<TreeNode> fathers = new HashSet<>();

        public TreeNode(IndustryMRPReportTargetEnum value) {
            this.value = value;
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            TreeNode treeNode = (TreeNode) o;
            return Objects.equals(value, treeNode.value);
        }

        @Override
        public int hashCode() {
            return Objects.hash(value);
        }
    }
}
