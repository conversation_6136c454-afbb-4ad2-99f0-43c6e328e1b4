package cloud.demand.app.modules.industry_report.service.mrp.hedge;

import cloud.demand.app.modules.industry_report.model.enums.IndustryMRPReportTargetEnum;
import cloud.demand.app.modules.industry_report.model.vo.MRPTargetVO;
import com.pugwoo.dbhelper.DBHelper;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.RecursiveTask;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Data
@Slf4j
public class BenchHostForecastNum extends ProductHedgeNum {

    @Override
    public void init(Map<String, Object> initContext) {
        super.init(initContext);
        demandDBHelper = (DBHelper) initContext.get("demandDBHelper");
    }

    @Override
    public IndustryMRPReportTargetEnum token() {
        return IndustryMRPReportTargetEnum.BENCH_HOST_FORECAST_NUM;
    }

    @Override
    public Set<IndustryMRPReportTargetEnum> sonTargets() {
        return null;
    }

    @Override
    public RecursiveTask<Object> task(Map<String, Object> initContext) {
        BenchHostForecastNum task = new BenchHostForecastNum();
        task.init(initContext);
        return task;
    }

    @Override
    protected Object compute() {
        Map<String, List<String>> rawBenchVersionMap = monthAddIncreaseMonthVersion(-3, content.getYearMonths());
        if (CollectionUtils.isEmpty(rawBenchVersionMap)) {
            return null;
        } else {
            Map benchVersionMap = new HashMap<>();
            for (Entry<String, List<String>> entry : rawBenchVersionMap.entrySet()) {
                benchVersionMap.put(entry.getKey(), entry.getValue().get(0));
            }
            List<MRPTargetVO> productHedgeNum = productHedgeNum(benchVersionMap, content.getVersion(), hostForecast);
            addToResult(productHedgeNum);
            return productHedgeNum;
        }
    }
}
