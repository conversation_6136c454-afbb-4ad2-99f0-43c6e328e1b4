package cloud.demand.app.modules.industry_report.entity;

// package a.b.c;

import cloud.demand.app.modules.p2p.ppl13week.dto.DailyZoneAppidGinstypePaymodeApprole;
import cloud.demand.app.modules.p2p.ppl13week.dto.DailyZoneAppidGinstypePaymodeApproleDO;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.ToString;

import com.pugwoo.dbhelper.annotation.Column;
import java.time.LocalDate;
import com.pugwoo.dbhelper.annotation.Table;

@Data
@ToString
@Table("ppl_billing_scale_daily")
public class PplBillingScaleDailyDO {

    /** 统计日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** appid<br/>Column: [app_id] */
    @Column(value = "app_id")
    private Long appId;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 可用区id<br/>Column: [zone_id] */
    @Column(value = "zone_id")
    private Long zoneId;

    /** 可用区名<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 区域<br/>Column: [area_name] */
    @Column(value = "area_name")
    private String areaName;

    /** region名<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** ginsfamily<br/>Column: [gins_family] */
    @Column(value = "gins_family")
    private String ginsFamily;

    /** 业务AppRole<br/>Column: [app_role] */
    @Column(value = "app_role")
    private String appRole;

    /** 组织架构标签<br/>Column: [org_label] */
    @Column(value = "org_label")
    private String orgLabel;

    /** 所属业务<br/>Column: [biz_type] */
    @Column(value = "biz_type")
    private String bizType;

    /** 当月净增计费用量（核数）<br/>Column: [diff_billcpu] */
    @Column(value = "diff_billcpu")
    private Double diffBillcpu;

    /** 当月平均计费用量（核数）<br/>Column: [cur_billcpu] */
    @Column(value = "cur_billcpu")
    private Double curBillcpu;

    /** 当月平均服务用量（核数）<br/>Column: [cur_freecpu] */
    @Column(value = "cur_freecpu")
    private Double curFreecpu;

    /** 需求类型(新增/退回)<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 是否直销客户(是1否0)<br/>Column: [is_direct_selling_customer] */
    @Column(value = "is_direct_selling_customer")
    private Integer isDirectSellingCustomer;

    /** 是否渲染客户(是1否0)，这个表是不要的字段，所以是null<br/>Column: [is_rendering_customer] */
    @Column(value = "is_rendering_customer")
    private Integer isRenderingCustomer;

    /** 是否个人用户(是1否0)<br/>Column: [is_personal_customer] */
    @Column(value = "is_personal_customer")
    private Integer isPersonalCustomer;

    /** CPU OR GPU<br/>Column: [cpu_or_gpu] */
    @Column(value = "cpu_or_gpu")
    private String cpuOrGpu;

    /** 当月净增服务用量（核数）<br/>Column: [diff_freecpu] */
    @Column(value = "diff_freecpu")
    private Double diffFreecpu;

    /** 当月平均计费用量（卡数）<br/>Column: [cur_billgpu] */
    @Column(value = "cur_billgpu")
    private Double curBillgpu;

    /** 当月净增计费用量（卡数）<br/>Column: [diff_billgpu] */
    @Column(value = "diff_billgpu")
    private Double diffBillgpu;

    /** 当月平均服务用量（卡数）<br/>Column: [cur_freegpu] */
    @Column(value = "cur_freegpu")
    private Double curFreegpu;

    /** 当月净增服务用量（卡数）<br/>Column: [diff_freegpu] */
    @Column(value = "diff_freegpu")
    private Double diffFreegpu;

    /** 行业分类<br/>Column: [industry_category] */
    @Column(value = "industry_category")
    private String industryCategory;

    /** 是否流转客户(是1否0)，这个表是不要的字段，所以是null<br/>Column: [is_turnover_customer] */
    @Column(value = "is_turnover_customer")
    private Integer isTurnoverCustomer;

    /** 机型<br/>Column: [gins_type] */
    @Column(value = "gins_type")
    private String ginsType;

    /** 月末存量相减计费核<br/>Column: [monthly_end_diff_bill_core_num] */
    @Column(value = "monthly_end_diff_bill_core_num")
    private Double monthlyEndDiffBillCoreNum;

    /** 月末存量相减服务核<br/>Column: [monthly_end_diff_free_core_num] */
    @Column(value = "monthly_end_diff_free_core_num")
    private Double monthlyEndDiffFreeCoreNum;

    /** 月末存量相减计费卡<br/>Column: [monthly_end_diff_bill_gpu_num] */
    @Column(value = "monthly_end_diff_bill_gpu_num")
    private Double monthlyEndDiffBillGpuNum;

    /** 月末存量相减服务卡<br/>Column: [monthly_end_diff_free_gpu_num] */
    @Column(value = "monthly_end_diff_free_gpu_num")
    private Double monthlyEndDiffFreeGpuNum;

    /** 计费模式<br/>Column: [paymode] */
    @Column(value = "pay_mode")
    private String payMode;

    public String key(String dateStr) {
        return String.join("@", dateStr,
                String.valueOf(appId), String.valueOf(zoneId),
                ginsType, payMode, appRole);
    }

    public String key(LocalDate localDate) {
        return key(DateUtils.formatDate(localDate));
    }

    public String key() {
        return key(statTime);
    }

    public static PplBillingScaleDailyDO copy(
            DailyZoneAppidGinstypePaymodeApproleDO dailyZoneAppidGinstypePaymodeApprole) {
        PplBillingScaleDailyDO pplBillingScaleDailyDO = new PplBillingScaleDailyDO();
        pplBillingScaleDailyDO.setStatTime(dailyZoneAppidGinstypePaymodeApprole.getStatTime());
        pplBillingScaleDailyDO.setAppId(dailyZoneAppidGinstypePaymodeApprole.getAppId());
        pplBillingScaleDailyDO.setZoneId(dailyZoneAppidGinstypePaymodeApprole.getZoneId());
        pplBillingScaleDailyDO.setZoneName(dailyZoneAppidGinstypePaymodeApprole.getZoneName());
        pplBillingScaleDailyDO.setCustomhouseTitle(dailyZoneAppidGinstypePaymodeApprole.getCustomhouseTitle());
        pplBillingScaleDailyDO.setAreaName(dailyZoneAppidGinstypePaymodeApprole.getAreaName());
        pplBillingScaleDailyDO.setRegionName(dailyZoneAppidGinstypePaymodeApprole.getRegionName());
        pplBillingScaleDailyDO.setGinsFamily(dailyZoneAppidGinstypePaymodeApprole.getGinsFamily());
        pplBillingScaleDailyDO.setAppRole(dailyZoneAppidGinstypePaymodeApprole.getAppRole());
        pplBillingScaleDailyDO.setBizType(dailyZoneAppidGinstypePaymodeApprole.getBiztype());
        pplBillingScaleDailyDO.setDiffBillcpu(dailyZoneAppidGinstypePaymodeApprole.getBuyBillcpu().subtract(
                dailyZoneAppidGinstypePaymodeApprole.getRtnBillcpu()).doubleValue());
        pplBillingScaleDailyDO.setCurBillcpu(dailyZoneAppidGinstypePaymodeApprole.getCurBillcpu().doubleValue());
        pplBillingScaleDailyDO.setCurFreecpu(dailyZoneAppidGinstypePaymodeApprole.getCurFreecpu().doubleValue());
        pplBillingScaleDailyDO.setDemandType(pplBillingScaleDailyDO.getDiffBillcpu() >= 0? "购买" : "退回");
        pplBillingScaleDailyDO.setIsRenderingCustomer(null);
        pplBillingScaleDailyDO.setCpuOrGpu(dailyZoneAppidGinstypePaymodeApprole.getGpu() > 0 ? "GPU" : "CPU");
        pplBillingScaleDailyDO.setDiffFreecpu(dailyZoneAppidGinstypePaymodeApprole.getBuyFreecpu().subtract(
                dailyZoneAppidGinstypePaymodeApprole.getRtnFreecpu()).doubleValue());
        pplBillingScaleDailyDO.setCurBillgpu(0.0);
        pplBillingScaleDailyDO.setDiffBillgpu(0.0);
        pplBillingScaleDailyDO.setCurFreegpu(0.0);
        pplBillingScaleDailyDO.setDiffFreegpu(0.0);
        pplBillingScaleDailyDO.setIsTurnoverCustomer(null);
        pplBillingScaleDailyDO.setGinsType(dailyZoneAppidGinstypePaymodeApprole.getGinstype());
        pplBillingScaleDailyDO.setPayMode(dailyZoneAppidGinstypePaymodeApprole.getPaymode());
        // 默认是自己的存量值
        pplBillingScaleDailyDO.setMonthlyEndDiffBillCoreNum(pplBillingScaleDailyDO.getCurBillcpu());
        pplBillingScaleDailyDO.setMonthlyEndDiffBillGpuNum(pplBillingScaleDailyDO.getCurBillgpu());
        pplBillingScaleDailyDO.setMonthlyEndDiffFreeCoreNum(pplBillingScaleDailyDO.getCurFreecpu());
        pplBillingScaleDailyDO.setMonthlyEndDiffFreeGpuNum(pplBillingScaleDailyDO.getCurFreegpu());
        return pplBillingScaleDailyDO;
    }

}