package cloud.demand.app.modules.yunti_demand.Entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/**
 * @author: williamhhu
 * @createTime: 2023/12/06 15:31
 * @description:
 */
@Table("bas_obs_cloud_cvm_type")
@Data
public class BasObsCloudCvmTypeDO {

    @Column("CvmInstanceId")
    private Integer cvmInstanceId;

    @Column(value = "CvmInstanceGroup")
    private String cvmInstanceGroup;

    @Column(value = "CvmInstanceModel")
    private String cvmInstanceModel;

    @Column(value = "CpuAmount")
    private Integer cpuAmount;

    @Column(value = "DResClassId")
    private Integer dResClassId;

    @Column(value = "CvmInstanceTypeCode")
    private String cvmInstanceTypeCode;

    @Column(value = "CvmInstanceClass")
    private String cvmInstanceClass;

    @Column(value = "HostDeviceClass")
    private String deviceType;

    @Column(value = "RamAmount")
    private Integer ramAmount;

    @Column(value = "CvmInstanceType")
    private String cvmInstanceType;

    @Column(value = "IsApply")
    private Integer isApply;

    @Column(value = "CoreType")
    private Integer coreType;

    @Column(value = "FirmType")
    private String firmType;

    @Column(value = "GpuType")
    private String gpuType;

    @Column(value = "GpuAmount")
    private Double gpuAmount;

    @Column(value = "DiskBlockNum")
    private Integer diskBlockNum;

    @Column(value = "DiskBlockSize")
    private Integer diskBlockSize;
}

