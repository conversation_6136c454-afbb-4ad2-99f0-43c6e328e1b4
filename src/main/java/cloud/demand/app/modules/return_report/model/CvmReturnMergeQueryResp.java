package cloud.demand.app.modules.return_report.model;

import cloud.demand.app.modules.return_report.service.model.QueryCvmMergeVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CvmReturnMergeQueryResp {

    /**结果数据*/
    private List<Item> data;

    /**合计*/
    private Item total;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Item {

        // 维度
        private String demandCategory1;
        private String demandCategory2;
        private String planProductName;
        private String areaType;
        private String pool;
        private String instanceModel;
        private String generationType;
        private String yearMonth;

        // 值
        private BigDecimal totalReturnCores;
        private BigDecimal returnedCores;
        private BigDecimal planToReturnCores;
        private BigDecimal applyCores;
        private BigDecimal netReturnCores;
    }

    public static CvmReturnMergeQueryResp build(List<QueryCvmMergeVO> result, List<String> groupBy) {
        CvmReturnMergeQueryResp resp = new CvmReturnMergeQueryResp();

        resp.setData(ListUtils.transform(result, o -> {
            Item item = new Item();
            if (groupBy.contains("demandCategory1")) {
                item.setDemandCategory1(o.getDemandCategory1());
            }
            if (groupBy.contains("demandCategory2")) {
                item.setDemandCategory2(o.getDemandCategory2());
            }
            if (groupBy.contains("planProductName")) {
                item.setPlanProductName(o.getPlanProductName());
            }
            if (groupBy.contains("areaType")) {
                item.setAreaType(o.getAreaType());
            }
            if (groupBy.contains("pool")) {
                item.setPool(o.getPool());
            }
            if (groupBy.contains("instanceModel")) {
                item.setInstanceModel(o.getInstanceModel());
            }
            if (groupBy.contains("generationType")) {
                item.setGenerationType(o.getGenerationType());
            }
            if (groupBy.contains("yearMonth")) {
                item.setYearMonth(o.getYearMonth());
            }

            item.setReturnedCores(o.getReturnedCores());
            item.setPlanToReturnCores(o.getPlanToReturnCores());
            item.setTotalReturnCores(o.getReturnedCores().add(o.getPlanToReturnCores()));
            item.setNetReturnCores(o.getReturnedCores().subtract(o.getApplyCores()));
            item.setApplyCores(o.getApplyCores());

            return item;
        }));


        // 合计值
        resp.total = new Item();
        resp.total.setReturnedCores(NumberUtils.sum(resp.getData(), o -> o.getReturnedCores()));
        resp.total.setPlanToReturnCores(NumberUtils.sum(resp.getData(), o -> o.getPlanToReturnCores()));
        resp.total.setTotalReturnCores(resp.total.getReturnedCores().add(resp.total.getPlanToReturnCores()));
        resp.total.setNetReturnCores(NumberUtils.sum(resp.getData(), o -> o.getNetReturnCores()));
        resp.total.setApplyCores(NumberUtils.sum(resp.getData(), o -> o.getApplyCores()));

        return resp;
    }

}
