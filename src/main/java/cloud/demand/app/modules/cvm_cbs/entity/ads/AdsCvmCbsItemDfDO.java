package cloud.demand.app.modules.cvm_cbs.entity.ads;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

@Data
@ToString
@Table("ads_cvm_cbs_item_df")
public class AdsCvmCbsItemDfDO {

    /**
     * 分区键，代表数据版本<br/>Column: [version]
     */
    @Column(value = "version")
    private String version;

    /**
     * 指标<br/>Column: [index]
     */
    @Column(value = "index")
    private String index;

    /**
     * 需求年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * 需求月份<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;


    /**
     * 境内/境外<br/>Column: [customhouse_title]
     */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /**
     * 地域<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 可用区<br/>Column: [zone_name]
     */
    @Column(value = "zone_name")
    private String zoneName;

    /**
     * 自定义BG名<br/>Column: [custom_bg_name]
     */
    @Column(value = "custom_bg_name")
    private String customBgName;

    /**
     * BG名<br/>Column: [bg_name]
     */
    @Column(value = "bg_name")
    private String bgName;

    /**
     * 部门名<br/>Column: [dept_name]
     */
    @Column(value = "dept_name")
    private String deptName;

    /**
     * 规划产品名<br/>Column: [plan_product_name]
     */
    @Column(value = "plan_product_name")
    private String planProductName;

    /**
     * 项目名称<br/>Column: [project_name]
     */
    @Column(value = "project_name")
    private String projectName;

    /**
     * CVM机型族<br/>Column: [cvm_instance_group]
     */
    @Column(value = "cvm_instance_group")
    private String cvmInstanceGroup;

    /**
     * CVM类型<br/>Column: [cvm_instance_type]
     */
    @Column(value = "cvm_instance_type")
    private String cvmInstanceType;

    /**
     * 母机机型<br/>Column: [host_type]
     */
    @Column(value = "host_type")
    private String hostType;

    /**
     * 核心数(单位：cpu，万核)<br/>Column: [amount]
     */
    @Column(value = "amount")
    private BigDecimal amount;

    /**
     * 云盘类型<br/>Column: [volume_type]
     */
    @Column(value = "volume_type")
    private String volumeType;

    /**
     * 实例io<br/>Column: [instance_io]
     */
    @Column(value = "instance_io")
    private Long instanceIo;

    @Column(value = "instance_num")
    private BigDecimal instanceNum;

}