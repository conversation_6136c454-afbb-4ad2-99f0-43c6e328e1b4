package cloud.demand.app.modules.cvm_cbs.web;

import cloud.demand.app.modules.cvm_cbs.domain.req.CvmCbs532ForecastReq;
import cloud.demand.app.modules.cvm_cbs.domain.req.CvmCbsParamTypeReq;
import cloud.demand.app.modules.cvm_cbs.domain.req.QueryCompareCvmCbsReq;
import cloud.demand.app.modules.cvm_cbs.domain.req.QueryReportCvmCbsReq;
import cloud.demand.app.modules.cvm_cbs.domain.resp.CvmCbsReportResp;
import cloud.demand.app.modules.cvm_cbs.domain.resp.CvmCbsWarningDataResp;
import cloud.demand.app.modules.cvm_cbs.entity.vo.CvmCbs532ForecastVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.CvmCbsRegionInfoVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.HighInstanceIoItemVO;
import cloud.demand.app.modules.cvm_cbs.entity.vo.VersionInfo;
import cloud.demand.app.modules.cvm_cbs.enums.CvmCbsIndex;
import cloud.demand.app.modules.cvm_cbs.service.CvmCbsReportService;
import cloud.demand.app.modules.industry_cockpit.DynamicProperties;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import yunti.boot.exception.ITException;
import yunti.boot.web.jsonrpc.JsonrpcController;
import yunti.boot.web.jsonrpc.JsonrpcParam;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonrpcController("/cvm-cbs/report")
public class CvmCbsReportController {

    @Resource
    @Qualifier("cvmCbsReportService2Impl")
    private CvmCbsReportService cvmCbsReportService2;

    @Resource
    @Qualifier("cvmCbsReportServiceImpl")
    private CvmCbsReportService cvmCbsReportService;

    @RequestMapping
    public List<String> listVersion() {
        return cvmCbsReportService.listVersion();
    }

    @RequestMapping
    public List<VersionInfo> listVersionInfo() {
        return cvmCbsReportService.listVersionInfo();
    }

    @RequestMapping
    public List<String> queryParams(@JsonrpcParam @Valid CvmCbsParamTypeReq req) {
        return cvmCbsReportService.queryParams(req);
    }

    @RequestMapping
    public List<CvmCbsRegionInfoVO> queryRegionInfo(@JsonrpcParam @Valid CvmCbsParamTypeReq req) {
        return cvmCbsReportService.queryRegionInfo(req);
    }

    @RequestMapping
    public CvmCbsReportResp queryReport(@JsonrpcParam @Valid QueryReportCvmCbsReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        filterIndex(req);
        if(DynamicProperties.allowABTest()){
            return cvmCbsReportService2.queryReport(req);
        }
        return cvmCbsReportService.queryReport(req);
    }

    @RequestMapping
    public CvmCbsWarningDataResp queryWarningData(@JsonrpcParam @Valid QueryCompareCvmCbsReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        filterIndex(req);
        if(DynamicProperties.allowABTest()){
            cvmCbsReportService2.queryWarningData(req);
        }
        return cvmCbsReportService.queryWarningData(req);
    }

    @RequestMapping
    public List<HighInstanceIoItemVO> queryHighIoDetailList(@JsonrpcParam @Valid QueryReportCvmCbsReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        filterIndex(req);
        if(DynamicProperties.allowABTest()){
            return cvmCbsReportService2.queryHighIoDetailList(req);
        }
        return cvmCbsReportService.queryHighIoDetailList(req);
    }

    @RequestMapping
    public List<HighInstanceIoItemVO> queryHighIoList(@JsonrpcParam @Valid QueryReportCvmCbsReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        filterIndex(req);
        if(DynamicProperties.allowABTest()){
            return cvmCbsReportService2.queryHighIoList(req);
        }
        return cvmCbsReportService.queryHighIoList(req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> exportReportDetail(@JsonrpcParam @Valid QueryReportCvmCbsReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        filterIndex(req);
        if(DynamicProperties.allowABTest()){
            cvmCbsReportService2.exportExcel(req);
        }
        return cvmCbsReportService.exportExcel(req);
    }

    @RequestMapping
    public List<CvmCbs532ForecastVO> query532ForecastRate(@JsonrpcParam @Valid CvmCbs532ForecastReq req){
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        return cvmCbsReportService.query532ForecastRate(req);
    }

    @RequestMapping
    public ResponseEntity<InputStreamResource> export532ForecastRate(@JsonrpcParam @Valid CvmCbs532ForecastReq req) {
        if (StringUtils.isBlank(req.getStartMonth()) || StringUtils.isBlank(req.getEndMonth())) {
            throw new ITException("起始月份或结束月份不能为空");
        }
        return cvmCbsReportService.export532ForecastRate(req);
    }

    public void filterIndex(QueryReportCvmCbsReq req) {
        if (req.hasVolumeType()) {
            List<String> noVolumeTypeIndexList = Arrays.stream(CvmCbsIndex.values()).filter(item -> !item.isHasVolumeType())
                    .map(item -> item.getCode()).collect(Collectors.toList());
            req.getIndexeList().removeAll(noVolumeTypeIndexList);
        }
        if (req.hasBg()) {
            List<String> noBgIndexList = Arrays.stream(CvmCbsIndex.values()).filter(item -> !item.isHasBg())
                    .map(item -> item.getCode()).collect(Collectors.toList());
            req.getIndexeList().removeAll(noBgIndexList);
        }
    }

}
