package cloud.demand.app.modules.cvm_cbs.domain.req;

import cloud.demand.app.modules.cvm_cbs.entity.vo.IIndexContextGroupKey;
import cloud.demand.app.modules.cvm_cbs.enums.CvmCbsIndex;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/1/15 15:46
 */
@Data
@AllArgsConstructor
public class IndexReq implements IIndexContextGroupKey {

    private String version;
    //指标
    private CvmCbsIndex cvmCbsIndex;

    private boolean sourceIndexIsRateIndex;

    private QueryReportCvmCbsReq req;

    private boolean findCache = true;

    public IndexReq(String version,CvmCbsIndex cvmCbsIndex,boolean sourceIndexIsRateIndex,QueryReportCvmCbsReq req){
        this.version = version;
        this.cvmCbsIndex = cvmCbsIndex;
        this.sourceIndexIsRateIndex = sourceIndexIsRateIndex;
        this.req = req;
    }

    public String getIndex(){
        if(Objects.isNull(cvmCbsIndex)){
            return "";
        }
        return cvmCbsIndex.getCode();
    }
}
