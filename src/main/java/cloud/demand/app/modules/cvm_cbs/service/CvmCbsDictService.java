package cloud.demand.app.modules.cvm_cbs.service;

import cloud.demand.app.modules.cvm_cbs.domain.req.BatchUpdateCvmCbsRateReq;
import cloud.demand.app.modules.cvm_cbs.domain.req.UpdateCvmCbsRateReq;
import cloud.demand.app.modules.cvm_cbs.domain.resp.CvmCbsRateResp;
import cloud.demand.app.modules.cvm_cbs.entity.ZiYanCbsSafeTotalDO;
import cloud.demand.app.modules.cvm_cbs.enums.CvmCbsIndex;
import cloud.demand.app.modules.cvm_cbs.enums.VolumeTypeEnum;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public interface CvmCbsDictService {

    /**
     * 获取所有的版本列表
     */
    List<String> getAllVersionList();

    /**
     * 获取CVM-CBS的配比
     */
    List<CvmCbsRateResp> getCvmCbsRate(String versionCode);

    /**
     * 修改配比
     */
    void updateCvmCbsRate(UpdateCvmCbsRateReq req);

    /**
     * 重置配比
     */
    void resetCvmCbsRate(String versionCode);

    /** 计算配比 */
    void calcCvmCbsRate(String versionCode);

    List<CvmCbsIndex> getCvmCbsIndexList();

    void batchUpdateCvmCbsRate(BatchUpdateCvmCbsRateReq req);

    List<String> getCvmInstanceTypeByInstanceFamily(List<String> instanceFamilyList);

    Map<String,String> getDeviceFamilyCvmInstanceMapping();

    ZiYanCbsSafeTotalDO getZiYanCbsSafeStock(LocalDate startDate, LocalDate endDate, VolumeTypeEnum volumeTypeEnum);
}
