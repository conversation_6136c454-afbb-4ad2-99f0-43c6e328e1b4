package cloud.demand.app.modules.cvm_cbs.service.impl;

import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.entity.BasObsCloudCvmTypeDO;
import cloud.demand.app.modules.cvm_cbs.entity.dws.DwsCvmCbsItemDfDO;
import cloud.demand.app.modules.cvm_cbs.service.CvmCbsCleanService;
import cloud.demand.app.modules.cvm_cbs.service.CvmCbsDictService;
import cloud.demand.app.modules.soe.model.clean.IRegionClean;
import cloud.demand.app.modules.soe.service.SoeCleanService;
import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.service.CommonDbHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CvmCbsCleanServiceImpl implements CvmCbsCleanService {

    @Resource
    private SoeCleanService cleanService;

    @Resource
    private DictService dictService;

    /**
     * sop 公共方法
     */
    @Resource
    private CommonDbHelper commonDbHelper;

    @Resource
    private CvmCbsDictService cvmCbsDictService;


    @Override
    public void clean(List<? extends IRegionClean> iRegionCleans) {
        if (ListUtils.isEmpty(iRegionCleans)) {
            return;
        }
        Map<String, BasObsCloudCvmTypeDO> cvmTypeMap = dictService.queryInstanceCodeInfos();

        Map<String, String> hostTypeToCvmInstanceMap = getHostTypeToCvmInstanceMap();

        List<CommonDbHelper.CustomBgByProduct> customBgByProducts = commonDbHelper.gePlanProductInfo();
        Map<String, CommonDbHelper.CustomBgByProduct> productMap = ListUtils.toMap(customBgByProducts, CommonDbHelper.CustomBgByProduct::getPlanProductName, item -> item);

        for (IRegionClean iRegionClean : iRegionCleans) {
            // 清洗region
            cleanService.cleanRegion(iRegionClean);
            DwsCvmCbsItemDfDO itemDfDO = (DwsCvmCbsItemDfDO) iRegionClean;
            //清洗CVM
            if (StringUtils.isBlank(itemDfDO.getCvmInstanceTypeEng())
                    || StringUtils.equals(itemDfDO.getCvmInstanceTypeEng(), Constant.EMPTY_VALUE_STR)) {
                cleanCvmWithDeviceHostType(itemDfDO, hostTypeToCvmInstanceMap);
            } else {
                cleanCvm(itemDfDO, cvmTypeMap);
            }
            //清洗自定义事业部
            cleanCustomBgName(itemDfDO, productMap);
        }

    }

    private void cleanCvm(DwsCvmCbsItemDfDO itemDfDO, Map<String, BasObsCloudCvmTypeDO> cvmTypeMap) {
        BasObsCloudCvmTypeDO cvmType = cvmTypeMap.get(itemDfDO.getCvmInstanceTypeEng());
        if (Objects.isNull(cvmType)) {
            return;
        }
        itemDfDO.setCvmInstanceType(cvmType.getCvmInstanceType());
        itemDfDO.setCvmInstanceGroup(cvmType.getCvmInstanceGroup());
    }

    private void cleanCvmWithDeviceHostType(DwsCvmCbsItemDfDO itemDfDO, Map<String, String> hostTypeToCvmInstanceMap) {
        String cvmInstanceGroup = hostTypeToCvmInstanceMap.get(itemDfDO.getHostType());
        if (StringUtils.isNotBlank(cvmInstanceGroup)) {
            itemDfDO.setCvmInstanceGroup(cvmInstanceGroup);
        }
    }

    private void cleanCustomBgName(DwsCvmCbsItemDfDO itemDfDO, Map<String, CommonDbHelper.CustomBgByProduct> productMap) {
        CommonDbHelper.CustomBgByProduct customBgByProduct = productMap.get(itemDfDO.getPlanProductName());
        if (Objects.isNull(customBgByProduct)) {
            return;
        }
        itemDfDO.setCustomBgName(customBgByProduct.getCustomBg());
    }

    /**
     * 母机机型到CVM机型族的映射
     *
     * @return Map<String, String> key：母机机型  value：cvm机型族
     */
    private Map<String, String> getHostTypeToCvmInstanceMap() {
        Map<String, String> ret = new HashMap<>();
        // 获取设备类型对应的机型族
        Map<String, String> deviceFamilyMap = commonDbHelper.getDeviceFamilyMap();

        Map<String, String> deviceFamilyCvmInstanceMappingMap = cvmCbsDictService.getDeviceFamilyCvmInstanceMapping();

        for (Map.Entry<String, String> entry : deviceFamilyMap.entrySet()) {
            ret.put(entry.getKey(), deviceFamilyCvmInstanceMappingMap.getOrDefault(entry.getValue(), "其他"));
        }
        return ret;
    }
}
