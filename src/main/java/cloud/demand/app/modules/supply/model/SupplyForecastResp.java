package cloud.demand.app.modules.supply.model;

import java.util.List;
import lombok.Data;

@Data
public class SupplyForecastResp {

    List data;
    long total;
    ForecastDemandSupplyResultSum summary;

    public void setSummary(ForecastDemandSupplyResultSum sum) {
        summary = new ForecastDemandSupplyResultSum();
        summary.setSumTotal(sum.getSumTotal());
        summary.setSumReUsed(sum.getSumReUsed());
        summary.setSumInv(sum.getSumInv());
        summary.setSumPur(sum.getSumPur());
        summary.setSumPurGap(sum.getSumPurGap());
        summary.setSumPurDelay(sum.getSumPurDelay());
        summary.setSumChainInv(sum.getSumChainInv());
        summary.setSumGap(sum.getSumGap());

        summary.setReUsed(sum.getReUsed());

        summary.setInvExMatched(sum.getInvExMatched());
        summary.setInvNotEx(sum.getInvNotEx());
        summary.setInvExNotMatch(sum.getInvExNotMatch());

        summary.setChainInvExNotMatch(sum.getChainInvExNotMatch());
        summary.setChainInvNotEx(sum.getChainInvNotEx());

        summary.setPurExMatched(sum.getPurExMatched());
        summary.setPurNotEx(sum.getPurNotEx());
        summary.setPurExNotMatch(sum.getPurExNotMatch());
    }
}
