package cloud.demand.app.modules.supply.model;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.Data;
import lombok.ToString;

/**
 * 后端供应方案(公司库存对冲)-汇总数据
 */
@Data
@ToString
@Table("ads_forecast_demand_supply_summary")
public class AdsForecastDemandSupplySummaryDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /**
     * 版本：如 2022-12-07<br/>Column: [version]
     */
    @Column(value = "version")
    private String version;

    /**
     * 年月<br/>Column: [dim_year_month]
     */
    @Column(value = "dim_year_month")
    private String yearMonth;

    /**
     * 对冲区域<br/>Column: [dim_area]
     */
    @Column(value = "dim_area")
    private String hedgingArea;

    /**
     * 地域：如华东，华北<br/>Column: [dim_region]
     */
    @Column(value = "dim_region")
    private String region;

    /**
     * 地区 如 上海 <br/>Column: [dim_zone]
     */
    @Column(value = "dim_zone")
    private String zone;

    /**
     * campus<br/>Column: [dim_campus]
     */
    @Column(value = "dim_campus")
    private String campus;

    /**
     * 设备类型：如 T0-CM62X-100G<br/>Column: [dim_device_type]
     */
    @Column(value = "dim_device_type")
    private String deviceType;

    /**
     * 产品 如 腾讯云CBS<br/>Column: [dim_plan_product]
     */
    @Column(value = "dim_plan_product")
    private String planProduct;

    /**
     * 业务类型<br/>Column: [dim_business_type]
     */
    @Column(value = "dim_business_type")
    private String dimBusinessType;

    /**
     * 行业 如：战略客户<br/>Column: [dim_industry]
     */
    @Column(value = "dim_industry")
    private String industry;

    /**
     * 客户 如：小红书<br/>Column: [dim_customer_name]
     */
    @Column(value = "dim_customer_name")
    private String customerName;

    /**
     * 原因类型：如 系统任务<br/>Column: [dim_reason_type]
     */
    @Column(value = "dim_reason_type")
    private String reasonType;

    /**
     * 原因：如 补全已执行预测<br/>Column: [dim_reason]
     */
    @Column(value = "dim_reason")
    private String reason;

    /**
     * 需求类型<br/>Column: [dim_demand_type]
     */
    @Column(value = "dim_demand_type")
    private String demandType;

    /**
     * 单位机型核心<br/>Column: [assist_device_core]
     */
    @Column(value = "assist_device_core")
    private Integer assistDeviceCore;

    /**
     * 原始需求数<br/>Column: [demand_amount]
     */
    @Column(value = "demand_amount")
    private BigDecimal demandAmount;

    /**
     * 已执行-星云复用<br/>Column: [ex_r_amount]
     */
    @Column(value = "ex_r_amount")
    private BigDecimal exRAmount;

    /**
     * 未执行-总量<br/>Column: [no_ex_amount]
     */
    @Column(value = "no_ex_amount")
    private BigDecimal noExAmount;

    /**
     * 未执行-采购满足<br/>Column: [no_ex_supply_pur]
     */
    @Column(value = "no_ex_supply_pur")
    private BigDecimal noExSupplyPur;

    /**
     * 未执行-采购-供应链库存<br/>Column: [no_ex_supply_pur_Chain]
     */
    @Column(value = "no_ex_supply_pur_Chain")
    private BigDecimal noExSupplyPurChain;

    /**
     * 未执行-库存<br/>Column: [no_ex_supply_inv]
     */
    @Column(value = "no_ex_supply_inv")
    private BigDecimal noExSupplyInv;

    /**
     * 已执行-总量<br/>Column: [ex_amount]
     */
    @Column(value = "ex_amount")
    private BigDecimal exAmount;

    /**
     * 已执行-已匹配-运管库存<br/>Column: [ex_match_o_amount]
     */
    @Column(value = "ex_match_o_amount")
    private BigDecimal exMatchOAmount;

    /**
     * 已执行-已匹配-采购<br/>Column: [ex_match_b_amount]
     */
    @Column(value = "ex_match_b_amount")
    private BigDecimal exMatchBAmount;

    /**
     * 已执行-未匹配-数量<br/>Column: [ex_no_match_amount]
     */
    @Column(value = "ex_no_match_amount")
    private BigDecimal exNoMatchAmount;

    /**
     * 已执行-未匹配-采购<br/>Column: [ex_no_match_supply_pur]
     */
    @Column(value = "ex_no_match_supply_pur")
    private BigDecimal exNoMatchSupplyPur;

    /**
     * 已执行-未匹配-采购-供应链库存<br/>Column: [ex_no_match_supply_pur_chain]
     */
    @Column(value = "ex_no_match_supply_pur_chain")
    private BigDecimal exNoMatchSupplyPurChain;

    /**
     * 已执行-未匹配-运管库存<br/>Column: [ex_no_match_supply_inv]
     */
    @Column(value = "ex_no_match_supply_inv")
    private BigDecimal exNoMatchSupplyInv;

    /**
     * 未参与对冲需求<br/>Column: [gap_amount]
     */
    @Column(value = "gap_amount")
    private BigDecimal gapAmount;

    /**
     * 采购供应<br/>Column: [supply_amount]
     */
    @Column(value = "supply_amount")
    private BigDecimal supplyAmount;

    /**
     * 供应缺口<br/>Column: [supply_gap]
     */
    @Column(value = "supply_gap")
    private BigDecimal supplyGap;

    /**
     * 供应延迟<br/>Column: [supply_dela]
     */
    @Column(value = "supply_dela")
    private BigDecimal supplyDela;

    /**
     * 供应明细<br/>Column: [supply_satis]
     */
    @Column(value = "supply_satis")
    private String supplySatis;

    @Column(value = "create_time")
    private Timestamp createTime;


    Integer hasRisk;

//    public Integer getHasRisk() {
//        return (supplyGap.intValue() + supplyDela.intValue()) > 0 ? 1 : 0;
//    }

    public Integer getHasRisk() {
        if (supplyDela != null && supplyDela != null) {
            return (supplyGap.intValue() + supplyDela.intValue()) > 0 ? 1 : 0;
        }
        return 0;
    }

}