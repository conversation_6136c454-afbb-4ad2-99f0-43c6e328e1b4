package cloud.demand.app.modules.res_plan_base_tag_report.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;

@Data
@ToString
@Table("res_plan_base_tag_report_version")
public class ResPlanBaseTagReportVersionDO {

    @Column(value = "version", isKey = true)
    private String version;

    /** 版本类别, 0 - 云报表, 1 - 公司报表<br/>Column: [version_type] */
    @Column(value = "version_type")
    private Integer versionType;

    /** 删除标记<br/>Column: [deleted] */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /** 任务结果状态<br/>Column: [deleted] */
    @Column(value = "status")
    private String status;

    /** 错误信息<br/>Column: [deleted] */
    @Column(value = "error_msg")
    private String errorMsg;

    /** 数据量<br/>Column: [data_item_num] */
    @Column(value = "data_item_num")
    private Long dataItemNum;

    /** 创建时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Timestamp createTime;

    /** 更新时间<br/>Column: [update_time] */
    @Column(value = "update_time")
    private Timestamp updateTime;

}
