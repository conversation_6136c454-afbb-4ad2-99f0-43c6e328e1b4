package cloud.demand.app.modules.cvmjxc.service.cdb;

import cloud.demand.app.common.utils.AmountUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.rrp.ReportCvmJxcDO;
import cloud.demand.app.entity.rrp.ReportPlanDetailDO;
import cloud.demand.app.modules.common.enums.IndicatorCDBEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.TaskLogService;
import cloud.demand.app.modules.plan_detail.service.cdb.PlanDetailCDBIndicatorEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

/**
 * 计算CDB概览指标
 */
@Service
@Slf4j
public class CalculateCDBIndicatorService {
    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private TaskLogService taskLogService;

    @Resource
    private DBHelper newckdemandDBHelper;

    public void calAndSave(String statTime) {
        String sql = "select indicator_code,SUM(logic_num) as logic_num from report_plan_detail" +
                " where stat_time=? AND product_type='CDB' GROUP BY indicator_code";
        List<ReportPlanDetailDO> list = rrpDBHelper.getRaw(ReportPlanDetailDO.class, sql, statTime);
        if (ListUtils.isEmpty(list)){
            String msg = "cdb plan detail data not exists: "+ statTime;
            taskLogService.genRunLog("genJxcData", "calAndSave", msg);
            return;
        }

        //  plan_detail表中获取
        ReportPlanDetailDO b1 = getByEnum(list, PlanDetailCDBIndicatorEnum.INVENTORY_ONLINE_GOOD);
        ReportPlanDetailDO b3 = getByEnum(list, PlanDetailCDBIndicatorEnum.INVENTORY_ONLINE_UGLY);
        ReportPlanDetailDO c = getByEnum(list, PlanDetailCDBIndicatorEnum.INVENTORY_OFFLINE);
        ReportPlanDetailDO a1 = getByEnum(list, PlanDetailCDBIndicatorEnum.SALE_OUT);
        ReportPlanDetailDO a2 = getByEnum(list, PlanDetailCDBIndicatorEnum.SALE_IN);
        ReportPlanDetailDO f1 = getByEnum(list, PlanDetailCDBIndicatorEnum.PDD_REDUNDANCY);
        ReportPlanDetailDO f2 = getByEnum(list, PlanDetailCDBIndicatorEnum.EX_REDUNDANCY);
        ReportPlanDetailDO f3 = getByEnum(list, PlanDetailCDBIndicatorEnum.MACHINE_REDUNDANCY);

        //  从ClickHouse中获取支撑、备机、测试、存储四个指标数据
        List<CkCdbDTO> ckDataList = genCkOtherFourData(statTime);
        CkCdbDTO l1 = getCkDataByEnum(ckDataList, IndicatorCDBEnum.OTHER_STANDBY);
        CkCdbDTO l2 = getCkDataByEnum(ckDataList, IndicatorCDBEnum.OTHER_SUPPORT);
        CkCdbDTO l3 = getCkDataByEnum(ckDataList, IndicatorCDBEnum.OTHER_TEST);
        CkCdbDTO kc = getCkDataByEnum(ckDataList, IndicatorCDBEnum.OTHER_STORAGE);

        List<ReportCvmJxcDO> result = new ArrayList<>();
        ArrayList<IndicatorCDBEnum> indicators = Lang.list(
                IndicatorCDBEnum.INVENTORY_ONLINE_GOOD, IndicatorCDBEnum.INVENTORY_ONLINE_UGLY,
                IndicatorCDBEnum.INVENTORY_TOTAL, IndicatorCDBEnum.INVENTORY_OFFLINE,
                IndicatorCDBEnum.SALE_EXTERNAL, IndicatorCDBEnum.SALE_INTERNAL,
                IndicatorCDBEnum.INVENTORY_ALL, IndicatorCDBEnum.SALE_ALL,
                IndicatorCDBEnum.OTHER_PDD_REDUNDANCY,IndicatorCDBEnum.OTHER_EXC_REDUNDANCY,
                IndicatorCDBEnum.OTHER_MACHINE_REDUNDANCY, IndicatorCDBEnum.OTHER_STANDBY,
                IndicatorCDBEnum.OTHER_SUPPORT, IndicatorCDBEnum.OTHER_TEST,
                IndicatorCDBEnum.OTHER_STORAGE,IndicatorCDBEnum.OTHER_OTHER,
                IndicatorCDBEnum.OTHER_TOTAL_CAP,IndicatorCDBEnum.OTHER_NOT_FOR_SALE,
                IndicatorCDBEnum.OTHER_P2P_RATE, IndicatorCDBEnum.OTHER_P2P_RATE_SA);

        BigDecimal K = a1.getLogicNum().add(a2.getLogicNum());
        BigDecimal Q = b1.getLogicNum().add(b3.getLogicNum()).add(c.getLogicNum());

        for (IndicatorCDBEnum each : indicators) {
            ReportCvmJxcDO jxcDO = each.toReportCvmJxcDO();
            result.add(jxcDO);
            switch (each){
                case INVENTORY_ONLINE_GOOD:
                    jxcDO.setLogicNum(b1.getLogicNum());
                    break;
                case INVENTORY_ONLINE_UGLY:
                    jxcDO.setLogicNum(b3.getLogicNum());
                    break;
                case INVENTORY_TOTAL:
                    jxcDO.setLogicNum(b1.getLogicNum().add(b3.getLogicNum()));
                    break;
                case INVENTORY_OFFLINE:
                    jxcDO.setLogicNum(c.getLogicNum());
                    break;
                case INVENTORY_ALL:
                    jxcDO.setLogicNum(b1.getLogicNum().add(b3.getLogicNum()).add(c.getLogicNum()));
                    break;
                case SALE_EXTERNAL:
                    jxcDO.setLogicNum(a1.getLogicNum());
                    break;
                case SALE_INTERNAL:
                    jxcDO.setLogicNum(a2.getLogicNum());
                    break;
                case SALE_ALL:
                    jxcDO.setLogicNum(a1.getLogicNum().add(a2.getLogicNum()));
                    break;
                case OTHER_PDD_REDUNDANCY:
                    jxcDO.setLogicNum(f1.getLogicNum());
                    break;
                case OTHER_EXC_REDUNDANCY:
                    jxcDO.setLogicNum(f2.getLogicNum());
                    break;
                case OTHER_MACHINE_REDUNDANCY:
                    jxcDO.setLogicNum(f3.getLogicNum());
                    break;
                case OTHER_STANDBY:
                    jxcDO.setLogicNum(l1.getLogicNum());
                    jxcDO.setCoreNum(l1.getCoreNum());
                    break;
                case OTHER_SUPPORT:
                    jxcDO.setLogicNum(l2.getLogicNum());
                    jxcDO.setCoreNum(l2.getCoreNum());
                    break;
                case OTHER_TEST:
                    jxcDO.setLogicNum(l3.getLogicNum());
                    jxcDO.setCoreNum(l3.getCoreNum());
                    break;
                case OTHER_STORAGE:
                    jxcDO.setLogicNum(kc.getLogicNum());
                    jxcDO.setCoreNum(kc.getCoreNum());
                    break;
                case OTHER_OTHER:
                    //  L = l1 + l2 + l3
                    jxcDO.setLogicNum(l1.getLogicNum().add(l2.getLogicNum()).add(l3.getLogicNum()));
                    jxcDO.setCoreNum(l1.getCoreNum().add(l2.getCoreNum()).add(l3.getCoreNum()));
                    break;
                case OTHER_TOTAL_CAP:
                    //  tm = kc + L
                    jxcDO.setLogicNum(l1.getLogicNum().add(l2.getLogicNum()).add(l3.getLogicNum()).add(kc.getLogicNum()));
                    jxcDO.setCoreNum(l1.getCoreNum().add(l2.getCoreNum()).add(l3.getCoreNum()).add(kc.getCoreNum()));
                    break;
                case OTHER_NOT_FOR_SALE:
                    //  NS = tm / 2 - K - Q
                    BigDecimal tm = l1.getLogicNum().add(l2.getLogicNum()).add(l3.getLogicNum()).add(kc.getLogicNum());
                    jxcDO.setLogicNum(tm.divide(BigDecimal.valueOf(2l)).subtract(K).subtract(Q));
                    break;
                case OTHER_P2P_RATE:
                    //   P2PRATE = K/K+Q
                    jxcDO.setLogicNum(AmountUtils.divideScale6(K, K.add(Q)));
                    break;
                case OTHER_P2P_RATE_SA:
                    //  P2PRATE_SA = K/K+Q+f1+f2+f3
                    BigDecimal redundancy = f1.getLogicNum().add(f2.getLogicNum()).add(f3.getLogicNum());
                    jxcDO.setLogicNum(AmountUtils.divideScale6(K, K.add(Q).add(redundancy)));
                    break;
            }
            jxcDO.setStatTime(DateUtils.parse(statTime));
        }
        rrpDBHelper.delete(ReportCvmJxcDO.class,
                "where stat_time=? and product_type=? and indicator_code in (?) ",
                statTime, ProductTypeEnum.CDB.getCode(), ListUtils.transform(indicators, IndicatorCDBEnum::getCode));
        rrpDBHelper.insertBatchWithoutReturnId(result);
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CkCdbDTO{
        @Column("cdb_store_type")
        private String indicatorName;

        @Column(value = "logic_num")
        private BigDecimal logicNum;

        @Column(value = "core_num")
        private BigDecimal coreNum;
    }

    /**
     * 其他-存储、测试、支撑、备机的数据源为ClickHouse，直接计算出概览数据
     */
    private List<CkCdbDTO> genCkOtherFourData(String statTime){
        String sql = ORMUtils.getSql("/sql/cdb/ck_other_four_indicators.sql");
        List<CkCdbDTO> results = newckdemandDBHelper.getRaw(CkCdbDTO.class, sql, MapUtils.of("statTime", statTime));
        if (ListUtils.isEmpty(results) || results.size() < 4){
            String msg = "cdb ck data is not complete, statTime = " + statTime;
            taskLogService.genRunLog("genJxcData", "genCkOtherFourData", msg);
        }
        return results;
    }

    /**
     * 从ClickHouse中获取到的数据在这里根据不同指标类型进行过滤
     */
    private CkCdbDTO getCkDataByEnum(List<CkCdbDTO> list, IndicatorCDBEnum e){
        List<CkCdbDTO> filter = ListUtils.filter(list, o -> Objects.equals(e.getName(), o.getIndicatorName()));
        if (filter.isEmpty()){
            CkCdbDTO ckCdbDTO = new CkCdbDTO(e.getName(), BigDecimal.ZERO, BigDecimal.ZERO);
            return ckCdbDTO;
        }
        return filter.get(0);
    }

    /**
     * 除线下库存外其他的detail获取
     */
    private ReportPlanDetailDO getByEnum(List<ReportPlanDetailDO> list, PlanDetailCDBIndicatorEnum e) {
        List<ReportPlanDetailDO> filter = ListUtils.filter(list, o -> o.getIndicatorCode().equals(e.getCode()));
        if (filter.isEmpty()) {
            ReportPlanDetailDO reportPlanDetailDO = new ReportPlanDetailDO();
            reportPlanDetailDO.setLogicNum(BigDecimal.ZERO);
            return reportPlanDetailDO;
        } else {
            return filter.get(0);
        }
    }
}
