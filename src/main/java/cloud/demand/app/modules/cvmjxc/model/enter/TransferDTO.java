package cloud.demand.app.modules.cvmjxc.model.enter;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 进-资源转入与转出复用的DTO
 */
@Data
public class TransferDTO {

    private String statTime;

    @Column("num")
    private BigDecimal num;

    @Column("core_num")
    private BigDecimal coreNum;

    @Column("logic_num")
    private BigDecimal logicNum;

}
