package cloud.demand.app.modules.cvmjxc.service;

import cloud.demand.app.modules.cvmjxc.model.external.BufferAverageCoreDTO;

import java.util.Date;
import java.util.List;

/**
 * 进销存提供给外部的服务
 */
public interface JxcExternalService {

    /**
     * 查询指定日期最近一个月的弹性用量的平均值，以机型族+可用区为聚合条件。
     *
     * 注意：不一定会查回数据，如果没有数据，返回空列表。
     * @param date
     * @return
     */
    List<BufferAverageCoreDTO> queryBufferCoreAverage(Date date);

}
