package cloud.demand.app.modules.cvmjxc.web.model;

import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class QueryP2pSummaryResp {

    Row cvm = new Row(ProductTypeEnum.CVM.getCode());
    Row gpu = new Row(ProductTypeEnum.GPU.getCode());
    Row cdb = new Row(ProductTypeEnum.CDB.getCode());
    Row cbs = new Row(ProductTypeEnum.CBS.getCode());
    Row cos = new Row(ProductTypeEnum.COS.getCode());
    Row metal = new Row(ProductTypeEnum.METAL.getCode());

    public void setPreOneMonthScale(QueryAllProductSummaryResp.Item item) {
        cvm.setPreOneMonthScale(item.getCvmCoresIn10k());
        gpu.setPreOneMonthScale(item.getGpuCard());
        metal.setPreOneMonthScale(item.getMetalCoresIn10k());
        cbs.setPreOneMonthScale(item.getCbsStoragePb());
        cdb.setPreOneMonthScale(item.getCdbMemoryTb());
        cos.setPreOneMonthScale(item.getCosStoragePb());
    }

    public void setPreOneMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setPreOneMonthTarget(item.getCvmCoresIn10k());
        gpu.setPreOneMonthTarget(item.getGpuCard());
        metal.setPreOneMonthTarget(item.getMetalCoresIn10k());
        cbs.setPreOneMonthTarget(item.getCbsStoragePb());
        cdb.setPreOneMonthTarget(item.getCdbMemoryTb());
        cos.setPreOneMonthTarget(item.getCosStoragePb());
    }

    public void setPreThreeMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setPreThreeMonthTarget(item.getCvmCoresIn10k());
        gpu.setPreThreeMonthTarget(item.getGpuCard());
        metal.setPreThreeMonthTarget(item.getMetalCoresIn10k());
        cbs.setPreThreeMonthTarget(item.getCbsStoragePb());
        cdb.setPreThreeMonthTarget(item.getCdbMemoryTb());
        cos.setPreThreeMonthTarget(item.getCosStoragePb());
    }

    public void setPreThreeMonthScale(QueryAllProductSummaryResp.Item item) {
        cvm.setPreThreeMonthScale(item.getCvmCoresIn10k());
        gpu.setPreThreeMonthScale(item.getGpuCard());
        metal.setPreThreeMonthScale(item.getMetalCoresIn10k());
        cbs.setPreThreeMonthScale(item.getCbsStoragePb());
        cdb.setPreThreeMonthScale(item.getCdbMemoryTb());
        cos.setPreThreeMonthScale(item.getCosStoragePb());
    }

    public void setPreTwoMonthScale(QueryAllProductSummaryResp.Item item) {
        cvm.setPreTwoMonthScale(item.getCvmCoresIn10k());
        gpu.setPreTwoMonthScale(item.getGpuCard());
        metal.setPreTwoMonthScale(item.getMetalCoresIn10k());
        cbs.setPreTwoMonthScale(item.getCbsStoragePb());
        cdb.setPreTwoMonthScale(item.getCdbMemoryTb());
        cos.setPreTwoMonthScale(item.getCosStoragePb());
    }

    public void setPreTwoMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setPreTwoMonthTarget(item.getCvmCoresIn10k());
        gpu.setPreTwoMonthTarget(item.getGpuCard());
        metal.setPreTwoMonthTarget(item.getMetalCoresIn10k());
        cbs.setPreTwoMonthTarget(item.getCbsStoragePb());
        cdb.setPreTwoMonthTarget(item.getCdbMemoryTb());
        cos.setPreTwoMonthTarget(item.getCosStoragePb());
    }

    public void setCurW1Scale(QueryAllProductSummaryResp.Item item) {
        cvm.setCurW1Scale(item.getCvmCoresIn10k());
        gpu.setCurW1Scale(item.getGpuCard());
        metal.setCurW1Scale(item.getMetalCoresIn10k());
        cbs.setCurW1Scale(item.getCbsStoragePb());
        cdb.setCurW1Scale(item.getCdbMemoryTb());
        cos.setCurW1Scale(item.getCosStoragePb());
    }

    public void setCurW2Scale(QueryAllProductSummaryResp.Item item) {
        cvm.setCurW2Scale(item.getCvmCoresIn10k());
        gpu.setCurW2Scale(item.getGpuCard());
        metal.setCurW2Scale(item.getMetalCoresIn10k());
        cbs.setCurW2Scale(item.getCbsStoragePb());
        cdb.setCurW2Scale(item.getCdbMemoryTb());
        cos.setCurW2Scale(item.getCosStoragePb());
    }

    public void setCurW3Scale(QueryAllProductSummaryResp.Item item) {
        cvm.setCurW3Scale(item.getCvmCoresIn10k());
        gpu.setCurW3Scale(item.getGpuCard());
        metal.setCurW3Scale(item.getMetalCoresIn10k());
        cbs.setCurW3Scale(item.getCbsStoragePb());
        cdb.setCurW3Scale(item.getCdbMemoryTb());
        cos.setCurW3Scale(item.getCosStoragePb());
    }

    public void setCurW4Scale(QueryAllProductSummaryResp.Item item) {
        cvm.setCurW4Scale(item.getCvmCoresIn10k());
        gpu.setCurW4Scale(item.getGpuCard());
        metal.setCurW4Scale(item.getMetalCoresIn10k());
        cbs.setCurW4Scale(item.getCbsStoragePb());
        cdb.setCurW4Scale(item.getCdbMemoryTb());
        cos.setCurW4Scale(item.getCosStoragePb());
    }

    public void setCurW5Scale(QueryAllProductSummaryResp.Item item) {
        cvm.setCurW5Scale(item.getCvmCoresIn10k());
        gpu.setCurW5Scale(item.getGpuCard());
        metal.setCurW5Scale(item.getMetalCoresIn10k());
        cbs.setCurW5Scale(item.getCbsStoragePb());
        cdb.setCurW5Scale(item.getCdbMemoryTb());
        cos.setCurW5Scale(item.getCosStoragePb());
    }

    public void setAfterOneMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setAfterOneMonthTarget(item.getCvmCoresIn10k());
        gpu.setAfterOneMonthTarget(item.getGpuCard());
        metal.setAfterOneMonthTarget(item.getMetalCoresIn10k());
        cbs.setAfterOneMonthTarget(item.getCbsStoragePb());
        cdb.setAfterOneMonthTarget(item.getCdbMemoryTb());
        cos.setAfterOneMonthTarget(item.getCosStoragePb());
    }

    public void setAfterTwoMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setAfterTwoMonthTarget(item.getCvmCoresIn10k());
        gpu.setAfterTwoMonthTarget(item.getGpuCard());
        metal.setAfterTwoMonthTarget(item.getMetalCoresIn10k());
        cbs.setAfterTwoMonthTarget(item.getCbsStoragePb());
        cdb.setAfterTwoMonthTarget(item.getCdbMemoryTb());
        cos.setAfterTwoMonthTarget(item.getCosStoragePb());
    }

    public void setAfterThreeMonthTarget(QueryAllProductSummaryResp.Item item) {
        cvm.setAfterThreeMonthTarget(item.getCvmCoresIn10k());
        gpu.setAfterThreeMonthTarget(item.getGpuCard());
        metal.setAfterThreeMonthTarget(item.getMetalCoresIn10k());
        cbs.setAfterThreeMonthTarget(item.getCbsStoragePb());
        cdb.setAfterThreeMonthTarget(item.getCdbMemoryTb());
        cos.setAfterThreeMonthTarget(item.getCosStoragePb());
    }


    @Data
    public class Row {

        String productType;
        BigDecimal preThreeMonthScale;
        BigDecimal preThreeMonthTarget;
        BigDecimal preTwoMonthScale;
        BigDecimal preTwoMonthTarget;
        BigDecimal preOneMonthScale;
        BigDecimal preOneMonthTarget;
        BigDecimal curW1Scale;
        BigDecimal curW2Scale;
        BigDecimal curW3Scale;
        BigDecimal curW4Scale;
        BigDecimal curW5Scale;
        BigDecimal afterOneMonthTarget;
        BigDecimal afterTwoMonthTarget;
        BigDecimal afterThreeMonthTarget;

        public Row(String productType) {
            this.productType = productType;
        }
    }
}
