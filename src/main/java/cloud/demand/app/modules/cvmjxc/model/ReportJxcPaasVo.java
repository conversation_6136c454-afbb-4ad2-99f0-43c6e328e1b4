package cloud.demand.app.modules.cvmjxc.model;

import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class ReportJxcPaasVo {

    @Column("category")
    String category;
    @Column("p_name")
    String indicatorName;
    @Column("start_val")
    BigDecimal startVal;
    @Column("end_val")
    BigDecimal endVal;
    @Column("diff")
    BigDecimal diff;

    String note;
}
