package cloud.demand.app.modules.resource_view.model;

import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前端页面的一个方块就一个  key value
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViewItem {

    String name;
    BigDecimal value;
    String unit;
    List<ViewItem> details;

    public ViewItem(String name, BigDecimal value) {
        this.name = name;
        this.value = value;
    }

    public ViewItem(String name, BigDecimal value, String unit) {
        this.name = name;
        this.value = value;
        this.unit = unit;
    }
}
