package cloud.demand.app.modules.resource_view.model;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;


/**
 * 为了兼容 不同维度的 group by，这里使用any 来替换
 */
@Data
@ToString
@Table("pod_version_info")
public class PodVersionInfoDO {

    @Column(value = "any_bg_id" , computed = "any(bg_id)")
    private Integer bgId;

    @Column(value = "any_bg_name", computed = "any(bg_name)")
    private String bgName;

    @Column(value = "any_dept_id", computed = "any(dept_id)")
    private Integer deptId;

    @Column(value = "any_dept_name", computed = "any(dept_name)")
    private String deptName;

    @Column(value = "any_plan_product_id", computed = "any(plan_product_id)")
    private Integer planProductId;

    @Column(value = "any_plan_product_name", computed = "any(plan_product_name)")
    private String planProductName;


}