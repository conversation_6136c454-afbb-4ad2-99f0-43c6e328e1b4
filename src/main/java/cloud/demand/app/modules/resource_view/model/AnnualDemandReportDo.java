package cloud.demand.app.modules.resource_view.model;

import com.google.common.base.Strings;
import com.pugwoo.dbhelper.annotation.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
//@Table("ads_tcres_annual_demand_report")
@Deprecated
public class AnnualDemandReportDo {

    @Column("version")
    private String version;

    @Column("p_ym")
    private Integer ym;

    @Column("p_date")
    private String date;

    @Column("dim_big_class")
    private String dimBigClass = "其他";

    @Column("dim_product_class")
    private String dimProductClass = "其他";

    @Column("dim_plan_product")
    private String dimPlanProduct = "未分类";

    @Column("dim_device_type")
    private String dimDeviceType = "未分类";

    @Column("dim_instance_type")
    private String dimInstanceType = "未分类";

    @Column("dim_cpu_type")
    private String dimCpuType = "未分类";

    @Column("dim_net_type")
    private String dimNetType = "未分类";

    @Column("dim_region")
    private String dimRegion = "未分类";

    @Column("dim_region_class")
    private String dimRegionClass = "未分类";

    @Column("dim_industry")
    private String dimIndustry = "未分类";

    @Column("dim_customer")
    private String dimCustomer = "未分类";


    @Column("dim_reason1")
    private String dimReason1 = "未分类";

    @Column("dim_reason2")
    private String dimReason2 = "未分类";


    @Column("dim_supply_way")
    private String dimSupplyWay = "未分类";


    @Column("p_index")
    private String pIndex;

    @Column("num")
    private BigDecimal num;

    @Column("cores")
    private BigDecimal cores;

    @Column("ori_id")
    private String oriId;

    @Column("ori_data")
    private String oriData;

    @Column("v_compute_type")
    private String computeType = "CPU";


    public void setDimBigClass(String dimBigClass) {
        if (Strings.isNullOrEmpty(dimBigClass)) {
            return;
        }
        this.dimBigClass = dimBigClass;
    }

    public void setDimProductClass(String dimProductClass) {
        if (Strings.isNullOrEmpty(dimProductClass)) {
            return;
        }
        this.dimProductClass = dimProductClass;
    }

    public void setDimPlanProduct(String dimPlanProduct) {
        if (Strings.isNullOrEmpty(dimPlanProduct)) {
            return;
        }
        this.dimPlanProduct = dimPlanProduct;
    }

    public void setDimDeviceType(String dimDeviceType) {
        if (Strings.isNullOrEmpty(dimDeviceType)) {
            return;
        }
        this.dimDeviceType = dimDeviceType;
    }

    public void setDimInstanceType(String dimInstanceType) {
        if (Strings.isNullOrEmpty(dimInstanceType)) {
            return;
        }
        this.dimInstanceType = dimInstanceType;
    }

    public void setDimCpuType(String dimCpuType) {
        if (Strings.isNullOrEmpty(dimCpuType)) {
            return;
        }
        this.dimCpuType = dimCpuType;
    }

    public void setDimNetType(String dimNetType) {
        if (Strings.isNullOrEmpty(dimNetType)) {
            return;
        }
        this.dimNetType = dimNetType;
    }

    public void setDimRegion(String dimRegion) {
        if (Strings.isNullOrEmpty(dimRegion)) {
            return;
        }
        this.dimRegion = dimRegion;
    }

    public void setDimRegionClass(String dimRegionClass) {
        if (Strings.isNullOrEmpty(dimRegionClass)) {
            return;
        }
        this.dimRegionClass = dimRegionClass;
    }

    public void setDimIndustry(String dimIndustry) {
        if (Strings.isNullOrEmpty(dimIndustry)) {
            return;
        }
        this.dimIndustry = dimIndustry;
    }

    public void setDimCustomer(String dimCustomer) {
        if (Strings.isNullOrEmpty(dimCustomer)) {
            return;
        }
        this.dimCustomer = dimCustomer;
    }

    public void setDimReason1(String dimReason1) {
        if (Strings.isNullOrEmpty(dimReason1)) {
            return;
        }
        this.dimReason1 = dimReason1;
    }

    public void setDimReason2(String dimReason2) {
        if (Strings.isNullOrEmpty(dimReason2)) {
            return;
        }
        this.dimReason2 = dimReason2;
    }

    public void setDimSupplyWay(String dimSupplyWay) {
        if (Strings.isNullOrEmpty(dimSupplyWay)) {
            return;
        }
        this.dimSupplyWay = dimSupplyWay;
    }

    public void setComputeType(String computeType) {
        if (Strings.isNullOrEmpty(computeType)) {
            return;
        }
        this.computeType = computeType;
    }

    @SneakyThrows
    public AnnualDemandReportDo duplicate() {
        AnnualDemandReportDo d = new AnnualDemandReportDo();
        BeanUtils.copyProperties(d, this);
        return d;
    }
}

