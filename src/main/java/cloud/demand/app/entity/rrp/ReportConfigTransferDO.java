package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 转移相关的业务部门策略表
 */
@Data
@ToString
@Table("report_config_transfer")
public class ReportConfigTransferDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 业务分组<br/>Column: [biz_group] */
    @Column(value = "biz_group")
    private String bizGroup;

    /** 规划产品<br/>Column: [plan_product] */
    @Column(value = "plan_product")
    private String planProduct;

    /** 一级业务集<br/>Column: [business1] */
    @Column(value = "business1")
    private String business1;

    /** 二级业务模块<br/>Column: [business2] */
    @Column(value = "business2")
    private String business2;

    /** 三级业务模块<br/>Column: [business3] */
    @Column(value = "business3")
    private String business3;

    /** 计算类型 CPU/GPU<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

}