package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Table("report_erp_return_detail")
public class ReportErpReturnDetailDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenInsert = true, setTimeWhenUpdate = true)
    private Date updateTime;

    /** 数据生成日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 需求大类<br/>Column: [demand_category1] */
    @Column(value = "demand_category1")
    private String demandCategory1;

    /** 需求子类<br/>Column: [demand_category2] */
    @Column(value = "demand_category2")
    private String demandCategory2;

    /** 事业群ID<br/>Column: [bg_id] */
    @Column(value = "bg_id")
    private Integer bgId;

    /** 事业群名称<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 部门id<br/>Column: [dept_id] */
    @Column(value = "dept_id")
    private Integer deptId;

    /** 部门名称<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 退回单号<br/>Column: [return_order_id] */
    @Column(value = "return_order_id")
    private String returnOrderId;

    /** 提单人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    /** 单状态<br/>Column: [order_status] */
    @Column(value = "order_status")
    private String orderStatus;

    /** 退回原因分类<br/>Column: [return_reason_type] */
    @Column(value = "return_reason_type")
    private String returnReasonType;

    /** 退回项目<br/>Column: [project] */
    @Column(value = "project")
    private String project;

    /** 退回原因备注<br/>Column: [reason_msg] */
    @Column(value = "reason_msg")
    private String reasonMsg;

    /** 固资号<br/>Column: [asset_id] */
    @Column(value = "asset_id")
    private String assetId;

    /** 退回前一级业务模块<br/>Column: [business1_name] */
    @Column(value = "business1_name")
    private String business1Name;

    /** 退回前二级业务模块<br/>Column: [business2_name] */
    @Column(value = "business2_name")
    private String business2Name;

    /** 退回前三级业务模块<br/>Column: [business3_name] */
    @Column(value = "business3_name")
    private String business3Name;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 处理器类型<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 设备上架时间<br/>Column: [start_use_time] */
    @Column(value = "start_use_time")
    private Date startUseTime;

    /** 地域类型(国内/海外)<br/>Column: [area_type] */
    @Column(value = "area_type")
    private String areaType;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    @Column(value = "region")
    private String region;

    @Column(value = "zone")
    private String zone;

    @Column(value = "campus")
    private String campus;

    @Column(value = "module")
    private String module;

    /** 逻辑核心<br/>Column: [logic_cpu_core] */
    @Column(value = "logic_cpu_core")
    private Integer logicCpuCore;

    /** 逻辑数量，例如GPU是卡<br/>Column: [logic_num] */
    @Column(value = "logic_num")
    private BigDecimal logicNum;

    /** 设备入库时间<br/>Column: [finish_time] */
    @Column(value = "finish_time")
    private Date finishTime;

    /** 运营状态<br/>Column: [cmdb_status] */
    @Column(value = "cmdb_status")
    private String cmdbStatus;

    /** 退回标签<br/>Column: [return_tag] */
    @Column(value = "return_tag")
    private String returnTag;

    /** 退回原因分类<br/>Column: [return_reason_class] */
    @Column(value = "return_reason_class")
    private String returnReasonClass;

    /** 退回设备年限<br/>Column: [device_use_year] */
    @Column(value = "device_use_year")
    private Integer deviceUseYear;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type", insertValueScript = "''")
    private String instanceType;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type", insertValueScript = "''")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform", insertValueScript = "''")
    private String cpuPlatform;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core", insertValueScript = "'0.000000'")
    private BigDecimal saleCore;

    /** 机型代次<br/>Column: [generation_type] */
    @Column(value = "generation_type")
    private String generationType;

    /** 成本分摊比例<br/>Column: [allocation_rate] */
    @Column(value = "allocation_rate")
    private BigDecimal allocationRate;

    /** 退化计划的预计退回日期<br/>Column: [plan_return_date] */
    @Column(value = "plan_return_date")
    private Date planReturnDate;

    /** campus映射腾讯云的可用区id<br/>Column: [txy_zone_id] */
    @Column(value = "txy_zone_id", insertValueScript = "0")
    private Long txyZoneId;

    /** campus映射腾讯云的可用区<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name", insertValueScript = "''")
    private String txyZoneName;

    /** campus映射腾讯云的地域<br/>Column: [txy_region_name] */
    @Column(value = "txy_region_name", insertValueScript = "''")
    private String txyRegionName;

    /** campus映射腾讯云的区域<br/>Column: [txy_area_name] */
    @Column(value = "txy_area_name", insertValueScript = "''")
    private String txyAreaName;

    /** campus映射腾讯云的境内外<br/>Column: [txy_customhouse_title] */
    @Column(value = "txy_customhouse_title", insertValueScript = "''")
    private String txyCustomhouseTitle;

}