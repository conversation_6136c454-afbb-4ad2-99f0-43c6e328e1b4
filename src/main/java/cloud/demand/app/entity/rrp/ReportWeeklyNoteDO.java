package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

/**
 * 进销存-周报-备注表
 */
@Data
@ToString
@Table("report_weekly_note")
public class ReportWeeklyNoteDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "deleted")
    private Boolean deleted;

    @Column(value = "create_time")
    private Date createTime;

    @Column(value = "update_time")
    private Date updateTime;

    /**
     * 周Id<br/>Column: [w_id]
     */
    @Column(value = "w_id")
    private Integer wId;

    /**
     * 产品类型<br/>Column: [product_type]
     */
    @Column(value = "product_type")
    private String productType;

    /**
     * 大类<br/>Column: [category]
     */
    @Column(value = "category")
    private String category;

    /**
     * 小类<br/>Column: [sub_category]
     */
    @Column(value = "sub_category")
    private String subCategory;

    /**
     * 备注<br/>Column: [note]
     */
    @Column(value = "note")
    private String note;

    /**
     * 修改人<br/>Column: [updater]
     */
    @Column(value = "updater")
    private String updater;

}