package cloud.demand.app.entity.rrp;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("report_config_gpu_type")
public class ReportConfigGpuTypeDO extends BaseDO {

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type", insertValueScript = "''")
    private String deviceType;

    /** GPU类型<br/>Column: [gpu_type] */
    @Column(value = "gpu_type")
    private String gpuType;

    /** GPU卡型<br/>Column: [gpu_card_type] */
    @Column(value = "gpu_card_type")
    private String gpuCardType;

    /** GPU卡型详情<br/>Column: [gpu_card_type_detail] */
    @Column(value = "gpu_card_type_detail")
    private String gpuCardTypeDetail;

}
