package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.time.LocalDate;
import java.util.Date;

/**
 * 云cvm退回明细表
 */
@Data
@ToString
@Table("report_cvm_return_detail")
public class ReportCvmReturnDetailDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private Date updateTime;

    /** 数据生成日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 需求大类<br/>Column: [demand_category1] */
    @Column(value = "demand_category1")
    private String demandCategory1;

    /** 需求子类<br/>Column: [demand_category2] */
    @Column(value = "demand_category2")
    private String demandCategory2;

    /** 事业群ID<br/>Column: [bg_id] */
    @Column(value = "bg_id")
    private Integer bgId;

    /** 事业群名称<br/>Column: [bg_name] */
    @Column(value = "bg_name")
    private String bgName;

    /** 部门id<br/>Column: [dept_id] */
    @Column(value = "dept_id")
    private Integer deptId;

    /** 部门名称<br/>Column: [dept_name] */
    @Column(value = "dept_name")
    private String deptName;

    /** 规划产品id<br/>Column: [plan_product_id] */
    @Column(value = "plan_product_id")
    private Integer planProductId;

    /** 规划产品<br/>Column: [plan_product_name] */
    @Column(value = "plan_product_name")
    private String planProductName;

    /** 退回项目类型<br/>Column: [project] */
    @Column(value = "project")
    private String project;

    /** 地域类型(国内/海外)<br/>Column: [area_type] */
    @Column(value = "area_type")
    private String areaType;

    /** 国家<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** 城市<br/>Column: [city] */
    @Column(value = "city")
    private String city;

    /** 可用区<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    /** cvm固资号<br/>Column: [instance_asset_id] */
    @Column(value = "instance_asset_id")
    private String instanceAssetId;

    /** 实例机型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 核心类型，大核心小核心<br/>Column: [core_type] */
    @Column(value = "core_type")
    private String coreType;

    /** cpu逻辑核心数<br/>Column: [logic_cpu_core] */
    @Column(value = "logic_cpu_core")
    private Integer logicCpuCore;

    /** 资源池类型<br/>Column: [pool] */
    @Column(value = "pool")
    private String pool;

    /** 退回单号<br/>Column: [return_order_id] */
    @Column(value = "return_order_id")
    private String returnOrderId;

    /** 退回时间<br/>Column: [return_time] */
    @Column(value = "return_time")
    private Date returnTime;

    /** 退回日期<br/>Column: [return_date] */
    @Column(value = "return_date")
    private LocalDate returnDate;

    /** 计划退回日期<br/>Column: [plan_return_date] */
    @Column(value = "plan_return_date")
    private LocalDate planReturnDate;

    @Column("generation_type")
    private String generationType;
}