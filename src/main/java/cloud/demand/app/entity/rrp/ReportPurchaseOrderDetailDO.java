package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购单底表
 */
@Data
@ToString
@Table("report_purchase_order_detail")
public class ReportPurchaseOrderDetailDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private Date updateTime;

    /** 数据生成日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 星云子单号<br/>Column: [sub_id] */
    @Column(value = "sub_id")
    private String subId;

    /** 固资编号<br/>Column: [asset_id] */
    @Column(value = "asset_id")
    private String assetId;

    /** 一级业务<br/>Column: [business1] */
    @Column(value = "business1")
    private String business1;

    /** 二级业务<br/>Column: [business2] */
    @Column(value = "business2")
    private String business2;

    /** 三级业务<br/>Column: [business3] */
    @Column(value = "business3")
    private String business3;

    /** 业务模块<br/>Column: [business] */
    @Column(value = "business")
    private String business;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 实际设备类型<br/>Column: [actual_device_type] */
    @Column(value = "actual_device_type")
    private String actualDeviceType;

    /** 处理器类型<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 逻辑核心数<br/>Column: [logic_core_num] */
    @Column(value = "logic_core_num")
    private Integer logicCoreNum;

    /** 逻辑数量，例如GPU存卡数<br/>Column: [logic_num] */
    @Column(value = "logic_num")
    private BigDecimal logicNum;

    /** 城市<br/>Column: [city] */
    @Column(value = "city")
    private String city;

    /** 地域类型<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** Zone园区<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 星云提单时间<br/>Column: [submit_time] */
    @Column(value = "submit_time")
    private Date submitTime;

    /** 需求月份<br/>Column: [plan_month] */
    @Column(value = "plan_month")
    private String planMonth;

    /** 需求日期<br/>Column: [expect_delivery_date] */
    @Column(value = "expect_delivery_date")
    private Date expectDeliveryDate;

    /** 实际交付时间<br/>Column: [into_buffer_time] */
    @Column(value = "into_buffer_time")
    private Date intoBufferTime;

    /** 规划产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** module<br/>Column: [module_name] */
    @Column(value = "module_name")
    private String moduleName;

    /** 运维区域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 实际机房管理单元<br/>Column: [idc_name] */
    @Column(value = "idc_name")
    private String idcName;

    /** 实际交付时长(天)<br/>Column: [delivery_duration] */
    @Column(value = "delivery_duration")
    private Integer deliveryDuration;

    /** 是否交付及时<br/>Column: [delivery_in_time] */
    @Column(value = "delivery_in_time")
    private String deliveryInTime;

    /** 提货时间<br/>Column: [delivery_time] */
    @Column(value = "delivery_time")
    private Date deliveryTime;

    /** erp单状态<br/>Column: [erp_status] */
    @Column(value = "erp_status")
    private String erpStatus;

    /** 调拨方式<br/>Column: [supply_way] */
    @Column(value = "supply_way")
    private String supplyWay;

    /** 采购原因<br/>Column: [pur_reason] */
    @Column(value = "pur_reason")
    private String purReason;

    /** 采购说明<br/>Column: [purpose] */
    @Column(value = "purpose")
    private String purpose;

    /**
     * 转移单id，对于复用时有值<br/>Column: [ts_order_id]
     */
    @Column(value = "ts_order_id")
    private String tsOrderId;

    /** 客户<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type", insertValueScript = "''")
    private String instanceType;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type", insertValueScript = "''")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform", insertValueScript = "''")
    private String cpuPlatform;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core", insertValueScript = "'0.000000'")
    private BigDecimal saleCore;

    /**erp匹配方式*/
    @Column(value = "erp_match_type", insertValueScript = "''")
    private String erpMatchType;

    /** 客户标注信息<br/>Column: [customer_remark] */
    @Column(value = "customer_remark", insertValueScript = "''")
    private String customerRemark;

    /** 提单人<br/>Column: [creator] */
    @Column(value = "creator", insertValueScript = "''")
    private String creator;

    /** campus映射腾讯云的可用区id<br/>Column: [txy_zone_id] */
    @Column(value = "txy_zone_id", insertValueScript = "''")
    private Long txyZoneId;

    /** campus映射腾讯云的可用区<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name", insertValueScript = "''")
    private String txyZoneName;

    /** campus映射腾讯云的地域<br/>Column: [txy_region_name] */
    @Column(value = "txy_region_name", insertValueScript = "''")
    private String txyRegionName;

    /** campus映射腾讯云的区域<br/>Column: [txy_area_name] */
    @Column(value = "txy_area_name", insertValueScript = "''")
    private String txyAreaName;

    /** campus映射腾讯云的境内外<br/>Column: [txy_customhouse_title] */
    @Column(value = "txy_customhouse_title", insertValueScript = "''")
    private String txyCustomhouseTitle;

    /**需求类型，单据类型*/
    @Column(value = "demand_type")
    private String demandType;

    /**OBS项目类型*/
    @Column(value = "obs_project_type", insertValueScript = "''")
    private String obsProjectType;

    /**
     * 项目类型：自研上云/非自研上云
     * 在代码中判断
     */
    @Column(value = "project_type", insertValueScript = "''")
    private String projectType;


}