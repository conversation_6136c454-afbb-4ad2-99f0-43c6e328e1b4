package cloud.demand.app.entity.rrp;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Data
@ToString
@Table("report_erp_transfer_detail")
public class ReportErpTransferDetailDO {

    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    @Column(value = "update_time", setTimeWhenUpdate = true, setTimeWhenInsert = true)
    private Date updateTime;

    /** 数据生成日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private Date statTime;

    /** 提单人<br/>Column: [applicant] */
    @Column(value = "applicant")
    private String applicant;

    /** 转移单号<br/>Column: [order_id] */
    @Column(value = "order_id")
    private String orderId;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 单来源<br/>Column: [from_source] */
    @Column(value = "from_source")
    private String fromSource;

    /** 单状态<br/>Column: [order_status] */
    @Column(value = "order_status")
    private String orderStatus;

    /** 固资号<br/>Column: [asset_id] */
    @Column(value = "asset_id")
    private String assetId;

    /** IP<br/>Column: [ip] */
    @Column(value = "ip")
    private String ip;

    /** 逻辑核心<br/>Column: [logic_core_num] */
    @Column(value = "logic_core_num")
    private Integer logicCoreNum;

    /** 逻辑数量，例如GPU是卡<br/>Column: [logic_num] */
    @Column(value = "logic_num")
    private BigDecimal logicNum;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 处理器类型<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 地域类型<br/>Column: [area_type] */
    @Column(value = "area_type")
    private String areaType;

    @Column(value = "zone")
    private String zone;

    @Column(value = "campus")
    private String campus;

    @Column(value = "module")
    private String module;

    /** 创建时间<br/>Column: [input_time] */
    @Column(value = "input_time")
    private Date inputTime;

    /** 结束时间<br/>Column: [end_time] */
    @Column(value = "end_time")
    private Date endTime;

    /** 源运维部门<br/>Column: [op_from_dept] */
    @Column(value = "op_from_dept")
    private String opFromDept;

    /** 源业务部门<br/>Column: [bs_from_dept] */
    @Column(value = "bs_from_dept")
    private String bsFromDept;

    /** 源规划产品<br/>Column: [bs_from_product] */
    @Column(value = "bs_from_product")
    private String bsFromProduct;

    /** 源业务模块1<br/>Column: [bs_from_busi1] */
    @Column(value = "bs_from_busi1")
    private String bsFromBusi1;

    /** 源业务模块2<br/>Column: [bs_from_busi2] */
    @Column(value = "bs_from_busi2")
    private String bsFromBusi2;

    /** 源业务模块3<br/>Column: [bs_from_busi3] */
    @Column(value = "bs_from_busi3")
    private String bsFromBusi3;

    /** 目的运维部门<br/>Column: [op_to_dept] */
    @Column(value = "op_to_dept")
    private String opToDept;

    /** 目的业务部门<br/>Column: [bs_to_dept] */
    @Column(value = "bs_to_dept")
    private String bsToDept;

    /** 目的规划产品<br/>Column: [bs_to_product] */
    @Column(value = "bs_to_product")
    private String bsToProduct;

    /** 目的业务模块1<br/>Column: [bs_to_busi1] */
    @Column(value = "bs_to_busi1")
    private String bsToBusi1;

    /** 目的业务模块2<br/>Column: [bs_to_busi2] */
    @Column(value = "bs_to_busi2")
    private String bsToBusi2;

    /** 目的业务模块3<br/>Column: [bs_to_busi3] */
    @Column(value = "bs_to_busi3")
    private String bsToBusi3;

    /** 转移类型，转入或转出<br/>Column: [transfer_type] */
    @Column(value = "transfer_type")
    private String transferType;

    /** 是否是r单<br/>Column: [is_r_order] */
    @Column(value = "is_r_order")
    private String isROrder;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type", insertValueScript = "''")
    private String instanceType;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type", insertValueScript = "''")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform", insertValueScript = "''")
    private String cpuPlatform;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core", insertValueScript = "'0.000000'")
    private BigDecimal saleCore;

    /** 转移原因<br/>Column: [reason] */
    @Column(value = "reason", insertValueScript = "''")
    private String reason;

    /** 转移备注<br/>Column: [memo] */
    @Column(value = "memo", insertValueScript = "''")
    private String memo;

    /** campus映射腾讯云的可用区id<br/>Column: [txy_zone_id] */
    @Column(value = "txy_zone_id", insertValueScript = "0")
    private Long txyZoneId;

    /** campus映射腾讯云的可用区<br/>Column: [txy_zone_name] */
    @Column(value = "txy_zone_name", insertValueScript = "''")
    private String txyZoneName;

    /** campus映射腾讯云的地域<br/>Column: [txy_region_name] */
    @Column(value = "txy_region_name", insertValueScript = "''")
    private String txyRegionName;

    /** campus映射腾讯云的区域<br/>Column: [txy_area_name] */
    @Column(value = "txy_area_name", insertValueScript = "''")
    private String txyAreaName;

    /** campus映射腾讯云的境内外<br/>Column: [txy_customhouse_title] */
    @Column(value = "txy_customhouse_title", insertValueScript = "''")
    private String txyCustomhouseTitle;

}