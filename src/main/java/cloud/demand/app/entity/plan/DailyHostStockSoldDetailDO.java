package cloud.demand.app.entity.plan;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 母机库存售卖详情
 */
@Data
@ToString
@Table("daily_host_stock_sold_detail")
public class DailyHostStockSoldDetailDO {

    /** 统计时间<br/>Column: [stattime] */
    @Column(value = "stattime", isKey = true)
    private Date stattime;

    /** 母机机型<br/>Column: [hosttype] */
    @Column(value = "hosttype", isKey = true)
    private String hosttype;

    /** zoneid<br/>Column: [zoneid] */
    @Column(value = "zoneid", isKey = true)
    private Long zoneid;

    /** 母机核数<br/>Column: [host_cpu] */
    @Column(value = "host_cpu")
    private Long hostCpu;

    /** 售卖核数(正常售出+超卖售出)<br/>Column: [sold_cpu] */
    @Column(value = "sold_cpu")
    private Long soldCpu;

    /** 超卖核数<br/>Column: [oversold_cpu] */
    @Column(value = "oversold_cpu")
    private Long oversoldCpu;

    /** 已售核数 售卖率=已售核数/母机核数<br/>Column: [allsold_cpu] */
    @Column(value = "allsold_cpu")
    private Long allsoldCpu;

    /** 全部核数(母机核数)<br/>Column: [all_cpu] */
    @Column(value = "all_cpu")
    private Long allCpu;

}