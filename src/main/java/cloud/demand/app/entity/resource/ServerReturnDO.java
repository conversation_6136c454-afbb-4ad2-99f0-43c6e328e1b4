package cloud.demand.app.entity.resource;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.sql.Timestamp;
import java.util.Date;

@Data
@ToString
@Table("server_return")
public class ServerReturnDO {

    /** ID<br/>Column: [ID] */
    @Column(value = "ID", isKey = true, isAutoIncrement = true)
    private Integer iD;

    /** 固资编号<br/>Column: [LogicPcCode] */
    @Column(value = "LogicPcCode")
    private String logicPcCode;

    /** 回收级别，L1紧急，L2普通<br/>Column: [RecycleLevel] */
    @Column(value = "RecycleLevel")
    private String recycleLevel;

    /** 入库时间<br/>Column: [StartUseDate] */
    @Column(value = "StartUseDate")
    private Date startUseDate;

    /** 机房管理单元<br/>Column: [BelongUnitName] */
    @Column(value = "BelongUnitName")
    private String belongUnitName;

    /** 设备所属region<br/>Column: [Region] */
    @Column(value = "Region")
    private String region;

    /** 设备所属zone<br/>Column: [Zone] */
    @Column(value = "Zone")
    private String zone;

    /** 设备所属campus<br/>Column: [Campus] */
    @Column(value = "Campus")
    private String campus;

    /** 设备所属module<br/>Column: [Module] */
    @Column(value = "Module")
    private String module;

    /** module业务类型<br/>Column: [ModuleBusiType] */
    @Column(value = "ModuleBusiType")
    private String moduleBusiType;

    /** 所属逻辑区<br/>Column: [LogicArea] */
    @Column(value = "LogicArea")
    private String logicArea;

    /** 设备类型<br/>Column: [DeviceType] */
    @Column(value = "DeviceType")
    private String deviceType;

    /** 设备型号<br/>Column: [ServerModel] */
    @Column(value = "ServerModel")
    private String serverModel;

    /** 运维部门ID<br/>Column: [BelongDeptId] */
    @Column(value = "BelongDeptId")
    private Integer belongDeptId;

    /** 运维部门<br/>Column: [BelongDept] */
    @Column(value = "BelongDept")
    private String belongDept;

    /** 业务部门ID<br/>Column: [BelongDpId] */
    @Column(value = "BelongDpId")
    private Integer belongDpId;

    /** 业务部门<br/>Column: [BelongDpName] */
    @Column(value = "BelongDpName")
    private String belongDpName;

    /** 业务集ID<br/>Column: [BelongBnId] */
    @Column(value = "BelongBnId")
    private Integer belongBnId;

    /** 业务集<br/>Column: [BelongBnName] */
    @Column(value = "BelongBnName")
    private String belongBnName;

    /** 业务ID<br/>Column: [Bussiness2Id] */
    @Column(value = "Bussiness2Id")
    private Integer bussiness2Id;

    /** 业务名<br/>Column: [Bussiness2Name] */
    @Column(value = "Bussiness2Name")
    private String bussiness2Name;

    /** 业务模块ID<br/>Column: [Bussiness3Id] */
    @Column(value = "Bussiness3Id")
    private Integer bussiness3Id;

    /** 业务模块<br/>Column: [Bussiness3Name] */
    @Column(value = "Bussiness3Name")
    private String bussiness3Name;

    /** 产品ID<br/>Column: [BelongPdId] */
    @Column(value = "BelongPdId")
    private Integer belongPdId;

    /** 产品<br/>Column: [BelongPdName] */
    @Column(value = "BelongPdName")
    private String belongPdName;

    /** 规划产品ID<br/>Column: [PlanProductId] */
    @Column(value = "PlanProductId")
    private Integer planProductId;

    /** 规划产品<br/>Column: [PlanProductName] */
    @Column(value = "PlanProductName")
    private String planProductName;

    /** 维护人<br/>Column: [Maintainer] */
    @Column(value = "Maintainer")
    private String maintainer;

    /** IP<br/>Column: [ServerIP] */
    @Column(value = "ServerIP")
    private String serverIP;

    /** RaidId<br/>Column: [RaidId] */
    @Column(value = "RaidId")
    private Integer raidId;

    @Column(value = "RaidName")
    private String raidName;

    /** 期望回收时间<br/>Column: [ExpectedTime] */
    @Column(value = "ExpectedTime")
    private Date expectedTime;

    /** Uwork下发回收状态(9：待下发，10：待定级，11：待审核，12：定级中)<br/>Column: [Uworkstatus] */
    @Column(value = "Uworkstatus")
    private Integer uworkstatus;

    /** Uwork下发回收信息<br/>Column: [Uworkerrmsg] */
    @Column(value = "Uworkerrmsg")
    private String uworkerrmsg;

    /** UWORK流程单号<br/>Column: [UworkInstanceId] */
    @Column(value = "UworkInstanceId")
    private String uworkInstanceId;

    /** 回收到资源中心时间<br/>Column: [RecycleTime] */
    @Column(value = "RecycleTime")
    private Date recycleTime;

    /** Uwork回收处理状态<br/>Column: [UworkFinishedStatus] */
    @Column(value = "UworkFinishedStatus")
    private Integer uworkFinishedStatus;

    /** Uwork回收处理信息<br/>Column: [UworkFinishederrmsg] */
    @Column(value = "UworkFinishederrmsg")
    private String uworkFinishederrmsg;

    /** Uwork回收完成时间<br/>Column: [UworkFinishedTime] */
    @Column(value = "UworkFinishedTime")
    private Date uworkFinishedTime;

    /** 插入时间<br/>Column: [insert_time] */
    @Column(value = "insert_time")
    private Date insertTime;

    /** 成本分摊结束日期<br/>Column: [allocation_end_date] */
    @Column(value = "allocation_end_date")
    private Date allocationEndDate;

    /** 来源服务<br/>Column: [Service] */
    @Column(value = "Service")
    private String service;

    /** 发起人<br/>Column: [Starter] */
    @Column(value = "Starter")
    private String starter;

    /** 流程单号<br/>Column: [InstanceId] */
    @Column(value = "InstanceId")
    private String instanceId;

    /** 主动被动退回标记<br/>Column: [ReturnFlag] */
    @Column(value = "ReturnFlag")
    private String returnFlag;

    /** é€€å›žåŽŸå› (äºŒçº§åˆ†ç±»)<br/>Column: [Reason] */
    @Column(value = "Reason")
    private String reason;

    /** 是否置换<br/>Column: [IsReplace] */
    @Column(value = "IsReplace")
    private String isReplace;

    /** 置换备注<br/>Column: [ReplaceRemark] */
    @Column(value = "ReplaceRemark")
    private String replaceRemark;

    /** 记录最近更改时间<br/>Column: [ChangeTime] */
    @Column(value = "ChangeTime")
    private Timestamp changeTime;

    /** Zeebe实例id<br/>Column: [ZeebeInstanceId] */
    @Column(value = "ZeebeInstanceId")
    private String zeebeInstanceId;

    /** 退回业务分类<br/>Column: [return_sys_type] */
    @Column(value = "return_sys_type")
    private String returnSysType;

    /** 设备备份维护人<br/>Column: [bak_maintainer] */
    @Column(value = "bak_maintainer")
    private String bakMaintainer;

    /** 库存分类<br/>Column: [inventory_class] */
    @Column(value = "inventory_class")
    private String inventoryClass;

    /** 成本分摊备注<br/>Column: [allocation_memo] */
    @Column(value = "allocation_memo")
    private String allocationMemo;

    /** 0 - 否， 1 - 是，是否是云退回<br/>Column: [is_yun_return] */
    @Column(value = "is_yun_return")
    private Integer isYunReturn;

    /** 是否紧急<br/>Column: [is_urgent] */
    @Column(value = "is_urgent")
    private Integer isUrgent;

    /** 空负载天数<br/>Column: [empty_load_day] */
    @Column(value = "empty_load_day")
    private Integer emptyLoadDay;

    /** 成本分摊比例<br/>Column: [allocation_rate] */
    @Column(value = "allocation_rate")
    private Float allocationRate;

    /** 原成本分摊标签<br/>Column: [init_allocation_memo] */
    @Column(value = "init_allocation_memo")
    private String initAllocationMemo;

    /** 原成本分摊结束时间<br/>Column: [init_allocation_end_date] */
    @Column(value = "init_allocation_end_date")
    private Date initAllocationEndDate;

}