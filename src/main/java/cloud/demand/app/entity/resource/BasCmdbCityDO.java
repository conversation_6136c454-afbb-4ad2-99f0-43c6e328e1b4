package cloud.demand.app.entity.resource;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;

/**
 * OMDB基础数据-城市信息
 */
@Data
@ToString
@Table("bas_cmdb_city")
public class BasCmdbCityDO {

    /** cmdb id<br/>Column: [Id] */
    @Column(value = "Id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 城市中文名称<br/>Column: [CityName] */
    @Column(value = "CityName")
    private String cityName;

    /** 城市英文全称<br/>Column: [EnglishName] */
    @Column(value = "EnglishName")
    private String englishName;

    /** 城市代码<br/>Column: [CityCode] */
    @Column(value = "CityCode")
    private String cityCode;

    /** ZoneId<br/>Column: [ZoneId] */
    @Column(value = "ZoneId")
    private Integer zoneId;

    /** Zone管辖区<br/>Column: [ZoneName] */
    @Column(value = "ZoneName")
    private String zoneName;

    /** 省份/州 ID<br/>Column: [ProvinceId] */
    @Column(value = "ProvinceId")
    private Integer provinceId;

    /** 省份/州<br/>Column: [Province] */
    @Column(value = "Province")
    private String province;

    /** 国内RegionID<br/>Column: [RegionId] */
    @Column(value = "RegionId")
    private Integer regionId;

    /** 国内Region区域名称<br/>Column: [RegionName] */
    @Column(value = "RegionName")
    private String regionName;

    /** 国家或地区，二位字母码<br/>Column: [CountryCode] */
    @Column(value = "CountryCode")
    private String countryCode;

    /** 国家或地区中文名称<br/>Column: [CountryChinese] */
    @Column(value = "CountryChinese")
    private String countryChinese;

    /** 0-物理,1-虚拟,2-腾讯云特有<br/>Column: [IsVirtual] */
    @Column(value = "IsVirtual")
    private Integer isVirtual;

    /** 是否可用 1-可用，0-不可用<br/>Column: [EnableFlag] */
    @Column(value = "EnableFlag")
    private Boolean enableFlag;

    /** 创建时间<br/>Column: [CreateTime] */
    @Column(value = "CreateTime")
    private Date createTime;

    /** 更新时间<br/>Column: [ModifyTime] */
    @Column(value = "ModifyTime")
    private Timestamp modifyTime;

    /** 经度<br/>Column: [longitude] */
    @Column(value = "longitude")
    private BigDecimal longitude;

    /** 纬度<br/>Column: [latitude] */
    @Column(value = "latitude")
    private BigDecimal latitude;

}