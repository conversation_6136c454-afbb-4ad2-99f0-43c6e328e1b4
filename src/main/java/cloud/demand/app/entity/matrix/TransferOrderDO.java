package cloud.demand.app.entity.matrix;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@Table("transfer_order")
public class TransferOrderDO {

    @Column(value = "OrderId", isKey = true)
    private String orderId;

    @Column(value = "InputTime")
    private Date inputTime;

    @Column(value = "Applicant")
    private String applicant;

    /** 单据来源<br/>Column: [FromSource] */
    @Column(value = "FromSource")
    private String fromSource;

    /** 0:服务器转移;1:业务平移<br/>Column: [TransferType] */
    @Column(value = "TransferType")
    private Integer transferType;

    /** 1位表示运维部门转移,2位表示业务转移.可组合<br/>Column: [TransferDemand] */
    @Column(value = "TransferDemand")
    private Integer transferDemand;

    /** 业务平移类型.仅当TransferType = 1时有效. 1表示业务集平移, 2业务平移, 3业务模块平移<br/>Column: [BusiTransType] */
    @Column(value = "BusiTransType")
    private Integer busiTransType;

    @Column(value = "OpFromDept")
    private String opFromDept;

    /** 源运维部门ID<br/>Column: [OpFromDeptId] */
    @Column(value = "OpFromDeptId")
    private Integer opFromDeptId;

    @Column(value = "OpFromProduct")
    private String opFromProduct;

    @Column(value = "Memo")
    private String memo;

    @Column(value = "OrderState")
    private Integer orderState;

    @Column(value = "OpToProduct")
    private String opToProduct;

    @Column(value = "OpToBusi1")
    private String opToBusi1;

    @Column(value = "OpToBusi2")
    private String opToBusi2;

    @Column(value = "OpToBusi3")
    private String opToBusi3;

    @Column(value = "OpToBusi3Id")
    private Integer opToBusi3Id;

    @Column(value = "OpToDept")
    private String opToDept;

    @Column(value = "OpToDeptId")
    private Integer opToDeptId;

    @Column(value = "OpToVindicator")
    private String opToVindicator;

    @Column(value = "OpToBakVindicator")
    private String opToBakVindicator;

    @Column(value = "BudFromDept")
    private String budFromDept;

    @Column(value = "BudFromDeptId")
    private Integer budFromDeptId;

    @Column(value = "BudFromProduct")
    private String budFromProduct;

    @Column(value = "BudFromProductId")
    private Integer budFromProductId;

    @Column(value = "BudFromSubject")
    private String budFromSubject;

    @Column(value = "BudFromSubjectId")
    private Integer budFromSubjectId;

    /** 转出预算是否拆分<br/>Column: [BudFromPlatFormId] */
    @Column(value = "BudFromPlatFormId")
    private Integer budFromPlatFormId;

    /** 转入预算是否拆分<br/>Column: [BudToPlatFormId] */
    @Column(value = "BudToPlatFormId")
    private Integer budToPlatFormId;

    @Column(value = "BudToDept")
    private String budToDept;

    @Column(value = "BudToDeptId")
    private Integer budToDeptId;

    @Column(value = "BudToProduct")
    private String budToProduct;

    @Column(value = "BudToProductId")
    private Integer budToProductId;

    @Column(value = "BudToSubject")
    private String budToSubject;

    @Column(value = "BudToSubjectId")
    private Integer budToSubjectId;

    @Column(value = "Reason")
    private String reason;

    @Column(value = "TransBudget")
    private Integer transBudget;

    /** 是否拆分平台资源，1拆分，0不拆分<br/>Column: [SubjectBudget] */
    @Column(value = "SubjectBudget")
    private Integer subjectBudget;

    @Column(value = "EndTime")
    private Date endTime;

    /** 业务平移修改结果. 0表示未修改,1表示成功,-1表示失败<br/>Column: [BusiTransCode] */
    @Column(value = "BusiTransCode")
    private Integer busiTransCode;

    /** 源业务部门<br/>Column: [BsFromDept] */
    @Column(value = "BsFromDept")
    private String bsFromDept;

    /** 源业务部门ID<br/>Column: [BsFromDeptId] */
    @Column(value = "BsFromDeptId")
    private Integer bsFromDeptId;

    @Column(value = "BsFromPlanProduct")
    private String bsFromPlanProduct;

    @Column(value = "BsFromPlanProductId")
    private Integer bsFromPlanProductId;

    /** 源产品<br/>Column: [BsFromProduct] */
    @Column(value = "BsFromProduct")
    private String bsFromProduct;

    /** 源产品ID<br/>Column: [BsFromProductId] */
    @Column(value = "BsFromProductId")
    private Integer bsFromProductId;

    /** 源业务集<br/>Column: [BsFromBusi1] */
    @Column(value = "BsFromBusi1")
    private String bsFromBusi1;

    /** 源业务集ID<br/>Column: [BsFromBusi1Id] */
    @Column(value = "BsFromBusi1Id")
    private Integer bsFromBusi1Id;

    /** 源业务<br/>Column: [BsFromBusi2] */
    @Column(value = "BsFromBusi2")
    private String bsFromBusi2;

    /** 源业务ID<br/>Column: [BsFromBusi2Id] */
    @Column(value = "BsFromBusi2Id")
    private Integer bsFromBusi2Id;

    /** 源业务模块<br/>Column: [BsFromBusi3] */
    @Column(value = "BsFromBusi3")
    private String bsFromBusi3;

    /** 源业务模块ID<br/>Column: [BsFromBusi3Id] */
    @Column(value = "BsFromBusi3Id")
    private Integer bsFromBusi3Id;

    /** 目的业务部门<br/>Column: [BsToDept] */
    @Column(value = "BsToDept")
    private String bsToDept;

    /** 目的业务部门ID<br/>Column: [BsToDeptId] */
    @Column(value = "BsToDeptId")
    private Integer bsToDeptId;

    @Column(value = "BsToPlanProduct")
    private String bsToPlanProduct;

    @Column(value = "BsToPlanProductId")
    private Integer bsToPlanProductId;

    /** 目的产品<br/>Column: [BsToProduct] */
    @Column(value = "BsToProduct")
    private String bsToProduct;

    /** 目的产品ID<br/>Column: [BsToProductId] */
    @Column(value = "BsToProductId")
    private Integer bsToProductId;

    /** 目的业务集<br/>Column: [BsToBusi1] */
    @Column(value = "BsToBusi1")
    private String bsToBusi1;

    /** 目的业务集ID<br/>Column: [BsToBusi1Id] */
    @Column(value = "BsToBusi1Id")
    private Integer bsToBusi1Id;

    /** 目的业务<br/>Column: [BsToBusi2] */
    @Column(value = "BsToBusi2")
    private String bsToBusi2;

    /** 目的业务ID<br/>Column: [BsToBusi2Id] */
    @Column(value = "BsToBusi2Id")
    private Integer bsToBusi2Id;

    /** 目的业务模块<br/>Column: [BsToBusi3] */
    @Column(value = "BsToBusi3")
    private String bsToBusi3;

    /** 目的业务模块ID<br/>Column: [BsToBusi3Id] */
    @Column(value = "BsToBusi3Id")
    private Integer bsToBusi3Id;

}