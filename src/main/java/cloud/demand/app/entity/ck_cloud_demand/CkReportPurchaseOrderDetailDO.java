package cloud.demand.app.entity.ck_cloud_demand;

import cloud.demand.app.entity.rrp.ReportPurchaseOrderDetailDO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

@Data
@ToString
@Table("report_purchase_order_detail")
public class CkReportPurchaseOrderDetailDO {

    /** 数据日期<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate statTime;

    /** 数据生成时间<br/>Column: [create_time] */
    @Column(value = "create_time")
    private Date createTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 星云子单号<br/>Column: [sub_id] */
    @Column(value = "sub_id")
    private String subId;

    /** 固资编号<br/>Column: [asset_id] */
    @Column(value = "asset_id")
    private String assetId;

    /** 一级业务<br/>Column: [business1] */
    @Column(value = "business1")
    private String business1;

    /** 二级业务<br/>Column: [business2] */
    @Column(value = "business2")
    private String business2;

    /** 三级业务<br/>Column: [business3] */
    @Column(value = "business3")
    private String business3;

    /** 业务模块<br/>Column: [business] */
    @Column(value = "business")
    private String business;

    /** 设备类型<br/>Column: [device_type] */
    @Column(value = "device_type")
    private String deviceType;

    /** 实际设备类型<br/>Column: [actual_device_type] */
    @Column(value = "actual_device_type")
    private String actualDeviceType;

    /** 处理器类型<br/>Column: [compute_type] */
    @Column(value = "compute_type")
    private String computeType;

    /** 逻辑核心数<br/>Column: [logic_core_num] */
    @Column(value = "logic_core_num")
    private Integer logicCoreNum;

    /** 逻辑数量，例如GPU存卡数<br/>Column: [logic_num] */
    @Column(value = "logic_num")
    private BigDecimal logicNum;

    /** 城市<br/>Column: [city] */
    @Column(value = "city")
    private String city;

    /** 地域类型<br/>Column: [country] */
    @Column(value = "country")
    private String country;

    /** Zone园区<br/>Column: [zone] */
    @Column(value = "zone")
    private String zone;

    /** 星云提单时间<br/>Column: [submit_time] */
    @Column(value = "submit_time")
    private Date submitTime;

    /** 需求月份<br/>Column: [plan_month] */
    @Column(value = "plan_month")
    private String planMonth;

    /** 需求日期<br/>Column: [expect_delivery_date] */
    @Column(value = "expect_delivery_date")
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate expectDeliveryDate;

    /** 实际交付时间<br/>Column: [into_buffer_time] */
    @Column(value = "into_buffer_time")
    private Date intoBufferTime;

    /** 规划产品<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /** module<br/>Column: [module_name] */
    @Column(value = "module_name")
    private String moduleName;

    /** 运维区域<br/>Column: [region] */
    @Column(value = "region")
    private String region;

    /** 实际机房管理单元<br/>Column: [idc_name] */
    @Column(value = "idc_name")
    private String idcName;

    /** 实际交付时长(天)<br/>Column: [delivery_duration] */
    @Column(value = "delivery_duration")
    private Integer deliveryDuration;

    /** 是否交付及时<br/>Column: [delivery_in_time] */
    @Column(value = "delivery_in_time")
    private String deliveryInTime;

    /** 提货时间<br/>Column: [delivery_time] */
    @Column(value = "delivery_time")
    private Date deliveryTime;

    /** erp单状态<br/>Column: [erp_status] */
    @Column(value = "erp_status")
    private String erpStatus;

    /** 调拨方式<br/>Column: [supply_way] */
    @Column(value = "supply_way")
    private String supplyWay;

    /** 采购原因<br/>Column: [pur_reason] */
    @Column(value = "pur_reason")
    private String purReason;

    /** 采购说明<br/>Column: [purpose] */
    @Column(value = "purpose")
    private String purpose;

    /** 转移单id，对于复用时有值<br/>Column: [ts_order_id] */
    @Column(value = "ts_order_id")
    private String tsOrderId;

    /** 客户<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 可生产实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 网卡类型<br/>Column: [device_net_type] */
    @Column(value = "device_net_type")
    private String deviceNetType;

    /** CPU平台<br/>Column: [cpu_platform] */
    @Column(value = "cpu_platform")
    private String cpuPlatform;

    /** 可售卖核心数(核)<br/>Column: [sale_core] */
    @Column(value = "sale_core")
    private BigDecimal saleCore;

    /** erp匹配方式<br/>Column: [erp_match_type] */
    @Column(value = "erp_match_type")
    private String erpMatchType;

    /** 客户标注信息<br/>Column: [customer_remark] */
    @Column(value = "customer_remark")
    private String customerRemark;

    /** 提单人<br/>Column: [creator] */
    @Column(value = "creator")
    private String creator;

    public static CkReportPurchaseOrderDetailDO from(ReportPurchaseOrderDetailDO detailDO) {
        CkReportPurchaseOrderDetailDO ckReportPurchaseOrderDetailDO = new CkReportPurchaseOrderDetailDO();
        ckReportPurchaseOrderDetailDO.setStatTime(dateConvert(detailDO.getStatTime()));
        ckReportPurchaseOrderDetailDO.setCreateTime(detailDO.getCreateTime());
        ckReportPurchaseOrderDetailDO.setProductType(detailDO.getProductType());
        ckReportPurchaseOrderDetailDO.setSubId(detailDO.getSubId());
        ckReportPurchaseOrderDetailDO.setAssetId(detailDO.getAssetId());
        ckReportPurchaseOrderDetailDO.setBusiness1(detailDO.getBusiness1());
        ckReportPurchaseOrderDetailDO.setBusiness2(detailDO.getBusiness2());
        ckReportPurchaseOrderDetailDO.setBusiness3(detailDO.getBusiness3());
        ckReportPurchaseOrderDetailDO.setBusiness(detailDO.getBusiness());
        ckReportPurchaseOrderDetailDO.setDeviceType(detailDO.getDeviceType());
        ckReportPurchaseOrderDetailDO.setActualDeviceType(detailDO.getActualDeviceType());
        ckReportPurchaseOrderDetailDO.setComputeType(detailDO.getComputeType());
        ckReportPurchaseOrderDetailDO.setLogicCoreNum(detailDO.getLogicCoreNum());
        ckReportPurchaseOrderDetailDO.setLogicNum(detailDO.getLogicNum());
        ckReportPurchaseOrderDetailDO.setCity(detailDO.getCity());
        ckReportPurchaseOrderDetailDO.setCountry(detailDO.getCountry());
        ckReportPurchaseOrderDetailDO.setZone(detailDO.getZone());
        ckReportPurchaseOrderDetailDO.setSubmitTime(detailDO.getSubmitTime());
        ckReportPurchaseOrderDetailDO.setPlanMonth(detailDO.getPlanMonth());
        ckReportPurchaseOrderDetailDO.setExpectDeliveryDate(dateConvert(detailDO.getExpectDeliveryDate()));
        ckReportPurchaseOrderDetailDO.setIntoBufferTime(detailDO.getIntoBufferTime());
        ckReportPurchaseOrderDetailDO.setProduct(detailDO.getProduct());
        ckReportPurchaseOrderDetailDO.setModuleName(detailDO.getModuleName());
        ckReportPurchaseOrderDetailDO.setRegion(detailDO.getRegion());
        ckReportPurchaseOrderDetailDO.setIdcName(detailDO.getIdcName());
        ckReportPurchaseOrderDetailDO.setDeliveryDuration(detailDO.getDeliveryDuration());
        ckReportPurchaseOrderDetailDO.setDeliveryInTime(detailDO.getDeliveryInTime());
        ckReportPurchaseOrderDetailDO.setDeliveryTime(detailDO.getDeliveryTime());
        ckReportPurchaseOrderDetailDO.setErpStatus(detailDO.getErpStatus());
        ckReportPurchaseOrderDetailDO.setSupplyWay(detailDO.getSupplyWay());
        ckReportPurchaseOrderDetailDO.setPurReason(detailDO.getPurReason());
        ckReportPurchaseOrderDetailDO.setPurpose(detailDO.getPurpose());
        ckReportPurchaseOrderDetailDO.setTsOrderId(detailDO.getTsOrderId());
        ckReportPurchaseOrderDetailDO.setCustomerName(detailDO.getCustomerName());
        ckReportPurchaseOrderDetailDO.setIndustry(detailDO.getIndustry());
        ckReportPurchaseOrderDetailDO.setInstanceType(detailDO.getInstanceType());
        ckReportPurchaseOrderDetailDO.setDeviceNetType(detailDO.getDeviceNetType());
        ckReportPurchaseOrderDetailDO.setCpuPlatform(detailDO.getCpuPlatform());
        ckReportPurchaseOrderDetailDO.setSaleCore(detailDO.getSaleCore());
        ckReportPurchaseOrderDetailDO.setErpMatchType(detailDO.getErpMatchType());
        ckReportPurchaseOrderDetailDO.setCustomerRemark(detailDO.getCustomerRemark());
        ckReportPurchaseOrderDetailDO.setCreator(detailDO.getCreator());
        return ckReportPurchaseOrderDetailDO;
    }

    private static LocalDate dateConvert(Date dateToConvert) {
        if (dateToConvert == null) {
            return null;
        }
        return new java.sql.Date(dateToConvert.getTime()).toLocalDate();
    }
}