package cloud.demand.app.entity.ck_cloud_demand;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class ResourceReportFGIForJson {

    /** 星云子单号<br/>Column: [sub_id] */
    @JsonProperty(value = "subId")
    private String subId;

    /** 固资编号<br/>Column: [asset_id] */
    @Column(value = "assetId")
    private String assetId;

    /** 规划产品<br/>Column: [product] */
    @JsonAlias("product")
    private String planProduct;

    /** 行业<br/>Column: [industry] */
    @JsonProperty(value = "industry")
    private String industry;

    /** 客户<br/>Column: [customer_name] */
    @JsonProperty(value = "customerName")
    private String customerName;

    /** 星云提单时间<br/>Column: [submit_time] */
    @Column(value = "submitTime")
    private Date submitTime;

    private BigDecimal num;

    private BigDecimal core;

}
