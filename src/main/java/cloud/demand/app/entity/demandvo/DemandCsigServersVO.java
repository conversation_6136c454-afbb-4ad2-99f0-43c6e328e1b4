package cloud.demand.app.entity.demandvo;

import cloud.demand.app.entity.demand.DemandCsigServersDO;
import com.pugwoo.dbhelper.annotation.Column;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
public class DemandCsigServersVO extends DemandCsigServersDO {

    /** 物理逻辑核<br/>Column: [logic_core_amount] */
    @Column(value = "sum_logic_core_amount", computed = "sum(logic_core_amount) / count(distinct  day)")
    private BigDecimal sumLogicCoreAmount;

    /** 物理逻辑核<br/>Column: [logic_core_amount] */
    @Column(value = "max_update_time", computed = "max(update_time)")
    private Date maxUpdateTime;

}
