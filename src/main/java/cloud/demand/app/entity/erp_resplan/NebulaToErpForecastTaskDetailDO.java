package cloud.demand.app.entity.erp_resplan;

// package a.b.c;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("nebula_to_erp_forecast_task_detail")
public class NebulaToErpForecastTaskDetailDO {

    /**
     * 主键<br/>Column: [id]
     */
    @Column(value = "id", isAutoIncrement = true)
    private Integer id;

    /**
     * 任务id<br/>Column: [info_id]
     */
    @Column(value = "info_id")
    private Integer infoId;

    /**
     * 需求年月<br/>Column: [demand_year_month]
     */
    @Column(value = "demand_year_month")
    private String demandYearMonth;

    /**
     * 设备类型<br/>Column: [device_type]
     */
    @Column(value = "device_type")
    private String deviceType;

    /**
     * 国家<br/>Column: [country]
     */
    @Column(value = "country")
    private String country;

    /**
     * 规划产品<br/>Column: [plan_product]
     */
    @Column(value = "plan_product")
    private String planProduct;

    /**
     * 行业<br/>Column: [industry]
     */
    @Column(value = "industry")
    private String industry;

    /**
     * 客户名<br/>Column: [customer_name]
     */
    @Column(value = "customer_name")
    private String customerName;

    @Column(value = "demand_type")
    private String demandType;

    /**
     * alter table nebula_to_erp_forecast_task_detail
     * add obs_project_type varchar(100) default '' not null comment '项目类型';
     */
    @Column(value = "obs_project_type")
    private String obsProjectType;

    /**
     * 更新的未执行量<br/>Column: [update_non_executed_num]
     */
    @Column(value = "update_non_executed_num")
    private Integer updateNonExecutedNum;

    @Column(value = "new_erp_num")
    @JsonProperty("newErp")
    private Integer newErpNum;

    @Column(value = "demand_week")
    @JsonProperty("week")
    private Integer demandWeek;

    @Column(value = "campus")
    private String campus;

    @Column(value = "city_name")
    private String cityName;
}