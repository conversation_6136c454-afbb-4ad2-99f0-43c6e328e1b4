package cloud.demand.app.entity.demand;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 公司库存
 */
@Data
@ToString
@Table("demand_csig_company_inventory_cost_snapshot")
public class DemandCsigCompanyInventoryCostSnapshotDO extends BaseDO {

    /** 统计时间<br/>Column: [stat_time] */
    @Column(value = "stat_time")
    private LocalDate statTime;

    /** 产品类型<br/>Column: [product_type] */
    @Column(value = "product_type")
    private String productType;

    /** 当日库存成本<br/>Column: [inv_cost] */
    @Column(value = "inv_cost")
    private BigDecimal invCost;

}
