package cloud.demand.app.entity.demand;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

import java.util.Date;

/**
 * 记录 java 服务请求的数据
 */
@Table("cd_request_log")
@Data
public class CdRequestLog {

    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;

    /** 创建的时间，这个相当于请求时的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 更新时间，这个相当于第二次插入的时间，处理结束的时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenUpdate = true)
    private Date updateTime;

    /** 请求的url<br/>Column: [url] */
    @Column(value = "url")
    private String url;

    /** 请求人，不一定有<br/>Column: [username] */
    @Column(value = "username")
    private String username;

    /** 客户端ip<br/>Column: [client_ip] */
    @Column(value = "client_ip", maxStringLength = 128)
    private String clientIp;

    /** 耗时(毫秒)<br/>Column: [cost_ms] */
    @Column(value = "cost_ms")
    private Integer costMs;

    /** 该条异常请求是否已处理<br/>Column: [is_handled] */
    @Column(value = "is_handled")
    private Boolean isHandled;

    /** 请求体<br/>Column: [request_body] */
    @Column(value = "request_body", maxStringLength = 15000)
    private String requestBody;

    /** 返回体<br/>Column: [response_body] */
    @Column(value = "response_body", maxStringLength = 65535)
    private String responseBody;

}
