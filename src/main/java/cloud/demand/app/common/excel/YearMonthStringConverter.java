package cloud.demand.app.common.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import org.apache.commons.lang3.StringUtils;

public class YearMonthStringConverter implements Converter<YearMonth> {

    private String format;

    private DateTimeFormatter dateTimeFormatter;

    public YearMonthStringConverter() {
        init("yyyy-MM");
    }

    public YearMonthStringConverter(String format) {
        init(format);
    }

    private void init(String format) {
        this.format = format;
        dateTimeFormatter = DateTimeFormatter.ofPattern(format);
    }

    @Override
    public Class supportJavaTypeKey() {
        return YearMonth.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }

    @Override
    public YearMonth convertToJavaData(CellData cellData, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        String value = cellData.getStringValue();
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return YearMonth.parse(value, dateTimeFormatter);
    }

    @Override
    public CellData convertToExcelData(YearMonth value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        if (value == null) {
            return new CellData<>(CellDataTypeEnum.STRING, "");
        }
        return new CellData<>(CellDataTypeEnum.STRING, dateTimeFormatter.format(value));
    }
}
