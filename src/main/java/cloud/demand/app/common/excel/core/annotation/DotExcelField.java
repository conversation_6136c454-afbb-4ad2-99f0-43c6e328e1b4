package cloud.demand.app.common.excel.core.annotation;

import cloud.demand.app.common.excel.core.ExcelGroup;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import com.alibaba.excel.converters.AutoConverter;
import com.alibaba.excel.converters.Converter;
import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Inherited
public @interface DotExcelField {

    /**
     *  组名，关联到{@link ExcelGroup#getCode()}
     * @see ExcelGroup
     */
    String[] group();

    /** excel表头的列名，默认取当前字段名 */
    String excelColumnName() default "";

    /** 对应实体类的字段名，可以映射到任意的实体类中，只要字段名和类型一致，默认取当前字段名 */
    String javaFieldName() default "";

    /**
     * 字段转换器
     * 比如excel中列的值为 是、否，需要转换成java中的 true、 false
     */
    Class<? extends Converter> converter() default AutoConverter.class;

    /** 字段值校验器 */
    Class<? extends ExcelColumnValueChecker>[] valueCheckers() default {};

}
