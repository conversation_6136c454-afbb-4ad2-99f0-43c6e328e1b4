package cloud.demand.app.common.excel.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.sop.enums.Executor;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import lombok.SneakyThrows;
import org.nutz.lang.Strings;

public class CustomerUinColumnChecker implements ExcelColumnValueChecker {

    public static final String CONTEXT_KEY_UIN_INFO_MAP = "CONTEXT_KEY_UIN_INFO_MAP";

    public static final String CONTEXT_KEY_UIN_INFO_MAP_TASK = "CONTEXT_KEY_UIN_INFO_MAP_TASK";

    private static final ExecutorService executor = Executors.newFixedThreadPool(10);

    private final PplDictService pplDictService;

    private final Set<String> uinSet = new HashSet<>();

    public CustomerUinColumnChecker() {
        this.pplDictService = SpringUtil.getBean(PplDictService.class);
    }

    @Override
    public void checkValue(int rowIndex, int columnIndex, String columnName, Object customerUin,
            List<ErrorMessage> errors, ParseContext<?> context) {
        if (customerUin == null) {
            return;
        }
        String uinStr = customerUin.toString();
        if (Strings.isBlank(uinStr)) {
            return;
        }
        int i = uinStr.indexOf(".");
        if (i > -1) {
            uinStr = uinStr.substring(0, i);
        }
        String uin = uinStr;
        Map<String, QueryInfoByUinRsp> uinMap = getUinInfoMap(context);
        if (uinSet.contains(uin)) {
            return;
        }
        List<Future<Boolean>> futureList = getUinInfoTaskResult(context);
        // 查询uin信息，放入上下文uinMap
        Future future = Executor.fiberTaskExecutor.getExecutorService().submit(() -> {
            uinMap.put(uin, pplDictService.queryInfoByUin(uin));
            return Boolean.TRUE;
        });

        futureList.add(future);
        uinSet.add(uin);
        context.getCustomContext().put(CONTEXT_KEY_UIN_INFO_MAP_TASK, futureList);
        context.getCustomContext().put(CONTEXT_KEY_UIN_INFO_MAP, uinMap);
    }

    private static List<Future<Boolean>> getUinInfoTaskResult(ParseContext<?> context) {
        if (context == null || context.getCustomContext() == null) {
            return new ArrayList<>();
        }
        Object obj = context.getCustomContext().get(CONTEXT_KEY_UIN_INFO_MAP_TASK);
        if (obj == null) {
            return new ArrayList<>();
        }
        if (obj instanceof List) {
            return ((List<Future<Boolean>>) obj);
        }
        return new ArrayList<>();
    }

    private static Map<String, QueryInfoByUinRsp> getUinInfoMap(ParseContext<?> context) {
        if (context == null || context.getCustomContext() == null) {
            return new ConcurrentHashMap<>();
        }
        Object obj = context.getCustomContext().get(CONTEXT_KEY_UIN_INFO_MAP);
        if (obj == null) {
            return new ConcurrentHashMap<>();
        }
        if (obj instanceof Map) {
            return ((Map<String, QueryInfoByUinRsp>) obj);
        }
        return new ConcurrentHashMap<>();
    }

    /**
     * 等待异步任务执行完成，从 context 中获取 完整的 uin 信息
     */
    @SneakyThrows
    public static Map<String, QueryInfoByUinRsp> getUinInfoMapCheckTask(ParseContext<?> context) {
        List<Future<Boolean>> futures = getUinInfoTaskResult(context);
        if (ListUtils.isNotEmpty(futures)) {
            for (Future<Boolean> future : futures) {
                future.get();
            }
        }
        return getUinInfoMap(context);
    }

}
