package cloud.demand.app.common.utils;


import de.siegmar.fastcsv.reader.CsvReader;
import de.siegmar.fastcsv.writer.CsvWriter;
import lombok.SneakyThrows;
import org.nutz.lang.Lang;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.Reader;
import java.io.Writer;
import java.net.URL;
import java.util.List;

public class CSVUtils {

    @SneakyThrows
    public static CsvReader getCsvReader(String fileName) {
        URL resource = Thread.currentThread().getContextClassLoader()
                .getResource(fileName);
        assert resource != null;
        File myObj = new File(resource.getFile());
        Reader reader = new FileReader(myObj);
        return CsvReader.builder().build(reader);
    }

    public static List<List<String>> getListFromCsv(String fileName) {
        CsvReader csvReader = getCsvReader(fileName);
        List<List<String>> ret = Lang.list();
        csvReader.stream().forEach((o) -> ret.add(o.getFields()));
        return ret;
    }

    @SneakyThrows
    public static void writeCsv(String url, List<List<String>> data) {
        File myObj = new File(url);
        Writer write = new FileWriter(myObj);
        CsvWriter csv = CsvWriter.builder().build(write);
        data.forEach(csv::writeRow);
        csv.close();
    }


}
