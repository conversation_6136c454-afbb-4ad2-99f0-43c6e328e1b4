package cloud.demand.app.common.utils.orm.convert;

import cloud.demand.app.common.utils.orm.ParamConvert;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import yunti.boot.exception.BizException;

/**
 *  年月范围（String类型的集合或者数组）参数精确到天的范围 <br/>
 *  输入年月格式：{@link #INPUT_FORMAT}
 *  输出年月日格式：{@link #OUTPUT_FORMAT}
 *  例： <br/>
 *  输入：["2023-08", "2023-08"] <br/>
 *  输出：["2023-08-01", "2023-08-31"]
 */
public class YearMonthStringListRangeAccurateToDayConvert implements ParamConvert {

    private static final DateTimeFormatter INPUT_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");

    private static final DateTimeFormatter OUTPUT_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    @Override
    public Object convert(Object normal) {
        if (normal == null) {
            return null;
        }
        if (normal.getClass().isArray()) {
            normal = ListUtils.newArrayList((Object[]) normal);
        }

        if (normal instanceof List && ((List<?>) normal).size() == 2) {
            List<String> result = new ArrayList<>();
            String start = ((List<?>) normal).get(0).toString();
            String end = ((List<?>) normal).get(1).toString();
            YearMonth startYearMonth = YearMonth.parse(start, INPUT_FORMAT);
            YearMonth endYearMonth = YearMonth.parse(end, INPUT_FORMAT);

            result.add(startYearMonth.atDay(1).format(OUTPUT_FORMAT));
            result.add(endYearMonth.atEndOfMonth().format(OUTPUT_FORMAT));
            return result;
        } else {
            throw new BizException("年月范围参数有误");
        }
    }
}
