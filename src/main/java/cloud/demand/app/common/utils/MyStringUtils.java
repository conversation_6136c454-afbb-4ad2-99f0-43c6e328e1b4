package cloud.demand.app.common.utils;

import com.google.common.collect.ImmutableSet;
import java.util.Arrays;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MyStringUtils {

    private static final Set<String> coreUnit = ImmutableSet.of("核", "C", "c");
    private static final Set<String> memoryUnit = ImmutableSet.of("G", "g");
    private static final String configPattern =
            "(\\d+)([" + String.join("", coreUnit) + String.join("", memoryUnit) + "])";

    /**
     * 在用分隔符separator分隔的parent句子里判断是否含有son句子
     *
     * @param son 子句
     * @param parent 父句
     * @param separator 分隔符
     * @return 是否包含
     */
    public static boolean containInSeparator(String son, String parent, String separator) {
        if (org.springframework.util.StringUtils.isEmpty(son) || org.springframework.util.StringUtils
                .isEmpty(parent) || org.springframework.util.StringUtils.isEmpty(separator)) {
            return false;
        }
        return Arrays.asList(parent.split(separator))
                .contains(son);
    }

    public static Integer getWinRate(String winRateText) {
        try {
            if (winRateText.contains("%")) {
                winRateText = winRateText.replace("%", "");
                return (int) Double.parseDouble(winRateText);
            } else if (winRateText.contains(".")) {
                double d = Double.parseDouble(winRateText) * 100;
                return (int) d;
            } else {
                return Integer.parseInt(winRateText);
            }
        } catch (Exception e) {
            log.warn("getWinRate error:", e);
            return null;
        }
    }

    public static Integer getDiskSize(String diskSizeText) {
        try {
            diskSizeText = diskSizeText != null
                    ? diskSizeText.replaceAll("G", "").replaceAll("g", "") : null;
            return diskSizeText == null ? null : Integer.parseInt(diskSizeText);
        } catch (Exception e) {
            log.warn("getDiskSize error:", e);
            return null;
        }
    }

    public static String trim(String str) {
        if (str == null) {
            return null;
        } else {
            return str.trim();
        }
    }
}
