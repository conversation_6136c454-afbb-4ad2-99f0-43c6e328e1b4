package cloud.demand.app.common.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.Map;

@Component
public class SpringUtil implements ApplicationContextAware {

    private static ApplicationContext applicationContext = null;

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }

    //通过name获取 Bean.
    public static Object getBean(String name) {
        return getApplicationContext().getBean(name);
    }

    //通过class获取Bean.
    public static <T> T getBean(Class<T> clazz) {
        return getApplicationContext().getBean(clazz);
    }

    //通过name,以及Clazz返回指定的Bean
    public static <T> T getBean(String name, Class<T> clazz) {
        return getApplicationContext().getBean(name, clazz);
    }

    /**
     * 通过class获取Bean名称以及Bean实例映射关系
     */
    public static <T> Map<String, T> getBeansOfType(Class<T> calss) {
        return getApplicationContext().getBeansOfType(calss);
    }

    /**
     * 通过class获取指定注解标注的Bean名称以及Bean实例映射关系
     */
    public static Map<String, Object> getBeansWithAnnotation(Class<? extends Annotation> cls) {
        return getApplicationContext().getBeansWithAnnotation(cls);
    }

}