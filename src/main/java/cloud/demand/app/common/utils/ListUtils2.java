package cloud.demand.app.common.utils;

import com.pugwoo.wooutils.collect.ListUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiConsumer;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.ToIntFunction;
import java.util.function.ToLongFunction;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.logging.log4j.util.TriConsumer;
import org.springframework.util.CollectionUtils;
import yunti.boot.exception.BizException;

public class ListUtils2 extends ListUtils {


    public static <T, R> List<R> toList(Collection<T> col, Predicate<T> p, Function<T, R> mapper) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyList();
        }
        return col.stream().filter(p).map(mapper).collect(Collectors.toList());
    }

    public static <T, R> List<R> toList(Collection<T> col, Function<T, R> mapper) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyList();
        }
        return col.stream().map(mapper).collect(Collectors.toList());
    }

    public static <T, R> List<R> toListFilterNull(Collection<T> col, Function<T, R> mapper) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyList();
        }
        return col.stream().map(mapper).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static <T, K> Map<K, T> toMap(Collection<T> col, Function<T, K> keyMapper) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyMap();
        }
        return col.stream().collect(Collectors.toMap(keyMapper, t -> t));
    }

    public static <T> Integer sumInteger(Collection<T> col, ToIntFunction<T> mapper) {
        return col.stream().mapToInt(mapper).sum();
    }

    public static <T> Long sumLong(Collection<T> col, ToLongFunction<T> mapper) {
        return col.stream().mapToLong(mapper).sum();
    }

    public static <T> Long sumLong(Collection<T> col, Predicate<T> p, ToLongFunction<T> mapper) {
        return col.stream().filter(p).mapToLong(mapper).sum();
    }

    public static <T, K> Map<K, List<T>> group(Collection<T> col, Function<T, K> function) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyMap();
        }
        return col.stream().collect(Collectors.groupingBy(function));
    }

    public static <T, K> Map<K, Long> groupCount(Collection<T> col, Function<T, K> function) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyMap();
        }
        return col.stream().collect(Collectors.groupingBy(function, Collectors.counting()));
    }


    /**
     *
     * @param list1 list1
     * @param key1Func key1
     * @param list2 list2
     * @param key2Func key2
     * @param triApply func
     * @param <T1> list1 type
     * @param <T2> list2 type
     * @param <K> key
     */
    public static <T1, T2, K> void groupAndApply(
            List<T1> list1, Function<T1, K> key1Func,
            List<T2> list2, Function<T2, K> key2Func,
            TriConsumer<K, List<T1>, List<T2>> triApply) {

        // Group list1 by key1Func
        Map<K, List<T1>> group1 = list1.stream()
                .collect(Collectors.groupingBy(key1Func));

        // Group list2 by key2Func
        Map<K, List<T2>> group2 = list2.stream()
                .collect(Collectors.groupingBy(key2Func));

        // Create a set of all keys
        Set<K> allKeys = new HashSet<>();
        allKeys.addAll(group1.keySet());
        allKeys.addAll(group2.keySet());

        // Process each key
        for (K key : allKeys) {
            List<T1> group1List = group1.get(key);
            List<T2> group2List = group2.get(key);
            triApply.accept(key,group1List, group2List);
        }
    }

    public static <T, K, V> Map<K, V> groupAndApply(
            List<T> list,
            Function<T, K> keyFunction,
            BiFunction<K, List<T>, V> valueFunction) {

        if (list == null || list.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<K, List<T>> grouped = list.stream()
                .collect(Collectors.groupingBy(keyFunction));

        Map<K, V> result = new HashMap<>();
        for (Map.Entry<K, List<T>> entry : grouped.entrySet()) {
            result.put(entry.getKey(), valueFunction.apply(entry.getKey(), entry.getValue()));
        }

        return result;
    }
    public static <T, K, V> void groupThenAccept(
            List<T> list,
            Function<T, K> keyFunction,
            BiConsumer<K, List<T>> valueFunction) {

        if (list == null || list.isEmpty()) {
            return;
        }
        Map<K, List<T>> grouped = list.stream()
                .collect(Collectors.groupingBy(keyFunction));
        for (Map.Entry<K, List<T>> entry : grouped.entrySet()) {
            valueFunction.accept(entry.getKey(), entry.getValue());
        }
    }

    public static <T, K> Map<K, List<T>> filterGroup(Collection<T> col, Predicate<T> p, Function<T, K> function) {
        if (CollectionUtils.isEmpty(col)) {
            return Collections.emptyMap();
        }
        return col.stream().filter(p).collect(Collectors.groupingBy(function));
    }

    public static <T> List<T> distinctByKey(List<T> list, Function<? super T, ?> keyExtractor) {
        List<T> newList = new ArrayList<>();
        list.stream().filter(distinctByKey(keyExtractor))  //filter保留true的值
                .forEach(newList::add);
        return newList;
    }

    public static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        //putIfAbsent方法添加键值对，如果map集合中没有该key对应的值，则直接添加，并返回null，如果已经存在对应的值，则依旧为原来的值。
        //如果返回null表示添加数据成功(不重复)，不重复(null==null :TRUE)
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    public static <T> T reduce(List<T> list) {
        T result = null;
        result = list.stream()
                .reduce((acc, p) -> {
                    Field[] fields = p.getClass().getDeclaredFields();
                    for (Field field : fields) {
                        field.setAccessible(true);
                        try {
                            Object value = field.get(p);
                            if (value instanceof Number) {
                                BigDecimal sum = BigDecimal.valueOf(((Number) field.get(acc)).doubleValue()).add(
                                        BigDecimal.valueOf(((Number) value).doubleValue())
                                );
                                if (value instanceof Integer) {
                                    field.set(acc, sum.intValue());
                                } else if (value instanceof Long) {
                                    field.set(acc, sum.longValue());
                                }
                                else if (value instanceof Float) {
                                    field.set(acc, sum.floatValue());
                                } else if (value instanceof Double) {
                                    field.set(acc, sum.doubleValue());
                                } else {
                                    field.set(acc, sum);
                                }
                            } else if (field.get(acc) == null) {
                                field.set(acc, value);
                            }
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                    }
                    return acc;
                }).orElse(null);
        return result;
    }

    public static boolean checkExist(Collection<?> list, Object obj) {
        if (list == null || list.isEmpty()) {
            return true;
        } else {
            for (Object t : list) {
                if (t != null && t.equals(obj)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     *   将集合数据 转 链表.<br/>
     * @param normalNodeList 集合数据
     * @param isBeginMapper 开始节点的判断（不是指链表的开始节点，是指从哪条数据开始拼装链表），可以为 null.<br/>
     *          可以指定任意一条数据开始，为 NUll 时使用第一条数据.<br/>
     *          获取到开始节点之后，先向前找前置节点进行添加，再向后找后置节点添加，直到链表结束。<br/>
     *          向前寻找的默认递归深度为 集合数据大小 * 集合数据大小，最大为 10000。<br/>
     * @param nextIdMapper 用于获取后置节点id<br/>
     * @param idMapper 用于获取当前节点id<br/>
     * @param <T>  集合数据类型
     * @param <R>  节点id类型
     */
    public static  <T,R> LinkedList<T> sortChain(List<T> normalNodeList,
            Function<? super T, Boolean> isBeginMapper,
            Function<? super T, ? extends R> nextIdMapper,
            Function<? super T, ? extends R> idMapper) {
        if (ListUtils.isEmpty(normalNodeList)) {
            return new LinkedList<>();
        }
        Objects.requireNonNull(normalNodeList, "idMapper");
        Objects.requireNonNull(nextIdMapper, "nextIdMapper");

        int maxLoop = normalNodeList.size() * normalNodeList.size();
        if (maxLoop > 10000 || maxLoop <= 0) {
            maxLoop = 10000;
        }
        LinkedList<T> chainResult = new LinkedList<>();
        if (isBeginMapper == null) {
            chainResult.addLast(normalNodeList.get(0));
        } else {
            for (T item : normalNodeList) {
                Boolean isBegin = isBeginMapper.apply(item);
                if (isBegin != null && isBegin) {
                    chainResult.addLast(item);
                    break;
                }
            }
        }
        // 往前面添加节点
        findAddNode(chainResult, normalNodeList, nextIdMapper, idMapper, maxLoop , 0, true);
        // 往后面添加节点
        findAddNode(chainResult, normalNodeList, nextIdMapper, idMapper, maxLoop , 0, false);
        return chainResult;
    }

    private static  <T,R> void findAddNode(LinkedList<T> chainResult, List<T> normalNodeList,
            Function<? super T, ? extends R> nextIdMapper,
            Function<? super T, ? extends R> idMapper,
            int maxLoop, int currentLoop, boolean findBefore) {
        if (currentLoop > maxLoop) {
            throw new BizException("节点链查找超过最大循环次数，请检查！");
        }
        T item;
        if (findBefore) {
            item = chainResult.getFirst();
        } else {
            item = chainResult.getLast();
        }
        for (T normalNode : normalNodeList) {
            R nextId;
            R id;
            if (findBefore) {
                nextId = nextIdMapper.apply(normalNode);
                id = idMapper.apply(item);
            } else {
                nextId = nextIdMapper.apply(item);
                id = idMapper.apply(normalNode);
            }
            if (Objects.equals(nextId, id)) {
                if (findBefore) {
                    chainResult.addFirst(normalNode);
                } else {
                    chainResult.addLast(normalNode);
                }
                findAddNode(chainResult, normalNodeList, nextIdMapper, idMapper,
                        maxLoop, currentLoop + 1, findBefore);
                break;
            }
        }
    }

}
