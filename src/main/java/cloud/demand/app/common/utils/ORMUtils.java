package cloud.demand.app.common.utils;

import cloud.demand.app.common.utils.orm.SingleWhere;
import cloud.demand.app.common.utils.orm.SingleWhereSqlAndParam;
import cloud.demand.app.web.model.common.Page;
import cn.hutool.core.util.ReflectUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.impl.SpringJdbcDBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.dbhelper.utils.NamedParameterUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.lang.annotation.Annotation;
import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.Scanner;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Nonnull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;

/**
 * 包装一些数据库操作
 */
@Slf4j
public class ORMUtils {

    static Map<String, String> filed2column = new ConcurrentHashMap<>();

    /**
     * 从offset转换成第几页
     *
     * @param offset
     * @param pageSize
     * @return
     */
    public static int toPageIndex(Integer offset, Integer pageSize) {
        if (offset == null || pageSize == null) {
            return 1;
        }
        if (offset < 0) {
            return 1;
        }
        return 1 + offset / pageSize;
    }

    /**
     * 执行统计sql，并返回结果
     *
     * @param dbHelper 查询dbHelper实例
     * @param sqlFile 放在resources目录下，必须斜杆开头/
     * @param clazz 返回的数据行对象
     */
    public static <T> List<T> query(DBHelper dbHelper, Class<T> clazz, String sqlFile) {
        String sql = getSql(sqlFile);
        return dbHelper.getRaw(clazz, sql);
    }

    /**
     * 执行统计sql，并返回结果
     *
     * @param dbHelper 查询dbHelper实例
     * @param sqlFile 放在resources目录下，必须斜杆开头/
     * @param clazz 返回的数据行对象
     * @param params 查询参数
     */
    public static <T> List<T> query(DBHelper dbHelper, Class<T> clazz, String sqlFile,
            Map<String, Object> params) {
        String sql = getSql(sqlFile);
        return dbHelper.getRaw(clazz, sql, params);
    }

    public static String getSql(String sqlFile) {
        InputStream input = ORMUtils.class.getResourceAsStream(sqlFile);
        if (input == null) {
            throw new RuntimeException("sqlFile: " + sqlFile + " not exists.");
        }

        String sql = readAll(input, "utf-8");
        if (sql == null || sql.trim().isEmpty()) {
            throw new RuntimeException("sqlFile:" + sqlFile + " is blank");
        }

        return sql;
    }

    private static String readAll(InputStream in, String charset) {
        Scanner scanner = new Scanner(in, charset);
        try {
            String content = scanner.useDelimiter("\\Z").next();
            return content;
        } catch (NoSuchElementException e) {
            return ""; // file is empty, ignore exception
        } finally {
            scanner.close();
            try {
                in.close();
            } catch (IOException e) { // ignore
            }
        }
    }

    public static DBHelder db(DBHelper dbHelper) {
        return new DBHelder(dbHelper);
    }

    @SneakyThrows
    public static Field getDeclaredField(@Nonnull Class<?> clz, @Nonnull String name) {
        String clzName = clz.getName();
        while (clz != null) {
            try {
                return clz.getDeclaredField(name);
            } catch (NoSuchFieldException ignored) {
            }
            clz = clz.getSuperclass();
        }
        throw new NoSuchFieldException(name + " filed not  find in " + clzName);
    }

    public static <T> String getFiledNameByMethod(IGetter<T> fn) {
        SerializedLambda lambda = getSerializedLambda(fn);
        return lambda.getImplMethodName();
    }

    @SneakyThrows
    public static <T> String getColumnByMethod(IGetter<T> fn) {
        SerializedLambda lambda = getSerializedLambda(fn);
        String methodName = lambda.getImplMethodName();
        String implClass = lambda.getImplClass().replace("/", ".");
        Class<?> tableClass = Class.forName(implClass);
        //使用的 lombok， 反过来一定真确
        String filedName = toLowerCaseFirstOne(methodName.substring(3));
        List<Field> declaredFields = DOInfoReader.getColumns(tableClass);
        for (Field declaredField : declaredFields) {
            if (declaredField.getName().equals(filedName)) {
                Column annotation = declaredField.getAnnotation(Column.class);
                return annotation.value();
            }
        }
        return null;
    }

    @SneakyThrows
    public static <K> Field getFiledByGetter(IGetter<K> fn) {
        SerializedLambda lambda = getSerializedLambda(fn);
        String methodName = lambda.getImplMethodName();
        String implClass = lambda.getImplClass().replace("/", ".");
        Class<?> tableClass = Class.forName(implClass);
        //使用的 lombok， 反过来一定真确
        String filedName = toLowerCaseFirstOne(methodName.substring(3));

        return tableClass.getDeclaredField(filedName);
    }

    @SneakyThrows
    public static <K> Object getValueByGetter(IGetter<K> fn, K obj) {
        Field field = getFiledByGetter(fn);
        return field.get(obj);
    }


    @SneakyThrows
    public static <K, T extends Annotation> T getAnnotationByGetter(Class<T> annotationClz, IGetter<K> fn) {
        SerializedLambda lambda = getSerializedLambda(fn);
        String methodName = lambda.getImplMethodName();
        String implClass = lambda.getImplClass().replace("/", ".");
        Class<?> tableClass = Class.forName(implClass);
        //使用的 lombok， 反过来一定真确
        String filedName = toLowerCaseFirstOne(methodName.substring(3));

        Field filedByGetter = getFiledByGetter(fn);
        return filedByGetter.getAnnotation(annotationClz);
    }

    @SafeVarargs
    @SneakyThrows
    public static <T> List<String> getColumnByMethod(IGetter<T>... fn) {
        ArrayList<String> ret = new ArrayList<>();
        for (IGetter<T> i : fn) {
            if (getColumnByMethod(i) != null) {
                ret.add(getColumnByMethod(i));
            }
        }
        return ret;
    }

    public static String getColumnByFieldName(Class<?> classz, String fieldName) {
        if (filed2column.containsKey(classz.toString() + fieldName)) {
            return filed2column.get(classz.toString() + fieldName);
        }
        List<Field> declaredFields = DOInfoReader.getColumns(classz);
        for (Field declaredField : declaredFields) {
            if (declaredField.getName().equals(fieldName)) {
                Column annotation = declaredField.getAnnotation(Column.class);
                if (annotation != null) {
                    filed2column.put(classz.toString() + fieldName, annotation.value());
                    return annotation.value();
                } else {
                    throw new RuntimeException(classz.getName() + " 没有这个字段请检查: " + fieldName);
                }
            }
        }
        return null;
    }

    public static List<String> getColumnByFieldName(Class<?> classz, String[] fieldNames) {
        ArrayList<String> ret = new ArrayList<>();
        for (String fieldName : fieldNames) {
            ret.add(getColumnByFieldName(classz, fieldName));
        }
        return ret;
    }

    public static List<String> getColumnByFieldName(Class<?> classz, List<String> fieldNames) {
        return getColumnByFieldName(classz, fieldNames.toArray(new String[0]));
    }

    public static String toLowerCaseFirstOne(String field) {
        if (Character.isLowerCase(field.charAt(0))) {
            return field;
        } else {
            char firstOne = Character.toLowerCase(field.charAt(0));
            String other = field.substring(1);
            return firstOne + other;
        }
    }

    @SneakyThrows
    public static SerializedLambda getSerializedLambda(Serializable fn) {
        Method method = fn.getClass().getDeclaredMethod("writeReplace");
        method.setAccessible(Boolean.TRUE);
        return (SerializedLambda) method.invoke(fn);
    }

    @FunctionalInterface
    public interface ISetter<T, U> extends Serializable {

        void set(T t, U u);
    }

    @FunctionalInterface
    public interface IGetter<T> extends Serializable {

        Object get(T source);
    }

    @Data
    public static class PageWrapper {

        Integer pageSize;
        Integer offset;
        Integer pageIndex;

        public PageWrapper(Integer offset, Integer pageSize) {
            this.pageSize = pageSize > 0 ? pageSize : 10;
            this.pageIndex = ORMUtils.toPageIndex(offset == null ? 0 : offset, pageSize);
            this.offset = offset;
        }

        public PageWrapper(Page page) {
            this.pageSize = page != null && page.getSize() > 0 ? page.getSize() : 10;
            this.offset = page == null ? 0 : page.getStart();
            this.pageIndex = ORMUtils.toPageIndex(this.offset, pageSize);
        }
    }


    public static class DBHelderWithOutInfoLog extends DBHelder {

        public DBHelderWithOutInfoLog(DBHelper dbHelder) {
            super(dbHelder);
        }

        @Override
        public <T> PageData<T> getPage(Class<T> clz, PageWrapper page, WhereContent whereContent) {
            super.turnOffFeature();
            PageData<T> ret = super.getPage(clz, page, whereContent);
            super.turnOnFeature();
            return ret;
        }


        @Override
        public <T> List<T> getAll(Class<T> clz, WhereContent whereContent) {
            super.turnOffFeature();
            List<T> ret = super.getAll(clz, whereContent);
            super.turnOnFeature();
            return ret;
        }

        @Override
        public <T> List<T> getRaw(Class<T> clazz, WhereContent whereContent) {
            super.turnOffFeature();
            List<T> ret = super.getRaw(clazz, whereContent);
            super.turnOnFeature();
            return ret;
        }

        @Override
        public <T> T getOne(Class<T> clazz, WhereContent whereContent) {
            super.turnOffFeature();
            T ret = super.getOne(clazz, whereContent);
            super.turnOnFeature();
            return ret;
        }
    }

    @Data
    @AllArgsConstructor
    public static class DBHelder {

        private DBHelper dbHelder;

        protected void turnOffFeature() {
            this.dbHelder.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        }

        protected void turnOnFeature() {
            this.dbHelder.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        }

        public DBHelder closeInfoLog() {
            return new DBHelderWithOutInfoLog(this.dbHelder);
        }

        public <T> PageData<T> getPage(Class<T> clz, PageWrapper page, WhereContent whereContent) {
            return this.dbHelder.getPage(clz,
                    page.getPageIndex(), page.getPageSize(),
                    whereContent.getSql(), whereContent.getParams());
        }

        public <T> T getById(Class<T> clz, Long id) {
            return this.dbHelder.getOne(clz, "where id =?", id);
        }


        /**
         * 返回一个 map 包含所有数据, 第一列数据要是最明细的数据
         *
         * @param keyType key type
         * @param valueType value type
         * @param sql sql
         * @param args args, 支持 in (?)
         * @param <K> k
         * @param <V> v
         * @return map
         */
        public <K, V> Map<K, V> getKVMap(Class<K> keyType, Class<V> valueType, String sql, Object... args) {

            List<Object> argsList = new ArrayList<>(); // 不要直接用Arrays.asList，它不支持clear方法
            if (args != null) {
                argsList.addAll(Arrays.asList(args));
            }
            return ((SpringJdbcDBHelper) (this.dbHelder)).getNamedParameterJdbcTemplate().query(
                    NamedParameterUtils.trans(sql, argsList),
                    NamedParameterUtils.transParam(argsList),
                    rs -> {
                        Map<K, V> resultMap = new HashMap<>();
                        while (rs.next()) {
                            K key = keyType.cast(rs.getObject(1));
                            V value = valueType.cast(rs.getObject(2));
                            if (resultMap.containsKey(key)) {
                                log.error("重复key:{},覆盖value:{}", key, resultMap.get(key));
                            }
                            resultMap.put(key, value);
                        }
                        return resultMap;
                    });
        }

        /**
         * 返回一个 map 包含所有数据, 第一列数据要是最明细的数据
         *
         * @param keyType key type
         * @param valueType value type
         * @param sql sql
         * @param args args, 支持 in (?)
         * @param <K> k
         * @param <V> v
         * @return map
         */
        public <K, V> Map<K, List<V>> getKVListMap(Class<K> keyType, Class<V> valueType, String sql, Object... args) {

            List<Object> argsList = new ArrayList<>(); // 不要直接用Arrays.asList，它不支持clear方法
            if (args != null) {
                argsList.addAll(Arrays.asList(args));
            }
            return ((SpringJdbcDBHelper) (this.dbHelder)).getNamedParameterJdbcTemplate().query(
                    NamedParameterUtils.trans(sql, argsList),
                    NamedParameterUtils.transParam(argsList),
                    rs -> {
                        Map<K, List<V>> resultMap = new HashMap<>();
                        while (rs.next()) {
                            K key = keyType.cast(rs.getObject(1));
                            V value = valueType.cast(rs.getObject(2));
                            List<V> orDefault = resultMap.getOrDefault(key, new ArrayList<>());
                            orDefault.add(value);
                            resultMap.put(key, orDefault);
                        }
                        return resultMap;
                    });
        }


        public <V> Map<String, V> getKVMap(Class<V> valueType, String sql, Object... args) {
            return getKVMap(String.class, valueType, sql, args);
        }

        public Map<String, String> getKVMap(String sql, Object... args) {
            return getKVMap(String.class, String.class, sql, args);
        }
        public Map<String, List<String>> getKVListMap(String sql, Object... args) {
            return getKVListMap(String.class, String.class, sql, args);
        }


        public <K, V> Multimap<K, V> getMultiMap(Class<K> keyType, Class<V> valueType, String sql, Object... args) {
            List<Object> argsList = new ArrayList<>(); // 不要直接用Arrays.asList，它不支持clear方法
            if (args != null) {
                argsList.addAll(Arrays.asList(args));
            }
            return ((SpringJdbcDBHelper) (this.dbHelder)).getNamedParameterJdbcTemplate().query(
                    NamedParameterUtils.trans(sql, argsList),
                    NamedParameterUtils.transParam(argsList),
                    rs -> {
                        Multimap<K, V> resultMap = ArrayListMultimap.create();
                        while (rs.next()) {
                            K key = keyType.cast(rs.getObject(1));
                            V value = valueType.cast(rs.getObject(2));
                            resultMap.put(key, value);
                        }
                        return resultMap;
                    });
        }

        public <V> Multimap<String, V> getMultiMap(Class<V> valueType, String sql, Object... args) {
            return getMultiMap(String.class, valueType, sql, args);
        }

        public Multimap<String, String> getMultiMap(String sql, Object... args) {
            return getMultiMap(String.class, String.class, sql, args);
        }


        public <T> List<T> getAll(Class<T> clz, WhereContent whereContent) {
            return this.dbHelder.getAll(clz, whereContent.getSql(), whereContent.getParams());
        }

        public <T> List<T> getRaw(Class<T> clazz, WhereContent whereContent) {
            return this.dbHelder.getRaw(clazz, whereContent.getSql(), whereContent.getParams());
        }

        public <T> T getOne(Class<T> clazz, WhereContent whereContent) {
            return this.dbHelder.getOne(clazz, whereContent.getSql(), whereContent.getParams());
        }

        public <T> List<T> getByIds(Class<T> clazz, Collection<?> ids) {
            WhereContent whereContent = new WhereContent();
            whereContent.andEqual("id in(?)", ids);
            return this.dbHelder.getAll(clazz, whereContent.getSql(), whereContent.getParams());
        }

        public <T> int delete(Class<T> clazz, WhereContent whereContent) {
            return this.dbHelder.delete(clazz, whereContent.getSql(), whereContent.getParams());
        }

    }

    public static class UpdateSetContent {

        private final List<Object> params = new ArrayList<>();
        private String setString = "";

        public String getSql() {
            return " SET " + setString;
        }

        public Object[] getParams() {
            return this.params.toArray();
        }

        public void addUpdateParam(String field, Object value) {
            String part = " " + field + " = ? ";
            if (setString.isEmpty()) {
                this.setString = part;
            } else {
                this.setString = String.join(" , ", this.setString, part);
            }
            this.params.add(value);
        }
    }

    /**
     * group by,  having, order by 附加子句暂时不支持参数传递
     *
     * @deprecated 请使用WhereSql代替WhereContent
     */
    @Slf4j
    @Deprecated
    public static class WhereContent {

        private String condition = "";
        private final List<String> groupBy = new ArrayList<>();
        private final List<String> order = new ArrayList<>();
        private final List<String> having = new ArrayList<>();
        private final List<Object> params = new ArrayList<>();

        public WhereContent(String sql, Object... params) {
            this.addAnd(sql, params);
        }

        public WhereContent() {
        }

        public WhereContent(WhereContent other) {
            addAnd(other);
        }

        protected String getCondition() {
            return (condition.isEmpty() ? " (1=1)" : this.condition);
        }

        protected List<String> getHaving() {
            return having;
        }

        protected List<String> getOrder() {
            return order;
        }

        protected List<String> getGroupBy() {
            return groupBy;
        }

        public boolean isEmpty() {
            return condition.isEmpty() && order.isEmpty() && groupBy.isEmpty() && having.isEmpty();
        }


        public String getSql() {
            String ret = " WHERE " + (condition.isEmpty() ? " (1=1)" : this.condition);
            ret += groupBy.isEmpty() ? "" : " group by " + Strings.join(",", this.groupBy.toArray());
            ret += having.isEmpty() ? "" : " having " + Strings.join(" and ", this.having.toArray());
            if (!groupBy.isEmpty() && order.isEmpty()) {
                // groupby 非空 ， orderby 为空， 则把groupby字段全部赋值给orderby字段
                ret += " order by " + Strings.join(",", this.groupBy.toArray());
            } else {
                ret += order.isEmpty() ? "" : " order by " + Strings.join(",", this.order.toArray());
            }
            return ret;
        }

        public Object[] getParams() {
            return this.params.toArray();
        }

        public WhereContent addOr(String sql, Object... params) {
            checkSql(sql, params);
            if (this.condition.isEmpty()) {
                this.condition = sql;
            } else {
                this.condition = "(" + this.condition + ") or (" + sql + ")";
            }
            if (params != null && params.length != 0) {
                this.params.addAll(ListUtils.newArrayList(params));
            }
            return this;
        }

        public WhereContent addOr(WhereContent whereContent) {
            if (whereContent == null || whereContent.isEmpty()) {
                return this;
            }
            addPostStat(whereContent);
            return addOr(whereContent.getCondition(), whereContent.getParams());
        }

        // 给当前条件取反
        public WhereContent not() {
            this.condition = "NOT ( " + this.condition + ")";
            return this;
        }

        public <T> WhereContent andIn(IGetter<T> columnGetter, Object value) {
            return this.andIn(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andNotIn(IGetter<T> columnGetter, Object value) {
            return this.andNotIn(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent orIn(IGetter<T> columnGetter, Object value) {
            return this.orIn(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent orIn(String columnName, Object value) {
            return this.addOr(columnName + " in (?) ", value);
        }


        public <T> WhereContent andInIfValueNotEmpty(String tableName, IGetter<T> columnGetter, List<?> value) {
            if (value == null || value.isEmpty()) {
                return this;
            }
            return this.andIn(tableName + "." + ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andInIfValueNotEmpty(IGetter<T> columnGetter, List<?> value) {
            if (value == null || value.isEmpty()) {
                return this;
            }
            return this.andIn(ORMUtils.getColumnByMethod(columnGetter), value);
        }


        public <T> WhereContent andNotInIfValueNotEmpty(IGetter<T> columnGetter, List<?> value) {
            if (value == null || value.isEmpty()) {
                return this;
            }
            return this.andNotIn(ORMUtils.getColumnByMethod(columnGetter), value);
        }


        public <T> WhereContent andInNoBracketsIfNotEmpty(String column, List<?> val) {
            if (val == null || val.isEmpty()) {
                return this;
            }
            return this.andInNoBrackets(column, val);
        }

        public <T> WhereContent andNotInNoBracketsIfNotEmpty(String column, List<?> val) {
            if (val == null || val.isEmpty()) {
                return this;
            }
            return this.andNotInNoBrackets(column, val);
        }

        public <T> WhereContent andNotInNoBrackets(String column, List<?> val) {
            return this.addAndNoBrackets(column + " not in (?)", val);
        }

        public <T> WhereContent andInNoBrackets(String column, List<?> val) {
            return this.addAndNoBrackets(column + " in (?)", val);
        }


        public <T> WhereContent andInIfValueNotEmpty(String columnName, List<?> value) {
            if (value == null || value.isEmpty()) {
                return this;
            }
            return this.andIn(columnName, value);
        }

        public <T> WhereContent andInIfValueNotEmpty(String tableName, String columnName, List<?> value) {
            if (value == null || value.isEmpty()) {
                return this;
            }
            return this.andIn(tableName + "." + columnName, value);
        }

        public <T> WhereContent andIn(String columnName, Object value) {
            return this.addAnd(columnName + " in (?) ", value);
        }

        public <T> WhereContent andNotIn(String columnName, Object value) {
            return this.addAnd(columnName + " not in (?) ", value);
        }

        public <T> WhereContent andIn(String tableName, String columnName, Object value) {
            return this.addAnd(tableName + "." + columnName + " in (?) ", value);
        }


        public <T> WhereContent andEqual(IGetter<T> columnGetter, Object value) {
            return this.andEqual(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andNotEqual(IGetter<T> columnGetter, Object value) {
            return this.andNotEqual(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andEqualIfValueNotEmpty(IGetter<T> columnGetter, String value) {
            if (Strings.isBlank(value)) {
                return this;
            }
            return this.andEqual(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent orEqualIfValueNotEmpty(IGetter<T> columnGetter, String value) {
            if (Strings.isBlank(value)) {
                return this;
            }
            return this.orEqual(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andEqualIfValueNotNull(IGetter<T> columnGetter, Object value) {
            if (value == null) {
                return this;
            }
            return this.andEqual(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andEqual(String columnName, Object value) {
            return this.addAnd(columnName + " = ? ", value);
        }

        public <T> WhereContent andNotEqual(String columnName, Object value) {
            return this.addAnd(columnName + " != ?", value);
        }

        public <T> WhereContent orEqual(String columnName, Object value) {
            return this.addOr(columnName + " = ?", value);
        }

        // greater than or equal
        public <T> WhereContent andGTE(String columnName, Object value) {
            return this.addAnd(columnName + " >= ?", value);
        }

        // greater than or equal
        public <T> WhereContent andGTE(IGetter<T> columnGetter, Object value) {
            return this.andGTE(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        // less than or equal
        public <T> WhereContent andLTE(String columnName, Object value) {
            return this.addAnd(columnName + " <= ?", value);
        }

        // less than or equal
        public <T> WhereContent andLTE(IGetter<T> columnGetter, Object value) {
            return this.andLTE(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        // less than
        public <T> WhereContent andLT(String columnName, Object value) {
            return this.addAnd(columnName + " < ?", value);
        }

        // like
        public <T> WhereContent andLT(IGetter<T> columnGetter, Object value) {
            return this.andLT(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        // like
        public <T> WhereContent andLIKE(String columnName, Object value) {
            return this.addAnd(columnName + " like ?", value);
        }

        // like
        public <T> WhereContent andLIKE(IGetter<T> columnGetter, Object value) {
            return this.andLIKE(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        // not like
        public <T> WhereContent andNotLIKE(String columnName, Object value) {
            return this.addAnd(columnName + "not like ?", value);
        }

        // not like
        public <T> WhereContent andNotLIKE(IGetter<T> columnGetter, Object value) {
            return this.andNotLIKE(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public <T> WhereContent andIfValueNotEmpty(IGetter<T> columnGetter, String value) {
            return andIfValueNotEmpty(ORMUtils.getColumnByMethod(columnGetter), value);
        }

        public WhereContent andIfValueNotEmpty(String columnName, String value) {
            return Strings.isEmpty(value) ? this : addAnd(columnName, value);
        }

        /**
         * 注意这个是可以填多个参数
         */
        public WhereContent addAnd(String sql, Object... params) {
            checkSql(sql, params);
            if (this.condition.isEmpty()) {
                this.condition = sql;
            } else {
                this.condition = " (" + this.condition + ") and (" + sql + ")";
            }
            if (params != null && params.length != 0) {
                this.params.addAll(ListUtils.newArrayList(params));
            }
            return this;
        }

        /**
         * 注意这个不会对前面的sql增加括号，有 or 的时候容易出问题
         */
        public WhereContent addAndNoBrackets(String sql, Object... params) {
            checkSql(sql, params);
            if (this.condition.isEmpty()) {
                this.condition = " (" + sql + ")\n";
            } else {
                this.condition = " " + this.condition + " and (" + sql + ")\n";
            }
            if (params != null && params.length != 0) {
                this.params.addAll(ListUtils.newArrayList(params));
            }
            return this;
        }

        public WhereContent addAnd(WhereContent whereContent) {
            if (whereContent == null || whereContent.isEmpty()) {
                return this;
            }
            addPostStat(whereContent);
            return addAnd(whereContent.getCondition(), whereContent.getParams());
        }

        public WhereContent groupBy(String group) {
            if (Strings.isNotBlank(group) && !this.groupBy.contains(group)) {
                this.groupBy.add(group);
            }
            return this;
        }

        public WhereContent groupBy(List<String> group) {
            if (Lang.isEmpty(group)) {
                return this;
            }
            return groupBy(group.toArray(new String[0]));
        }

        public WhereContent groupBy(String... group) {
            if (group != null) {
                for (String s : group) {
                    groupBy(s);
                }
            }
            return this;
        }

        public WhereContent resetGroupBy() {
            groupBy.clear();
            return this;
        }

        public WhereContent having(String having) {
            if (Strings.isNotBlank(having) && !this.having.contains(having)) {
                this.having.add(having);
            }
            return this;
        }

        //warning
        protected void addPostStat(WhereContent w) {
            w.getGroupBy().forEach(this::groupBy);
            w.getOrder().forEach(this::order);
            w.getHaving().forEach(this::having);
        }

        public WhereContent order(List<String> orders) {
            ListUtils.forEach(orders, this::order);
            return this;
        }

        public WhereContent order(String order) {
            if (Strings.isNotBlank(order) && !this.order.contains(order)) {
                this.order.add(order);
            }
            return this;
        }

        public WhereContent orderDesc(List<String> orders) {
            ListUtils.forEach(orders, this::orderDesc);
            return this;
        }

        public WhereContent orderDesc(String order) {
            if (Strings.isNotBlank(order) && !this.order.contains(order)) {
                this.order.add(order + " desc ");
            }
            return this;
        }


        private void checkSql(String sql, Object... params) {
            int cnt = 0;
            if (!Strings.isBlank(sql)) {
                for (int i = 0; i < sql.length(); i++) {
                    if (sql.charAt(i) == '?') {
                        cnt++;
                    }
                }
            }
            if (params != null && cnt != params.length) {
                log.warn("sql params num is wrong sql: {}, params size:{}", sql, params.length);
            }
        }

        /**
         * 使用{@link SingleWhere} 注解生成 or 条件 <br/>
         * 注意：当值为 null 时，不会添加此条件
         *
         * @param fn get方法，例：{@code QueryAppliedSupplyListReq::getVersionCode}
         * @param req 封装的入参实体类(带指定get方法的)
         */
        public <K> WhereContent addOrBySingleWhereAnnotation(IGetter<K> fn, K req) {
            SingleWhereSqlAndParam sqlAndParam = SingleWhereSqlAndParam.from(fn, req);
            if (sqlAndParam == null) {
                return this;
            }
            return addOr(sqlAndParam.getSql(), sqlAndParam.getParam());
        }

        /**
         * 使用{@link SingleWhere} 注解生成 and 条件 <br/>
         * 注意：当值为 null 时，不会添加此条件
         *
         * @param fn get方法，例：{@code QueryAppliedSupplyListReq::getVersionCode}
         * @param req 封装的入参实体类(带指定get方法的)
         */
        public <K> WhereContent addAndBySingleWhereAnnotation(IGetter<K> fn, K req) {
            SingleWhereSqlAndParam sqlAndParam = SingleWhereSqlAndParam.from(fn, req);
            if (sqlAndParam == null) {
                return this;
            }
            return addAnd(sqlAndParam.getSql(),
                    sqlAndParam.isManyParams() ? sqlAndParam.getManyParams() : new Object[]{sqlAndParam.getParam()});
        }

        /**
         * 将入参中所有带有 {@link SingleWhere} 的值非null的字段 以 and 形式添加到查询条件中
         *
         * @param req 查询条件入参
         */
        @SneakyThrows
        public WhereContent addAndAllSingleWhereAnnotationField(Object req) {
            if (req == null) {
                return this;
            }
            Field[] fields = ReflectUtil.getFields(req.getClass());
            for (Field field : fields) {
                SingleWhereSqlAndParam sqlAndParam = SingleWhereSqlAndParam.from(field, req);
                if (sqlAndParam == null) {
                    continue;
                }
                addAnd(sqlAndParam.getSql(),
                        sqlAndParam.isManyParams() ? sqlAndParam.getManyParams()
                                : new Object[]{sqlAndParam.getParam()});
            }
            return this;
        }

    }

    /**
     *
     * @param sql  计数的sql
     * @param isRemoveLimit  是否去除limit参数
     * @return
     */
    public static String getCountSql(String sql,boolean isRemoveLimit){
        if (isRemoveLimit){
            String trim = sql.replaceAll("(?i)LIMIT.*", "").trim();
            return "select count(1) from (" + trim + ") as t";
        }else {
            return "select count(1) from (" + sql + ") as t";
        }

    }
}
