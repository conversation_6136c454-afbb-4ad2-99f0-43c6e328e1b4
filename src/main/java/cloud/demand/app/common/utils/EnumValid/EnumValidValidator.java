package cloud.demand.app.common.utils.EnumValid;

import yunti.boot.exception.BizException;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

public class EnumValidValidator implements ConstraintValidator<EnumValid, Object> {

    private boolean canNull;
    private Class<?> enumClass;
    private String methodName;

    @Override
    public void initialize(EnumValid constraintAnnotation) {
        this.canNull = constraintAnnotation.canNull();
        this.enumClass = constraintAnnotation.enumClass();
        this.methodName = constraintAnnotation.methodName();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null) {
            return canNull;
        }
        try {
            Object[] enumConstants = enumClass.getEnumConstants();

            Method method = enumClass.getDeclaredMethod(methodName);

            for (Object enumConstant : enumConstants) {
                Object res = method.invoke(enumConstant);
                if (res.equals(value)) {
                    return true;
                }
            }

        } catch (NoSuchMethodException e) {
            throw new BizException("未能找到合适的函数进行处理");
        } catch (InvocationTargetException | IllegalAccessException e) {
            throw new BizException("验证属性合法性时出现异常：", e);
        }
        return false;
    }
}
