package cloud.demand.app.common.config;

import com.pugwoo.wooutils.redis.RedisLimitAspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedisLimitConfig {

    /**
     * 使用限频器注解@RateLimit
     */
    @Bean
    @ConditionalOnMissingBean
    RedisLimitAspect redisLimitAspect() {
        return new RedisLimitAspect();
    }

}
